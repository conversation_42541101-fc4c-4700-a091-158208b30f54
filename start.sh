#!/bin/bash

# Function to display progress
echo_step() {
  echo "================================================"
  echo "  $1"
  echo "================================================"
}

# Check if PostgreSQL is installed
if ! command -v psql &> /dev/null; then
  echo "PostgreSQL is not installed. Please install PostgreSQL first."
  exit 1
fi

# Set current directory
PROJECT_DIR=$(pwd)

# Install server dependencies
echo_step "Installing server dependencies..."
cd "${PROJECT_DIR}/server"
npm install

# Install client dependencies
echo_step "Installing client dependencies..."
cd "${PROJECT_DIR}/client"
npm install
npm install @fortawesome/fontawesome-free

# Setup database
echo_step "Setting up database..."
echo "Please enter your PostgreSQL password (if asked)"

# Create database
psql -U postgres -c "CREATE DATABASE medapp;" || true

# Run schema creation
psql -U postgres -d medapp -f "${PROJECT_DIR}/server/sql/db.sql"
psql -U postgres -d medapp -f "${PROJECT_DIR}/server/sql/update_schema.sql"

# Start server in background
echo_step "Starting the server..."
cd "${PROJECT_DIR}/server"
npm run dev &
SERVER_PID=$!

# Wait for server to start
echo "Waiting for server to start..."
sleep 5

# Start client
echo_step "Starting the client..."
cd "${PROJECT_DIR}/client"
npm start

# Cleanup on exit
trap "kill $SERVER_PID" EXIT 