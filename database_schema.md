# Medical Application Database Schema

## Overview

This document provides a detailed explanation of the database schema used in the Medical Application. The application uses PostgreSQL as its database management system and follows a relational database design pattern to store and manage medical data, user information, and system logs.

## Database Tables and Relationships

The database consists of several interconnected tables that store different aspects of the medical system. Below is a detailed description of each table and its relationships.

### Core Tables

#### 1. Users Table

The `users` table stores authentication and authorization information for all system users.

**Table Structure:**
```sql
CREATE TABLE IF NOT EXISTS users (
  user_id SERIAL PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  role VARCHAR(20) NOT NULL DEFAULT 'user',
  is_locked BOOLEAN DEFAULT false,
  failed_login_attempts INTEGER DEFAULT 0,
  last_login TIMESTAMP,
  lockout_until TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Key Fields:**
- `user_id`: Unique identifier for each user
- `username`: Unique username for login
- `email`: User's email address
- `password`: Hashed password
- `role`: User's role in the system (admin, doctor, staff, patient, kin, researcher, user)
- `is_locked`: Flag indicating if the account is locked
- `failed_login_attempts`: Count of consecutive failed login attempts
- `lockout_until`: Timestamp until which the account is locked

**Relationships:**
- One-to-one relationship with `doctors` table (for doctor users)
- One-to-one relationship with `patients` table (for patient users)
- One-to-many relationship with `medical_records` table (created_by)
- One-to-many relationship with `patient_visits` table (created_by)

#### 2. Patients Table

The `patients` table stores comprehensive information about patients, including personal details, medical history, and various health metrics.

**Table Structure (Simplified - Full table has 100+ columns):**
```sql
CREATE TABLE IF NOT EXISTS patients (
  patient_id SERIAL PRIMARY KEY,
  first_name VARCHAR(50) NOT NULL,
  last_name VARCHAR(50) NOT NULL,
  unique_id VARCHAR(50) UNIQUE,
  date_of_birth DATE NOT NULL,
  gender VARCHAR(10),
  phone VARCHAR(20),
  email VARCHAR(100),
  address TEXT,
  doctor_id INTEGER REFERENCES doctors(doctor_id) ON DELETE SET NULL,
  
  -- Vital Signs
  lying_bp_systolic INTEGER,
  lying_bp_diastolic INTEGER,
  standing_bp_systolic INTEGER,
  standing_bp_diastolic INTEGER,
  heart_rate INTEGER,
  heart_rhythm VARCHAR(50),
  body_temperature DECIMAL(4,1),
  respiratory_rate INTEGER,
  pulse_oximetry INTEGER,
  
  -- Many more health-related fields...
  
  -- System fields
  weight DECIMAL(5,2),
  height DECIMAL(5,2),
  bmi DECIMAL(4,1),
  last_edited_by INTEGER REFERENCES users(user_id) ON DELETE SET NULL,
  last_edited_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Key Fields:**
- `patient_id`: Unique identifier for each patient
- `unique_id`: System-generated unique identifier (format: PXXXXX)
- `doctor_id`: Foreign key to the assigned doctor
- Various health metrics and medical information fields
- `last_edited_by`: User who last edited the patient record
- `last_edited_at`: Timestamp of the last edit

**Relationships:**
- Many-to-one relationship with `doctors` table
- One-to-many relationship with `medical_records` table
- One-to-many relationship with `patient_visits` table
- One-to-many relationship with `appointments` table
- Many-to-many relationship with `doctors` table through `doctor_patient_assignments` table

#### 3. Doctors Table

The `doctors` table stores information specific to medical professionals.

**Table Structure:**
```sql
CREATE TABLE IF NOT EXISTS doctors (
  doctor_id SERIAL PRIMARY KEY,
  first_name VARCHAR(50) NOT NULL,
  last_name VARCHAR(50) NOT NULL,
  specialty VARCHAR(100),
  email VARCHAR(100) UNIQUE,
  phone VARCHAR(20),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Key Fields:**
- `doctor_id`: Unique identifier for each doctor
- `first_name`, `last_name`: Doctor's name
- `specialty`: Doctor's medical specialty
- `email`, `phone`: Contact information

**Relationships:**
- One-to-many relationship with `patients` table
- One-to-many relationship with `patient_visits` table
- One-to-many relationship with `appointments` table
- Many-to-many relationship with `patients` table through `doctor_patient_assignments` table

#### 4. Patient Visits Table

The `patient_visits` table records detailed information about each patient visit, including vital signs, assessments, and diagnoses.

**Table Structure (Simplified - Full table has 80+ columns):**
```sql
CREATE TABLE IF NOT EXISTS patient_visits (
  visit_id SERIAL PRIMARY KEY,
  patient_id INTEGER NOT NULL REFERENCES patients(patient_id) ON DELETE CASCADE,
  doctor_id INTEGER REFERENCES doctors(doctor_id) ON DELETE SET NULL,
  visit_date DATE NOT NULL DEFAULT CURRENT_DATE,
  visit_time TIME,
  end_time TIME,
  visit_reason TEXT,
  status VARCHAR(20) DEFAULT 'scheduled',
  
  -- Vital Signs
  lying_bp_systolic INTEGER,
  lying_bp_diastolic INTEGER,
  -- Many more health metrics...
  
  -- Notes and diagnosis
  notes TEXT,
  diagnosis TEXT,
  treatment_plan TEXT,
  follow_up_instructions TEXT,
  assessment_plan VARCHAR(50),
  referrals TEXT,
  medication_changes TEXT,
  
  -- System fields
  created_by INTEGER REFERENCES users(user_id),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Key Fields:**
- `visit_id`: Unique identifier for each visit
- `patient_id`: Foreign key to the patient
- `doctor_id`: Foreign key to the doctor conducting the visit
- `visit_date`, `visit_time`, `end_time`: When the visit occurred
- Various health metrics and assessments
- `diagnosis`, `treatment_plan`: Medical decisions and plans
- `created_by`: User who created the visit record

**Relationships:**
- Many-to-one relationship with `patients` table
- Many-to-one relationship with `doctors` table
- Many-to-one relationship with `users` table (created_by)

### Supporting Tables

#### 5. Medical Records Table

The `medical_records` table stores historical medical documentation for patients.

**Table Structure:**
```sql
CREATE TABLE IF NOT EXISTS medical_records (
  record_id SERIAL PRIMARY KEY,
  patient_id INTEGER REFERENCES patients(patient_id) ON DELETE CASCADE,
  record_date DATE NOT NULL DEFAULT CURRENT_DATE,
  diagnosis TEXT,
  treatment TEXT,
  notes TEXT,
  attending_doctor_id INTEGER REFERENCES doctors(doctor_id) ON DELETE SET NULL,
  created_by INTEGER REFERENCES users(user_id),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Key Fields:**
- `record_id`: Unique identifier for each record
- `patient_id`: Foreign key to the patient
- `record_date`: Date of the record
- `diagnosis`, `treatment`, `notes`: Medical information
- `attending_doctor_id`: Doctor responsible for the record
- `created_by`: User who created the record

**Relationships:**
- Many-to-one relationship with `patients` table
- Many-to-one relationship with `doctors` table
- Many-to-one relationship with `users` table (created_by)
- One-to-many relationship with `prescriptions` table

#### 6. Prescriptions Table

The `prescriptions` table stores medication prescriptions associated with medical records.

**Table Structure:**
```sql
CREATE TABLE IF NOT EXISTS prescriptions (
  prescription_id SERIAL PRIMARY KEY,
  record_id INTEGER REFERENCES medical_records(record_id) ON DELETE CASCADE,
  medication VARCHAR(100) NOT NULL,
  dosage VARCHAR(50),
  frequency VARCHAR(50),
  duration VARCHAR(50),
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Key Fields:**
- `prescription_id`: Unique identifier for each prescription
- `record_id`: Foreign key to the associated medical record
- `medication`: Name of the medication
- `dosage`, `frequency`, `duration`: Prescription details

**Relationships:**
- Many-to-one relationship with `medical_records` table

#### 7. Appointments Table

The `appointments` table manages scheduled appointments between patients and doctors.

**Table Structure:**
```sql
CREATE TABLE IF NOT EXISTS appointments (
  appointment_id SERIAL PRIMARY KEY,
  patient_id INTEGER NOT NULL REFERENCES patients(patient_id),
  doctor_id INTEGER NOT NULL REFERENCES doctors(doctor_id),
  title VARCHAR(255) NOT NULL,
  date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  status VARCHAR(50) NOT NULL CHECK (status IN ('scheduled', 'completed', 'cancelled', 'no-show')),
  type VARCHAR(50) NOT NULL CHECK (type IN ('checkup', 'follow-up', 'urgent', 'consultation', 'other')),
  notes TEXT,
  created_by INTEGER REFERENCES users(user_id),
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP
);
```

**Key Fields:**
- `appointment_id`: Unique identifier for each appointment
- `patient_id`: Foreign key to the patient
- `doctor_id`: Foreign key to the doctor
- `date`, `start_time`, `end_time`: When the appointment is scheduled
- `status`: Current status of the appointment
- `type`: Type of appointment
- `created_by`: User who created the appointment

**Relationships:**
- Many-to-one relationship with `patients` table
- Many-to-one relationship with `doctors` table
- Many-to-one relationship with `users` table (created_by)

#### 8. Doctor-Patient Assignments Table

The `doctor_patient_assignments` table manages the many-to-many relationship between doctors and patients.

**Table Structure:**
```sql
CREATE TABLE IF NOT EXISTS doctor_patient_assignments (
  assignment_id SERIAL PRIMARY KEY,
  doctor_id INTEGER REFERENCES users(user_id) ON DELETE CASCADE,
  patient_id INTEGER REFERENCES patients(patient_id) ON DELETE CASCADE,
  assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(doctor_id, patient_id)
);
```

**Key Fields:**
- `assignment_id`: Unique identifier for each assignment
- `doctor_id`: Foreign key to the doctor (user_id)
- `patient_id`: Foreign key to the patient
- `assigned_date`: When the assignment was made

**Relationships:**
- Many-to-one relationship with `users` table (doctors)
- Many-to-one relationship with `patients` table

### Audit and Logging Tables

#### 9. Patient Access Logs Table

The `patient_access_logs` table records all access to patient records for audit purposes.

**Table Structure:**
```sql
CREATE TABLE IF NOT EXISTS patient_access_logs (
  log_id SERIAL PRIMARY KEY,
  patient_id INTEGER NOT NULL REFERENCES patients(patient_id) ON DELETE CASCADE,
  user_id INTEGER NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  access_type VARCHAR(20) NOT NULL,
  access_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  ip_address VARCHAR(45),
  user_agent TEXT
);
```

**Key Fields:**
- `log_id`: Unique identifier for each log entry
- `patient_id`: Foreign key to the patient whose record was accessed
- `user_id`: Foreign key to the user who accessed the record
- `access_type`: Type of access (view, edit, delete)
- `access_time`: When the access occurred
- `ip_address`, `user_agent`: Information about the access

**Relationships:**
- Many-to-one relationship with `patients` table
- Many-to-one relationship with `users` table

#### 10. Patient Edit Logs Table

The `patient_edit_logs` table records all changes made to patient records for audit purposes.

**Table Structure:**
```sql
CREATE TABLE IF NOT EXISTS patient_edit_logs (
  edit_id SERIAL PRIMARY KEY,
  patient_id INTEGER NOT NULL REFERENCES patients(patient_id) ON DELETE CASCADE,
  user_id INTEGER NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  edit_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  field_changed VARCHAR(100) NOT NULL,
  old_value TEXT,
  new_value TEXT
);
```

**Key Fields:**
- `edit_id`: Unique identifier for each edit log
- `patient_id`: Foreign key to the patient whose record was edited
- `user_id`: Foreign key to the user who made the edit
- `edit_time`: When the edit occurred
- `field_changed`: Which field was changed
- `old_value`, `new_value`: The values before and after the change

**Relationships:**
- Many-to-one relationship with `patients` table
- Many-to-one relationship with `users` table

## Entity Relationship Diagram (ERD)

```
+----------------+       +-------------------+       +----------------+
|     USERS      |       | DOCTOR_PATIENT    |       |    DOCTORS     |
+----------------+       | ASSIGNMENTS       |       +----------------+
| user_id (PK)   |       +-------------------+       | doctor_id (PK) |
| username       |       | assignment_id (PK)|       | first_name     |
| email          |<----->| doctor_id (FK)    |<----->| last_name      |
| password       |       | patient_id (FK)   |       | specialty      |
| role           |       | assigned_date     |       | email          |
| is_locked      |       +-------------------+       | phone          |
| ...            |                                   | ...            |
+----------------+                                   +----------------+
       ^                                                    ^
       |                                                    |
       |                                                    |
       |                                                    |
       v                                                    v
+----------------+                                   +----------------+
|    PATIENTS    |                                   | PATIENT_VISITS |
+----------------+                                   +----------------+
| patient_id (PK)|                                   | visit_id (PK)  |
| first_name     |                                   | patient_id (FK)|
| last_name      |<----------------------------------| doctor_id (FK) |
| unique_id      |                                   | visit_date     |
| date_of_birth  |                                   | visit_time     |
| doctor_id (FK) |                                   | diagnosis      |
| ...            |                                   | ...            |
+----------------+                                   +----------------+
       ^                                                    ^
       |                                                    |
       |                                                    |
       |                                                    |
       v                                                    v
+----------------+                                   +----------------+
| MEDICAL_RECORDS|                                   |  APPOINTMENTS  |
+----------------+                                   +----------------+
| record_id (PK) |                                   | appt_id (PK)   |
| patient_id (FK)|                                   | patient_id (FK)|
| record_date    |                                   | doctor_id (FK) |
| diagnosis      |                                   | date           |
| treatment      |                                   | start_time     |
| ...            |                                   | end_time       |
+----------------+                                   | status         |
       ^                                             | ...            |
       |                                             +----------------+
       |
       |
       v
+----------------+
|  PRESCRIPTIONS |
+----------------+
| presc_id (PK)  |
| record_id (FK) |
| medication     |
| dosage         |
| frequency      |
| ...            |
+----------------+
```

## Database Indexes

The database uses several indexes to optimize query performance:

```sql
-- Patient access logs indexes
CREATE INDEX IF NOT EXISTS idx_access_logs_patient_id ON patient_access_logs(patient_id);
CREATE INDEX IF NOT EXISTS idx_access_logs_user_id ON patient_access_logs(user_id);

-- Patient edit logs indexes
CREATE INDEX IF NOT EXISTS idx_edit_logs_patient_id ON patient_edit_logs(patient_id);
CREATE INDEX IF NOT EXISTS idx_edit_logs_user_id ON patient_edit_logs(user_id);
```

## Data Types and Constraints

### Common Data Types

- **INTEGER**: Used for IDs and numeric values without decimals
- **SERIAL**: Auto-incrementing integers for primary keys
- **VARCHAR**: Variable-length character strings with specified maximum length
- **TEXT**: Unlimited length text fields
- **DATE**: Date values (without time)
- **TIME**: Time values (without date)
- **TIMESTAMP**: Date and time values
- **BOOLEAN**: True/false values
- **DECIMAL**: Numeric values with specified precision and scale

### Key Constraints

- **PRIMARY KEY**: Uniquely identifies each record in a table
- **FOREIGN KEY**: Ensures referential integrity between tables
- **UNIQUE**: Ensures all values in a column are different
- **NOT NULL**: Ensures a column cannot have NULL values
- **CHECK**: Ensures all values in a column satisfy a specific condition
- **DEFAULT**: Provides a default value for a column when none is specified

## Database Migrations

The database schema evolves over time through migrations. Key migrations include:

1. Initial schema creation (`db.sql`)
2. Schema updates (`update_schema.sql`)
3. Adding missing fields to patients table
4. Adding vital signs and lab results
5. Setting timezone to UTC
6. Adding account lockout functionality
7. Adding end time to visits

## Timezone Handling

The database is configured to use UTC timezone to prevent automatic conversions and ensure consistent date/time handling:

```sql
-- Set the timezone for the database to UTC
SET timezone TO 'UTC';

-- Make sure timestamps are displayed in UTC timezone by default
ALTER DATABASE medapp SET timezone TO 'UTC';
```

## Security Considerations

1. **Password Storage**: Passwords are hashed using bcrypt before storage
2. **Account Lockout**: Accounts are locked after multiple failed login attempts
3. **Audit Logging**: All access to patient records is logged
4. **Edit Tracking**: All changes to patient records are tracked with before/after values
5. **Role-Based Access**: Different user roles have different access levels

## Conclusion

The Medical Application database schema is designed to efficiently store and manage medical data while ensuring data integrity, security, and auditability. The schema supports the complex relationships between patients, doctors, visits, and medical records, and includes comprehensive logging for compliance with healthcare regulations.
