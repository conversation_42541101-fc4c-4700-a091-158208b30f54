# Transport Layer Security Implementation Guide

## Overview

This document outlines the implementation of Transport Layer Security (TLS) in the medical application system. TLS is essential for securing data in transit between clients and servers, protecting sensitive medical information from interception and tampering.

## Implementation Components

### 1. HTTPS Configuration

#### Server-Side Implementation

The server has been configured to support both HTTP and HTTPS protocols, with a preference for HTTPS:

```javascript
// HTTPS Configuration
const httpsOptions = {
  key: fs.existsSync(path.join(__dirname, 'certs/key.pem')) 
    ? fs.readFileSync(path.join(__dirname, 'certs/key.pem')) 
    : null,
  cert: fs.existsSync(path.join(__dirname, 'certs/cert.pem')) 
    ? fs.readFileSync(path.join(__dirname, 'certs/cert.pem')) 
    : null
};

// Determine if we can use HTTPS
const useHttps = httpsOptions.key && httpsOptions.cert;

// Create HTTP and HTTPS servers
const httpServer = http.createServer(app);
const httpsServer = https.createServer(httpsOptions, app);
```

#### Client-Side Configuration

The client has been configured to prefer HTTPS connections:

```typescript
// API URL configuration
// Use HTTPS in production and when available in development
const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';
const preferHttps = true; 
const protocol = process.env.NODE_ENV === 'production' || !isLocalhost || preferHttps ? 'https' : 'http';
```

### 2. HTTP Strict Transport Security (HSTS)

HSTS headers have been implemented to instruct browsers to only use secure HTTPS connections:

```javascript
// Add HSTS middleware
app.use((req, res, next) => {
  res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
  next();
});
```

This configuration:
- Sets a max-age of 31536000 seconds (1 year)
- Includes all subdomains
- Indicates the site should be included in browser preload lists

### 3. HTTP to HTTPS Redirection

Automatic redirection from HTTP to HTTPS has been implemented for production environments:

```javascript
// HTTP to HTTPS redirect middleware
const redirectToHttps = (req, res, next) => {
  if (!req.secure && process.env.NODE_ENV !== 'development') {
    return res.redirect(`https://${req.headers.host}${req.url}`);
  }
  next();
};

// In production, HTTP server just redirects to HTTPS
if (process.env.NODE_ENV === 'production' && actualHttpsPort) {
  app.use(redirectToHttps);
}
```

### 4. Certificate Management

For development environments, self-signed certificates are generated using OpenSSL:

```javascript
// Generate a self-signed certificate valid for 365 days
execSync(
  `openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout ${path.join(certsDir, 'key.pem')} -out ${path.join(certsDir, 'cert.pem')} -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"`,
  { stdio: 'inherit' }
);
```

For production, it's recommended to use certificates from a trusted Certificate Authority (CA).

### 5. Flexible Port Configuration

The server has been configured to use flexible port assignments with fallbacks:

```javascript
// Start servers
const HTTP_PORT = process.env.HTTP_PORT || 8080;
const HTTPS_PORT = process.env.HTTPS_PORT || 8443;
const FALLBACK_HTTP_PORT = 9090; // Fallback port if primary is in use
const FALLBACK_HTTPS_PORT = 9443; // Fallback port if primary is in use
```

## Security Considerations

### 1. Certificate Validation

In production environments:
- Use certificates from trusted Certificate Authorities
- Implement proper certificate validation
- Set up automatic certificate renewal

### 2. Cipher Suite Configuration

For production, configure secure cipher suites:

```javascript
const httpsOptions = {
  key: fs.readFileSync(path.join(__dirname, 'certs/key.pem')),
  cert: fs.readFileSync(path.join(__dirname, 'certs/cert.pem')),
  ciphers: [
    'ECDHE-ECDSA-AES256-GCM-SHA384',
    'ECDHE-RSA-AES256-GCM-SHA384',
    'ECDHE-ECDSA-CHACHA20-POLY1305',
    'ECDHE-RSA-CHACHA20-POLY1305',
    'ECDHE-ECDSA-AES128-GCM-SHA256',
    'ECDHE-RSA-AES128-GCM-SHA256'
  ].join(':'),
  honorCipherOrder: true,
  minVersion: 'TLSv1.2'
};
```

### 3. Content Security Policy

Implement Content Security Policy headers to prevent XSS attacks:

```javascript
app.use((req, res, next) => {
  res.setHeader(
    'Content-Security-Policy',
    "default-src 'self'; script-src 'self'; connect-src 'self'; img-src 'self' data:; style-src 'self' 'unsafe-inline';"
  );
  next();
});
```

## Testing TLS Implementation

### 1. Local Development Testing

For local development, use the following steps:

1. Generate self-signed certificates:
   ```bash
   npm run generate-certs
   ```

2. Start the server:
   ```bash
   npm start
   ```

3. Access the application via HTTPS:
   ```
   https://localhost:8443
   ```

### 2. Production Testing

For production environments:

1. Verify HTTPS is enforced by attempting to access via HTTP
2. Verify HSTS headers are present using browser developer tools
3. Test certificate validity using online SSL checkers
4. Verify secure cipher suites using SSL Labs or similar tools

## Deployment Considerations

### 1. Environment Variables

Configure the following environment variables:

```
HTTP_PORT=8080
HTTPS_PORT=8443
NODE_ENV=production
```

### 2. Certificate Installation

For production:
1. Obtain certificates from a trusted CA
2. Install certificates in a secure location
3. Configure the application to use these certificates
4. Set up automatic renewal

### 3. Load Balancer Configuration

When using a load balancer:
1. Configure SSL termination at the load balancer
2. Ensure secure communication between load balancer and application servers
3. Configure proper header forwarding

## Conclusion

This implementation provides a robust Transport Layer Security solution for the medical application, ensuring data confidentiality and integrity during transmission. The flexible configuration allows for seamless development and production deployments while maintaining high security standards.
