# Doctor Dashboard Statistics

## Summary of Changes

We've updated the doctor dashboard to correctly display the number of assigned patients. Previously, the dashboard was showing the count of patients who had visits with the doctor (from the `patient_visits` table) instead of showing patients officially assigned to the doctor (from the `patients` table).

## How Patient Assignment Works

In the MedApp system, there are two ways a patient can be related to a doctor:

1. **Official Assignment**: Patients are officially assigned to a doctor through the `doctor_id` field in the `patients` table. This is the primary way to establish a doctor-patient relationship and is managed through:
   - The Admin interface (Admin → Patient Management → Assign Doctor)
   - When creating a new patient and selecting a doctor
   - API endpoints that update the patient's doctor

2. **Patient Visits**: A record is created in the `patient_visits` table whenever a doctor sees a patient. This may include patients not officially assigned to the doctor (e.g., covering for another doctor, emergency visits).

## Dashboard Statistics

The Doctor Dashboard now correctly shows:

| Statistic | Description | Source |
|-----------|-------------|--------|
| Total Patients on Platform | Count of all patients in the system | `SELECT COUNT(DISTINCT patient_id) FROM patients` |
| Your Assigned Patients | Count of patients officially assigned to the doctor | `SELECT COUNT(DISTINCT patient_id) FROM patients WHERE doctor_id = ?` |

## Additional Available Statistics

We've also added a new detailed statistics endpoint that provides more comprehensive information:

- `GET /api/doctors/detailed-dashboard-stats/:doctorId`

This endpoint returns:

| Statistic | Description | Source |
|-----------|-------------|--------|
| totalPatients | Count of all patients in the system | `patients` table |
| assignedPatients | Count of patients officially assigned to the doctor | `patients` table where `doctor_id = ?` |
| patientsWithVisits | Count of unique patients who have had visits with the doctor | `patient_visits` table where `doctor_id = ?` |
| totalVisits | Count of all visits conducted by the doctor | `patient_visits` table where `doctor_id = ?` |
| appointmentsCount | Count of all appointments for this doctor | `appointments` table where `doctor_id = ?` |
| upcomingAppointments | Count of future appointments for this doctor | `appointments` table where `doctor_id = ?` and date in future |

## Technical Implementation

The fix was applied by changing the SQL query in the `/api/doctors/dashboard-stats/:doctorId` endpoint to:

```javascript
const assignedPatientsResult = await pool.query(
  `SELECT COUNT(DISTINCT patient_id) 
   FROM patients 
   WHERE doctor_id = $1`,
  [doctorId]
);
```

Previously, it was incorrectly using the `patient_visits` table for this count.

## Testing

To test the fix, log in as any doctor (e.g., patel_priya/doctor123) and check that the "Your Assigned Patients" count matches the number of patients with that doctor's ID in the patients table. 