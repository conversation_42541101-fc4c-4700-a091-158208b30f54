# Medical Records Management System

## Overview
A comprehensive medical records management system designed for healthcare providers to manage patient information, appointments, and medical records efficiently.

## Features
- **User Authentication**
  - Role-based access control (<PERSON><PERSON>, Doctor, Staff)
  - Secure login/logout functionality
  - Password reset capabilities
  - First-time login password change enforcement
  - Configurable password policies
  - Password expiration and history tracking

- **Doctor Dashboard**
  - Overview of total patients in the system
  - Count of assigned patients
  - Weekly appointment schedule
  - Patient visit history

- **Patient Management**
  - Patient registration and profile management
  - Medical history tracking
  - Visit records with comprehensive health data
  - Appointment scheduling

- **Medical Records**
  - Detailed visit documentation
  - Vital signs tracking
  - Lab results management
  - Treatment plans and prescriptions

## Technical Stack
- **Frontend**: React with TypeScript, Material-UI
- **Backend**: Node.js, Express
- **Database**: PostgreSQL
- **Authentication**: JWT (JSON Web Tokens)

## Database Schema
- `users`: Authentication and user management
- `doctors`: Doctor-specific information
- `patients`: Patient demographics and basic health data
- `patient_visits`: Comprehensive visit records
- `medical_records`: Historical medical documentation
- `prescriptions`: Medication records

## Recent Updates
- Fixed doctor dashboard loading issue
- Implemented proper linking between user and doctor records
- Added comprehensive patient visit tracking
- Enhanced appointment management system

## Setup Instructions

### Prerequisites
- Node.js (v14 or higher)
- PostgreSQL (v12 or higher)
- npm or yarn

### Database Setup
1. Create a PostgreSQL database named 'medapp'
2. Run the schema setup scripts:
   ```bash
   psql -d medapp -f server/sql/db.sql
   psql -d medapp -f server/sql/update_schema.sql
   ```
3. Run the password management migration:
   ```bash
   cd server
   node scripts/run_password_management_migration.js
   ```

### Backend Setup
1. Navigate to the server directory:
   ```bash
   cd server
   ```
2. Install dependencies:
   ```bash
   npm install
   ```
3. Create a .env file with the following:
   ```
   HTTP_PORT=5000
   HTTPS_PORT=5001
   JWT_SECRET=your_jwt_secret
   DB_USER=your_db_user
   DB_PASSWORD=your_db_password
   DB_HOST=localhost
   DB_PORT=5432
   DB_NAME=medapp
   NODE_ENV=development
   ```
4. Generate self-signed certificates for HTTPS (optional but recommended):
   ```bash
   npm run generate-certs
   ```
5. Start the server:
   ```bash
   npm start
   ```

   The server will start in HTTP mode if certificates are not found, or in both HTTP and HTTPS modes if certificates are available.

   **Note**: If ports 5000/5001 are already in use, the server will automatically try fallback ports 3000/3001. The client is configured to automatically detect which port the server is running on.

### Frontend Setup
1. Navigate to the client directory:
   ```bash
   cd client
   ```
2. Install dependencies:
   ```bash
   npm install
   ```
3. Configure HTTPS for the React development server by creating or updating `.env` file:
   ```
   # Enable HTTPS for the React development server
   HTTPS=true
   SSL_CRT_FILE=../server/certs/cert.pem
   SSL_KEY_FILE=../server/certs/key.pem

   # API URL - set to use HTTPS
   REACT_APP_API_URL=https://localhost:5001
   ```

   **Note**: Make sure you've generated certificates in the server directory first.

4. Start the development server:
   ```bash
   npm start
   ```

   The React development server will start using HTTPS. You may need to accept the self-signed certificate warning in your browser.

## Default Users
The system comes with pre-configured users for testing:

1. Doctor Account:
   - Username: dr.smith
   - Email: <EMAIL>
   - Password: doctor123

2. Admin Account:
   - Username: admin
   - Email: <EMAIL>
   - Password: admin123

## API Documentation
The API endpoints are organized into the following categories:

### Authentication Routes
- POST `/api/auth/login`: User login
- POST `/api/auth/register`: User registration
- GET `/api/auth/user`: Get authenticated user data
- POST `/api/auth/change-password`: Change user password
- GET `/api/auth/password-policy`: Get current password policy
- POST `/api/auth/validate-password`: Validate a password against policy
- POST `/api/auth/reset-password`: Reset user password (admin function)

### Admin Routes
- GET `/api/admin/password-policy`: Get current password policy (admin only)
- PUT `/api/admin/password-policy`: Update password policy (admin only)
- POST `/api/admin/users/create-with-default-password`: Create user with default password

### Doctor Routes
- GET `/api/doctors/dashboard-stats/:doctorId`: Get doctor's dashboard statistics
- GET `/api/doctors/upcoming-appointments/:doctorId`: Get upcoming appointments
- GET `/api/doctors/:id/patients`: Get doctor's patients

### Patient Routes
- GET `/api/patients`: Get all patients
- POST `/api/patients`: Create new patient
- GET `/api/patients/:id`: Get patient details
- PUT `/api/patients/:id`: Update patient information

### Visit Routes
- GET `/api/visits/patient/:patientId`: Get patient visits
- POST `/api/visits`: Create new visit record
- GET `/api/visits/:id`: Get visit details

## Security
- JWT-based authentication
- Password hashing using bcrypt
- Role-based access control
- Input validation and sanitization
- Configurable password policies with enforcement
- First-time login password change requirement
- Password history tracking to prevent reuse
- Password expiration with forced renewal
- Default password generation for new users
- HTTPS with TLS for secure communication
- HTTP Strict Transport Security (HSTS) headers
- Automatic HTTP to HTTPS redirection

## Contributing
1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License
This project is licensed under the MIT License.