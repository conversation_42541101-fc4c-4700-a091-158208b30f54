# BEERS Criteria Implementation Guide

## Table of Contents
1. [Overview](#overview)
2. [Background](#background)
3. [Implementation](#implementation)
   - [Database Schema](#database-schema)
   - [Data Structure](#data-structure)
   - [Categories](#categories)
4. [Usage](#usage)
   - [Medication Checking](#medication-checking)
   - [Alerts and Overrides](#alerts-and-overrides)
   - [Reference Guide](#reference-guide)
5. [Technical Details](#technical-details)
   - [API Endpoints](#api-endpoints)
   - [Models](#models)
   - [Frontend Components](#frontend-components)
6. [Maintenance](#maintenance)
   - [Updating Criteria](#updating-criteria)
   - [Adding New Categories](#adding-new-categories)
7. [References](#references)

## Overview

The BEERS Criteria implementation in our medical application provides healthcare providers with guidance on potentially inappropriate medications for older adults. This implementation is based on the 2023 American Geriatrics Society BEERS Criteria® for Potentially Inappropriate Medication Use in Older Adults.

The system:
- Checks prescribed medications against the BEERS criteria
- Generates alerts for potentially inappropriate medications
- Provides recommendations and rationales for avoiding certain medications
- Allows healthcare providers to override alerts with documented reasons
- Serves as a reference guide for medication safety in older adults

## Background

The American Geriatrics Society (AGS) BEERS Criteria® is a list of potentially inappropriate medications that are typically best avoided by older adults in most circumstances or under specific situations, such as in certain diseases or conditions.

First published in 1991 and regularly updated, the BEERS Criteria is an important tool for healthcare providers to improve medication safety in older adults. The criteria are used to identify medications that pose potential risks outweighing potential benefits for people 65 years of age or older.

Our implementation is based on the 2023 update of the BEERS Criteria, which includes:
- Medications to avoid in older adults
- Medications to use with caution
- Medications to avoid with specific diseases or conditions
- Potentially clinically important drug-drug interactions
- Medications to avoid or adjust dosage based on kidney function

## Implementation

### Implementation Process

The BEERS criteria implementation followed these steps:

1. **Research and Analysis**:
   - Reviewed the 2023 American Geriatrics Society BEERS Criteria
   - Analyzed the Nursing Center's 2023 BEERS Criteria Guideline Summary
   - Identified key categories and data structure requirements

2. **Database Design**:
   - Created a dedicated table for BEERS criteria
   - Added fields to patient_visits table for alerts and overrides
   - Designed a flexible schema to accommodate all types of criteria

3. **Data Population**:
   - Created a script to populate the database with all criteria
   - Organized data by categories (avoid, use_with_caution, etc.)
   - Included detailed recommendations and rationales

4. **Backend Implementation**:
   - Developed models for accessing and manipulating criteria
   - Created API endpoints for retrieving criteria and checking medications
   - Implemented logic for generating alerts based on patient data

5. **Frontend Development**:
   - Built components for displaying alerts and criteria
   - Created forms for overriding alerts
   - Developed a comprehensive reference guide

6. **Testing and Validation**:
   - Verified all criteria were correctly imported
   - Tested medication checking with various scenarios
   - Validated alert generation and override functionality

### Database Schema

The BEERS criteria are stored in a dedicated table in the database with the following schema:

```sql
CREATE TABLE beers_criteria (
    criteria_id SERIAL PRIMARY KEY,
    medication_name VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL,
    condition_name VARCHAR(100),
    interacting_medication VARCHAR(100),
    recommendation TEXT NOT NULL,
    rationale TEXT NOT NULL,
    quality_of_evidence VARCHAR(20),
    strength_of_recommendation VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

Additionally, the `patient_visits` table includes fields for storing BEERS criteria alerts and overrides:

```sql
ALTER TABLE patient_visits ADD COLUMN beers_criteria_alerts TEXT;
ALTER TABLE patient_visits ADD COLUMN beers_criteria_overrides TEXT;
```

The alerts and overrides are stored as JSON strings, allowing for flexible and extensible data storage.

### Data Structure

Each BEERS criterion includes:

- **Medication Name**: The name of the medication or medication class
- **Category**: The type of criterion (avoid, use_with_caution, etc.)
- **Condition Name**: For disease-specific criteria, the condition to avoid the medication with
- **Interacting Medication**: For drug-drug interactions, the medication to avoid concurrent use with
- **Recommendation**: Specific guidance for healthcare providers
- **Rationale**: The reason for the recommendation
- **Quality of Evidence**: The strength of the evidence supporting the recommendation (high, moderate, low)
- **Strength of Recommendation**: How strongly the recommendation is made (strong, weak, insufficient)

### Categories

The BEERS criteria are organized into five main categories:

1. **Avoid** (`avoid`): Medications to avoid in older adults regardless of diseases or conditions
   - Includes anticholinergics, antispasmodics, antihistamines, antipsychotics, benzodiazepines, and other medications with high risk in older adults
   - Example medications: Diphenhydramine, Amitriptyline, Diazepam, Glimepiride

2. **Use with Caution** (`use_with_caution`): Medications that should be used with caution in older adults
   - Includes antithrombotics, SIADH-causing medications, and SGLT2 inhibitors
   - Example medications: Aspirin (in adults ≥70 years), Dabigatran, Prasugrel, SSRIs, Tramadol, Dextromethorphan-quinidine

3. **Disease Interaction** (`disease_interaction`): Medications to avoid in older adults with specific diseases or conditions
   - Organized by disease/condition: Heart failure, Syncope, Delirium, Dementia, History of falls, Parkinson disease, Gastric/duodenal ulcers, Urinary incontinence, Lower urinary tract symptoms, SIADH/hyponatremia
   - Example interactions: NSAIDs in heart failure, Anticholinergics in dementia, Antipsychotics in Parkinson disease

4. **Drug Interaction** (`drug_interaction`): Potentially clinically important drug-drug interactions to avoid
   - Includes interactions between RAS inhibitors, opioids with benzodiazepines or gabapentinoids, multiple anticholinergics, CNS-active drugs, and specific medication combinations
   - Example interactions: Warfarin with certain antibiotics, Lithium with ACE inhibitors, Opioids with benzodiazepines

5. **Adjust for Kidney Function** (`adjust_for_kidney_function`): Medications to avoid or adjust dosage based on kidney function
   - Includes antimicrobials, cardiovascular medications, anticoagulants, CNS medications, pain medications, and GI medications
   - Example medications: Ciprofloxacin, Nitrofurantoin, Dabigatran, Gabapentin, NSAIDs, Colchicine

## Usage

### Clinical Workflow Integration

The BEERS criteria are integrated into the clinical workflow in several ways:

1. **During Medication Prescribing**:
   - When a provider enters a new medication for a patient ≥65 years old, the system automatically checks it against the BEERS criteria
   - Alerts appear in real-time before the prescription is finalized
   - Providers can make informed decisions at the point of care

2. **During Medication Review**:
   - When reviewing a patient's medication list, existing medications are checked against the BEERS criteria
   - Alerts for potentially inappropriate medications are displayed
   - This facilitates regular medication reviews and deprescribing when appropriate

3. **During Patient Visits**:
   - The system checks all medications at each visit
   - New alerts may appear if the patient's conditions have changed
   - This ensures ongoing medication safety monitoring

4. **In Clinical Decision Support**:
   - The BEERS criteria are part of the broader clinical decision support system
   - They work alongside other medication safety tools (drug interactions, allergies, etc.)
   - This provides comprehensive medication safety checks

### Medication Checking

When medications are prescribed to a patient 65 years or older, the system automatically checks them against the BEERS criteria. The check considers:

- **The patient's age**: Only applies to patients ≥65 years
- **The patient's medical conditions**: For disease-specific criteria
- **Other medications the patient is taking**: For drug-drug interactions
- **The patient's kidney function**: For medications that need adjustment based on renal function
- **The medication dose**: For dose-dependent criteria

The checking algorithm follows these steps:
1. Identify if the patient is ≥65 years old
2. Check the medication against the "avoid" category
3. Check the medication against the "use_with_caution" category
4. Check for disease interactions based on the patient's conditions
5. Check for drug-drug interactions with other medications
6. Check if kidney function adjustments are needed based on lab values

If a potential issue is identified, an alert is generated with:
- The medication name
- The type of issue (e.g., "avoid in older adults", "drug interaction")
- The recommendation
- The rationale
- The quality of evidence and strength of recommendation

### Alerts and Overrides

Alerts are displayed prominently when viewing a patient's medications. They are color-coded by severity:
- **Red**: Strong recommendations to avoid
- **Yellow**: Use with caution recommendations
- **Orange**: Recommendations to adjust dosage

Healthcare providers can:

1. **Review the alert**: See details about why the medication might be inappropriate
   - View the full recommendation and rationale
   - See the quality of evidence and strength of recommendation
   - Access references and additional information

2. **Override the alert**: If they determine the benefit outweighs the risk, they can override with a documented reason
   - Select from common override reasons or enter a custom reason
   - Document the clinical justification
   - Specify a review date if the override is temporary

3. **Change the medication**: Based on the alert, they may choose an alternative medication
   - The system may suggest safer alternatives
   - The provider can select a different medication
   - The new medication is also checked against the BEERS criteria

All overrides are logged for audit purposes and include:
- The healthcare provider who made the override
- The date and time of the override
- The reason for the override
- The clinical justification
- The review date (if applicable)

### Reference Guide

The BEERS Criteria Reference Guide provides a comprehensive view of all criteria, organized by category. Healthcare providers can:

- **Browse all criteria by category**:
  - View medications to avoid in older adults
  - View medications to use with caution
  - View disease-specific criteria
  - View drug-drug interactions
  - View kidney function adjustments

- **Search for specific medications**:
  - Find all criteria related to a specific medication
  - See all categories a medication appears in
  - View detailed recommendations for each criterion

- **Filter by disease/condition**:
  - See all medications to avoid with a specific condition
  - Filter by multiple conditions for complex patients

- **View detailed recommendations and rationales**:
  - Read the full recommendation text
  - Understand the rationale behind each recommendation
  - See the quality of evidence and strength of recommendation

The Reference Guide also includes educational resources:
- Links to the official AGS BEERS Criteria publication
- Clinical pearls for geriatric medication management
- Best practices for deprescribing

This serves as an educational resource and reference tool for medication safety in older adults, promoting evidence-based prescribing practices.

## Technical Details

### API Endpoints

The following API endpoints are available for interacting with the BEERS criteria:

#### Retrieval Endpoints

- `GET /api/beers-criteria`: Get all BEERS criteria
  - Optional query parameters: `limit`, `offset` for pagination
  - Returns an array of criteria objects and total count

- `GET /api/beers-criteria/:id`: Get a specific criterion by ID
  - Path parameter: `id` (integer)
  - Returns a single criterion object or 404 if not found

- `GET /api/beers-criteria/medication/:name`: Get criteria by medication name
  - Path parameter: `name` (string)
  - Uses partial matching (ILIKE %name%)
  - Returns an array of criteria objects

- `GET /api/beers-criteria/category/:category`: Get criteria by category
  - Path parameter: `category` (string)
  - Valid categories: `avoid`, `use_with_caution`, `disease_interaction`, `drug_interaction`, `adjust_for_kidney_function`
  - Returns an array of criteria objects

- `GET /api/beers-criteria/condition/:condition`: Get criteria by condition
  - Path parameter: `condition` (string)
  - Uses partial matching (ILIKE %condition%)
  - Returns an array of criteria objects

#### Functional Endpoints

- `POST /api/beers-criteria/check`: Check medications against BEERS criteria
  - Request body:
    ```json
    {
      "medications": ["medication1", "medication2", ...],
      "patientData": {
        "age": 75,
        "conditions": ["condition1", "condition2", ...],
        "kidney_function": {
          "egfr": 45,
          "creatinine": 1.2
        },
        "other_medications": ["medication3", "medication4", ...]
      }
    }
    ```
  - Returns an array of alert objects

- `POST /api/beers-criteria/override`: Override a BEERS criteria alert
  - Request body:
    ```json
    {
      "visitId": 123,
      "alertId": 456,
      "reason": "Benefit outweighs risk",
      "notes": "Patient has been stable on this medication for years",
      "reviewDate": "2023-12-31"
    }
    ```
  - Returns the saved override object

#### Administrative Endpoints (Admin Only)

- `POST /api/beers-criteria`: Create a new BEERS criterion
  - Requires admin authentication
  - Request body includes all criterion fields
  - Returns the created criterion object

- `PUT /api/beers-criteria/:id`: Update a BEERS criterion
  - Requires admin authentication
  - Path parameter: `id` (integer)
  - Request body includes fields to update
  - Returns the updated criterion object

- `DELETE /api/beers-criteria/:id`: Delete a BEERS criterion
  - Requires admin authentication
  - Path parameter: `id` (integer)
  - Returns success message

### Models

The `BeersCriteria` model provides methods for interacting with the BEERS criteria data:

#### Retrieval Methods

- `getAll(limit = null, offset = 0)`: Get all criteria with optional pagination
- `getById(criteriaId)`: Get a specific criterion by ID
- `getByMedicationName(medicationName)`: Get criteria by medication name (partial match)
- `getByCategory(category)`: Get criteria by category
- `getByCondition(condition)`: Get criteria by condition (partial match)

#### Medication Checking Methods

- `checkMedications(medications, patientData)`: Check medications against criteria
  - Analyzes patient data (age, conditions, other medications, kidney function)
  - Returns an array of alerts for potentially inappropriate medications

- `criterionApplies(criterion, patientData)`: Determine if a criterion applies to a patient
  - Checks category-specific conditions (e.g., disease interactions, kidney function)
  - Returns boolean indicating if the criterion applies

- `generateAlertMessage(criterion)`: Generate a user-friendly alert message
  - Creates a formatted message based on the criterion type
  - Includes recommendation and rationale

#### Alert Management Methods

- `saveAlerts(visitId, alerts)`: Save alerts for a patient visit
  - Stores alerts as JSON in the patient_visits table
  - Returns boolean indicating success

- `getAlerts(visitId)`: Get alerts for a patient visit
  - Retrieves and parses stored JSON alerts
  - Returns an array of alert objects

- `saveOverride(visitId, override)`: Save an override for an alert
  - Stores override information with the alert
  - Returns the saved override object

- `getOverrides(visitId)`: Get overrides for a patient visit
  - Retrieves and parses stored JSON overrides
  - Returns an array of override objects

#### Administrative Methods

- `create(criteriaData)`: Create a new BEERS criterion
- `update(criteriaId, criteriaData)`: Update a BEERS criterion
- `delete(criteriaId)`: Delete a BEERS criterion

### Frontend Components

The BEERS criteria implementation includes several frontend components:

#### Alert Components

- `BeersCriteriaAlerts`: Displays alerts for potentially inappropriate medications
  - Props:
    - `alerts`: Array of alert objects
    - `onOverride`: Function to handle override action
    - `onDismiss`: Function to handle dismiss action
  - Features:
    - Color-coded by severity
    - Expandable details
    - Override and dismiss buttons

- `BeersCriteriaOverrideForm`: Form for overriding alerts with reasons
  - Props:
    - `alert`: Alert object to override
    - `onSubmit`: Function to handle form submission
    - `onCancel`: Function to handle cancellation
  - Features:
    - Dropdown of common override reasons
    - Custom reason text input
    - Clinical notes text area
    - Optional review date picker

#### Reference Components

- `BeersCriteriaReference`: Reference guide for browsing all criteria
  - Props:
    - `criteria`: Array of criteria objects (optional, can load from API)
    - `initialCategory`: Initial category to display (optional)
  - Features:
    - Category tabs
    - Searchable medication list
    - Filterable by condition
    - Detailed view of selected criterion

- `BeersCriteriaDetail`: Displays detailed information about a criterion
  - Props:
    - `criterion`: Criterion object to display
  - Features:
    - Formatted recommendation and rationale
    - Evidence quality and recommendation strength indicators
    - Related medications or conditions

#### Integration Components

- `MedicationBeersCriteriaCheck`: Checks medications against BEERS criteria
  - Props:
    - `medications`: Array of medication strings
    - `patientData`: Patient data object
    - `onAlertsGenerated`: Function to handle generated alerts
  - Features:
    - Automatic checking on mount and when medications change
    - Loading state during API calls
    - Error handling

- `PatientVisitBeersCriteria`: Manages BEERS criteria for a patient visit
  - Props:
    - `visitId`: Patient visit ID
    - `patientData`: Patient data object
    - `medications`: Array of medication strings
  - Features:
    - Loads existing alerts and overrides
    - Checks medications against criteria
    - Manages alert state and overrides

## Maintenance

### Updating Criteria

The BEERS criteria are updated periodically by the American Geriatrics Society (typically every 3-5 years). To update the criteria in our system:

1. **Prepare the Update**:
   - Review the new AGS BEERS Criteria publication
   - Identify changes from the previous version
   - Document new medications, removed medications, and changed recommendations

2. **Update the Data**:
   - Modify the `populate_beers_criteria.js` script with the new criteria
   - Add new medications and categories as needed
   - Update recommendations, rationales, and evidence levels
   - Remove deprecated criteria

3. **Run the Update Script**:
   ```bash
   node scripts/populate_beers_criteria.js
   ```

4. **Verify the Update**:
   - Check the database to ensure all criteria were updated correctly
   - Verify counts for each category
   - Test the medication checking functionality with the new criteria

5. **Update Documentation**:
   - Update the reference guide with the new criteria
   - Document the version and date of the BEERS criteria being used
   - Provide release notes highlighting major changes

6. **Communicate Changes**:
   - Notify users about the updated criteria
   - Highlight significant changes that may affect patient care
   - Provide training if necessary

### Adding New Categories

If new categories are added to the BEERS criteria:

1. **Database Updates**:
   - No schema changes are needed as the category is a string field
   - Add the new category to the `populate_beers_criteria.js` script
   - Run the script to update the database

2. **Backend Updates**:
   - Update the `criterionApplies` method in the `BeersCriteria` model to handle the new category
   - Update the `generateAlertMessage` method to create appropriate messages for the new category
   - Add any new logic needed for checking medications against the new category

3. **Frontend Updates**:
   - Update the `getCategoryLabel` function in `BeersCriteriaReference.tsx` to include the new category:
     ```typescript
     const getCategoryLabel = (category: string): string => {
       switch (category) {
         // Existing categories...
         case 'new_category':
           return 'New Category Display Name';
         default:
           return category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
       }
     };
     ```
   - Add styling for the new category in the CSS files
   - Update the category filter in the reference guide

4. **Testing**:
   - Test the display of the new category in the reference guide
   - Test medication checking with medications in the new category
   - Test alert generation and override functionality

### Monitoring and Optimization

Regular monitoring and optimization of the BEERS criteria implementation helps ensure its effectiveness:

1. **Usage Monitoring**:
   - Track how often alerts are generated
   - Monitor which criteria trigger alerts most frequently
   - Analyze override patterns and reasons

2. **Performance Optimization**:
   - Monitor query performance for medication checks
   - Optimize database indexes if needed
   - Consider caching frequently accessed criteria

3. **User Feedback**:
   - Collect feedback from healthcare providers on alert usefulness
   - Identify potential false positives or alert fatigue issues
   - Make adjustments to improve clinical relevance

4. **Clinical Impact Assessment**:
   - Evaluate the impact on prescribing patterns
   - Assess reduction in potentially inappropriate medications
   - Measure outcomes related to medication safety

## References

### Official Guidelines and Publications

1. American Geriatrics Society 2023 Updated AGS Beers Criteria® for Potentially Inappropriate Medication Use in Older Adults. Journal of the American Geriatrics Society, 2023.
2. Nursing Center's 2023 BEERS Criteria Guideline Summary
3. The American Geriatrics Society: [https://www.americangeriatrics.org](https://www.americangeriatrics.org)

### Implementation Resources

4. Fick DM, Semla TP, Steinman M, et al. American Geriatrics Society 2023 Updated AGS Beers Criteria® for Potentially Inappropriate Medication Use in Older Adults. J Am Geriatr Soc. 2023;71(12):3724-3748.
5. By the 2019 American Geriatrics Society Beers Criteria® Update Expert Panel. American Geriatrics Society 2019 Updated AGS Beers Criteria® for Potentially Inappropriate Medication Use in Older Adults. J Am Geriatr Soc. 2019;67(4):674-694.
6. O'Mahony D. STOPP/START criteria for potentially inappropriate medications/potential prescribing omissions in older people: origin and progress. Expert Rev Clin Pharmacol. 2020;13(1):15-22.

### Technical Documentation

7. PostgreSQL Documentation: [https://www.postgresql.org/docs/](https://www.postgresql.org/docs/)
8. Express.js Documentation: [https://expressjs.com/](https://expressjs.com/)
9. React Documentation: [https://reactjs.org/docs/getting-started.html](https://reactjs.org/docs/getting-started.html)
10. TypeScript Documentation: [https://www.typescriptlang.org/docs/](https://www.typescriptlang.org/docs/)

### Related Clinical Resources

11. Hanlon JT, Schmader KE. The medication appropriateness index at 20: where it started, where it has been, and where it may be going. Drugs Aging. 2013;30(11):893-900.
12. Rochon PA. Drug prescribing for older adults. UpToDate. Accessed January 2023.
13. Steinman MA, Fick DM. Using wisely: a reminder on the proper use of the American Geriatrics Society Beers Criteria®. J Am Geriatr Soc. 2019;67(4):644-646.
14. 2023 American Geriatrics Society Beers Criteria® Update Expert Panel. American Geriatrics Society 2023 Updated Beers Criteria® for Potentially Inappropriate Medication Use in Older Adults. J Am Geriatr Soc. 2023;71(12):3724-3748.
