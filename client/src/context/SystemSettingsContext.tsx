import React, { createContext, useState, useEffect, ReactNode } from 'react';
import axios from 'axios';
import { API_URL } from '../config';

// Define the shape of our system settings
export interface SystemSettings {
  setting_id?: number;
  system_name: string;
  timezone: string;
  date_format: string;
  maintenance_mode: boolean;
  session_timeout: number;
  language: string;
  currency: string;
  created_at?: string;
  updated_at?: string;
}

// Define the context type
interface SystemSettingsContextType {
  settings: SystemSettings;
  loading: boolean;
  error: string | null;
  refreshSettings: () => Promise<void>;
}

// Create the context with default values
const SystemSettingsContext = createContext<SystemSettingsContextType>({
  settings: {
    system_name: 'Medical Records System',
    timezone: 'UTC',
    date_format: 'MM/DD/YYYY',
    maintenance_mode: false,
    session_timeout: 30,
    language: 'en',
    currency: 'USD'
  },
  loading: true,
  error: null,
  refreshSettings: async () => {}
});

interface SystemSettingsProviderProps {
  children: ReactNode;
}

export const SystemSettingsProvider: React.FC<SystemSettingsProviderProps> = ({ children }) => {
  // Try to get settings from localStorage first
  const getInitialSettings = (): SystemSettings => {
    try {
      const storedSettings = localStorage.getItem('systemSettings');
      if (storedSettings) {
        const parsedSettings = JSON.parse(storedSettings);
        console.log('Using settings from localStorage:', parsedSettings);
        return parsedSettings;
      }
    } catch (error) {
      console.error('Error reading settings from localStorage:', error);
    }

    // Default settings if nothing in localStorage
    return {
      system_name: 'Medical Records System',
      timezone: 'UTC',
      date_format: 'MM/DD/YYYY',
      maintenance_mode: false,
      session_timeout: 30,
      language: 'en',
      currency: 'USD'
    };
  };

  const [settings, setSettings] = useState<SystemSettings>(getInitialSettings());
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Function to fetch settings from the server
  const fetchSettings = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        // If not authenticated, use default settings
        setLoading(false);
        return;
      }

      try {
        // First try the regular settings endpoint
        const response = await axios.get(`${API_URL}/api/settings`, {
          headers: {
            'x-auth-token': token
          }
        });

        console.log('Fetched system settings:', response.data);
        setSettings(response.data);
        // Store settings in localStorage for use by other components
        localStorage.setItem('systemSettings', JSON.stringify(response.data));
        setError(null);
      } catch (settingsErr) {
        console.warn('Error fetching from /api/settings, trying admin endpoint:', settingsErr);

        // If that fails, try the admin endpoint as fallback
        try {
          const adminResponse = await axios.get(`${API_URL}/api/admin/system-settings`, {
            headers: {
              'x-auth-token': token
            }
          });

          console.log('Fetched system settings from admin endpoint:', adminResponse.data);
          setSettings(adminResponse.data);
          // Store settings in localStorage for use by other components
          localStorage.setItem('systemSettings', JSON.stringify(adminResponse.data));
          setError(null);
        } catch (adminErr) {
          console.error('Error fetching from admin endpoint:', adminErr);
          throw adminErr; // Re-throw to be caught by outer catch
        }
      }
    } catch (err) {
      console.error('Error fetching system settings:', err);
      setError('Failed to load system settings');
    } finally {
      setLoading(false);
    }
  };

  // Fetch settings on component mount
  useEffect(() => {
    fetchSettings();
  }, []);

  return (
    <SystemSettingsContext.Provider
      value={{
        settings,
        loading,
        error,
        refreshSettings: fetchSettings
      }}
    >
      {children}
    </SystemSettingsContext.Provider>
  );
};

export default SystemSettingsContext;
