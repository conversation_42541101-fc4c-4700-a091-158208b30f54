import React, { createContext, useState, useContext, ReactNode } from 'react';

interface AdminLayoutContextType {
  sidebarOpen: boolean;
  toggleSidebar: () => void;
  setSidebarOpen: (open: boolean) => void;
}

// Create context with default values
const AdminLayoutContext = createContext<AdminLayoutContextType>({
  sidebarOpen: true,
  toggleSidebar: () => {},
  setSidebarOpen: () => {},
});

// Custom hook to use the admin layout context
export const useAdminLayout = () => useContext(AdminLayoutContext);

interface AdminLayoutProviderProps {
  children: ReactNode;
}

// Provider component
export const AdminLayoutProvider: React.FC<AdminLayoutProviderProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(true);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <AdminLayoutContext.Provider
      value={{
        sidebarOpen,
        toggleSidebar,
        setSidebarOpen,
      }}
    >
      {children}
    </AdminLayoutContext.Provider>
  );
};

export default AdminLayoutContext;
