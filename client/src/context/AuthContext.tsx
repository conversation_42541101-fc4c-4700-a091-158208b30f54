import React, { createContext, useState, useEffect, ReactNode } from 'react';
import axios from 'axios';
import { API_URL } from '../config';
import LoadingSpinner from '../components/common/LoadingSpinner';

// Define PasswordChange interface
interface PasswordChange {
  required: boolean;
  reason?: string;
  expiryDate?: string;
}

// Define User interface locally
interface User {
  user_id: number;
  username: string;
  email: string;
  role: string;
  doctor_id?: number | null;
  patient_id?: number | null;
  created_at?: string;
  passwordChange?: PasswordChange;
  is_first_login?: boolean;
  default_password?: boolean;
}

interface AuthContextType {
  token: string | null;
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  error: string | null;
  passwordChangeRequired: boolean;
  login: (emailOrUsername: string, password: string) => Promise<void>;
  logout: () => void;
  clearError: () => void;
}

// Set auth token in axios defaults
const setAuthToken = (token: string | null) => {
  if (token) {
    axios.defaults.headers.common['x-auth-token'] = token;
    localStorage.setItem('token', token);
    console.log('Token set in axios defaults:', token.substring(0, 15) + '...');
  } else {
    delete axios.defaults.headers.common['x-auth-token'];
    localStorage.removeItem('token');
    console.log('Token removed from axios defaults');
  }
};

// Configure axios baseURL if not already set
if (!axios.defaults.baseURL) {
  axios.defaults.baseURL = API_URL;
  console.log('Axios baseURL set to:', API_URL);
}

const AuthContext = createContext<AuthContextType>({
  token: null,
  isAuthenticated: false,
  user: null,
  loading: true,
  error: null,
  passwordChangeRequired: false,
  login: async () => {},
  logout: () => {},
  clearError: () => {},
});

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [token, setToken] = useState<string | null>(localStorage.getItem('token'));
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [passwordChangeRequired, setPasswordChangeRequired] = useState<boolean>(false);

  // Load user
  const loadUser = async () => {
    const storedToken = localStorage.getItem('token');
    if (storedToken) {
      setAuthToken(storedToken);
      setLoading(true);

      try {
        console.log('Loading user with token:', storedToken.substring(0, 15) + '...');
        console.log('User endpoint URL:', `${API_URL}/api/auth/user`);

        const res = await axios.get(`${API_URL}/api/auth/user`, {
          headers: {
            'x-auth-token': storedToken,
            'Content-Type': 'application/json'
          }
        });

        console.log('User data loaded:', res.data);
        setUser(res.data);
        setIsAuthenticated(true);
        setToken(storedToken);

        // Check if password change is required
        if (res.data.passwordChange && res.data.passwordChange.required) {
          console.log('Password change required:', res.data.passwordChange);
          setPasswordChangeRequired(true);
        } else {
          setPasswordChangeRequired(false);
        }
      } catch (err: any) {
        console.error('Error loading user:', err);

        if (err.response) {
          console.error('Error response data:', err.response.data);
          console.error('Error response status:', err.response.status);
        }

        // Clear authentication state on error
        localStorage.removeItem('token');
        setToken(null);
        setUser(null);
        setIsAuthenticated(false);
        setAuthToken(null);
      } finally {
        setLoading(false);
      }
    } else {
      console.log('No token found in localStorage');
      setToken(null);
      setUser(null);
      setIsAuthenticated(false);
      setAuthToken(null);
      setLoading(false);
    }
  };

  // Registration has been removed - users can only be created by administrators

  // Login user
  const login = async (emailOrUsername: string, password: string) => {
    try {
      console.log('Login attempt with:', { emailOrUsername });
      console.log('Using API URL:', API_URL);
      setLoading(true);

      // Explicitly create the full URL for the login endpoint
      const loginUrl = `${API_URL}/api/auth/login`;
      console.log('Login URL:', loginUrl);

      const response = await axios.post(loginUrl, {
        email: emailOrUsername,
        password,
      });

      console.log('Login response status:', response.status);
      console.log('Login response data:', response.data);

      if (response.data.token) {
        // Set token in localStorage and axios defaults
        console.log('Setting auth token from login response');
        setAuthToken(response.data.token);
        setToken(response.data.token);

        // Load user data after successful login
        console.log('Loading user data after successful login');
        await loadUser();

        setIsAuthenticated(true);
        setError(null);
        console.log('Login successful, authentication state updated');
        return response.data;
      } else {
        console.error('Login response did not contain a token');
        throw new Error('Login failed - no token received');
      }
    } catch (err: any) {
      console.error('Login error:', err);

      // More detailed error reporting
      if (err.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        console.error('Error response data:', err.response.data);
        console.error('Error response status:', err.response.status);

        let errorMessage = 'Invalid credentials';
        if (err.response?.data?.msg) {
          errorMessage = err.response.data.msg;
        }

        // Always add attempt information to the error message if available
        // The Login component will handle displaying it appropriately
        if (err.response?.data?.attemptsUsed && err.response?.data?.maxAttempts) {
          // If the message already contains attempt info, don't duplicate it
          if (!errorMessage.includes(`Attempt ${err.response?.data?.attemptsUsed} of ${err.response?.data?.maxAttempts}`)) {
            errorMessage += ` (Attempt ${err.response?.data?.attemptsUsed} of ${err.response?.data?.maxAttempts})`;
          }
        }

        setError(errorMessage);
      } else if (err.request) {
        // The request was made but no response was received
        console.error('Error request:', err.request);
        console.error('Network error details:', {
          url: `${API_URL}/api/auth/login`,
          method: 'POST',
          data: { email: emailOrUsername, password: '******' }
        });
        setError('Network error - no response from server. Please check console for details.');
      } else {
        // Something happened in setting up the request
        console.error('Error message:', err.message);
        console.error('Error details:', err);
        setError(`Login error: ${err.message}`);
      }

      setAuthToken(null);
      setToken(null);
      setIsAuthenticated(false);
      setLoading(false);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Logout
  const logout = () => {
    setAuthToken(null);
    setIsAuthenticated(false);
    setUser(null);
    setError(null);
  };

  // Clear error
  const clearError = () => {
    setError(null);
  };

  // Set initial auth token
  useEffect(() => {
    const storedToken = localStorage.getItem('token');
    if (storedToken) {
      setAuthToken(storedToken);
    }
    loadUser();
  }, []);

  if (loading) {
    return <LoadingSpinner fullScreen size="large" message="Loading application..." />;
  }

  return (
    <AuthContext.Provider
      value={{
        token,
        isAuthenticated,
        user,
        loading,
        error,
        passwordChangeRequired,
        login,
        logout,
        clearError
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;