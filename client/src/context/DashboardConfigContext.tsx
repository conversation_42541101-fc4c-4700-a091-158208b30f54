import React, { createContext, useState, useEffect, useContext, ReactNode } from 'react';
import { WidgetConfig } from '../components/admin/dashboard/DashboardWidgetGrid';
import AuthContext from './AuthContext';
import { API_URL } from '../config';

interface DashboardConfig {
  id?: number;
  user_id?: number;
  name: string;
  is_default: boolean;
  widgets: WidgetConfig[];
  created_at?: string;
  updated_at?: string;
}

interface DashboardConfigContextType {
  dashboards: DashboardConfig[];
  currentDashboard: DashboardConfig | null;
  loading: boolean;
  error: string | null;
  setCurrentDashboard: (dashboard: DashboardConfig) => void;
  addDashboard: (dashboard: Omit<DashboardConfig, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => Promise<DashboardConfig>;
  updateDashboard: (dashboard: DashboardConfig) => Promise<DashboardConfig>;
  deleteDashboard: (id: number) => Promise<void>;
  addWidget: (widget: WidgetConfig) => Promise<void>;
  updateWidget: (widget: WidgetConfig) => Promise<void>;
  removeWidget: (widgetId: string) => Promise<void>;
  moveWidget: (widgetId: string, direction: 'up' | 'down') => Promise<void>;
  setDefaultDashboard: (dashboardId: number) => Promise<void>;
  refreshDashboards: () => Promise<void>;
}

const DashboardConfigContext = createContext<DashboardConfigContextType>({
  dashboards: [],
  currentDashboard: null,
  loading: false,
  error: null,
  setCurrentDashboard: () => {},
  addDashboard: async () => ({ name: '', is_default: false, widgets: [] }),
  updateDashboard: async () => ({ name: '', is_default: false, widgets: [] }),
  deleteDashboard: async () => {},
  addWidget: async () => {},
  updateWidget: async () => {},
  removeWidget: async () => {},
  moveWidget: async () => {},
  setDefaultDashboard: async () => {},
  refreshDashboards: async () => {}
});

interface DashboardConfigProviderProps {
  children: ReactNode;
}

export const DashboardConfigProvider: React.FC<DashboardConfigProviderProps> = ({ children }) => {
  const { user } = useContext(AuthContext);
  const [dashboards, setDashboards] = useState<DashboardConfig[]>([]);
  const [currentDashboard, setCurrentDashboard] = useState<DashboardConfig | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Default dashboard configuration
  const defaultDashboardConfig: DashboardConfig = {
    name: 'Default Dashboard',
    is_default: true,
    widgets: [
      {
        id: 'users-stats',
        type: 'statistic',
        title: 'Total Users',
        width: 3,
        position: 0,
        props: {
          dataKey: 'totalUsers',
          icon: 'PeopleIcon',
          color: 'primary'
        }
      },
      {
        id: 'doctors-stats',
        type: 'statistic',
        title: 'Doctors',
        width: 3,
        position: 1,
        props: {
          dataKey: 'totalDoctors',
          icon: 'DoctorIcon',
          color: 'success'
        }
      },
      {
        id: 'patients-stats',
        type: 'statistic',
        title: 'Patients',
        width: 3,
        position: 2,
        props: {
          dataKey: 'totalPatients',
          icon: 'PatientIcon',
          color: 'warning'
        }
      },
      {
        id: 'visits-stats',
        type: 'statistic',
        title: 'Total Visits',
        width: 3,
        position: 3,
        props: {
          dataKey: 'totalVisits',
          icon: 'StatisticsIcon',
          color: 'info'
        }
      },
      {
        id: 'visits-chart',
        type: 'chart',
        title: 'Patient Visits',
        width: 8,
        position: 4,
        props: {
          chartType: 'line',
          dataKey: 'visitsPerMonth'
        }
      },
      {
        id: 'recent-activity',
        type: 'activity',
        title: 'Recent Activity',
        width: 4,
        position: 5,
        props: {
          dataKey: 'recentActivity',
          maxItems: 5
        }
      }
    ]
  };

  // Fetch dashboards from API
  const fetchDashboards = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/admin/dashboard-config`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch dashboard configurations');
      }

      const data = await response.json();

      if (Array.isArray(data) && data.length > 0) {
        setDashboards(data);

        // Set current dashboard to the default one or the first one
        const defaultDashboard = data.find(d => d.is_default) || data[0];
        setCurrentDashboard(defaultDashboard);
      } else {
        // If no dashboards exist, use the default configuration
        setDashboards([defaultDashboardConfig]);
        setCurrentDashboard(defaultDashboardConfig);
      }

      setError(null);
    } catch (err) {
      console.error('Error fetching dashboard configurations:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');

      // Use default dashboard if API fails
      setDashboards([defaultDashboardConfig]);
      setCurrentDashboard(defaultDashboardConfig);
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch of dashboards
  useEffect(() => {
    if (user) {
      fetchDashboards();
    }
  }, [user, fetchDashboards]);

  // Add a new dashboard
  const addDashboard = async (dashboard: Omit<DashboardConfig, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/admin/dashboard-config`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: JSON.stringify(dashboard)
      });

      if (!response.ok) {
        throw new Error('Failed to create dashboard configuration');
      }

      const newDashboard = await response.json();
      setDashboards([...dashboards, newDashboard]);

      if (dashboard.is_default) {
        setCurrentDashboard(newDashboard);
      }

      return newDashboard;
    } catch (err) {
      console.error('Error creating dashboard configuration:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Update an existing dashboard
  const updateDashboard = async (dashboard: DashboardConfig) => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/admin/dashboard-config/${dashboard.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: JSON.stringify(dashboard)
      });

      if (!response.ok) {
        throw new Error('Failed to update dashboard configuration');
      }

      const updatedDashboard = await response.json();

      setDashboards(dashboards.map(d => d.id === dashboard.id ? updatedDashboard : d));

      if (currentDashboard?.id === dashboard.id) {
        setCurrentDashboard(updatedDashboard);
      }

      return updatedDashboard;
    } catch (err) {
      console.error('Error updating dashboard configuration:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Delete a dashboard
  const deleteDashboard = async (id: number) => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/admin/dashboard-config/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error('Failed to delete dashboard configuration');
      }

      setDashboards(dashboards.filter(d => d.id !== id));

      if (currentDashboard?.id === id) {
        const newCurrentDashboard = dashboards.find(d => d.id !== id);
        setCurrentDashboard(newCurrentDashboard || null);
      }
    } catch (err) {
      console.error('Error deleting dashboard configuration:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Add a widget to the current dashboard
  const addWidget = async (widget: WidgetConfig) => {
    if (!currentDashboard) return;

    try {
      const updatedWidgets = [...currentDashboard.widgets, widget];
      const updatedDashboard = { ...currentDashboard, widgets: updatedWidgets };

      await updateDashboard(updatedDashboard);
    } catch (err) {
      console.error('Error adding widget:', err);
      throw err;
    }
  };

  // Update a widget in the current dashboard
  const updateWidget = async (widget: WidgetConfig) => {
    if (!currentDashboard) return;

    try {
      const updatedWidgets = currentDashboard.widgets.map(w =>
        w.id === widget.id ? widget : w
      );
      const updatedDashboard = { ...currentDashboard, widgets: updatedWidgets };

      await updateDashboard(updatedDashboard);
    } catch (err) {
      console.error('Error updating widget:', err);
      throw err;
    }
  };

  // Remove a widget from the current dashboard
  const removeWidget = async (widgetId: string) => {
    if (!currentDashboard) return;

    try {
      const updatedWidgets = currentDashboard.widgets.filter(w => w.id !== widgetId);
      const updatedDashboard = { ...currentDashboard, widgets: updatedWidgets };

      await updateDashboard(updatedDashboard);
    } catch (err) {
      console.error('Error removing widget:', err);
      throw err;
    }
  };

  // Move a widget up or down in the current dashboard
  const moveWidget = async (widgetId: string, direction: 'up' | 'down') => {
    if (!currentDashboard) return;

    try {
      const widgets = [...currentDashboard.widgets];
      const index = widgets.findIndex(w => w.id === widgetId);

      if (index === -1) return;

      if (direction === 'up' && index > 0) {
        // Swap with the widget above
        [widgets[index - 1], widgets[index]] = [widgets[index], widgets[index - 1]];
      } else if (direction === 'down' && index < widgets.length - 1) {
        // Swap with the widget below
        [widgets[index], widgets[index + 1]] = [widgets[index + 1], widgets[index]];
      }

      // Update positions
      widgets.forEach((widget, i) => {
        widget.position = i;
      });

      const updatedDashboard = { ...currentDashboard, widgets };
      await updateDashboard(updatedDashboard);
    } catch (err) {
      console.error('Error moving widget:', err);
      throw err;
    }
  };

  // Set a dashboard as the default
  const setDefaultDashboard = async (dashboardId: number) => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/admin/dashboard-config/${dashboardId}/set-default`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error('Failed to set default dashboard');
      }

      // Update local state
      setDashboards(dashboards.map(d => ({
        ...d,
        is_default: d.id === dashboardId
      })));

      // Set the current dashboard to the new default
      const newDefault = dashboards.find(d => d.id === dashboardId);
      if (newDefault) {
        setCurrentDashboard({ ...newDefault, is_default: true });
      }
    } catch (err) {
      console.error('Error setting default dashboard:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Refresh dashboards from API
  const refreshDashboards = async () => {
    await fetchDashboards();
  };

  return (
    <DashboardConfigContext.Provider
      value={{
        dashboards,
        currentDashboard,
        loading,
        error,
        setCurrentDashboard,
        addDashboard,
        updateDashboard,
        deleteDashboard,
        addWidget,
        updateWidget,
        removeWidget,
        moveWidget,
        setDefaultDashboard,
        refreshDashboards
      }}
    >
      {children}
    </DashboardConfigContext.Provider>
  );
};

export default DashboardConfigContext;
