import { Doctor } from '../types';
import { get, post, put, del } from '../utils/apiUtils';

/**
 * Doctor service for handling doctor-related API calls
 */

// Get all doctors
export const getDoctors = async (): Promise<Doctor[]> => {
  return get<Doctor[]>('/api/doctors');
};

// Search doctors
export const searchDoctors = async (searchTerm: string): Promise<Doctor[]> => {
  return get<Doctor[]>(`/api/doctors?search=${encodeURIComponent(searchTerm)}`);
};

// Get a doctor by ID
export const getDoctorById = async (doctorId: number): Promise<Doctor> => {
  return get<Doctor>(`/api/doctors/${doctorId}`);
};

// Create a new doctor
export const createDoctor = async (doctorData: Partial<Doctor>): Promise<Doctor> => {
  return post<Doctor>('/api/doctors', doctorData);
};

// Update a doctor
export const updateDoctor = async (doctorId: number, doctorData: Partial<Doctor>): Promise<Doctor> => {
  return put<Doctor>(`/api/doctors/${doctorId}`, doctorData);
};

// Delete a doctor
export const deleteDoctor = async (doctorId: number): Promise<{ message: string }> => {
  return del<{ message: string }>(`/api/doctors/${doctorId}`);
};