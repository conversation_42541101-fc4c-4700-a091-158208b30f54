import { get, post, put, del } from '../utils/apiUtils';

/**
 * Interface for BEERS criteria
 */
export interface BeersCriterion {
  criteria_id: number;
  medication_name: string;
  category: string;
  condition_name: string | null;
  interacting_medication: string | null;
  recommendation: string;
  rationale: string;
  quality_of_evidence: string;
  strength_of_recommendation: string;
  created_at: string;
  updated_at: string;
}

/**
 * Interface for BEERS criteria alert
 */
export interface BeersCriteriaAlert {
  medication: string;
  criterion: BeersCriterion;
  message: string;
  prescriptionId?: number;
}

/**
 * Interface for BEERS criteria override
 */
export interface BeersCriteriaOverride {
  alert: BeersCriteriaAlert;
  reason: string;
  overridden_by: number;
  overridden_at: string;
}

/**
 * Get all BEERS criteria
 * @returns Promise with array of BEERS criteria
 */
export const getAllBeersCriteria = async (): Promise<BeersCriterion[]> => {
  return get<BeersCriterion[]>('/api/beers-criteria');
};

/**
 * Get BEERS criteria by ID
 * @param criteriaId Criteria ID
 * @returns Promise with BEERS criterion
 */
export const getBeersCriteriaById = async (criteriaId: number): Promise<BeersCriterion> => {
  return get<BeersCriterion>(`/api/beers-criteria/${criteriaId}`);
};

/**
 * Get BEERS criteria by medication name
 * @param medicationName Medication name
 * @returns Promise with array of BEERS criteria
 */
export const getBeersCriteriaByMedication = async (medicationName: string): Promise<BeersCriterion[]> => {
  return get<BeersCriterion[]>(`/api/beers-criteria/medication/${encodeURIComponent(medicationName)}`);
};

/**
 * Get BEERS criteria by category
 * @param category Category
 * @returns Promise with array of BEERS criteria
 */
export const getBeersCriteriaByCategory = async (category: string): Promise<BeersCriterion[]> => {
  return get<BeersCriterion[]>(`/api/beers-criteria/category/${encodeURIComponent(category)}`);
};

/**
 * Get BEERS criteria by condition
 * @param condition Condition
 * @returns Promise with array of BEERS criteria
 */
export const getBeersCriteriaByCondition = async (condition: string): Promise<BeersCriterion[]> => {
  return get<BeersCriterion[]>(`/api/beers-criteria/condition/${encodeURIComponent(condition)}`);
};

/**
 * Check medications against BEERS criteria
 * @param medications Array of medication names
 * @param patientId Patient ID
 * @param visitId Optional visit ID to include visit-specific data
 * @returns Promise with array of BEERS criteria alerts
 */
export const checkMedications = async (
  medications: string[],
  patientId: number,
  visitId?: number
): Promise<BeersCriteriaAlert[]> => {
  return post<BeersCriteriaAlert[]>('/api/beers-criteria/check', { medications, patientId, visitId });
};

/**
 * Override BEERS criteria alert for a prescription
 * @param prescriptionId Prescription ID
 * @param criteriaId BEERS criteria ID
 * @param reason Override reason
 * @returns Promise with updated prescription
 */
export const overrideBeersCriteriaAlert = async (
  prescriptionId: number,
  criteriaId: number,
  reason: string
): Promise<any> => {
  return post<any>(`/api/prescriptions/${prescriptionId}/override-beers`, { criteriaId, reason });
};

/**
 * Save BEERS criteria alerts for a visit
 * @param visitId Visit ID
 * @param alerts Array of BEERS criteria alerts
 * @returns Promise with success message
 */
export const saveAlerts = async (
  visitId: number,
  alerts: BeersCriteriaAlert[]
): Promise<{ msg: string }> => {
  return post<{ msg: string }>(`/api/beers-criteria/visit/${visitId}/alerts`, { alerts });
};

/**
 * Save BEERS criteria overrides for a visit
 * @param visitId Visit ID
 * @param overrides Array of BEERS criteria overrides
 * @returns Promise with success message
 */
export const saveOverrides = async (
  visitId: number,
  overrides: BeersCriteriaOverride[]
): Promise<{ msg: string }> => {
  return post<{ msg: string }>(`/api/beers-criteria/visit/${visitId}/overrides`, { overrides });
};

/**
 * Get BEERS criteria alerts for a visit
 * @param visitId Visit ID
 * @returns Promise with array of BEERS criteria alerts
 */
export const getAlerts = async (visitId: number): Promise<BeersCriteriaAlert[]> => {
  return get<BeersCriteriaAlert[]>(`/api/beers-criteria/visit/${visitId}/alerts`);
};

/**
 * Get BEERS criteria overrides for a visit
 * @param visitId Visit ID
 * @returns Promise with array of BEERS criteria overrides
 */
export const getOverrides = async (visitId: number): Promise<BeersCriteriaOverride[]> => {
  return get<BeersCriteriaOverride[]>(`/api/beers-criteria/visit/${visitId}/overrides`);
};

/**
 * Create a new BEERS criterion
 * @param criterionData The criterion data
 * @returns Promise with the created BEERS criterion
 */
export const createBeersCriterion = async (criterionData: Partial<BeersCriterion>): Promise<BeersCriterion> => {
  return post<BeersCriterion>('/api/beers-criteria', criterionData);
};

/**
 * Update an existing BEERS criterion
 * @param criteriaId The criterion ID
 * @param criterionData The updated criterion data
 * @returns Promise with the updated BEERS criterion
 */
export const updateBeersCriterion = async (
  criteriaId: number,
  criterionData: Partial<BeersCriterion>
): Promise<BeersCriterion> => {
  return put<BeersCriterion>(`/api/beers-criteria/${criteriaId}`, criterionData);
};

/**
 * Delete a BEERS criterion
 * @param criteriaId The criterion ID
 * @returns Promise with success message
 */
export const deleteBeersCriterion = async (criteriaId: number): Promise<{ msg: string }> => {
  return del<{ msg: string }>(`/api/beers-criteria/${criteriaId}`);
};
