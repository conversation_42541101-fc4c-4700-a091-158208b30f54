import { Visit } from '../types';
import { get, post, put, del } from '../utils/apiUtils';
import { processDateFields, ensureTextFields, processTimeFields } from '../utils/dataProcessingUtils';

// Define prescription interface
interface Prescription {
  id?: number;
  medication: string;
  dosage: string;
  frequency: string;
  duration: string;
  notes?: string;
  beers_criteria_id?: number;
  beers_criteria_name?: string;
  beers_criteria_category?: string;
  beers_criteria_recommendation?: string;
  beers_criteria_rationale?: string;
  beers_override_reason?: string;
  beers_overridden_at?: string;
  beers_overridden_by?: number;
  overridden_by_username?: string;
}

// Define common date fields for visits
const VISIT_DATE_FIELDS = [
  'influenza_vaccination_date',
  'pneumococcal_vaccination_date',
  'zoster_vaccination_date',
  'tdap_vaccination_date',
  'covid19_vaccination_date',
  'covid19_booster_date',
  'hepatitis_a_vaccination_date',
  'hepatitis_b_vaccination_date',
  'mmr_vaccination_date',
  'varicella_vaccination_date',
  'psqi_assessment_date'
];

// Define required text fields with default values
const REQUIRED_TEXT_FIELDS = {
  'referrals': '',
  'medication_changes': ''
};

// Define time fields that need special handling
const TIME_FIELDS = ['visit_time', 'end_time', 'psqi_bedtime', 'psqi_wake_up_time'];

/**
 * Create a new visit
 * @param visitData Visit data to create
 * @returns Created visit
 */
export const createVisit = async (visitData: Partial<Visit>): Promise<Visit> => {
  // Process the data
  let processedData = processDateFields(visitData, VISIT_DATE_FIELDS);
  processedData = ensureTextFields(processedData, REQUIRED_TEXT_FIELDS);

  // Process time fields to avoid database errors
  processedData = processTimeFields(processedData, TIME_FIELDS);

  return post<Visit>('/api/visits', processedData);
};

/**
 * Get visits for a patient
 * @param patientId Patient ID
 * @returns Array of visits
 */
export const getPatientVisits = async (patientId: number): Promise<Visit[]> => {
  return get<Visit[]>(`/api/visits/patient/${patientId}`);
};

/**
 * Get the most recent visit for a patient
 * @param patientId Patient ID
 * @returns Most recent visit or null if no visits exist
 */
export const getMostRecentVisit = async (patientId: number): Promise<Visit | null> => {
  console.log(`Fetching most recent visit for patient ID: ${patientId}`);

  try {
    const visits = await getPatientVisits(patientId);
    console.log(`Retrieved ${visits?.length || 0} visits for patient ID: ${patientId}`);

    if (!visits || visits.length === 0) {
      console.log('No visits found for this patient');
      return null;
    }

    // Sort visits by date (most recent first)
    const sortedVisits = visits.sort((a, b) =>
      new Date(b.visit_date).getTime() - new Date(a.visit_date).getTime()
    );

    console.log('Most recent visit date:', sortedVisits[0].visit_date);

    // Check if vaccination fields exist in the most recent visit
    const recentVisit = sortedVisits[0];
    const vaccinationFields = [
      'hepatitis_b_vaccination_date',
      'mmr_vaccination_date',
      'varicella_vaccination_date',
      'influenza_vaccination_date',
      'pneumococcal_vaccination_date',
      'zoster_vaccination_date',
      'tdap_vaccination_date',
      'covid19_vaccination_date',
      'covid19_booster_date',
      'hepatitis_a_vaccination_date'
    ];

    console.log('Checking vaccination fields in most recent visit:');
    vaccinationFields.forEach(field => {
      console.log(`${field}: ${recentVisit[field as keyof Visit] || 'not found'}`);
    });

    return recentVisit;
  } catch (error) {
    console.error('Error in getMostRecentVisit:', error);
    return null;
  }
};

/**
 * Get vaccination data for a patient
 * @param patientId Patient ID
 * @returns Object containing vaccination data from the most recent visit or patient record
 */
export const getVaccinationData = async (patientId: number): Promise<Record<string, any>> => {
  console.log(`Fetching vaccination data for patient ID: ${patientId}`);

  try {
    // First try to get data from the most recent visit
    const recentVisit = await getMostRecentVisit(patientId);

    if (recentVisit) {
      console.log('Found vaccination data from recent visit');

      // Define vaccination fields to extract
      const vaccinationFields = [
        'hepatitis_b_vaccination_date',
        'mmr_vaccination_date',
        'varicella_vaccination_date',
        'influenza_vaccination_date',
        'pneumococcal_vaccination_date',
        'zoster_vaccination_date',
        'tdap_vaccination_date',
        'covid19_vaccination_date',
        'covid19_booster_date',
        'hepatitis_a_vaccination_date',
        'other_vaccinations'
      ];

      // Extract vaccination data from the visit
      const vaccinationData: Record<string, any> = {};
      vaccinationFields.forEach(field => {
        if (field in recentVisit && recentVisit[field as keyof Visit] !== null) {
          vaccinationData[field] = recentVisit[field as keyof Visit];
        }
      });

      return vaccinationData;
    } else {
      // If no visits exist, get data from the patient record
      console.log('No visits found, fetching vaccination data from patient record');

      // Get patient data
      const patientResponse = await get<any>(`/api/patients/${patientId}`);

      if (!patientResponse) {
        console.log('No patient data found');
        return {};
      }

      // Define vaccination fields to extract from patient record
      const vaccinationFields = [
        'hepatitis_b_vaccination_date',
        'mmr_vaccination_date',
        'varicella_vaccination_date',
        'influenza_vaccination_date',
        'pneumococcal_vaccination_date',
        'zoster_vaccination_date',
        'tdap_vaccination_date',
        'covid19_vaccination_date',
        'covid19_booster_date',
        'hepatitis_a_vaccination_date',
        'other_vaccinations'
      ];

      // Extract vaccination data from the patient record
      const vaccinationData: Record<string, any> = {};
      vaccinationFields.forEach(field => {
        if (field in patientResponse && patientResponse[field] !== null) {
          vaccinationData[field] = patientResponse[field];
        }
      });

      console.log('Vaccination data from patient record:', vaccinationData);
      return vaccinationData;
    }
  } catch (error) {
    console.error('Error in getVaccinationData:', error);
    return {};
  }
};

/**
 * Get visits for a doctor
 * @param doctorId Doctor ID
 * @returns Array of visits
 */
export const getDoctorVisits = async (doctorId: number): Promise<Visit[]> => {
  return get<Visit[]>(`/api/visits/doctor/${doctorId}`);
};

/**
 * Get a single visit by ID
 * @param visitId Visit ID
 * @returns Visit data
 */
export const getVisitById = async (visitId: number): Promise<Visit> => {
  return get<Visit>(`/api/visits/${visitId}`);
};

/**
 * Update a visit
 * @param visitId Visit ID
 * @param visitData Visit data to update
 * @returns Updated visit
 */
export const updateVisit = async (visitId: number, visitData: Partial<Visit>): Promise<Visit> => {
  console.log('Original visit data before processing:', JSON.stringify(visitData, null, 2));

  // Process the data
  let processedData = processDateFields(visitData, VISIT_DATE_FIELDS);
  console.log('After processDateFields:', JSON.stringify(processedData, null, 2));

  processedData = ensureTextFields(processedData, REQUIRED_TEXT_FIELDS);
  console.log('After ensureTextFields:', JSON.stringify(processedData, null, 2));

  // Process time fields to avoid database errors
  processedData = processTimeFields(processedData, TIME_FIELDS);
  console.log('After processTimeFields:', JSON.stringify(processedData, null, 2));

  // Ensure required fields are present
  if (!processedData.patient_id) {
    console.error('Missing required field: patient_id');
  }

  if (!processedData.visit_date) {
    console.error('Missing required field: visit_date');
  }

  try {
    const result = await put<Visit>(`/api/visits/${visitId}`, processedData);
    console.log('Visit update successful:', result);
    return result;
  } catch (error) {
    console.error('Error in updateVisit:', error);
    if (error instanceof Error) {
      console.error('Error message:', error.message);
    }
    throw error;
  }
};

/**
 * Delete a visit
 * @param visitId Visit ID
 * @returns Success message
 */
export const deleteVisit = async (visitId: number): Promise<{ message: string }> => {
  return del<{ message: string }>(`/api/visits/${visitId}`);
};

/**
 * Save prescriptions for a visit
 * @param visitId Visit ID
 * @param prescriptions Array of prescriptions to save
 * @param patientId Optional patient ID (if not provided, will be fetched from the visit)
 * @returns Promise with the saved prescriptions
 */
export const savePrescriptionsForVisit = async (visitId: number, prescriptions: Prescription[], patientId?: number): Promise<any> => {
  if (!prescriptions || prescriptions.length === 0) {
    console.log('No prescriptions to save');
    return [];
  }

  try {
    console.log(`Saving ${prescriptions.length} prescriptions for visit ${visitId}`);
    console.log('DETAILED DEBUG: Full prescriptions data:', JSON.stringify(prescriptions, null, 2));

    // Separate existing prescriptions (with positive id) from new ones (with negative or no id)
    const existingPrescriptions = prescriptions.filter(p => p.id && p.id > 0);
    const newPrescriptions = prescriptions.filter(p => !p.id || p.id <= 0);

    console.log(`Found ${existingPrescriptions.length} existing prescriptions and ${newPrescriptions.length} new prescriptions`);
    console.log('Existing prescriptions:', existingPrescriptions.map(p => p.id));
    console.log('New prescriptions:', newPrescriptions.map(p => p.id));

    // If patientId is not provided, fetch the visit to get the patient_id
    let patient_id = patientId;
    if (!patient_id) {
      try {
        const visit = await getVisitById(visitId);
        patient_id = visit.patient_id;
        console.log(`Retrieved patient_id ${patient_id} from visit ${visitId}`);
      } catch (err) {
        console.error('Error fetching patient_id from visit:', err);
        // Continue without patient_id
      }
    }

    // Check if user is authenticated
    const token = localStorage.getItem('token');

    // If we have a token, log that the user is authenticated
    if (token) {
      try {
        // We're just using the presence of the token to indicate the user is logged in
        // The actual user ID should be passed in the prescription objects
        console.log('User is authenticated, token exists');
      } catch (err) {
        console.error('Error with token:', err);
      }
    }

    // Handle existing prescriptions first (update them)
    const updatedPrescriptions = [];
    if (existingPrescriptions.length > 0) {
      console.log('Updating existing prescriptions:', existingPrescriptions);

      for (const prescription of existingPrescriptions) {
        try {
          // Update each prescription individually
          const updatedPrescription = await put(`/api/prescriptions/${prescription.id}`, {
            medication: prescription.medication,
            dosage: prescription.dosage,
            frequency: prescription.frequency,
            duration: prescription.duration,
            notes: prescription.notes || '',
            // Include all BEERS criteria fields
            beers_criteria_id: prescription.beers_criteria_id || null,
            beers_criteria_name: prescription.beers_criteria_name || null,
            beers_criteria_category: prescription.beers_criteria_category || null,
            beers_criteria_recommendation: prescription.beers_criteria_recommendation || null,
            beers_criteria_rationale: prescription.beers_criteria_rationale || null,
            beers_override_reason: prescription.beers_override_reason || null,
            beers_overridden_at: prescription.beers_overridden_at || (prescription.beers_override_reason ? new Date().toISOString() : null),
            beers_overridden_by: prescription.beers_overridden_by || null,
            visit_id: visitId,
            patient_id: patient_id
          });

          updatedPrescriptions.push(updatedPrescription);
          console.log(`Updated prescription ${prescription.id}:`, updatedPrescription);
        } catch (err) {
          console.error(`Error updating prescription ${prescription.id}:`, err);
        }
      }
    }

    // Now handle new prescriptions (create them)
    let newCreatedPrescriptions = [];
    if (newPrescriptions.length > 0) {
      // Format the request body for the batch API
      const requestBody = {
        visit_id: visitId,
        patient_id: patient_id, // Include patient_id for better data integrity
        prescriptions: newPrescriptions.map(p => {
          // Remove any temporary negative IDs
          const { id, ..._ } = p;

          return {
            medication: p.medication,
            dosage: p.dosage,
            frequency: p.frequency,
            duration: p.duration,
            notes: p.notes || '',
            // Include all BEERS criteria fields
            beers_criteria_id: p.beers_criteria_id || null,
            beers_criteria_name: p.beers_criteria_name || null,
            beers_criteria_category: p.beers_criteria_category || null,
            beers_criteria_recommendation: p.beers_criteria_recommendation || null,
            beers_criteria_rationale: p.beers_criteria_rationale || null,
            beers_override_reason: p.beers_override_reason || null,
            beers_overridden_at: p.beers_overridden_at || (p.beers_override_reason ? new Date().toISOString() : null),
            beers_overridden_by: p.beers_overridden_by || null, // Use the value set in the component

            // Log the beers_overridden_by value for debugging
            ...(p.beers_override_reason ? { _debug_overridden_by: `Using user ID: ${p.beers_overridden_by}` } : {}),
            // Add patient_id to each prescription
            patient_id: patient_id
          };
        })
      };

      // Debug log the request body
      console.log('New prescription batch request body:', JSON.stringify(requestBody, null, 2));

      // Log BEERS criteria overrides specifically
      const overriddenPrescriptions = requestBody.prescriptions.filter(p => p.beers_override_reason);
      if (overriddenPrescriptions.length > 0) {
        console.log('BEERS criteria overrides:', overriddenPrescriptions.map(p => ({
          medication: p.medication,
          beers_criteria_id: p.beers_criteria_id,
          beers_override_reason: p.beers_override_reason,
          beers_overridden_by: p.beers_overridden_by,
          beers_overridden_at: p.beers_overridden_at
        })));
      }

      // Use the batch API to create multiple prescriptions at once
      newCreatedPrescriptions = await post('/api/prescriptions/batch', requestBody);
      console.log('New prescriptions saved successfully:', newCreatedPrescriptions);
    }

    // Combine updated and new prescriptions
    const allPrescriptions = [...updatedPrescriptions, ...newCreatedPrescriptions];
    console.log('All prescriptions saved successfully:', allPrescriptions);
    return allPrescriptions;
  } catch (error) {
    console.error('Error saving prescriptions for visit:', error);
    throw error;
  }
};

/**
 * Get recent visits
 * @param limit Maximum number of visits to return
 * @returns Array of recent visits
 */
export const getRecentVisits = async (limit = 10): Promise<Visit[]> => {
  return get<Visit[]>(`/api/visits/recent?limit=${limit}`);
};

/**
 * Get combined health metrics data for a patient
 * This fetches data from both the patients table (baseline) and patient_visits table
 * @param patientId Patient ID
 * @returns Combined health metrics data
 */
export const getHealthMetricsData = async (patientId: number): Promise<{
  success: boolean;
  data: Visit[];
  baselineIncluded: boolean;
}> => {
  return get<{
    success: boolean;
    data: Visit[];
    baselineIncluded: boolean;
  }>(`/api/visits/health-metrics/${patientId}`);
};