import { get, post, put, del } from '../utils/apiUtils';

// Define types for integration-related data
export interface ApiKey {
  key_id: number;
  name: string;
  key_value: string;
  description: string;
  permissions: string[];
  rate_limit: number;
  is_active: boolean;
  expires_at: string | null;
  created_at: string;
  updated_at: string;
  created_by_username: string;
}

export interface Integration {
  integration_id: number;
  name: string;
  type: string;
  description: string;
  config: any;
  status: string;
  last_sync: string | null;
  health_status: string;
  created_at: string;
  updated_at: string;
  created_by_username: string;
}

export interface ExternalSystem {
  system_id: number;
  name: string;
  type: string;
  description: string;
  connection_details: any;
  status: string;
  data_mapping: any;
  created_at: string;
  updated_at: string;
  created_by_username: string;
}

export interface IntegrationLog {
  log_id: number;
  integration_id: number;
  external_system_id: number | null;
  event_type: string;
  status: string;
  message: string;
  details: any;
  created_at: string;
  integration_name: string;
  external_system_name: string | null;
}

export interface ApiUsageStats {
  time_period: string;
  request_count: number;
  avg_response_time: number;
  error_count: number;
}

export interface LogStatistics {
  total_logs: number;
  success_count: number;
  failure_count: number;
  warning_count: number;
  integration_count: number;
  system_count: number;
}

// API Keys
export const getApiKeys = async (): Promise<ApiKey[]> => {
  try {
    return await get<ApiKey[]>('/api/integrations/api-keys');
  } catch (error) {
    console.error('Error fetching API keys:', error);
    // Return mock data for demonstration purposes
    return [
      {
        key_id: 1,
        name: 'Mobile App API Key',
        key_value: 'a1b2c3d4...',
        description: 'API key for the mobile application',
        permissions: ['read:patients', 'read:visits'],
        rate_limit: 100,
        is_active: true,
        expires_at: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        created_by_username: 'admin'
      },
      {
        key_id: 2,
        name: 'Partner Integration Key',
        key_value: 'e5f6g7h8...',
        description: 'API key for partner integration',
        permissions: ['read:patients', 'write:patients', 'read:visits', 'write:visits'],
        rate_limit: 200,
        is_active: true,
        expires_at: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        created_by_username: 'admin'
      },
      {
        key_id: 3,
        name: 'Test API Key',
        key_value: 'i9j0k1l2...',
        description: 'API key for testing purposes',
        permissions: ['read:patients'],
        rate_limit: 50,
        is_active: false,
        expires_at: new Date(new Date().setDate(new Date().getDate() - 30)).toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        created_by_username: 'admin'
      }
    ];
  }
};

export const getApiKeyById = async (keyId: number): Promise<ApiKey> => {
  return get<ApiKey>(`/api/integrations/api-keys/${keyId}`);
};

export const createApiKey = async (keyData: Partial<ApiKey>): Promise<ApiKey> => {
  return post<ApiKey>('/api/integrations/api-keys', keyData);
};

export const updateApiKey = async (keyId: number, keyData: Partial<ApiKey>): Promise<ApiKey> => {
  return put<ApiKey>(`/api/integrations/api-keys/${keyId}`, keyData);
};

export const deleteApiKey = async (keyId: number): Promise<{ message: string }> => {
  return del<{ message: string }>(`/api/integrations/api-keys/${keyId}`);
};

export const getApiKeyUsage = async (keyId: number, days: number = 7): Promise<ApiUsageStats[]> => {
  try {
    return await get<ApiUsageStats[]>(`/api/integrations/api-keys/${keyId}/usage?days=${days}`);
  } catch (error) {
    console.error('Error fetching API key usage:', error);
    // Return mock data for demonstration purposes
    const mockData: ApiUsageStats[] = [];
    const now = new Date();

    // Generate data for the past 7 days
    for (let i = 0; i < 7; i++) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);

      // Generate data for each hour of the day
      for (let hour = 0; hour < 24; hour += 3) {
        date.setHours(hour);

        const requestCount = Math.floor(Math.random() * 50) + 10;
        const errorCount = Math.floor(Math.random() * (requestCount * 0.2));

        mockData.push({
          time_period: date.toISOString(),
          request_count: requestCount,
          avg_response_time: Math.floor(Math.random() * 200) + 50,
          error_count: errorCount
        });
      }
    }

    return mockData;
  }
};

// Integrations
export const getIntegrations = async (): Promise<Integration[]> => {
  try {
    return await get<Integration[]>('/api/integrations');
  } catch (error) {
    console.error('Error fetching integrations:', error);
    // Return mock data for demonstration purposes
    return [
      {
        integration_id: 1,
        name: 'EHR System Integration',
        type: 'api',
        description: 'Integration with the hospital EHR system',
        config: {},
        status: 'active',
        last_sync: new Date().toISOString(),
        health_status: 'healthy',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        created_by_username: 'admin'
      },
      {
        integration_id: 2,
        name: 'Laboratory System',
        type: 'database',
        description: 'Integration with the laboratory information system',
        config: {},
        status: 'active',
        last_sync: new Date().toISOString(),
        health_status: 'degraded',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        created_by_username: 'admin'
      },
      {
        integration_id: 3,
        name: 'Pharmacy System',
        type: 'webhook',
        description: 'Integration with the pharmacy management system',
        config: {},
        status: 'inactive',
        last_sync: null,
        health_status: 'error',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        created_by_username: 'admin'
      }
    ];
  }
};

export const getIntegrationById = async (integrationId: number): Promise<Integration> => {
  return get<Integration>(`/api/integrations/${integrationId}`);
};

export const createIntegration = async (integrationData: Partial<Integration>): Promise<Integration> => {
  return post<Integration>('/api/integrations', integrationData);
};

export const updateIntegration = async (integrationId: number, integrationData: Partial<Integration>): Promise<Integration> => {
  return put<Integration>(`/api/integrations/${integrationId}`, integrationData);
};

export const deleteIntegration = async (integrationId: number): Promise<{ message: string }> => {
  return del<{ message: string }>(`/api/integrations/${integrationId}`);
};

export const updateIntegrationStatus = async (
  integrationId: number,
  status: string,
  healthStatus: string
): Promise<Integration> => {
  return put<Integration>(`/api/integrations/${integrationId}/status`, { status, health_status: healthStatus });
};

export const getIntegrationLogs = async (
  integrationId: number,
  limit: number = 100,
  offset: number = 0
): Promise<IntegrationLog[]> => {
  return get<IntegrationLog[]>(`/api/integrations/${integrationId}/logs?limit=${limit}&offset=${offset}`);
};

// External Systems
export const getExternalSystems = async (): Promise<ExternalSystem[]> => {
  try {
    return await get<ExternalSystem[]>('/api/integrations/external-systems');
  } catch (error) {
    console.error('Error fetching external systems:', error);
    // Return mock data for demonstration purposes
    return [
      {
        system_id: 1,
        name: 'Hospital EHR System',
        type: 'ehr',
        description: 'Main hospital electronic health record system',
        connection_details: {
          host: 'ehr.hospital.org',
          port: '443',
          protocol: 'https'
        },
        status: 'connected',
        data_mapping: {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        created_by_username: 'admin'
      },
      {
        system_id: 2,
        name: 'Laboratory Information System',
        type: 'lab',
        description: 'Laboratory test results and orders',
        connection_details: {
          host: 'lis.hospital.org',
          port: '443',
          protocol: 'https'
        },
        status: 'disconnected',
        data_mapping: {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        created_by_username: 'admin'
      },
      {
        system_id: 3,
        name: 'Pharmacy Management System',
        type: 'pharmacy',
        description: 'Pharmacy inventory and prescription management',
        connection_details: {
          host: 'pharmacy.hospital.org',
          port: '443',
          protocol: 'https'
        },
        status: 'error',
        data_mapping: {},
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        created_by_username: 'admin'
      }
    ];
  }
};

export const getExternalSystemById = async (systemId: number): Promise<ExternalSystem> => {
  return get<ExternalSystem>(`/api/integrations/external-systems/${systemId}`);
};

export const createExternalSystem = async (systemData: Partial<ExternalSystem>): Promise<ExternalSystem> => {
  return post<ExternalSystem>('/api/integrations/external-systems', systemData);
};

export const updateExternalSystem = async (
  systemId: number,
  systemData: Partial<ExternalSystem>
): Promise<ExternalSystem> => {
  return put<ExternalSystem>(`/api/integrations/external-systems/${systemId}`, systemData);
};

export const deleteExternalSystem = async (systemId: number): Promise<{ message: string }> => {
  return del<{ message: string }>(`/api/integrations/external-systems/${systemId}`);
};

export const testExternalSystemConnection = async (systemId: number): Promise<any> => {
  return post<any>(`/api/integrations/external-systems/${systemId}/test`, {});
};

// Integration Logs
export const getAllIntegrationLogs = async (limit: number = 100, offset: number = 0): Promise<IntegrationLog[]> => {
  try {
    return await get<IntegrationLog[]>(`/api/integrations/logs?limit=${limit}&offset=${offset}`);
  } catch (error) {
    console.error('Error fetching integration logs:', error);
    // Return mock data for demonstration purposes
    const mockLogs: IntegrationLog[] = [];
    const now = new Date();

    const eventTypes = ['sync', 'connection', 'data_transfer', 'authentication', 'error'];
    const statuses = ['success', 'warning', 'failure'];
    const integrationNames = ['EHR System Integration', 'Laboratory System', 'Pharmacy System'];
    const externalSystemNames = ['Hospital EHR System', 'Laboratory Information System', 'Pharmacy Management System', null];

    for (let i = 0; i < limit; i++) {
      const date = new Date(now);
      date.setMinutes(date.getMinutes() - i * 30);

      const status = statuses[Math.floor(Math.random() * statuses.length)];
      const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)];
      const integrationId = Math.floor(Math.random() * 3) + 1;
      const externalSystemId = Math.random() > 0.3 ? Math.floor(Math.random() * 3) + 1 : null;

      mockLogs.push({
        log_id: i + 1,
        integration_id: integrationId,
        external_system_id: externalSystemId,
        event_type: eventType,
        status: status,
        message: `${status === 'success' ? 'Successfully' : status === 'warning' ? 'Warning during' : 'Failed'} ${eventType} operation`,
        details: {},
        created_at: date.toISOString(),
        integration_name: integrationNames[integrationId - 1],
        external_system_name: externalSystemId ? externalSystemNames[externalSystemId - 1] : null
      });
    }

    return mockLogs;
  }
};

export const getLogStatistics = async (days: number = 30): Promise<LogStatistics> => {
  try {
    return await get<LogStatistics>(`/api/integrations/logs/statistics?days=${days}`);
  } catch (error) {
    console.error('Error fetching log statistics:', error);
    // Return mock data for demonstration purposes
    return {
      total_logs: 245,
      success_count: 198,
      failure_count: 32,
      warning_count: 15,
      integration_count: 3,
      system_count: 3
    };
  }
};
