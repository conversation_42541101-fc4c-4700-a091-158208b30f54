import { get, post, put, del } from '../utils/apiUtils';

interface Prescription {
  prescription_id: number;
  medication: string;
  dosage: string;
  frequency: string;
  duration: string;
  notes?: string;
  beers_criteria_id?: number;
  beers_criteria_name?: string;
  beers_criteria_category?: string;
  beers_criteria_recommendation?: string;
  beers_criteria_rationale?: string;
  beers_override_reason?: string;
  beers_overridden_at?: string;
  beers_overridden_by?: number;
  overridden_by_username?: string;
  created_at: string;
}

/**
 * Get prescriptions for a patient that are not associated with a visit
 * @param patientId Patient ID
 * @returns Array of prescriptions
 */
export const getPrescriptionsByPatientId = async (patientId: number): Promise<Prescription[]> => {
  return get<Prescription[]>(`/api/prescriptions/patient/${patientId}`);
};

/**
 * Get all prescriptions for a patient, including those associated with visits
 * @param patientId Patient ID
 * @returns Array of prescriptions
 */
export const getAllPrescriptionsByPatientId = async (patientId: number): Promise<Prescription[]> => {
  return get<Prescription[]>(`/api/prescriptions/patient/${patientId}/all`);
};

/**
 * Get prescriptions for a visit
 * @param visitId Visit ID
 * @returns Array of prescriptions
 */
export const getPrescriptionsByVisitId = async (visitId: number): Promise<Prescription[]> => {
  return get<Prescription[]>(`/api/prescriptions/visit/${visitId}`);
};

/**
 * Get a prescription by ID
 * @param prescriptionId Prescription ID
 * @returns Prescription object
 */
export const getPrescriptionById = async (prescriptionId: number): Promise<Prescription> => {
  return get<Prescription>(`/api/prescriptions/${prescriptionId}`);
};

/**
 * Create a new prescription
 * @param prescription Prescription data
 * @returns Created prescription
 */
export const createPrescription = async (prescription: Omit<Prescription, 'prescription_id' | 'created_at'>): Promise<Prescription> => {
  return post<Prescription>('/api/prescriptions', prescription);
};

/**
 * Update a prescription
 * @param prescriptionId Prescription ID
 * @param prescription Updated prescription data
 * @returns Updated prescription
 */
export const updatePrescription = async (prescriptionId: number, prescription: Partial<Prescription>): Promise<Prescription> => {
  return put<Prescription>(`/api/prescriptions/${prescriptionId}`, prescription);
};

/**
 * Delete a prescription
 * @param prescriptionId Prescription ID
 * @returns Success message
 */
export const deletePrescription = async (prescriptionId: number): Promise<{ success: boolean }> => {
  return del<{ success: boolean }>(`/api/prescriptions/${prescriptionId}`);
};

/**
 * Override BEERS criteria for a prescription
 * @param prescriptionId Prescription ID
 * @param criteriaId BEERS criteria ID
 * @param reason Override reason
 * @returns Updated prescription
 */
export const overrideBeersCriteria = async (
  prescriptionId: number,
  criteriaId: number,
  reason: string
): Promise<Prescription> => {
  return post<Prescription>(`/api/prescriptions/${prescriptionId}/override-beers`, {
    criteriaId,
    reason
  });
};
