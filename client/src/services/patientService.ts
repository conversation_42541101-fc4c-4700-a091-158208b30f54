import { Patient } from '../types';
import { get, post, put, del } from '../utils/apiUtils';

// Define prescription interface
interface Prescription {
  id?: number;
  medication: string;
  dosage: string;
  frequency: string;
  duration: string;
  notes?: string;
  beers_criteria_id?: number;
  beers_criteria_name?: string;
  beers_criteria_category?: string;
  beers_criteria_recommendation?: string;
  beers_criteria_rationale?: string;
  beers_override_reason?: string;
  beers_overridden_at?: string;
  beers_overridden_by?: number;
  overridden_by_username?: string;
}

/**
 * Patient service for handling patient-related API calls
 */

// Get all patients
export const getPatients = async (): Promise<Patient[]> => {
  return get<Patient[]>('/api/patients');
};

// Search patients
export const searchPatients = async (searchTerm: string): Promise<Patient[]> => {
  return get<Patient[]>(`/api/patients?search=${encodeURIComponent(searchTerm)}`);
};

// Get a patient by ID
export const getPatientById = async (patientId: number): Promise<Patient> => {
  return get<Patient>(`/api/patients/${patientId}`);
};

// Get patient dashboard data
export const getPatientDashboard = async (patientId: number): Promise<any> => {
  return get<any>(`/api/patients/dashboard/${patientId}`);
};

// Create a new patient
export const createPatient = async (patientData: Partial<Patient>): Promise<Patient> => {
  return post<Patient>('/api/patients', patientData);
};

// Update a patient
export const updatePatient = async (patientId: number, patientData: Partial<Patient>): Promise<Patient> => {
  return put<Patient>(`/api/patients/${patientId}`, patientData);
};

// Delete a patient
export const deletePatient = async (patientId: number): Promise<{ message: string }> => {
  return del<{ message: string }>(`/api/patients/${patientId}`);
};

/**
 * Save prescriptions for a patient
 * @param patientId Patient ID
 * @param prescriptions Array of prescriptions to save
 * @returns Promise with the saved prescriptions
 */
export const savePrescriptionsForPatient = async (patientId: number, prescriptions: Prescription[]): Promise<any> => {
  if (!prescriptions || prescriptions.length === 0) {
    console.log('No prescriptions to save for patient');
    return [];
  }

  try {
    console.log(`Saving ${prescriptions.length} prescriptions for patient ${patientId}`);
    console.log('DETAILED DEBUG: Full prescriptions data:', JSON.stringify(prescriptions, null, 2));

    // Separate existing prescriptions (with positive id) from new ones (with negative or no id)
    const existingPrescriptions = prescriptions.filter(p => p.id && p.id > 0);
    const newPrescriptions = prescriptions.filter(p => !p.id || p.id <= 0);

    console.log(`Found ${existingPrescriptions.length} existing prescriptions and ${newPrescriptions.length} new prescriptions`);
    console.log('Existing prescriptions:', existingPrescriptions.map(p => p.id));
    console.log('New prescriptions:', newPrescriptions.map(p => p.id));

    // Handle existing prescriptions first (update them)
    const updatedPrescriptions = [];
    if (existingPrescriptions.length > 0) {
      console.log('Updating existing prescriptions:', existingPrescriptions);

      for (const prescription of existingPrescriptions) {
        try {
          // Update each prescription individually
          const updatedPrescription = await put(`/api/prescriptions/${prescription.id}`, {
            medication: prescription.medication,
            dosage: prescription.dosage,
            frequency: prescription.frequency,
            duration: prescription.duration,
            notes: prescription.notes || '',
            // Include all BEERS criteria fields
            beers_criteria_id: prescription.beers_criteria_id || null,
            beers_criteria_name: prescription.beers_criteria_name || null,
            beers_criteria_category: prescription.beers_criteria_category || null,
            beers_criteria_recommendation: prescription.beers_criteria_recommendation || null,
            beers_criteria_rationale: prescription.beers_criteria_rationale || null,
            beers_override_reason: prescription.beers_override_reason || null,
            beers_overridden_at: prescription.beers_overridden_at || (prescription.beers_override_reason ? new Date().toISOString() : null),
            beers_overridden_by: prescription.beers_overridden_by || null,
            patient_id: patientId,
            visit_id: null // Ensure no visit_id for patient prescriptions
          });

          updatedPrescriptions.push(updatedPrescription);
          console.log(`Updated prescription ${prescription.id}:`, updatedPrescription);
        } catch (err) {
          console.error(`Error updating prescription ${prescription.id}:`, err);
        }
      }
    }

    // Now handle new prescriptions (create them)
    let newCreatedPrescriptions = [];
    if (newPrescriptions.length > 0) {
      // Format the request body for the batch API
      const requestBody = {
        patient_id: patientId,
        prescriptions: newPrescriptions.map(p => {
          // Remove any temporary negative IDs
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { id, ...rest } = p;

          return {
            medication: p.medication,
            dosage: p.dosage,
            frequency: p.frequency,
            duration: p.duration,
            notes: p.notes || '',
            // Include all BEERS criteria fields
            beers_criteria_id: p.beers_criteria_id || null,
            beers_criteria_name: p.beers_criteria_name || null,
            beers_criteria_category: p.beers_criteria_category || null,
            beers_criteria_recommendation: p.beers_criteria_recommendation || null,
            beers_criteria_rationale: p.beers_criteria_rationale || null,
            beers_override_reason: p.beers_override_reason || null,
            beers_overridden_at: p.beers_overridden_at || (p.beers_override_reason ? new Date().toISOString() : null),
            beers_overridden_by: p.beers_overridden_by || null,
            // Add patient_id to each prescription
            patient_id: patientId
          };
        })
      };

      // Debug log the request body
      console.log('New prescription batch request body:', JSON.stringify(requestBody, null, 2));

      // Log BEERS criteria overrides specifically
      const overriddenPrescriptions = requestBody.prescriptions.filter(p => p.beers_override_reason);
      if (overriddenPrescriptions.length > 0) {
        console.log('BEERS criteria overrides:', overriddenPrescriptions.map(p => ({
          medication: p.medication,
          beers_criteria_id: p.beers_criteria_id,
          beers_override_reason: p.beers_override_reason,
          beers_overridden_by: p.beers_overridden_by,
          beers_overridden_at: p.beers_overridden_at
        })));
      }

      // Use the batch API to create multiple prescriptions at once
      newCreatedPrescriptions = await post('/api/prescriptions/batch', requestBody);
      console.log('New prescriptions saved successfully:', newCreatedPrescriptions);
    }

    // Combine updated and new prescriptions
    const allPrescriptions = [...updatedPrescriptions, ...newCreatedPrescriptions];
    console.log('All prescriptions saved successfully:', allPrescriptions);
    return allPrescriptions;
  } catch (error) {
    console.error('Error saving prescriptions for patient:', error);
    throw error;
  }
};