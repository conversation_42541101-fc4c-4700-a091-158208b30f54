import { API_URL } from '../config';

export interface GuidanceCategory {
  category_id: number;
  name: string;
  description: string | null;
  parent_id: number | null;
  parent_name?: string;
  guidance_count?: number;
  level?: number;
  path?: number[];
  created_at: string;
  updated_at: string;
}

export interface GuidanceTag {
  tag_id: number;
  name: string;
  usage_count?: number;
  created_at: string;
}

export interface GuidanceAccess {
  access_id: number;
  guidance_id: number;
  role: string;
  can_view: boolean;
  can_edit: boolean;
  created_at: string;
  updated_at: string;
}

export interface GuidanceVersion {
  version_id: number;
  guidance_id: number;
  version_number: number;
  title: string;
  content: string;
  summary: string | null;
  category_id: number | null;
  created_by: number | null;
  created_by_username?: string;
  created_at: string;
}

export interface GuidanceAudit {
  audit_id: number;
  guidance_id: number;
  action: 'create' | 'update' | 'delete' | 'publish' | 'unpublish' | 'update_access';
  details: any;
  performed_by: number | null;
  performed_by_username?: string;
  performed_at: string;
}

export interface ClinicalGuidance {
  guidance_id: number;
  title: string;
  content: string;
  summary: string | null;
  category_id: number | null;
  category_name?: string;
  context_key: string | null;
  is_published: boolean;
  created_by: number | null;
  created_by_username?: string;
  created_at: string;
  updated_at: string;
  tags?: string[];
}

export interface GuidanceSearchOptions {
  search?: string;
  categoryId?: number;
  contextKey?: string;
  tags?: string[];
  publishedOnly?: boolean;
}

/**
 * Get all clinical guidance entries
 * @param options Search options
 * @returns Array of guidance entries
 */
export const getAllGuidance = async (options: GuidanceSearchOptions = {}): Promise<ClinicalGuidance[]> => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('No authentication token found');
    }

    // Build query string
    const params = new URLSearchParams();
    if (options.search) params.append('search', options.search);
    if (options.categoryId) params.append('categoryId', options.categoryId.toString());
    if (options.contextKey) params.append('contextKey', options.contextKey);
    if (options.tags && options.tags.length > 0) params.append('tags', options.tags.join(','));
    if (options.publishedOnly !== undefined) params.append('publishedOnly', options.publishedOnly.toString());

    const queryString = params.toString() ? `?${params.toString()}` : '';
    
    const response = await fetch(`${API_URL}/api/clinical-guidance${queryString}`, {
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': token
      }
    });

    if (!response.ok) {
      throw new Error(`Error fetching guidance: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error in getAllGuidance:', error);
    throw error;
  }
};

/**
 * Get a single guidance entry by ID
 * @param id Guidance ID
 * @returns Guidance entry
 */
export const getGuidanceById = async (id: number): Promise<ClinicalGuidance> => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('No authentication token found');
    }

    const response = await fetch(`${API_URL}/api/clinical-guidance/${id}`, {
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': token
      }
    });

    if (!response.ok) {
      throw new Error(`Error fetching guidance: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error in getGuidanceById(${id}):`, error);
    throw error;
  }
};

/**
 * Create a new guidance entry
 * @param guidance Guidance data
 * @returns Created guidance entry
 */
export const createGuidance = async (guidance: Partial<ClinicalGuidance>): Promise<ClinicalGuidance> => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('No authentication token found');
    }

    const response = await fetch(`${API_URL}/api/clinical-guidance`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': token
      },
      body: JSON.stringify(guidance)
    });

    if (!response.ok) {
      throw new Error(`Error creating guidance: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error in createGuidance:', error);
    throw error;
  }
};

/**
 * Update a guidance entry
 * @param id Guidance ID
 * @param guidance Updated guidance data
 * @returns Updated guidance entry
 */
export const updateGuidance = async (id: number, guidance: Partial<ClinicalGuidance>): Promise<ClinicalGuidance> => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('No authentication token found');
    }

    const response = await fetch(`${API_URL}/api/clinical-guidance/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': token
      },
      body: JSON.stringify(guidance)
    });

    if (!response.ok) {
      throw new Error(`Error updating guidance: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error in updateGuidance(${id}):`, error);
    throw error;
  }
};

/**
 * Delete a guidance entry
 * @param id Guidance ID
 * @returns Success message
 */
export const deleteGuidance = async (id: number): Promise<{ msg: string }> => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('No authentication token found');
    }

    const response = await fetch(`${API_URL}/api/clinical-guidance/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': token
      }
    });

    if (!response.ok) {
      throw new Error(`Error deleting guidance: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error in deleteGuidance(${id}):`, error);
    throw error;
  }
};

/**
 * Get all guidance categories
 * @param includeHierarchy Whether to include hierarchical structure
 * @returns Array of categories
 */
export const getAllCategories = async (includeHierarchy: boolean = false): Promise<GuidanceCategory[]> => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('No authentication token found');
    }

    const response = await fetch(`${API_URL}/api/clinical-guidance/categories/all?hierarchy=${includeHierarchy}`, {
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': token
      }
    });

    if (!response.ok) {
      throw new Error(`Error fetching categories: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error in getAllCategories:', error);
    throw error;
  }
};

/**
 * Get all guidance tags
 * @returns Array of tags
 */
export const getAllTags = async (): Promise<GuidanceTag[]> => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('No authentication token found');
    }

    const response = await fetch(`${API_URL}/api/clinical-guidance/tags/all`, {
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': token
      }
    });

    if (!response.ok) {
      throw new Error(`Error fetching tags: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error in getAllTags:', error);
    throw error;
  }
};

/**
 * Get version history for a guidance entry
 * @param id Guidance ID
 * @returns Array of versions
 */
export const getVersionHistory = async (id: number): Promise<GuidanceVersion[]> => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('No authentication token found');
    }

    const response = await fetch(`${API_URL}/api/clinical-guidance/${id}/versions`, {
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': token
      }
    });

    if (!response.ok) {
      throw new Error(`Error fetching version history: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error in getVersionHistory(${id}):`, error);
    throw error;
  }
};

/**
 * Get audit trail for a guidance entry
 * @param id Guidance ID
 * @returns Array of audit entries
 */
export const getAuditTrail = async (id: number): Promise<GuidanceAudit[]> => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('No authentication token found');
    }

    const response = await fetch(`${API_URL}/api/clinical-guidance/${id}/audit`, {
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': token
      }
    });

    if (!response.ok) {
      throw new Error(`Error fetching audit trail: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error in getAuditTrail(${id}):`, error);
    throw error;
  }
};

/**
 * Get access control for a guidance entry
 * @param id Guidance ID
 * @returns Array of access control entries
 */
export const getAccess = async (id: number): Promise<GuidanceAccess[]> => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('No authentication token found');
    }

    const response = await fetch(`${API_URL}/api/clinical-guidance/${id}/access`, {
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': token
      }
    });

    if (!response.ok) {
      throw new Error(`Error fetching access control: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error in getAccess(${id}):`, error);
    throw error;
  }
};

/**
 * Update access control for a guidance entry
 * @param id Guidance ID
 * @param accessList List of access control entries
 * @returns Success message
 */
export const updateAccess = async (id: number, accessList: Partial<GuidanceAccess>[]): Promise<{ msg: string }> => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('No authentication token found');
    }

    const response = await fetch(`${API_URL}/api/clinical-guidance/${id}/access`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': token
      },
      body: JSON.stringify({ accessList })
    });

    if (!response.ok) {
      throw new Error(`Error updating access control: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error in updateAccess(${id}):`, error);
    throw error;
  }
};

/**
 * Get guidance by context key
 * @param contextKey Context key
 * @returns Array of guidance entries
 */
export const getGuidanceByContext = async (contextKey: string): Promise<ClinicalGuidance[]> => {
  try {
    const token = localStorage.getItem('token');
    if (!token) {
      throw new Error('No authentication token found');
    }

    const response = await fetch(`${API_URL}/api/clinical-guidance/context/${contextKey}`, {
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': token
      }
    });

    if (!response.ok) {
      throw new Error(`Error fetching guidance by context: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    console.error(`Error in getGuidanceByContext(${contextKey}):`, error);
    throw error;
  }
};
