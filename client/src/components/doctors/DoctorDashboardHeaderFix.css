/* Direct fix for black bar behind Add Patient button and Last updated text */

/* Global fix for any black bars in the doctor dashboard */
.MuiPaper-root[style*="linear-gradient"] * {
  background-color: transparent !important;
}

/* Target the specific Box component in the header that contains the Add Patient button and Last updated text */
.MuiPaper-root[style*="linear-gradient"] .MuiGrid-item:last-child .MuiBox-root {
  background-color: transparent !important;
}

/* Target the specific elements using the classes we added */
.add-patient-button {
  background-color: white !important;
}

.last-updated-chip {
  background-color: rgba(255, 255, 255, 0.15) !important;
}

.refresh-button {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Additional selectors for dark mode */
.dark-mode .MuiPaper-root[style*="linear-gradient"] .MuiGrid-item:last-child .MuiBox-root {
  background-color: transparent !important;
}

/* Target the Button and Chip components inside the Box */
.dark-mode .MuiPaper-root[style*="linear-gradient"] .MuiGrid-item:last-child .MuiBox-root .MuiButton-root,
.dark-mode .MuiPaper-root[style*="linear-gradient"] .MuiGrid-item:last-child .MuiBox-root .MuiChip-root,
.dark-mode .MuiPaper-root[style*="linear-gradient"] .MuiGrid-item:last-child .MuiBox-root .MuiIconButton-root {
  background-color: rgba(255, 255, 255, 0.15) !important;
}

/* Specific override for the Add Patient button */
.dark-mode .MuiPaper-root[style*="linear-gradient"] .MuiGrid-item:last-child .MuiBox-root .MuiButton-root,
.dark-mode .add-patient-button {
  background-color: white !important;
}

/* Ensure the tooltip and icon button have the correct background */
.dark-mode .MuiPaper-root[style*="linear-gradient"] .MuiGrid-item:last-child .MuiBox-root .MuiIconButton-root,
.dark-mode .refresh-button {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Ensure the Last updated chip has the correct background */
.dark-mode .MuiPaper-root[style*="linear-gradient"] .MuiGrid-item:last-child .MuiBox-root .MuiChip-root,
.dark-mode .last-updated-chip {
  background-color: rgba(255, 255, 255, 0.15) !important;
}

/* Fix for any remaining black bars in dark mode */
.dark-mode .MuiPaper-root .MuiBox-root,
.dark-mode .MuiPaper-root .MuiGrid-root,
.dark-mode .MuiPaper-root .MuiCardContent-root {
  background-color: transparent !important;
}
