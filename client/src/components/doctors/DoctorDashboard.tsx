import React, { useEffect, useState, useContext, useCallback, useMemo } from 'react';
import './DoctorDashboardHeaderFix.css'; // Import the CSS fix for the black bar
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Paper,
  List,
  ListItem,
  ListItemText,
  Divider,
  CircularProgress,
  Avatar,
  Button,
  IconButton,
  Chip,
  Tooltip,
  Badge,
  LinearProgress,
  useTheme,
  alpha,
  Tab,
  Tabs,
  Alert,
  AlertTitle,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  SelectChangeEvent,
  InputAdornment
} from '@mui/material';
import LoadingSpinner from '../common/LoadingSpinner';
import { styled } from '@mui/material/styles';
import { useNavigate } from 'react-router-dom';
import AuthContext from '../../context/AuthContext';
import { API_URL } from '../../config';
import {
  Person as PersonIcon,
  MedicalServices as MedicalServicesIcon,
  CalendarToday as CalendarIcon,
  AccessTime as AccessTimeIcon,
  Refresh as RefreshIcon,
  ArrowForward as ArrowForwardIcon,
  LocalHospital as LocalHospitalIcon,
  Assignment as AssignmentIcon,
  Notifications as NotificationsIcon,
  Dashboard as DashboardIcon,
  TrendingUp as TrendingUpIcon,
  Event as EventIcon,
  People as PeopleIcon,
  MoreVert as MoreVertIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Search as SearchIcon
} from '@mui/icons-material';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  Filler
} from 'chart.js';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  ChartTooltip,
  Legend,
  Filler
);

interface DashboardStats {
  totalPatients: number;
  assignedPatients: number;
  patientsWithVisits?: number;
  totalVisits?: number;
  appointmentsCount?: number;
  upcomingAppointments?: number;
}

interface Appointment {
  appointment_id: number;
  patient_id: number;
  patient_name: string;
  date: string;
  reason: string;
  start_time?: string;
  end_time?: string;
  status?: string;
  notes?: string;
}

interface Patient {
  patient_id: number;
  first_name: string;
  last_name: string;
  date_of_birth: string;
  gender: string;
  phone?: string;
  email?: string;
  address?: string;
  emergency_contact?: string;
  medical_history?: string;
  allergies?: string;
  last_visit_date?: string;
  visit_count: number; // Changed from optional to required with default 0
  doctor_name?: string; // Added to match the type in types/index.ts
}

interface ActivityItem {
  id: number;
  type: 'visit' | 'prescription' | 'note' | 'test' | 'appointment';
  patient_id: number;
  patient_name: string;
  timestamp: string;
  description: string;
  status?: 'completed' | 'pending' | 'cancelled';
}

interface DoctorDetails {
  doctor_id: number;
  first_name: string;
  last_name: string;
  specialty: string;
  license_number: string;
  years_of_experience?: number;
  education?: string;
  email?: string;
  phone?: string;
  patients_count?: number;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

// TabPanel component
function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`dashboard-tabpanel-${index}`}
      aria-labelledby={`dashboard-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const StyledCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  borderRadius: 12,
  boxShadow: `0 4px 12px ${theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.2)' : 'rgba(0,0,0,0.05)'}`,
  transition: 'transform 0.3s ease, box-shadow 0.3s ease',
  overflow: 'hidden',
  backgroundColor: theme.palette.mode === 'dark' ? '#1e1e1e' : theme.palette.background.paper,
  border: theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : 'none',
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: `0 8px 24px ${theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.3)' : 'rgba(0,0,0,0.1)'}`,
  },
  '& .MuiCardContent-root': {
    flexGrow: 1,
    padding: theme.spacing(2.5),
    backgroundColor: 'transparent !important', // Ensure transparency
  },
  '& .MuiBox-root': {
    backgroundColor: 'transparent !important', // Ensure transparency
  },
  '& .MuiGrid-root': {
    backgroundColor: 'transparent !important', // Ensure transparency for Grid components
  },
  '& .MuiChip-root': {
    backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.15)' : undefined, // Proper background for chips in dark mode
  },
}));

const DashboardCard = styled(Card)(({ theme }) => ({
  borderRadius: 12,
  boxShadow: `0 4px 12px ${theme.palette.mode === 'dark' ? 'rgba(0,0,0,0.2)' : 'rgba(0,0,0,0.05)'}`,
  height: '100%',
  backgroundColor: theme.palette.mode === 'dark' ? '#1e1e1e' : theme.palette.background.paper,
  border: theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : 'none',
  '& .MuiCardContent-root': {
    backgroundColor: 'transparent !important', // Ensure transparency
  },
  '& .MuiBox-root': {
    backgroundColor: 'transparent !important', // Ensure transparency
  },
  '& .MuiGrid-root': {
    backgroundColor: 'transparent !important', // Ensure transparency for Grid components
  },
  '& .MuiChip-root': {
    backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.15)' : undefined, // Proper background for chips in dark mode
  },
  '& .MuiListItem-root': {
    backgroundColor: 'transparent !important', // Ensure transparency for list items
  },
}));

const ClickableListItem = styled(ListItem)(({ theme }) => ({
  cursor: 'pointer',
  transition: 'all 0.2s ease',
  borderRadius: 8,
  margin: '4px 0',
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, theme.palette.mode === 'dark' ? 0.15 : 0.08),
    transform: 'translateX(5px)',
  },
}));

const AppointmentItem = styled(ListItem)(({ theme }) => ({
  cursor: 'pointer',
  transition: 'all 0.2s ease',
  borderRadius: 8,
  margin: '8px 0',
  border: `1px solid ${alpha(theme.palette.divider, theme.palette.mode === 'dark' ? 0.3 : 0.8)}`,
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, theme.palette.mode === 'dark' ? 0.15 : 0.05),
    borderColor: alpha(theme.palette.primary.main, theme.palette.mode === 'dark' ? 0.5 : 0.3),
  },
}));

const DoctorDashboard: React.FC = () => {
  const theme = useTheme();
  const [stats, setStats] = useState<DashboardStats>({ totalPatients: 0, assignedPatients: 0 });
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [recentPatients, setRecentPatients] = useState<Patient[]>([]);
  const [recentActivity, setRecentActivity] = useState<ActivityItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [lastUpdated, setLastUpdated] = useState<string>('');
  const [doctorDetails, setDoctorDetails] = useState<DoctorDetails | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');

  // Success message state
  const [actionSuccess, setActionSuccess] = useState<string | null>(null);
  const { user } = useContext(AuthContext);
  const navigate = useNavigate();

  const fetchDoctorDetails = useCallback(async () => {
    try {
      if (!user?.doctor_id) {
        return;
      }

      const token = localStorage.getItem('token');
      if (!token) {
        return;
      }

      // Fetch doctor details
      const response = await fetch(`${API_URL}/api/doctors/${user.doctor_id}`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        console.error('Failed to fetch doctor details');
        return;
      }

      const data = await response.json();
      setDoctorDetails(data);
    } catch (error) {
      console.error('Error fetching doctor details:', error);
      // Create mock doctor details for development/demo purposes
      setDoctorDetails({
        doctor_id: user?.doctor_id || 0, // Provide a default value to satisfy TypeScript
        first_name: user?.username?.split('_')[0] || 'John',
        last_name: user?.username?.split('_')[1] || 'Doe',
        specialty: 'General Medicine',
        license_number: 'MD12345',
        years_of_experience: 15,
        education: 'Harvard Medical School',
        email: user?.email || '<EMAIL>',
        phone: '************',
        patients_count: stats.assignedPatients
      });
    }
  }, [user?.doctor_id, user?.username, user?.email, stats.assignedPatients]);

  const fetchDashboardData = useCallback(async () => {
    try {
      setLoading(true);
      setRefreshing(true);
      setError(null);

      if (!user?.doctor_id) {
        throw new Error('Doctor ID not found');
      }

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      // Fetch doctor details
      await fetchDoctorDetails();

      // Fetch detailed statistics
      const statsResponse = await fetch(`${API_URL}/api/doctors/detailed-dashboard-stats/${user.doctor_id}`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!statsResponse.ok) {
        const errorData = await statsResponse.text();
        console.error('Stats response error:', errorData);
        throw new Error(`Failed to fetch dashboard statistics: ${errorData}`);
      }

      const statsData = await statsResponse.json();
      setStats(statsData);

      // Fetch upcoming appointments for the week
      const appointmentsResponse = await fetch(
        `${API_URL}/api/doctors/upcoming-appointments/${user.doctor_id}`,
        {
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          }
        }
      );

      if (!appointmentsResponse.ok) {
        const errorData = await appointmentsResponse.text();
        console.error('Appointments response error:', errorData);
        throw new Error(`Failed to fetch appointments: ${errorData}`);
      }

      const appointmentsData = await appointmentsResponse.json();

      // Process appointment data to ensure proper formatting
      const processedAppointments = appointmentsData.map((appointment: any) => {
        console.log('Processing appointment:', appointment);

        // Ensure we have valid start_time and end_time
        return {
          ...appointment,
          // Make sure start_time and end_time are properly formatted
          start_time: appointment.start_time || (appointment.date && appointment.date.includes(' ') ?
            appointment.date.split(' ')[1] : undefined),
          end_time: appointment.end_time || undefined
        };
      });

      console.log('Processed appointments:', processedAppointments);
      setAppointments(processedAppointments);

      // Fetch recent patients
      const patientsResponse = await fetch(
        `${API_URL}/api/doctors/${user.doctor_id}/patients?limit=5`,
        {
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          }
        }
      );

      if (!patientsResponse.ok) {
        const errorData = await patientsResponse.text();
        console.error('Patients response error:', errorData);
        throw new Error(`Failed to fetch patients: ${errorData}`);
      }

      const patientsData = await patientsResponse.json();
      setRecentPatients(patientsData);

      setLastUpdated(new Date().toLocaleTimeString());
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      setError(error instanceof Error ? error.message : 'An error occurred');

      // Set mock data for development/demo purposes
      setStats({
        totalPatients: 120,
        assignedPatients: 42,
        patientsWithVisits: 38,
        totalVisits: 156,
        appointmentsCount: 24,
        upcomingAppointments: 8
      });

      // Mock appointments data
      if (appointments.length === 0) {
        const mockAppointments = generateMockAppointments();
        setAppointments(mockAppointments);
      }

      // Mock patients data
      if (recentPatients.length === 0) {
        const mockPatients = generateMockPatients();
        setRecentPatients(mockPatients);
      }

      // Mock activity data
      if (recentActivity.length === 0) {
        const mockActivity = generateMockActivity();
        setRecentActivity(mockActivity);
      }

      setLastUpdated(new Date().toLocaleTimeString());
    } finally {
      setLoading(false);
      setTimeout(() => setRefreshing(false), 600);
    }
  }, [user?.doctor_id, appointments.length, recentPatients.length, recentActivity.length, fetchDoctorDetails]);

  // Generate mock data for development/demo purposes
  const generateMockAppointments = (): Appointment[] => {
    const mockAppointments: Appointment[] = [];
    const reasons = ['Annual Checkup', 'Follow-up', 'Consultation', 'Vaccination', 'Lab Results Review'];
    const names = ['John Smith', 'Sarah Johnson', 'Michael Brown', 'Emily Davis', 'Robert Wilson', 'Jennifer Lee'];

    const today = new Date();

    for (let i = 0; i < 5; i++) {
      const appointmentDate = new Date(today);
      appointmentDate.setDate(today.getDate() + i);

      mockAppointments.push({
        appointment_id: i + 1,
        patient_id: i + 100,
        patient_name: names[i % names.length],
        date: appointmentDate.toISOString(),
        reason: reasons[i % reasons.length]
      });
    }

    return mockAppointments;
  };

  const generateMockPatients = (): Patient[] => {
    const mockPatients: Patient[] = [];
    const names = [
      { first: 'John', last: 'Smith' },
      { first: 'Sarah', last: 'Johnson' },
      { first: 'Michael', last: 'Brown' },
      { first: 'Emily', last: 'Davis' },
      { first: 'Robert', last: 'Wilson' }
    ];
    const genders = ['Male', 'Female'];

    for (let i = 0; i < 5; i++) {
      const birthDate = new Date();
      birthDate.setFullYear(birthDate.getFullYear() - 20 - Math.floor(Math.random() * 60));

      const lastVisitDate = new Date();
      lastVisitDate.setDate(lastVisitDate.getDate() - Math.floor(Math.random() * 30));

      mockPatients.push({
        patient_id: i + 100,
        first_name: names[i].first,
        last_name: names[i].last,
        date_of_birth: birthDate.toISOString().split('T')[0],
        gender: genders[i % 2],
        phone: `555-${100 + i}`,
        email: `${names[i].first.toLowerCase()}.${names[i].last.toLowerCase()}@example.com`,
        last_visit_date: lastVisitDate.toISOString().split('T')[0],
        visit_count: Math.floor(Math.random() * 5) + 1 // Add random number of visits between 1 and 5
      });
    }

    return mockPatients;
  };

  const generateMockActivity = (): ActivityItem[] => {
    const mockActivity: ActivityItem[] = [];
    const activityTypes: Array<'visit' | 'prescription' | 'note' | 'test' | 'appointment'> = [
      'visit', 'prescription', 'note', 'test', 'appointment'
    ];
    const statuses: Array<'completed' | 'pending' | 'cancelled'> = [
      'completed', 'pending', 'cancelled'
    ];
    const descriptions = [
      'Regular checkup',
      'Prescribed medication for hypertension',
      'Added notes from consultation',
      'Ordered blood test',
      'Scheduled follow-up appointment',
      'Reviewed test results',
      'Updated patient history',
      'Vaccination administered',
      'Referred to specialist'
    ];

    // Use the mock patients to generate activity
    const patients = generateMockPatients();

    for (let i = 0; i < 10; i++) {
      const patient = patients[i % patients.length];
      const activityDate = new Date();
      activityDate.setDate(activityDate.getDate() - i);
      activityDate.setHours(9 + Math.floor(Math.random() * 8));
      activityDate.setMinutes(Math.floor(Math.random() * 60));

      mockActivity.push({
        id: i + 1,
        type: activityTypes[i % activityTypes.length],
        patient_id: patient.patient_id,
        patient_name: `${patient.first_name} ${patient.last_name}`,
        timestamp: activityDate.toISOString(),
        description: descriptions[i % descriptions.length],
        status: i < 7 ? 'completed' : statuses[i % statuses.length]
      });
    }

    // Sort by timestamp (most recent first)
    return mockActivity.sort((a, b) =>
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  };

  useEffect(() => {
    if (user?.doctor_id) {
      fetchDashboardData();
    } else {
      setLoading(false);
      setError('Doctor ID not found. Please ensure your account is properly set up as a doctor.');
    }
  }, [user?.doctor_id, fetchDashboardData]);

  // Debug appointments data when it changes
  useEffect(() => {
    if (appointments.length > 0) {
      console.log('Appointments data loaded:', appointments);
      appointments.forEach((appointment, index) => {
        console.log(`Appointment ${index + 1}:`, {
          id: appointment.appointment_id,
          patient: appointment.patient_name,
          date: appointment.date,
          start_time: appointment.start_time,
          end_time: appointment.end_time,
          formatted_date: formatDateShort(appointment.date),
          formatted_start_time: appointment.start_time ? formatTime(appointment.start_time) : 'No start time',
          formatted_end_time: appointment.end_time ? formatTime(appointment.end_time) : 'No end time'
        });
      });
    }
  }, [appointments]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatDateShort = (dateString: string) => {
    if (!dateString) return '';

    try {
      // If the date string contains time information, extract just the date part
      let dateToFormat = dateString;

      if (dateString.includes(' ')) {
        // Handle format like "2025-04-28 09:00:00"
        dateToFormat = dateString.split(' ')[0];
      } else if (dateString.includes('T')) {
        // Handle ISO format like "2025-04-28T09:00:00Z"
        dateToFormat = dateString.split('T')[0];
      }

      const date = new Date(dateToFormat);

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        console.error('Invalid date for formatDateShort:', dateString);
        return 'Invalid date';
      }

      return date.toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric'
      });
    } catch (error) {
      console.error('Error in formatDateShort:', error, 'Input was:', dateString);
      return 'Invalid date';
    }
  };

  const formatTime = (timeString: string) => {
    if (!timeString) return 'Time not specified';

    try {
      console.log('Formatting time:', timeString);

      // Handle ISO date strings (with T)
      if (timeString.includes('T')) {
        const date = new Date(timeString);
        return date.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit'
        });
      }

      // Handle date-only strings (YYYY-MM-DD)
      if (timeString.match(/^\d{4}-\d{2}-\d{2}$/)) {
        return 'Time not specified';
      }

      // Handle date-time strings without T (YYYY-MM-DD HH:MM:SS)
      if (timeString.includes(' ') && timeString.split(' ')[1].includes(':')) {
        const timePart = timeString.split(' ')[1];
        const [hours, minutes] = timePart.split(':').map(Number);

        // Format as 12-hour time
        const period = hours >= 12 ? 'PM' : 'AM';
        const hour12 = hours % 12 || 12;
        return `${hour12}:${minutes.toString().padStart(2, '0')} ${period}`;
      }

      // Handle time-only strings (HH:MM:SS)
      if (timeString.includes(':')) {
        const [hours, minutes] = timeString.split(':').map(Number);

        // Check if we have valid numbers
        if (!isNaN(hours) && !isNaN(minutes)) {
          // Format as 12-hour time
          const period = hours >= 12 ? 'PM' : 'AM';
          const hour12 = hours % 12 || 12;
          return `${hour12}:${minutes.toString().padStart(2, '0')} ${period}`;
        }
      }

      // Default fallback
      const date = new Date(timeString);
      if (!isNaN(date.getTime())) {
        return date.toLocaleTimeString('en-US', {
          hour: '2-digit',
          minute: '2-digit'
        });
      }

      console.error('Could not parse time format:', timeString);
      return 'Invalid time';
    } catch (error) {
      console.error('Error formatting time:', error, 'Input was:', timeString);
      return 'Invalid time';
    }
  };

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  const handleAppointmentClick = (patientId: number) => {
    if (patientId) {
      navigate(`/patients/${patientId}`);
    } else {
      console.error('Patient ID is undefined, cannot navigate');
    }
  };

  const handlePatientClick = (patientId: number) => {
    if (patientId) {
      navigate(`/patients/${patientId}`);
    }
  };

  const handleRefresh = () => {
    fetchDashboardData();
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Add patient handler
  const handleAddPatient = () => {
    // Navigate to the patient creation page
    navigate('/patients/new');
  };

  // Handle search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  // Filter patients based on search term
  const filteredPatients = useMemo(() => {
    if (!searchTerm.trim()) return recentPatients;

    const lowerCaseSearch = searchTerm.toLowerCase();
    return recentPatients.filter(patient =>
      patient.first_name.toLowerCase().includes(lowerCaseSearch) ||
      patient.last_name.toLowerCase().includes(lowerCaseSearch) ||
      `${patient.first_name} ${patient.last_name}`.toLowerCase().includes(lowerCaseSearch) ||
      (patient.phone && patient.phone.includes(lowerCaseSearch)) ||
      (patient.email && patient.email.toLowerCase().includes(lowerCaseSearch))
    );
  }, [recentPatients, searchTerm]);

  // Success message handler
  const handleActionSuccess = (message: string) => {
    setActionSuccess(message);
    // Clear success message after 5 seconds
    setTimeout(() => {
      setActionSuccess(null);
    }, 5000);
    // Refresh dashboard data
    fetchDashboardData();
  };

  // Generate chart data for patient visits
  const generateVisitChartData = () => {
    const labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const data = Array.from({ length: 12 }, () => Math.floor(Math.random() * 20) + 5);

    return {
      labels,
      datasets: [
        {
          label: 'Patient Visits',
          data,
          borderColor: theme.palette.primary.main,
          backgroundColor: alpha(theme.palette.primary.main, 0.1),
          tension: 0.3,
          fill: true,
          borderWidth: 2,
          pointRadius: 3,
          pointBackgroundColor: theme.palette.primary.main
        }
      ]
    };
  };

  // Generate chart data for patient demographics
  const generatePatientDemographicsData = () => {
    return {
      labels: ['Male', 'Female', 'Other'],
      datasets: [
        {
          data: [58, 42, 0],
          backgroundColor: [
            alpha(theme.palette.primary.main, 0.7),
            alpha(theme.palette.secondary.main, 0.7),
            alpha(theme.palette.warning.main, 0.7)
          ],
          borderColor: [
            theme.palette.primary.main,
            theme.palette.secondary.main,
            theme.palette.warning.main
          ],
          borderWidth: 1
        }
      ]
    };
  };

  if (loading && !refreshing) {
    return <LoadingSpinner size="large" message="Loading doctor dashboard..." fullScreen />;
  }

  return (
    <Box sx={{ flexGrow: 1, p: 3 }}>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 4,
          borderRadius: 2,
          background: 'linear-gradient(to right, #2C4B2B, #A1A43A)', // Dark Green to Olive Green from logo
          color: 'white',
          position: 'relative',
          overflow: 'hidden',
          boxShadow: '0 4px 12px rgba(44, 75, 43, 0.15)'
        }}
      >
        <Box sx={{
          position: 'absolute',
          top: 0,
          right: 0,
          width: '30%',
          height: '100%',
          opacity: 0.15,
          background: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23F2A65A' fill-opacity='0.6'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          backgroundSize: 'cover'
        }} />

        <Grid container spacing={2} alignItems="center">
          <Grid item>
            <Avatar
              sx={{
                width: 60,
                height: 60,
                bgcolor: 'white',
                color: '#D97B3A', // Orange from logo
                boxShadow: '0 4px 8px rgba(0,0,0,0.15)'
              }}
            >
              <MedicalServicesIcon fontSize="large" />
            </Avatar>
          </Grid>
          <Grid item xs>
            <Typography variant="h4" fontWeight="500">
              Doctor Dashboard
            </Typography>
            <Box sx={{ mt: 0.5 }}>
              <Typography variant="subtitle1" sx={{ opacity: 0.9, fontWeight: 600, fontSize: '1.1rem' }}>
                Welcome back, Dr. {doctorDetails ?
                  `${doctorDetails.first_name} ${doctorDetails.last_name}` :
                  (user?.username ?
                    (user.username.includes('_') ?
                      `${user.username.split('_')[0]} ${user.username.split('_')[1]}` :
                      user.username) :
                    'Doctor')}
              </Typography>
              <Typography variant="body2" sx={{ opacity: 0.8, mt: 0.5, mb: 1 }}>
                {new Date().toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 1 }}>
                {doctorDetails?.specialty && (
                  <Chip
                    icon={<LocalHospitalIcon />}
                    label={doctorDetails.specialty}
                    size="small"
                    sx={{
                      bgcolor: 'rgba(255,255,255,0.15)',
                      color: 'white',
                      '& .MuiChip-icon': { color: 'white' }
                    }}
                  />
                )}
                {doctorDetails?.license_number && (
                  <Chip
                    icon={<AssignmentIcon />}
                    label={`License: ${doctorDetails.license_number}`}
                    size="small"
                    sx={{
                      bgcolor: 'rgba(255,255,255,0.15)',
                      color: 'white',
                      '& .MuiChip-icon': { color: 'white' }
                    }}
                  />
                )}
                {doctorDetails?.patients_count !== undefined && (
                  <Chip
                    icon={<PeopleIcon />}
                    label={`${doctorDetails.patients_count || stats.assignedPatients} Patients`}
                    size="small"
                    sx={{
                      bgcolor: 'rgba(255,255,255,0.15)',
                      color: 'white',
                      '& .MuiChip-icon': { color: 'white' }
                    }}
                  />
                )}
                {appointments?.length > 0 && (
                  <Chip
                    icon={<CalendarIcon />}
                    label={`${appointments.length} Upcoming Appointments`}
                    size="small"
                    sx={{
                      bgcolor: 'rgba(255,255,255,0.15)',
                      color: 'white',
                      '& .MuiChip-icon': { color: 'white' }
                    }}
                  />
                )}
              </Box>
            </Box>
          </Grid>
          <Grid item>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 2,
              backgroundColor: 'transparent !important' // Ensure transparent background
            }}>
              <Button
                variant="contained"
                startIcon={<PersonIcon />}
                onClick={handleAddPatient}
                sx={{
                  bgcolor: 'white',
                  color: '#1976d2',
                  fontWeight: 600,
                  '&:hover': { bgcolor: 'rgba(255,255,255,0.9)' },
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                  mr: 1
                }}
                className="add-patient-button" // Add class for CSS targeting
              >
                Add Patient
              </Button>

              {lastUpdated && (
                <Chip
                  icon={<InfoIcon />}
                  label={`Last updated: ${lastUpdated}`}
                  sx={{
                    bgcolor: 'rgba(255,255,255,0.15)',
                    color: 'white',
                    '& .MuiChip-icon': { color: 'white' }
                  }}
                  className="last-updated-chip" // Add class for CSS targeting
                />
              )}
              <Tooltip title="Refresh dashboard">
                <IconButton
                  color="inherit"
                  onClick={handleRefresh}
                  disabled={refreshing}
                  sx={{
                    bgcolor: 'rgba(255,255,255,0.1)',
                    '&:hover': { bgcolor: 'rgba(255,255,255,0.2)' }
                  }}
                  className="refresh-button" // Add class for CSS targeting
                >
                  {refreshing ? <CircularProgress size={24} color="inherit" /> : <RefreshIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {error && (
        <Alert
          severity="error"
          sx={{
            mb: 3,
            borderRadius: 2,
            boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
          }}
        >
          <AlertTitle>Error</AlertTitle>
          {error}
        </Alert>
      )}

      {actionSuccess && (
        <Alert
          severity="success"
          sx={{
            mb: 3,
            borderRadius: 2,
            boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
          }}
        >
          <AlertTitle>Success</AlertTitle>
          {actionSuccess}
        </Alert>
      )}

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StyledCard>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2, bgcolor: 'transparent' }}>
                <Typography variant="h6" fontWeight="500">Total Patients</Typography>
                <Avatar sx={{ bgcolor: alpha('#2C4B2B', 0.1), color: '#2C4B2B' }}>
                  <PeopleIcon />
                </Avatar>
              </Box>
              <Typography variant="h3" fontWeight="600" color="primary.main">
                {stats.totalPatients}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                Total patients on the platform
              </Typography>
            </CardContent>
          </StyledCard>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StyledCard>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2, bgcolor: 'transparent' }}>
                <Typography variant="h6" fontWeight="500">Your Patients</Typography>
                <Avatar sx={{ bgcolor: alpha('#A1A43A', 0.1), color: '#A1A43A' }}>
                  <PersonIcon />
                </Avatar>
              </Box>
              <Typography variant="h3" fontWeight="600" color="success.main">
                {stats.assignedPatients}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                Patients assigned to you
              </Typography>
            </CardContent>
          </StyledCard>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StyledCard>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2, bgcolor: 'transparent' }}>
                <Typography variant="h6" fontWeight="500">Total Visits</Typography>
                <Avatar sx={{ bgcolor: alpha('#D97B3A', 0.1), color: '#D97B3A' }}>
                  <AssignmentIcon />
                </Avatar>
              </Box>
              <Typography variant="h3" fontWeight="600" color="info.main">
                {stats.totalVisits || 0}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                Patient visits conducted
              </Typography>
            </CardContent>
          </StyledCard>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StyledCard>
            <CardContent>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2, bgcolor: 'transparent' }}>
                <Typography variant="h6" fontWeight="500">Upcoming</Typography>
                <Avatar sx={{ bgcolor: alpha('#F6B21A', 0.1), color: '#F6B21A' }}>
                  <CalendarIcon />
                </Avatar>
              </Box>
              <Typography variant="h3" fontWeight="600" color="warning.main">
                {stats.upcomingAppointments || appointments.length}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                Upcoming appointments
              </Typography>
            </CardContent>
          </StyledCard>
        </Grid>
      </Grid>

      {/* Main Dashboard Content */}
      <Grid container spacing={4}>
        {/* Left Column - Appointments and Activity */}
        <Grid item xs={12} md={8}>
          {/* Upcoming Appointments */}
          <DashboardCard sx={{ mb: 4 }}>
            <CardContent sx={{ p: 0 }}>
              <Box sx={{
                p: 2,
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                borderBottom: `1px solid ${alpha(theme.palette.divider, theme.palette.mode === 'dark' ? 0.2 : 0.1)}`,
                backgroundColor: 'transparent'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Avatar sx={{ bgcolor: alpha('#F6B21A', 0.1), color: '#F6B21A' }}>
                    <CalendarIcon />
                  </Avatar>
                  <Typography variant="h6" fontWeight="500">
                    Upcoming Appointments
                  </Typography>
                </Box>
                <Chip
                  label={`${appointments.length} this week`}
                  size="small"
                  sx={{
                    borderRadius: 1,
                    bgcolor: '#D97B3A', // Orange from logo
                    color: 'white'
                  }}
                />
              </Box>

              <List sx={{ p: 0 }}>
                {appointments.length > 0 ? (
                  appointments.map((appointment, index) => (
                    <React.Fragment key={appointment.appointment_id}>
                      <AppointmentItem
                        onClick={() => handleAppointmentClick(appointment.patient_id)}
                        sx={{ px: 2, py: 1.5 }}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                          <Avatar
                            sx={{
                              bgcolor: alpha(theme.palette.primary.main, 0.1),
                              color: theme.palette.primary.main,
                              mr: 2
                            }}
                          >
                            {appointment.patient_name.charAt(0)}
                          </Avatar>

                          <Box sx={{ flexGrow: 1 }}>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                              <Typography variant="subtitle1" fontWeight="500">
                                {appointment.patient_name}
                              </Typography>
                              <Chip
                                label={(() => {
                                  // For debugging
                                  console.log('Appointment date data:', appointment.date);

                                  if (!appointment.date) {
                                    return 'No date';
                                  }

                                  return formatDateShort(appointment.date);
                                })()}
                                size="small"
                                sx={{
                                  borderRadius: 1,
                                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                                  color: theme.palette.primary.main,
                                  fontWeight: 500
                                }}
                              />
                            </Box>

                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <Typography variant="body2" color="text.secondary">
                                {appointment.reason || 'No reason specified'}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                {appointment.start_time ? (
                                  <>
                                    {formatTime(appointment.start_time)}
                                    {appointment.end_time ? ` - ${formatTime(appointment.end_time)}` : ''}
                                  </>
                                ) : (
                                  'Time not specified'
                                )}
                              </Typography>
                            </Box>
                          </Box>

                          <IconButton
                            size="small"
                            sx={{ ml: 1 }}
                            onClick={(e) => {
                              e.stopPropagation();
                              handleAppointmentClick(appointment.patient_id);
                            }}
                          >
                            <ArrowForwardIcon fontSize="small" />
                          </IconButton>
                        </Box>
                      </AppointmentItem>
                      {index < appointments.length - 1 && <Divider sx={{ mx: 2 }} />}
                    </React.Fragment>
                  ))
                ) : (
                  <Box sx={{ p: 4, textAlign: 'center' }}>
                    <CalendarIcon sx={{ fontSize: 48, color: alpha(theme.palette.text.secondary, 0.2), mb: 2 }} />
                    <Typography variant="h6" color="text.secondary" gutterBottom>
                      No Upcoming Appointments
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      You have no scheduled appointments for this week
                    </Typography>
                  </Box>
                )}
              </List>

              {appointments.length > 0 && (
                <Box sx={{ p: 2, borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`, textAlign: 'center' }}>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<CalendarIcon />}
                    sx={{
                      borderRadius: 2,
                      color: '#F6B21A',
                      borderColor: alpha('#F6B21A', 0.3),
                      '&:hover': {
                        borderColor: '#F6B21A',
                        bgcolor: alpha('#F6B21A', 0.04)
                      }
                    }}
                  >
                    View All Appointments
                  </Button>
                </Box>
              )}
            </CardContent>
          </DashboardCard>

          {/* Recent Activity Feed */}
          <DashboardCard sx={{ mb: 4 }}>
            <CardContent sx={{ p: 0 }}>
              <Box sx={{
                p: 2,
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                borderBottom: `1px solid ${alpha(theme.palette.divider, theme.palette.mode === 'dark' ? 0.2 : 0.1)}`,
                backgroundColor: 'transparent'
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Avatar sx={{ bgcolor: alpha('#A1A43A', 0.1), color: '#A1A43A' }}>
                    <AccessTimeIcon />
                  </Avatar>
                  <Typography variant="h6" fontWeight="500">
                    Recent Activity
                  </Typography>
                </Box>
                <Chip
                  label={`${recentActivity.length} activities`}
                  size="small"
                  sx={{
                    borderRadius: 1,
                    bgcolor: '#A1A43A', // Olive Green from logo
                    color: 'white'
                  }}
                />
              </Box>

              <List sx={{ p: 0 }}>
                {recentActivity.length > 0 ? (
                  recentActivity.slice(0, 5).map((activity, index) => (
                    <React.Fragment key={activity.id}>
                      <ListItem
                        sx={{
                          px: 2,
                          py: 1.5,
                          transition: 'background-color 0.2s ease',
                          '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.05) }
                        }}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'flex-start', width: '100%' }}>
                          <Avatar
                            sx={{
                              bgcolor:
                                activity.type === 'visit' ? alpha(theme.palette.success.main, 0.1) :
                                activity.type === 'prescription' ? alpha(theme.palette.warning.main, 0.1) :
                                activity.type === 'note' ? alpha(theme.palette.info.main, 0.1) :
                                activity.type === 'test' ? alpha(theme.palette.error.main, 0.1) :
                                alpha(theme.palette.primary.main, 0.1),
                              color:
                                activity.type === 'visit' ? theme.palette.success.main :
                                activity.type === 'prescription' ? theme.palette.warning.main :
                                activity.type === 'note' ? theme.palette.info.main :
                                activity.type === 'test' ? theme.palette.error.main :
                                theme.palette.primary.main,
                              mr: 2,
                              width: 40,
                              height: 40
                            }}
                          >
                            {activity.type === 'visit' ? <MedicalServicesIcon fontSize="small" /> :
                             activity.type === 'prescription' ? <AssignmentIcon fontSize="small" /> :
                             activity.type === 'note' ? <InfoIcon fontSize="small" /> :
                             activity.type === 'test' ? <LocalHospitalIcon fontSize="small" /> :
                             <CalendarIcon fontSize="small" />}
                          </Avatar>

                          <Box sx={{ flexGrow: 1 }}>
                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                              <Typography variant="subtitle2" fontWeight="500">
                                {activity.description}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                {formatDateShort(activity.timestamp)} {formatTime(activity.timestamp)}
                              </Typography>
                            </Box>

                            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                              <Typography
                                variant="body2"
                                color="primary"
                                sx={{
                                  cursor: 'pointer',
                                  '&:hover': { textDecoration: 'underline' }
                                }}
                                onClick={() => handlePatientClick(activity.patient_id)}
                              >
                                {activity.patient_name}
                              </Typography>
                              {activity.status && (
                                <Chip
                                  label={activity.status}
                                  size="small"
                                  color={
                                    activity.status === 'completed' ? 'success' :
                                    activity.status === 'pending' ? 'warning' :
                                    'error'
                                  }
                                  sx={{
                                    borderRadius: 1,
                                    height: 20,
                                    '& .MuiChip-label': { px: 1, py: 0.25, fontSize: '0.7rem' }
                                  }}
                                />
                              )}
                            </Box>
                          </Box>
                        </Box>
                      </ListItem>
                      {index < recentActivity.slice(0, 5).length - 1 && <Divider sx={{ mx: 2 }} />}
                    </React.Fragment>
                  ))
                ) : (
                  <Box sx={{ p: 4, textAlign: 'center' }}>
                    <AccessTimeIcon sx={{ fontSize: 48, color: alpha(theme.palette.text.secondary, 0.2), mb: 2 }} />
                    <Typography variant="h6" color="text.secondary" gutterBottom>
                      No Recent Activity
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Your recent activities will appear here
                    </Typography>
                  </Box>
                )}
              </List>

              {recentActivity.length > 5 && (
                <Box sx={{ p: 2, borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`, textAlign: 'center' }}>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<AccessTimeIcon />}
                    sx={{
                      borderRadius: 2,
                      color: '#A1A43A',
                      borderColor: alpha('#A1A43A', 0.3),
                      '&:hover': {
                        borderColor: '#A1A43A',
                        bgcolor: alpha('#A1A43A', 0.04)
                      }
                    }}
                  >
                    View All Activity
                  </Button>
                </Box>
              )}
            </CardContent>
          </DashboardCard>

          {/* Patient Activity Chart */}
          <DashboardCard>
            <CardContent>
              <Box sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                mb: 3
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Avatar sx={{ bgcolor: alpha('#D97B3A', 0.1), color: '#D97B3A' }}>
                    <TrendingUpIcon />
                  </Avatar>
                  <Typography variant="h6" fontWeight="500">
                    Patient Visits
                  </Typography>
                </Box>
                <Chip
                  label="This Year"
                  size="small"
                  sx={{
                    borderRadius: 1,
                    bgcolor: '#D97B3A', // Orange from logo
                    color: 'white'
                  }}
                />
              </Box>

              <Box sx={{ height: 300 }}>
                <Line
                  data={generateVisitChartData()}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                      legend: {
                        display: true,
                        position: 'top',
                        labels: {
                          boxWidth: 10,
                          usePointStyle: true,
                          pointStyle: 'circle'
                        }
                      },
                      tooltip: {
                        mode: 'index',
                        intersect: false
                      }
                    },
                    scales: {
                      x: {
                        grid: {
                          display: false
                        }
                      },
                      y: {
                        beginAtZero: true,
                        border: {
                          display: false
                        },
                        grid: {
                          display: true
                        },
                        ticks: {
                          precision: 0
                        }
                      }
                    }
                  }}
                />
              </Box>
            </CardContent>
          </DashboardCard>
        </Grid>

        {/* Right Column - Recent Patients and Demographics */}
        <Grid item xs={12} md={4}>
          {/* Patient Management */}
          <DashboardCard
            sx={{
              mb: 4,
              overflow: 'hidden',
              borderRadius: 3,
              boxShadow: '0 10px 30px rgba(0,0,0,0.05)',
              transition: 'transform 0.3s ease, box-shadow 0.3s ease',
              '&:hover': {
                boxShadow: '0 12px 40px rgba(0,0,0,0.08)',
              }
            }}
          >
            <CardContent sx={{ p: 0 }}>
              <Box sx={{
                p: 2,
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                borderBottom: `1px solid ${alpha(theme.palette.divider, 0.08)}`,
                background: 'transparent',
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                  <Avatar sx={{
                    bgcolor: 'transparent',
                    color: '#2C4B2B', // Dark Green from logo
                    width: 40,
                    height: 40,
                    border: `2px solid ${alpha('#2C4B2B', 0.2)}`,
                  }}>
                    <PersonIcon sx={{ fontSize: '1.2rem' }} />
                  </Avatar>
                  <Box>
                    <Typography variant="subtitle1" fontWeight="600" sx={{ lineHeight: 1.2 }}>
                      Your Patients
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {stats.assignedPatients} patients assigned to you
                    </Typography>
                  </Box>
                </Box>
                <Button
                  variant="outlined"
                  startIcon={<i className="fas fa-plus"></i>}
                  size="small"
                  onClick={handleAddPatient}
                  sx={{
                    borderRadius: 1.5,
                    textTransform: 'none',
                    fontWeight: 500,
                    px: 1.5,
                    py: 0.5,
                    color: '#2C4B2B',
                    borderColor: alpha('#2C4B2B', 0.3),
                    '&:hover': {
                      borderColor: '#2C4B2B',
                      bgcolor: alpha('#2C4B2B', 0.04)
                    }
                  }}
                >
                  Add Patient
                </Button>
              </Box>

              {/* Search Bar */}
              <Box sx={{
                px: 2,
                py: 1.5,
                borderBottom: `1px solid ${alpha(theme.palette.divider, 0.08)}`,
                background: alpha(theme.palette.background.paper, 0.5)
              }}>
                <TextField
                  fullWidth
                  size="small"
                  placeholder="Search patients by name, phone, or email..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon fontSize="small" color="action" />
                      </InputAdornment>
                    ),
                    endAdornment: searchTerm && (
                      <InputAdornment position="end">
                        <IconButton
                          size="small"
                          onClick={() => setSearchTerm('')}
                          edge="end"
                        >
                          <i className="fas fa-times" style={{ fontSize: '0.7rem' }}></i>
                        </IconButton>
                      </InputAdornment>
                    ),
                    sx: {
                      borderRadius: 2,
                      bgcolor: alpha(theme.palette.common.white, 0.8),
                      '&:hover': {
                        bgcolor: theme.palette.common.white,
                      },
                      boxShadow: '0 2px 6px rgba(0,0,0,0.03)',
                      transition: 'all 0.2s ease',
                    }
                  }}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      '& fieldset': {
                        borderColor: alpha(theme.palette.divider, 0.3),
                      },
                      '&:hover fieldset': {
                        borderColor: alpha(theme.palette.primary.main, 0.5),
                      },
                      '&.Mui-focused fieldset': {
                        borderColor: theme.palette.primary.main,
                      },
                    },
                  }}
                />
              </Box>

              {recentPatients.length > 0 ? (
                <Box
                  sx={{
                    maxHeight: { xs: '450px', sm: '400px', md: '350px' },
                    overflowY: 'auto',
                    '&::-webkit-scrollbar': {
                      width: '6px',
                    },
                    '&::-webkit-scrollbar-track': {
                      background: alpha(theme.palette.divider, 0.1),
                      borderRadius: 3,
                    },
                    '&::-webkit-scrollbar-thumb': {
                      background: alpha(theme.palette.primary.main, 0.2),
                      borderRadius: 3,
                      '&:hover': {
                        background: alpha(theme.palette.primary.main, 0.3),
                      }
                    },
                    px: { xs: 1, sm: 1.5, md: 2 },
                    py: 1.5
                  }}
                >
                  {filteredPatients.length === 0 && searchTerm && (
                    <Box sx={{
                      textAlign: 'center',
                      py: 4,
                      px: 2,
                      bgcolor: alpha(theme.palette.background.paper, 0.5),
                      borderRadius: 2,
                      border: `1px dashed ${alpha(theme.palette.divider, 0.3)}`
                    }}>
                      <SearchIcon sx={{ fontSize: '2rem', color: alpha(theme.palette.text.secondary, 0.5), mb: 1 }} />
                      <Typography variant="body1" color="text.secondary" gutterBottom>
                        No patients found matching "{searchTerm}"
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Try a different search term or clear the search
                      </Typography>
                      <Button
                        variant="text"
                        size="small"
                        onClick={() => setSearchTerm('')}
                        sx={{ mt: 2, textTransform: 'none' }}
                      >
                        Clear Search
                      </Button>
                    </Box>
                  )}

                  {filteredPatients.map((patient, index) => (
                    <Paper
                      key={patient.patient_id}
                      elevation={0}
                      sx={{
                        p: { xs: 1.5, sm: 2, md: 2.5 },
                        mb: 1.5,
                        cursor: 'pointer',
                        transition: 'all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1)',
                        borderRadius: 2,
                        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                        position: 'relative',
                        overflow: 'hidden',
                        backgroundColor: alpha(theme.palette.background.paper, 0.7),
                        backdropFilter: 'blur(8px)',
                        '&::before': {
                          content: '""',
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          width: '4px',
                          height: '100%',
                          backgroundColor: patient.gender === 'Female'
                            ? theme.palette.error.main // Orange for female
                            : theme.palette.primary.main, // Dark Green for male
                          opacity: 0.7
                        },
                        '&:hover': {
                          bgcolor: alpha(theme.palette.background.paper, 1),
                          transform: 'translateY(-3px)',
                          boxShadow: '0 8px 24px rgba(0,0,0,0.08)',
                          borderColor: alpha(patient.gender === 'Female'
                            ? theme.palette.error.main
                            : theme.palette.primary.main, 0.3)
                        },
                        '&:active': {
                          transform: 'translateY(-1px)',
                          boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                        }
                      }}
                      onClick={() => handlePatientClick(patient.patient_id)}
                    >
                      <Grid container spacing={1} alignItems="flex-start">
                        {/* Patient Info */}
                        <Grid item xs={12} sm={7} md={8}>
                          <Box sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                            <Box sx={{
                              display: 'flex',
                              alignItems: 'center',
                              mb: 1,
                              pl: 1
                            }}>
                              <Box>
                                <Typography
                                  variant="subtitle1"
                                  fontWeight="600"
                                  sx={{
                                    lineHeight: 1.2,
                                    fontSize: '1rem',
                                    color: patient.gender === 'Female'
                                      ? theme.palette.error.main
                                      : theme.palette.primary.main,
                                    mb: 0.2
                                  }}
                                >
                                  {patient.first_name} {patient.last_name}
                                </Typography>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    <i
                                      className={`fas fa-${patient.gender === 'Female' ? 'venus' : 'mars'}`}
                                      style={{
                                        fontSize: '0.8rem',
                                        color: patient.gender === 'Female' ? theme.palette.error.main : theme.palette.primary.main,
                                        marginRight: '4px'
                                      }}
                                    ></i>
                                    <Typography
                                      variant="caption"
                                      sx={{
                                        fontWeight: 500,
                                        color: patient.gender === 'Female' ? theme.palette.error.main : theme.palette.primary.main
                                      }}
                                    >
                                      {calculateAge(patient.date_of_birth)} yrs
                                    </Typography>
                                  </Box>

                                </Box>
                              </Box>
                            </Box>

                            <Box sx={{
                              display: 'flex',
                              flexDirection: 'column',
                              gap: 0.5,
                              pl: 1,
                              mt: 0.5,
                              ml: 0.5,
                              borderLeft: `1px dashed ${alpha(theme.palette.divider, 0.5)}`,
                              paddingLeft: 2
                            }}>
                              {patient.phone && (
                                <Typography
                                  variant="caption"
                                  sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: 0.8,
                                    color: theme.palette.text.secondary,
                                    fontSize: '0.75rem',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    maxWidth: '100%'
                                  }}
                                >
                                  <i className="fas fa-phone" style={{
                                    fontSize: '0.7rem',
                                    color: theme.palette.info.main,
                                    width: 14,
                                    textAlign: 'center'
                                  }}></i>
                                  {patient.phone}
                                </Typography>
                              )}
                              {patient.email && (
                                <Typography
                                  variant="caption"
                                  sx={{
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: 0.8,
                                    color: theme.palette.text.secondary,
                                    fontSize: '0.75rem',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    maxWidth: '100%'
                                  }}
                                >
                                  <i className="fas fa-envelope" style={{
                                    fontSize: '0.7rem',
                                    color: theme.palette.warning.main,
                                    width: 14,
                                    textAlign: 'center'
                                  }}></i>
                                  {patient.email}
                                </Typography>
                              )}
                              <Typography
                                variant="caption"
                                sx={{
                                  display: 'flex',
                                  alignItems: 'center',
                                  gap: 0.8,
                                  color: theme.palette.text.secondary,
                                  fontSize: '0.75rem',
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  maxWidth: '100%'
                                }}
                              >
                                <i className="fas fa-user-md" style={{
                                  fontSize: '0.7rem',
                                  color: theme.palette.success.main,
                                  width: 14,
                                  textAlign: 'center'
                                }}></i>
                                Doctor: {patient.doctor_name ? patient.doctor_name : 'You'}
                              </Typography>
                            </Box>
                          </Box>
                        </Grid>

                        {/* Right: Actions */}
                        <Grid item xs={12} sm={5} md={4} sx={{ mt: { xs: 1, sm: 0 } }}>
                          <Box sx={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: { xs: 'flex-start', sm: 'flex-end' },
                            justifyContent: 'flex-start',
                            height: '100%'
                          }}>
                            {/* Actions */}
                            <Box sx={{
                              display: 'flex',
                              gap: 1,
                              flexWrap: { xs: 'nowrap', sm: 'wrap' },
                              justifyContent: { xs: 'flex-start', sm: 'flex-end' },
                              width: '100%',
                              mb: 'auto'
                            }}>
                              <Button
                                size="small"
                                variant="contained"
                                startIcon={<i className="fas fa-eye" style={{ fontSize: '0.7rem' }}></i>}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handlePatientClick(patient.patient_id);
                                }}
                                sx={{
                                  minWidth: { xs: '40px', sm: '0' },
                                  borderRadius: 2,
                                  textTransform: 'none',
                                  fontWeight: 500,
                                  fontSize: '0.75rem',
                                  py: 0.5,
                                  px: { xs: 1.2, sm: 1.5 },
                                  bgcolor: alpha(theme.palette.primary.main, 0.9),
                                  color: '#fff',
                                  boxShadow: `0 2px 8px ${alpha(theme.palette.primary.main, 0.25)}`,
                                  '&:hover': {
                                    bgcolor: theme.palette.primary.main,
                                    boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.35)}`
                                  }
                                }}
                              >
                                View
                              </Button>
                              <Button
                                size="small"
                                variant="outlined"
                                startIcon={<i className="fas fa-plus" style={{ fontSize: '0.7rem' }}></i>}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  navigate(`/patients/${patient.patient_id}/visits/new`);
                                }}
                                sx={{
                                  minWidth: { xs: '40px', sm: '0' },
                                  borderRadius: 2,
                                  textTransform: 'none',
                                  fontWeight: 500,
                                  fontSize: '0.75rem',
                                  py: 0.5,
                                  px: { xs: 1.2, sm: 1.5 },
                                  color: theme.palette.info.main,
                                  borderColor: alpha(theme.palette.info.main, 0.5),
                                  '&:hover': {
                                    borderColor: theme.palette.info.main,
                                    bgcolor: alpha(theme.palette.info.main, 0.04),
                                    boxShadow: `0 2px 8px ${alpha(theme.palette.info.main, 0.15)}`
                                  }
                                }}
                              >
                                Add Visit
                              </Button>
                            </Box>
                          </Box>
                        </Grid>
                      </Grid>

                      {/* Visit Status - Bottom of Card */}
                      <Box sx={{
                        mt: 2,
                        pt: 1.5,
                        borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        px: 1
                      }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                          <Box sx={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: 28,
                            height: 28,
                            borderRadius: '50%',
                            bgcolor: (patient.visit_count || 0) > 0
                              ? alpha(theme.palette.success.main, 0.1)
                              : alpha(theme.palette.warning.main, 0.1),
                            border: `1px solid ${(patient.visit_count || 0) > 0
                              ? alpha(theme.palette.success.main, 0.3)
                              : alpha(theme.palette.warning.main, 0.3)}`,
                          }}>
                            <Typography variant="caption" fontWeight="700" sx={{
                              color: (patient.visit_count || 0) > 0
                                ? theme.palette.success.main
                                : theme.palette.warning.main,
                              fontSize: '0.75rem'
                            }}>
                              {patient.visit_count || 0}
                            </Typography>
                          </Box>

                          <Box>
                            <Typography variant="caption" sx={{
                              color: 'text.secondary',
                              fontSize: '0.7rem',
                              display: 'block',
                              lineHeight: 1
                            }}>
                              {(patient.visit_count || 0) > 0 ? 'Total Visits' : 'No visits recorded'}
                            </Typography>

                            {patient.last_visit_date && (
                              <Typography variant="caption" sx={{
                                color: 'text.primary',
                                fontSize: '0.75rem',
                                fontWeight: 500,
                                display: 'flex',
                                alignItems: 'center',
                                gap: 0.5
                              }}>
                                <i className="fas fa-calendar-check" style={{
                                  fontSize: '0.7rem',
                                  color: theme.palette.info.main
                                }}></i>
                                Last: {new Date(patient.last_visit_date).toLocaleDateString()}
                              </Typography>
                            )}
                          </Box>
                        </Box>

                        <Chip
                          size="small"
                          icon={(patient.visit_count || 0) > 0
                            ? <i className="fas fa-user-check" style={{ fontSize: '0.7rem' }}></i>
                            : <i className="fas fa-user-plus" style={{ fontSize: '0.7rem' }}></i>
                          }
                          label={(patient.visit_count || 0) > 0 ? "Returning" : "New Patient"}
                          sx={{
                            height: 24,
                            borderRadius: 2,
                            bgcolor: (patient.visit_count || 0) > 0
                              ? alpha(theme.palette.success.main, 0.1)
                              : alpha(theme.palette.warning.main, 0.1),
                            color: (patient.visit_count || 0) > 0
                              ? theme.palette.success.main
                              : theme.palette.warning.main,
                            border: `1px solid ${(patient.visit_count || 0) > 0
                              ? alpha(theme.palette.success.main, 0.3)
                              : alpha(theme.palette.warning.main, 0.3)}`,
                            '& .MuiChip-label': {
                              px: 0.8,
                              fontWeight: 500,
                              fontSize: '0.75rem'
                            },
                            '& .MuiChip-icon': {
                              ml: 0.5,
                              color: (patient.visit_count || 0) > 0
                                ? theme.palette.success.main
                                : theme.palette.warning.main
                            }
                          }}
                        />
                      </Box>
                    </Paper>
                  ))}
                </Box>
              ) : (
                <Box sx={{
                  p: 5,
                  textAlign: 'center',
                  background: theme.palette.background.paper,
                }}>
                  <Box sx={{
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    bgcolor: alpha(theme.palette.primary.main, 0.05),
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    margin: '0 auto',
                    mb: 3,
                    border: `2px dashed ${alpha(theme.palette.primary.main, 0.2)}`
                  }}>
                    <PersonIcon sx={{ fontSize: 36, color: alpha(theme.palette.primary.main, 0.6) }} />
                  </Box>
                  <Typography variant="h6" color="text.primary" gutterBottom fontWeight="500">
                    No Patients Found
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 4, maxWidth: '80%', mx: 'auto' }}>
                    You don't have any patients assigned to you yet. Add a new patient to get started with patient management.
                  </Typography>
                  <Button
                    variant="contained"
                    color="primary"
                    startIcon={<i className="fas fa-plus"></i>}
                    onClick={handleAddPatient}
                    sx={{
                      borderRadius: 2,
                      textTransform: 'none',
                      fontWeight: 500,
                      px: 3,
                      py: 1,
                      boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
                    }}
                  >
                    Add New Patient
                  </Button>
                </Box>
              )}

              {recentPatients.length > 0 && (
                <Box sx={{
                  p: 2,
                  borderTop: `1px solid ${alpha(theme.palette.divider, 0.08)}`,
                  textAlign: 'center',
                  background: theme.palette.background.paper,
                }}>
                  <Button
                    variant="contained"
                    size="small"
                    endIcon={<i className="fas fa-arrow-right"></i>}
                    color="primary"
                    sx={{
                      borderRadius: 2,
                      textTransform: 'none',
                      fontWeight: 500,
                      px: 2,
                      py: 0.75,
                      boxShadow: `0 2px 8px ${alpha(theme.palette.primary.main, 0.25)}`,
                      '&:hover': {
                        boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.35)}`,
                      }
                    }}
                    onClick={() => navigate('/patients')}
                  >
                    View All Patients
                  </Button>
                </Box>
              )}
            </CardContent>
          </DashboardCard>

          {/* Patient Demographics */}
          <DashboardCard
            sx={{
              overflow: 'hidden',
              borderRadius: 3,
              boxShadow: '0 10px 30px rgba(0,0,0,0.05)',
              transition: 'transform 0.3s ease, box-shadow 0.3s ease',
              '&:hover': {
                boxShadow: '0 12px 40px rgba(0,0,0,0.08)',
              }
            }}
          >
            <CardContent sx={{ p: 0 }}>
              <Box sx={{
                p: 3,
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                borderBottom: `1px solid ${alpha(theme.palette.divider, 0.08)}`,
                background: theme.palette.background.paper,
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <Avatar sx={{
                    bgcolor: 'transparent',
                    color: theme.palette.info.main,
                    width: 48,
                    height: 48,
                    border: `2px solid ${alpha(theme.palette.info.main, 0.2)}`,
                  }}>
                    <i className="fas fa-chart-pie" style={{ fontSize: '1.2rem' }}></i>
                  </Avatar>
                  <Box>
                    <Typography variant="h6" fontWeight="600" sx={{ lineHeight: 1.2, letterSpacing: '-0.01em' }}>
                      Age Distribution by Gender
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                      Demographic breakdown of your patient population
                    </Typography>
                  </Box>
                </Box>
                <Tooltip title="View detailed patient demographics">
                  <Button
                    variant="text"
                    color="info"
                    size="small"
                    endIcon={<i className="fas fa-external-link-alt" style={{ fontSize: '0.7rem' }}></i>}
                    sx={{
                      textTransform: 'none',
                      fontWeight: 500,
                      '&:hover': {
                        bgcolor: alpha(theme.palette.info.main, 0.04)
                      }
                    }}
                    onClick={() => navigate('/analytics')}
                  >
                    Full Report
                  </Button>
                </Tooltip>
              </Box>

              <Box sx={{ p: 3 }}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 3,
                    borderRadius: 2,
                    border: `1px solid ${alpha(theme.palette.divider, 0.08)}`,
                    bgcolor: alpha(theme.palette.background.paper, 0.5)
                  }}
                >
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Box sx={{ height: 240, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                        <Box sx={{ width: '100%', height: '100%', position: 'relative' }}>
                          <Doughnut
                            data={generatePatientDemographicsData()}
                            options={{
                              responsive: true,
                              maintainAspectRatio: true,
                              plugins: {
                                legend: {
                                  display: false
                                },
                                tooltip: {
                                  backgroundColor: alpha(theme.palette.common.black, 0.75),
                                  titleFont: {
                                    size: 14,
                                    weight: 'bold',
                                    family: "'Inter', sans-serif"
                                  },
                                  bodyFont: {
                                    size: 13,
                                    family: "'Inter', sans-serif"
                                  },
                                  padding: 12,
                                  cornerRadius: 8,
                                  boxPadding: 6,
                                  usePointStyle: true
                                }
                              },
                              cutout: '75%'
                            }}
                          />
                          <Box sx={{
                            position: 'absolute',
                            top: '50%',
                            left: '50%',
                            transform: 'translate(-50%, -50%)',
                            textAlign: 'center'
                          }}>
                            <Typography variant="h4" fontWeight="700" color="text.primary" sx={{ mb: 0 }}>
                              {stats.assignedPatients}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Total Patients
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
                        <Typography variant="subtitle2" color="text.primary" gutterBottom sx={{ fontWeight: 600, mb: 3, letterSpacing: '0.02em' }}>
                          Gender Distribution
                        </Typography>

                        <Box sx={{ mb: 3 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Box sx={{
                                width: 10,
                                height: 10,
                                borderRadius: '2px',
                                bgcolor: theme.palette.primary.main,
                                mr: 1.5
                              }} />
                              <Typography variant="body2" fontWeight="500">
                                Male
                              </Typography>
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="body2" fontWeight="600">
                                {Math.round(stats.assignedPatients * 0.58)}
                              </Typography>
                              <Chip
                                label="58%"
                                size="small"
                                sx={{
                                  height: 20,
                                  fontSize: '0.7rem',
                                  fontWeight: 600,
                                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                                  color: theme.palette.primary.main,
                                  border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                                }}
                              />
                            </Box>
                          </Box>
                          <LinearProgress
                            variant="determinate"
                            value={58}
                            sx={{
                              height: 8,
                              borderRadius: 4,
                              bgcolor: alpha(theme.palette.primary.main, 0.1),
                              '& .MuiLinearProgress-bar': {
                                bgcolor: theme.palette.primary.main,
                                borderRadius: 4
                              }
                            }}
                          />
                        </Box>

                        <Box sx={{ mb: 3 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <Box sx={{
                                width: 10,
                                height: 10,
                                borderRadius: '2px',
                                bgcolor: theme.palette.secondary.main,
                                mr: 1.5
                              }} />
                              <Typography variant="body2" fontWeight="500">
                                Female
                              </Typography>
                            </Box>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Typography variant="body2" fontWeight="600">
                                {Math.round(stats.assignedPatients * 0.42)}
                              </Typography>
                              <Chip
                                label="42%"
                                size="small"
                                sx={{
                                  height: 20,
                                  fontSize: '0.7rem',
                                  fontWeight: 600,
                                  bgcolor: alpha(theme.palette.secondary.main, 0.1),
                                  color: theme.palette.secondary.main,
                                  border: `1px solid ${alpha(theme.palette.secondary.main, 0.2)}`,
                                }}
                              />
                            </Box>
                          </Box>
                          <LinearProgress
                            variant="determinate"
                            value={42}
                            sx={{
                              height: 8,
                              borderRadius: 4,
                              bgcolor: alpha(theme.palette.secondary.main, 0.1),
                              '& .MuiLinearProgress-bar': {
                                bgcolor: theme.palette.secondary.main,
                                borderRadius: 4
                              }
                            }}
                          />
                        </Box>

                        <Box sx={{ mt: 2 }}>
                          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                            Age distribution shows a balanced patient population with a slight predominance of male patients.
                          </Typography>
                          <Button
                            variant="outlined"
                            size="small"
                            color="info"
                            startIcon={<i className="fas fa-chart-bar"></i>}
                            sx={{
                              borderRadius: 2,
                              textTransform: 'none',
                              fontWeight: 500,
                              borderColor: alpha(theme.palette.info.main, 0.3),
                              '&:hover': {
                                borderColor: theme.palette.info.main,
                                bgcolor: alpha(theme.palette.info.main, 0.04)
                              }
                            }}
                            onClick={() => navigate('/analytics')}
                          >
                            View Age Distribution
                          </Button>
                        </Box>
                      </Box>
                    </Grid>
                  </Grid>
                </Paper>
              </Box>
            </CardContent>
          </DashboardCard>
        </Grid>
      </Grid>


    </Box>
  );
};

export default DoctorDashboard;