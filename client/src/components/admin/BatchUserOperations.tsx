import React, { useState } from 'react';
import {
  <PERSON>,
  Button,
  Dialog,
  <PERSON>alogA<PERSON>,
  <PERSON>alogContent,
  DialogTitle,
  Typography,
  // Divider - Unused import
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  CircularProgress,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  // Tooltip - Unused import
  // IconButton - Unused import
  // Grid - Unused import
  Paper,
  Avatar
} from '@mui/material';
import {
  Delete as DeleteIcon,
  Lock as LockIcon,
  LockOpen as LockOpenIcon,
  PersonAdd as PersonAddIcon,
  Group as GroupIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  // Warning as WarningIcon - Unused import
  Info as InfoIcon
} from '@mui/icons-material';
import { API_URL } from '../../config';

interface User {
  user_id: number;
  username: string;
  email: string;
  role: string;
  is_locked: boolean;
  created_at: string;
  last_login: string | null;
}

interface BatchUserOperationsProps {
  selectedUsers: User[];
  onClose: () => void;
  onOperationComplete: () => void;
  open: boolean;
}

type BatchOperation = 'lock' | 'unlock' | 'delete' | 'change-role';

const BatchUserOperations: React.FC<BatchUserOperationsProps> = ({
  selectedUsers,
  onClose,
  onOperationComplete,
  open
}) => {
  const [operation, setOperation] = useState<BatchOperation>('lock');
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<{ success: User[]; failed: User[] } | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [newRole, setNewRole] = useState<string>('staff');

  const handleOperationChange = (event: SelectChangeEvent) => {
    setOperation(event.target.value as BatchOperation);
    setResults(null);
    setError(null);
  };

  const handleRoleChange = (event: SelectChangeEvent) => {
    setNewRole(event.target.value);
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  const getOperationTitle = () => {
    switch (operation) {
      case 'lock':
        return 'Lock User Accounts';
      case 'unlock':
        return 'Unlock User Accounts';
      case 'delete':
        return 'Delete Users';
      case 'change-role':
        return 'Change User Roles';
      default:
        return 'Batch Operation';
    }
  };

  const getOperationIcon = () => {
    switch (operation) {
      case 'lock':
        return <LockIcon />;
      case 'unlock':
        return <LockOpenIcon />;
      case 'delete':
        return <DeleteIcon />;
      case 'change-role':
        return <PersonAddIcon />;
      default:
        return <GroupIcon />;
    }
  };

  const getOperationDescription = () => {
    switch (operation) {
      case 'lock':
        return 'This will prevent the selected users from logging into the system until their accounts are unlocked.';
      case 'unlock':
        return 'This will allow the selected users to log into the system again.';
      case 'delete':
        return 'This will permanently delete the selected users from the system. This action cannot be undone.';
      case 'change-role':
        return 'This will change the role of all selected users to the specified role.';
      default:
        return '';
    }
  };

  const getOperationButtonText = () => {
    switch (operation) {
      case 'lock':
        return 'Lock Accounts';
      case 'unlock':
        return 'Unlock Accounts';
      case 'delete':
        return 'Delete Users';
      case 'change-role':
        return 'Change Roles';
      default:
        return 'Apply';
    }
  };

  const getOperationButtonColor = () => {
    switch (operation) {
      case 'lock':
        return 'warning';
      case 'unlock':
        return 'primary';
      case 'delete':
        return 'error';
      case 'change-role':
        return 'secondary';
      default:
        return 'primary';
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'error';
      case 'doctor':
        return 'warning';
      case 'staff':
        return 'info';
      case 'patient':
        return 'success';
      case 'kin':
        return 'secondary';
      case 'researcher':
        return 'primary';
      default:
        return 'default';
    }
  };

  const executeBatchOperation = async () => {
    try {
      setLoading(true);
      setError(null);
      setResults(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const userIds = selectedUsers.map(user => user.user_id);
      let endpoint = '';
      let method = 'POST';
      let body: any = { user_ids: userIds };

      switch (operation) {
        case 'lock':
          endpoint = '/api/admin/users/batch/lock';
          break;
        case 'unlock':
          endpoint = '/api/admin/users/batch/unlock';
          break;
        case 'delete':
          endpoint = '/api/admin/users/batch/delete';
          break;
        case 'change-role':
          endpoint = '/api/admin/users/batch/change-role';
          body.new_role = newRole;
          break;
        default:
          throw new Error('Invalid operation');
      }

      const response = await fetch(`${API_URL}${endpoint}`, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: JSON.stringify(body)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.msg || 'Operation failed');
      }

      const data = await response.json();
      setResults({
        success: data.success || [],
        failed: data.failed || []
      });

      // If all operations were successful, call the onOperationComplete callback
      if (data.failed?.length === 0) {
        setTimeout(() => {
          onOperationComplete();
          onClose();
        }, 1500);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error executing batch operation:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{ sx: { borderRadius: 2 } }}
    >
      <DialogTitle sx={{ pb: 1, borderBottom: '1px solid #eee', display: 'flex', alignItems: 'center', gap: 1 }}>
        <Avatar sx={{ bgcolor: getOperationButtonColor() + '.main' }}>
          {getOperationIcon()}
        </Avatar>
        <Typography variant="h6">{getOperationTitle()}</Typography>
      </DialogTitle>
      <DialogContent sx={{ pt: 3, pb: 2, px: 3 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {results ? (
          <Box>
            <Typography variant="subtitle1" fontWeight="500" sx={{ mb: 2 }}>
              Operation Results
            </Typography>

            {results.success.length > 0 && (
              <Paper elevation={0} sx={{ p: 2, mb: 2, bgcolor: 'success.light', color: 'success.contrastText', borderRadius: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <CheckIcon />
                  <Typography variant="subtitle2">
                    Successfully processed {results.success.length} user{results.success.length !== 1 ? 's' : ''}
                  </Typography>
                </Box>
                <List dense sx={{ maxHeight: 200, overflow: 'auto' }}>
                  {results.success.map(user => (
                    <ListItem key={user.user_id}>
                      <ListItemText
                        primary={user.username}
                        secondary={user.email}
                        primaryTypographyProps={{ fontWeight: 500 }}
                      />
                    </ListItem>
                  ))}
                </List>
              </Paper>
            )}

            {results.failed.length > 0 && (
              <Paper elevation={0} sx={{ p: 2, bgcolor: 'error.light', color: 'error.contrastText', borderRadius: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <CloseIcon />
                  <Typography variant="subtitle2">
                    Failed to process {results.failed.length} user{results.failed.length !== 1 ? 's' : ''}
                  </Typography>
                </Box>
                <List dense sx={{ maxHeight: 200, overflow: 'auto' }}>
                  {results.failed.map(user => (
                    <ListItem key={user.user_id}>
                      <ListItemText
                        primary={user.username}
                        secondary={user.email}
                        primaryTypographyProps={{ fontWeight: 500 }}
                      />
                    </ListItem>
                  ))}
                </List>
              </Paper>
            )}
          </Box>
        ) : (
          <Box>
            <Box sx={{ mb: 3 }}>
              <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
                <InputLabel id="batch-operation-label">Operation</InputLabel>
                <Select
                  labelId="batch-operation-label"
                  value={operation}
                  onChange={handleOperationChange}
                  label="Operation"
                  disabled={loading}
                >
                  <MenuItem value="lock">Lock Accounts</MenuItem>
                  <MenuItem value="unlock">Unlock Accounts</MenuItem>
                  <MenuItem value="change-role">Change Role</MenuItem>
                  <MenuItem value="delete">Delete Users</MenuItem>
                </Select>
              </FormControl>

              {operation === 'change-role' && (
                <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
                  <InputLabel id="new-role-label">New Role</InputLabel>
                  <Select
                    labelId="new-role-label"
                    value={newRole}
                    onChange={handleRoleChange}
                    label="New Role"
                    disabled={loading}
                  >
                    <MenuItem value="admin">Admin</MenuItem>
                    <MenuItem value="doctor">Doctor</MenuItem>
                    <MenuItem value="staff">Staff</MenuItem>
                    <MenuItem value="patient">Patient</MenuItem>
                    <MenuItem value="kin">Kin</MenuItem>
                    <MenuItem value="researcher">Researcher</MenuItem>
                  </Select>
                </FormControl>
              )}

              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {getOperationDescription()}
              </Typography>

              <Paper elevation={0} sx={{ p: 2, bgcolor: 'info.light', color: 'info.contrastText', borderRadius: 2, mb: 3 }}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                  <InfoIcon sx={{ mt: 0.5 }} />
                  <Typography variant="body2">
                    You are about to perform this operation on <strong>{selectedUsers.length}</strong> user{selectedUsers.length !== 1 ? 's' : ''}.
                    Please review the list below before proceeding.
                  </Typography>
                </Box>
              </Paper>
            </Box>

            <Typography variant="subtitle1" fontWeight="500" sx={{ mb: 1 }}>
              Selected Users
            </Typography>
            <Paper variant="outlined" sx={{ maxHeight: 300, overflow: 'auto', borderRadius: 2 }}>
              <List dense>
                {selectedUsers.map(user => (
                  <ListItem key={user.user_id} divider>
                    <ListItemIcon>
                      <Avatar sx={{ width: 28, height: 28, bgcolor: getRoleColor(user.role) + '.main', fontSize: '0.75rem' }}>
                        {user.username.charAt(0).toUpperCase()}
                      </Avatar>
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="body2" fontWeight="500">
                            {user.username}
                          </Typography>
                          <Chip
                            label={user.role.toUpperCase()}
                            color={getRoleColor(user.role)}
                            size="small"
                            sx={{ height: 20, fontSize: '0.7rem' }}
                          />
                          {user.is_locked && (
                            <Chip
                              label="LOCKED"
                              color="error"
                              size="small"
                              sx={{ height: 20, fontSize: '0.7rem' }}
                            />
                          )}
                        </Box>
                      }
                      secondary={user.email}
                    />
                  </ListItem>
                ))}
              </List>
            </Paper>
          </Box>
        )}
      </DialogContent>
      <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid #eee' }}>
        <Button onClick={handleClose} variant="outlined" disabled={loading}>
          {results ? 'Close' : 'Cancel'}
        </Button>
        {!results && (
          <Button
            onClick={executeBatchOperation}
            color={getOperationButtonColor()}
            variant="contained"
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : getOperationIcon()}
          >
            {loading ? 'Processing...' : getOperationButtonText()}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default BatchUserOperations;
