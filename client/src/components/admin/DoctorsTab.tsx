import React from 'react';

interface Doctor {
  doctor_id: number;
  user_id: number;
  first_name: string;
  last_name: string;
  specialty: string | null;
  phone: string | null;
  email: string;
  username: string;
}

interface DoctorsTabProps {
  doctors: Doctor[];
  onEditDoctor: (doctor: Doctor) => void;
  onDeleteDoctor: (doctorId: number, userId: number) => void;
  onRefreshData: () => void;
}

const DoctorsTab: React.FC<DoctorsTabProps> = ({ 
  doctors, 
  onEditDoctor, 
  onDeleteDoctor, 
  onRefreshData 
}) => {
  console.log('DoctorsTab rendered with', doctors?.length || 0, 'doctors');
  
  return (
    <div>
      <h3>Doctors Management</h3>
      
      {/* Debug Info */}
      <div style={{ backgroundColor: '#f8f9fa', padding: '10px', marginBottom: '15px', borderRadius: '5px' }}>
        <p><strong>Debug:</strong> DoctorsTab component rendered</p>
        <p><strong>Data:</strong> {doctors ? `${doctors.length} doctors found` : 'No doctors data'}</p>
      </div>
      
      {doctors && doctors.length > 0 ? (
        <div>
          <p>Found {doctors.length} doctors</p>
          <div className="table-responsive">
            <table className="table table-striped">
              <thead className="bg-primary text-white">
                <tr>
                  <th>Name</th>
                  <th>Username</th>
                  <th>Specialty</th>
                  <th>Phone</th>
                  <th>Email</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {doctors.map(doctor => (
                  <tr key={doctor.doctor_id}>
                    <td>
                      {doctor.first_name} {doctor.last_name}
                    </td>
                    <td>{doctor.username}</td>
                    <td>{doctor.specialty}</td>
                    <td>{doctor.phone}</td>
                    <td>{doctor.email}</td>
                    <td>
                      <button
                        className="btn btn-sm btn-primary mr-2"
                        onClick={() => onEditDoctor(doctor)}
                      >
                        <i className="fas fa-edit"></i> Edit
                      </button>
                      <button
                        className="btn btn-sm btn-danger"
                        onClick={() => onDeleteDoctor(doctor.doctor_id, doctor.user_id)}
                      >
                        <i className="fas fa-trash"></i> Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          <div style={{ margin: '20px 0', padding: '15px', border: '1px solid #ccc' }}>
            <h4>Debug Information</h4>
            <pre style={{ maxHeight: '200px', overflow: 'auto' }}>
              {JSON.stringify(doctors, null, 2)}
            </pre>
          </div>
        </div>
      ) : (
        <div className="alert alert-info">
          <p>No doctors found or data is still loading.</p>
          <p>Doctors state: {doctors ? `Array with ${doctors.length} items` : 'undefined'}</p>
          <button className="btn btn-primary" onClick={onRefreshData}>
            <i className="fas fa-sync-alt"></i> Refresh Data
          </button>
        </div>
      )}
    </div>
  );
};

export default DoctorsTab; 