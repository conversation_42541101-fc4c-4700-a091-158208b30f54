import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  CircularProgress,
  Alert,
  Chip,
  TextField,
  InputAdornment,
  Tooltip,
  Grid,
  Avatar,
  Menu,
  MenuItem,
  Divider,
  Tabs,
  Tab,
  Checkbox,
  Toolbar,
  ListItemIcon
} from '@mui/material';
import {
  Lock as LockIcon,
  LockOpen as LockOpenIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Info as InfoIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon,
  Sort as SortIcon,
  PersonAdd as PersonAddIcon,
  People as PeopleIcon,
  Edit as EditIcon,
  Key as KeyIcon,
  FileUpload as UploadIcon,
  MoreVert as MoreIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { API_URL } from '../../config';
import UserCreateForm from './UserCreateForm';
import EditUserDialog from './EditUserDialog';
import DeleteUserDialog from './DeleteUserDialog';
import ResetPasswordDialog from './ResetPasswordDialog';
import BatchUserOperations from './BatchUserOperations';
import UserImportExport from './UserImportExport';
import UserActivityDashboard from './UserActivityDashboard';

interface User {
  user_id: number;
  username: string;
  email: string;
  role: string;
  is_locked: boolean;
  created_at: string;
  last_login: string | null;
}

// Add these type definitions
type SortField = 'username' | 'role' | 'is_locked' | 'created_at' | 'last_login';
type SortDirection = 'asc' | 'desc';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`user-tabpanel-${index}`}
      aria-labelledby={`user-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const UserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [lockDialogOpen, setLockDialogOpen] = useState(false);
  const [unlockDialogOpen, setUnlockDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [resetPasswordDialogOpen, setResetPasswordDialogOpen] = useState(false);
  const [actionLoading, setActionLoading] = useState(false);
  const [sortField, setSortField] = useState<SortField>('username');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [tabValue, setTabValue] = useState(0);

  // Batch operations
  const [selectedUserIds, setSelectedUserIds] = useState<number[]>([]);
  const [batchOperationsOpen, setBatchOperationsOpen] = useState(false);

  // Import/Export
  const [importExportOpen, setImportExportOpen] = useState(false);

  // User Activity Dashboard
  const [activityDashboardOpen, setActivityDashboardOpen] = useState(false);

  // Actions menu
  const [actionsMenuAnchorEl, setActionsMenuAnchorEl] = useState<null | HTMLElement>(null);

  // Fetch users
  const fetchUsers = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(`${API_URL}/api/users`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }

      const userData = await response.json();
      setUsers(userData);
      setFilteredUsers(userData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch users');
      console.error('Error fetching users:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, []);

  // Filter users based on search term
  useEffect(() => {
    if (!searchTerm.trim()) {
      setFilteredUsers(users);
      return;
    }

    const lowercasedTerm = searchTerm.toLowerCase();
    const filtered = users.filter(user => {
      return (
        user.username.toLowerCase().includes(lowercasedTerm) ||
        user.email.toLowerCase().includes(lowercasedTerm) ||
        user.role.toLowerCase().includes(lowercasedTerm)
      );
    });

    setFilteredUsers(filtered);
  }, [searchTerm, users]);

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const openLockDialog = (user: User) => {
    setSelectedUser(user);
    setLockDialogOpen(true);
  };

  const openUnlockDialog = (user: User) => {
    setSelectedUser(user);
    setUnlockDialogOpen(true);
  };

  const closeLockDialog = () => {
    setLockDialogOpen(false);
    setSelectedUser(null);
  };

  const closeUnlockDialog = () => {
    setUnlockDialogOpen(false);
    setSelectedUser(null);
  };

  const openEditDialog = (user: User) => {
    setSelectedUser(user);
    setEditDialogOpen(true);
  };

  const closeEditDialog = () => {
    setEditDialogOpen(false);
    setSelectedUser(null);
  };

  const openDeleteDialog = (user: User) => {
    setSelectedUser(user);
    setDeleteDialogOpen(true);
  };

  const closeDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setSelectedUser(null);
  };

  const openResetPasswordDialog = (user: User) => {
    setSelectedUser(user);
    setResetPasswordDialogOpen(true);
  };

  const closeResetPasswordDialog = () => {
    setResetPasswordDialogOpen(false);
    setSelectedUser(null);
  };

  const lockUser = async () => {
    if (!selectedUser) return;

    try {
      setActionLoading(true);
      const token = localStorage.getItem('token');

      const response = await fetch(`${API_URL}/api/users/${selectedUser.user_id}/lock`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token || ''
        }
      });

      if (!response.ok) {
        throw new Error('Failed to lock user account');
      }

      // Update the user in the list
      setUsers(prevUsers =>
        prevUsers.map(user =>
          user.user_id === selectedUser.user_id ? { ...user, is_locked: true } : user
        )
      );

      closeLockDialog();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to lock user account');
      console.error('Error locking user:', err);
    } finally {
      setActionLoading(false);
    }
  };

  const unlockUser = async () => {
    if (!selectedUser) return;

    try {
      setActionLoading(true);
      const token = localStorage.getItem('token');

      const response = await fetch(`${API_URL}/api/users/${selectedUser.user_id}/unlock`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token || ''
        }
      });

      if (!response.ok) {
        throw new Error('Failed to unlock user account');
      }

      // Update the user in the list
      setUsers(prevUsers =>
        prevUsers.map(user =>
          user.user_id === selectedUser.user_id ? { ...user, is_locked: false } : user
        )
      );

      closeUnlockDialog();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to unlock user account');
      console.error('Error unlocking user:', err);
    } finally {
      setActionLoading(false);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'error';
      case 'doctor':
        return 'warning';
      case 'staff':
        return 'info';
      case 'patient':
        return 'success';
      case 'kin':
        return 'secondary';
      case 'researcher':
        return 'primary';
      default:
        return 'default';
    }
  };

  // Add this function to handle sort
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      // Toggle direction if same field
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // Set new field and default to ascending
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Add this function to get sorted users
  const getSortedUsers = (users: User[]) => {
    return [...users].sort((a, b) => {
      let valueA: any = a[sortField];
      let valueB: any = b[sortField];

      // Handle null values for last_login
      if (sortField === 'last_login') {
        valueA = valueA || ''; // Convert null to empty string for sorting
        valueB = valueB || '';
      }

      // Compare based on direction
      const compareResult = valueA < valueB ? -1 : valueA > valueB ? 1 : 0;
      return sortDirection === 'asc' ? compareResult : -compareResult;
    });
  };

  // Add functions for filter menu
  const handleOpenRoleMenu = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleCloseRoleMenu = () => {
    setAnchorEl(null);
  };

  const handleFilterByRole = (role: string | null) => {
    if (role === null) {
      // Reset to show all users
      setFilteredUsers(users);
    } else {
      // Filter by selected role
      const filtered = users.filter(user => user.role === role);
      setFilteredUsers(filtered);
    }
    handleCloseRoleMenu();
    setPage(0); // Reset to first page when filtering
  };

  // Get unique roles for filter menu
  const uniqueRoles = Array.from(new Set(users.map(user => user.role)));

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle batch selection
  const handleSelectAll = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelectedIds = filteredUsers.map(user => user.user_id);
      setSelectedUserIds(newSelectedIds);
    } else {
      setSelectedUserIds([]);
    }
  };

  const handleSelectUser = (event: React.ChangeEvent<HTMLInputElement>, userId: number) => {
    if (event.target.checked) {
      setSelectedUserIds(prev => [...prev, userId]);
    } else {
      setSelectedUserIds(prev => prev.filter(id => id !== userId));
    }
  };

  const isSelected = (userId: number) => selectedUserIds.includes(userId);

  const getSelectedUsers = () => {
    return users.filter(user => selectedUserIds.includes(user.user_id));
  };

  // Batch operations handlers
  const openBatchOperations = () => {
    setBatchOperationsOpen(true);
  };

  const closeBatchOperations = () => {
    setBatchOperationsOpen(false);
  };

  // Import/Export handlers
  const openImportExport = () => {
    setImportExportOpen(true);
  };

  const closeImportExport = () => {
    setImportExportOpen(false);
  };

  // User Activity Dashboard handlers
  const openActivityDashboard = () => {
    setActivityDashboardOpen(true);
    closeActionsMenu();
  };

  const closeActivityDashboard = () => {
    setActivityDashboardOpen(false);
  };

  // Actions menu handlers
  const openActionsMenu = (event: React.MouseEvent<HTMLElement>) => {
    setActionsMenuAnchorEl(event.currentTarget);
  };

  const closeActionsMenu = () => {
    setActionsMenuAnchorEl(null);
  };

  if (loading && tabValue === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 4,
          borderRadius: 2,
          background: 'linear-gradient(to right, #3f51b5, #5c6bc0)',
          color: 'white',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        <Box sx={{
          position: 'absolute',
          top: 0,
          right: 0,
          width: '30%',
          height: '100%',
          opacity: 0.1,
          background: `url('https://www.transparenttextures.com/patterns/cubes.png')`,
          backgroundSize: 'cover'
        }} />

        <Grid container spacing={2} alignItems="center">
          <Grid item>
            <Avatar
              sx={{
                width: 60,
                height: 60,
                bgcolor: 'white',
                color: '#3f51b5',
                boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
              }}
            >
              <PeopleIcon fontSize="large" />
            </Avatar>
          </Grid>
          <Grid item xs>
            <Typography variant="h4" fontWeight="500">
              User Management
            </Typography>
            <Typography variant="subtitle1" sx={{ opacity: 0.9, mt: 0.5 }}>
              Manage all users, roles, and permissions in the system
            </Typography>
          </Grid>
          <Grid item>
            {tabValue === 0 && (
              <Button
                variant="contained"
                startIcon={<RefreshIcon />}
                onClick={fetchUsers}
                size="medium"
                sx={{
                  bgcolor: 'rgba(255,255,255,0.9)',
                  color: '#3f51b5',
                  '&:hover': { bgcolor: 'white' }
                }}
              >
                Refresh
              </Button>
            )}
          </Grid>
        </Grid>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Tabs Section */}
      <Paper sx={{ mb: 4, borderRadius: 2, overflow: 'hidden', boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              py: 2,
              transition: 'all 0.2s ease',
              '&:hover': {
                bgcolor: 'rgba(0,0,0,0.02)'
              }
            },
            '& .Mui-selected': {
              fontWeight: 'bold'
            }
          }}
        >
          <Tab
            icon={<PeopleIcon />}
            label="Manage Users"
            id="user-tab-0"
            aria-controls="user-tabpanel-0"
            iconPosition="start"
          />
          <Tab
            icon={<PersonAddIcon />}
            label="Create User"
            id="user-tab-1"
            aria-controls="user-tabpanel-1"
            iconPosition="start"
          />
        </Tabs>
      </Paper>

      <TabPanel value={tabValue} index={0}>
        {/* Search and Stats Section */}
        <Grid container spacing={3} sx={{ mb: 4 }}>
          <Grid item md={8}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                borderRadius: 2,
                boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                height: '100%'
              }}
            >
              <Typography variant="subtitle1" fontWeight="500" sx={{ mb: 2 }}>
                Search Users
              </Typography>
              <TextField
                placeholder="Search users by name, email, or role"
                variant="outlined"
                size="small"
                value={searchTerm}
                onChange={handleSearchChange}
                fullWidth
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                sx={{
                  '& .MuiOutlinedInput-root': {
                    borderRadius: 2,
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#3f51b5'
                    }
                  }
                }}
              />
            </Paper>
          </Grid>

          <Grid item md={4}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                borderRadius: 2,
                boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <Typography variant="subtitle1" fontWeight="500">
                  User Statistics
                </Typography>
                <Chip
                  label={`${filteredUsers.length} users`}
                  color="primary"
                  size="small"
                  sx={{ fontWeight: 'bold' }}
                />
              </Box>
              <Divider sx={{ my: 1.5 }} />
              <Grid container spacing={1}>
                {uniqueRoles.map(role => {
                  const count = users.filter(user => user.role === role).length;
                  return (
                    <Grid item xs={6} key={role}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Chip
                          label={role.toUpperCase()}
                          color={getRoleColor(role)}
                          size="small"
                          sx={{ minWidth: 80 }}
                        />
                        <Typography variant="body2">{count}</Typography>
                      </Box>
                    </Grid>
                  );
                })}
              </Grid>
            </Paper>
          </Grid>
        </Grid>

        {/* User Types Guide */}
        <Paper
          elevation={0}
          sx={{
            mb: 4,
            borderRadius: 2,
            boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
            overflow: 'hidden'
          }}
        >
          <Box sx={{
            p: 2,
            bgcolor: '#f5f5f5',
            borderBottom: '1px solid #eee',
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}>
            <InfoIcon color="primary" fontSize="small" />
            <Typography variant="subtitle1" fontWeight="500">
              User Types Guide
            </Typography>
          </Box>
          <Box sx={{ p: 2 }}>
            <Grid container spacing={2}>
              <Grid item md={4}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1.5 }}>
                  <Chip label="ADMIN" color="error" size="small" sx={{ minWidth: 90 }} />
                  <Typography variant="body2">System administrators</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1.5 }}>
                  <Chip label="DOCTOR" color="warning" size="small" sx={{ minWidth: 90 }} />
                  <Typography variant="body2">Medical professionals</Typography>
                </Box>
              </Grid>
              <Grid item md={4}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1.5 }}>
                  <Chip label="STAFF" color="info" size="small" sx={{ minWidth: 90 }} />
                  <Typography variant="body2">Administrative staff</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1.5 }}>
                  <Chip label="PATIENT" color="success" size="small" sx={{ minWidth: 90 }} />
                  <Typography variant="body2">Patient users</Typography>
                </Box>
              </Grid>
              <Grid item md={4}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1.5 }}>
                  <Chip label="KIN" color="secondary" size="small" sx={{ minWidth: 90 }} />
                  <Typography variant="body2">Patient relatives</Typography>
                </Box>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1.5 }}>
                  <Chip label="RESEARCHER" color="primary" size="small" sx={{ minWidth: 90 }} />
                  <Typography variant="body2">Research access users</Typography>
                </Box>
              </Grid>
            </Grid>
          </Box>
        </Paper>

        {/* Advanced Actions Toolbar */}
        <Box sx={{ mb: 2, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
          <Button
            variant="outlined"
            startIcon={<UploadIcon />}
            onClick={openImportExport}
            size="small"
          >
            Import/Export
          </Button>

          <Button
            variant="outlined"
            color="primary"
            startIcon={<MoreIcon />}
            onClick={openActionsMenu}
            size="small"
          >
            More Actions
          </Button>
          <Menu
            anchorEl={actionsMenuAnchorEl}
            open={Boolean(actionsMenuAnchorEl)}
            onClose={closeActionsMenu}
          >
            <MenuItem onClick={openActivityDashboard}>
              <ListItemIcon>
                <InfoIcon fontSize="small" />
              </ListItemIcon>
              User Activity Dashboard
            </MenuItem>
            <Divider />
            <MenuItem onClick={openActivityDashboard}>
              <ListItemIcon>
                <PeopleIcon fontSize="small" />
              </ListItemIcon>
              Inactive Users Report
            </MenuItem>
          </Menu>
        </Box>

        {/* Users Table */}
        <Paper
          elevation={0}
          sx={{
            width: '100%',
            mb: 2,
            borderRadius: 2,
            overflow: 'hidden',
            boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
          }}>
          {/* Batch Operations Toolbar */}
          {selectedUserIds.length > 0 && (
            <Toolbar
              sx={{
                pl: { sm: 2 },
                pr: { xs: 1, sm: 1 },
                bgcolor: 'primary.light',
                color: 'primary.contrastText',
                borderBottom: '1px solid',
                borderColor: 'primary.main'
              }}
            >
              <Typography
                sx={{ flex: '1 1 100%' }}
                color="inherit"
                variant="subtitle1"
                component="div"
              >
                {selectedUserIds.length} {selectedUserIds.length === 1 ? 'user' : 'users'} selected
              </Typography>
              <Button
                variant="contained"
                color="primary"
                size="small"
                onClick={openBatchOperations}
                sx={{ mr: 1 }}
              >
                Batch Operations
              </Button>
              <Button
                variant="outlined"
                color="inherit"
                size="small"
                onClick={() => setSelectedUserIds([])}
              >
                Clear Selection
              </Button>
            </Toolbar>
          )}

          <TableContainer>
            <Table aria-label="users table">
              <TableHead>
                <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                  <TableCell padding="checkbox">
                    <Checkbox
                      color="primary"
                      indeterminate={selectedUserIds.length > 0 && selectedUserIds.length < filteredUsers.length}
                      checked={filteredUsers.length > 0 && selectedUserIds.length === filteredUsers.length}
                      onChange={handleSelectAll}
                      inputProps={{
                        'aria-label': 'select all users',
                      }}
                    />
                  </TableCell>
                  <TableCell
                    sx={{
                      cursor: 'pointer',
                      userSelect: 'none',
                      '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.04)' }
                    }}
                    onClick={() => handleSort('username')}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      Username
                      {sortField === 'username' && (
                        sortDirection === 'asc' ?
                          <ArrowUpwardIcon fontSize="small" color="primary" /> :
                          <ArrowDownwardIcon fontSize="small" color="primary" />
                      )}
                    </Box>
                  </TableCell>
                  <TableCell
                    sx={{
                      cursor: 'pointer',
                      userSelect: 'none',
                      '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.04)' }
                    }}
                    onClick={() => handleSort('role')}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        Role
                        {sortField === 'role' && (
                          sortDirection === 'asc' ?
                            <ArrowUpwardIcon fontSize="small" color="primary" /> :
                            <ArrowDownwardIcon fontSize="small" color="primary" />
                        )}
                      </Box>
                      <Tooltip title="Filter by role">
                        <IconButton size="small" onClick={handleOpenRoleMenu}>
                          <SortIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Menu
                        anchorEl={anchorEl}
                        open={Boolean(anchorEl)}
                        onClose={handleCloseRoleMenu}
                      >
                        <MenuItem onClick={() => handleFilterByRole(null)}>
                          <strong>Show All Roles</strong>
                        </MenuItem>
                        <Divider />
                        {uniqueRoles.map(role => (
                          <MenuItem key={role} onClick={() => handleFilterByRole(role)}>
                            <Chip
                              label={role.toUpperCase()}
                              color={getRoleColor(role)}
                              size="small"
                              sx={{ fontWeight: 500, mr: 1 }}
                            />
                            {role.charAt(0).toUpperCase() + role.slice(1)}
                          </MenuItem>
                        ))}
                      </Menu>
                    </Box>
                  </TableCell>
                  <TableCell
                    sx={{
                      cursor: 'pointer',
                      userSelect: 'none',
                      '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.04)' }
                    }}
                    onClick={() => handleSort('is_locked')}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      Account Status
                      {sortField === 'is_locked' && (
                        sortDirection === 'asc' ?
                          <ArrowUpwardIcon fontSize="small" color="primary" /> :
                          <ArrowDownwardIcon fontSize="small" color="primary" />
                      )}
                    </Box>
                  </TableCell>
                  <TableCell
                    sx={{
                      cursor: 'pointer',
                      userSelect: 'none',
                      '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.04)' }
                    }}
                    onClick={() => handleSort('created_at')}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      Created
                      {sortField === 'created_at' && (
                        sortDirection === 'asc' ?
                          <ArrowUpwardIcon fontSize="small" color="primary" /> :
                          <ArrowDownwardIcon fontSize="small" color="primary" />
                      )}
                    </Box>
                  </TableCell>
                  <TableCell
                    sx={{
                      cursor: 'pointer',
                      userSelect: 'none',
                      '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.04)' }
                    }}
                    onClick={() => handleSort('last_login')}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      Last Login
                      {sortField === 'last_login' && (
                        sortDirection === 'asc' ?
                          <ArrowUpwardIcon fontSize="small" color="primary" /> :
                          <ArrowDownwardIcon fontSize="small" color="primary" />
                      )}
                    </Box>
                  </TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredUsers.length > 0 ? (
                  getSortedUsers(filteredUsers)
                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                    .map((user) => {
                      const isItemSelected = isSelected(user.user_id);

                      return (
                        <TableRow
                          key={user.user_id}
                          sx={{
                            '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.03)' },
                            ...(isItemSelected ? { bgcolor: 'rgba(25, 118, 210, 0.08)' } : {})
                          }}
                          selected={isItemSelected}
                        >
                          <TableCell padding="checkbox">
                            <Checkbox
                              color="primary"
                              checked={isItemSelected}
                              onChange={(event) => handleSelectUser(event, user.user_id)}
                              inputProps={{
                                'aria-labelledby': `user-${user.user_id}`,
                              }}
                            />
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Avatar
                                sx={{
                                  width: 30,
                                  height: 30,
                                  bgcolor: getRoleColor(user.role) + '.main',
                                  fontSize: '0.875rem'
                                }}
                              >
                                {user.username.charAt(0).toUpperCase()}
                              </Avatar>
                              <Box>
                                <Typography variant="body2" sx={{ fontWeight: 500 }}>
                                  {user.username}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {user.email}
                                </Typography>
                              </Box>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={user.role.toUpperCase()}
                              color={getRoleColor(user.role)}
                              size="small"
                              sx={{ fontWeight: 500 }}
                            />
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={user.is_locked ? 'LOCKED' : 'ACTIVE'}
                              color={user.is_locked ? 'error' : 'success'}
                              size="small"
                              variant={user.is_locked ? 'filled' : 'outlined'}
                            />
                          </TableCell>
                          <TableCell>{formatDate(user.created_at)}</TableCell>
                          <TableCell>{formatDate(user.last_login)}</TableCell>
                          <TableCell align="center">
                            <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1 }}>
                              {user.is_locked ? (
                                <Tooltip title="Unlock account">
                                  <IconButton
                                    size="small"
                                    sx={{ color: 'primary.main' }}
                                    onClick={() => openUnlockDialog(user)}
                                  >
                                    <LockOpenIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              ) : (
                                <Tooltip title="Lock account">
                                  <IconButton
                                    size="small"
                                    sx={{ color: 'warning.main' }}
                                    onClick={() => openLockDialog(user)}
                                  >
                                    <LockIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              )}
                              <Tooltip title="Reset password">
                                <IconButton
                                  size="small"
                                  sx={{ color: 'secondary.main' }}
                                  onClick={() => openResetPasswordDialog(user)}
                                >
                                  <KeyIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Edit user">
                                <IconButton
                                  size="small"
                                  sx={{ color: 'info.main' }}
                                  onClick={() => openEditDialog(user)}
                                >
                                  <EditIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                              <Tooltip title="Delete user">
                                <IconButton
                                  size="small"
                                  sx={{ color: 'error.main' }}
                                  onClick={() => openDeleteDialog(user)}
                                >
                                  <DeleteIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            </Box>
                          </TableCell>
                        </TableRow>
                      );
                    })
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} align="center">
                      {searchTerm ? 'No users match your search' : 'No users found'}
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>
          <TablePagination
            rowsPerPageOptions={[5, 10, 25, 50]}
            component="div"
            count={filteredUsers.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            sx={{
              borderTop: '1px solid #eee',
              '.MuiTablePagination-selectLabel, .MuiTablePagination-displayedRows': {
                fontWeight: 500,
              },
              '.MuiTablePagination-select': {
                borderRadius: 1,
              },
              '.MuiTablePagination-actions': {
                '& .MuiIconButton-root': {
                  padding: '4px',
                  borderRadius: 1,
                  backgroundColor: 'rgba(0, 0, 0, 0.04)',
                  '&:hover': {
                    backgroundColor: 'rgba(0, 0, 0, 0.08)',
                  },
                  '&.Mui-disabled': {
                    opacity: 0.4,
                  },
                  mx: 0.5
                }
              }
            }}
          />
        </Paper>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <UserCreateForm onUserCreated={() => {
          fetchUsers();
          setTabValue(0); // Switch back to user list tab after creating a user
        }} />
      </TabPanel>

      {/* Lock User Dialog */}
      <Dialog
        open={lockDialogOpen}
        onClose={closeLockDialog}
        maxWidth="sm"
        fullWidth
        PaperProps={{ sx: { borderRadius: 2 } }}
      >
        <DialogTitle sx={{ pb: 1, borderBottom: '1px solid #eee' }}>
          <Typography variant="h6">Lock User Account</Typography>
        </DialogTitle>
        <DialogContent sx={{ pt: 3, pb: 2, px: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <Avatar sx={{ bgcolor: 'warning.main' }}>
              <LockIcon />
            </Avatar>
            <Typography variant="body1" sx={{ fontWeight: 500 }}>
              Lock account for {selectedUser?.username}
            </Typography>
          </Box>
          <Typography color="text.secondary">
            This will prevent the user from logging into the system until the account is unlocked.
            The user will be notified about their account status on their next login attempt.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid #eee' }}>
          <Button onClick={closeLockDialog} variant="outlined" disabled={actionLoading}>Cancel</Button>
          <Button
            onClick={lockUser}
            color="warning"
            variant="contained"
            disabled={actionLoading}
            startIcon={actionLoading ? <CircularProgress size={20} /> : <LockIcon />}
          >
            Lock Account
          </Button>
        </DialogActions>
      </Dialog>

      {/* Unlock User Dialog */}
      <Dialog
        open={unlockDialogOpen}
        onClose={closeUnlockDialog}
        maxWidth="sm"
        fullWidth
        PaperProps={{ sx: { borderRadius: 2 } }}
      >
        <DialogTitle sx={{ pb: 1, borderBottom: '1px solid #eee' }}>
          <Typography variant="h6">Unlock User Account</Typography>
        </DialogTitle>
        <DialogContent sx={{ pt: 3, pb: 2, px: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <Avatar sx={{ bgcolor: 'primary.main' }}>
              <LockOpenIcon />
            </Avatar>
            <Typography variant="body1" sx={{ fontWeight: 500 }}>
              Unlock account for {selectedUser?.username}
            </Typography>
          </Box>
          <Typography color="text.secondary">
            This will allow the user to log into the system again.
            Consider contacting the user to inform them that their account has been unlocked.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid #eee' }}>
          <Button onClick={closeUnlockDialog} variant="outlined" disabled={actionLoading}>Cancel</Button>
          <Button
            onClick={unlockUser}
            color="primary"
            variant="contained"
            disabled={actionLoading}
            startIcon={actionLoading ? <CircularProgress size={20} /> : <LockOpenIcon />}
          >
            Unlock Account
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit User Dialog */}
      <EditUserDialog
        open={editDialogOpen}
        onClose={closeEditDialog}
        user={selectedUser}
        onUserUpdated={fetchUsers}
      />

      {/* Delete User Dialog */}
      <DeleteUserDialog
        open={deleteDialogOpen}
        onClose={closeDeleteDialog}
        user={selectedUser}
        onUserDeleted={fetchUsers}
      />

      {/* Reset Password Dialog */}
      <ResetPasswordDialog
        open={resetPasswordDialogOpen}
        onClose={closeResetPasswordDialog}
        user={selectedUser}
        onPasswordReset={fetchUsers}
      />

      {/* Batch Operations Dialog */}
      <BatchUserOperations
        open={batchOperationsOpen}
        onClose={closeBatchOperations}
        selectedUsers={getSelectedUsers()}
        onOperationComplete={() => {
          fetchUsers();
          setSelectedUserIds([]);
        }}
      />

      {/* Import/Export Dialog */}
      <UserImportExport
        open={importExportOpen}
        onClose={closeImportExport}
        onOperationComplete={fetchUsers}
      />

      {/* User Activity Dashboard Dialog */}
      <Dialog
        open={activityDashboardOpen}
        onClose={closeActivityDashboard}
        maxWidth="xl"
        fullWidth
        PaperProps={{ sx: { borderRadius: 2 } }}
      >
        <DialogTitle sx={{ pb: 1, borderBottom: '1px solid #eee' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6">User Activity Dashboard</Typography>
            <IconButton onClick={closeActivityDashboard} size="small">
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ p: 0 }}>
          <UserActivityDashboard />
        </DialogContent>
      </Dialog>
    </Box>
  );
};

export default UserManagement;