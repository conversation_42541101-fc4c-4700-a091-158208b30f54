import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  LinearProgress,
  Grid,
  Divider,
  Chip,
  useTheme,
  alpha
} from '@mui/material';
import {
  Memory as MemoryIcon,
  Storage as StorageIcon,
  Speed as SpeedIcon,
  CloudQueue as CloudIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import DashboardWidget, { DashboardWidgetProps } from '../DashboardWidget';
import { API_URL } from '../../../../config';

interface ServerMetrics {
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  uptime: number;
  activeConnections: number;
  responseTime: number;
  status: 'healthy' | 'warning' | 'critical';
  lastUpdated: string;
}

interface ServerStatusWidgetProps extends Omit<DashboardWidgetProps, 'children'> {
  updateInterval?: number; // in milliseconds
}

const ServerStatusWidget: React.FC<ServerStatusWidgetProps> = ({
  updateInterval = 60000, // Default to 1 minute
  ...widgetProps
}) => {
  const theme = useTheme();
  const [metrics, setMetrics] = useState<ServerMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchServerMetrics = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/admin/system-health`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch server metrics');
      }

      const data = await response.json();
      setMetrics(data);
      setError(null);
    } catch (err) {
      console.error('Error fetching server metrics:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
      
      // Use mock data if API fails
      setMetrics({
        cpuUsage: Math.random() * 60 + 10, // 10-70%
        memoryUsage: Math.random() * 50 + 30, // 30-80%
        diskUsage: Math.random() * 30 + 40, // 40-70%
        uptime: Math.floor(Math.random() * 30 + 1) * 86400, // 1-30 days in seconds
        activeConnections: Math.floor(Math.random() * 50 + 5), // 5-55 connections
        responseTime: Math.random() * 200 + 50, // 50-250ms
        status: 'healthy',
        lastUpdated: new Date().toISOString()
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchServerMetrics();
    
    // Set up interval for periodic updates
    const intervalId = setInterval(fetchServerMetrics, updateInterval);
    
    // Clean up interval on component unmount
    return () => clearInterval(intervalId);
  }, [updateInterval]);

  // Format uptime from seconds to days, hours, minutes
  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    return `${days}d ${hours}h ${minutes}m`;
  };

  // Get color based on usage percentage
  const getUsageColor = (percentage: number) => {
    if (percentage < 60) return theme.palette.success.main;
    if (percentage < 80) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  // Get status chip color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'success';
      case 'warning':
        return 'warning';
      case 'critical':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <DashboardWidget 
      {...widgetProps}
      loading={loading}
      onRefresh={fetchServerMetrics}
      refreshable={true}
    >
      {error ? (
        <Box sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="error" variant="body2">
            {error}
          </Typography>
        </Box>
      ) : metrics ? (
        <Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Chip 
              label={metrics.status.toUpperCase()}
              color={getStatusColor(metrics.status) as any}
              size="small"
              icon={metrics.status !== 'healthy' ? <WarningIcon /> : undefined}
            />
            <Typography variant="caption" color="text.secondary">
              Last updated: {new Date(metrics.lastUpdated).toLocaleTimeString()}
            </Typography>
          </Box>

          <Grid container spacing={2}>
            {/* CPU Usage */}
            <Grid item xs={12}>
              <Box sx={{ mb: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <SpeedIcon fontSize="small" sx={{ mr: 1, color: getUsageColor(metrics.cpuUsage) }} />
                    <Typography variant="body2">CPU Usage</Typography>
                  </Box>
                  <Typography variant="body2" fontWeight="medium">
                    {metrics.cpuUsage.toFixed(1)}%
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={metrics.cpuUsage} 
                  sx={{ 
                    height: 8, 
                    borderRadius: 4,
                    bgcolor: alpha(getUsageColor(metrics.cpuUsage), 0.1),
                    '& .MuiLinearProgress-bar': {
                      bgcolor: getUsageColor(metrics.cpuUsage)
                    }
                  }}
                />
              </Box>
            </Grid>

            {/* Memory Usage */}
            <Grid item xs={12}>
              <Box sx={{ mb: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <MemoryIcon fontSize="small" sx={{ mr: 1, color: getUsageColor(metrics.memoryUsage) }} />
                    <Typography variant="body2">Memory Usage</Typography>
                  </Box>
                  <Typography variant="body2" fontWeight="medium">
                    {metrics.memoryUsage.toFixed(1)}%
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={metrics.memoryUsage} 
                  sx={{ 
                    height: 8, 
                    borderRadius: 4,
                    bgcolor: alpha(getUsageColor(metrics.memoryUsage), 0.1),
                    '& .MuiLinearProgress-bar': {
                      bgcolor: getUsageColor(metrics.memoryUsage)
                    }
                  }}
                />
              </Box>
            </Grid>

            {/* Disk Usage */}
            <Grid item xs={12}>
              <Box sx={{ mb: 1 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <StorageIcon fontSize="small" sx={{ mr: 1, color: getUsageColor(metrics.diskUsage) }} />
                    <Typography variant="body2">Disk Usage</Typography>
                  </Box>
                  <Typography variant="body2" fontWeight="medium">
                    {metrics.diskUsage.toFixed(1)}%
                  </Typography>
                </Box>
                <LinearProgress 
                  variant="determinate" 
                  value={metrics.diskUsage} 
                  sx={{ 
                    height: 8, 
                    borderRadius: 4,
                    bgcolor: alpha(getUsageColor(metrics.diskUsage), 0.1),
                    '& .MuiLinearProgress-bar': {
                      bgcolor: getUsageColor(metrics.diskUsage)
                    }
                  }}
                />
              </Box>
            </Grid>
          </Grid>

          <Divider sx={{ my: 2 }} />

          <Grid container spacing={2}>
            <Grid item xs={6}>
              <Typography variant="body2" color="text.secondary">Uptime</Typography>
              <Typography variant="body1" fontWeight="medium">
                {formatUptime(metrics.uptime)}
              </Typography>
            </Grid>
            <Grid item xs={6}>
              <Typography variant="body2" color="text.secondary">Response Time</Typography>
              <Typography variant="body1" fontWeight="medium">
                {metrics.responseTime.toFixed(0)} ms
              </Typography>
            </Grid>
            <Grid item xs={12}>
              <Typography variant="body2" color="text.secondary">Active Connections</Typography>
              <Typography variant="body1" fontWeight="medium">
                {metrics.activeConnections}
              </Typography>
            </Grid>
          </Grid>
        </Box>
      ) : (
        <Box sx={{ p: 2, textAlign: 'center' }}>
          <Typography color="text.secondary" variant="body2">
            Loading server metrics...
          </Typography>
        </Box>
      )}
    </DashboardWidget>
  );
};

export default ServerStatusWidget;
