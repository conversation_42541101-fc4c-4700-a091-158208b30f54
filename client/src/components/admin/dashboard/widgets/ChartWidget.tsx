import React from 'react';
import { Box, useTheme } from '@mui/material';
import DashboardWidget, { DashboardWidgetProps } from '../DashboardWidget';
import { 
  Chart as ChartJS, 
  CategoryScale, 
  LinearScale, 
  PointElement, 
  LineElement, 
  BarElement, 
  ArcElement, 
  Title, 
  Tooltip, 
  Legend, 
  Filler,
  ChartData,
  ChartOptions
} from 'chart.js';
import { Line, Bar, Pie, Doughnut } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

type ChartType = 'line' | 'bar' | 'pie' | 'doughnut';

interface ChartWidgetProps extends Omit<DashboardWidgetProps, 'children'> {
  chartType: ChartType;
  data: ChartData<any>;
  options?: ChartOptions<any>;
  height?: number;
}

const ChartWidget: React.FC<ChartWidgetProps> = ({
  chartType,
  data,
  options,
  height = 300,
  ...widgetProps
}) => {
  const theme = useTheme();

  // Default chart options based on theme
  const defaultOptions: ChartOptions<any> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          color: theme.palette.text.primary,
          font: {
            family: theme.typography.fontFamily,
            size: 12
          },
          boxWidth: 12,
          padding: 15
        }
      },
      tooltip: {
        backgroundColor: theme.palette.mode === 'dark' 
          ? theme.palette.grey[800] 
          : theme.palette.grey[100],
        titleColor: theme.palette.text.primary,
        bodyColor: theme.palette.text.secondary,
        borderColor: theme.palette.divider,
        borderWidth: 1,
        padding: 10,
        boxPadding: 5,
        usePointStyle: true,
        titleFont: {
          family: theme.typography.fontFamily,
          size: 13
        },
        bodyFont: {
          family: theme.typography.fontFamily,
          size: 12
        }
      }
    },
    scales: chartType === 'line' || chartType === 'bar' ? {
      x: {
        grid: {
          color: theme.palette.divider,
          drawBorder: false
        },
        ticks: {
          color: theme.palette.text.secondary,
          font: {
            family: theme.typography.fontFamily,
            size: 11
          }
        }
      },
      y: {
        grid: {
          color: theme.palette.divider,
          drawBorder: false
        },
        ticks: {
          color: theme.palette.text.secondary,
          font: {
            family: theme.typography.fontFamily,
            size: 11
          }
        }
      }
    } : undefined
  };

  // Merge default options with provided options
  const mergedOptions = {
    ...defaultOptions,
    ...options
  };

  // Render the appropriate chart based on chartType
  const renderChart = () => {
    switch (chartType) {
      case 'line':
        return <Line data={data} options={mergedOptions} />;
      case 'bar':
        return <Bar data={data} options={mergedOptions} />;
      case 'pie':
        return <Pie data={data} options={mergedOptions} />;
      case 'doughnut':
        return <Doughnut data={data} options={mergedOptions} />;
      default:
        return <Line data={data} options={mergedOptions} />;
    }
  };

  return (
    <DashboardWidget {...widgetProps}>
      <Box sx={{ height, width: '100%' }}>
        {renderChart()}
      </Box>
    </DashboardWidget>
  );
};

export default ChartWidget;
