import React from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Typography,
  Divider,
  Avatar,
  Chip,
  useTheme,
  alpha
} from '@mui/material';
import {
  Person as PersonIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Delete as DeleteIcon,
  Add as AddIcon,
  Login as LoginIcon,
  Logout as LogoutIcon,
  Settings as SettingsIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import DashboardWidget, { DashboardWidgetProps } from '../DashboardWidget';

interface ActivityItem {
  id: number | string;
  type: 'login' | 'logout' | 'create' | 'update' | 'delete' | 'view' | 'settings' | 'security' | string;
  description: string;
  user: string;
  timestamp: string;
  details?: string;
  entityType?: 'patient' | 'doctor' | 'user' | 'record' | 'visit' | 'system' | string;
}

interface ActivityWidgetProps extends Omit<DashboardWidgetProps, 'children'> {
  activities: ActivityItem[];
  maxItems?: number;
  showUser?: boolean;
  showTime?: boolean;
  showType?: boolean;
  emptyMessage?: string;
}

const ActivityWidget: React.FC<ActivityWidgetProps> = ({
  activities,
  maxItems = 5,
  showUser = true,
  showTime = true,
  showType = true,
  emptyMessage = 'No recent activity',
  ...widgetProps
}) => {
  const theme = useTheme();

  // Format timestamp
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return new Intl.DateTimeFormat('en-US', {
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
      day: 'numeric',
      month: 'short'
    }).format(date);
  };

  // Get icon for activity type
  const getActivityIcon = (type: string, entityType?: string) => {
    switch (type) {
      case 'login':
        return <LoginIcon fontSize="small" sx={{ color: theme.palette.success.main }} />;
      case 'logout':
        return <LogoutIcon fontSize="small" sx={{ color: theme.palette.info.main }} />;
      case 'create':
        return <AddIcon fontSize="small" sx={{ color: theme.palette.success.main }} />;
      case 'update':
        return <EditIcon fontSize="small" sx={{ color: theme.palette.warning.main }} />;
      case 'delete':
        return <DeleteIcon fontSize="small" sx={{ color: theme.palette.error.main }} />;
      case 'view':
        return <ViewIcon fontSize="small" sx={{ color: theme.palette.info.main }} />;
      case 'settings':
        return <SettingsIcon fontSize="small" sx={{ color: theme.palette.primary.main }} />;
      case 'security':
        return <SecurityIcon fontSize="small" sx={{ color: theme.palette.error.main }} />;
      default:
        return <PersonIcon fontSize="small" sx={{ color: theme.palette.primary.main }} />;
    }
  };

  // Get color for activity type
  const getActivityColor = (type: string) => {
    switch (type) {
      case 'login':
        return theme.palette.success.main;
      case 'logout':
        return theme.palette.info.main;
      case 'create':
        return theme.palette.success.main;
      case 'update':
        return theme.palette.warning.main;
      case 'delete':
        return theme.palette.error.main;
      case 'view':
        return theme.palette.info.main;
      case 'settings':
        return theme.palette.primary.main;
      case 'security':
        return theme.palette.error.main;
      default:
        return theme.palette.primary.main;
    }
  };

  // Get label for activity type
  const getActivityTypeLabel = (type: string) => {
    switch (type) {
      case 'login':
        return 'Login';
      case 'logout':
        return 'Logout';
      case 'create':
        return 'Created';
      case 'update':
        return 'Updated';
      case 'delete':
        return 'Deleted';
      case 'view':
        return 'Viewed';
      case 'settings':
        return 'Settings';
      case 'security':
        return 'Security';
      default:
        return type.charAt(0).toUpperCase() + type.slice(1);
    }
  };

  return (
    <DashboardWidget {...widgetProps}>
      {activities.length === 0 ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', p: 3 }}>
          <Typography variant="body2" color="text.secondary">
            {emptyMessage}
          </Typography>
        </Box>
      ) : (
        <List disablePadding>
          {activities.slice(0, maxItems).map((activity, index) => (
            <React.Fragment key={activity.id}>
              {index > 0 && <Divider component="li" />}
              <ListItem 
                alignItems="flex-start"
                sx={{ 
                  py: 1.5,
                  px: 2,
                  '&:hover': {
                    bgcolor: alpha(theme.palette.primary.main, 0.05)
                  }
                }}
              >
                <ListItemIcon sx={{ minWidth: 40 }}>
                  <Avatar 
                    sx={{ 
                      width: 32, 
                      height: 32,
                      bgcolor: alpha(getActivityColor(activity.type), 0.1),
                      color: getActivityColor(activity.type)
                    }}
                  >
                    {getActivityIcon(activity.type, activity.entityType)}
                  </Avatar>
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 1 }}>
                      <Typography variant="body2" fontWeight="500" component="span">
                        {activity.description}
                      </Typography>
                      {showType && (
                        <Chip
                          label={getActivityTypeLabel(activity.type)}
                          size="small"
                          sx={{ 
                            height: 20,
                            fontSize: '0.7rem',
                            bgcolor: alpha(getActivityColor(activity.type), 0.1),
                            color: getActivityColor(activity.type),
                            '& .MuiChip-label': { px: 1 }
                          }}
                        />
                      )}
                    </Box>
                  }
                  secondary={
                    <Box sx={{ mt: 0.5 }}>
                      {showUser && (
                        <Typography variant="caption" color="text.secondary" component="span">
                          by {activity.user}
                        </Typography>
                      )}
                      {showUser && showTime && (
                        <Typography variant="caption" color="text.secondary" component="span">
                          {' • '}
                        </Typography>
                      )}
                      {showTime && (
                        <Typography variant="caption" color="text.secondary" component="span">
                          {formatTime(activity.timestamp)}
                        </Typography>
                      )}
                      {activity.details && (
                        <Typography variant="caption" display="block" sx={{ mt: 0.5 }}>
                          {activity.details}
                        </Typography>
                      )}
                    </Box>
                  }
                />
              </ListItem>
            </React.Fragment>
          ))}
        </List>
      )}
    </DashboardWidget>
  );
};

export default ActivityWidget;
