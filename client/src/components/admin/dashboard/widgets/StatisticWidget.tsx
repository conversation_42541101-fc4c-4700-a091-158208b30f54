import React from 'react';
import {
  Box,
  Typography,
  Avatar,
  Chip,
  useTheme,
  alpha
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  TrendingFlat as TrendingFlatIcon
} from '@mui/icons-material';
import DashboardWidget, { DashboardWidgetProps } from '../DashboardWidget';
import { SparklineChart } from '../../../common/SparklineChart';

interface StatisticWidgetProps extends Omit<DashboardWidgetProps, 'children'> {
  value: number | string;
  previousValue?: number;
  percentChange?: number;
  trend?: 'up' | 'down' | 'flat';
  trendLabel?: string;
  trendColor?: 'success' | 'error' | 'warning' | 'info' | 'default';
  format?: (value: number | string) => string;
  sparklineData?: number[];
  isLowerBetter?: boolean;
  suffix?: string;
  prefix?: string;
}

const StatisticWidget: React.FC<StatisticWidgetProps> = ({
  value,
  previousValue,
  percentChange,
  trend,
  trendLabel,
  trendColor = 'default',
  format,
  sparklineData,
  isLowerBetter = false,
  suffix,
  prefix,
  ...widgetProps
}) => {
  const theme = useTheme();

  // Determine trend if not explicitly provided
  if (previousValue !== undefined && trend === undefined) {
    if (typeof value === 'number' && typeof previousValue === 'number') {
      if (value > previousValue) {
        trend = 'up';
        trendColor = isLowerBetter ? 'error' : 'success';
      } else if (value < previousValue) {
        trend = 'down';
        trendColor = isLowerBetter ? 'success' : 'error';
      } else {
        trend = 'flat';
        trendColor = 'info';
      }
    }
  }

  // Calculate percent change if not provided
  if (percentChange === undefined && typeof value === 'number' && typeof previousValue === 'number' && previousValue !== 0) {
    percentChange = ((value - previousValue) / previousValue) * 100;
  }

  // Format the value
  const formattedValue = format ? format(value) : value;

  // Determine trend icon
  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUpIcon fontSize="small" />;
      case 'down':
        return <TrendingDownIcon fontSize="small" />;
      case 'flat':
      default:
        return <TrendingFlatIcon fontSize="small" />;
    }
  };

  // Determine trend label if not provided
  const defaultTrendLabel = trendLabel || (
    percentChange !== undefined 
      ? `${percentChange > 0 ? '+' : ''}${percentChange.toFixed(1)}%` 
      : trend === 'up' 
        ? 'Increased' 
        : trend === 'down' 
          ? 'Decreased' 
          : 'No change'
  );

  return (
    <DashboardWidget {...widgetProps}>
      <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
        <Box>
          <Typography variant="h3" fontWeight="500" sx={{ mb: 1 }}>
            {prefix && <span style={{ fontSize: '0.7em', opacity: 0.7 }}>{prefix}</span>}
            {formattedValue}
            {suffix && <span style={{ fontSize: '0.7em', opacity: 0.7 }}>{suffix}</span>}
          </Typography>
        </Box>
        
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: 2 }}>
          {trend && (
            <Chip
              size="small"
              icon={getTrendIcon()}
              label={defaultTrendLabel}
              color={trendColor}
              variant={trendColor === 'default' ? 'outlined' : 'filled'}
              sx={{ 
                height: 24,
                '& .MuiChip-label': {
                  px: 1,
                  fontSize: '0.75rem'
                }
              }}
            />
          )}
          
          {sparklineData && sparklineData.length > 1 && (
            <Box sx={{ ml: 'auto' }}>
              <SparklineChart 
                data={sparklineData} 
                height={24} 
                width={80} 
                isLowerBetter={isLowerBetter}
              />
            </Box>
          )}
        </Box>
      </Box>
    </DashboardWidget>
  );
};

export default StatisticWidget;
