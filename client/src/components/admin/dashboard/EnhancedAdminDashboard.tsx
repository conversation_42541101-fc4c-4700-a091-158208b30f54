import React, { useState, useEffect, useContext, useCallback } from 'react';
import {
  Box,
  Typography,
  Grid,
  Button,
  CircularProgress,
  Alert,
  Paper,
  Avatar,
  IconButton,
  Tooltip,
  useTheme,
  alpha
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import AuthContext from '../../../context/AuthContext';
import LoadingSpinner from '../../../components/common/LoadingSpinner';
import { API_URL } from '../../../config';
import {
  People as PeopleIcon,
  LocalHospital as DoctorIcon,
  AssignmentInd as PatientIcon,
  Timeline as StatisticsIcon,
  SecurityOutlined as SecurityIcon,
  Settings as SettingsIcon,
  TrendingUp as TrendingUpIcon,
  Refresh as RefreshIcon,
  Dashboard as DashboardIcon,
  Add as AddIcon,
  ViewQuilt as ViewQuiltIcon
} from '@mui/icons-material';

// Import dashboard components
import DashboardWidgetGrid, { WidgetConfig } from './DashboardWidgetGrid';
import StatisticWidget from './widgets/StatisticWidget';
import ChartWidget from './widgets/ChartWidget';
import ActivityWidget from './widgets/ActivityWidget';
import ServerStatusWidget from './widgets/ServerStatusWidget';
import DashboardCustomizer from './DashboardCustomizer';
import { DashboardConfigProvider } from '../../../context/DashboardConfigContext';

interface SystemStats {
  totalUsers: number;
  totalDoctors: number;
  totalPatients: number;
  totalVisits: number;
  activeUsers: number;
  patientsByGender?: {
    male: number;
    female: number;
    other: number;
  };
  recentActivity?: Array<{
    id: number;
    type: string;
    description: string;
    user: string;
    timestamp: string;
  }>;
  visitsPerMonth?: Array<{
    month: string;
    count: number;
  }>;
  visitsTrend?: {
    currentMonth: number;
    previousMonth: number;
    percentChange: number;
  };
}

const EnhancedAdminDashboard: React.FC = () => {
  const { user } = useContext(AuthContext);
  const navigate = useNavigate();
  const theme = useTheme();
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [isCustomizerOpen, setIsCustomizerOpen] = useState(false);

  // Default widgets configuration
  const [widgets, setWidgets] = useState<WidgetConfig[]>([
    {
      id: 'users-stats',
      type: 'statistic',
      title: 'Total Users',
      width: 3,
      position: 0
    },
    {
      id: 'doctors-stats',
      type: 'statistic',
      title: 'Doctors',
      width: 3,
      position: 1
    },
    {
      id: 'patients-stats',
      type: 'statistic',
      title: 'Patients',
      width: 3,
      position: 2
    },
    {
      id: 'visits-stats',
      type: 'statistic',
      title: 'Total Visits',
      width: 3,
      position: 3
    },
    {
      id: 'visits-chart',
      type: 'chart',
      title: 'Patient Visits',
      width: 8,
      position: 4
    },
    {
      id: 'recent-activity',
      type: 'activity',
      title: 'Recent Activity',
      width: 4,
      position: 5
    },
    {
      id: 'server-status',
      type: 'server-status',
      title: 'Server Status',
      width: 4,
      position: 6
    }
  ]);

  const fetchStats = useCallback(async () => {
    try {
      setLoading(true);
      setRefreshing(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/admin/detailed-stats`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch system statistics');
      }

      const data = await response.json();

      // Process the data from the API
      setStats({
        totalUsers: data.totalUsers || 0,
        totalDoctors: data.totalDoctors || 0,
        totalPatients: data.totalPatients || 0,
        totalVisits: data.totalVisits || 0,
        activeUsers: data.activeUsers || 0,
        patientsByGender: data.patientsByGender || { male: 0, female: 0, other: 0 },
        recentActivity: data.recentActivity || generateFallbackActivity(),
        visitsPerMonth: data.visitsPerMonth || generateFallbackVisitsData(),
        visitsTrend: data.visitsTrend || { currentMonth: 0, previousMonth: 0, percentChange: 0 }
      });

      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error fetching admin stats:', err);
      // Use fallback data if API fails
      setStats({
        totalUsers: 15,
        totalDoctors: 6,
        totalPatients: 50,
        totalVisits: 120,
        activeUsers: 8,
        patientsByGender: { male: 25, female: 22, other: 3 },
        recentActivity: generateFallbackActivity(),
        visitsPerMonth: generateFallbackVisitsData(),
        visitsTrend: { currentMonth: 120, previousMonth: 100, percentChange: 20 }
      });
    } finally {
      setLoading(false);
      setTimeout(() => setRefreshing(false), 500);
    }
  }, []);

  // Generate fallback activity data if API fails
  const generateFallbackActivity = () => {
    return [
      {
        id: 1,
        type: 'system',
        description: 'System statistics refreshed',
        user: user?.username || 'Admin',
        timestamp: new Date().toISOString()
      },
      {
        id: 2,
        type: 'user',
        description: 'New user account created',
        user: 'System',
        timestamp: new Date(Date.now() - 3600000).toISOString()
      },
      {
        id: 3,
        type: 'patient',
        description: 'Patient record updated',
        user: 'Dr. Smith',
        timestamp: new Date(Date.now() - 7200000).toISOString()
      }
    ];
  };

  // Generate fallback visits data if API fails
  const generateFallbackVisitsData = () => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const currentMonth = new Date().getMonth();

    return months.map((month, index) => ({
      month,
      count: Math.floor(Math.random() * 50) + 50 + (index === currentMonth ? 20 : 0)
    }));
  };

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  // Render widget based on type and configuration
  const renderWidget = (config: WidgetConfig, index: number) => {
    switch (config.type) {
      case 'statistic':
        return renderStatisticWidget(config);
      case 'chart':
        return renderChartWidget(config);
      case 'activity':
        return renderActivityWidget(config);
      case 'server-status':
        return renderServerStatusWidget(config);
      default:
        return null;
    }
  };

  // Render statistic widget
  const renderStatisticWidget = (config: WidgetConfig) => {
    let value = 0;
    let icon = <DashboardIcon />;
    let color = theme.palette.primary.main;
    let trend: 'up' | 'down' | 'flat' = 'flat';
    let trendLabel = '';
    let sparklineData = [50, 60, 55, 70, 65, 75];

    // Configure widget based on ID
    if (config.id === 'users-stats') {
      value = stats?.totalUsers || 0;
      icon = <PeopleIcon />;
      color = theme.palette.primary.main;
      trendLabel = `${stats?.activeUsers || 0} active`;
    } else if (config.id === 'doctors-stats') {
      value = stats?.totalDoctors || 0;
      icon = <DoctorIcon />;
      color = theme.palette.success.main;
      trend = 'up';
      trendLabel = 'Active';
    } else if (config.id === 'patients-stats') {
      value = stats?.totalPatients || 0;
      icon = <PatientIcon />;
      color = theme.palette.warning.main;
      trendLabel = `${stats?.patientsByGender?.male || 0} male, ${stats?.patientsByGender?.female || 0} female`;
    } else if (config.id === 'visits-stats') {
      value = stats?.totalVisits || 0;
      icon = <StatisticsIcon />;
      color = theme.palette.info.main;
      trend = stats?.visitsTrend?.percentChange && stats.visitsTrend.percentChange > 0 ? 'up' : 'down';
      trendLabel = stats?.visitsTrend?.percentChange
        ? `${stats.visitsTrend.percentChange > 0 ? '+' : ''}${stats.visitsTrend.percentChange}%`
        : 'This month';
    }

    return (
      <StatisticWidget
        id={config.id}
        title={config.title}
        icon={icon}
        value={value}
        trend={trend}
        trendLabel={trendLabel}
        sparklineData={sparklineData}
        color={color}
        refreshable={true}
        onRefresh={fetchStats}
        onRemove={(id) => handleRemoveWidget(id)}
        onMoveUp={(id) => handleMoveWidget(id, 'up')}
        onMoveDown={(id) => handleMoveWidget(id, 'down')}
      />
    );
  };

  // Render chart widget
  const renderChartWidget = (config: WidgetConfig) => {
    if (config.id === 'visits-chart') {
      const chartData = {
        labels: stats?.visitsPerMonth?.map(item => item.month) || [],
        datasets: [
          {
            label: 'Patient Visits',
            data: stats?.visitsPerMonth?.map(item => item.count) || [],
            borderColor: theme.palette.primary.main,
            backgroundColor: alpha(theme.palette.primary.main, 0.1),
            tension: 0.3,
            fill: true
          }
        ]
      };

      return (
        <ChartWidget
          id={config.id}
          title={config.title}
          chartType="line"
          data={chartData}
          refreshable={true}
          onRefresh={fetchStats}
          onRemove={(id) => handleRemoveWidget(id)}
          onMoveUp={(id) => handleMoveWidget(id, 'up')}
          onMoveDown={(id) => handleMoveWidget(id, 'down')}
        />
      );
    }
    return null;
  };

  // Render activity widget
  const renderActivityWidget = (config: WidgetConfig) => {
    return (
      <ActivityWidget
        id={config.id}
        title={config.title}
        activities={stats?.recentActivity || []}
        maxItems={5}
        refreshable={true}
        onRefresh={fetchStats}
        onRemove={(id) => handleRemoveWidget(id)}
        onMoveUp={(id) => handleMoveWidget(id, 'up')}
        onMoveDown={(id) => handleMoveWidget(id, 'down')}
      />
    );
  };

  // Render server status widget
  const renderServerStatusWidget = (config: WidgetConfig) => {
    return (
      <ServerStatusWidget
        id={config.id}
        title={config.title}
        updateInterval={60000}
        onRemove={(id) => handleRemoveWidget(id)}
        onMoveUp={(id) => handleMoveWidget(id, 'up')}
        onMoveDown={(id) => handleMoveWidget(id, 'down')}
      />
    );
  };

  // Handle adding a new widget
  const handleAddWidget = () => {
    setIsCustomizerOpen(true);
  };

  // Handle removing a widget
  const handleRemoveWidget = (id: string) => {
    setWidgets(widgets.filter(widget => widget.id !== id));
  };

  // Handle moving a widget up or down
  const handleMoveWidget = (id: string, direction: 'up' | 'down') => {
    const index = widgets.findIndex(widget => widget.id === id);
    if (index === -1) return;

    const newWidgets = [...widgets];
    if (direction === 'up' && index > 0) {
      // Swap with the widget above
      [newWidgets[index - 1], newWidgets[index]] = [newWidgets[index], newWidgets[index - 1]];
    } else if (direction === 'down' && index < widgets.length - 1) {
      // Swap with the widget below
      [newWidgets[index], newWidgets[index + 1]] = [newWidgets[index + 1], newWidgets[index]];
    }

    // Update positions
    newWidgets.forEach((widget, i) => {
      widget.position = i;
    });

    setWidgets(newWidgets);
  };

  if (loading && !stats) {
    return <LoadingSpinner size="large" message="Loading admin dashboard..." />;
  }

  return (
    <DashboardConfigProvider>
        <Box sx={{ p: { xs: 1, md: 2 } }}>
          {/* Header with user info and quick actions */}
          <Paper
            elevation={0}
            sx={{
              p: 3,
              mb: 4,
              borderRadius: 2,
              background: `linear-gradient(to right, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
              color: 'white',
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            <Box sx={{
              position: 'absolute',
              top: 0,
              right: 0,
              width: '30%',
              height: '100%',
              opacity: 0.1,
              background: `url('https://www.transparenttextures.com/patterns/cubes.png')`,
              backgroundSize: 'cover'
            }} />

            <Grid container spacing={2} alignItems="center">
              <Grid item>
                <Avatar
                  sx={{
                    width: 60,
                    height: 60,
                    bgcolor: 'white',
                    color: theme.palette.primary.main,
                    boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                  }}
                >
                  <DashboardIcon fontSize="large" />
                </Avatar>
              </Grid>
              <Grid item xs>
                <Typography variant="h4" fontWeight="500">
                  Enhanced Admin Dashboard
                </Typography>
                <Typography variant="subtitle1" sx={{ opacity: 0.9, mt: 0.5 }}>
                  Welcome back, {user?.username || 'Administrator'}
                </Typography>
              </Grid>
              <Grid item>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Tooltip title="Customize Dashboard">
                    <Button
                      variant="contained"
                      startIcon={<ViewQuiltIcon />}
                      onClick={() => setIsCustomizerOpen(true)}
                      sx={{
                        bgcolor: 'rgba(255,255,255,0.2)',
                        color: 'white',
                        '&:hover': { bgcolor: 'rgba(255,255,255,0.3)' }
                      }}
                    >
                      Customize
                    </Button>
                  </Tooltip>
                  <Tooltip title="Refresh dashboard data">
                    <IconButton
                      color="inherit"
                      onClick={fetchStats}
                      disabled={refreshing}
                      sx={{ bgcolor: 'rgba(255,255,255,0.1)', '&:hover': { bgcolor: 'rgba(255,255,255,0.2)' } }}
                    >
                      {refreshing ? <i className="fas fa-circle-notch fa-spin" /> : <RefreshIcon />}
                    </IconButton>
                  </Tooltip>
                </Box>
              </Grid>
            </Grid>
          </Paper>

          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {/* Dashboard Widgets */}
          <DashboardWidgetGrid
            widgets={widgets}
            renderWidget={renderWidget}
            onAddWidget={handleAddWidget}
            onRemoveWidget={handleRemoveWidget}
            onMoveWidget={handleMoveWidget}
            spacing={3}
            showAddButton={true}
          />

          {/* Dashboard Customizer Dialog */}
          <DashboardCustomizer
            open={isCustomizerOpen}
            onClose={() => setIsCustomizerOpen(false)}
          />
        </Box>
      </DashboardConfigProvider>
  );
};

export default EnhancedAdminDashboard;
