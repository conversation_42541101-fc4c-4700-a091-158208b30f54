import React, { useState, useContext, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Typography,
  Box,
  Divider,
  Grid,
  IconButton,
  Chip,
  useTheme,
  SelectChangeEvent,
  Tabs,
  Tab,
  Alert
} from '@mui/material';
import {
  Close as CloseIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Dashboard as DashboardIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  Star as StarIcon
} from '@mui/icons-material';
import DashboardConfigContext from '../../../context/DashboardConfigContext';
import { WidgetConfig } from './DashboardWidgetGrid';

interface DashboardCustomizerProps {
  open: boolean;
  onClose: () => void;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index, ...other }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`dashboard-tabpanel-${index}`}
      aria-labelledby={`dashboard-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const DashboardCustomizer: React.FC<DashboardCustomizerProps> = ({ open, onClose }) => {
  const theme = useTheme();
  const {
    dashboards,
    currentDashboard,
    loading,
    addDashboard,
    updateDashboard,
    deleteDashboard,
    setDefaultDashboard,
    setCurrentDashboard,
    refreshDashboards
  } = useContext(DashboardConfigContext);

  const [tabValue, setTabValue] = useState(0);
  const [editMode, setEditMode] = useState(false);
  const [selectedDashboard, setSelectedDashboard] = useState<number | null>(null);
  const [dashboardName, setDashboardName] = useState('');
  const [isDefault, setIsDefault] = useState(false);
  const [selectedWidgets, setSelectedWidgets] = useState<WidgetConfig[]>([]);
  const [newDashboardName, setNewDashboardName] = useState('');
  const [newDashboardIsDefault, setNewDashboardIsDefault] = useState(false);
  const [confirmDelete, setConfirmDelete] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Initialize state when dialog opens or current dashboard changes
  useEffect(() => {
    if (open && currentDashboard) {
      setSelectedDashboard(currentDashboard.id || null);
      setDashboardName(currentDashboard.name);
      setIsDefault(currentDashboard.is_default);
      setSelectedWidgets(currentDashboard.widgets);
      setEditMode(false);
      setTabValue(0);
      setConfirmDelete(false);
      setErrorMessage(null);
      setSuccessMessage(null);
    }
  }, [open, currentDashboard]);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle dashboard selection change
  const handleDashboardChange = (event: SelectChangeEvent<number>) => {
    const dashboardId = event.target.value as number;
    const dashboard = dashboards.find(d => d.id === dashboardId);

    if (dashboard) {
      setSelectedDashboard(dashboardId);
      setDashboardName(dashboard.name);
      setIsDefault(dashboard.is_default);
      setSelectedWidgets(dashboard.widgets);
      setEditMode(false);
      setConfirmDelete(false);
    }
  };

  // Handle edit mode toggle
  const handleEditToggle = () => {
    setEditMode(!editMode);
  };

  // Handle dashboard name change
  const handleNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setDashboardName(event.target.value);
  };

  // Handle default toggle
  const handleDefaultToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
    setIsDefault(event.target.checked);
  };

  // Handle save dashboard
  const handleSaveDashboard = async () => {
    try {
      if (!selectedDashboard) return;

      const dashboard = dashboards.find(d => d.id === selectedDashboard);
      if (!dashboard) return;

      const updatedDashboard = {
        ...dashboard,
        name: dashboardName,
        is_default: isDefault,
        widgets: selectedWidgets
      };

      await updateDashboard(updatedDashboard);

      if (isDefault) {
        await setDefaultDashboard(selectedDashboard);
      }

      setEditMode(false);
      setSuccessMessage('Dashboard updated successfully');
      setTimeout(() => setSuccessMessage(null), 3000);
    } catch (err) {
      console.error('Error saving dashboard:', err);
      setErrorMessage(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  // Handle delete dashboard
  const handleDeleteDashboard = async () => {
    try {
      if (!selectedDashboard) return;

      await deleteDashboard(selectedDashboard);

      setConfirmDelete(false);
      setSuccessMessage('Dashboard deleted successfully');
      setTimeout(() => setSuccessMessage(null), 3000);

      // Select another dashboard if available
      if (dashboards.length > 1) {
        const newDashboard = dashboards.find(d => d.id !== selectedDashboard);
        if (newDashboard) {
          setSelectedDashboard(newDashboard.id || null);
          setDashboardName(newDashboard.name);
          setIsDefault(newDashboard.is_default);
          setSelectedWidgets(newDashboard.widgets);
        }
      }
    } catch (err) {
      console.error('Error deleting dashboard:', err);
      setErrorMessage(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  // Handle create new dashboard
  const handleCreateDashboard = async () => {
    try {
      if (!newDashboardName.trim()) {
        setErrorMessage('Dashboard name is required');
        return;
      }

      const newDashboard = await addDashboard({
        name: newDashboardName,
        is_default: newDashboardIsDefault,
        widgets: []
      });

      setNewDashboardName('');
      setNewDashboardIsDefault(false);
      setSuccessMessage('Dashboard created successfully');
      setTimeout(() => setSuccessMessage(null), 3000);

      // Switch to the new dashboard
      setSelectedDashboard(newDashboard.id || null);
      setDashboardName(newDashboard.name);
      setIsDefault(newDashboard.is_default);
      setSelectedWidgets(newDashboard.widgets);
      setTabValue(0);
    } catch (err) {
      console.error('Error creating dashboard:', err);
      setErrorMessage(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  // Handle set current dashboard
  const handleSetCurrentDashboard = () => {
    if (!selectedDashboard) return;

    const dashboard = dashboards.find(d => d.id === selectedDashboard);
    if (dashboard) {
      setCurrentDashboard(dashboard);
      onClose();
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          overflow: 'hidden'
        }
      }}
    >
      <DialogTitle sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        bgcolor: theme.palette.primary.main,
        color: 'white',
        p: 2
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <DashboardIcon sx={{ mr: 1 }} />
          <Typography variant="h6">Dashboard Customizer</Typography>
        </Box>
        <IconButton onClick={onClose} sx={{ color: 'white' }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <Tabs
        value={tabValue}
        onChange={handleTabChange}
        sx={{
          borderBottom: 1,
          borderColor: 'divider',
          bgcolor: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.02)'
        }}
      >
        <Tab label="Manage Dashboards" />
        <Tab label="Create New Dashboard" />
      </Tabs>

      <DialogContent sx={{ p: 0 }}>
        {errorMessage && (
          <Alert severity="error" sx={{ m: 2 }} onClose={() => setErrorMessage(null)}>
            {errorMessage}
          </Alert>
        )}

        {successMessage && (
          <Alert severity="success" sx={{ m: 2 }} onClose={() => setSuccessMessage(null)}>
            {successMessage}
          </Alert>
        )}

        <TabPanel value={tabValue} index={0}>
          <Box>
            <FormControl fullWidth sx={{ mb: 3 }}>
              <InputLabel id="dashboard-select-label">Select Dashboard</InputLabel>
              <Select
                labelId="dashboard-select-label"
                value={selectedDashboard || ''}
                onChange={handleDashboardChange}
                label="Select Dashboard"
                disabled={loading}
              >
                {dashboards.map(dashboard => (
                  <MenuItem key={dashboard.id} value={dashboard.id}>
                    <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                      <Typography sx={{ flexGrow: 1 }}>
                        {dashboard.name}
                      </Typography>
                      {dashboard.is_default && (
                        <Chip
                          size="small"
                          label="Default"
                          color="primary"
                          icon={<StarIcon fontSize="small" />}
                          sx={{ ml: 1 }}
                        />
                      )}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {selectedDashboard && (
              <>
                <Box sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  mb: 2
                }}>
                  <Typography variant="h6">Dashboard Settings</Typography>
                  <Box>
                    <Button
                      variant="outlined"
                      color={editMode ? 'primary' : 'secondary'}
                      startIcon={editMode ? <SaveIcon /> : <EditIcon />}
                      onClick={editMode ? handleSaveDashboard : handleEditToggle}
                      sx={{ mr: 1 }}
                    >
                      {editMode ? 'Save Changes' : 'Edit Dashboard'}
                    </Button>
                    <Button
                      variant="contained"
                      color="primary"
                      onClick={handleSetCurrentDashboard}
                    >
                      Use This Dashboard
                    </Button>
                  </Box>
                </Box>

                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      label="Dashboard Name"
                      value={dashboardName}
                      onChange={handleNameChange}
                      fullWidth
                      disabled={!editMode}
                      margin="normal"
                    />

                    <FormControlLabel
                      control={
                        <Switch
                          checked={isDefault}
                          onChange={handleDefaultToggle}
                          disabled={!editMode}
                        />
                      }
                      label="Set as Default Dashboard"
                      sx={{ mt: 2 }}
                    />
                  </Grid>

                  <Grid item xs={12} md={6}>
                    <Box sx={{
                      border: '1px solid',
                      borderColor: 'divider',
                      borderRadius: 1,
                      p: 2,
                      height: '100%'
                    }}>
                      <Typography variant="subtitle2" gutterBottom>
                        Dashboard Actions
                      </Typography>
                      <Divider sx={{ mb: 2 }} />

                      <Button
                        variant="outlined"
                        color="error"
                        startIcon={<DeleteIcon />}
                        onClick={() => setConfirmDelete(true)}
                        disabled={dashboards.length <= 1}
                        fullWidth
                        sx={{ mb: 1 }}
                      >
                        Delete Dashboard
                      </Button>

                      <Button
                        variant="outlined"
                        startIcon={<RefreshIcon />}
                        onClick={refreshDashboards}
                        fullWidth
                      >
                        Refresh Dashboards
                      </Button>
                    </Box>
                  </Grid>
                </Grid>

                {confirmDelete && (
                  <Alert
                    severity="warning"
                    sx={{ mt: 3 }}
                    action={
                      <Box>
                        <Button
                          color="inherit"
                          size="small"
                          onClick={() => setConfirmDelete(false)}
                          sx={{ mr: 1 }}
                        >
                          Cancel
                        </Button>
                        <Button
                          color="error"
                          size="small"
                          variant="contained"
                          onClick={handleDeleteDashboard}
                        >
                          Delete
                        </Button>
                      </Box>
                    }
                  >
                    Are you sure you want to delete this dashboard? This action cannot be undone.
                  </Alert>
                )}
              </>
            )}
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Box>
            <Typography variant="h6" gutterBottom>
              Create New Dashboard
            </Typography>

            <TextField
              label="Dashboard Name"
              value={newDashboardName}
              onChange={(e) => setNewDashboardName(e.target.value)}
              fullWidth
              margin="normal"
              required
            />

            <FormControlLabel
              control={
                <Switch
                  checked={newDashboardIsDefault}
                  onChange={(e) => setNewDashboardIsDefault(e.target.checked)}
                />
              }
              label="Set as Default Dashboard"
              sx={{ mt: 2, mb: 3 }}
            />

            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleCreateDashboard}
              disabled={!newDashboardName.trim()}
              fullWidth
            >
              Create Dashboard
            </Button>
          </Box>
        </TabPanel>
      </DialogContent>

      <DialogActions sx={{ p: 2, borderTop: '1px solid', borderColor: 'divider' }}>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};

export default DashboardCustomizer;
