import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>eader,
  IconButton,
  Menu,
  MenuItem,
  Typography,
  Box,
  Divider,
  useTheme,
  Tooltip,
  CircularProgress,
  alpha
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  Refresh as RefreshIcon,
  Fullscreen as FullscreenIcon,
  Close as CloseIcon,
  Settings as SettingsIcon,
  ArrowUpward as ArrowUpwardIcon,
  ArrowDownward as ArrowDownwardIcon
} from '@mui/icons-material';

export interface DashboardWidgetProps {
  id: string;
  title: string;
  subtitle?: string;
  icon?: React.ReactNode;
  refreshable?: boolean;
  onRefresh?: () => Promise<void>;
  onRemove?: (id: string) => void;
  onExpand?: (id: string) => void;
  onConfigure?: (id: string) => void;
  onMoveUp?: (id: string) => void;
  onMoveDown?: (id: string) => void;
  loading?: boolean;
  height?: number | string;
  width?: number | string;
  minHeight?: number | string;
  maxHeight?: number | string;
  children: React.ReactNode;
  color?: string;
  variant?: 'default' | 'outlined' | 'elevation';
  className?: string;
}

const DashboardWidget: React.FC<DashboardWidgetProps> = ({
  id,
  title,
  subtitle,
  icon,
  refreshable = false,
  onRefresh,
  onRemove,
  onExpand,
  onConfigure,
  onMoveUp,
  onMoveDown,
  loading = false,
  height = 'auto',
  width = '100%',
  minHeight = '200px',
  maxHeight,
  children,
  color,
  variant = 'default',
  className
}) => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [refreshing, setRefreshing] = useState(false);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleRefresh = async () => {
    if (onRefresh) {
      setRefreshing(true);
      await onRefresh();
      setRefreshing(false);
    }
  };

  const handleRemove = () => {
    if (onRemove) {
      onRemove(id);
    }
    handleMenuClose();
  };

  const handleExpand = () => {
    if (onExpand) {
      onExpand(id);
    }
    handleMenuClose();
  };

  const handleConfigure = () => {
    if (onConfigure) {
      onConfigure(id);
    }
    handleMenuClose();
  };

  const handleMoveUp = () => {
    if (onMoveUp) {
      onMoveUp(id);
    }
    handleMenuClose();
  };

  const handleMoveDown = () => {
    if (onMoveDown) {
      onMoveDown(id);
    }
    handleMenuClose();
  };

  // Determine card background color based on theme and provided color
  const cardBgColor = color
    ? alpha(color, theme.palette.mode === 'dark' ? 0.15 : 0.05)
    : undefined;

  // Determine header background color based on theme and provided color
  const headerBgColor = color
    ? alpha(color, theme.palette.mode === 'dark' ? 0.2 : 0.1)
    : theme.palette.mode === 'dark'
      ? alpha(theme.palette.primary.main, 0.15)
      : alpha(theme.palette.primary.main, 0.05);

  return (
    <Card
      sx={{
        height,
        width,
        minHeight,
        maxHeight,
        display: 'flex',
        flexDirection: 'column',
        borderRadius: 2,
        overflow: 'hidden',
        boxShadow: variant === 'elevation' ? 3 : 'none',
        border: variant === 'outlined' ? 1 : 'none',
        borderColor: 'divider',
        bgcolor: cardBgColor,
        transition: 'all 0.3s ease',
        '&:hover': {
          boxShadow: variant === 'elevation' ? 5 : variant === 'outlined' ? 'none' : 1,
        }
      }}
      className={className}
    >
      <CardHeader
        avatar={icon}
        title={
          <Typography variant="h6" fontWeight="500">
            {title}
          </Typography>
        }
        subheader={subtitle && (
          <Typography variant="body2" color="text.secondary">
            {subtitle}
          </Typography>
        )}
        action={
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {refreshable && (
              <Tooltip title="Refresh">
                <IconButton
                  size="small"
                  onClick={handleRefresh}
                  disabled={refreshing}
                >
                  {refreshing ? (
                    <CircularProgress size={20} />
                  ) : (
                    <RefreshIcon fontSize="small" />
                  )}
                </IconButton>
              </Tooltip>
            )}
            <Tooltip title="Widget options">
              <IconButton
                size="small"
                onClick={handleMenuOpen}
              >
                <MoreVertIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <Menu
              anchorEl={anchorEl}
              open={Boolean(anchorEl)}
              onClose={handleMenuClose}
            >
              {onRefresh && (
                <MenuItem onClick={handleRefresh}>
                  <RefreshIcon fontSize="small" sx={{ mr: 1 }} />
                  Refresh
                </MenuItem>
              )}
              {onExpand && (
                <MenuItem onClick={handleExpand}>
                  <FullscreenIcon fontSize="small" sx={{ mr: 1 }} />
                  Expand
                </MenuItem>
              )}
              {onConfigure && (
                <MenuItem onClick={handleConfigure}>
                  <SettingsIcon fontSize="small" sx={{ mr: 1 }} />
                  Configure
                </MenuItem>
              )}
              {onMoveUp && (
                <MenuItem onClick={handleMoveUp}>
                  <ArrowUpwardIcon fontSize="small" sx={{ mr: 1 }} />
                  Move Up
                </MenuItem>
              )}
              {onMoveDown && (
                <MenuItem onClick={handleMoveDown}>
                  <ArrowDownwardIcon fontSize="small" sx={{ mr: 1 }} />
                  Move Down
                </MenuItem>
              )}
              {onRemove && (
                <>
                  <Divider />
                  <MenuItem onClick={handleRemove}>
                    <CloseIcon fontSize="small" sx={{ mr: 1 }} />
                    Remove
                  </MenuItem>
                </>
              )}
            </Menu>
          </Box>
        }
        sx={{
          bgcolor: headerBgColor,
          borderBottom: '1px solid',
          borderColor: 'divider',
          p: 1.5,
        }}
      />
      <CardContent
        sx={{
          flexGrow: 1,
          overflow: 'auto',
          position: 'relative',
          p: 2
        }}
      >
        {loading ? (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100%',
              width: '100%',
              position: 'absolute',
              top: 0,
              left: 0,
              bgcolor: alpha(theme.palette.background.paper, 0.7),
              zIndex: 1
            }}
          >
            <CircularProgress />
          </Box>
        ) : null}
        {children}
      </CardContent>
    </Card>
  );
};

export default DashboardWidget;
