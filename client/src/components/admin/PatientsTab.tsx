import React from 'react';

interface Patient {
  patient_id: number;
  first_name: string;
  last_name: string;
  unique_id: string;
  date_of_birth: string;
  gender: string;
  doctor_id: number;
  doctor_name?: string | null;
}

interface PatientsTabProps {
  patients: Patient[];
  onEditPatient: (patient: Patient) => void;
  onDeletePatient: (patientId: number) => void;
  onRefreshData: () => void;
}

const PatientsTab: React.FC<PatientsTabProps> = ({ 
  patients, 
  onEditPatient, 
  onDeletePatient, 
  onRefreshData 
}) => {
  console.log('PatientsTab rendered with', patients?.length || 0, 'patients');
  
  return (
    <div>
      <h3>Patients Management</h3>
      
      {/* Debug Info */}
      <div style={{ backgroundColor: '#f8f9fa', padding: '10px', marginBottom: '15px', borderRadius: '5px' }}>
        <p><strong>Debug:</strong> PatientsTab component rendered</p>
        <p><strong>Data:</strong> {patients ? `${patients.length} patients found` : 'No patients data'}</p>
      </div>
      
      {patients && patients.length > 0 ? (
        <div>
          <p>Found {patients.length} patients</p>
          <div className="table-responsive">
            <table className="table table-striped">
              <thead className="bg-primary text-white">
                <tr>
                  <th>Name</th>
                  <th>ID</th>
                  <th>Date of Birth</th>
                  <th>Gender</th>
                  <th>Doctor</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {patients.map(patient => (
                  <tr key={patient.patient_id}>
                    <td>
                      {patient.first_name} {patient.last_name}
                    </td>
                    <td>{patient.unique_id}</td>
                    <td>{new Date(patient.date_of_birth).toLocaleDateString()}</td>
                    <td>{patient.gender}</td>
                    <td>{patient.doctor_name || 'Not assigned'}</td>
                    <td>
                      <button
                        className="btn btn-sm btn-primary mr-2"
                        onClick={() => onEditPatient(patient)}
                      >
                        <i className="fas fa-edit"></i> Edit
                      </button>
                      <button
                        className="btn btn-sm btn-danger"
                        onClick={() => onDeletePatient(patient.patient_id)}
                      >
                        <i className="fas fa-trash"></i> Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          
          <div style={{ margin: '20px 0', padding: '15px', border: '1px solid #ccc' }}>
            <h4>Debug Information</h4>
            <pre style={{ maxHeight: '200px', overflow: 'auto' }}>
              {JSON.stringify(patients, null, 2)}
            </pre>
          </div>
        </div>
      ) : (
        <div className="alert alert-info">
          <p>No patients found or data is still loading.</p>
          <p>Patients state: {patients ? `Array with ${patients.length} items` : 'undefined'}</p>
          <button className="btn btn-primary" onClick={onRefreshData}>
            <i className="fas fa-sync-alt"></i> Refresh Data
          </button>
        </div>
      )}
    </div>
  );
};

export default PatientsTab; 