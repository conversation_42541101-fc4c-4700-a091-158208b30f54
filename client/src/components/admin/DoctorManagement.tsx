import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  Snackbar,
  Alert,
  CircularProgress,
  InputAdornment,
  Grid,
  Tooltip,
  Avatar,
  Card,
  CardContent,
  Tabs,
  Tab,
  MenuItem
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Search as SearchIcon,
  Person as PersonIcon,
  GetApp as GetAppIcon,
  Info as InfoIcon,
  People as PeopleIcon,
  Clear as ClearIcon,
  FilterListOff as FilterListOffIcon,
  LocalHospital as DoctorIcon
} from '@mui/icons-material';
import { API_URL } from '../../config';

interface Doctor {
  doctor_id: number;
  first_name: string;
  last_name: string;
  specialty: string | null;
  email: string;
  phone: string | null;
  user_id?: number;
  username?: string;
  user_email?: string;
  patient_count?: number;
  visit_count?: number;
}

interface DoctorFormData {
  first_name: string;
  last_name: string;
  specialty: string;
  email: string;
  phone: string;
}

interface Patient {
  patient_id: number;
  first_name: string;
  last_name: string;
  unique_id: string;
  date_of_birth: string;
  gender: string;
  phone: string;
  email: string | null;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`doctor-tabpanel-${index}`}
      aria-labelledby={`doctor-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const DoctorManagement: React.FC = () => {
  // State variables
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [filteredDoctors, setFilteredDoctors] = useState<Doctor[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [notification, setNotification] = useState<{ type: 'success' | 'error', message: string } | null>(null);

  // Dialog states
  const [isAddDialogOpen, setIsAddDialogOpen] = useState<boolean>(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState<boolean>(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState<boolean>(false);
  const [selectedDoctor, setSelectedDoctor] = useState<Doctor | null>(null);

  // Form data
  const [formData, setFormData] = useState<DoctorFormData>({
    first_name: '',
    last_name: '',
    specialty: '',
    email: '',
    phone: ''
  });

  // Add new state variables
  const [detailsDialogOpen, setDetailsDialogOpen] = useState<boolean>(false);
  const [patientsDialogOpen, setPatientsDialogOpen] = useState<boolean>(false);
  const [doctorPatients, setDoctorPatients] = useState<Patient[]>([]);
  const [loadingPatients, setLoadingPatients] = useState<boolean>(false);
  const [tabValue, setTabValue] = useState<number>(0);
  const [doctorStats, setDoctorStats] = useState<{visitCount: number, patientCount: number}>({
    visitCount: 0,
    patientCount: 0
  });
  const [specialtyFilter, setSpecialtyFilter] = useState<string>('');
  const [specialties, setSpecialties] = useState<string[]>([]);

  // Fetch doctors on component mount
  useEffect(() => {
    fetchDoctors();
  }, []);

  // Extract unique specialties from doctors
  useEffect(() => {
    if (doctors.length > 0) {
      const uniqueSpecialties = Array.from(
        new Set(
          doctors
            .map(doctor => doctor.specialty)
            .filter(specialty => specialty !== null && specialty !== '') as string[]
        )
      ).sort();

      setSpecialties(uniqueSpecialties);
    }
  }, [doctors]);

  // Filter doctors based on search term and specialty
  useEffect(() => {
    let filtered = [...doctors];

    // Apply search term filter
    if (searchTerm.trim()) {
      const lowercasedTerm = searchTerm.toLowerCase();
      filtered = filtered.filter(doctor => {
        return (
          doctor.first_name.toLowerCase().includes(lowercasedTerm) ||
          doctor.last_name.toLowerCase().includes(lowercasedTerm) ||
          (doctor.specialty && doctor.specialty.toLowerCase().includes(lowercasedTerm)) ||
          doctor.email.toLowerCase().includes(lowercasedTerm) ||
          (doctor.phone && doctor.phone.toLowerCase().includes(lowercasedTerm))
        );
      });
    }

    // Apply specialty filter
    if (specialtyFilter) {
      filtered = filtered.filter(doctor => doctor.specialty === specialtyFilter);
    }

    setFilteredDoctors(filtered);
  }, [searchTerm, doctors, specialtyFilter]);

  // Fetch doctors from API
  const fetchDoctors = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(`${API_URL}/api/admin/doctors`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch doctors');
      }

      const data = await response.json();
      setDoctors(data);
      setFilteredDoctors(data);
    } catch (err) {
      console.error('Error fetching doctors:', err);
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Reset form to default values
  const resetForm = () => {
    setFormData({
      first_name: '',
      last_name: '',
      specialty: '',
      email: '',
      phone: ''
    });
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Handle specialty filter change
  const handleSpecialtyFilterChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSpecialtyFilter(e.target.value);
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm('');
    setSpecialtyFilter('');
  };

  // Handle opening edit dialog
  const handleOpenEditDialog = (doctor: Doctor) => {
    setSelectedDoctor(doctor);
    setFormData({
      first_name: doctor.first_name,
      last_name: doctor.last_name,
      specialty: doctor.specialty || '',
      email: doctor.email,
      phone: doctor.phone || ''
    });
    setIsEditDialogOpen(true);
  };

  // Handle opening delete dialog
  const handleOpenDeleteDialog = (doctor: Doctor) => {
    setSelectedDoctor(doctor);
    setIsDeleteDialogOpen(true);
  };

  // Handle closing all dialogs
  const handleCloseDialogs = () => {
    setIsAddDialogOpen(false);
    setIsEditDialogOpen(false);
    setIsDeleteDialogOpen(false);
    setDetailsDialogOpen(false);
    setPatientsDialogOpen(false);
    setSelectedDoctor(null);
    resetForm();
  };

  // Add a new doctor
  const handleAddDoctor = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(`${API_URL}/api/doctors`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.msg || 'Failed to add doctor');
      }

      setNotification({
        type: 'success',
        message: 'Doctor added successfully'
      });
      handleCloseDialogs();
      fetchDoctors();
    } catch (err) {
      console.error('Error adding doctor:', err);
      setNotification({
        type: 'error',
        message: err instanceof Error ? err.message : 'An error occurred'
      });
    }
  };

  // Update an existing doctor
  const handleUpdateDoctor = async () => {
    if (!selectedDoctor) return;

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(`${API_URL}/api/doctors/${selectedDoctor.doctor_id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.msg || 'Failed to update doctor');
      }

      setNotification({
        type: 'success',
        message: 'Doctor updated successfully'
      });
      handleCloseDialogs();
      fetchDoctors();
    } catch (err) {
      console.error('Error updating doctor:', err);
      setNotification({
        type: 'error',
        message: err instanceof Error ? err.message : 'An error occurred'
      });
    }
  };

  // Delete a doctor
  const handleDeleteDoctor = async () => {
    if (!selectedDoctor) return;

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(`${API_URL}/api/doctors/${selectedDoctor.doctor_id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.msg || 'Failed to delete doctor');
      }

      setNotification({
        type: 'success',
        message: 'Doctor deleted successfully'
      });
      handleCloseDialogs();
      fetchDoctors();
    } catch (err) {
      console.error('Error deleting doctor:', err);
      setNotification({
        type: 'error',
        message: err instanceof Error ? err.message : 'An error occurred'
      });
    }
  };

  // Handle closing notification
  const handleCloseNotification = () => {
    setNotification(null);
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle opening details dialog
  const handleOpenDetailsDialog = (doctor: Doctor) => {
    setSelectedDoctor(doctor);
    setDetailsDialogOpen(true);
    fetchDoctorStats(doctor.doctor_id);
  };

  // Handle opening patients dialog
  const handleOpenPatientsDialog = (doctor: Doctor) => {
    setSelectedDoctor(doctor);
    setPatientsDialogOpen(true);
    fetchDoctorPatients(doctor.doctor_id);
  };

  // Fetch doctor statistics
  const fetchDoctorStats = async (doctorId: number) => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(`${API_URL}/api/admin/doctors/${doctorId}/stats`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch doctor statistics');
      }

      const data = await response.json();
      setDoctorStats({
        visitCount: data.visitCount || 0,
        patientCount: data.patientCount || 0
      });
    } catch (err) {
      console.error('Error fetching doctor stats:', err);
      setNotification({
        type: 'error',
        message: err instanceof Error ? err.message : 'Failed to fetch doctor statistics'
      });
    }
  };

  // Fetch doctor's patients
  const fetchDoctorPatients = async (doctorId: number) => {
    try {
      setLoadingPatients(true);
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(`${API_URL}/api/doctors/${doctorId}/patients`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch doctor\'s patients');
      }

      const data = await response.json();
      setDoctorPatients(data);
    } catch (err) {
      console.error('Error fetching doctor\'s patients:', err);
      setNotification({
        type: 'error',
        message: err instanceof Error ? err.message : 'Failed to fetch doctor\'s patients'
      });
    } finally {
      setLoadingPatients(false);
    }
  };



  // Calculate age
  const calculateAge = (dateOfBirth: string): number => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDifference = today.getMonth() - birthDate.getMonth();

    if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  // Export doctors to CSV
  const exportDoctorsToCSV = () => {
    // Define the headers
    const headers = [
      'ID',
      'First Name',
      'Last Name',
      'Specialty',
      'Email',
      'Phone',
      'Username'
    ];

    // Map the doctor data to CSV format
    const data = doctors.map(doctor => [
      doctor.doctor_id,
      doctor.first_name,
      doctor.last_name,
      doctor.specialty || '',
      doctor.email,
      doctor.phone || '',
      doctor.username || ''
    ]);

    // Combine headers and data
    const csvContent = [
      headers.join(','),
      ...data.map(row => row.join(','))
    ].join('\n');

    // Create a blob and download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `doctors_export_${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();

    // Clean up
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    setNotification({
      type: 'success',
      message: 'Doctors exported to CSV successfully'
    });
  };

  if (loading && doctors.length === 0) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="80vh">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 4,
          borderRadius: 2,
          background: 'linear-gradient(to right, #4caf50, #66bb6a)',
          color: 'white',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        <Box sx={{
          position: 'absolute',
          top: 0,
          right: 0,
          width: '30%',
          height: '100%',
          opacity: 0.1,
          background: `url('https://www.transparenttextures.com/patterns/cubes.png')`,
          backgroundSize: 'cover'
        }} />

        <Grid container spacing={2} alignItems="center">
          <Grid item>
            <Avatar
              sx={{
                width: 60,
                height: 60,
                bgcolor: 'white',
                color: '#4caf50',
                boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
              }}
            >
              <DoctorIcon fontSize="large" />
            </Avatar>
          </Grid>
          <Grid item xs>
            <Typography variant="h4" fontWeight="500">
              Doctor Management
            </Typography>
            <Typography variant="subtitle1" sx={{ opacity: 0.9, mt: 0.5 }}>
              Manage all doctors, specialties, and patient assignments
            </Typography>
          </Grid>
          <Grid item>
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button
                variant="outlined"
                startIcon={<GetAppIcon />}
                onClick={exportDoctorsToCSV}
                size="medium"
                sx={{
                  borderColor: 'white',
                  color: 'white',
                  '&:hover': {
                    borderColor: 'white',
                    bgcolor: 'rgba(255,255,255,0.1)'
                  }
                }}
              >
                Export CSV
              </Button>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setIsAddDialogOpen(true)}
                size="medium"
                sx={{
                  bgcolor: 'white',
                  color: '#4caf50',
                  '&:hover': {
                    bgcolor: 'rgba(255,255,255,0.9)'
                  }
                }}
              >
                Add Doctor
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      <Paper elevation={2} sx={{ mb: 4, p: { xs: 1.5, sm: 2 }, borderRadius: 2 }}>
        <Box sx={{
          display: 'flex',
          flexDirection: { xs: 'column', sm: 'row' },
          gap: 2,
          alignItems: 'center'
        }}>
          <TextField
            placeholder="Search by name, email..."
            variant="outlined"
            size="small"
            value={searchTerm}
            onChange={handleSearchChange}
            sx={{ flexGrow: 1, width: { xs: '100%', sm: 'auto' } }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />

          <Box sx={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: 2,
            width: { xs: '100%', sm: 'auto' }
          }}>
            <TextField
              select
              label="Specialty"
              value={specialtyFilter}
              onChange={handleSpecialtyFilterChange}
              size="small"
              sx={{ minWidth: { xs: '100%', sm: 180 } }}
              InputProps={{
                startAdornment: specialtyFilter ? (
                  <Tooltip title="Clear filter">
                    <IconButton
                      size="small"
                      onClick={() => setSpecialtyFilter('')}
                      sx={{ position: 'absolute', right: 28, zIndex: 1 }}
                    >
                      <ClearIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                ) : undefined
              }}
            >
              <MenuItem value="">All Specialties</MenuItem>
              {specialties.map(specialty => (
                <MenuItem key={specialty} value={specialty}>
                  {specialty}
                </MenuItem>
              ))}
            </TextField>

            <Box sx={{ display: 'flex', gap: 1, width: { xs: '100%', sm: 'auto' } }}>
              {(searchTerm || specialtyFilter) && (
                <Button
                  variant="outlined"
                  size="small"
                  onClick={clearFilters}
                  startIcon={<FilterListOffIcon />}
                  sx={{ flex: { xs: 1, sm: 'none' } }}
                >
                  Clear
                </Button>
              )}

              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={fetchDoctors}
                size="small"
                sx={{ flex: { xs: 1, sm: 'none' } }}
              >
                Refresh
              </Button>
            </Box>
          </Box>
        </Box>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper elevation={2} sx={{ borderRadius: 2 }}>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                <TableCell>ID</TableCell>
                <TableCell>Name</TableCell>
                <TableCell>Specialty</TableCell>
                <TableCell>Email</TableCell>
                <TableCell>Phone</TableCell>
                <TableCell align="center">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredDoctors.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    {searchTerm ? `No doctors matching "${searchTerm}"` : 'No doctors found'}
                  </TableCell>
                </TableRow>
              ) : (
                filteredDoctors.map((doctor) => (
                  <TableRow key={doctor.doctor_id}>
                    <TableCell>{doctor.doctor_id}</TableCell>
                    <TableCell>{`${doctor.first_name} ${doctor.last_name}`}</TableCell>
                    <TableCell>{doctor.specialty || 'N/A'}</TableCell>
                    <TableCell>{doctor.email}</TableCell>
                    <TableCell>{doctor.phone || 'N/A'}</TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1 }}>
                        <Tooltip title="View Details">
                          <IconButton
                            size="small"
                            sx={{ color: 'info.main' }}
                            onClick={() => handleOpenDetailsDialog(doctor)}
                          >
                            <InfoIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="View Patients">
                          <IconButton
                            size="small"
                            sx={{ color: 'info.main' }}
                            onClick={() => handleOpenPatientsDialog(doctor)}
                          >
                            <PeopleIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Edit Doctor">
                          <IconButton
                            size="small"
                            sx={{ color: 'primary.main' }}
                            onClick={() => handleOpenEditDialog(doctor)}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete Doctor">
                          <IconButton
                            size="small"
                            sx={{ color: 'error.main' }}
                            onClick={() => handleOpenDeleteDialog(doctor)}
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      </Paper>

      {/* Add Doctor Dialog */}
      <Dialog
        open={isAddDialogOpen}
        onClose={handleCloseDialogs}
        maxWidth="sm"
        fullWidth
        PaperProps={{ sx: { borderRadius: 2 } }}
      >
        <DialogTitle sx={{ pb: 1, borderBottom: '1px solid #eee' }}>
          <Typography variant="h6">Add New Doctor</Typography>
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                autoFocus
                name="first_name"
                label="First Name"
                type="text"
                fullWidth
                variant="outlined"
                value={formData.first_name}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                name="last_name"
                label="Last Name"
                type="text"
                fullWidth
                variant="outlined"
                value={formData.last_name}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="specialty"
                label="Specialty"
                type="text"
                fullWidth
                variant="outlined"
                value={formData.specialty}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="email"
                label="Email"
                type="email"
                fullWidth
                variant="outlined"
                value={formData.email}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="phone"
                label="Phone"
                type="text"
                fullWidth
                variant="outlined"
                value={formData.phone}
                onChange={handleInputChange}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid #eee' }}>
          <Button onClick={handleCloseDialogs} variant="outlined">Cancel</Button>
          <Button onClick={handleAddDoctor} variant="contained" startIcon={<AddIcon />}>Add Doctor</Button>
        </DialogActions>
      </Dialog>

      {/* Edit Doctor Dialog */}
      <Dialog
        open={isEditDialogOpen}
        onClose={handleCloseDialogs}
        maxWidth="sm"
        fullWidth
        PaperProps={{ sx: { borderRadius: 2 } }}
      >
        <DialogTitle sx={{ pb: 1, borderBottom: '1px solid #eee' }}>
          <Typography variant="h6">Edit Doctor</Typography>
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                autoFocus
                name="first_name"
                label="First Name"
                type="text"
                fullWidth
                variant="outlined"
                value={formData.first_name}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                name="last_name"
                label="Last Name"
                type="text"
                fullWidth
                variant="outlined"
                value={formData.last_name}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="specialty"
                label="Specialty"
                type="text"
                fullWidth
                variant="outlined"
                value={formData.specialty}
                onChange={handleInputChange}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="email"
                label="Email"
                type="email"
                fullWidth
                variant="outlined"
                value={formData.email}
                onChange={handleInputChange}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                name="phone"
                label="Phone"
                type="text"
                fullWidth
                variant="outlined"
                value={formData.phone}
                onChange={handleInputChange}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid #eee' }}>
          <Button onClick={handleCloseDialogs} variant="outlined">Cancel</Button>
          <Button onClick={handleUpdateDoctor} variant="contained">Save Changes</Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={isDeleteDialogOpen}
        onClose={handleCloseDialogs}
        maxWidth="sm"
        fullWidth
        PaperProps={{ sx: { borderRadius: 2 } }}
      >
        <DialogTitle sx={{ pb: 1, borderBottom: '1px solid #eee' }}>
          <Typography variant="h6">Confirm Deletion</Typography>
        </DialogTitle>
        <DialogContent sx={{ pt: 3, pb: 2, px: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <Avatar sx={{ bgcolor: 'error.main' }}>
              <DeleteIcon />
            </Avatar>
            <Typography variant="body1" sx={{ fontWeight: 500 }}>
              Are you sure you want to delete this doctor?
            </Typography>
          </Box>
          <Typography color="text.secondary">
            You are about to delete Dr. {selectedDoctor?.first_name} {selectedDoctor?.last_name}.
            This action cannot be undone and will remove all associations with patients.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid #eee' }}>
          <Button onClick={handleCloseDialogs} variant="outlined">Cancel</Button>
          <Button
            onClick={handleDeleteDoctor}
            color="error"
            variant="contained"
            startIcon={<DeleteIcon />}
          >
            Delete Doctor
          </Button>
        </DialogActions>
      </Dialog>

      {/* Doctor Details Dialog */}
      <Dialog
        open={detailsDialogOpen}
        onClose={handleCloseDialogs}
        maxWidth="md"
        fullWidth
      >
        {selectedDoctor && (
          <>
            <DialogTitle>
              Doctor Details: Dr. {selectedDoctor.first_name} {selectedDoctor.last_name}
            </DialogTitle>
            <DialogContent>
              <Box sx={{ py: 2 }}>
                <Tabs value={tabValue} onChange={handleTabChange} centered>
                  <Tab label="Basic Information" />
                  <Tab label="Statistics" />
                </Tabs>

                <TabPanel value={tabValue} index={0}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Doctor ID
                      </Typography>
                      <Typography variant="body1">
                        {selectedDoctor.doctor_id}
                      </Typography>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Full Name
                      </Typography>
                      <Typography variant="body1">
                        Dr. {selectedDoctor.first_name} {selectedDoctor.last_name}
                      </Typography>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Specialty
                      </Typography>
                      <Typography variant="body1">
                        {selectedDoctor.specialty || 'Not specified'}
                      </Typography>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Email
                      </Typography>
                      <Typography variant="body1">
                        {selectedDoctor.email}
                      </Typography>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Phone
                      </Typography>
                      <Typography variant="body1">
                        {selectedDoctor.phone || 'Not provided'}
                      </Typography>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" color="text.secondary">
                        Associated User Account
                      </Typography>
                      <Typography variant="body1">
                        {selectedDoctor.username ? `${selectedDoctor.username} (${selectedDoctor.user_email})` : 'No user account linked'}
                      </Typography>
                    </Grid>
                  </Grid>
                </TabPanel>

                <TabPanel value={tabValue} index={1}>
                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={6}>
                      <Card>
                        <CardContent>
                          <Typography variant="h6" gutterBottom align="center">
                            Total Patients
                          </Typography>
                          <Typography variant="h3" align="center" color="primary">
                            {doctorStats.patientCount}
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Card>
                        <CardContent>
                          <Typography variant="h6" gutterBottom align="center">
                            Total Visits
                          </Typography>
                          <Typography variant="h3" align="center" color="secondary">
                            {doctorStats.visitCount}
                          </Typography>
                        </CardContent>
                      </Card>
                    </Grid>
                  </Grid>
                </TabPanel>
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseDialogs}>Close</Button>
              <Button
                variant="outlined"
                color="primary"
                onClick={() => {
                  handleCloseDialogs();
                  if (selectedDoctor) {
                    handleOpenPatientsDialog(selectedDoctor);
                  }
                }}
              >
                View Patients
              </Button>
              <Button
                variant="contained"
                color="primary"
                onClick={() => {
                  handleCloseDialogs();
                  if (selectedDoctor) {
                    handleOpenEditDialog(selectedDoctor);
                  }
                }}
              >
                Edit Doctor
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Doctor Patients Dialog */}
      <Dialog
        open={patientsDialogOpen}
        onClose={handleCloseDialogs}
        maxWidth="md"
        fullWidth
      >
        {selectedDoctor && (
          <>
            <DialogTitle>
              Patients of Dr. {selectedDoctor.first_name} {selectedDoctor.last_name}
            </DialogTitle>
            <DialogContent>
              {loadingPatients ? (
                <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
                  <CircularProgress />
                </Box>
              ) : doctorPatients.length === 0 ? (
                <Box sx={{ py: 3, textAlign: 'center' }}>
                  <Typography variant="body1">
                    No patients assigned to this doctor.
                  </Typography>
                </Box>
              ) : (
                <TableContainer component={Paper}>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>MedID</TableCell>
                        <TableCell>Name</TableCell>
                        <TableCell>Age/Gender</TableCell>
                        <TableCell>Contact</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {doctorPatients.map((patient) => (
                        <TableRow key={patient.patient_id}>
                          <TableCell>{patient.unique_id}</TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <PersonIcon sx={{ mr: 1, color: 'primary.main' }} />
                              <Typography variant="body1">
                                {patient.first_name} {patient.last_name}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            {calculateAge(patient.date_of_birth)} yrs / {patient.gender}
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">{patient.phone}</Typography>
                            <Typography variant="body2" color="text.secondary">
                              {patient.email || 'No email'}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseDialogs}>Close</Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification !== null}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification?.type || 'info'}
          sx={{ width: '100%' }}
        >
          {notification?.message || ''}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default DoctorManagement;