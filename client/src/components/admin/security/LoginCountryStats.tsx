import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  LinearProgress,
  useTheme,
  alpha,
  Chip,
  CircularProgress
} from '@mui/material';
import PublicIcon from '@mui/icons-material/Public';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';

interface CountryStats {
  country: string;
  total: number;
  successful: number;
  failed: number;
}

interface LoginCountryStatsProps {
  countries: CountryStats[];
  loading?: boolean;
  error?: string | null;
}

const LoginCountryStats: React.FC<LoginCountryStatsProps> = ({ 
  countries, 
  loading = false, 
  error = null 
}) => {
  const theme = useTheme();

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  if (!countries || countries.length === 0) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="body1" color="text.secondary">
          No country data available. Login activity by country will appear here once collected.
        </Typography>
      </Box>
    );
  }

  // Calculate the maximum total for scaling
  const maxTotal = Math.max(...countries.map(country => country.total));

  return (
    <TableContainer component={Paper} elevation={0} sx={{ 
      borderRadius: 2,
      boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
      overflow: 'hidden'
    }}>
      <Table size="small">
        <TableHead sx={{ bgcolor: alpha(theme.palette.primary.main, 0.03) }}>
          <TableRow>
            <TableCell sx={{ fontWeight: 600 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <PublicIcon fontSize="small" color="primary" />
                Country
              </Box>
            </TableCell>
            <TableCell sx={{ fontWeight: 600 }}>Login Attempts</TableCell>
            <TableCell sx={{ fontWeight: 600 }}>Success Rate</TableCell>
            <TableCell sx={{ fontWeight: 600 }}>Details</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {countries.map((country, index) => {
            const successRate = country.total > 0 
              ? Math.round((country.successful / country.total) * 100) 
              : 0;
            
            return (
              <TableRow 
                key={country.country} 
                sx={{ 
                  '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.02) },
                  bgcolor: index % 2 === 0 ? 'transparent' : alpha(theme.palette.primary.main, 0.01)
                }}
              >
                <TableCell>
                  <Typography variant="body2" fontWeight={500}>
                    {country.country}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                    <Typography variant="body2">{country.total} attempts</Typography>
                    <LinearProgress 
                      variant="determinate" 
                      value={(country.total / maxTotal) * 100}
                      sx={{ 
                        height: 4, 
                        borderRadius: 2,
                        bgcolor: alpha(theme.palette.primary.main, 0.1)
                      }}
                    />
                  </Box>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <LinearProgress 
                      variant="determinate" 
                      value={successRate}
                      sx={{ 
                        height: 8, 
                        borderRadius: 2,
                        width: 60,
                        bgcolor: alpha(theme.palette.error.main, 0.1),
                        '& .MuiLinearProgress-bar': {
                          bgcolor: successRate > 80 
                            ? theme.palette.success.main 
                            : successRate > 50 
                              ? theme.palette.warning.main 
                              : theme.palette.error.main
                        }
                      }}
                    />
                    <Typography variant="body2" fontWeight={500}>
                      {successRate}%
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Chip
                      icon={<CheckCircleIcon fontSize="small" />}
                      label={country.successful}
                      size="small"
                      sx={{
                        bgcolor: alpha(theme.palette.success.main, 0.1),
                        color: theme.palette.success.main,
                        fontWeight: 500,
                        '& .MuiChip-icon': { color: theme.palette.success.main }
                      }}
                    />
                    <Chip
                      icon={<ErrorIcon fontSize="small" />}
                      label={country.failed}
                      size="small"
                      sx={{
                        bgcolor: alpha(theme.palette.error.main, 0.1),
                        color: theme.palette.error.main,
                        fontWeight: 500,
                        '& .MuiChip-icon': { color: theme.palette.error.main }
                      }}
                    />
                  </Box>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default LoginCountryStats;
