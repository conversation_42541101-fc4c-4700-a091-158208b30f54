import React, { useEffect, useState } from 'react';
import { Box, Typography, Paper, CircularProgress, useTheme, alpha } from '@mui/material';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, CircleMarker } from 'react-leaflet';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';

// Fix for default marker icons in react-leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon-2x.png',
  iconUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-icon.png',
  shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
});

interface Location {
  lat: number;
  lng: number;
  city: string;
  country: string;
  count: number;
  status: string;
}

interface LoginGeoMapProps {
  locations: Location[];
  loading?: boolean;
  error?: string | null;
}

const LoginGeoMap: React.FC<LoginGeoMapProps> = ({ locations, loading = false, error = null }) => {
  const theme = useTheme();
  const [mapCenter, setMapCenter] = useState<[number, number]>([20, 0]);
  const [mapZoom, setMapZoom] = useState(2);

  // If there are locations, center the map on the most active location
  useEffect(() => {
    if (locations && locations.length > 0) {
      // Sort by count (descending) and use the top location as center
      const sortedLocations = [...locations].sort((a, b) => b.count - a.count);
      setMapCenter([sortedLocations[0].lat, sortedLocations[0].lng]);
      setMapZoom(3);
    }
  }, [locations]);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  if (!locations || locations.length === 0) {
    return (
      <Box sx={{ p: 3, textAlign: 'center', height: 400, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Typography variant="body1" color="text.secondary">
          No geographic data available. Login activity locations will appear here once collected.
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ height: 400, width: '100%', position: 'relative' }}>
      <MapContainer
        center={mapCenter as L.LatLngExpression}
        zoom={mapZoom}
        style={{ height: '100%', width: '100%', borderRadius: 8 }}
      >
        <TileLayer
          attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
        />

        {locations.map((location, index) => (
          <CircleMarker
            key={`${location.lat}-${location.lng}-${index}`}
            center={[location.lat, location.lng] as L.LatLngExpression}
            radius={Math.min(20, Math.max(5, Math.sqrt(location.count) * 3))}
            pathOptions={{
              fillColor: location.status === 'success'
                ? theme.palette.success.main
                : location.status === 'failed'
                  ? theme.palette.error.main
                  : theme.palette.warning.main,
              color: location.status === 'success'
                ? theme.palette.success.dark
                : location.status === 'failed'
                  ? theme.palette.error.dark
                  : theme.palette.warning.dark,
              fillOpacity: 0.6,
              weight: 1
            }}
          >
            <Popup>
              <Box sx={{ p: 1 }}>
                <Typography variant="subtitle2" fontWeight="bold">
                  {location.city}, {location.country}
                </Typography>
                <Typography variant="body2">
                  {location.count} login {location.count === 1 ? 'attempt' : 'attempts'}
                </Typography>
                <Typography variant="body2" sx={{
                  color: location.status === 'success'
                    ? theme.palette.success.main
                    : location.status === 'failed'
                      ? theme.palette.error.main
                      : theme.palette.warning.main,
                  fontWeight: 500
                }}>
                  Status: {location.status.charAt(0).toUpperCase() + location.status.slice(1)}
                </Typography>
              </Box>
            </Popup>
          </CircleMarker>
        ))}
      </MapContainer>
    </Box>
  );
};

export default LoginGeoMap;
