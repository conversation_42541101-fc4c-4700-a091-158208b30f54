import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  Tabs,
  Tab,
  CircularProgress,
  useTheme,
  alpha,
  Divider,
  IconButton,
  Tooltip,
  Alert
} from '@mui/material';
import {
  Security as SecurityIcon,
  Refresh as RefreshIcon,
  Public as PublicIcon,
  DevicesOther as DevicesIcon,
  Timeline as TimelineIcon,
  Download as DownloadIcon,
  Notifications as NotificationsIcon
} from '@mui/icons-material';
import axios from 'axios';
import { API_URL } from '../../../config';
import LoginGeoMap from './LoginGeoMap';
import LoginCountryStats from './LoginCountryStats';
import LoginDeviceStats from './LoginDeviceStats';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`security-tabpanel-${index}`}
      aria-labelledby={`security-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

const EnhancedSecurityDashboard: React.FC = () => {
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [geoStats, setGeoStats] = useState<any>(null);
  const [loginStats, setLoginStats] = useState<any>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<string>('');

  const fetchData = async () => {
    try {
      setRefreshing(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      // Fetch login statistics
      const loginStatsResponse = await axios.get(`${API_URL}/api/logs/login-activity/stats`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      setLoginStats(loginStatsResponse.data);

      // Fetch geographic statistics
      const geoStatsResponse = await axios.get(`${API_URL}/api/logs/login-activity/geo-stats`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      setGeoStats(geoStatsResponse.data);
      setLastUpdated(new Date().toLocaleTimeString());
    } catch (err) {
      console.error('Error fetching security data:', err);
      setError('Failed to fetch security data. Please try again later.');
    } finally {
      setLoading(false);
      setTimeout(() => setRefreshing(false), 500);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleRefresh = () => {
    fetchData();
  };

  const handleExportData = () => {
    // Implement export functionality
    alert('Export functionality will be implemented in a future update.');
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress size={60} thickness={4} />
      </Box>
    );
  }

  return (
    <Box sx={{ mb: 4 }}>
      {/* Header */}
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 3,
          borderRadius: 2,
          background: 'linear-gradient(to right, #1a237e, #3949ab)',
          color: 'white',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        <Box sx={{
          position: 'absolute',
          top: 0,
          right: 0,
          width: '30%',
          height: '100%',
          opacity: 0.1,
          background: `url('https://www.transparenttextures.com/patterns/cubes.png')`,
          backgroundSize: 'cover'
        }} />

        <Grid container spacing={2} alignItems="center">
          <Grid item>
            <SecurityIcon sx={{ fontSize: 40 }} />
          </Grid>
          <Grid item xs>
            <Typography variant="h4" fontWeight="500">
              Enhanced Security Dashboard
            </Typography>
            <Typography variant="subtitle1" sx={{ opacity: 0.9, mt: 0.5 }}>
              Advanced security monitoring and threat detection
            </Typography>
          </Grid>
          <Grid item>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Tooltip title="Refresh data">
                <IconButton
                  color="inherit"
                  onClick={handleRefresh}
                  disabled={refreshing}
                  sx={{
                    bgcolor: 'rgba(255,255,255,0.1)',
                    '&:hover': { bgcolor: 'rgba(255,255,255,0.2)' }
                  }}
                >
                  {refreshing ? <CircularProgress size={24} color="inherit" /> : <RefreshIcon />}
                </IconButton>
              </Tooltip>
              <Tooltip title="Export data">
                <IconButton
                  color="inherit"
                  onClick={handleExportData}
                  sx={{
                    bgcolor: 'rgba(255,255,255,0.1)',
                    '&:hover': { bgcolor: 'rgba(255,255,255,0.2)' }
                  }}
                >
                  <DownloadIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Grid>
        </Grid>

        {lastUpdated && (
          <Typography variant="caption" sx={{ display: 'block', mt: 1, opacity: 0.8 }}>
            Last updated: {lastUpdated}
          </Typography>
        )}
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Tabs */}
      <Paper
        elevation={0}
        sx={{
          borderRadius: 2,
          boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
          overflow: 'hidden'
        }}
      >
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              py: 2
            }
          }}
        >
          <Tab
            icon={<PublicIcon />}
            label="Geographic Analysis"
            iconPosition="start"
          />
          <Tab
            icon={<DevicesIcon />}
            label="Device & Browser Analysis"
            iconPosition="start"
          />
          <Tab
            icon={<TimelineIcon />}
            label="Threat Detection"
            iconPosition="start"
          />
        </Tabs>

        {/* Geographic Analysis Tab */}
        <TabPanel value={tabValue} index={0}>
          <Typography variant="h6" gutterBottom>
            Login Attempt Locations
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            This map shows the geographic distribution of login attempts. Larger circles indicate more login attempts from that location.
          </Typography>

          <LoginGeoMap 
            locations={geoStats?.locations || []} 
            loading={loading} 
            error={error} 
          />

          <Box sx={{ mt: 4 }}>
            <Typography variant="h6" gutterBottom>
              Login Attempts by Country
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              Breakdown of login attempts by country, showing success rates and failed attempts.
            </Typography>

            <LoginCountryStats 
              countries={geoStats?.countries || []} 
              loading={loading} 
              error={error} 
            />
          </Box>
        </TabPanel>

        {/* Device & Browser Analysis Tab */}
        <TabPanel value={tabValue} index={1}>
          <Typography variant="h6" gutterBottom>
            Device & Browser Analysis
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            This analysis shows the distribution of login attempts across different devices and browsers.
          </Typography>

          <LoginDeviceStats 
            deviceStats={loginStats?.deviceStats || []} 
            browserStats={loginStats?.browserStats || []} 
            loading={loading} 
            error={error} 
          />
        </TabPanel>

        {/* Threat Detection Tab */}
        <TabPanel value={tabValue} index={2}>
          <Typography variant="h6" gutterBottom>
            Threat Detection & Alerting
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            Real-time monitoring and detection of suspicious activities and potential security threats.
          </Typography>

          <Alert 
            severity="info" 
            sx={{ mb: 3 }}
            icon={<NotificationsIcon />}
          >
            Threat detection features will be available in a future update. This will include anomaly detection, suspicious activity alerts, and automated response capabilities.
          </Alert>

          <Box sx={{ textAlign: 'center', p: 4 }}>
            <Typography variant="h6" color="text.secondary">
              Coming Soon
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Advanced threat detection and alerting capabilities are under development.
            </Typography>
          </Box>
        </TabPanel>
      </Paper>
    </Box>
  );
};

export default EnhancedSecurityDashboard;
