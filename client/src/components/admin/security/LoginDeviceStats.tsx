import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  useTheme,
  alpha,
  CircularProgress,
  Divider
} from '@mui/material';
import {
  Laptop as LaptopIcon,
  PhoneAndroid as PhoneIcon,
  Tablet as TabletIcon,
  DevicesOther as OtherDeviceIcon,
  Language as BrowserIcon
} from '@mui/icons-material';
import { Doughnut } from 'react-chartjs-2';

interface DeviceStat {
  device: string;
  count: number;
}

interface BrowserStat {
  browser: string;
  count: number;
}

interface LoginDeviceStatsProps {
  deviceStats: DeviceStat[];
  browserStats: BrowserStat[];
  loading?: boolean;
  error?: string | null;
}

const LoginDeviceStats: React.FC<LoginDeviceStatsProps> = ({
  deviceStats,
  browserStats,
  loading = false,
  error = null
}) => {
  const theme = useTheme();

  // Get device icon based on device type
  const getDeviceIcon = (device: string) => {
    switch (device.toLowerCase()) {
      case 'desktop':
        return <LaptopIcon />;
      case 'mobile':
        return <PhoneIcon />;
      case 'tablet':
        return <TabletIcon />;
      default:
        return <OtherDeviceIcon />;
    }
  };

  // Prepare chart data for devices
  const deviceChartData = {
    labels: deviceStats.map(stat => stat.device),
    datasets: [
      {
        data: deviceStats.map(stat => stat.count),
        backgroundColor: [
          theme.palette.primary.main,
          theme.palette.success.main,
          theme.palette.warning.main,
          theme.palette.error.main,
          theme.palette.info.main
        ],
        borderWidth: 1,
        borderColor: theme.palette.background.paper
      }
    ]
  };

  // Prepare chart data for browsers
  const browserChartData = {
    labels: browserStats.map(stat => stat.browser),
    datasets: [
      {
        data: browserStats.map(stat => stat.count),
        backgroundColor: [
          theme.palette.primary.main,
          theme.palette.success.main,
          theme.palette.warning.main,
          theme.palette.error.main,
          theme.palette.info.main
        ],
        borderWidth: 1,
        borderColor: theme.palette.background.paper
      }
    ]
  };

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const label = context.label || '';
            const value = context.raw || 0;
            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
            const percentage = Math.round((value / total) * 100);
            return `${label}: ${value} (${percentage}%)`;
          }
        }
      }
    },
    cutout: '70%'
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 200 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  if ((!deviceStats || deviceStats.length === 0) && (!browserStats || browserStats.length === 0)) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="body1" color="text.secondary">
          No device or browser data available. This information will appear once collected.
        </Typography>
      </Box>
    );
  }

  return (
    <Grid container spacing={3}>
      {/* Device Stats */}
      <Grid item xs={12} md={6}>
        <Paper
          elevation={0}
          sx={{
            p: 2,
            borderRadius: 2,
            boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
            height: '100%'
          }}
        >
          <Typography variant="subtitle1" fontWeight="500" gutterBottom>
            Device Distribution
          </Typography>
          <Divider sx={{ mb: 2 }} />

          <Box sx={{ display: 'flex', height: 200 }}>
            <Box sx={{ width: '50%', height: '100%', position: 'relative' }}>
              <Doughnut data={deviceChartData} options={chartOptions} />
            </Box>
            <Box sx={{ width: '50%', pl: 2 }}>
              {deviceStats.map((stat, index) => (
                <Box
                  key={stat.device}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    mb: 1,
                    p: 1,
                    borderRadius: 1,
                    bgcolor: index === 0 ? alpha(theme.palette.primary.main, 0.1) : 'transparent'
                  }}
                >
                  <Box
                    sx={{
                      width: 24,
                      height: 24,
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 1,
                      bgcolor: deviceChartData.datasets[0].backgroundColor[index % 5],
                      color: 'white'
                    }}
                  >
                    {getDeviceIcon(stat.device)}
                  </Box>
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="body2" fontWeight="500">
                      {stat.device}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {stat.count} logins
                    </Typography>
                  </Box>
                </Box>
              ))}
            </Box>
          </Box>
        </Paper>
      </Grid>

      {/* Browser Stats */}
      <Grid item xs={12} md={6}>
        <Paper
          elevation={0}
          sx={{
            p: 2,
            borderRadius: 2,
            boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
            height: '100%'
          }}
        >
          <Typography variant="subtitle1" fontWeight="500" gutterBottom>
            Browser Distribution
          </Typography>
          <Divider sx={{ mb: 2 }} />

          <Box sx={{ display: 'flex', height: 200 }}>
            <Box sx={{ width: '50%', height: '100%', position: 'relative' }}>
              <Doughnut data={browserChartData} options={chartOptions} />
            </Box>
            <Box sx={{ width: '50%', pl: 2 }}>
              {browserStats.map((stat, index) => (
                <Box
                  key={stat.browser}
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    mb: 1,
                    p: 1,
                    borderRadius: 1,
                    bgcolor: index === 0 ? alpha(theme.palette.primary.main, 0.1) : 'transparent'
                  }}
                >
                  <Box
                    sx={{
                      width: 24,
                      height: 24,
                      borderRadius: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 1,
                      bgcolor: browserChartData.datasets[0].backgroundColor[index % 5],
                      color: 'white'
                    }}
                  >
                    <BrowserIcon fontSize="small" />
                  </Box>
                  <Box sx={{ flexGrow: 1 }}>
                    <Typography variant="body2" fontWeight="500">
                      {stat.browser}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {stat.count} logins
                    </Typography>
                  </Box>
                </Box>
              ))}
            </Box>
          </Box>
        </Paper>
      </Grid>
    </Grid>
  );
};

export default LoginDeviceStats;
