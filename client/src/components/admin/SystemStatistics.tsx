import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Divider,
  Avatar,
  Button,
  IconButton,
  Tooltip,
  LinearProgress,
  Chip,
  useTheme,
  alpha
} from '@mui/material';
import {
  PeopleOutline as UserIcon,
  LocalHospital as DoctorIcon,
  PersonOutline as PatientIcon,
  EventNote as VisitIcon,
  TrendingUp as ActivityIcon,
  Storage as DatabaseIcon,
  Business as OrgIcon,
  Favorite as HeartIcon,
  // MonitorHeart as VitalsIcon - Unused import
  // MedicalInformation as ConditionsIcon - Unused import
  Refresh as RefreshIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Assessment as AssessmentIcon,
  CheckCircle as CheckCircleIcon,
  // Warning as WarningIcon - Unused import
  Error as ErrorIcon,
  Info as InfoIcon,
  Speed as SpeedIcon,
  <PERSON><PERSON><PERSON> as BarChartIcon,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Timeline as TimelineIcon,
  Equalizer as EqualizerIcon
} from '@mui/icons-material';
import { API_URL } from '../../config';
import { Chart as ChartJS, ArcElement, Tooltip as ChartTooltip, Legend, CategoryScale, LinearScale, PointElement, LineElement, BarElement, Title } from 'chart.js';
import { Pie, Line, Bar } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(
  ArcElement,
  ChartTooltip,
  Legend,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title
);

interface SystemStats {
  totalUsers: number;
  totalDoctors: number;
  totalPatients: number;
  totalVisits: number;
  activeUsers: number;

  // Demographics data
  patientsByGender: {
    male: number;
    female: number;
    other: number;
  };
  patientsByAge: {
    range: string;
    count: number;
    male: number;
    female: number;
    other: number;
  }[];

  // Activity data
  visitsPerMonth: {
    month: string;
    count: number;
  }[];
  visitsTrend: {
    currentMonth: number;
    previousMonth: number;
    percentChange: number;
  };

  // Provider analysis data
  topDoctors: {
    id: number;
    name: string;
    specialty: string;
    patientCount: number;
    visitCount: number;
    recentVisits: number;
  }[];
  doctorEfficiency: {
    id: number;
    name: string;
    uniquePatients: number;
    totalVisits: number;
    visitsPerPatient: number;
  }[];

  // Health metrics data
  commonConditions: {
    name: string;
    count: number;
    percentage: string;
  }[];
  averageVitals: {
    systolic: number;
    diastolic: number;
    heartRate: number;
    oxygenLevel: number;
    temperature: string;
    respiratoryRate: number;
  };
  averageLabValues: {
    bloodGlucose: number;
    cholesterol: number;
    hba1c: string;
  };
  bpCategories: {
    normal: { count: number; percentage: number };
    elevated: { count: number; percentage: number };
    hypertensionStage1: { count: number; percentage: number };
    hypertensionStage2: { count: number; percentage: number };
  };
  glucoseCategories: {
    low: { count: number; percentage: number };
    normal: { count: number; percentage: number };
    prediabetes: { count: number; percentage: number };
    diabetes: { count: number; percentage: number };
  };
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const SystemStatistics: React.FC = () => {
  const theme = useTheme();
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [lastUpdated, setLastUpdated] = useState<string>('');

  const fetchStats = useCallback(async () => {
    try {
      setRefreshing(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      // Fetch real data from the API
      const response = await fetch(`${API_URL}/api/admin/detailed-stats`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch system statistics');
      }

      const data = await response.json();
      console.log('Fetched system statistics:', data);
      setStats(data);
      setError(null);
      setLastUpdated(new Date().toLocaleTimeString());
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error fetching statistics:', err);

      // Fallback to mock data if API call fails
        const mockData: SystemStats = {
          totalUsers: 108,
          totalDoctors: 28,
          totalPatients: 876,
          totalVisits: 3450,
          activeUsers: 87,

          // Demographics data
          patientsByGender: {
            male: 412,
            female: 443,
            other: 21
          },
          patientsByAge: [
            { range: '65-70', count: 35, male: 18, female: 16, other: 1 },
            { range: '71-75', count: 28, male: 14, female: 13, other: 1 },
            { range: '76-80', count: 22, male: 10, female: 11, other: 1 },
            { range: '81-85', count: 15, male: 6, female: 8, other: 1 },
            { range: '86-90', count: 8, male: 3, female: 5, other: 0 },
            { range: '91+', count: 5, male: 2, female: 3, other: 0 }
          ],

          // Activity data
          visitsPerMonth: [
            { month: 'Jan', count: 245 },
            { month: 'Feb', count: 230 },
            { month: 'Mar', count: 278 },
            { month: 'Apr', count: 300 },
            { month: 'May', count: 265 },
            { month: 'Jun', count: 310 },
            { month: 'Jul', count: 352 },
            { month: 'Aug', count: 334 },
            { month: 'Sep', count: 371 },
            { month: 'Oct', count: 398 },
            { month: 'Nov', count: 312 },
            { month: 'Dec', count: 287 }
          ],
          visitsTrend: {
            currentMonth: 312,
            previousMonth: 287,
            percentChange: 8
          },

          // Provider analysis data
          topDoctors: [
            { id: 1, name: 'Dr. Sarah Wilson', specialty: 'Cardiology', patientCount: 87, visitCount: 342, recentVisits: 28 },
            { id: 2, name: 'Dr. John Smith', specialty: 'General Practice', patientCount: 72, visitCount: 285, recentVisits: 22 },
            { id: 3, name: 'Dr. Raj Patel', specialty: 'Neurology', patientCount: 65, visitCount: 210, recentVisits: 18 },
            { id: 4, name: 'Dr. David Chen', specialty: 'Pediatrics', patientCount: 58, visitCount: 195, recentVisits: 15 },
            { id: 5, name: 'Dr. Maria Rodriguez', specialty: 'Oncology', patientCount: 52, visitCount: 178, recentVisits: 12 }
          ],
          doctorEfficiency: [
            { id: 1, name: 'Dr. Sarah Wilson', uniquePatients: 87, totalVisits: 342, visitsPerPatient: 3.9 },
            { id: 2, name: 'Dr. John Smith', uniquePatients: 72, totalVisits: 285, visitsPerPatient: 3.9 },
            { id: 3, name: 'Dr. Raj Patel', uniquePatients: 65, totalVisits: 210, visitsPerPatient: 3.2 },
            { id: 4, name: 'Dr. David Chen', uniquePatients: 58, totalVisits: 195, visitsPerPatient: 3.4 },
            { id: 5, name: 'Dr. Maria Rodriguez', uniquePatients: 52, totalVisits: 178, visitsPerPatient: 3.4 }
          ],

          // Health metrics data
          commonConditions: [
            { name: 'Hypertension', count: 25, percentage: '18.5' },
            { name: 'Diabetes', count: 18, percentage: '13.3' },
            { name: 'Asthma', count: 12, percentage: '8.9' },
            { name: 'COPD', count: 8, percentage: '5.9' },
            { name: 'Heart Disease', count: 15, percentage: '11.1' }
          ],
          averageVitals: {
            systolic: 120,
            diastolic: 80,
            heartRate: 72,
            oxygenLevel: 95,
            temperature: '36.5',
            respiratoryRate: 16
          },
          averageLabValues: {
            bloodGlucose: 95,
            cholesterol: 185,
            hba1c: '5.7'
          },
          bpCategories: {
            normal: { count: 450, percentage: 45 },
            elevated: { count: 220, percentage: 22 },
            hypertensionStage1: { count: 180, percentage: 18 },
            hypertensionStage2: { count: 150, percentage: 15 }
          },
          glucoseCategories: {
            low: { count: 50, percentage: 5 },
            normal: { count: 650, percentage: 65 },
            prediabetes: { count: 200, percentage: 20 },
            diabetes: { count: 100, percentage: 10 }
          }
        };

        setStats(mockData);
      } finally {
        setLoading(false);
        setTimeout(() => setRefreshing(false), 600);
      }
    }, []);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleRefresh = () => {
    fetchStats();
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Paper
          elevation={0}
          sx={{
            p: 3,
            mb: 4,
            borderRadius: 2,
            background: 'linear-gradient(to right, #3f51b5, #5c6bc0)',
            color: 'white',
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          <Box sx={{
            position: 'absolute',
            top: 0,
            right: 0,
            width: '30%',
            height: '100%',
            opacity: 0.1,
            background: `url('https://www.transparenttextures.com/patterns/cubes.png')`,
            backgroundSize: 'cover'
          }} />

          <Grid container spacing={2} alignItems="center">
            <Grid item>
              <Avatar
                sx={{
                  width: 60,
                  height: 60,
                  bgcolor: 'white',
                  color: '#3f51b5',
                  boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                }}
              >
                <AssessmentIcon fontSize="large" />
              </Avatar>
            </Grid>
            <Grid item xs>
              <Typography variant="h4" fontWeight="500">
                System Statistics
              </Typography>
              <Typography variant="subtitle1" sx={{ opacity: 0.9, mt: 0.5 }}>
                Loading system data...
              </Typography>
            </Grid>
          </Grid>
        </Paper>

        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 8 }}>
          <CircularProgress size={60} thickness={4} />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Loading System Statistics
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Please wait while we gather the latest data...
          </Typography>
        </Box>
      </Box>
    );
  }

  if (!stats) {
    return (
      <Box sx={{ p: 3 }}>
        <Paper
          elevation={0}
          sx={{
            p: 3,
            mb: 4,
            borderRadius: 2,
            background: 'linear-gradient(to right, #f44336, #e57373)',
            color: 'white'
          }}
        >
          <Grid container spacing={2} alignItems="center">
            <Grid item>
              <Avatar
                sx={{
                  width: 60,
                  height: 60,
                  bgcolor: 'white',
                  color: '#f44336',
                  boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                }}
              >
                <ErrorIcon fontSize="large" />
              </Avatar>
            </Grid>
            <Grid item xs>
              <Typography variant="h4" fontWeight="500">
                System Statistics
              </Typography>
              <Typography variant="subtitle1" sx={{ opacity: 0.9, mt: 0.5 }}>
                Failed to load data
              </Typography>
            </Grid>
            <Grid item>
              <Button
                variant="contained"
                startIcon={<RefreshIcon />}
                onClick={handleRefresh}
                sx={{
                  bgcolor: 'white',
                  color: '#f44336',
                  '&:hover': {
                    bgcolor: 'rgba(255,255,255,0.9)'
                  }
                }}
              >
                Retry
              </Button>
            </Grid>
          </Grid>
        </Paper>

        <Alert
          severity="error"
          sx={{
            borderRadius: 2,
            boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
          }}
        >
          Failed to load system statistics. Please try again later.
        </Alert>
      </Box>
    );
  }

  // Prepare data for gender distribution chart
  const genderData = {
    labels: ['Male', 'Female', 'Other'],
    datasets: [
      {
        data: stats.patientsByGender ? [
          stats.patientsByGender.male || 0,
          stats.patientsByGender.female || 0,
          stats.patientsByGender.other || 0
        ] : [0, 0, 0],
        backgroundColor: ['#4dabf5', '#f06292', '#9575cd'],
        borderWidth: 1,
        borderRadius: 4
      }
    ]
  };

  // Prepare data for visits chart
  const visitsChartData = {
    labels: stats.visitsPerMonth ? stats.visitsPerMonth.map(item => item.month) : [],
    datasets: [
      {
        label: 'Patient Visits',
        data: stats.visitsPerMonth ? stats.visitsPerMonth.map(item => item.count) : [],
        borderColor: '#3f51b5',
        backgroundColor: 'rgba(63, 81, 181, 0.1)',
        tension: 0.3,
        fill: true,
        pointBackgroundColor: '#3f51b5',
        pointBorderColor: '#fff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6
      }
    ]
  };

  // Prepare data for age distribution chart by gender
  const ageChartData = {
    labels: stats.patientsByAge ? stats.patientsByAge.map(item => item.range) : ['No Data'],
    datasets: [
      {
        label: 'Male',
        data: stats.patientsByAge ? stats.patientsByAge.map(item => item.male) : [0],
        backgroundColor: '#4dabf5', // Blue for male
        borderWidth: 1,
        borderRadius: {
          topLeft: 4,
          topRight: 4,
          bottomLeft: 0,
          bottomRight: 0
        },
        maxBarThickness: 50
      },
      {
        label: 'Female',
        data: stats.patientsByAge ? stats.patientsByAge.map(item => item.female) : [0],
        backgroundColor: '#f06292', // Pink for female
        borderWidth: 1,
        borderRadius: 0,
        maxBarThickness: 50
      },
      {
        label: 'Other',
        data: stats.patientsByAge ? stats.patientsByAge.map(item => item.other) : [0],
        backgroundColor: '#9575cd', // Purple for other
        borderWidth: 1,
        borderRadius: {
          topLeft: 0,
          topRight: 0,
          bottomLeft: 4,
          bottomRight: 4
        },
        maxBarThickness: 50
      }
    ]
  };

  // We're now using inline data for the conditions chart in the Health Metrics tab

  return (
    <Box sx={{ p: 3 }}>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 4,
          borderRadius: 2,
          background: 'linear-gradient(to right, #3f51b5, #5c6bc0)',
          color: 'white',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        <Box sx={{
          position: 'absolute',
          top: 0,
          right: 0,
          width: '30%',
          height: '100%',
          opacity: 0.1,
          background: `url('https://www.transparenttextures.com/patterns/cubes.png')`,
          backgroundSize: 'cover'
        }} />

        <Grid container spacing={2} alignItems="center">
          <Grid item>
            <Avatar
              sx={{
                width: 60,
                height: 60,
                bgcolor: 'white',
                color: '#3f51b5',
                boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
              }}
            >
              <AssessmentIcon fontSize="large" />
            </Avatar>
          </Grid>
          <Grid item xs>
            <Typography variant="h4" fontWeight="500">
              System Statistics
            </Typography>
            <Typography variant="subtitle1" sx={{ opacity: 0.9, mt: 0.5 }}>
              Comprehensive analytics and system health monitoring
            </Typography>
          </Grid>
          <Grid item>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              {lastUpdated && (
                <Chip
                  icon={<InfoIcon />}
                  label={`Last updated: ${lastUpdated}`}
                  sx={{
                    bgcolor: 'rgba(255,255,255,0.15)',
                    color: 'white',
                    '& .MuiChip-icon': { color: 'white' }
                  }}
                />
              )}
              <Tooltip title="Refresh statistics">
                <IconButton
                  color="inherit"
                  onClick={handleRefresh}
                  disabled={refreshing}
                  sx={{
                    bgcolor: 'rgba(255,255,255,0.1)',
                    '&:hover': { bgcolor: 'rgba(255,255,255,0.2)' }
                  }}
                >
                  {refreshing ? <CircularProgress size={24} color="inherit" /> : <RefreshIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {error && (
        <Alert
          severity="error"
          sx={{
            mb: 3,
            borderRadius: 2,
            boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
          }}
        >
          {error}
        </Alert>
      )}

      {/* Overview Cards */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" gutterBottom sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          mb: 2,
          '&::after': {
            content: '""',
            display: 'block',
            height: '2px',
            background: 'linear-gradient(to right, #3f51b5, transparent)',
            flexGrow: 1,
            ml: 2
          }
        }}>
          <EqualizerIcon sx={{ mr: 1 }} />
          Overview
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{
              borderRadius: 2,
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
              transition: 'transform 0.3s ease',
              '&:hover': { transform: 'translateY(-5px)' }
            }}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="subtitle1" color="text.secondary">Total Users</Typography>
                  <Avatar sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1), color: theme.palette.primary.main }}>
                    <UserIcon />
                  </Avatar>
                </Box>
                <Typography variant="h4" fontWeight="500">{stats.totalUsers}</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <Chip
                    size="small"
                    icon={<TrendingUpIcon fontSize="small" />}
                    label={`${stats.activeUsers} active`}
                    color="primary"
                    variant="outlined"
                    sx={{ borderRadius: 1, fontWeight: 500 }}
                  />
                  <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                    in the last 30 days
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={(stats.activeUsers / stats.totalUsers) * 100}
                  sx={{ mt: 2, height: 6, borderRadius: 3 }}
                />
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{
              borderRadius: 2,
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
              transition: 'transform 0.3s ease',
              '&:hover': { transform: 'translateY(-5px)' }
            }}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="subtitle1" color="text.secondary">Doctors</Typography>
                  <Avatar sx={{ bgcolor: alpha(theme.palette.success.main, 0.1), color: theme.palette.success.main }}>
                    <DoctorIcon />
                  </Avatar>
                </Box>
                <Typography variant="h4" fontWeight="500">{stats.totalDoctors}</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <Chip
                    size="small"
                    icon={<PieChartIcon fontSize="small" />}
                    label="Medical professionals"
                    color="success"
                    variant="outlined"
                    sx={{ borderRadius: 1, fontWeight: 500 }}
                  />
                </Box>
                <Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}>
                  <Box sx={{
                    width: 8,
                    height: 8,
                    borderRadius: '50%',
                    bgcolor: theme.palette.success.main,
                    mr: 1
                  }} />
                  <Typography variant="caption" color="text.secondary">
                    {Math.round((stats.totalDoctors / stats.totalPatients) * 100)}% doctor-to-patient ratio
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{
              borderRadius: 2,
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
              transition: 'transform 0.3s ease',
              '&:hover': { transform: 'translateY(-5px)' }
            }}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="subtitle1" color="text.secondary">Patients</Typography>
                  <Avatar sx={{ bgcolor: alpha(theme.palette.warning.main, 0.1), color: theme.palette.warning.main }}>
                    <PatientIcon />
                  </Avatar>
                </Box>
                <Typography variant="h4" fontWeight="500">{stats.totalPatients}</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <Chip
                    size="small"
                    icon={<BarChartIcon fontSize="small" />}
                    label="Registered patients"
                    color="warning"
                    variant="outlined"
                    sx={{ borderRadius: 1, fontWeight: 500 }}
                  />
                </Box>
                <Box sx={{
                  mt: 2,
                  display: 'flex',
                  justifyContent: 'space-between',
                  bgcolor: alpha(theme.palette.warning.main, 0.1),
                  p: 0.5,
                  borderRadius: 1
                }}>
                  <Typography variant="caption" sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box component="span" sx={{
                      width: 8,
                      height: 8,
                      borderRadius: '50%',
                      bgcolor: '#2196f3',
                      display: 'inline-block',
                      mr: 0.5
                    }} />
                    M: {stats.patientsByGender.male}
                  </Typography>
                  <Typography variant="caption" sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box component="span" sx={{
                      width: 8,
                      height: 8,
                      borderRadius: '50%',
                      bgcolor: '#e91e63',
                      display: 'inline-block',
                      mr: 0.5
                    }} />
                    F: {stats.patientsByGender.female}
                  </Typography>
                  <Typography variant="caption" sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box component="span" sx={{
                      width: 8,
                      height: 8,
                      borderRadius: '50%',
                      bgcolor: '#9c27b0',
                      display: 'inline-block',
                      mr: 0.5
                    }} />
                    O: {stats.patientsByGender.other}
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <Card sx={{
              borderRadius: 2,
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
              transition: 'transform 0.3s ease',
              '&:hover': { transform: 'translateY(-5px)' }
            }}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="subtitle1" color="text.secondary">Total Visits</Typography>
                  <Avatar sx={{ bgcolor: alpha(theme.palette.info.main, 0.1), color: theme.palette.info.main }}>
                    <VisitIcon />
                  </Avatar>
                </Box>
                <Typography variant="h4" fontWeight="500">{stats.totalVisits}</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <Chip
                    size="small"
                    icon={<TimelineIcon fontSize="small" />}
                    label="Patient visits"
                    color="info"
                    variant="outlined"
                    sx={{ borderRadius: 1, fontWeight: 500 }}
                  />
                </Box>
                <Box sx={{ mt: 2 }}>
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center' }}>
                    <TrendingUpIcon fontSize="small" sx={{ mr: 0.5, color: theme.palette.success.main }} />
                    Avg {Math.round(stats.totalVisits / 12)} visits per month
                  </Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>

      {/* Detailed Statistics */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" gutterBottom sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          mb: 2,
          '&::after': {
            content: '""',
            display: 'block',
            height: '2px',
            background: 'linear-gradient(to right, #3f51b5, transparent)',
            flexGrow: 1,
            ml: 2
          }
        }}>
          <AssessmentIcon sx={{ mr: 1 }} />
          Detailed Analytics
        </Typography>

        <Paper
          elevation={0}
          sx={{
            borderRadius: 2,
            boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
            overflow: 'hidden'
          }}
        >
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            aria-label="statistics tabs"
            variant="scrollable"
            scrollButtons="auto"
            sx={{
              borderBottom: '1px solid #eee',
              '& .MuiTab-root': {
                minHeight: 64,
                py: 2,
                px: 3,
                fontWeight: 500,
                transition: 'all 0.2s ease',
                '&:hover': {
                  bgcolor: 'rgba(0, 0, 0, 0.03)'
                }
              },
              '& .Mui-selected': {
                fontWeight: 600,
                color: theme.palette.primary.main,
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  bottom: 0,
                  left: 0,
                  width: '100%',
                  height: 3,
                  bgcolor: theme.palette.primary.main,
                  borderTopLeftRadius: 3,
                  borderTopRightRadius: 3
                }
              }
            }}
          >
            <Tab
              label="Demographics"
              icon={<PieChartIcon />}
              iconPosition="start"
            />
            <Tab
              label="Activity"
              icon={<TimelineIcon />}
              iconPosition="start"
            />
            <Tab
              label="Provider Analysis"
              icon={<DoctorIcon />}
              iconPosition="start"
            />
            <Tab
              label="Health Metrics"
              icon={<HeartIcon />}
              iconPosition="start"
            />
          </Tabs>

        {/* Demographics Tab */}
        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Gender Distribution
                  </Typography>
                  <Box height={300}>
                    <Pie
                      data={genderData}
                      options={{
                        maintainAspectRatio: false,
                        plugins: {
                          legend: {
                            position: 'right',
                            labels: {
                              boxWidth: 15,
                              padding: 15
                            }
                          },
                          tooltip: {
                            callbacks: {
                              label: function(context) {
                                const total = context.dataset.data.reduce((sum, val) => sum + (val as number), 0);
                                const value = context.raw as number;
                                const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                return `${context.label}: ${value} (${percentage}%)`;
                              }
                            }
                          }
                        }
                      }}
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Age Distribution by Gender
                  </Typography>
                  <Box height={300}>
                    <Bar
                      data={ageChartData}
                      options={{
                        maintainAspectRatio: false,
                        responsive: true,
                        scales: {
                          y: {
                            stacked: true,
                            beginAtZero: true,
                            min: 0,
                            suggestedMax: 50,
                            grid: {
                              display: true
                            },
                            border: {
                              display: false
                            },
                            ticks: {
                              precision: 0
                            }
                          },
                          x: {
                            stacked: true,
                            grid: {
                              display: false
                            },
                            border: {
                              display: false
                            }
                          }
                        },
                        plugins: {
                          legend: {
                            position: 'top',
                            labels: {
                              usePointStyle: true,
                              boxWidth: 10,
                              padding: 15
                            }
                          },
                          tooltip: {
                            callbacks: {
                              label: function(context) {
                                const datasetLabel = context.dataset.label || '';
                                const value = context.parsed.y;
                                const total = context.chart.data.datasets.reduce((sum, dataset, i) => {
                                  return sum + (dataset.data[context.dataIndex] as number);
                                }, 0);
                                const percentage = Math.round((value / total) * 100);
                                return `${datasetLabel}: ${value} (${percentage}%)`;
                              },
                              footer: function(tooltipItems) {
                                const total = tooltipItems.reduce((sum, tooltipItem) => {
                                  return sum + (tooltipItem.parsed.y as number);
                                }, 0);
                                return `Total: ${total}`;
                              }
                            }
                          }
                        }
                      }}
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Activity Tab */}
        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Patient Visits by Month
                  </Typography>
                  <Box height={400}>
                    <Line
                      data={visitsChartData}
                      options={{
                        maintainAspectRatio: false,
                        scales: {
                          y: {
                            beginAtZero: true
                          }
                        },
                        plugins: {
                          legend: {
                            position: 'top',
                          },
                          tooltip: {
                            mode: 'index',
                            intersect: false,
                          }
                        }
                      }}
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={4}>
              <Card sx={{ height: '100%' }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Monthly Trend
                  </Typography>

                  <Box sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    height: '100%',
                    py: 4
                  }}>
                    {stats.visitsTrend ? (
                      <>
                        <Box sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mb: 2
                        }}>
                          {stats.visitsTrend.percentChange > 0 ? (
                            <TrendingUpIcon
                              sx={{
                                fontSize: 48,
                                color: 'success.main',
                                mr: 1
                              }}
                            />
                          ) : (
                            <TrendingDownIcon
                              sx={{
                                fontSize: 48,
                                color: 'error.main',
                                mr: 1
                              }}
                            />
                          )}
                          <Typography
                            variant="h3"
                            color={stats.visitsTrend.percentChange > 0 ? 'success.main' : 'error.main'}
                          >
                            {stats.visitsTrend.percentChange > 0 ? '+' : ''}{stats.visitsTrend.percentChange}%
                          </Typography>
                        </Box>

                        <Typography variant="body2" color="text.secondary" align="center" sx={{ mb: 3 }}>
                          Change from previous month
                        </Typography>

                        <Grid container spacing={2}>
                          <Grid item xs={6}>
                            <Paper
                              elevation={0}
                              sx={{
                                p: 2,
                                bgcolor: alpha(theme.palette.primary.main, 0.1),
                                borderRadius: 2,
                                textAlign: 'center'
                              }}
                            >
                              <Typography variant="h5" color="primary.main">
                                {stats.visitsTrend.currentMonth}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Current Month
                              </Typography>
                            </Paper>
                          </Grid>
                          <Grid item xs={6}>
                            <Paper
                              elevation={0}
                              sx={{
                                p: 2,
                                bgcolor: alpha(theme.palette.secondary.main, 0.1),
                                borderRadius: 2,
                                textAlign: 'center'
                              }}
                            >
                              <Typography variant="h5" color="secondary.main">
                                {stats.visitsTrend.previousMonth}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                Previous Month
                              </Typography>
                            </Paper>
                          </Grid>
                        </Grid>

                        <Box sx={{ mt: 4, width: '100%' }}>
                          <Typography variant="body2" color="text.secondary" gutterBottom>
                            Monthly Progress
                          </Typography>
                          <LinearProgress
                            variant="determinate"
                            value={Math.min(100, (stats.visitsTrend.currentMonth / stats.visitsTrend.previousMonth) * 100 - 100)}
                            color={stats.visitsTrend.percentChange > 0 ? "success" : "error"}
                            sx={{ height: 10, borderRadius: 5 }}
                          />
                        </Box>
                      </>
                    ) : (
                      <Box sx={{ textAlign: 'center', py: 4 }}>
                        <Typography variant="h6" color="text.secondary">
                          Monthly trend data not available
                        </Typography>
                        <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                          Please check the API connection or try again later.
                        </Typography>
                      </Box>
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Provider Analysis Tab */}
        <TabPanel value={tabValue} index={2}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Top Doctors by Patient Count
                  </Typography>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Rank</TableCell>
                          <TableCell>Doctor Name</TableCell>
                          <TableCell>Specialty</TableCell>
                          <TableCell align="right">Patient Count</TableCell>
                          <TableCell align="right">Total Visits</TableCell>
                          <TableCell align="right">Recent Visits (30d)</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {stats.topDoctors ? (
                          stats.topDoctors.map((doctor, index) => (
                            <TableRow key={index}>
                              <TableCell>{index + 1}</TableCell>
                              <TableCell>{doctor.name}</TableCell>
                              <TableCell>{doctor.specialty}</TableCell>
                              <TableCell align="right">
                                <Chip
                                  label={doctor.patientCount}
                                  color="primary"
                                  size="small"
                                  variant="outlined"
                                />
                              </TableCell>
                              <TableCell align="right">{doctor.visitCount}</TableCell>
                              <TableCell align="right">
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                                  {doctor.recentVisits}
                                  <Box
                                    sx={{
                                      ml: 1,
                                      width: 40,
                                      height: 8,
                                      borderRadius: 4,
                                      bgcolor: theme.palette.grey[200],
                                      overflow: 'hidden'
                                    }}
                                  >
                                    <Box
                                      sx={{
                                        width: `${Math.min(100, (doctor.recentVisits / doctor.visitCount) * 100)}%`,
                                        height: '100%',
                                        bgcolor: theme.palette.primary.main
                                      }}
                                    />
                                  </Box>
                                </Box>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={6} align="center">No data available</TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Doctor Efficiency (Visits per Patient)
                  </Typography>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Doctor Name</TableCell>
                          <TableCell align="right">Unique Patients</TableCell>
                          <TableCell align="right">Total Visits</TableCell>
                          <TableCell align="right">Visits per Patient</TableCell>
                          <TableCell align="right">Efficiency Rating</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {stats.doctorEfficiency ? (
                          stats.doctorEfficiency.map((doctor, index) => (
                            <TableRow key={index}>
                              <TableCell>{doctor.name}</TableCell>
                              <TableCell align="right">{doctor.uniquePatients}</TableCell>
                              <TableCell align="right">{doctor.totalVisits}</TableCell>
                              <TableCell align="right">{doctor.visitsPerPatient.toFixed(1)}</TableCell>
                              <TableCell align="right">
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                                  <LinearProgress
                                    variant="determinate"
                                    value={Math.min(100, (doctor.visitsPerPatient / 5) * 100)}
                                    sx={{
                                      width: 100,
                                      height: 8,
                                      borderRadius: 4,
                                      mr: 1,
                                      bgcolor: alpha(theme.palette.success.main, 0.2),
                                      '& .MuiLinearProgress-bar': {
                                        bgcolor: theme.palette.success.main
                                      }
                                    }}
                                  />
                                  <Chip
                                    label={doctor.visitsPerPatient >= 4 ? 'Excellent' :
                                           doctor.visitsPerPatient >= 3 ? 'Good' :
                                           doctor.visitsPerPatient >= 2 ? 'Average' : 'Below Average'}
                                    color={doctor.visitsPerPatient >= 4 ? 'success' :
                                           doctor.visitsPerPatient >= 3 ? 'primary' :
                                           doctor.visitsPerPatient >= 2 ? 'warning' : 'error'}
                                    size="small"
                                  />
                                </Box>
                              </TableCell>
                            </TableRow>
                          ))
                        ) : (
                          <TableRow>
                            <TableCell colSpan={5} align="center">No data available</TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </TableContainer>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Health Metrics Tab */}
        <TabPanel value={tabValue} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Common Medical Conditions
                  </Typography>
                  <Box height={350}>
                    <Bar
                      data={{
                        labels: stats.commonConditions ?
                          stats.commonConditions.map(item => `${item.name} (${item.percentage}%)`) :
                          ['No Data'],
                        datasets: [
                          {
                            label: 'Cases',
                            data: stats.commonConditions ?
                              stats.commonConditions.map(item => item.count) :
                              [0],
                            backgroundColor: [
                              '#26c6da',
                              '#29b6f6',
                              '#26a69a',
                              '#66bb6a',
                              '#9ccc65',
                              '#d4e157',
                              '#ffee58',
                              '#ffca28',
                              '#ffa726',
                              '#ff7043'
                            ],
                            borderWidth: 1
                          }
                        ]
                      }}
                      options={{
                        maintainAspectRatio: false,
                        indexAxis: 'y',
                        scales: {
                          x: {
                            beginAtZero: true
                          }
                        }
                      }}
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Blood Pressure Categories
                  </Typography>
                  <Box height={350}>
                    <Pie
                      data={{
                        labels: stats.bpCategories ? [
                          `Normal (${stats.bpCategories.normal.percentage}%)`,
                          `Elevated (${stats.bpCategories.elevated.percentage}%)`,
                          `Stage 1 Hypertension (${stats.bpCategories.hypertensionStage1.percentage}%)`,
                          `Stage 2 Hypertension (${stats.bpCategories.hypertensionStage2.percentage}%)`
                        ] : ['No Data'],
                        datasets: [
                          {
                            data: stats.bpCategories ? [
                              stats.bpCategories.normal.count,
                              stats.bpCategories.elevated.count,
                              stats.bpCategories.hypertensionStage1.count,
                              stats.bpCategories.hypertensionStage2.count
                            ] : [1],
                            backgroundColor: [
                              '#4caf50', // Green for normal
                              '#ffeb3b', // Yellow for elevated
                              '#ff9800', // Orange for stage 1
                              '#f44336'  // Red for stage 2
                            ],
                            borderWidth: 1
                          }
                        ]
                      }}
                      options={{
                        maintainAspectRatio: false,
                        plugins: {
                          legend: {
                            position: 'right',
                          }
                        }
                      }}
                    />
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Average Vital Signs
                  </Typography>
                  <Box sx={{ p: 2 }}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6}>
                        <Card elevation={3} sx={{ p: 2, mb: 2, bgcolor: '#e3f2fd' }}>
                          <Typography variant="subtitle1" gutterBottom align="center">
                            Blood Pressure
                          </Typography>
                          <Typography variant="h4" align="center">
                            {stats.averageVitals ? `${stats.averageVitals.systolic}/${stats.averageVitals.diastolic}` : 'N/A'}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" align="center">
                            mmHg
                          </Typography>
                        </Card>
                      </Grid>

                      <Grid item xs={12} sm={6}>
                        <Card elevation={3} sx={{ p: 2, mb: 2, bgcolor: '#e8f5e9' }}>
                          <Typography variant="subtitle1" gutterBottom align="center">
                            Heart Rate
                          </Typography>
                          <Typography variant="h4" align="center">
                            {stats.averageVitals?.heartRate || 'N/A'}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" align="center">
                            bpm
                          </Typography>
                        </Card>
                      </Grid>

                      <Grid item xs={12} sm={6}>
                        <Card elevation={3} sx={{ p: 2, bgcolor: '#f3e5f5' }}>
                          <Typography variant="subtitle1" gutterBottom align="center">
                            Oxygen Saturation
                          </Typography>
                          <Typography variant="h4" align="center">
                            {stats.averageVitals?.oxygenLevel ? `${stats.averageVitals.oxygenLevel}%` : 'N/A'}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" align="center">
                            SpO2
                          </Typography>
                        </Card>
                      </Grid>

                      <Grid item xs={12} sm={6}>
                        <Card elevation={3} sx={{ p: 2, bgcolor: '#fff8e1' }}>
                          <Typography variant="subtitle1" gutterBottom align="center">
                            Temperature
                          </Typography>
                          <Typography variant="h4" align="center">
                            {stats.averageVitals?.temperature ? `${stats.averageVitals.temperature}°C` : 'N/A'}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" align="center">
                            {stats.averageVitals?.temperature ?
                              `${(parseFloat(stats.averageVitals.temperature) * 9/5 + 32).toFixed(1)}°F` : ''}
                          </Typography>
                        </Card>
                      </Grid>
                    </Grid>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Lab Values
                  </Typography>
                  <Box sx={{ p: 2 }}>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <Card elevation={3} sx={{ p: 2, mb: 2, bgcolor: '#e8f5e9' }}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Box>
                              <Typography variant="subtitle1" gutterBottom>
                                Blood Glucose
                              </Typography>
                              <Typography variant="h4">
                                {stats.averageLabValues?.bloodGlucose || 'N/A'}
                              </Typography>
                              <Typography variant="body2" color="text.secondary">
                                mg/dL
                              </Typography>
                            </Box>
                            <Box sx={{ width: '50%' }}>
                              <Typography variant="caption" color="text.secondary" gutterBottom>
                                Distribution
                              </Typography>
                              <Box sx={{ display: 'flex', mb: 1 }}>
                                {stats.glucoseCategories ? (
                                  <>
                                    <Box sx={{ width: `${stats.glucoseCategories.low.percentage}%`, height: 8, bgcolor: '#ff9800', borderTopLeftRadius: 4, borderBottomLeftRadius: 4 }} />
                                    <Box sx={{ width: `${stats.glucoseCategories.normal.percentage}%`, height: 8, bgcolor: '#4caf50' }} />
                                    <Box sx={{ width: `${stats.glucoseCategories.prediabetes.percentage}%`, height: 8, bgcolor: '#ff9800' }} />
                                    <Box sx={{ width: `${stats.glucoseCategories.diabetes.percentage}%`, height: 8, bgcolor: '#f44336', borderTopRightRadius: 4, borderBottomRightRadius: 4 }} />
                                  </>
                                ) : (
                                  <Box sx={{ width: '100%', height: 8, bgcolor: '#e0e0e0', borderRadius: 4 }} />
                                )}
                              </Box>
                              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                <Typography variant="caption">Low</Typography>
                                <Typography variant="caption">Normal</Typography>
                                <Typography variant="caption">Pre-diabetic</Typography>
                                <Typography variant="caption">Diabetic</Typography>
                              </Box>
                            </Box>
                          </Box>
                        </Card>
                      </Grid>

                      <Grid item xs={12} sm={6}>
                        <Card elevation={3} sx={{ p: 2, bgcolor: '#e3f2fd' }}>
                          <Typography variant="subtitle1" gutterBottom align="center">
                            Total Cholesterol
                          </Typography>
                          <Typography variant="h4" align="center">
                            {stats.averageLabValues?.cholesterol || 'N/A'}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" align="center">
                            mg/dL
                          </Typography>
                          <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                            {stats.averageLabValues?.cholesterol ? (
                              <Chip
                                label={stats.averageLabValues.cholesterol < 200 ? "Optimal" :
                                      stats.averageLabValues.cholesterol < 240 ? "Borderline" : "High"}
                                color={stats.averageLabValues.cholesterol < 200 ? "success" :
                                      stats.averageLabValues.cholesterol < 240 ? "warning" : "error"}
                                size="small"
                              />
                            ) : (
                              <Chip label="No Data" color="default" size="small" />
                            )}
                          </Box>
                        </Card>
                      </Grid>

                      <Grid item xs={12} sm={6}>
                        <Card elevation={3} sx={{ p: 2, bgcolor: '#f3e5f5' }}>
                          <Typography variant="subtitle1" gutterBottom align="center">
                            HbA1c
                          </Typography>
                          <Typography variant="h4" align="center">
                            {stats.averageLabValues?.hba1c ? `${stats.averageLabValues.hba1c}%` : 'N/A'}
                          </Typography>
                          <Typography variant="body2" color="text.secondary" align="center">
                            Glycated Hemoglobin
                          </Typography>
                          <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                            {stats.averageLabValues?.hba1c ? (
                              <Chip
                                label={parseFloat(stats.averageLabValues.hba1c) < 5.7 ? "Normal" :
                                      parseFloat(stats.averageLabValues.hba1c) < 6.5 ? "Prediabetes" : "Diabetes"}
                                color={parseFloat(stats.averageLabValues.hba1c) < 5.7 ? "success" :
                                      parseFloat(stats.averageLabValues.hba1c) < 6.5 ? "warning" : "error"}
                                size="small"
                              />
                            ) : (
                              <Chip label="No Data" color="default" size="small" />
                            )}
                          </Box>
                        </Card>
                      </Grid>
                    </Grid>
                  </Box>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Health Insights
                  </Typography>
                  <Typography variant="body1" paragraph>
                    Based on the data, our system shows that the most common condition treated is
                    <strong> {stats.commonConditions?.[0]?.name || 'Hypertension'}</strong> with
                    <strong> {stats.commonConditions?.[0]?.count || 0}</strong> cases
                    ({stats.commonConditions?.[0]?.percentage || 0}% of all visits).
                  </Typography>
                  {stats.averageVitals && (
                    <Typography variant="body1" paragraph>
                      The average vital signs across all patients are within normal ranges,
                      with blood pressure averaging {stats.averageVitals.systolic}/{stats.averageVitals.diastolic} mmHg,
                      heart rate at {stats.averageVitals.heartRate} bpm
                      {stats.averageVitals.temperature && `, and temperature at ${stats.averageVitals.temperature}°C`}.
                    </Typography>
                  )}
                  {stats.bpCategories && (
                    <Typography variant="body1" paragraph>
                      Blood pressure distribution shows that {stats.bpCategories.normal.percentage}% of patients have normal blood pressure,
                      while {stats.bpCategories.hypertensionStage1.percentage + stats.bpCategories.hypertensionStage2.percentage}%
                      show some form of hypertension.
                    </Typography>
                  )}
                  <Typography variant="body1">
                    This data can help inform resource allocation, staff training, and preventive care initiatives,
                    particularly for conditions like hypertension and diabetes which require ongoing management.
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>
        </Paper>
      </Box>

      {/* System Health */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" gutterBottom sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 1,
          mb: 2,
          '&::after': {
            content: '""',
            display: 'block',
            height: '2px',
            background: 'linear-gradient(to right, #3f51b5, transparent)',
            flexGrow: 1,
            ml: 2
          }
        }}>
          <SpeedIcon sx={{ mr: 1 }} />
          System Health
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Card sx={{
              borderRadius: 2,
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
              transition: 'transform 0.3s ease',
              '&:hover': { transform: 'translateY(-5px)' },
              height: '100%'
            }}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="h6" fontWeight="500">System Activity</Typography>
                  <Avatar sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1), color: theme.palette.primary.main }}>
                    <ActivityIcon />
                  </Avatar>
                </Box>

                <Box sx={{ mb: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2" color="text.secondary">Active Sessions</Typography>
                    <Typography variant="body2" fontWeight="600">42</Typography>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={42}
                    sx={{
                      height: 8,
                      borderRadius: 4,
                      bgcolor: alpha(theme.palette.primary.main, 0.1),
                      '& .MuiLinearProgress-bar': {
                        borderRadius: 4,
                        bgcolor: theme.palette.primary.main
                      }
                    }}
                  />
                </Box>

                <Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body2" color="text.secondary">Response Time</Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Box
                        component="span"
                        sx={{
                          width: 8,
                          height: 8,
                          borderRadius: '50%',
                          bgcolor: theme.palette.success.main,
                          display: 'inline-block',
                          mr: 0.5
                        }}
                      />
                      <Typography variant="body2" fontWeight="600">245ms</Typography>
                    </Box>
                  </Box>
                  <LinearProgress
                    variant="determinate"
                    value={25}
                    color="success"
                    sx={{
                      height: 8,
                      borderRadius: 4,
                      bgcolor: alpha(theme.palette.success.main, 0.1),
                      '& .MuiLinearProgress-bar': {
                        borderRadius: 4
                      }
                    }}
                  />
                </Box>

                <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center' }}>
                  <Chip
                    icon={<CheckCircleIcon />}
                    label="All Systems Operational"
                    color="success"
                    sx={{ borderRadius: 2, fontWeight: 500, px: 1 }}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card sx={{
              borderRadius: 2,
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
              transition: 'transform 0.3s ease',
              '&:hover': { transform: 'translateY(-5px)' },
              height: '100%'
            }}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="h6" fontWeight="500">Database Status</Typography>
                  <Avatar sx={{ bgcolor: alpha(theme.palette.info.main, 0.1), color: theme.palette.info.main }}>
                    <DatabaseIcon />
                  </Avatar>
                </Box>

                <Box sx={{
                  p: 2,
                  borderRadius: 2,
                  bgcolor: alpha(theme.palette.success.main, 0.1),
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1,
                  mb: 3
                }}>
                  <CheckCircleIcon color="success" />
                  <Typography variant="body1" fontWeight="600" color="success.main">
                    Healthy
                  </Typography>
                </Box>

                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Storage Usage
                </Typography>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                  <Box sx={{ flexGrow: 1 }}>
                    <LinearProgress
                      variant="determinate"
                      value={32}
                      color="info"
                      sx={{
                        height: 10,
                        borderRadius: 5,
                        bgcolor: alpha(theme.palette.info.main, 0.1),
                        '& .MuiLinearProgress-bar': {
                          borderRadius: 5
                        }
                      }}
                    />
                  </Box>
                  <Typography variant="body2" fontWeight="600">
                    32%
                  </Typography>
                </Box>

                <Typography variant="caption" color="text.secondary">
                  3.2GB used of 10GB total
                </Typography>

                <Divider sx={{ my: 2 }} />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2" color="text.secondary">Last Backup</Typography>
                  <Typography variant="body2" fontWeight="500">Today, 03:45 AM</Typography>
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="body2" color="text.secondary">Uptime</Typography>
                  <Typography variant="body2" fontWeight="500">99.98%</Typography>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card sx={{
              borderRadius: 2,
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
              transition: 'transform 0.3s ease',
              '&:hover': { transform: 'translateY(-5px)' },
              height: '100%'
            }}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="h6" fontWeight="500">Organization</Typography>
                  <Avatar sx={{ bgcolor: alpha(theme.palette.warning.main, 0.1), color: theme.palette.warning.main }}>
                    <OrgIcon />
                  </Avatar>
                </Box>

                <Grid container spacing={2} sx={{ mb: 2 }}>
                  <Grid item xs={6}>
                    <Paper
                      elevation={0}
                      sx={{
                        p: 2,
                        textAlign: 'center',
                        borderRadius: 2,
                        bgcolor: alpha(theme.palette.primary.main, 0.05)
                      }}
                    >
                      <Typography variant="h4" fontWeight="500" color="primary.main">8</Typography>
                      <Typography variant="body2" color="text.secondary">Departments</Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6}>
                    <Paper
                      elevation={0}
                      sx={{
                        p: 2,
                        textAlign: 'center',
                        borderRadius: 2,
                        bgcolor: alpha(theme.palette.warning.main, 0.05)
                      }}
                    >
                      <Typography variant="h4" fontWeight="500" color="warning.main">3</Typography>
                      <Typography variant="body2" color="text.secondary">Locations</Typography>
                    </Paper>
                  </Grid>
                </Grid>

                <Divider sx={{ my: 2 }} />

                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Department Distribution
                </Typography>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <Box
                    sx={{
                      width: 10,
                      height: 10,
                      borderRadius: '50%',
                      bgcolor: theme.palette.primary.main
                    }}
                  />
                  <Typography variant="body2">General Medicine</Typography>
                  <Box
                    sx={{
                      ml: 'auto',
                      px: 1,
                      py: 0.25,
                      borderRadius: 1,
                      bgcolor: alpha(theme.palette.primary.main, 0.1),
                      fontSize: '0.75rem',
                      fontWeight: 500
                    }}
                  >
                    35%
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                  <Box
                    sx={{
                      width: 10,
                      height: 10,
                      borderRadius: '50%',
                      bgcolor: theme.palette.success.main
                    }}
                  />
                  <Typography variant="body2">Pediatrics</Typography>
                  <Box
                    sx={{
                      ml: 'auto',
                      px: 1,
                      py: 0.25,
                      borderRadius: 1,
                      bgcolor: alpha(theme.palette.success.main, 0.1),
                      fontSize: '0.75rem',
                      fontWeight: 500
                    }}
                  >
                    25%
                  </Box>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Box
                    sx={{
                      width: 10,
                      height: 10,
                      borderRadius: '50%',
                      bgcolor: theme.palette.warning.main
                    }}
                  />
                  <Typography variant="body2">Other Departments</Typography>
                  <Box
                    sx={{
                      ml: 'auto',
                      px: 1,
                      py: 0.25,
                      borderRadius: 1,
                      bgcolor: alpha(theme.palette.warning.main, 0.1),
                      fontSize: '0.75rem',
                      fontWeight: 500
                    }}
                  >
                    40%
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
};

export default SystemStatistics;