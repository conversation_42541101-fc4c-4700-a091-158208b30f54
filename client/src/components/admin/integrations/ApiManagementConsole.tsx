import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Button,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Divider,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  useTheme,
  alpha
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Add as AddIcon,
  VpnKey as ApiKeyIcon,
  Timeline as TimelineIcon,
  ContentCopy as CopyIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { 
  getApiKeys, 
  createApiKey,
  ApiKey,
  getApiKeyUsage,
  ApiUsageStats
} from '../../../services/integrationService';
import ApiKeyCard from './shared/ApiKeyCard';
import ApiUsageChart from './shared/ApiUsageChart';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`api-tabpanel-${index}`}
      aria-labelledby={`api-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

const ApiManagementConsole: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [selectedKeyId, setSelectedKeyId] = useState<number | null>(null);
  const [usageStats, setUsageStats] = useState<ApiUsageStats[]>([]);
  const [usageLoading, setUsageLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [newKeyData, setNewKeyData] = useState({
    name: '',
    description: '',
    permissions: [] as string[],
    rate_limit: 100,
    expires_at: ''
  });
  const [newKeyResult, setNewKeyResult] = useState<ApiKey | null>(null);

  const fetchData = async () => {
    try {
      setRefreshing(true);
      setLoading(true);
      setError(null);

      const keysData = await getApiKeys();
      setApiKeys(keysData);

      if (keysData.length > 0 && !selectedKeyId) {
        setSelectedKeyId(keysData[0].key_id);
      }
    } catch (err) {
      console.error('Error fetching API keys:', err);
      setError('Failed to load API keys. Please try again.');
    } finally {
      setLoading(false);
      setTimeout(() => setRefreshing(false), 500);
    }
  };

  const fetchUsageStats = async (keyId: number) => {
    try {
      setUsageLoading(true);
      const stats = await getApiKeyUsage(keyId, 7);
      setUsageStats(stats);
    } catch (err) {
      console.error('Error fetching API key usage:', err);
    } finally {
      setUsageLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    if (selectedKeyId) {
      fetchUsageStats(selectedKeyId);
    }
  }, [selectedKeyId]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleKeySelect = (keyId: number) => {
    setSelectedKeyId(keyId);
  };

  const handleCreateKey = async () => {
    try {
      setLoading(true);
      const newKey = await createApiKey(newKeyData);
      setNewKeyResult(newKey);
      await fetchData();
    } catch (err) {
      console.error('Error creating API key:', err);
      setError('Failed to create API key. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleCloseCreateDialog = () => {
    setCreateDialogOpen(false);
    setNewKeyResult(null);
    setNewKeyData({
      name: '',
      description: '',
      permissions: [],
      rate_limit: 100,
      expires_at: ''
    });
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  const availablePermissions = [
    'read:patients',
    'write:patients',
    'read:visits',
    'write:visits',
    'read:doctors',
    'write:doctors',
    'read:appointments',
    'write:appointments'
  ];

  return (
    <Box sx={{ pb: 4 }}>
      <Paper 
        elevation={2} 
        sx={{ 
          p: 3, 
          mb: 3, 
          borderRadius: 2,
          background: theme.palette.mode === 'dark' 
            ? alpha(theme.palette.primary.dark, 0.4) 
            : alpha(theme.palette.primary.light, 0.1)
        }}
      >
        <Grid container spacing={2} alignItems="center">
          <Grid item>
            <ApiKeyIcon fontSize="large" color="primary" />
          </Grid>
          <Grid item xs>
            <Typography variant="h4" component="h1" gutterBottom>
              API Management Console
            </Typography>
            <Typography variant="subtitle1" color="textSecondary">
              Manage API keys, monitor usage, and control access to your system
            </Typography>
          </Grid>
          <Grid item>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={() => setCreateDialogOpen(true)}
              sx={{ mr: 1 }}
            >
              Generate API Key
            </Button>
            <Button
              variant="outlined"
              color="primary"
              startIcon={<RefreshIcon />}
              onClick={fetchData}
              disabled={refreshing}
            >
              {refreshing ? <CircularProgress size={24} color="inherit" /> : 'Refresh'}
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper elevation={2} sx={{ borderRadius: 2, mb: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange} 
            aria-label="api management tabs"
            variant="fullWidth"
          >
            <Tab 
              icon={<ApiKeyIcon />} 
              label="API Keys" 
              id="api-tab-0" 
              aria-controls="api-tabpanel-0" 
            />
            <Tab 
              icon={<TimelineIcon />} 
              label="Usage Monitoring" 
              id="api-tab-1" 
              aria-controls="api-tabpanel-1" 
            />
          </Tabs>
        </Box>

        {/* API Keys Tab */}
        <TabPanel value={tabValue} index={0}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : apiKeys.length === 0 ? (
            <Alert severity="info">
              No API keys found. Click the "Generate API Key" button to create one.
            </Alert>
          ) : (
            <Grid container spacing={3}>
              {apiKeys.map((key) => (
                <Grid item xs={12} md={6} lg={4} key={key.key_id}>
                  <ApiKeyCard apiKey={key} onRefresh={fetchData} />
                </Grid>
              ))}
            </Grid>
          )}
        </TabPanel>

        {/* Usage Monitoring Tab */}
        <TabPanel value={tabValue} index={1}>
          <Box sx={{ mb: 3 }}>
            <FormControl fullWidth variant="outlined">
              <InputLabel id="api-key-select-label">Select API Key</InputLabel>
              <Select
                labelId="api-key-select-label"
                id="api-key-select"
                value={selectedKeyId || ''}
                onChange={(e) => handleKeySelect(e.target.value as number)}
                label="Select API Key"
                disabled={loading || apiKeys.length === 0}
              >
                {apiKeys.map((key) => (
                  <MenuItem key={key.key_id} value={key.key_id}>
                    {key.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
          
          {usageLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : selectedKeyId ? (
            <ApiUsageChart data={usageStats} />
          ) : (
            <Alert severity="info">
              No API key selected. Please select an API key to view usage statistics.
            </Alert>
          )}
        </TabPanel>
      </Paper>

      {/* Create API Key Dialog */}
      <Dialog
        open={createDialogOpen}
        onClose={handleCloseCreateDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {newKeyResult ? 'API Key Generated' : 'Generate New API Key'}
        </DialogTitle>
        <DialogContent>
          {newKeyResult ? (
            <Box>
              <Alert severity="success" sx={{ mb: 3 }}>
                API key has been successfully generated. Please copy your key now as it won't be fully visible again.
              </Alert>
              
              <Typography variant="subtitle1" gutterBottom>
                API Key Details:
              </Typography>
              
              <Grid container spacing={2} sx={{ mb: 2 }}>
                <Grid item xs={12} sm={3}>
                  <Typography variant="body2" color="textSecondary">
                    Name:
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={9}>
                  <Typography variant="body2">
                    {newKeyResult.name}
                  </Typography>
                </Grid>
                
                <Grid item xs={12} sm={3}>
                  <Typography variant="body2" color="textSecondary">
                    Key:
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={9}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        fontFamily: 'monospace', 
                        backgroundColor: alpha(theme.palette.primary.main, 0.1),
                        p: 1,
                        borderRadius: 1,
                        flexGrow: 1
                      }}
                    >
                      {newKeyResult.key_value}
                    </Typography>
                    <Button 
                      startIcon={<CopyIcon />} 
                      onClick={() => copyToClipboard(newKeyResult.key_value)}
                      sx={{ ml: 1 }}
                    >
                      Copy
                    </Button>
                  </Box>
                </Grid>
              </Grid>
              
              <DialogContentText color="error" sx={{ mt: 2 }}>
                Important: This is the only time the full key will be displayed. Please store it securely.
              </DialogContentText>
            </Box>
          ) : (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <TextField
                  label="Name"
                  fullWidth
                  value={newKeyData.name}
                  onChange={(e) => setNewKeyData({ ...newKeyData, name: e.target.value })}
                  required
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  label="Description"
                  fullWidth
                  multiline
                  rows={2}
                  value={newKeyData.description}
                  onChange={(e) => setNewKeyData({ ...newKeyData, description: e.target.value })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Rate Limit (requests per minute)"
                  type="number"
                  fullWidth
                  value={newKeyData.rate_limit}
                  onChange={(e) => setNewKeyData({ ...newKeyData, rate_limit: parseInt(e.target.value) || 100 })}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  label="Expiration Date"
                  type="date"
                  fullWidth
                  InputLabelProps={{ shrink: true }}
                  value={newKeyData.expires_at}
                  onChange={(e) => setNewKeyData({ ...newKeyData, expires_at: e.target.value })}
                  helperText="Leave blank for no expiration"
                />
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel id="permissions-label">Permissions</InputLabel>
                  <Select
                    labelId="permissions-label"
                    multiple
                    value={newKeyData.permissions}
                    onChange={(e) => setNewKeyData({ 
                      ...newKeyData, 
                      permissions: e.target.value as string[] 
                    })}
                    renderValue={(selected) => (
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {(selected as string[]).map((value) => (
                          <Chip key={value} label={value} />
                        ))}
                      </Box>
                    )}
                  >
                    {availablePermissions.map((permission) => (
                      <MenuItem key={permission} value={permission}>
                        {permission}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          {newKeyResult ? (
            <Button onClick={handleCloseCreateDialog}>Close</Button>
          ) : (
            <>
              <Button onClick={handleCloseCreateDialog}>Cancel</Button>
              <Button 
                onClick={handleCreateKey} 
                variant="contained" 
                color="primary"
                disabled={!newKeyData.name}
              >
                {loading ? <CircularProgress size={24} /> : 'Generate Key'}
              </Button>
            </>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ApiManagementConsole;
