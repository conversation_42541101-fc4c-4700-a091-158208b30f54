import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Button,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Divider,
  useTheme,
  alpha
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Add as AddIcon,
  Dashboard as DashboardIcon,
  Storage as StorageIcon,
  VpnKey as ApiKeyIcon,
  Timeline as TimelineIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { 
  getIntegrations, 
  getExternalSystems, 
  getApiKeys, 
  getLogStatistics,
  Integration,
  ExternalSystem,
  ApiKey,
  LogStatistics
} from '../../../services/integrationService';
import IntegrationHealthMonitor from './shared/IntegrationHealthMonitor';
import IntegrationCard from './shared/IntegrationCard';
import ExternalSystemCard from './shared/ExternalSystemCard';
import ApiKeyCard from './shared/ApiKeyCard';
import IntegrationLogTable from './shared/IntegrationLogTable';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`integration-tabpanel-${index}`}
      aria-labelledby={`integration-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

const IntegrationDashboard: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [integrations, setIntegrations] = useState<Integration[]>([]);
  const [externalSystems, setExternalSystems] = useState<ExternalSystem[]>([]);
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [logStats, setLogStats] = useState<LogStatistics | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  const fetchData = async () => {
    try {
      setRefreshing(true);
      setLoading(true);
      setError(null);

      // Fetch all data in parallel
      const [integrationsData, systemsData, keysData, statsData] = await Promise.all([
        getIntegrations(),
        getExternalSystems(),
        getApiKeys(),
        getLogStatistics()
      ]);

      setIntegrations(integrationsData);
      setExternalSystems(systemsData);
      setApiKeys(keysData);
      setLogStats(statsData);
    } catch (err) {
      console.error('Error fetching integration data:', err);
      setError('Failed to load integration data. Please try again.');
    } finally {
      setLoading(false);
      setTimeout(() => setRefreshing(false), 500);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleAddIntegration = () => {
    navigate('/admin/integrations/new');
  };

  const handleAddExternalSystem = () => {
    navigate('/admin/integrations/external-systems/new');
  };

  const handleAddApiKey = () => {
    navigate('/admin/integrations/api-keys/new');
  };

  const handleViewLogs = () => {
    navigate('/admin/integrations/logs');
  };

  return (
    <Box sx={{ pb: 4 }}>
      <Paper 
        elevation={2} 
        sx={{ 
          p: 3, 
          mb: 3, 
          borderRadius: 2,
          background: theme.palette.mode === 'dark' 
            ? alpha(theme.palette.primary.dark, 0.4) 
            : alpha(theme.palette.primary.light, 0.1)
        }}
      >
        <Grid container spacing={2} alignItems="center">
          <Grid item>
            <DashboardIcon fontSize="large" color="primary" />
          </Grid>
          <Grid item xs>
            <Typography variant="h4" component="h1" gutterBottom>
              Integration Dashboard
            </Typography>
            <Typography variant="subtitle1" color="textSecondary">
              Manage system integrations, external connections, and API access
            </Typography>
          </Grid>
          <Grid item>
            <Button
              variant="contained"
              color="primary"
              startIcon={<RefreshIcon />}
              onClick={fetchData}
              disabled={refreshing}
              sx={{ mr: 1 }}
            >
              {refreshing ? <CircularProgress size={24} color="inherit" /> : 'Refresh'}
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Paper elevation={2} sx={{ borderRadius: 2, mb: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange} 
            aria-label="integration dashboard tabs"
            variant="fullWidth"
          >
            <Tab 
              icon={<DashboardIcon />} 
              label="Overview" 
              id="integration-tab-0" 
              aria-controls="integration-tabpanel-0" 
            />
            <Tab 
              icon={<StorageIcon />} 
              label="Integrations" 
              id="integration-tab-1" 
              aria-controls="integration-tabpanel-1" 
            />
            <Tab 
              icon={<StorageIcon />} 
              label="External Systems" 
              id="integration-tab-2" 
              aria-controls="integration-tabpanel-2" 
            />
            <Tab 
              icon={<ApiKeyIcon />} 
              label="API Keys" 
              id="integration-tab-3" 
              aria-controls="integration-tabpanel-3" 
            />
            <Tab 
              icon={<TimelineIcon />} 
              label="Logs" 
              id="integration-tab-4" 
              aria-controls="integration-tabpanel-4" 
            />
          </Tabs>
        </Box>

        {/* Overview Tab */}
        <TabPanel value={tabValue} index={0}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <IntegrationHealthMonitor 
                  integrations={integrations} 
                  externalSystems={externalSystems} 
                  logStats={logStats}
                />
              </Grid>
              
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Recent Activity
                </Typography>
                <IntegrationLogTable limit={5} />
              </Grid>
            </Grid>
          )}
        </TabPanel>

        {/* Integrations Tab */}
        <TabPanel value={tabValue} index={1}>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddIntegration}
            >
              Add Integration
            </Button>
          </Box>
          
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : integrations.length === 0 ? (
            <Alert severity="info">
              No integrations found. Click the "Add Integration" button to create one.
            </Alert>
          ) : (
            <Grid container spacing={3}>
              {integrations.map((integration) => (
                <Grid item xs={12} md={6} lg={4} key={integration.integration_id}>
                  <IntegrationCard integration={integration} onRefresh={fetchData} />
                </Grid>
              ))}
            </Grid>
          )}
        </TabPanel>

        {/* External Systems Tab */}
        <TabPanel value={tabValue} index={2}>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddExternalSystem}
            >
              Add External System
            </Button>
          </Box>
          
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : externalSystems.length === 0 ? (
            <Alert severity="info">
              No external systems found. Click the "Add External System" button to create one.
            </Alert>
          ) : (
            <Grid container spacing={3}>
              {externalSystems.map((system) => (
                <Grid item xs={12} md={6} lg={4} key={system.system_id}>
                  <ExternalSystemCard system={system} onRefresh={fetchData} />
                </Grid>
              ))}
            </Grid>
          )}
        </TabPanel>

        {/* API Keys Tab */}
        <TabPanel value={tabValue} index={3}>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddApiKey}
            >
              Generate API Key
            </Button>
          </Box>
          
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : apiKeys.length === 0 ? (
            <Alert severity="info">
              No API keys found. Click the "Generate API Key" button to create one.
            </Alert>
          ) : (
            <Grid container spacing={3}>
              {apiKeys.map((key) => (
                <Grid item xs={12} md={6} lg={4} key={key.key_id}>
                  <ApiKeyCard apiKey={key} onRefresh={fetchData} />
                </Grid>
              ))}
            </Grid>
          )}
        </TabPanel>

        {/* Logs Tab */}
        <TabPanel value={tabValue} index={4}>
          <Box sx={{ mb: 2, display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleViewLogs}
            >
              View All Logs
            </Button>
          </Box>
          
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : (
            <IntegrationLogTable limit={10} />
          )}
        </TabPanel>
      </Paper>
    </Box>
  );
};

export default IntegrationDashboard;
