import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Chip,
  IconButton,
  Button,
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  CircularProgress,
  Tooltip,
  useTheme,
  alpha
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  HelpOutline as UnknownIcon,
  Sync as SyncIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { Integration, updateIntegrationStatus, deleteIntegration } from '../../../../services/integrationService';

interface IntegrationCardProps {
  integration: Integration;
  onRefresh: () => void;
}

const IntegrationCard: React.FC<IntegrationCardProps> = ({ integration, onRefresh }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircleIcon sx={{ color: theme.palette.success.main }} />;
      case 'degraded':
        return <WarningIcon sx={{ color: theme.palette.warning.main }} />;
      case 'error':
        return <ErrorIcon sx={{ color: theme.palette.error.main }} />;
      default:
        return <UnknownIcon sx={{ color: theme.palette.grey[500] }} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'active':
        return theme.palette.success.main;
      case 'degraded':
      case 'inactive':
        return theme.palette.warning.main;
      case 'error':
        return theme.palette.error.main;
      default:
        return theme.palette.grey[500];
    }
  };

  const handleEdit = () => {
    navigate(`/admin/integrations/edit/${integration.integration_id}`);
  };

  const handleDelete = async () => {
    try {
      setLoading(true);
      await deleteIntegration(integration.integration_id);
      setDeleteDialogOpen(false);
      onRefresh();
    } catch (error) {
      console.error('Error deleting integration:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleStatus = async () => {
    try {
      setLoading(true);
      const newStatus = integration.status === 'active' ? 'inactive' : 'active';
      await updateIntegrationStatus(
        integration.integration_id,
        newStatus,
        integration.health_status
      );
      onRefresh();
    } catch (error) {
      console.error('Error updating integration status:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSync = async () => {
    try {
      setLoading(true);
      // Update health status to simulate a sync
      await updateIntegrationStatus(
        integration.integration_id,
        integration.status,
        Math.random() > 0.3 ? 'healthy' : Math.random() > 0.5 ? 'degraded' : 'error'
      );
      onRefresh();
    } catch (error) {
      console.error('Error syncing integration:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  return (
    <>
      <Card 
        elevation={2} 
        sx={{ 
          height: '100%', 
          display: 'flex', 
          flexDirection: 'column',
          borderRadius: 2,
          position: 'relative',
          overflow: 'visible',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '4px',
            backgroundColor: getStatusColor(integration.health_status),
            borderTopLeftRadius: 8,
            borderTopRightRadius: 8
          }
        }}
      >
        <CardContent sx={{ flexGrow: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
            <Typography variant="h6" component="h2" gutterBottom noWrap>
              {integration.name}
            </Typography>
            <Chip 
              size="small" 
              label={integration.status} 
              color={integration.status === 'active' ? 'success' : 'default'} 
            />
          </Box>
          
          <Typography variant="body2" color="textSecondary" gutterBottom>
            Type: {integration.type}
          </Typography>
          
          <Typography variant="body2" sx={{ mb: 2 }}>
            {integration.description || 'No description provided.'}
          </Typography>
          
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Typography variant="body2" color="textSecondary" sx={{ mr: 1 }}>
              Health:
            </Typography>
            <Chip
              size="small"
              icon={getStatusIcon(integration.health_status)}
              label={integration.health_status}
              sx={{ 
                backgroundColor: alpha(getStatusColor(integration.health_status), 0.1),
                color: getStatusColor(integration.health_status),
                borderColor: getStatusColor(integration.health_status)
              }}
              variant="outlined"
            />
          </Box>
          
          <Typography variant="body2" color="textSecondary">
            Last Sync: {formatDate(integration.last_sync)}
          </Typography>
        </CardContent>
        
        <CardActions sx={{ justifyContent: 'space-between', p: 2, pt: 0 }}>
          <Box>
            <Tooltip title="Edit Integration">
              <IconButton size="small" onClick={handleEdit} disabled={loading}>
                <EditIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <Tooltip title="Delete Integration">
              <IconButton 
                size="small" 
                onClick={() => setDeleteDialogOpen(true)} 
                disabled={loading}
                color="error"
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
          
          <Box>
            <Tooltip title="Sync Now">
              <IconButton size="small" onClick={handleSync} disabled={loading || integration.status !== 'active'}>
                {loading ? <CircularProgress size={20} /> : <SyncIcon fontSize="small" />}
              </IconButton>
            </Tooltip>
            <Tooltip title={integration.status === 'active' ? 'Deactivate' : 'Activate'}>
              <IconButton 
                size="small" 
                onClick={handleToggleStatus} 
                disabled={loading}
                color={integration.status === 'active' ? 'error' : 'success'}
              >
                {integration.status === 'active' ? <StopIcon fontSize="small" /> : <PlayIcon fontSize="small" />}
              </IconButton>
            </Tooltip>
          </Box>
        </CardActions>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Delete Integration</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the integration "{integration.name}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)} disabled={loading}>
            Cancel
          </Button>
          <Button onClick={handleDelete} color="error" disabled={loading}>
            {loading ? <CircularProgress size={24} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default IntegrationCard;
