import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Chip,
  LinearProgress,
  Tooltip,
  useTheme,
  alpha
} from '@mui/material';
import {
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  HelpOutline as UnknownIcon
} from '@mui/icons-material';
import { Integration, ExternalSystem, LogStatistics } from '../../../../services/integrationService';

interface IntegrationHealthMonitorProps {
  integrations: Integration[];
  externalSystems: ExternalSystem[];
  logStats: LogStatistics | null;
}

const IntegrationHealthMonitor: React.FC<IntegrationHealthMonitorProps> = ({
  integrations,
  externalSystems,
  logStats
}) => {
  const theme = useTheme();

  // Calculate health metrics
  const totalIntegrations = integrations.length;
  const activeIntegrations = integrations.filter(i => i.status === 'active').length;
  const healthyIntegrations = integrations.filter(i => i.health_status === 'healthy').length;
  const degradedIntegrations = integrations.filter(i => i.health_status === 'degraded').length;
  const errorIntegrations = integrations.filter(i => i.health_status === 'error').length;

  const totalSystems = externalSystems.length;
  const connectedSystems = externalSystems.filter(s => s.status === 'connected').length;
  const errorSystems = externalSystems.filter(s => s.status === 'error').length;

  // Calculate percentages
  const integrationHealthPercentage = totalIntegrations > 0 
    ? (healthyIntegrations / totalIntegrations) * 100 
    : 0;
  
  const systemConnectionPercentage = totalSystems > 0 
    ? (connectedSystems / totalSystems) * 100 
    : 0;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'connected':
      case 'active':
        return theme.palette.success.main;
      case 'degraded':
      case 'inactive':
        return theme.palette.warning.main;
      case 'error':
      case 'disconnected':
        return theme.palette.error.main;
      default:
        return theme.palette.grey[500];
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'connected':
      case 'active':
        return <CheckCircleIcon sx={{ color: theme.palette.success.main }} />;
      case 'degraded':
      case 'inactive':
        return <WarningIcon sx={{ color: theme.palette.warning.main }} />;
      case 'error':
      case 'disconnected':
        return <ErrorIcon sx={{ color: theme.palette.error.main }} />;
      default:
        return <UnknownIcon sx={{ color: theme.palette.grey[500] }} />;
    }
  };

  return (
    <Paper 
      elevation={1} 
      sx={{ 
        p: 3, 
        borderRadius: 2,
        background: theme.palette.mode === 'dark' 
          ? alpha(theme.palette.background.paper, 0.6) 
          : alpha(theme.palette.background.paper, 0.8)
      }}
    >
      <Typography variant="h6" gutterBottom>
        System Health Overview
      </Typography>
      
      <Grid container spacing={4}>
        {/* Integration Health */}
        <Grid item xs={12} md={6}>
          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              Integration Health
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Box sx={{ width: '100%', mr: 1 }}>
                <LinearProgress 
                  variant="determinate" 
                  value={integrationHealthPercentage} 
                  sx={{ 
                    height: 10, 
                    borderRadius: 5,
                    backgroundColor: alpha(theme.palette.primary.main, 0.2),
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: integrationHealthPercentage > 70 
                        ? theme.palette.success.main 
                        : integrationHealthPercentage > 30 
                          ? theme.palette.warning.main 
                          : theme.palette.error.main
                    }
                  }} 
                />
              </Box>
              <Typography variant="body2" color="textSecondary">
                {Math.round(integrationHealthPercentage)}%
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            <Tooltip title="Healthy integrations">
              <Chip 
                icon={<CheckCircleIcon />} 
                label={`Healthy: ${healthyIntegrations}`} 
                color="success" 
                variant="outlined" 
              />
            </Tooltip>
            <Tooltip title="Degraded integrations">
              <Chip 
                icon={<WarningIcon />} 
                label={`Degraded: ${degradedIntegrations}`} 
                color="warning" 
                variant="outlined" 
              />
            </Tooltip>
            <Tooltip title="Error integrations">
              <Chip 
                icon={<ErrorIcon />} 
                label={`Error: ${errorIntegrations}`} 
                color="error" 
                variant="outlined" 
              />
            </Tooltip>
            <Tooltip title="Active integrations">
              <Chip 
                label={`Active: ${activeIntegrations}/${totalIntegrations}`} 
                color="primary" 
                variant="outlined" 
              />
            </Tooltip>
          </Box>
        </Grid>
        
        {/* External System Connectivity */}
        <Grid item xs={12} md={6}>
          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle1" gutterBottom>
              External System Connectivity
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Box sx={{ width: '100%', mr: 1 }}>
                <LinearProgress 
                  variant="determinate" 
                  value={systemConnectionPercentage} 
                  sx={{ 
                    height: 10, 
                    borderRadius: 5,
                    backgroundColor: alpha(theme.palette.primary.main, 0.2),
                    '& .MuiLinearProgress-bar': {
                      backgroundColor: systemConnectionPercentage > 70 
                        ? theme.palette.success.main 
                        : systemConnectionPercentage > 30 
                          ? theme.palette.warning.main 
                          : theme.palette.error.main
                    }
                  }} 
                />
              </Box>
              <Typography variant="body2" color="textSecondary">
                {Math.round(systemConnectionPercentage)}%
              </Typography>
            </Box>
          </Box>
          
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            <Tooltip title="Connected systems">
              <Chip 
                icon={<CheckCircleIcon />} 
                label={`Connected: ${connectedSystems}`} 
                color="success" 
                variant="outlined" 
              />
            </Tooltip>
            <Tooltip title="Disconnected systems">
              <Chip 
                icon={<WarningIcon />} 
                label={`Disconnected: ${totalSystems - connectedSystems - errorSystems}`} 
                color="warning" 
                variant="outlined" 
              />
            </Tooltip>
            <Tooltip title="Error systems">
              <Chip 
                icon={<ErrorIcon />} 
                label={`Error: ${errorSystems}`} 
                color="error" 
                variant="outlined" 
              />
            </Tooltip>
            <Tooltip title="Total systems">
              <Chip 
                label={`Total: ${totalSystems}`} 
                color="primary" 
                variant="outlined" 
              />
            </Tooltip>
          </Box>
        </Grid>
        
        {/* Log Statistics */}
        <Grid item xs={12}>
          <Typography variant="subtitle1" gutterBottom>
            Integration Activity (Last 30 Days)
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={6} sm={3}>
              <Paper 
                elevation={0} 
                sx={{ 
                  p: 2, 
                  textAlign: 'center',
                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                  borderRadius: 2
                }}
              >
                <Typography variant="h4" color="primary">
                  {logStats?.total_logs || 0}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Total Events
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Paper 
                elevation={0} 
                sx={{ 
                  p: 2, 
                  textAlign: 'center',
                  backgroundColor: alpha(theme.palette.success.main, 0.1),
                  borderRadius: 2
                }}
              >
                <Typography variant="h4" sx={{ color: theme.palette.success.main }}>
                  {logStats?.success_count || 0}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Successful
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Paper 
                elevation={0} 
                sx={{ 
                  p: 2, 
                  textAlign: 'center',
                  backgroundColor: alpha(theme.palette.warning.main, 0.1),
                  borderRadius: 2
                }}
              >
                <Typography variant="h4" sx={{ color: theme.palette.warning.main }}>
                  {logStats?.warning_count || 0}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Warnings
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Paper 
                elevation={0} 
                sx={{ 
                  p: 2, 
                  textAlign: 'center',
                  backgroundColor: alpha(theme.palette.error.main, 0.1),
                  borderRadius: 2
                }}
              >
                <Typography variant="h4" sx={{ color: theme.palette.error.main }}>
                  {logStats?.failure_count || 0}
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Failures
                </Typography>
              </Paper>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default IntegrationHealthMonitor;
