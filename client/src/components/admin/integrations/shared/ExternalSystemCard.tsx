import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardA<PERSON>,
  Typography,
  Chip,
  IconButton,
  Button,
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  CircularProgress,
  Tooltip,
  useTheme,
  alpha
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Link as LinkIcon,
  LinkOff as LinkOffIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { ExternalSystem, deleteExternalSystem, testExternalSystemConnection } from '../../../../services/integrationService';

interface ExternalSystemCardProps {
  system: ExternalSystem;
  onRefresh: () => void;
}

const ExternalSystemCard: React.FC<ExternalSystemCardProps> = ({ system, onRefresh }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
  const [testDialogOpen, setTestDialogOpen] = useState(false);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected':
        return <CheckCircleIcon sx={{ color: theme.palette.success.main }} />;
      case 'disconnected':
        return <WarningIcon sx={{ color: theme.palette.warning.main }} />;
      case 'error':
        return <ErrorIcon sx={{ color: theme.palette.error.main }} />;
      default:
        return <WarningIcon sx={{ color: theme.palette.grey[500] }} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected':
        return theme.palette.success.main;
      case 'disconnected':
        return theme.palette.warning.main;
      case 'error':
        return theme.palette.error.main;
      default:
        return theme.palette.grey[500];
    }
  };

  const handleEdit = () => {
    navigate(`/admin/integrations/external-systems/edit/${system.system_id}`);
  };

  const handleDelete = async () => {
    try {
      setLoading(true);
      await deleteExternalSystem(system.system_id);
      setDeleteDialogOpen(false);
      onRefresh();
    } catch (error) {
      console.error('Error deleting external system:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTestConnection = async () => {
    try {
      setLoading(true);
      setTestResult(null);
      const result = await testExternalSystemConnection(system.system_id);
      setTestResult(result);
      setTestDialogOpen(true);
      onRefresh(); // Refresh to get updated status
    } catch (error) {
      console.error('Error testing connection:', error);
      setTestResult({
        success: false,
        message: 'An error occurred while testing the connection.'
      });
      setTestDialogOpen(true);
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Card 
        elevation={2} 
        sx={{ 
          height: '100%', 
          display: 'flex', 
          flexDirection: 'column',
          borderRadius: 2,
          position: 'relative',
          overflow: 'visible',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '4px',
            backgroundColor: getStatusColor(system.status),
            borderTopLeftRadius: 8,
            borderTopRightRadius: 8
          }
        }}
      >
        <CardContent sx={{ flexGrow: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
            <Typography variant="h6" component="h2" gutterBottom noWrap>
              {system.name}
            </Typography>
            <Chip 
              size="small" 
              icon={getStatusIcon(system.status)}
              label={system.status} 
              sx={{ 
                backgroundColor: alpha(getStatusColor(system.status), 0.1),
                color: getStatusColor(system.status),
                borderColor: getStatusColor(system.status)
              }}
              variant="outlined"
            />
          </Box>
          
          <Typography variant="body2" color="textSecondary" gutterBottom>
            Type: {system.type}
          </Typography>
          
          <Typography variant="body2" sx={{ mb: 2 }}>
            {system.description || 'No description provided.'}
          </Typography>
          
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1 }}>
            {system.connection_details && system.connection_details.host && (
              <Chip 
                size="small" 
                label={`Host: ${system.connection_details.host}`} 
                variant="outlined" 
              />
            )}
            {system.connection_details && system.connection_details.port && (
              <Chip 
                size="small" 
                label={`Port: ${system.connection_details.port}`} 
                variant="outlined" 
              />
            )}
            {system.connection_details && system.connection_details.protocol && (
              <Chip 
                size="small" 
                label={`Protocol: ${system.connection_details.protocol}`} 
                variant="outlined" 
              />
            )}
          </Box>
          
          <Typography variant="body2" color="textSecondary">
            Created: {new Date(system.created_at).toLocaleDateString()}
          </Typography>
        </CardContent>
        
        <CardActions sx={{ justifyContent: 'space-between', p: 2, pt: 0 }}>
          <Box>
            <Tooltip title="Edit System">
              <IconButton size="small" onClick={handleEdit} disabled={loading}>
                <EditIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <Tooltip title="Delete System">
              <IconButton 
                size="small" 
                onClick={() => setDeleteDialogOpen(true)} 
                disabled={loading}
                color="error"
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
          
          <Box>
            <Tooltip title="Test Connection">
              <IconButton 
                size="small" 
                onClick={handleTestConnection} 
                disabled={loading}
                color="primary"
              >
                {loading ? (
                  <CircularProgress size={20} />
                ) : system.status === 'connected' ? (
                  <LinkIcon fontSize="small" />
                ) : (
                  <LinkOffIcon fontSize="small" />
                )}
              </IconButton>
            </Tooltip>
          </Box>
        </CardActions>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Delete External System</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the external system "{system.name}"? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)} disabled={loading}>
            Cancel
          </Button>
          <Button onClick={handleDelete} color="error" disabled={loading}>
            {loading ? <CircularProgress size={24} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Test Connection Result Dialog */}
      <Dialog
        open={testDialogOpen}
        onClose={() => setTestDialogOpen(false)}
      >
        <DialogTitle>
          Connection Test {testResult?.success ? 'Successful' : 'Failed'}
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            {testResult?.message || 'Connection test completed.'}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setTestDialogOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ExternalSystemCard;
