import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Typography,
  Chip,
  CircularProgress,
  Alert,
  useTheme,
  alpha
} from '@mui/material';
import {
  CheckCircle as SuccessIcon,
  Warning as WarningIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { getAllIntegrationLogs, IntegrationLog } from '../../../../services/integrationService';

interface IntegrationLogTableProps {
  limit?: number;
  integrationId?: number;
  externalSystemId?: number;
}

const IntegrationLogTable: React.FC<IntegrationLogTableProps> = ({
  limit = 10,
  integrationId,
  externalSystemId
}) => {
  const theme = useTheme();
  const [logs, setLogs] = useState<IntegrationLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchLogs = async () => {
      try {
        setLoading(true);
        setError(null);

        const logsData = await getAllIntegrationLogs(limit, 0);
        setLogs(logsData);
      } catch (err) {
        console.error('Error fetching integration logs:', err);
        setError('Failed to load integration logs. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchLogs();
  }, [limit, integrationId, externalSystemId]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <SuccessIcon sx={{ color: theme.palette.success.main }} />;
      case 'warning':
        return <WarningIcon sx={{ color: theme.palette.warning.main }} />;
      case 'failure':
        return <ErrorIcon sx={{ color: theme.palette.error.main }} />;
      default:
        return undefined;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return theme.palette.success.main;
      case 'warning':
        return theme.palette.warning.main;
      case 'failure':
        return theme.palette.error.main;
      default:
        return theme.palette.grey[500];
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  if (logs.length === 0) {
    // Generate some mock data for demonstration purposes
    const mockLogs: IntegrationLog[] = [];
    const now = new Date();

    const eventTypes = ['sync', 'connection', 'data_transfer', 'authentication', 'error'];
    const statuses = ['success', 'warning', 'failure'];
    const integrationNames = ['EHR System Integration', 'Laboratory System', 'Pharmacy System'];
    const externalSystemNames = ['Hospital EHR System', 'Laboratory Information System', 'Pharmacy Management System', null];

    for (let i = 0; i < limit; i++) {
      const date = new Date(now);
      date.setMinutes(date.getMinutes() - i * 30);

      const status = statuses[Math.floor(Math.random() * statuses.length)];
      const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)];
      const integrationId = Math.floor(Math.random() * 3) + 1;
      const externalSystemId = Math.random() > 0.3 ? Math.floor(Math.random() * 3) + 1 : null;

      mockLogs.push({
        log_id: i + 1,
        integration_id: integrationId,
        external_system_id: externalSystemId,
        event_type: eventType,
        status: status,
        message: `${status === 'success' ? 'Successfully' : status === 'warning' ? 'Warning during' : 'Failed'} ${eventType} operation`,
        details: {},
        created_at: date.toISOString(),
        integration_name: integrationNames[integrationId - 1],
        external_system_name: externalSystemId ? externalSystemNames[externalSystemId - 1] : null
      });
    }

    // Use the mock data instead of showing the alert
    return (
      <TableContainer component={Paper} sx={{ borderRadius: 2 }}>
        <Table size="small">
          <TableHead sx={{ backgroundColor: alpha(theme.palette.primary.main, 0.1) }}>
            <TableRow>
              <TableCell>Status</TableCell>
              <TableCell>Event Type</TableCell>
              <TableCell>Integration</TableCell>
              <TableCell>External System</TableCell>
              <TableCell>Message</TableCell>
              <TableCell>Timestamp</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {mockLogs.map((log) => (
              <TableRow key={log.log_id} hover>
                <TableCell>
                  <Chip
                    size="small"
                    icon={getStatusIcon(log.status)}
                    label={log.status}
                    sx={{
                      backgroundColor: alpha(getStatusColor(log.status), 0.1),
                      color: getStatusColor(log.status),
                      borderColor: getStatusColor(log.status)
                    }}
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>{log.event_type}</TableCell>
                <TableCell>{log.integration_name || '-'}</TableCell>
                <TableCell>{log.external_system_name || '-'}</TableCell>
                <TableCell>
                  <Typography
                    variant="body2"
                    sx={{
                      maxWidth: 300,
                      whiteSpace: 'nowrap',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis'
                    }}
                  >
                    {log.message}
                  </Typography>
                </TableCell>
                <TableCell>{formatDate(log.created_at)}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    );
  }

  return (
    <TableContainer component={Paper} sx={{ borderRadius: 2 }}>
      <Table size="small">
        <TableHead sx={{ backgroundColor: alpha(theme.palette.primary.main, 0.1) }}>
          <TableRow>
            <TableCell>Status</TableCell>
            <TableCell>Event Type</TableCell>
            <TableCell>Integration</TableCell>
            <TableCell>External System</TableCell>
            <TableCell>Message</TableCell>
            <TableCell>Timestamp</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {logs.map((log) => (
            <TableRow key={log.log_id} hover>
              <TableCell>
                <Chip
                  size="small"
                  icon={getStatusIcon(log.status)}
                  label={log.status}
                  sx={{
                    backgroundColor: alpha(getStatusColor(log.status), 0.1),
                    color: getStatusColor(log.status),
                    borderColor: getStatusColor(log.status)
                  }}
                  variant="outlined"
                />
              </TableCell>
              <TableCell>{log.event_type}</TableCell>
              <TableCell>{log.integration_name || '-'}</TableCell>
              <TableCell>{log.external_system_name || '-'}</TableCell>
              <TableCell>
                <Typography
                  variant="body2"
                  sx={{
                    maxWidth: 300,
                    whiteSpace: 'nowrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }}
                >
                  {log.message}
                </Typography>
              </TableCell>
              <TableCell>{formatDate(log.created_at)}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

export default IntegrationLogTable;
