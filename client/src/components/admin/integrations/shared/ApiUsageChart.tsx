import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  useTheme,
  alpha
} from '@mui/material';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line
} from 'recharts';
import { ApiUsageStats } from '../../../../services/integrationService';

interface ApiUsageChartProps {
  data: ApiUsageStats[];
}

const ApiUsageChart: React.FC<ApiUsageChartProps> = ({ data }) => {
  const theme = useTheme();

  // Format the data for the charts
  const formattedData = data.length > 0 ? data.map(stat => {
    const date = new Date(stat.time_period);
    return {
      ...stat,
      time: date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }),
      date: date.toLocaleDateString(),
      formattedTime: `${date.toLocaleDateString()} ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`,
      avg_response_time: Math.round(stat.avg_response_time)
    };
  }) : [];

  // Calculate summary statistics
  const totalRequests = formattedData.reduce((sum, item) => sum + item.request_count, 0);
  const totalErrors = formattedData.reduce((sum, item) => sum + item.error_count, 0);
  const avgResponseTime = formattedData.length > 0
    ? Math.round(formattedData.reduce((sum, item) => sum + item.avg_response_time, 0) / formattedData.length)
    : 0;
  const errorRate = totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0;

  return (
    <Box>
      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={6} sm={3}>
          <Paper
            elevation={1}
            sx={{
              p: 2,
              textAlign: 'center',
              backgroundColor: alpha(theme.palette.primary.main, 0.1),
              borderRadius: 2
            }}
          >
            <Typography variant="h4" color="primary">
              {totalRequests}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              Total Requests
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Paper
            elevation={1}
            sx={{
              p: 2,
              textAlign: 'center',
              backgroundColor: alpha(theme.palette.success.main, 0.1),
              borderRadius: 2
            }}
          >
            <Typography variant="h4" sx={{ color: theme.palette.success.main }}>
              {avgResponseTime} ms
            </Typography>
            <Typography variant="body2" color="textSecondary">
              Avg Response Time
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Paper
            elevation={1}
            sx={{
              p: 2,
              textAlign: 'center',
              backgroundColor: alpha(theme.palette.error.main, 0.1),
              borderRadius: 2
            }}
          >
            <Typography variant="h4" sx={{ color: theme.palette.error.main }}>
              {totalErrors}
            </Typography>
            <Typography variant="body2" color="textSecondary">
              Total Errors
            </Typography>
          </Paper>
        </Grid>
        <Grid item xs={6} sm={3}>
          <Paper
            elevation={1}
            sx={{
              p: 2,
              textAlign: 'center',
              backgroundColor: alpha(theme.palette.warning.main, 0.1),
              borderRadius: 2
            }}
          >
            <Typography variant="h4" sx={{ color: theme.palette.warning.main }}>
              {errorRate.toFixed(1)}%
            </Typography>
            <Typography variant="body2" color="textSecondary">
              Error Rate
            </Typography>
          </Paper>
        </Grid>
      </Grid>

      {/* Request Count Chart */}
      <Paper elevation={1} sx={{ p: 3, mb: 4, borderRadius: 2 }}>
        <Typography variant="h6" gutterBottom>
          API Requests Over Time
        </Typography>
        <Box sx={{ height: 300 }}>
          <ResponsiveContainer width="100%" height="100%">
            <BarChart
              data={formattedData}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="formattedTime"
                tick={{ fontSize: 12 }}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis />
              <Tooltip
                formatter={(value: number) => [`${value}`, 'Requests']}
                labelFormatter={(label: string) => `Time: ${label}`}
              />
              <Legend />
              <Bar
                name="Successful Requests"
                dataKey={(entry: ApiUsageStats) => entry.request_count - entry.error_count}
                fill={theme.palette.success.main}
                stackId="a"
              />
              <Bar
                name="Error Requests"
                dataKey="error_count"
                fill={theme.palette.error.main}
                stackId="a"
              />
            </BarChart>
          </ResponsiveContainer>
        </Box>
      </Paper>

      {/* Response Time Chart */}
      <Paper elevation={1} sx={{ p: 3, borderRadius: 2 }}>
        <Typography variant="h6" gutterBottom>
          Average Response Time
        </Typography>
        <Box sx={{ height: 300 }}>
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={formattedData}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis
                dataKey="formattedTime"
                tick={{ fontSize: 12 }}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis unit=" ms" />
              <Tooltip
                formatter={(value: number) => [`${value} ms`, 'Response Time']}
                labelFormatter={(label: string) => `Time: ${label}`}
              />
              <Legend />
              <Line
                type="monotone"
                name="Response Time"
                dataKey="avg_response_time"
                stroke={theme.palette.primary.main}
                activeDot={{ r: 8 }}
              />
            </LineChart>
          </ResponsiveContainer>
        </Box>
      </Paper>
    </Box>
  );
};

export default ApiUsageChart;
