import React, { useState } from 'react';
import {
  Card,
  CardContent,
  CardActions,
  Typography,
  Chip,
  IconButton,
  Button,
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  CircularProgress,
  Tooltip,
  useTheme,
  alpha
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  VpnKey as KeyIcon,
  Block as BlockIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { ApiKey, deleteApiKey, updateApiKey } from '../../../../services/integrationService';

interface ApiKeyCardProps {
  apiKey: ApiKey;
  onRefresh: () => void;
}

const ApiKeyCard: React.FC<ApiKeyCardProps> = ({ apiKey, onRefresh }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const handleEdit = () => {
    navigate(`/admin/integrations/api-keys/edit/${apiKey.key_id}`);
  };

  const handleDelete = async () => {
    try {
      setLoading(true);
      await deleteApiKey(apiKey.key_id);
      setDeleteDialogOpen(false);
      onRefresh();
    } catch (error) {
      console.error('Error deleting API key:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleStatus = async () => {
    try {
      setLoading(true);
      await updateApiKey(apiKey.key_id, {
        ...apiKey,
        is_active: !apiKey.is_active
      });
      onRefresh();
    } catch (error) {
      console.error('Error updating API key status:', error);
    } finally {
      setLoading(false);
    }
  };

  const isExpired = apiKey.expires_at ? new Date(apiKey.expires_at) < new Date() : false;

  return (
    <>
      <Card
        elevation={2}
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          borderRadius: 2,
          position: 'relative',
          overflow: 'visible',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '4px',
            backgroundColor: isExpired
              ? theme.palette.error.main
              : apiKey.is_active
                ? theme.palette.success.main
                : theme.palette.warning.main,
            borderTopLeftRadius: 8,
            borderTopRightRadius: 8
          }
        }}
      >
        <CardContent sx={{ flexGrow: 1 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
            <Typography variant="h6" component="h2" gutterBottom noWrap>
              {apiKey.name}
            </Typography>
            <Chip
              size="small"
              icon={apiKey.is_active ? <CheckCircleIcon /> : <BlockIcon />}
              label={apiKey.is_active ? 'Active' : 'Inactive'}
              color={apiKey.is_active ? 'success' : 'default'}
            />
          </Box>

          <Typography variant="body2" sx={{ mb: 2 }}>
            {apiKey.description || 'No description provided.'}
          </Typography>

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <KeyIcon fontSize="small" sx={{ mr: 1, color: theme.palette.primary.main }} />
            <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
              {apiKey.key_value}
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1 }}>
            {apiKey.permissions && apiKey.permissions.map((permission, index) => (
              <Chip
                key={index}
                size="small"
                label={permission}
                variant="outlined"
              />
            ))}
          </Box>

          <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 2 }}>
            <Typography variant="body2" color="textSecondary">
              Rate Limit: {apiKey.rate_limit}/min
            </Typography>

            {apiKey.expires_at && (
              <Typography
                variant="body2"
                color={isExpired ? 'error' : 'textSecondary'}
              >
                {isExpired ? 'Expired' : 'Expires'}: {new Date(apiKey.expires_at).toLocaleDateString()}
              </Typography>
            )}
          </Box>
        </CardContent>

        <CardActions sx={{ justifyContent: 'space-between', p: 2, pt: 0 }}>
          <Box>
            <Tooltip title="Edit API Key">
              <IconButton size="small" onClick={handleEdit} disabled={loading}>
                <EditIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <Tooltip title="Delete API Key">
              <IconButton
                size="small"
                onClick={() => setDeleteDialogOpen(true)}
                disabled={loading}
                color="error"
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>

          <Box>
            <Tooltip title={apiKey.is_active ? 'Deactivate Key' : 'Activate Key'}>
              <IconButton
                size="small"
                onClick={handleToggleStatus}
                disabled={loading || isExpired}
                color={apiKey.is_active ? 'warning' : 'success'}
              >
                {loading ? (
                  <CircularProgress size={20} />
                ) : apiKey.is_active ? (
                  <BlockIcon fontSize="small" />
                ) : (
                  <CheckCircleIcon fontSize="small" />
                )}
              </IconButton>
            </Tooltip>
          </Box>
        </CardActions>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Delete API Key</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the API key "{apiKey.name}"? This action cannot be undone and will immediately revoke access for any services using this key.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)} disabled={loading}>
            Cancel
          </Button>
          <Button onClick={handleDelete} color="error" disabled={loading}>
            {loading ? <CircularProgress size={24} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default ApiKeyCard;
