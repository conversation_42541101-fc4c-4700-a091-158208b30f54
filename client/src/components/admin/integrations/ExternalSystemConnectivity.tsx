import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Button,
  CircularProgress,
  Alert,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  useTheme,
  alpha
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Add as AddIcon,
  Storage as StorageIcon,
  Link as LinkIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { 
  getExternalSystems, 
  createExternalSystem,
  testExternalSystemConnection,
  ExternalSystem
} from '../../../services/integrationService';
import ExternalSystemCard from './shared/ExternalSystemCard';

const ExternalSystemConnectivity: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [externalSystems, setExternalSystems] = useState<ExternalSystem[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [newSystemData, setNewSystemData] = useState({
    name: '',
    type: '',
    description: '',
    connection_details: {
      host: '',
      port: '',
      protocol: 'https',
      path: ''
    }
  });

  const fetchData = async () => {
    try {
      setRefreshing(true);
      setLoading(true);
      setError(null);

      const systemsData = await getExternalSystems();
      setExternalSystems(systemsData);
    } catch (err) {
      console.error('Error fetching external systems:', err);
      setError('Failed to load external systems. Please try again.');
    } finally {
      setLoading(false);
      setTimeout(() => setRefreshing(false), 500);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const handleCreateSystem = async () => {
    try {
      setLoading(true);
      await createExternalSystem(newSystemData);
      setCreateDialogOpen(false);
      setNewSystemData({
        name: '',
        type: '',
        description: '',
        connection_details: {
          host: '',
          port: '',
          protocol: 'https',
          path: ''
        }
      });
      await fetchData();
    } catch (err) {
      console.error('Error creating external system:', err);
      setError('Failed to create external system. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const systemTypes = [
    'ehr', 
    'lab', 
    'pharmacy', 
    'insurance', 
    'billing', 
    'imaging', 
    'api', 
    'database', 
    'other'
  ];

  const protocols = ['http', 'https', 'ftp', 'sftp', 'jdbc', 'odbc'];

  return (
    <Box sx={{ pb: 4 }}>
      <Paper 
        elevation={2} 
        sx={{ 
          p: 3, 
          mb: 3, 
          borderRadius: 2,
          background: theme.palette.mode === 'dark' 
            ? alpha(theme.palette.primary.dark, 0.4) 
            : alpha(theme.palette.primary.light, 0.1)
        }}
      >
        <Grid container spacing={2} alignItems="center">
          <Grid item>
            <StorageIcon fontSize="large" color="primary" />
          </Grid>
          <Grid item xs>
            <Typography variant="h4" component="h1" gutterBottom>
              External System Connectivity
            </Typography>
            <Typography variant="subtitle1" color="textSecondary">
              Manage connections to external systems and data sources
            </Typography>
          </Grid>
          <Grid item>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={() => setCreateDialogOpen(true)}
              sx={{ mr: 1 }}
            >
              Add System
            </Button>
            <Button
              variant="outlined"
              color="primary"
              startIcon={<RefreshIcon />}
              onClick={fetchData}
              disabled={refreshing}
            >
              {refreshing ? <CircularProgress size={24} color="inherit" /> : 'Refresh'}
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : externalSystems.length === 0 ? (
        <Alert severity="info" sx={{ mb: 3 }}>
          No external systems found. Click the "Add System" button to create one.
        </Alert>
      ) : (
        <Grid container spacing={3}>
          {externalSystems.map((system) => (
            <Grid item xs={12} md={6} lg={4} key={system.system_id}>
              <ExternalSystemCard system={system} onRefresh={fetchData} />
            </Grid>
          ))}
        </Grid>
      )}

      {/* Create External System Dialog */}
      <Dialog
        open={createDialogOpen}
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Add External System</DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 0 }}>
            <Grid item xs={12}>
              <TextField
                label="System Name"
                fullWidth
                value={newSystemData.name}
                onChange={(e) => setNewSystemData({ ...newSystemData, name: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel id="system-type-label">System Type</InputLabel>
                <Select
                  labelId="system-type-label"
                  value={newSystemData.type}
                  onChange={(e) => setNewSystemData({ ...newSystemData, type: e.target.value })}
                  label="System Type"
                  required
                >
                  {systemTypes.map((type) => (
                    <MenuItem key={type} value={type}>
                      {type.toUpperCase()}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel id="protocol-label">Protocol</InputLabel>
                <Select
                  labelId="protocol-label"
                  value={newSystemData.connection_details.protocol}
                  onChange={(e) => setNewSystemData({ 
                    ...newSystemData, 
                    connection_details: {
                      ...newSystemData.connection_details,
                      protocol: e.target.value
                    }
                  })}
                  label="Protocol"
                >
                  {protocols.map((protocol) => (
                    <MenuItem key={protocol} value={protocol}>
                      {protocol.toUpperCase()}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Description"
                fullWidth
                multiline
                rows={2}
                value={newSystemData.description}
                onChange={(e) => setNewSystemData({ ...newSystemData, description: e.target.value })}
              />
            </Grid>
            
            <Grid item xs={12}>
              <Divider>
                <Typography variant="body2" color="textSecondary">
                  Connection Details
                </Typography>
              </Divider>
            </Grid>
            
            <Grid item xs={12} sm={8}>
              <TextField
                label="Host"
                fullWidth
                value={newSystemData.connection_details.host}
                onChange={(e) => setNewSystemData({ 
                  ...newSystemData, 
                  connection_details: {
                    ...newSystemData.connection_details,
                    host: e.target.value
                  }
                })}
                placeholder="e.g., api.example.com"
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                label="Port"
                fullWidth
                value={newSystemData.connection_details.port}
                onChange={(e) => setNewSystemData({ 
                  ...newSystemData, 
                  connection_details: {
                    ...newSystemData.connection_details,
                    port: e.target.value
                  }
                })}
                placeholder="e.g., 443"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                label="Path"
                fullWidth
                value={newSystemData.connection_details.path}
                onChange={(e) => setNewSystemData({ 
                  ...newSystemData, 
                  connection_details: {
                    ...newSystemData.connection_details,
                    path: e.target.value
                  }
                })}
                placeholder="e.g., /api/v1"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleCreateSystem} 
            variant="contained" 
            color="primary"
            disabled={!newSystemData.name || !newSystemData.type}
          >
            {loading ? <CircularProgress size={24} /> : 'Add System'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ExternalSystemConnectivity;
