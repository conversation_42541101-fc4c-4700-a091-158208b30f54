import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
  CircularProgress,
  Alert,
  Typography,
  Box,
  Chip
} from '@mui/material';
import { API_URL } from '../../config';
import axios from 'axios';

interface User {
  user_id: number;
  username: string;
  email: string;
  role: string;
  is_locked: boolean;
  created_at: string;
  last_login: string | null;
}

interface DeleteUserDialogProps {
  open: boolean;
  onClose: () => void;
  user: User | null;
  onUserDeleted: () => void;
}

const DeleteUserDialog: React.FC<DeleteUserDialogProps> = ({ open, onClose, user, onUserDeleted }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleDelete = async () => {
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        setError('Authentication token not found. Please log in again.');
        return;
      }

      // Make the API request
      await axios.delete(
        `${API_URL}/api/admin/users/${user.user_id}`,
        {
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          }
        }
      );

      onUserDeleted();
      onClose();
    } catch (err) {
      if (axios.isAxiosError(err)) {
        setError(err.response?.data?.msg || 'Failed to delete user');
      } else {
        setError('An error occurred while deleting the user');
      }
      console.error('Error deleting user:', err);
    } finally {
      setLoading(false);
    }
  };

  const getRoleColor = (role: string): "error" | "warning" | "info" | "success" | "secondary" | "primary" => {
    switch (role) {
      case 'admin':
        return 'error';
      case 'doctor':
        return 'warning';
      case 'staff':
        return 'info';
      case 'patient':
        return 'success';
      case 'kin':
        return 'secondary';
      case 'researcher':
        return 'primary';
      default:
        return 'primary';
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Delete User</DialogTitle>
      <DialogContent>
        {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}
        <DialogContentText>
          Are you sure you want to delete this user? This action cannot be undone.
        </DialogContentText>
        {user && (
          <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid #eee' }}>
            <Typography variant="subtitle1" gutterBottom>
              User Details:
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="body2" sx={{ fontWeight: 'bold', mr: 1, minWidth: 80 }}>
                Username:
              </Typography>
              <Typography variant="body2">{user.username}</Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="body2" sx={{ fontWeight: 'bold', mr: 1, minWidth: 80 }}>
                Email:
              </Typography>
              <Typography variant="body2">{user.email}</Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Typography variant="body2" sx={{ fontWeight: 'bold', mr: 1, minWidth: 80 }}>
                Role:
              </Typography>
              <Chip
                label={user.role.toUpperCase()}
                color={getRoleColor(user.role)}
                size="small"
                sx={{ fontWeight: 500 }}
              />
            </Box>
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        <Button
          onClick={handleDelete}
          variant="contained"
          color="error"
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : null}
        >
          {loading ? 'Deleting...' : 'Delete User'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default DeleteUserDialog;
