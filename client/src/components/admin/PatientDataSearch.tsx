import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  Paper,
  TextField,
  Button,
  CircularProgress,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  <PERSON>,
  Divider,
  Grid
} from '@mui/material';
import {
  Search as SearchIcon,
  ExpandMore as ExpandMoreIcon,
  Person as PersonIcon,
  CalendarToday as CalendarIcon,
  LocalHospital as HospitalIcon,
  Assignment as AssignmentIcon
} from '@mui/icons-material';
import { API_URL } from '../../config';

interface Patient {
  patient_id: number;
  first_name: string;
  last_name: string;
  unique_id?: string;
  date_of_birth: string;
  gender?: string;
}

interface Visit {
  visit_id: number;
  patient_id: number;
  doctor_id?: number;
  visit_date: string;
  visit_reason?: string;
  status?: string;
  patient_first_name?: string;
  patient_last_name?: string;
  doctor_first_name?: string;
  doctor_last_name?: string;
  lying_bp_systolic?: number;
  lying_bp_diastolic?: number;
  standing_bp_systolic?: number;
  standing_bp_diastolic?: number;
  lying_heart_rate?: number;
  standing_heart_rate?: number;
  heart_rate?: number;
  temperature?: number;
  ldl_cholesterol?: number;
  hdl_cholesterol?: number;
  bilirubin_t?: number;
  magnesium?: number;
  albumin?: number;
  parathyroid_hormone?: number;
  alkaline_phosphatase_bone?: number;
  free_t4?: number;
  free_t3?: number;
  hemoglobin?: number;
  ferritin?: number;
  vitamin_b12?: number;
  folate?: number;
}

interface MedicalRecord {
  record_id: number;
  patient_id: number;
  record_date: string;
  diagnosis?: string;
  treatment?: string;
  notes?: string;
  patient_first_name?: string;
  patient_last_name?: string;
}

interface SearchResults {
  patients: Patient[];
  visits: Visit[];
  records: MedicalRecord[];
}

const PatientDataSearch: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [results, setResults] = useState<SearchResults | null>(null);
  const [activeTab, setActiveTab] = useState<number>(0);
  const [expandedPatient, setExpandedPatient] = useState<number | null>(null);
  const [expandedVisit, setExpandedVisit] = useState<number | null>(null);
  const [expandedRecord, setExpandedRecord] = useState<number | null>(null);

  const handleSearch = async () => {
    if (!searchTerm.trim()) {
      setError('Please enter a search term');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/debug/search-patient-data`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: JSON.stringify({ searchTerm })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to search patient data');
      }

      const data = await response.json();
      setResults(data);
      setActiveTab(0); // Reset to patients tab
      // Reset expanded items
      setExpandedPatient(null);
      setExpandedVisit(null);
      setExpandedRecord(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error searching patient data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    // Reset expanded items when changing tabs
    setExpandedPatient(null);
    setExpandedVisit(null);
    setExpandedRecord(null);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
        <SearchIcon sx={{ mr: 1 }} />
        Patient Data Search
      </Typography>

      <Typography variant="body1" paragraph color="text.secondary">
        Search for patient data across all tables. Enter a patient's name or ID to find their information.
        Use the "Show All Fields" button to view all available data fields for each record.
      </Typography>

      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <TextField
            fullWidth
            label="Search Patients"
            variant="outlined"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
            InputProps={{
              startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
            }}
            sx={{ mr: 2 }}
          />
          <Button
            variant="contained"
            color="primary"
            onClick={handleSearch}
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : <SearchIcon />}
          >
            {loading ? 'Searching...' : 'Search'}
          </Button>
        </Box>

        {error && (
          <Alert severity="error" sx={{ mt: 2 }}>
            {error}
          </Alert>
        )}
      </Paper>

      {results && (
        <Paper sx={{ p: 3 }}>
          <Tabs value={activeTab} onChange={handleTabChange} sx={{ mb: 2 }}>
            <Tab
              label={`Patients (${results.patients.length})`}
              icon={<PersonIcon />}
              iconPosition="start"
            />
            <Tab
              label={`Visits (${results.visits.length})`}
              icon={<HospitalIcon />}
              iconPosition="start"
            />
            <Tab
              label={`Medical Records (${results.records.length})`}
              icon={<AssignmentIcon />}
              iconPosition="start"
            />
          </Tabs>

          {/* Patients Tab */}
          {activeTab === 0 && (
            <>
              {results.patients.length === 0 ? (
                <Alert severity="info">No patients found matching your search criteria.</Alert>
              ) : (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>ID</TableCell>
                        <TableCell>Name</TableCell>
                        <TableCell>Date of Birth</TableCell>
                        <TableCell>Gender</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {results.patients.map((patient) => (
                        <TableRow key={patient.patient_id} hover>
                          <TableCell>{patient.unique_id || patient.patient_id}</TableCell>
                          <TableCell>{patient.first_name} {patient.last_name}</TableCell>
                          <TableCell>{formatDate(patient.date_of_birth)}</TableCell>
                          <TableCell>{patient.gender}</TableCell>
                          <TableCell>
                            <Button
                              variant="outlined"
                              size="small"
                              onClick={() => {
                                setActiveTab(1); // Switch to visits tab
                              }}
                              sx={{ mr: 1 }}
                            >
                              View Visits
                            </Button>
                            <Button
                              variant="outlined"
                              size="small"
                              color="info"
                              onClick={() => {
                                setExpandedPatient(patient.patient_id === expandedPatient ? null : patient.patient_id);
                              }}
                            >
                              {patient.patient_id === expandedPatient ? 'Hide Details' : 'Show All Fields'}
                            </Button>
                          </TableCell>
                        </TableRow>
                      ))}
                      {expandedPatient && results.patients.map(patient =>
                        patient.patient_id === expandedPatient ? (
                          <TableRow key={`expanded-${patient.patient_id}`}>
                            <TableCell colSpan={5}>
                              <Box sx={{ p: 2, bgcolor: 'background.paper' }}>
                                <Typography variant="subtitle1" gutterBottom>
                                  All Fields for {patient.first_name} {patient.last_name}
                                </Typography>
                                <Divider sx={{ mb: 2 }} />
                                <Grid container spacing={2}>
                                  {Object.entries(patient).map(([key, value]) => (
                                    <Grid item xs={12} sm={6} md={4} lg={3} key={key}>
                                      <Box sx={{ mb: 1 }}>
                                        <Typography variant="caption" color="text.secondary">
                                          {key}
                                        </Typography>
                                        <Typography variant="body2">
                                          {value === null ? (
                                            <Typography variant="body2" color="text.secondary" fontStyle="italic">
                                              NULL
                                            </Typography>
                                          ) : typeof value === 'object' ? (
                                            JSON.stringify(value)
                                          ) : typeof value === 'boolean' ? (
                                            value ? 'Yes' : 'No'
                                          ) : key.includes('date') && typeof value === 'string' ? (
                                            formatDate(value)
                                          ) : (
                                            String(value)
                                          )}
                                        </Typography>
                                      </Box>
                                    </Grid>
                                  ))}
                                </Grid>
                              </Box>
                            </TableCell>
                          </TableRow>
                        ) : null
                      )}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </>
          )}

          {/* Visits Tab */}
          {activeTab === 1 && (
            <>
              {results.visits.length === 0 ? (
                <Alert severity="info">No visits found for the matching patients.</Alert>
              ) : (
                <Box>
                  {results.visits.map((visit) => (
                    <Accordion key={visit.visit_id} sx={{ mb: 1 }}>
                      <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                          <CalendarIcon sx={{ mr: 1 }} />
                          <Typography sx={{ flexGrow: 1 }}>
                            Visit on {formatDate(visit.visit_date)} - {visit.patient_first_name} {visit.patient_last_name}
                          </Typography>
                          <Chip
                            label={visit.status || 'Unknown'}
                            color={visit.status === 'completed' ? 'success' : 'primary'}
                            size="small"
                            sx={{ ml: 1 }}
                          />
                        </Box>
                      </AccordionSummary>
                      <AccordionDetails>
                        <Typography variant="subtitle1" gutterBottom>
                          Visit Details
                        </Typography>
                        <Divider sx={{ mb: 2 }} />

                        <Box sx={{ mb: 2 }}>
                          <Typography variant="subtitle2" gutterBottom>
                            Basic Information
                          </Typography>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                            <Box>
                              <Typography variant="body2" color="text.secondary">Patient</Typography>
                              <Typography variant="body1">{visit.patient_first_name} {visit.patient_last_name}</Typography>
                            </Box>
                            <Box>
                              <Typography variant="body2" color="text.secondary">Doctor</Typography>
                              <Typography variant="body1">
                                {visit.doctor_first_name && visit.doctor_last_name
                                  ? `Dr. ${visit.doctor_first_name} ${visit.doctor_last_name}`
                                  : 'Not assigned'}
                              </Typography>
                            </Box>
                            <Box>
                              <Typography variant="body2" color="text.secondary">Visit Reason</Typography>
                              <Typography variant="body1">{visit.visit_reason || 'Not specified'}</Typography>
                            </Box>
                          </Box>
                        </Box>

                        <Box sx={{ mb: 2 }}>
                          <Typography variant="subtitle2" gutterBottom>
                            Vital Signs
                          </Typography>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                            {visit.lying_bp_systolic && visit.lying_bp_diastolic && (
                              <Box>
                                <Typography variant="body2" color="text.secondary">Lying Blood Pressure</Typography>
                                <Typography variant="body1">{visit.lying_bp_systolic}/{visit.lying_bp_diastolic} mmHg</Typography>
                              </Box>
                            )}
                            {visit.standing_bp_systolic && visit.standing_bp_diastolic && (
                              <Box>
                                <Typography variant="body2" color="text.secondary">Standing Blood Pressure</Typography>
                                <Typography variant="body1">{visit.standing_bp_systolic}/{visit.standing_bp_diastolic} mmHg</Typography>
                              </Box>
                            )}
                            {visit.lying_heart_rate && (
                              <Box>
                                <Typography variant="body2" color="text.secondary">Lying Heart Rate</Typography>
                                <Typography variant="body1">{visit.lying_heart_rate} bpm</Typography>
                              </Box>
                            )}
                            {visit.standing_heart_rate && (
                              <Box>
                                <Typography variant="body2" color="text.secondary">Standing Heart Rate</Typography>
                                <Typography variant="body1">{visit.standing_heart_rate} bpm</Typography>
                              </Box>
                            )}
                            {visit.heart_rate && (
                              <Box>
                                <Typography variant="body2" color="text.secondary">Heart Rate</Typography>
                                <Typography variant="body1">{visit.heart_rate} bpm</Typography>
                              </Box>
                            )}
                            {visit.temperature && (
                              <Box>
                                <Typography variant="body2" color="text.secondary">Temperature</Typography>
                                <Typography variant="body1">{visit.temperature} °C</Typography>
                              </Box>
                            )}
                          </Box>
                        </Box>

                        <Box sx={{ mb: 2 }}>
                          <Typography variant="subtitle2" gutterBottom>
                            Lab Results
                          </Typography>
                          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                            {visit.ldl_cholesterol && (
                              <Box>
                                <Typography variant="body2" color="text.secondary">LDL Cholesterol</Typography>
                                <Typography variant="body1">{visit.ldl_cholesterol} mg/dL</Typography>
                              </Box>
                            )}
                            {visit.hdl_cholesterol && (
                              <Box>
                                <Typography variant="body2" color="text.secondary">HDL Cholesterol</Typography>
                                <Typography variant="body1">{visit.hdl_cholesterol} mg/dL</Typography>
                              </Box>
                            )}
                            {visit.bilirubin_t && (
                              <Box>
                                <Typography variant="body2" color="text.secondary">Bilirubin Total</Typography>
                                <Typography variant="body1">{visit.bilirubin_t} mg/dL</Typography>
                              </Box>
                            )}
                            {visit.magnesium && (
                              <Box>
                                <Typography variant="body2" color="text.secondary">Magnesium</Typography>
                                <Typography variant="body1">{visit.magnesium} mg/dL</Typography>
                              </Box>
                            )}
                            {visit.albumin && (
                              <Box>
                                <Typography variant="body2" color="text.secondary">Albumin</Typography>
                                <Typography variant="body1">{visit.albumin} g/dL</Typography>
                              </Box>
                            )}
                            {visit.parathyroid_hormone && (
                              <Box>
                                <Typography variant="body2" color="text.secondary">Parathyroid Hormone</Typography>
                                <Typography variant="body1">{visit.parathyroid_hormone} pg/mL</Typography>
                              </Box>
                            )}
                            {visit.alkaline_phosphatase_bone && (
                              <Box>
                                <Typography variant="body2" color="text.secondary">Alkaline Phosphatase (Bone)</Typography>
                                <Typography variant="body1">{visit.alkaline_phosphatase_bone} U/L</Typography>
                              </Box>
                            )}
                            {visit.free_t4 && (
                              <Box>
                                <Typography variant="body2" color="text.secondary">Free T4</Typography>
                                <Typography variant="body1">{visit.free_t4} ng/dL</Typography>
                              </Box>
                            )}
                            {visit.free_t3 && (
                              <Box>
                                <Typography variant="body2" color="text.secondary">Free T3</Typography>
                                <Typography variant="body1">{visit.free_t3} pg/mL</Typography>
                              </Box>
                            )}
                            {visit.hemoglobin && (
                              <Box>
                                <Typography variant="body2" color="text.secondary">Hemoglobin</Typography>
                                <Typography variant="body1">{visit.hemoglobin} g/dL</Typography>
                              </Box>
                            )}
                            {visit.ferritin && (
                              <Box>
                                <Typography variant="body2" color="text.secondary">Ferritin</Typography>
                                <Typography variant="body1">{visit.ferritin} ng/mL</Typography>
                              </Box>
                            )}
                            {visit.vitamin_b12 && (
                              <Box>
                                <Typography variant="body2" color="text.secondary">Vitamin B12</Typography>
                                <Typography variant="body1">{visit.vitamin_b12} pg/mL</Typography>
                              </Box>
                            )}
                            {visit.folate && (
                              <Box>
                                <Typography variant="body2" color="text.secondary">Folate</Typography>
                                <Typography variant="body1">{visit.folate} ng/mL</Typography>
                              </Box>
                            )}
                          </Box>
                        </Box>

                        <Box sx={{ mt: 3 }}>
                          <Button
                            variant="outlined"
                            size="small"
                            color="info"
                            onClick={() => {
                              setExpandedVisit(visit.visit_id === expandedVisit ? null : visit.visit_id);
                            }}
                            sx={{ mb: 2 }}
                          >
                            {visit.visit_id === expandedVisit ? 'Hide All Fields' : 'Show All Fields'}
                          </Button>

                          {visit.visit_id === expandedVisit && (
                            <Box sx={{ mt: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                              <Typography variant="subtitle2" gutterBottom>
                                All Visit Fields
                              </Typography>
                              <Divider sx={{ mb: 2 }} />
                              <Grid container spacing={2}>
                                {Object.entries(visit).map(([key, value]) => (
                                  <Grid item xs={12} sm={6} md={4} lg={3} key={key}>
                                    <Box sx={{ mb: 1 }}>
                                      <Typography variant="caption" color="text.secondary">
                                        {key}
                                      </Typography>
                                      <Typography variant="body2">
                                        {value === null ? (
                                          <Typography variant="body2" color="text.secondary" fontStyle="italic">
                                            NULL
                                          </Typography>
                                        ) : typeof value === 'object' ? (
                                          JSON.stringify(value)
                                        ) : typeof value === 'boolean' ? (
                                          value ? 'Yes' : 'No'
                                        ) : key.includes('date') && typeof value === 'string' ? (
                                          formatDate(value)
                                        ) : (
                                          String(value)
                                        )}
                                      </Typography>
                                    </Box>
                                  </Grid>
                                ))}
                              </Grid>
                            </Box>
                          )}
                        </Box>
                      </AccordionDetails>
                    </Accordion>
                  ))}
                </Box>
              )}
            </>
          )}

          {/* Medical Records Tab */}
          {activeTab === 2 && (
            <>
              {results.records.length === 0 ? (
                <Alert severity="info">No medical records found for the matching patients.</Alert>
              ) : (
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Patient</TableCell>
                        <TableCell>Date</TableCell>
                        <TableCell>Diagnosis</TableCell>
                        <TableCell>Treatment</TableCell>
                        <TableCell>Notes</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {results.records.map((record) => (
                        <React.Fragment key={record.record_id}>
                          <TableRow hover>
                            <TableCell>{record.patient_first_name} {record.patient_last_name}</TableCell>
                            <TableCell>{formatDate(record.record_date)}</TableCell>
                            <TableCell>{record.diagnosis || 'N/A'}</TableCell>
                            <TableCell>{record.treatment || 'N/A'}</TableCell>
                            <TableCell>
                              {record.notes || 'N/A'}
                              <Button
                                variant="outlined"
                                size="small"
                                color="info"
                                onClick={() => {
                                  setExpandedRecord(record.record_id === expandedRecord ? null : record.record_id);
                                }}
                                sx={{ ml: 2 }}
                              >
                                {record.record_id === expandedRecord ? 'Hide Details' : 'Show All Fields'}
                              </Button>
                            </TableCell>
                          </TableRow>
                          {record.record_id === expandedRecord && (
                            <TableRow>
                              <TableCell colSpan={5}>
                                <Box sx={{ p: 2, bgcolor: 'background.paper' }}>
                                  <Typography variant="subtitle1" gutterBottom>
                                    All Fields for Record #{record.record_id}
                                  </Typography>
                                  <Divider sx={{ mb: 2 }} />
                                  <Grid container spacing={2}>
                                    {Object.entries(record).map(([key, value]) => (
                                      <Grid item xs={12} sm={6} md={4} lg={3} key={key}>
                                        <Box sx={{ mb: 1 }}>
                                          <Typography variant="caption" color="text.secondary">
                                            {key}
                                          </Typography>
                                          <Typography variant="body2">
                                            {value === null ? (
                                              <Typography variant="body2" color="text.secondary" fontStyle="italic">
                                                NULL
                                              </Typography>
                                            ) : typeof value === 'object' ? (
                                              JSON.stringify(value)
                                            ) : typeof value === 'boolean' ? (
                                              value ? 'Yes' : 'No'
                                            ) : key.includes('date') && typeof value === 'string' ? (
                                              formatDate(value)
                                            ) : (
                                              String(value)
                                            )}
                                          </Typography>
                                        </Box>
                                      </Grid>
                                    ))}
                                  </Grid>
                                </Box>
                              </TableCell>
                            </TableRow>
                          )}
                        </React.Fragment>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </>
          )}
        </Paper>
      )}
    </Box>
  );
};

export default PatientDataSearch;
