import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Snackbar,
  Card,
  CardContent,
  CardActions,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  // Dialog - Unused import
  // DialogTitle - Unused import
  // DialogContent - Unused import
  // DialogActions - Unused import
} from '@mui/material';
import {
  Person as PersonIcon,
  LocalHospital as DoctorIcon,
  Add as AddIcon,
  // EventNote as VisitIcon - Unused import
  Check as CheckIcon,
  CalendarMonth as CalendarIcon
} from '@mui/icons-material';
import { API_URL } from '../../config';

interface Doctor {
  doctor_id: string;
  first_name: string;
  last_name: string;
  specialty: string;
}

interface Patient {
  patient_id: string;
  first_name: string;
  last_name: string;
  date_of_birth: string;
  doctor_id: string;
  unique_id?: string; // Make it optional since it might not always be present
}

interface User {
  user_id: string;
  username: string;
  email: string;
  password?: string; // Only included for development purposes
  created_at?: string; // Creation date
}

interface SnackbarState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'info' | 'warning';
}

const SamplePatientGenerator: React.FC = () => {
  // State for form fields
  const [firstName, setFirstName] = useState<string>('');
  const [lastName, setLastName] = useState<string>('');
  const [selectedDoctorId, setSelectedDoctorId] = useState<string>('');
  const [gender, setGender] = useState<string>('');
  const [birthDate, setBirthDate] = useState<string>(new Date(new Date().setFullYear(new Date().getFullYear() - 65)).toISOString().split('T')[0]);
  const [createdAt, setCreatedAt] = useState<string>('');

  // State for data
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [generatedPatient, setGeneratedPatient] = useState<Patient | null>(null);
  const [generatedUser, setGeneratedUser] = useState<User | null>(null);

  // State for UI
  const [loading, setLoading] = useState<boolean>(false);
  const [doctorsLoading, setDoctorsLoading] = useState<boolean>(true);

  // Notification state
  const [snackbar, setSnackbar] = useState<SnackbarState>({
    open: false,
    message: '',
    severity: 'info'
  });

  // Fetch doctors on component mount
  useEffect(() => {
    fetchDoctors();
  }, []);

  const fetchDoctors = async () => {
    try {
      setDoctorsLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/doctors`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch doctors');
      }

      const data = await response.json();
      setDoctors(data);
    } catch (err) {
      console.error('Error fetching doctors:', err);
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to fetch doctors',
        severity: 'error'
      });
    } finally {
      setDoctorsLoading(false);
    }
  };

  const generatePatient = async () => {
    try {
      if (!firstName.trim()) {
        throw new Error('First name is required');
      }

      if (!lastName.trim()) {
        throw new Error('Last name is required');
      }

      if (!selectedDoctorId) {
        throw new Error('Please select a doctor');
      }

      if (!gender) {
        throw new Error('Please select a gender');
      }

      setLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/debug/generate-simple-patient`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: JSON.stringify({
          firstName,
          lastName,
          doctorId: selectedDoctorId,
          gender,
          birthDate,
          createdAt: createdAt || undefined
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to generate patient');
      }

      // Show success message
      setSnackbar({
        open: true,
        message: data.msg || 'Patient generated successfully',
        severity: 'success'
      });

      // Store the generated patient
      setGeneratedPatient(data.patient);

      // Store the generated user if available
      if (data.user) {
        setGeneratedUser(data.user);
      } else {
        setGeneratedUser(null);
      }

    } catch (err) {
      console.error('Error generating patient:', err);
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to generate patient',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Visit generation has been moved to the VisitGenerator component

  const resetForm = () => {
    setFirstName('');
    setLastName('');
    setSelectedDoctorId('');
    setGender('');
    setBirthDate(new Date(new Date().setFullYear(new Date().getFullYear() - 65)).toISOString().split('T')[0]);
    setCreatedAt('');
    setGeneratedPatient(null);
    setGeneratedUser(null);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
        <PersonIcon sx={{ mr: 1 }} />
        Sample Patient Generator
      </Typography>

      <Typography variant="body1" paragraph color="text.secondary">
        This tool allows you to quickly generate a patient with a specified name, gender, and doctor.
        All other patient data will be randomly generated.
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Generate New Patient
            </Typography>

            <Box component="form" sx={{ mt: 2 }}>
              <TextField
                fullWidth
                label="First Name"
                variant="outlined"
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
                sx={{ mb: 2 }}
                required
              />

              <TextField
                fullWidth
                label="Last Name"
                variant="outlined"
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
                sx={{ mb: 2 }}
                required
              />

              <TextField
                fullWidth
                label="Date of Birth"
                type="date"
                value={birthDate}
                onChange={(e) => setBirthDate(e.target.value)}
                sx={{ mb: 2 }}
                InputLabelProps={{
                  shrink: true,
                }}
                InputProps={{
                  startAdornment: (
                    <CalendarIcon sx={{ color: 'action.active', mr: 1, my: 0.5 }} />
                  ),
                }}
                required
              />

              <FormControl fullWidth sx={{ mb: 2 }} required>
                <InputLabel>Gender</InputLabel>
                <Select
                  value={gender}
                  label="Gender"
                  onChange={(e) => setGender(e.target.value as string)}
                >
                  <MenuItem value="Male">Male</MenuItem>
                  <MenuItem value="Female">Female</MenuItem>
                  <MenuItem value="Other">Other</MenuItem>
                  <MenuItem value="Unknown">Unknown</MenuItem>
                </Select>
              </FormControl>

              <FormControl fullWidth sx={{ mb: 3 }} required>
                <InputLabel>Assigned Doctor</InputLabel>
                <Select
                  value={selectedDoctorId}
                  label="Assigned Doctor"
                  onChange={(e) => setSelectedDoctorId(e.target.value as string)}
                  disabled={doctorsLoading}
                >
                  {doctorsLoading ? (
                    <MenuItem value="">
                      <CircularProgress size={20} sx={{ mr: 1 }} />
                      Loading doctors...
                    </MenuItem>
                  ) : doctors.length === 0 ? (
                    <MenuItem value="">No doctors available</MenuItem>
                  ) : (
                    doctors.map((doctor) => (
                      <MenuItem key={doctor.doctor_id} value={doctor.doctor_id}>
                        Dr. {doctor.first_name} {doctor.last_name} ({doctor.specialty})
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>

              <TextField
                fullWidth
                label="Account Creation Date/Time (Optional)"
                type="datetime-local"
                value={createdAt}
                onChange={(e) => setCreatedAt(e.target.value)}
                sx={{ mb: 3 }}
                InputLabelProps={{
                  shrink: true,
                }}
                InputProps={{
                  startAdornment: (
                    <CalendarIcon sx={{ color: 'action.active', mr: 1, my: 0.5 }} />
                  ),
                }}
                helperText="Leave blank to use current date/time"
              />

              <Alert severity="info" sx={{ mb: 3 }}>
                This will generate a patient with the provided name, date of birth, and doctor, using comprehensive random data for all other fields.
              </Alert>

              <Box sx={{ mb: 2, ml: 2 }}>
                <Typography variant="body2" paragraph>
                  <strong>• Basic Information:</strong> Gender, contact details, address
                </Typography>

                <Typography variant="subtitle2" color="primary" sx={{ mt: 1 }}>
                  Comprehensive Medical Data:
                </Typography>
                <Grid container spacing={1} sx={{ ml: 1 }}>
                  <Grid item xs={6}>
                    <Typography variant="body2">• Vital Signs</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">• Physical Measurements</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">• Lab Results</Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2">• Health Status</Typography>
                  </Grid>
                </Grid>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Button
                  variant="outlined"
                  onClick={resetForm}
                  disabled={loading || (!firstName && !lastName && !selectedDoctorId)}
                >
                  Reset
                </Button>

                <Button
                  variant="contained"
                  color="primary"
                  startIcon={loading ? <CircularProgress size={20} /> : <AddIcon />}
                  onClick={generatePatient}
                  disabled={loading || !firstName || !lastName || !selectedDoctorId || !gender}
                >
                  {loading ? 'Generating...' : 'Generate Patient'}
                </Button>
              </Box>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          {generatedPatient ? (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                  <CheckIcon sx={{ mr: 1, color: 'success.main' }} />
                  Patient Generated Successfully
                </Typography>

                <Divider sx={{ my: 2 }} />

                <List>
                  <ListItem>
                    <ListItemIcon>
                      <PersonIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Patient Name"
                      secondary={`${generatedPatient.first_name} ${generatedPatient.last_name}`}
                    />
                  </ListItem>

                  <ListItem>
                    <ListItemIcon>
                      <DoctorIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Assigned Doctor"
                      secondary={doctors.find(d => d.doctor_id === generatedPatient.doctor_id)
                        ? `Dr. ${doctors.find(d => d.doctor_id === generatedPatient.doctor_id)?.first_name} ${doctors.find(d => d.doctor_id === generatedPatient.doctor_id)?.last_name}`
                        : `Doctor ID: ${generatedPatient.doctor_id}`
                      }
                    />
                  </ListItem>

                  <ListItem>
                    <ListItemText
                      primary="Date of Birth"
                      secondary={new Date(generatedPatient.date_of_birth).toLocaleDateString()}
                    />
                  </ListItem>

                  <ListItem>
                    <ListItemText
                      primary="Patient ID"
                      secondary={generatedPatient.unique_id || generatedPatient.patient_id}
                    />
                  </ListItem>
                </List>

                <Divider sx={{ my: 2 }} />

                {generatedUser ? (
                  <>
                    <Typography variant="subtitle1" gutterBottom>
                      User Account Created
                    </Typography>
                    <Alert severity="info" sx={{ mb: 2 }}>
                      A user account has been created for this patient. They can log in with the following credentials:
                    </Alert>

                    <List dense>
                      <ListItem>
                        <ListItemText
                          primary="Username"
                          secondary={generatedUser.username}
                          primaryTypographyProps={{ fontWeight: 'bold' }}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemText
                          primary="Email"
                          secondary={generatedUser.email}
                          primaryTypographyProps={{ fontWeight: 'bold' }}
                        />
                      </ListItem>
                      {generatedUser.password && (
                        <ListItem>
                          <ListItemText
                            primary="Password"
                            secondary={generatedUser.password}
                            primaryTypographyProps={{ fontWeight: 'bold', color: 'error.main' }}
                            secondaryTypographyProps={{ color: 'error.main' }}
                          />
                        </ListItem>
                      )}
                      {generatedUser.created_at && (
                        <ListItem>
                          <ListItemText
                            primary="Account Created"
                            secondary={new Date(generatedUser.created_at).toLocaleString()}
                            primaryTypographyProps={{ fontWeight: 'bold' }}
                          />
                        </ListItem>
                      )}
                    </List>
                  </>
                ) : (
                  <Alert severity="warning" sx={{ mb: 2 }}>
                    No user account was created for this patient. They won't be able to log in.
                  </Alert>
                )}

                <Divider sx={{ my: 2 }} />

                <Typography variant="subtitle1" gutterBottom>
                  Comprehensive Data Generated
                </Typography>
                <Alert severity="success" sx={{ mb: 2 }}>
                  A comprehensive patient record has been created with the following data:
                </Alert>

                <Grid container spacing={1}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      • Vital signs (BP, heart rate, etc.)
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      • Physical measurements
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      • Lab results
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      • Health status assessments
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>

              <CardActions>
                <Button
                  fullWidth
                  variant="outlined"
                  onClick={resetForm}
                >
                  Generate Another Patient
                </Button>
              </CardActions>
            </Card>
          ) : (
            <Paper sx={{ p: 4, height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}>
              <PersonIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" align="center">
                No Patient Generated Yet
              </Typography>
              <Typography variant="body2" color="text.secondary" align="center" sx={{ mt: 1 }}>
                Fill out the form and click "Generate Patient" to create a new patient with random data.
              </Typography>
            </Paper>
          )}
        </Grid>
      </Grid>

      {/* Visit generation has been moved to the VisitGenerator component */}

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default SamplePatientGenerator;
