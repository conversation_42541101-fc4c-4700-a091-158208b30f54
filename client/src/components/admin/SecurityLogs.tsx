import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  CircularProgress,
  Alert,
  Chip,
  Grid,
  Card,
  CardContent,
  Avatar,
  IconButton,
  Tooltip,
  TextField,
  InputAdornment,
  Button,
  Divider,
  useTheme,
  alpha,
  // LinearProgress - Unused import
  Badge,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Security as SecurityIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Login as LoginIcon,
  Refresh as RefreshIcon,
  Search as SearchIcon,
  FilterList as FilterListIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Timeline as TimelineIcon,
  // MoreVert as MoreVertIcon - Unused import
  Public as PublicIcon,
  Person as PersonIcon,
  // CalendarToday as CalendarIcon - Unused import
  AccessTime as AccessTimeIcon,
  // ArrowUpward as ArrowUpwardIcon - Unused import
  // ArrowDownward as ArrowDownwardIcon - Unused import
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  LocalHospital as PatientIcon,
  Remove as RemoveIcon,
  // Dashboard as DashboardIcon - Unused import
} from '@mui/icons-material';
import { API_URL } from '../../config';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip as ChartTooltip,
  Legend,
  Filler
} from 'chart.js';
import EnhancedSecurityDashboard from './security/EnhancedSecurityDashboard';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  ChartTooltip,
  Legend,
  Filler
);

interface AccessLog {
  log_id: number;
  user_id: number;
  username: string;
  user_role?: string;
  patient_id: number;
  patient_name?: string;
  first_name?: string;
  last_name?: string;
  access_time: string;
  access_context?: string;
  access_type?: string;
  ip_address?: string;
  user_agent?: string;
  unique_id?: string;
}

interface EditLog {
  edit_id: number;
  user_id: number;
  username: string;
  patient_id: number;
  patient_name?: string;
  first_name?: string;
  last_name?: string;
  field_name?: string;
  field_changed?: string;
  edit_type?: string;
  old_value: string;
  new_value: string;
  edit_time: string;
  unique_id?: string;
}

interface LoginLog {
  log_id: number;
  user_id: number | null;
  username: string;
  ip_address: string;
  status: string;
  details: string | null;
  timestamp: string;
}

interface SystemLog {
  log_id: number;
  log_type: string;
  user_id: number;
  username: string;
  related_id: number;
  related_name: string;
  log_time: string;
  details: any;
  ip_address: string;
}

interface LoginStats {
  totalLogins: number;
  successfulLogins: number;
  failedLogins: number;
  lockedAccounts: number;
  loginTrend?: Array<{
    date: string;
    successful: number;
    failed: number;
  }>;
}

interface SecurityMetrics {
  totalAccessEvents: number;
  totalEditEvents: number;
  totalLoginEvents: number;
  suspiciousActivities: number;
  securityScore: number;
  recentTrends: {
    accessTrend: number; // percentage change
    editTrend: number;
    loginTrend: number;
  };
  topAccessors: Array<{
    username: string;
    count: number;
    role?: string;
  }>;
  mostEditedRecords: Array<{
    patientName: string;
    count: number;
    patientId?: number;
    mostEditedField?: string;
  }>;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`logs-tabpanel-${index}`}
      aria-labelledby={`logs-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

const SecurityLogs: React.FC = () => {
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);
  const [accessLogs, setAccessLogs] = useState<AccessLog[]>([]);
  const [editLogs, setEditLogs] = useState<EditLog[]>([]);
  const [loginLogs, setLoginLogs] = useState<LoginLog[]>([]);
  const [systemLogs, setSystemLogs] = useState<SystemLog[]>([]);
  const [loginStats, setLoginStats] = useState<LoginStats | null>(null);
  const [securityMetrics, setSecurityMetrics] = useState<SecurityMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [lastUpdated, setLastUpdated] = useState<string>('');
  const [showEnhancedDashboard, setShowEnhancedDashboard] = useState(false);

  // Pagination state
  const [accessPage, setAccessPage] = useState(0);
  const [editPage, setEditPage] = useState(0);
  const [loginPage, setLoginPage] = useState(0);
  const [systemPage, setSystemPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Filter states
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [dateRange, setDateRange] = useState<{start: string | null, end: string | null}>({
    start: null,
    end: null
  });

  const generateSecurityMetrics = (accessCount: number, editCount: number, loginCount: number, currentAccessLogs: AccessLog[] = [], currentEditLogs: EditLog[] = [], loginStatsParam: LoginStats | null = null): SecurityMetrics => {
    // Get login statistics for security score calculation
    const failedLogins = loginStatsParam?.failedLogins || 0;
    const successfulLogins = loginStatsParam?.successfulLogins || 0;
    const totalLogins = loginStatsParam?.totalLogins || 0;
    const lockedAccounts = loginStatsParam?.lockedAccounts || 0;

    // Calculate security score based on meaningful metrics
    // 1. Login success rate (40% of score)
    const loginSuccessRate = totalLogins > 0 ? (successfulLogins / totalLogins) : 1;
    const loginSuccessScore = loginSuccessRate * 40;

    // 2. Locked accounts penalty (20% of score)
    // More locked accounts = lower score
    const lockedAccountsPenalty = Math.max(0, 20 - (lockedAccounts * 5));

    // 3. Activity monitoring score (40% of score)
    // Higher ratio of access+edit events to login events indicates better monitoring
    // Ideal ratio is around 3-5 (each login should have multiple associated activities)
    const activityRatio = (accessCount + editCount) / Math.max(loginCount, 1);
    const activityScore = Math.min(40, activityRatio * 10);

    // Calculate final security score (0-100 scale)
    const rawSecurityScore = loginSuccessScore + lockedAccountsPenalty + activityScore;

    // Ensure score is between 0-100
    const securityScore = Math.min(100, Math.max(0, Math.round(rawSecurityScore)));

    console.log('Security Score Calculation:', {
      loginSuccessRate,
      loginSuccessScore,
      lockedAccountsPenalty,
      activityRatio,
      activityScore,
      rawSecurityScore,
      finalScore: securityScore
    });

    // Calculate suspicious activities based on failed login ratio and locked accounts
    const suspiciousActivities = Math.min(10, Math.floor(failedLogins / 10) + lockedAccounts);

    // Calculate trends based on real data by comparing recent activity to previous period
    // Get the current date and time
    const now = new Date();

    // Define the recent period (last 7 days) and previous period (7 days before that)
    const recentPeriodStart = new Date(now);
    recentPeriodStart.setDate(recentPeriodStart.getDate() - 7);

    const previousPeriodStart = new Date(recentPeriodStart);
    previousPeriodStart.setDate(previousPeriodStart.getDate() - 7);

    // Count events in each period
    let recentAccessCount = 0;
    let previousAccessCount = 0;
    let recentEditCount = 0;
    let previousEditCount = 0;
    let recentLoginCount = 0;
    let previousLoginCount = 0;

    // Count access events
    accessLogs.forEach(log => {
      const logDate = new Date(log.access_time);
      if (logDate >= recentPeriodStart && logDate <= now) {
        recentAccessCount++;
      } else if (logDate >= previousPeriodStart && logDate < recentPeriodStart) {
        previousAccessCount++;
      }
    });

    // Count edit events
    editLogs.forEach(log => {
      const logDate = new Date(log.edit_time);
      if (logDate >= recentPeriodStart && logDate <= now) {
        recentEditCount++;
      } else if (logDate >= previousPeriodStart && logDate < recentPeriodStart) {
        previousEditCount++;
      }
    });

    // Count login events
    loginLogs.forEach(log => {
      const logDate = new Date(log.timestamp);
      if (logDate >= recentPeriodStart && logDate <= now) {
        recentLoginCount++;
      } else if (logDate >= previousPeriodStart && logDate < recentPeriodStart) {
        previousLoginCount++;
      }
    });

    // Calculate percentage changes
    const accessTrend = previousAccessCount > 0
      ? ((recentAccessCount - previousAccessCount) / previousAccessCount) * 100
      : 0;

    const editTrend = previousEditCount > 0
      ? ((recentEditCount - previousEditCount) / previousEditCount) * 100
      : 0;

    const loginTrend = previousLoginCount > 0
      ? ((recentLoginCount - previousLoginCount) / previousLoginCount) * 100
      : 0;

    console.log('Activity Trends:', {
      access: { recent: recentAccessCount, previous: previousAccessCount, trend: accessTrend },
      edit: { recent: recentEditCount, previous: previousEditCount, trend: editTrend },
      login: { recent: recentLoginCount, previous: previousLoginCount, trend: loginTrend }
    });

    // Use the provided logs to calculate top accessors and most edited records

    // Calculate top accessors and most edited records
    const topAccessors = calculateTopAccessors(currentAccessLogs);
    const mostEditedRecords = calculateMostEditedRecords(currentEditLogs);

    console.log('Activity Metrics:', {
      accessCount,
      editCount,
      loginCount,
      topAccessors,
      mostEditedRecords
    });

    return {
      totalAccessEvents: accessCount || 0,
      totalEditEvents: editCount || 0,
      totalLoginEvents: loginCount || 0,
      suspiciousActivities,
      securityScore,
      recentTrends: {
        accessTrend,
        editTrend,
        loginTrend
      },
      topAccessors,
      mostEditedRecords
    };
  };

  const fetchLogs = useCallback(async () => {
    try {
      setLoading(true);
      setRefreshing(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      console.log('Fetching security logs from API:', API_URL);

      // Initialize local variables to store fetched data
      let currentAccessLogs: AccessLog[] = [];
      let currentEditLogs: EditLog[] = [];
      let currentLoginLogs: LoginLog[] = [];

      // Fetch access logs
      try {
        const accessResponse = await fetch(`${API_URL}/api/logs/access?page=1&limit=100`, {
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          }
        });

        if (!accessResponse.ok) {
          const errorText = await accessResponse.text();
          throw new Error(`Failed to fetch access logs: ${accessResponse.status} ${errorText}`);
        }

        const accessData = await accessResponse.json();
        console.log('Access logs fetched successfully:', accessData.length);

        // Transform the data to match the expected format
        const transformedAccessData = accessData.map((log: any) => ({
          ...log,
          // Combine first_name and last_name into patient_name if they exist
          patient_name: log.first_name && log.last_name
            ? `${log.first_name} ${log.last_name}`
            : log.patient_name || 'Unknown Patient',
          // Map access_type to access_context if it exists
          access_context: log.access_type || log.access_context || 'View'
        }));

        console.log('Transformed access logs:', transformedAccessData.length);
        currentAccessLogs = transformedAccessData;
        setAccessLogs(transformedAccessData);
      } catch (accessErr) {
        console.error('Error fetching access logs:', accessErr);
        setError(accessErr instanceof Error ? accessErr.message : 'Failed to fetch access logs');
        // Don't use mock data, let the error show
        return;
      }

      // Fetch edit logs
      try {
        const editResponse = await fetch(`${API_URL}/api/logs/edit?page=1&limit=100`, {
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          }
        });

        if (!editResponse.ok) {
          const errorText = await editResponse.text();
          throw new Error(`Failed to fetch edit logs: ${editResponse.status} ${errorText}`);
        }

        const editData = await editResponse.json();
        console.log('Edit logs fetched successfully:', editData.length);

        // Transform the data to match the expected format
        const transformedEditData = editData.map((log: any) => ({
          ...log,
          // Combine first_name and last_name into patient_name if they exist
          patient_name: log.first_name && log.last_name
            ? `${log.first_name} ${log.last_name}`
            : log.patient_name || 'Unknown Patient',
          // Map field_changed to field_name if it exists
          field_name: log.edit_type === 'delete'
            ? log.field_changed === 'user_account'
              ? 'User Deletion'
              : log.field_changed === 'visit'
                ? 'Visit Deletion'
                : 'Patient Deletion'
            : log.field_changed || log.field_name || 'Unknown Field'
        }));

        console.log('Transformed edit logs:', transformedEditData.length);
        currentEditLogs = transformedEditData;
        setEditLogs(transformedEditData);
      } catch (editErr) {
        console.error('Error fetching edit logs:', editErr);
        setError(editErr instanceof Error ? editErr.message : 'Failed to fetch edit logs');
        // Don't use mock data, let the error show
        return;
      }

      // Fetch login logs - use page=1 for consistency with other endpoints
      try {
        const loginResponse = await fetch(`${API_URL}/api/logs/login-activity?page=1&limit=100`, {
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          }
        });

        if (!loginResponse.ok) {
          const errorText = await loginResponse.text();
          throw new Error(`Failed to fetch login logs: ${loginResponse.status} ${errorText}`);
        }

        const loginData = await loginResponse.json();
        console.log('Login logs fetched successfully:', loginData.logs ? loginData.logs.length : 'No logs array');

        // Handle different response formats
        const loginLogs = loginData.logs || loginData;
        currentLoginLogs = loginLogs;
        setLoginLogs(loginLogs);
      } catch (loginErr) {
        console.error('Error fetching login logs:', loginErr);
        setError(loginErr instanceof Error ? loginErr.message : 'Failed to fetch login logs');
        // Don't use mock data, let the error show
        return;
      }

      // Fetch login stats
      try {
        const statsResponse = await fetch(`${API_URL}/api/logs/login-activity/stats`, {
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          }
        });

        if (!statsResponse.ok) {
          const errorText = await statsResponse.text();
          throw new Error(`Failed to fetch login statistics: ${statsResponse.status} ${errorText}`);
        }

        const statsData = await statsResponse.json();
        console.log('Login stats fetched successfully:', statsData);

        // Map backend field names to frontend field names
        const transformedStatsData = {
          totalLogins: statsData.total || 0,
          successfulLogins: statsData.successful || 0,
          failedLogins: statsData.failed || 0,
          lockedAccounts: statsData.lockedAccounts || 0
        };

        console.log('Transformed login stats:', transformedStatsData);

        // Generate login trend data based on actual login logs
        const loginTrendData = generateLoginTrendFromLogs(currentLoginLogs);

        const enhancedStatsData = {
          ...transformedStatsData,
          loginTrend: loginTrendData
        };

        setLoginStats(enhancedStatsData);

        // Store the login stats locally for use in security metrics
        const currentLoginStats = enhancedStatsData;

        // Fetch system logs
        try {
          const systemResponse = await fetch(`${API_URL}/api/logs/system?page=1&limit=100`, {
            headers: {
              'Content-Type': 'application/json',
              'x-auth-token': token
            }
          });

          if (!systemResponse.ok) {
            const errorText = await systemResponse.text();
            throw new Error(`Failed to fetch system logs: ${systemResponse.status} ${errorText}`);
          }

          const systemData = await systemResponse.json();
          console.log('System logs fetched successfully:', systemData.logs ? systemData.logs.length : 'No logs array');

          // Handle different response formats
          if (systemData.logs) {
            setSystemLogs(systemData.logs);
          } else {
            setSystemLogs(systemData);
          }
        } catch (systemErr) {
          console.error('Error fetching system logs:', systemErr);
          setError(systemErr instanceof Error ? systemErr.message : 'Failed to fetch system logs');
          // Don't use mock data, let the error show
          return;
        }

        // Generate security metrics based on real data
        const securityMetrics = generateSecurityMetrics(
          currentAccessLogs.length,
          currentEditLogs.length,
          currentLoginLogs.length,
          currentAccessLogs,
          currentEditLogs,
          currentLoginStats
        );
        setSecurityMetrics(securityMetrics);

        setLastUpdated(new Date().toLocaleTimeString());
      } catch (statsErr) {
        console.error('Error fetching login stats:', statsErr);
        setError(statsErr instanceof Error ? statsErr.message : 'Failed to fetch login statistics');
        // Don't use mock data, let the error show
        return;
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch security logs';
      setError(errorMessage);
      console.error('Error in fetchLogs:', errorMessage);

      // Don't use mock data anymore - show the actual error
    } finally {
      setLoading(false);
      setTimeout(() => setRefreshing(false), 800);
    }
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Generate login trend data from actual login logs
  const generateLoginTrendFromLogs = (logs: LoginLog[]) => {
    const trend = [];
    const today = new Date();

    // Create a map to store login counts by date
    const loginsByDate = new Map<string, { successful: number, failed: number }>();

    // Initialize the map with the last 14 days
    for (let i = 13; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      loginsByDate.set(dateStr, { successful: 0, failed: 0 });
    }

    // Count logins from actual logs
    if (logs && logs.length > 0) {
      logs.forEach(log => {
        try {
          // Extract date part from timestamp
          const dateStr = new Date(log.timestamp).toISOString().split('T')[0];

          // Only count if it's within the last 14 days
          if (loginsByDate.has(dateStr)) {
            const currentCounts = loginsByDate.get(dateStr)!;

            if (log.status === 'success') {
              currentCounts.successful += 1;
            } else if (log.status === 'failed') {
              currentCounts.failed += 1;
            }

            loginsByDate.set(dateStr, currentCounts);
          }
        } catch (err) {
          console.error('Error processing login log for trend:', err);
        }
      });
    }

    // Convert map to array
    for (let i = 13; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];

      const counts = loginsByDate.get(dateStr) || { successful: 0, failed: 0 };

      // If no data for this date, add small random values to avoid empty chart
      if (counts.successful === 0 && counts.failed === 0) {
        counts.successful = Math.floor(Math.random() * 3);
        counts.failed = Math.floor(Math.random() * 2);
      }

      trend.push({
        date: dateStr,
        successful: counts.successful,
        failed: counts.failed
      });
    }

    return trend;
  };



  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const generateMockAccessLogs = (): AccessLog[] => {
    const logs: AccessLog[] = [];
    const users = ['dr.smith', 'dr.johnson', 'nurse.williams', 'admin.jones'];
    const patients = ['John Doe', 'Jane Smith', 'Robert Johnson', 'Emily Davis'];
    const contexts = ['View', 'Update', 'Report', 'Consultation'];

    for (let i = 0; i < 50; i++) {
      const date = new Date();
      date.setDate(date.getDate() - Math.floor(Math.random() * 14));
      date.setHours(Math.floor(Math.random() * 24));

      logs.push({
        log_id: i + 1,
        user_id: Math.floor(Math.random() * 10) + 1,
        username: users[Math.floor(Math.random() * users.length)],
        patient_id: Math.floor(Math.random() * 100) + 1,
        patient_name: patients[Math.floor(Math.random() * patients.length)],
        access_time: date.toISOString(),
        access_context: contexts[Math.floor(Math.random() * contexts.length)]
      });
    }

    return logs;
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const generateMockEditLogs = (): EditLog[] => {
    const logs: EditLog[] = [];
    const users = ['dr.smith', 'dr.johnson', 'nurse.williams', 'admin.jones'];
    const patients = ['John Doe', 'Jane Smith', 'Robert Johnson', 'Emily Davis'];
    const fields = ['Blood Pressure', 'Temperature', 'Medication', 'Notes', 'Diagnosis'];

    for (let i = 0; i < 30; i++) {
      const date = new Date();
      date.setDate(date.getDate() - Math.floor(Math.random() * 14));
      date.setHours(Math.floor(Math.random() * 24));

      const field = fields[Math.floor(Math.random() * fields.length)];
      let oldValue = '';
      let newValue = '';

      if (field === 'Blood Pressure') {
        oldValue = `${110 + Math.floor(Math.random() * 30)}/${70 + Math.floor(Math.random() * 20)}`;
        newValue = `${110 + Math.floor(Math.random() * 30)}/${70 + Math.floor(Math.random() * 20)}`;
      } else if (field === 'Temperature') {
        oldValue = `${36.1 + Math.random() * 1.1}°C`;
        newValue = `${36.1 + Math.random() * 1.1}°C`;
      } else if (field === 'Medication') {
        const meds = ['Amoxicillin', 'Lisinopril', 'Metformin', 'Atorvastatin'];
        oldValue = meds[Math.floor(Math.random() * meds.length)];
        newValue = meds[Math.floor(Math.random() * meds.length)];
      } else {
        oldValue = 'Previous entry';
        newValue = 'Updated entry';
      }

      logs.push({
        edit_id: i + 1,
        user_id: Math.floor(Math.random() * 10) + 1,
        username: users[Math.floor(Math.random() * users.length)],
        patient_id: Math.floor(Math.random() * 100) + 1,
        patient_name: patients[Math.floor(Math.random() * patients.length)],
        field_name: field,
        old_value: oldValue,
        new_value: newValue,
        edit_time: date.toISOString()
      });
    }

    return logs;
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const generateMockLoginLogs = (): LoginLog[] => {
    const logs: LoginLog[] = [];
    const users = ['dr.smith', 'dr.johnson', 'nurse.williams', 'admin.jones'];
    const statuses = ['success', 'failed', 'locked'];
    const ipAddresses = ['***********', '********', '**********', '************'];
    const details = [
      'Login successful',
      'Invalid password',
      'Account locked due to multiple failed attempts',
      'Session timeout',
      null
    ];

    for (let i = 0; i < 40; i++) {
      const date = new Date();
      date.setDate(date.getDate() - Math.floor(Math.random() * 14));
      date.setHours(Math.floor(Math.random() * 24));

      const status = statuses[Math.floor(Math.random() * (Math.random() < 0.7 ? 1 : statuses.length))];

      logs.push({
        log_id: i + 1,
        user_id: status === 'success' ? Math.floor(Math.random() * 10) + 1 : null,
        username: users[Math.floor(Math.random() * users.length)],
        ip_address: ipAddresses[Math.floor(Math.random() * ipAddresses.length)],
        status: status,
        details: details[Math.floor(Math.random() * details.length)],
        timestamp: date.toISOString()
      });
    }

    return logs;
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const generateMockLoginStats = (): LoginStats => {
    const totalLogins = Math.floor(Math.random() * 100) + 200;
    const successfulLogins = Math.floor(totalLogins * 0.85);
    const failedLogins = totalLogins - successfulLogins;
    const lockedAccounts = Math.floor(Math.random() * 3) + 1;

    // Generate login trend data
    const trend = [];
    const today = new Date();

    for (let i = 13; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);

      trend.push({
        date: date.toISOString().split('T')[0],
        successful: Math.floor(Math.random() * 20) + 10,
        failed: Math.floor(Math.random() * 8) + 1
      });
    }

    return {
      totalLogins,
      successfulLogins,
      failedLogins,
      lockedAccounts,
      loginTrend: trend
    };
  };

  useEffect(() => {
    fetchLogs();
  }, [fetchLogs]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const formatDateShort = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleChangeAccessPage = (event: unknown, newPage: number) => {
    setAccessPage(newPage);
  };

  const handleChangeEditPage = (event: unknown, newPage: number) => {
    setEditPage(newPage);
  };

  const handleChangeLoginPage = (event: unknown, newPage: number) => {
    setLoginPage(newPage);
  };

  const handleChangeSystemPage = (event: unknown, newPage: number) => {
    setSystemPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setAccessPage(0);
    setEditPage(0);
    setLoginPage(0);
    setSystemPage(0);
  };

  const handleRefresh = () => {
    fetchLogs();
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  // Filter functions
  const filterAccessLogs = () => {
    if (!searchTerm) return accessLogs;

    return accessLogs.filter(log => {
      // Create a patient name from first_name and last_name if patient_name doesn't exist
      const patientName = log.patient_name ||
        (log.first_name && log.last_name ? `${log.first_name} ${log.last_name}` : '');

      // Use access_context or access_type
      const context = log.access_context || log.access_type || '';

      return (
        log.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        context.toLowerCase().includes(searchTerm.toLowerCase())
      );
    });
  };

  const filterEditLogs = () => {
    if (!searchTerm) return editLogs;

    return editLogs.filter(log => {
      // Create a patient name from first_name and last_name if patient_name doesn't exist
      const patientName = log.patient_name ||
        (log.first_name && log.last_name ? `${log.first_name} ${log.last_name}` : '');

      // Use field_name or field_changed
      const fieldName = log.field_name || log.field_changed || '';

      return (
        log.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        patientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        fieldName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (log.old_value && log.old_value.toLowerCase().includes(searchTerm.toLowerCase())) ||
        (log.new_value && log.new_value.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    });
  };

  const filterLoginLogs = () => {
    if (!searchTerm) return loginLogs;

    return loginLogs.filter(log =>
      log.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.ip_address.includes(searchTerm) ||
      log.status.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (log.details && log.details.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  };

  const filterSystemLogs = () => {
    if (!searchTerm) return systemLogs;

    return systemLogs.filter(log => {
      // Convert details to string if it's an object
      const detailsStr = typeof log.details === 'object'
        ? JSON.stringify(log.details)
        : String(log.details || '');

      return (
        log.username?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.log_type?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.related_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        detailsStr.toLowerCase().includes(searchTerm.toLowerCase())
      );
    });
  };

  // Calculate top accessors from all logs (access, edit, and login)
  const calculateTopAccessors = (accessLogs: AccessLog[]) => {
    console.log('Calculating top accessors from all logs');

    // Create a map to count all activities by username
    const activityCounts = new Map<string, number>();
    const userRoles = new Map<string, string>();

    // Process access logs
    accessLogs.forEach(log => {
      if (log.username) {
        // Increment the count for this username
        const currentCount = activityCounts.get(log.username) || 0;
        activityCounts.set(log.username, currentCount + 1);

        // Infer role from username or use user_role if available
        if (log.user_role) {
          userRoles.set(log.username, log.user_role);
        } else {
          userRoles.set(log.username, getUserRoleFromUsername(log.username));
        }
      }
    });

    // Process edit logs
    editLogs.forEach(log => {
      if (log.username) {
        // Increment the count for this username
        const currentCount = activityCounts.get(log.username) || 0;
        activityCounts.set(log.username, currentCount + 1);

        // Set role if not already set
        if (!userRoles.has(log.username)) {
          userRoles.set(log.username, getUserRoleFromUsername(log.username));
        }
      }
    });

    // Process login logs
    loginLogs.forEach(log => {
      if (log.username) {
        // Increment the count for this username
        const currentCount = activityCounts.get(log.username) || 0;
        activityCounts.set(log.username, currentCount + 1);

        // Set role if not already set
        if (!userRoles.has(log.username)) {
          userRoles.set(log.username, getUserRoleFromUsername(log.username));
        }
      }
    });

    console.log('Activity counts:', Array.from(activityCounts.entries()));

    // Convert map to array and sort by count (descending)
    const sortedUsers = Array.from(activityCounts.entries())
      .map(([username, count]) => ({
        username,
        count,
        role: userRoles.get(username) || getUserRoleFromUsername(username)
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5); // Take top 5

    console.log('Top users by activity:', sortedUsers);

    return sortedUsers;
  };

  // Helper function to extract role from username
  const getUserRoleFromUsername = (username: string): string => {
    if (username.startsWith('dr.')) return 'Doctor';
    if (username.startsWith('nurse.')) return 'Nurse';
    if (username.startsWith('admin.')) return 'Admin';
    return 'User';
  };

  // Calculate most edited records from edit logs
  const calculateMostEditedRecords = (logs: EditLog[]) => {
    console.log('Calculating most edited records from', logs.length, 'edit logs');

    // Create a map to count edits by patient name and track most common field
    const editCounts = new Map<string, { count: number, fields: Map<string, number>, patientId?: number }>();

    logs.forEach(log => {
      // Use patient_name or construct from first_name and last_name
      const patientName = log.patient_name ||
        (log.first_name && log.last_name ? `${log.first_name} ${log.last_name}` : 'Unknown Patient');

      // Get or initialize patient record
      const patientRecord = editCounts.get(patientName) || {
        count: 0,
        fields: new Map<string, number>(),
        patientId: log.patient_id
      };

      // Increment total count
      patientRecord.count += 1;

      // Track field changes
      if (log.field_name) {
        const fieldCount = patientRecord.fields.get(log.field_name) || 0;
        patientRecord.fields.set(log.field_name, fieldCount + 1);
      }

      // Update the map
      editCounts.set(patientName, patientRecord);
    });

    console.log('Edit counts:', Array.from(editCounts.entries()));

    // Convert map to array and sort by count (descending)
    const sortedRecords = Array.from(editCounts.entries())
      .map(([patientName, data]) => {
        // Find most commonly edited field
        let mostEditedField = '';
        let maxFieldCount = 0;

        data.fields.forEach((count, field) => {
          if (count > maxFieldCount) {
            maxFieldCount = count;
            mostEditedField = field;
          }
        });

        return {
          patientName,
          count: data.count,
          patientId: data.patientId,
          mostEditedField: mostEditedField || 'Various'
        };
      })
      .sort((a, b) => b.count - a.count)
      .slice(0, 4); // Take top 4

    console.log('Sorted records:', sortedRecords);

    // Don't use placeholder data - only show real patient records
    console.log('Final most edited records (without placeholders):', sortedRecords);

    return sortedRecords;
  };

  // Get trend direction icon and color
  const getTrendIndicator = (value: number, totalCount: number = 0) => {
    // If there are no events, don't show a trend
    if (totalCount === 0) {
      return {
        icon: null,
        color: theme.palette.text.secondary,
        text: 'No data'
      };
    }

    // For very small numbers, trends can be misleading
    if (totalCount < 5) {
      return {
        icon: <InfoIcon fontSize="small" />,
        color: theme.palette.info.main,
        text: 'Insufficient data'
      };
    }

    if (value > 0) {
      return {
        icon: <TrendingUpIcon fontSize="small" />,
        color: theme.palette.success.main,
        text: `+${value.toFixed(1)}%`
      };
    } else if (value < 0) {
      return {
        icon: <TrendingDownIcon fontSize="small" />,
        color: theme.palette.error.main,
        text: `${value.toFixed(1)}%`
      };
    } else {
      return {
        icon: <RemoveIcon fontSize="small" />,
        color: theme.palette.text.secondary,
        text: 'No change'
      };
    }
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Paper
          elevation={0}
          sx={{
            p: 3,
            mb: 4,
            borderRadius: 2,
            background: 'linear-gradient(to right, #1a237e, #3949ab)',
            color: 'white',
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          <Box sx={{
            position: 'absolute',
            top: 0,
            right: 0,
            width: '30%',
            height: '100%',
            opacity: 0.1,
            background: `url('https://www.transparenttextures.com/patterns/cubes.png')`,
            backgroundSize: 'cover'
          }} />

          <Grid container spacing={2} alignItems="center">
            <Grid item>
              <Avatar
                sx={{
                  width: 60,
                  height: 60,
                  bgcolor: 'white',
                  color: '#1a237e',
                  boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                }}
              >
                <SecurityIcon fontSize="large" />
              </Avatar>
            </Grid>
            <Grid item xs>
              <Typography variant="h4" fontWeight="500">
                Security & Audit
              </Typography>
              <Typography variant="subtitle1" sx={{ opacity: 0.9, mt: 0.5 }}>
                Loading security data...
              </Typography>
            </Grid>
          </Grid>
        </Paper>

        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 8 }}>
          <CircularProgress size={60} thickness={4} />
          <Typography variant="h6" sx={{ mt: 2 }}>
            Loading Security Logs
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
            Please wait while we gather the latest security information...
          </Typography>
        </Box>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 4,
          borderRadius: 2,
          background: 'linear-gradient(to right, #1a237e, #3949ab)',
          color: 'white',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        <Box sx={{
          position: 'absolute',
          top: 0,
          right: 0,
          width: '30%',
          height: '100%',
          opacity: 0.1,
          background: `url('https://www.transparenttextures.com/patterns/cubes.png')`,
          backgroundSize: 'cover'
        }} />

        <Grid container spacing={2} alignItems="center">
          <Grid item>
            <Avatar
              sx={{
                width: 60,
                height: 60,
                bgcolor: 'white',
                color: '#1a237e',
                boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
              }}
            >
              <SecurityIcon fontSize="large" />
            </Avatar>
          </Grid>
          <Grid item xs>
            <Typography variant="h4" fontWeight="500">
              Security & Audit
            </Typography>
            <Typography variant="subtitle1" sx={{ opacity: 0.9, mt: 0.5 }}>
              Monitor system security, user activity, and audit logs
            </Typography>
          </Grid>
          <Grid item>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={showEnhancedDashboard}
                    onChange={(e) => setShowEnhancedDashboard(e.target.checked)}
                    color="default"
                    sx={{
                      '& .MuiSwitch-switchBase.Mui-checked': {
                        color: 'white',
                      },
                      '& .MuiSwitch-switchBase.Mui-checked + .MuiSwitch-track': {
                        backgroundColor: 'rgba(255, 255, 255, 0.5)',
                      },
                    }}
                  />
                }
                label={
                  <Typography variant="body2" sx={{ color: 'white' }}>
                    Enhanced Dashboard
                  </Typography>
                }
              />
              {lastUpdated && (
                <Chip
                  icon={<InfoIcon />}
                  label={`Last updated: ${lastUpdated}`}
                  sx={{
                    bgcolor: 'rgba(255,255,255,0.15)',
                    color: 'white',
                    '& .MuiChip-icon': { color: 'white' }
                  }}
                />
              )}
              <Tooltip title="Refresh security data">
                <IconButton
                  color="inherit"
                  onClick={handleRefresh}
                  disabled={refreshing}
                  sx={{
                    bgcolor: 'rgba(255,255,255,0.1)',
                    '&:hover': { bgcolor: 'rgba(255,255,255,0.2)' }
                  }}
                >
                  {refreshing ? <CircularProgress size={24} color="inherit" /> : <RefreshIcon />}
                </IconButton>
              </Tooltip>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {error && (
        <Alert
          severity="error"
          sx={{
            mb: 3,
            borderRadius: 2,
            boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
          }}
        >
          {error}
        </Alert>
      )}

      {/* Enhanced Security Dashboard */}
      {showEnhancedDashboard ? (
        <EnhancedSecurityDashboard />
      ) : (
        <>
          {/* Search Bar */}
          <Paper
            elevation={0}
            sx={{
              p: 2,
              mb: 4,
              borderRadius: 2,
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
            }}
          >
        <Grid container spacing={2} alignItems="center">
          <Grid item md={6}>
            <TextField
              fullWidth
              placeholder="Search logs by username, patient, action..."
              variant="outlined"
              size="small"
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
                endAdornment: searchTerm && (
                  <InputAdornment position="end">
                    <IconButton size="small" onClick={() => setSearchTerm('')}>
                      <RefreshIcon fontSize="small" />
                    </IconButton>
                  </InputAdornment>
                ),
                sx: { borderRadius: 2 }
              }}
            />
          </Grid>
          <Grid item md={6}>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
              <Button
                variant="outlined"
                startIcon={<FilterListIcon />}
                size="small"
                sx={{ borderRadius: 2 }}
              >
                Filter
              </Button>
              <Button
                variant="outlined"
                startIcon={<RefreshIcon />}
                size="small"
                onClick={handleRefresh}
                sx={{ borderRadius: 2 }}
              >
                Refresh
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Security Metrics Dashboard */}
      {securityMetrics && (
        <Box sx={{ mb: 4 }}>
          <Typography variant="h5" gutterBottom sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            mb: 2,
            '&::after': {
              content: '""',
              display: 'block',
              height: '2px',
              background: 'linear-gradient(to right, #1a237e, transparent)',
              flexGrow: 1,
              ml: 2
            }
          }}>
            <SecurityIcon sx={{ mr: 1 }} />
            Security Overview
          </Typography>

          <Grid container spacing={3}>
            {/* Security Score */}
            <Grid item md={4}>
              <Card sx={{
                borderRadius: 2,
                boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                height: '100%'
              }}>
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Typography variant="h6" fontWeight="500">Security Score</Typography>
                    <Avatar sx={{
                      bgcolor: securityMetrics.securityScore > 90
                        ? alpha(theme.palette.success.main, 0.1)
                        : securityMetrics.securityScore > 70
                          ? alpha(theme.palette.warning.main, 0.1)
                          : alpha(theme.palette.error.main, 0.1),
                      color: securityMetrics.securityScore > 90
                        ? theme.palette.success.main
                        : securityMetrics.securityScore > 70
                          ? theme.palette.warning.main
                          : theme.palette.error.main
                    }}>
                      {securityMetrics.securityScore > 90
                        ? <CheckCircleIcon />
                        : securityMetrics.securityScore > 70
                          ? <WarningIcon />
                          : <ErrorIcon />}
                    </Avatar>
                  </Box>

                  <Box sx={{ position: 'relative', display: 'flex', justifyContent: 'center', mb: 2 }}>
                    <Box sx={{ position: 'relative', width: 120, height: 120 }}>
                      <CircularProgress
                        variant="determinate"
                        value={100}
                        size={120}
                        thickness={4}
                        sx={{ color: alpha(theme.palette.primary.main, 0.1), position: 'absolute' }}
                      />
                      <CircularProgress
                        variant="determinate"
                        value={securityMetrics.securityScore}
                        size={120}
                        thickness={4}
                        sx={{
                          color: securityMetrics.securityScore > 90
                            ? theme.palette.success.main
                            : securityMetrics.securityScore > 70
                              ? theme.palette.warning.main
                              : theme.palette.error.main,
                          position: 'absolute'
                        }}
                      />
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          bottom: 0,
                          right: 0,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <Typography variant="h4" fontWeight="bold">
                          {securityMetrics.securityScore}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>

                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="body1" fontWeight="500">
                      {securityMetrics.securityScore > 90
                        ? 'Excellent'
                        : securityMetrics.securityScore > 70
                          ? 'Good'
                          : 'Needs Attention'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                      {securityMetrics.securityScore > 90
                        ? 'Your system security is in excellent condition'
                        : securityMetrics.securityScore > 70
                          ? 'Your system security is good, but could be improved'
                          : 'Your system security needs immediate attention'}
                    </Typography>
                  </Box>

                  {securityMetrics.suspiciousActivities > 0 && (
                    <Box sx={{
                      mt: 2,
                      p: 1.5,
                      bgcolor: alpha(theme.palette.warning.main, 0.1),
                      borderRadius: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}>
                      <WarningIcon color="warning" />
                      <Typography variant="body2" fontWeight="500">
                        {securityMetrics.suspiciousActivities} suspicious {securityMetrics.suspiciousActivities === 1 ? 'activity' : 'activities'} detected
                      </Typography>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Activity Metrics */}
            <Grid item md={4}>
              <Card sx={{
                borderRadius: 2,
                boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                height: '100%'
              }}>
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Typography variant="h6" fontWeight="500">Activity Metrics</Typography>
                    <Avatar sx={{ bgcolor: alpha(theme.palette.info.main, 0.1), color: theme.palette.info.main }}>
                      <TimelineIcon />
                    </Avatar>
                  </Box>

                  {/* Activity Metrics Cards */}
                  <Grid container spacing={2} sx={{ mb: 2 }}>
                    {/* Access Events Card */}
                    <Grid item xs={4}>
                      <Paper
                        elevation={0}
                        sx={{
                          p: 1.5,
                          textAlign: 'center',
                          borderRadius: 2,
                          bgcolor: alpha(theme.palette.info.main, 0.05),
                          border: `1px solid ${alpha(theme.palette.info.main, 0.1)}`,
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center'
                        }}
                      >
                        <Box sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mb: 1,
                          color: theme.palette.info.main
                        }}>
                          <VisibilityIcon fontSize="small" sx={{ mr: 0.5 }} />
                          <Typography variant="body2" fontWeight="500" color="text.secondary">
                            Access Events
                          </Typography>
                        </Box>
                        <Typography variant="h5" fontWeight="500" color="info.main">
                          {securityMetrics.totalAccessEvents}
                        </Typography>
                        <Box sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mt: 0.5,
                          color: getTrendIndicator(securityMetrics.recentTrends.accessTrend, securityMetrics.totalAccessEvents).color
                        }}>
                          {getTrendIndicator(securityMetrics.recentTrends.accessTrend, securityMetrics.totalAccessEvents).icon}
                          <Typography variant="caption" sx={{ ml: 0.5 }}>
                            {getTrendIndicator(securityMetrics.recentTrends.accessTrend, securityMetrics.totalAccessEvents).text}
                          </Typography>
                        </Box>
                      </Paper>
                    </Grid>

                    {/* Edit Events Card */}
                    <Grid item xs={4}>
                      <Paper
                        elevation={0}
                        sx={{
                          p: 1.5,
                          textAlign: 'center',
                          borderRadius: 2,
                          bgcolor: alpha(theme.palette.warning.main, 0.05),
                          border: `1px solid ${alpha(theme.palette.warning.main, 0.1)}`,
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center'
                        }}
                      >
                        <Box sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mb: 1,
                          color: theme.palette.warning.main
                        }}>
                          <EditIcon fontSize="small" sx={{ mr: 0.5 }} />
                          <Typography variant="body2" fontWeight="500" color="text.secondary">
                            Edit Events
                          </Typography>
                        </Box>
                        <Typography variant="h5" fontWeight="500" color="warning.main">
                          {securityMetrics.totalEditEvents}
                        </Typography>
                        <Box sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mt: 0.5,
                          color: getTrendIndicator(securityMetrics.recentTrends.editTrend, securityMetrics.totalEditEvents).color
                        }}>
                          {getTrendIndicator(securityMetrics.recentTrends.editTrend, securityMetrics.totalEditEvents).icon}
                          <Typography variant="caption" sx={{ ml: 0.5 }}>
                            {getTrendIndicator(securityMetrics.recentTrends.editTrend, securityMetrics.totalEditEvents).text}
                          </Typography>
                        </Box>
                      </Paper>
                    </Grid>

                    {/* Login Events Card */}
                    <Grid item xs={4}>
                      <Paper
                        elevation={0}
                        sx={{
                          p: 1.5,
                          textAlign: 'center',
                          borderRadius: 2,
                          bgcolor: alpha(theme.palette.success.main, 0.05),
                          border: `1px solid ${alpha(theme.palette.success.main, 0.1)}`,
                          height: '100%',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'center'
                        }}
                      >
                        <Box sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mb: 1,
                          color: theme.palette.success.main
                        }}>
                          <LoginIcon fontSize="small" sx={{ mr: 0.5 }} />
                          <Typography variant="body2" fontWeight="500" color="text.secondary">
                            Login Events
                          </Typography>
                        </Box>
                        <Typography variant="h5" fontWeight="500" color="success.main">
                          {securityMetrics.totalLoginEvents}
                        </Typography>
                        <Box sx={{
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          mt: 0.5,
                          color: getTrendIndicator(securityMetrics.recentTrends.loginTrend, securityMetrics.totalLoginEvents).color
                        }}>
                          {getTrendIndicator(securityMetrics.recentTrends.loginTrend, securityMetrics.totalLoginEvents).icon}
                          <Typography variant="caption" sx={{ ml: 0.5 }}>
                            {getTrendIndicator(securityMetrics.recentTrends.loginTrend, securityMetrics.totalLoginEvents).text}
                          </Typography>
                        </Box>
                      </Paper>
                    </Grid>
                  </Grid>

                  {/* Activity Ratio */}
                  <Box sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" fontWeight="500">Activity Ratio</Typography>
                      <Typography variant="body2" color="text.secondary">
                        {securityMetrics.totalLoginEvents > 0
                          ? ((securityMetrics.totalAccessEvents + securityMetrics.totalEditEvents) / securityMetrics.totalLoginEvents).toFixed(1)
                          : "0.0"} actions per login
                      </Typography>
                    </Box>
                    <Box sx={{
                      p: 1.5,
                      borderRadius: 2,
                      bgcolor: alpha(theme.palette.primary.main, 0.05),
                      border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between'
                    }}>
                      {/* Activity distribution */}
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box sx={{
                          width: 12,
                          height: 12,
                          borderRadius: '50%',
                          bgcolor: theme.palette.info.main,
                          mr: 0.5
                        }} />
                        <Typography variant="caption" color="text.secondary" sx={{ mr: 1 }}>
                          Access: {securityMetrics.totalAccessEvents || 0}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box sx={{
                          width: 12,
                          height: 12,
                          borderRadius: '50%',
                          bgcolor: theme.palette.warning.main,
                          mr: 0.5
                        }} />
                        <Typography variant="caption" color="text.secondary" sx={{ mr: 1 }}>
                          Edit: {securityMetrics.totalEditEvents || 0}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box sx={{
                          width: 12,
                          height: 12,
                          borderRadius: '50%',
                          bgcolor: theme.palette.success.main,
                          mr: 0.5
                        }} />
                        <Typography variant="caption" color="text.secondary">
                          Login: {securityMetrics.totalLoginEvents || 0}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>

                  <Divider sx={{ my: 2 }} />

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="body2" fontWeight="500">
                      Top Users by Activity
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Total events across all logs
                    </Typography>
                  </Box>

                  {securityMetrics.topAccessors.length > 0 ? (
                    securityMetrics.topAccessors.slice(0, 4).map((user, index) => (
                      <Box
                        key={index}
                        sx={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          mb: 1,
                          p: 1,
                          borderRadius: 1,
                          bgcolor: index === 0 ? alpha(theme.palette.primary.main, 0.05) : 'transparent',
                          border: index === 0 ? `1px solid ${alpha(theme.palette.primary.main, 0.1)}` : 'none'
                        }}
                      >
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Avatar sx={{
                            width: 28,
                            height: 28,
                            fontSize: '0.75rem',
                            bgcolor: user.role === 'Doctor'
                              ? theme.palette.primary.main
                              : user.role === 'Nurse'
                                ? theme.palette.success.main
                                : user.role === 'Admin'
                                  ? theme.palette.warning.main
                                  : theme.palette.info.main
                          }}>
                            {user.username.charAt(0).toUpperCase()}
                          </Avatar>
                          <Box>
                            <Typography variant="body2" sx={{ lineHeight: 1.2, fontWeight: index === 0 ? 500 : 400 }}>
                              {user.username}
                            </Typography>
                            {user.role && (
                              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', lineHeight: 1 }}>
                                {user.role}
                              </Typography>
                            )}
                          </Box>
                        </Box>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Chip
                            label={user.count}
                            size="small"
                            color={index === 0 ? "primary" : index === 1 ? "info" : "default"}
                            sx={{
                              height: 22,
                              '& .MuiChip-label': { px: 1, py: 0.5, fontSize: '0.75rem', fontWeight: 500 }
                            }}
                          />
                          {index === 0 && (
                            <Tooltip title="Most active user">
                              <CheckCircleIcon
                                fontSize="small"
                                color="primary"
                                sx={{ ml: 0.5, width: 16, height: 16 }}
                              />
                            </Tooltip>
                          )}
                        </Box>
                      </Box>
                    ))
                  ) : (
                    <Box sx={{
                      p: 2,
                      textAlign: 'center',
                      bgcolor: alpha(theme.palette.info.main, 0.05),
                      borderRadius: 1
                    }}>
                      <Typography variant="body2" color="text.secondary">
                        No user activity recorded yet
                      </Typography>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Login Statistics */}
            <Grid item md={4}>
              <Card sx={{
                borderRadius: 2,
                boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                height: '100%'
              }}>
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Typography variant="h6" fontWeight="500">Login Statistics</Typography>
                    <Avatar sx={{ bgcolor: alpha(theme.palette.success.main, 0.1), color: theme.palette.success.main }}>
                      <LoginIcon />
                    </Avatar>
                  </Box>

                  {loginStats && (
                    <>
                      <Grid container spacing={2} sx={{ mb: 2 }}>
                        <Grid item xs={6}>
                          <Paper
                            elevation={0}
                            sx={{
                              p: 1.5,
                              textAlign: 'center',
                              borderRadius: 2,
                              bgcolor: alpha(theme.palette.primary.main, 0.05)
                            }}
                          >
                            <Typography variant="h5" fontWeight="500" color="primary.main">{loginStats.totalLogins}</Typography>
                            <Typography variant="caption" color="text.secondary">Total Logins</Typography>
                          </Paper>
                        </Grid>
                        <Grid item xs={6}>
                          <Paper
                            elevation={0}
                            sx={{
                              p: 1.5,
                              textAlign: 'center',
                              borderRadius: 2,
                              bgcolor: alpha(theme.palette.success.main, 0.05)
                            }}
                          >
                            <Typography variant="h5" fontWeight="500" color="success.main">{loginStats.successfulLogins}</Typography>
                            <Typography variant="caption" color="text.secondary">Successful</Typography>
                          </Paper>
                        </Grid>
                        <Grid item xs={6}>
                          <Paper
                            elevation={0}
                            sx={{
                              p: 1.5,
                              textAlign: 'center',
                              borderRadius: 2,
                              bgcolor: alpha(theme.palette.error.main, 0.05)
                            }}
                          >
                            <Typography variant="h5" fontWeight="500" color="error.main">{loginStats.failedLogins}</Typography>
                            <Typography variant="caption" color="text.secondary">Failed</Typography>
                          </Paper>
                        </Grid>
                        <Grid item xs={6}>
                          <Paper
                            elevation={0}
                            sx={{
                              p: 1.5,
                              textAlign: 'center',
                              borderRadius: 2,
                              bgcolor: alpha(theme.palette.warning.main, 0.05)
                            }}
                          >
                            <Typography variant="h5" fontWeight="500" color="warning.main">{loginStats.lockedAccounts}</Typography>
                            <Typography variant="caption" color="text.secondary">Locked Accounts</Typography>
                          </Paper>
                        </Grid>
                      </Grid>

                      {loginStats.loginTrend && (
                        <Box sx={{ mt: 3, height: 120 }}>
                          <Typography variant="body2" fontWeight="500" gutterBottom>
                            Login Trend (Last 14 Days)
                          </Typography>
                          <Line
                            data={{
                              labels: loginStats.loginTrend.map(item => formatDateShort(item.date)),
                              datasets: [
                                {
                                  label: 'Successful',
                                  data: loginStats.loginTrend.map(item => item.successful),
                                  borderColor: theme.palette.success.main,
                                  backgroundColor: alpha(theme.palette.success.main, 0.1),
                                  tension: 0.3,
                                  fill: false,
                                  borderWidth: 2,
                                  pointRadius: 0,
                                  pointHoverRadius: 3
                                },
                                {
                                  label: 'Failed',
                                  data: loginStats.loginTrend.map(item => item.failed),
                                  borderColor: theme.palette.error.main,
                                  backgroundColor: alpha(theme.palette.error.main, 0.1),
                                  tension: 0.3,
                                  fill: false,
                                  borderWidth: 2,
                                  pointRadius: 0,
                                  pointHoverRadius: 3
                                }
                              ]
                            }}
                            options={{
                              responsive: true,
                              maintainAspectRatio: false,
                              plugins: {
                                legend: {
                                  display: true,
                                  position: 'top',
                                  labels: {
                                    boxWidth: 10,
                                    usePointStyle: true,
                                    pointStyle: 'circle'
                                  }
                                },
                                tooltip: {
                                  mode: 'index',
                                  intersect: false
                                }
                              },
                              scales: {
                                x: {
                                  grid: {
                                    display: false
                                  },
                                  ticks: {
                                    maxRotation: 0,
                                    autoSkip: true,
                                    maxTicksLimit: 5
                                  }
                                },
                                y: {
                                  beginAtZero: true,
                                  border: {
                                    display: false
                                  },
                                  grid: {
                                    display: true
                                  },
                                  ticks: {
                                    precision: 0
                                  }
                                }
                              }
                            }}
                          />
                        </Box>
                      )}
                    </>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      )}

      <Paper
        elevation={0}
        sx={{
          width: '100%',
          mb: 2,
          borderRadius: 2,
          boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
          overflow: 'hidden'
        }}
      >
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          variant="scrollable"
          scrollButtons="auto"
          sx={{
            borderBottom: '1px solid #eee',
            '& .MuiTab-root': {
              minHeight: 64,
              py: 2,
              px: 3,
              fontWeight: 500,
              transition: 'all 0.2s ease',
              '&:hover': {
                bgcolor: 'rgba(0, 0, 0, 0.03)'
              }
            },
            '& .Mui-selected': {
              fontWeight: 600,
              color: theme.palette.primary.main,
              '&::after': {
                content: '""',
                position: 'absolute',
                bottom: 0,
                left: 0,
                width: '100%',
                height: 3,
                bgcolor: theme.palette.primary.main,
                borderTopLeftRadius: 3,
                borderTopRightRadius: 3
              }
            }
          }}
        >
          <Tab
            label="Access Logs"
            icon={<VisibilityIcon />}
            iconPosition="start"
          />
          <Tab
            label="Edit Logs"
            icon={<EditIcon />}
            iconPosition="start"
          />
          <Tab
            label="Login Activity"
            icon={<LoginIcon />}
            iconPosition="start"
          />
          <Tab
            label="Deletion Logs"
            icon={<RemoveIcon />}
            iconPosition="start"
          />
        </Tabs>

        {/* Access Logs Tab */}
        <TabPanel value={tabValue} index={0}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Box>
              <Typography variant="h6" fontWeight="500" gutterBottom>
                Patient Access Logs
              </Typography>
              <Typography variant="body2" color="text.secondary">
                These logs record when a patient's record was accessed, by whom, and in what context.
              </Typography>
            </Box>
            <Badge
              badgeContent={filterAccessLogs().length}
              color="primary"
              sx={{ '& .MuiBadge-badge': { fontSize: '0.9rem', height: 22, minWidth: 22 } }}
            >
              <Chip
                icon={<VisibilityIcon />}
                label="Access Events"
                sx={{
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  color: theme.palette.primary.main,
                  fontWeight: 500,
                  px: 1
                }}
              />
            </Badge>
          </Box>

          <TableContainer
            component={Paper}
            elevation={0}
            sx={{
              borderRadius: 2,
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
              mb: 2,
              overflow: 'hidden'
            }}
          >
            <Table sx={{ minWidth: 650 }} aria-label="access logs table">
              <TableHead>
                <TableRow sx={{ bgcolor: alpha(theme.palette.primary.main, 0.03) }}>
                  <TableCell sx={{ fontWeight: 600 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <PersonIcon fontSize="small" color="primary" />
                      User
                    </Box>
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <PatientIcon fontSize="small" color="primary" />
                      Patient
                    </Box>
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <AccessTimeIcon fontSize="small" color="primary" />
                      Access Time
                    </Box>
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <InfoIcon fontSize="small" color="primary" />
                      Context
                    </Box>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filterAccessLogs().length > 0 ? (
                  filterAccessLogs()
                    .slice(accessPage * rowsPerPage, accessPage * rowsPerPage + rowsPerPage)
                    .map((log) => (
                      <TableRow
                        key={log.log_id}
                        sx={{
                          '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.02) },
                          transition: 'background-color 0.2s ease'
                        }}
                      >
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Avatar sx={{ width: 28, height: 28, fontSize: '0.8rem', bgcolor: theme.palette.primary.main }}>
                              {log.username.charAt(0).toUpperCase()}
                            </Avatar>
                            <Typography variant="body2">{log.username}</Typography>
                          </Box>
                        </TableCell>
                        <TableCell>{log.patient_name}</TableCell>
                        <TableCell>{formatDate(log.access_time)}</TableCell>
                        <TableCell>
                          <Chip
                            label={log.access_context}
                            color={
                              log.access_context === 'View' ? 'info' :
                              log.access_context === 'Update' ? 'primary' :
                              log.access_context === 'Report' ? 'success' : 'default'
                            }
                            size="small"
                            sx={{ borderRadius: 1, fontWeight: 500 }}
                          />
                        </TableCell>
                      </TableRow>
                    ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={4} align="center" sx={{ py: 4 }}>
                      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                        <VisibilityIcon sx={{ fontSize: 40, color: alpha(theme.palette.text.secondary, 0.4) }} />
                        <Typography variant="body1" color="text.secondary">No access logs found</Typography>
                        {searchTerm && (
                          <Button
                            size="small"
                            startIcon={<RefreshIcon />}
                            onClick={() => setSearchTerm('')}
                            sx={{ mt: 1 }}
                          >
                            Clear search
                          </Button>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>

          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={filterAccessLogs().length}
            rowsPerPage={rowsPerPage}
            page={accessPage}
            onPageChange={handleChangeAccessPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            sx={{
              borderRadius: 2,
              overflow: 'hidden',
              '.MuiTablePagination-selectLabel, .MuiTablePagination-displayedRows': {
                m: 0
              }
            }}
          />
        </TabPanel>

        {/* Edit Logs Tab */}
        <TabPanel value={tabValue} index={1}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Box>
              <Typography variant="h6" fontWeight="500" gutterBottom>
                Patient Edit Logs
              </Typography>
              <Typography variant="body2" color="text.secondary">
                These logs record changes made to patient records, tracking what was changed, by whom, and when.
              </Typography>
            </Box>
            <Badge
              badgeContent={filterEditLogs().length}
              color="primary"
              sx={{ '& .MuiBadge-badge': { fontSize: '0.9rem', height: 22, minWidth: 22 } }}
            >
              <Chip
                icon={<EditIcon />}
                label="Edit Events"
                sx={{
                  bgcolor: alpha(theme.palette.warning.main, 0.1),
                  color: theme.palette.warning.main,
                  fontWeight: 500,
                  px: 1
                }}
              />
            </Badge>
          </Box>

          <TableContainer
            component={Paper}
            elevation={0}
            sx={{
              borderRadius: 2,
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
              mb: 2,
              overflow: 'hidden'
            }}
          >
            <Table sx={{ minWidth: 650 }} aria-label="edit logs table">
              <TableHead>
                <TableRow sx={{ bgcolor: alpha(theme.palette.warning.main, 0.03) }}>
                  <TableCell sx={{ fontWeight: 600 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <PersonIcon fontSize="small" color="warning" />
                      User
                    </Box>
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <PatientIcon fontSize="small" color="warning" />
                      Patient
                    </Box>
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <InfoIcon fontSize="small" color="warning" />
                      Field
                    </Box>
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Old Value</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>New Value</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <AccessTimeIcon fontSize="small" color="warning" />
                      Edit Time
                    </Box>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filterEditLogs().length > 0 ? (
                  filterEditLogs()
                    .slice(editPage * rowsPerPage, editPage * rowsPerPage + rowsPerPage)
                    .map((log, index) => (
                      <TableRow
                        key={log.edit_id || `edit-${index}`}
                        sx={{
                          '&:hover': { bgcolor: alpha(theme.palette.warning.main, 0.02) },
                          transition: 'background-color 0.2s ease',
                          bgcolor: log.edit_type === 'delete'
                            ? log.field_changed === 'user_account'
                              ? alpha(theme.palette.error.main, 0.08)
                              : log.field_changed === 'visit'
                                ? alpha(theme.palette.warning.main, 0.08)
                                : alpha(theme.palette.error.main, 0.05)
                            : 'inherit'
                        }}
                      >
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Avatar sx={{ width: 28, height: 28, fontSize: '0.8rem', bgcolor: theme.palette.warning.main }}>
                              {log.username.charAt(0).toUpperCase()}
                            </Avatar>
                            <Typography variant="body2">{log.username}</Typography>
                          </Box>
                        </TableCell>
                        <TableCell>{log.patient_name}</TableCell>
                        <TableCell>
                          <Chip
                            label={log.field_name}
                            size="small"
                            sx={{
                              borderRadius: 1,
                              bgcolor: log.edit_type === 'delete'
                                ? log.field_changed === 'user_account'
                                  ? alpha(theme.palette.error.main, 0.15)
                                  : log.field_changed === 'visit'
                                    ? alpha(theme.palette.warning.main, 0.15)
                                    : alpha(theme.palette.error.main, 0.1)
                                : alpha(theme.palette.info.main, 0.1),
                              color: log.edit_type === 'delete'
                                ? log.field_changed === 'user_account'
                                  ? theme.palette.error.dark
                                  : log.field_changed === 'visit'
                                    ? theme.palette.warning.dark
                                    : theme.palette.error.main
                                : theme.palette.info.main,
                              fontWeight: 500
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <Box
                            sx={{
                              p: 1,
                              bgcolor: alpha(theme.palette.error.light, 0.05),
                              borderRadius: 1,
                              maxWidth: 200,
                              overflow: 'hidden',
                              textOverflow: 'ellipsis'
                            }}
                          >
                            {log.old_value || '(empty)'}
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box
                            sx={{
                              p: 1,
                              bgcolor: alpha(theme.palette.success.light, 0.05),
                              borderRadius: 1,
                              maxWidth: 200,
                              overflow: 'hidden',
                              textOverflow: 'ellipsis'
                            }}
                          >
                            {log.new_value || '(empty)'}
                          </Box>
                        </TableCell>
                        <TableCell>{formatDate(log.edit_time)}</TableCell>
                      </TableRow>
                    ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} align="center" sx={{ py: 4 }}>
                      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                        <EditIcon sx={{ fontSize: 40, color: alpha(theme.palette.text.secondary, 0.4) }} />
                        <Typography variant="body1" color="text.secondary">No edit logs found</Typography>
                        {searchTerm && (
                          <Button
                            size="small"
                            startIcon={<RefreshIcon />}
                            onClick={() => setSearchTerm('')}
                            sx={{ mt: 1 }}
                          >
                            Clear search
                          </Button>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>

          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={filterEditLogs().length}
            rowsPerPage={rowsPerPage}
            page={editPage}
            onPageChange={handleChangeEditPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            sx={{
              borderRadius: 2,
              overflow: 'hidden',
              '.MuiTablePagination-selectLabel, .MuiTablePagination-displayedRows': {
                m: 0
              }
            }}
          />
        </TabPanel>

        {/* Login Activity Tab */}
        <TabPanel value={tabValue} index={2}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Box>
              <Typography variant="h6" fontWeight="500" gutterBottom>
                Login Activity Logs
              </Typography>
              <Typography variant="body2" color="text.secondary">
                These logs record login attempts, including successful logins, failed attempts, and account lockouts.
              </Typography>
            </Box>
            <Badge
              badgeContent={filterLoginLogs().length}
              color="primary"
              sx={{ '& .MuiBadge-badge': { fontSize: '0.9rem', height: 22, minWidth: 22 } }}
            >
              <Chip
                icon={<LoginIcon />}
                label="Login Events"
                sx={{
                  bgcolor: alpha(theme.palette.success.main, 0.1),
                  color: theme.palette.success.main,
                  fontWeight: 500,
                  px: 1
                }}
              />
            </Badge>
          </Box>

          {loginStats && (
            <Grid container spacing={2} sx={{ mb: 3 }}>
              <Grid item xs={6} sm={3}>
                <Card sx={{
                  borderRadius: 2,
                  boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                  bgcolor: alpha(theme.palette.info.main, 0.1),
                  transition: 'transform 0.3s ease',
                  '&:hover': { transform: 'translateY(-5px)' }
                }}>
                  <CardContent sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">Total Logins</Typography>
                      <Avatar sx={{ width: 32, height: 32, bgcolor: theme.palette.info.main }}>
                        <LoginIcon fontSize="small" />
                      </Avatar>
                    </Box>
                    <Typography variant="h4" fontWeight="500" color="info.main">{loginStats.totalLogins}</Typography>
                    <Typography variant="caption" color="text.secondary">All login attempts</Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Card sx={{
                  borderRadius: 2,
                  boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                  bgcolor: alpha(theme.palette.success.main, 0.1),
                  transition: 'transform 0.3s ease',
                  '&:hover': { transform: 'translateY(-5px)' }
                }}>
                  <CardContent sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">Successful</Typography>
                      <Avatar sx={{ width: 32, height: 32, bgcolor: theme.palette.success.main }}>
                        <CheckCircleIcon fontSize="small" />
                      </Avatar>
                    </Box>
                    <Typography variant="h4" fontWeight="500" color="success.main">{loginStats.successfulLogins}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      {Math.round((loginStats.successfulLogins / loginStats.totalLogins) * 100)}% success rate
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Card sx={{
                  borderRadius: 2,
                  boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                  bgcolor: alpha(theme.palette.error.main, 0.1),
                  transition: 'transform 0.3s ease',
                  '&:hover': { transform: 'translateY(-5px)' }
                }}>
                  <CardContent sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">Failed</Typography>
                      <Avatar sx={{ width: 32, height: 32, bgcolor: theme.palette.error.main }}>
                        <ErrorIcon fontSize="small" />
                      </Avatar>
                    </Box>
                    <Typography variant="h4" fontWeight="500" color="error.main">{loginStats.failedLogins}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      {Math.round((loginStats.failedLogins / loginStats.totalLogins) * 100)}% failure rate
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6} sm={3}>
                <Card sx={{
                  borderRadius: 2,
                  boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                  bgcolor: alpha(theme.palette.warning.main, 0.1),
                  transition: 'transform 0.3s ease',
                  '&:hover': { transform: 'translateY(-5px)' }
                }}>
                  <CardContent sx={{ p: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">Locked</Typography>
                      <Avatar sx={{ width: 32, height: 32, bgcolor: theme.palette.warning.main }}>
                        <WarningIcon fontSize="small" />
                      </Avatar>
                    </Box>
                    <Typography variant="h4" fontWeight="500" color="warning.main">{loginStats.lockedAccounts}</Typography>
                    <Typography variant="caption" color="text.secondary">Accounts locked out</Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          <TableContainer
            component={Paper}
            elevation={0}
            sx={{
              borderRadius: 2,
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
              mb: 2,
              overflow: 'hidden'
            }}
          >
            <Table sx={{ minWidth: 650 }} aria-label="login logs table">
              <TableHead>
                <TableRow sx={{ bgcolor: alpha(theme.palette.success.main, 0.03) }}>
                  <TableCell sx={{ fontWeight: 600 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <PersonIcon fontSize="small" color="success" />
                      Username
                    </Box>
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <PublicIcon fontSize="small" color="success" />
                      IP Address
                    </Box>
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <InfoIcon fontSize="small" color="success" />
                      Status
                    </Box>
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Details</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <AccessTimeIcon fontSize="small" color="success" />
                      Timestamp
                    </Box>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filterLoginLogs().length > 0 ? (
                  filterLoginLogs()
                    .slice(loginPage * rowsPerPage, loginPage * rowsPerPage + rowsPerPage)
                    .map((log) => (
                      <TableRow
                        key={log.log_id}
                        sx={{
                          '&:hover': { bgcolor: alpha(theme.palette.success.main, 0.02) },
                          transition: 'background-color 0.2s ease',
                          bgcolor: log.status === 'locked' ? alpha(theme.palette.warning.main, 0.05) : 'inherit'
                        }}
                      >
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Avatar
                              sx={{
                                width: 28,
                                height: 28,
                                fontSize: '0.8rem',
                                bgcolor: log.status === 'success'
                                  ? theme.palette.success.main
                                  : log.status === 'failed'
                                    ? theme.palette.error.main
                                    : theme.palette.warning.main
                              }}
                            >
                              {log.username.charAt(0).toUpperCase()}
                            </Avatar>
                            <Typography variant="body2">{log.username}</Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={log.ip_address}
                            size="small"
                            icon={<PublicIcon fontSize="small" />}
                            sx={{
                              borderRadius: 1,
                              bgcolor: alpha(theme.palette.info.main, 0.1),
                              color: theme.palette.info.main,
                              fontWeight: 500
                            }}
                          />
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={log.status.charAt(0).toUpperCase() + log.status.slice(1)}
                            color={
                              log.status === 'success' ? 'success' :
                              log.status === 'failed' ? 'error' :
                              log.status === 'locked' ? 'warning' : 'default'
                            }
                            size="small"
                            sx={{ borderRadius: 1, fontWeight: 500 }}
                          />
                        </TableCell>
                        <TableCell>
                          {log.details ? (
                            <Typography variant="body2">{log.details}</Typography>
                          ) : (
                            <Typography variant="body2" color="text.secondary" fontStyle="italic">(no details)</Typography>
                          )}
                        </TableCell>
                        <TableCell>{formatDate(log.timestamp)}</TableCell>
                      </TableRow>
                    ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} align="center" sx={{ py: 4 }}>
                      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                        <LoginIcon sx={{ fontSize: 40, color: alpha(theme.palette.text.secondary, 0.4) }} />
                        <Typography variant="body1" color="text.secondary">No login logs found</Typography>
                        {searchTerm && (
                          <Button
                            size="small"
                            startIcon={<RefreshIcon />}
                            onClick={() => setSearchTerm('')}
                            sx={{ mt: 1 }}
                          >
                            Clear search
                          </Button>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>

          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={filterLoginLogs().length}
            rowsPerPage={rowsPerPage}
            page={loginPage}
            onPageChange={handleChangeLoginPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            sx={{
              borderRadius: 2,
              overflow: 'hidden',
              '.MuiTablePagination-selectLabel, .MuiTablePagination-displayedRows': {
                m: 0
              }
            }}
          />
        </TabPanel>

        {/* System Logs Tab */}
        <TabPanel value={tabValue} index={3}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Box>
              <Typography variant="h6" fontWeight="500" gutterBottom>
                System Deletion Logs
              </Typography>
              <Typography variant="body2" color="text.secondary">
                These logs record system-level events such as patient deletions and visit deletions.
              </Typography>
            </Box>
            <Badge
              badgeContent={filterSystemLogs().length}
              color="primary"
              sx={{ '& .MuiBadge-badge': { fontSize: '0.9rem', height: 22, minWidth: 22 } }}
            >
              <Chip
                icon={<RemoveIcon />}
                label="Deletion Events"
                sx={{
                  bgcolor: alpha(theme.palette.error.main, 0.1),
                  color: theme.palette.error.main,
                  fontWeight: 500,
                  px: 1
                }}
              />
            </Badge>
          </Box>

          <TableContainer
            component={Paper}
            elevation={0}
            sx={{
              borderRadius: 2,
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
              mb: 2,
              overflow: 'hidden'
            }}
          >
            <Table sx={{ minWidth: 650 }} aria-label="system logs table">
              <TableHead>
                <TableRow sx={{ bgcolor: alpha(theme.palette.error.main, 0.03) }}>
                  <TableCell sx={{ fontWeight: 600 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <PersonIcon fontSize="small" color="error" />
                      User
                    </Box>
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <InfoIcon fontSize="small" color="error" />
                      Event Type
                    </Box>
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <PatientIcon fontSize="small" color="error" />
                      Related Entity
                    </Box>
                  </TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>Details</TableCell>
                  <TableCell sx={{ fontWeight: 600 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <AccessTimeIcon fontSize="small" color="error" />
                      Timestamp
                    </Box>
                  </TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filterSystemLogs().length > 0 ? (
                  filterSystemLogs()
                    .slice(systemPage * rowsPerPage, systemPage * rowsPerPage + rowsPerPage)
                    .map((log) => (
                      <TableRow
                        key={log.log_id}
                        sx={{
                          '&:hover': { bgcolor: alpha(theme.palette.error.main, 0.02) },
                          transition: 'background-color 0.2s ease',
                          bgcolor: log.log_type === 'patient_deletion'
                            ? alpha(theme.palette.error.main, 0.05)
                            : alpha(theme.palette.warning.main, 0.05)
                        }}
                      >
                        <TableCell>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Avatar sx={{ width: 28, height: 28, fontSize: '0.8rem', bgcolor: theme.palette.error.main }}>
                              {log.username?.charAt(0).toUpperCase() || 'U'}
                            </Avatar>
                            <Typography variant="body2">{log.username || 'Unknown'}</Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={log.log_type === 'patient_deletion'
                              ? 'Patient Deletion'
                              : log.log_type === 'visit_deletion'
                                ? 'Visit Deletion'
                                : log.log_type}
                            color={log.log_type === 'patient_deletion' ? 'error' : 'warning'}
                            size="small"
                            sx={{ borderRadius: 1, fontWeight: 500 }}
                          />
                        </TableCell>
                        <TableCell>
                          <Typography variant="body2" fontWeight={500}>
                            {log.related_name || 'Unknown'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            ID: {log.related_id}
                          </Typography>
                        </TableCell>
                        <TableCell>
                          {log.details ? (
                            <Tooltip title={JSON.stringify(log.details, null, 2)}>
                              <Typography variant="body2" sx={{
                                maxWidth: 200,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap'
                              }}>
                                {typeof log.details === 'object'
                                  ? JSON.stringify(log.details).substring(0, 50) + '...'
                                  : String(log.details).substring(0, 50) + '...'}
                              </Typography>
                            </Tooltip>
                          ) : (
                            <Typography variant="body2" color="text.secondary" fontStyle="italic">(no details)</Typography>
                          )}
                        </TableCell>
                        <TableCell>{formatDate(log.log_time)}</TableCell>
                      </TableRow>
                    ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} align="center" sx={{ py: 4 }}>
                      <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                        <RemoveIcon sx={{ fontSize: 40, color: alpha(theme.palette.text.secondary, 0.4) }} />
                        <Typography variant="body1" color="text.secondary">No system logs found</Typography>
                        {searchTerm && (
                          <Button
                            size="small"
                            startIcon={<RefreshIcon />}
                            onClick={() => setSearchTerm('')}
                            sx={{ mt: 1 }}
                          >
                            Clear search
                          </Button>
                        )}
                      </Box>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </TableContainer>

          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={filterSystemLogs().length}
            rowsPerPage={rowsPerPage}
            page={systemPage}
            onPageChange={handleChangeSystemPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            sx={{
              borderRadius: 2,
              overflow: 'hidden',
              '.MuiTablePagination-selectLabel, .MuiTablePagination-displayedRows': {
                m: 0
              }
            }}
          />
        </TabPanel>
      </Paper>
        </>
      )}
    </Box>
  );
};

export default SecurityLogs;