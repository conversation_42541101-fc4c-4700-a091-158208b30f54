import React, { useState } from 'react';
import {
  Box,
  <PERSON>ton,
  <PERSON>alog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  Typography,
  Divider,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Paper,
  TextField,
  FormControl,
  FormControlLabel,
  Checkbox,
  Grid,
  IconButton,
  Tooltip,
  Stepper,
  Step,
  StepLabel,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Avatar
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  CloudDownload as DownloadIcon,
  Description as FileIcon,
  Check as CheckIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
  Help as HelpIcon,
  Delete as DeleteIcon,
  FileDownload as TemplateIcon
} from '@mui/icons-material';
import { API_URL } from '../../config';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`import-export-tabpanel-${index}`}
      aria-labelledby={`import-export-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface User {
  username: string;
  email: string;
  role: string;
  password?: string;
}

interface ImportResult {
  success: User[];
  failed: { user: User; reason: string }[];
  total: number;
}

interface UserImportExportProps {
  open: boolean;
  onClose: () => void;
  onOperationComplete: () => void;
}

const UserImportExport: React.FC<UserImportExportProps> = ({
  open,
  onClose,
  onOperationComplete
}) => {
  const [tabValue, setTabValue] = useState(0);
  const [file, setFile] = useState<File | null>(null);
  const [fileContent, setFileContent] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [importStep, setImportStep] = useState(0);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [exportFormat, setExportFormat] = useState<'csv' | 'json'>('csv');
  const [includePasswords, setIncludePasswords] = useState(false);
  const [exportRoles, setExportRoles] = useState<string[]>(['admin', 'doctor', 'staff', 'patient', 'kin', 'researcher']);
  const [parsedUsers, setParsedUsers] = useState<User[]>([]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    // Reset states when switching tabs
    setFile(null);
    setFileContent('');
    setError(null);
    setImportStep(0);
    setImportResult(null);
  };

  const handleClose = () => {
    if (!loading) {
      onClose();
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const selectedFile = files[0];
      setFile(selectedFile);
      
      // Read file content
      const reader = new FileReader();
      reader.onload = (e) => {
        const content = e.target?.result as string;
        setFileContent(content);
        
        try {
          // Try to parse the file content
          const fileExt = selectedFile.name.split('.').pop()?.toLowerCase();
          if (fileExt === 'json') {
            const parsed = JSON.parse(content);
            if (Array.isArray(parsed)) {
              setParsedUsers(parsed);
            } else {
              throw new Error('JSON file must contain an array of users');
            }
          } else if (fileExt === 'csv') {
            const lines = content.split('\n');
            const headers = lines[0].split(',').map(h => h.trim());
            
            // Validate required headers
            const requiredHeaders = ['username', 'email', 'role'];
            const missingHeaders = requiredHeaders.filter(h => !headers.includes(h));
            
            if (missingHeaders.length > 0) {
              throw new Error(`CSV is missing required headers: ${missingHeaders.join(', ')}`);
            }
            
            const users: User[] = [];
            for (let i = 1; i < lines.length; i++) {
              if (!lines[i].trim()) continue;
              
              const values = lines[i].split(',').map(v => v.trim());
              const user: any = {};
              
              headers.forEach((header, index) => {
                user[header] = values[index] || '';
              });
              
              users.push(user);
            }
            
            setParsedUsers(users);
          } else {
            throw new Error('Unsupported file format. Please upload a CSV or JSON file.');
          }
          
          setImportStep(1);
          setError(null);
        } catch (err) {
          setError(err instanceof Error ? err.message : 'Failed to parse file');
          setParsedUsers([]);
        }
      };
      
      reader.onerror = () => {
        setError('Failed to read file');
      };
      
      reader.readAsText(selectedFile);
    }
  };

  const handleImport = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(`${API_URL}/api/admin/users/import`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: JSON.stringify({ users: parsedUsers })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.msg || 'Import failed');
      }

      const result = await response.json();
      setImportResult(result);
      setImportStep(2);

      // If all imports were successful, call the onOperationComplete callback
      if (result.failed.length === 0) {
        setTimeout(() => {
          onOperationComplete();
        }, 1000);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred during import');
      console.error('Error importing users:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const queryParams = new URLSearchParams();
      queryParams.append('format', exportFormat);
      queryParams.append('include_passwords', includePasswords.toString());
      
      if (exportRoles.length > 0 && exportRoles.length < 6) {
        queryParams.append('roles', exportRoles.join(','));
      }

      const response = await fetch(`${API_URL}/api/admin/users/export?${queryParams.toString()}`, {
        headers: {
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.msg || 'Export failed');
      }

      // Get the filename from the Content-Disposition header or use a default
      const contentDisposition = response.headers.get('Content-Disposition');
      let filename = 'users';
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename="(.+)"/);
        if (filenameMatch) {
          filename = filenameMatch[1];
        }
      }

      // Add appropriate extension
      if (!filename.endsWith(`.${exportFormat}`)) {
        filename += `.${exportFormat}`;
      }

      // Create a download link and trigger the download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      setLoading(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred during export');
      console.error('Error exporting users:', err);
      setLoading(false);
    }
  };

  const downloadTemplate = (format: 'csv' | 'json') => {
    let content = '';
    let filename = '';
    let mimeType = '';

    if (format === 'csv') {
      content = 'username,email,role,password\n';
      content += 'john_doe,<EMAIL>,staff,password123\n';
      content += 'jane_smith,<EMAIL>,doctor,password456\n';
      filename = 'user_import_template.csv';
      mimeType = 'text/csv';
    } else {
      const template = [
        {
          username: 'john_doe',
          email: '<EMAIL>',
          role: 'staff',
          password: 'password123'
        },
        {
          username: 'jane_smith',
          email: '<EMAIL>',
          role: 'doctor',
          password: 'password456'
        }
      ];
      content = JSON.stringify(template, null, 2);
      filename = 'user_import_template.json';
      mimeType = 'application/json';
    }

    const blob = new Blob([content], { type: mimeType });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  };

  const handleRoleToggle = (role: string) => {
    setExportRoles(prev => {
      if (prev.includes(role)) {
        return prev.filter(r => r !== role);
      } else {
        return [...prev, role];
      }
    });
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{ sx: { borderRadius: 2 } }}
    >
      <DialogTitle sx={{ pb: 1, borderBottom: '1px solid #eee' }}>
        <Typography variant="h6">User Import & Export</Typography>
      </DialogTitle>
      <DialogContent sx={{ pt: 2, pb: 2, px: 3 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{
            mb: 2,
            '& .MuiTab-root': {
              py: 1.5,
              transition: 'all 0.2s ease',
              '&:hover': {
                bgcolor: 'rgba(0,0,0,0.02)'
              }
            },
            '& .Mui-selected': {
              fontWeight: 'bold'
            }
          }}
        >
          <Tab
            icon={<UploadIcon />}
            label="Import Users"
            id="import-export-tab-0"
            aria-controls="import-export-tabpanel-0"
            iconPosition="start"
          />
          <Tab
            icon={<DownloadIcon />}
            label="Export Users"
            id="import-export-tab-1"
            aria-controls="import-export-tabpanel-1"
            iconPosition="start"
          />
        </Tabs>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        <TabPanel value={tabValue} index={0}>
          <Stepper activeStep={importStep} sx={{ mb: 4 }}>
            <Step>
              <StepLabel>Upload File</StepLabel>
            </Step>
            <Step>
              <StepLabel>Review Data</StepLabel>
            </Step>
            <Step>
              <StepLabel>Import Results</StepLabel>
            </Step>
          </Stepper>

          {importStep === 0 && (
            <Box>
              <Paper
                elevation={0}
                sx={{
                  p: 3,
                  mb: 3,
                  borderRadius: 2,
                  border: '2px dashed #ccc',
                  textAlign: 'center',
                  bgcolor: 'background.default'
                }}
              >
                <input
                  type="file"
                  accept=".csv,.json"
                  id="user-import-file"
                  style={{ display: 'none' }}
                  onChange={handleFileChange}
                />
                <label htmlFor="user-import-file">
                  <Button
                    variant="contained"
                    component="span"
                    startIcon={<UploadIcon />}
                    sx={{ mb: 2 }}
                  >
                    Select File
                  </Button>
                </label>
                <Typography variant="body2" color="text.secondary">
                  {file ? `Selected: ${file.name}` : 'Upload a CSV or JSON file containing user data'}
                </Typography>
              </Paper>

              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" fontWeight="500" sx={{ mb: 1 }}>
                  Download Template
                </Typography>
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <Button
                    variant="outlined"
                    startIcon={<TemplateIcon />}
                    onClick={() => downloadTemplate('csv')}
                    size="small"
                  >
                    CSV Template
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<TemplateIcon />}
                    onClick={() => downloadTemplate('json')}
                    size="small"
                  >
                    JSON Template
                  </Button>
                </Box>
              </Box>

              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  borderRadius: 2,
                  bgcolor: 'info.light',
                  color: 'info.contrastText'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                  <InfoIcon sx={{ mt: 0.5 }} />
                  <Box>
                    <Typography variant="body2" fontWeight="500">
                      Import Format Requirements:
                    </Typography>
                    <Typography variant="body2">
                      • CSV files must include headers: username, email, role (password is optional)
                    </Typography>
                    <Typography variant="body2">
                      • JSON files must contain an array of user objects with the same fields
                    </Typography>
                    <Typography variant="body2">
                      • Valid roles: admin, doctor, staff, patient, kin, researcher
                    </Typography>
                  </Box>
                </Box>
              </Paper>
            </Box>
          )}

          {importStep === 1 && (
            <Box>
              <Typography variant="subtitle1" fontWeight="500" sx={{ mb: 2 }}>
                Review Users to Import ({parsedUsers.length})
              </Typography>
              
              <Paper variant="outlined" sx={{ maxHeight: 300, overflow: 'auto', mb: 3, borderRadius: 2 }}>
                <List dense>
                  {parsedUsers.map((user, index) => (
                    <ListItem key={index} divider>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="body2" fontWeight="500">
                              {user.username}
                            </Typography>
                            <Chip
                              label={user.role.toUpperCase()}
                              color="primary"
                              size="small"
                              sx={{ height: 20, fontSize: '0.7rem' }}
                            />
                          </Box>
                        }
                        secondary={user.email}
                      />
                    </ListItem>
                  ))}
                </List>
              </Paper>

              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  borderRadius: 2,
                  bgcolor: 'warning.light',
                  color: 'warning.contrastText',
                  mb: 3
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                  <InfoIcon sx={{ mt: 0.5 }} />
                  <Typography variant="body2">
                    You are about to import {parsedUsers.length} users. This will create new user accounts
                    in the system. If a username or email already exists, that user will be skipped.
                  </Typography>
                </Box>
              </Paper>

              <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                <Button
                  variant="outlined"
                  onClick={() => setImportStep(0)}
                  disabled={loading}
                >
                  Back
                </Button>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleImport}
                  disabled={loading || parsedUsers.length === 0}
                  startIcon={loading ? <CircularProgress size={20} /> : <UploadIcon />}
                >
                  {loading ? 'Importing...' : 'Import Users'}
                </Button>
              </Box>
            </Box>
          )}

          {importStep === 2 && importResult && (
            <Box>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Import Complete
              </Typography>
              
              <Paper elevation={0} sx={{ p: 2, mb: 3, borderRadius: 2, bgcolor: 'success.light', color: 'success.contrastText' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CheckIcon />
                  <Typography variant="subtitle1">
                    Successfully imported {importResult.success.length} of {importResult.total} users
                  </Typography>
                </Box>
              </Paper>
              
              {importResult.failed.length > 0 && (
                <Paper elevation={0} sx={{ p: 2, mb: 3, borderRadius: 2, bgcolor: 'error.light', color: 'error.contrastText' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                    <ErrorIcon />
                    <Typography variant="subtitle1">
                      Failed to import {importResult.failed.length} users
                    </Typography>
                  </Box>
                  
                  <List dense sx={{ maxHeight: 200, overflow: 'auto' }}>
                    {importResult.failed.map((item, index) => (
                      <ListItem key={index}>
                        <ListItemText
                          primary={`${item.user.username} (${item.user.email})`}
                          secondary={`Reason: ${item.reason}`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Paper>
              )}
              
              <Button
                variant="contained"
                color="primary"
                onClick={() => {
                  onOperationComplete();
                  onClose();
                }}
              >
                Done
              </Button>
            </Box>
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" fontWeight="500" sx={{ mb: 2 }}>
                Export Format
              </Typography>
              <Paper elevation={0} sx={{ p: 2, borderRadius: 2, mb: 3 }}>
                <FormControl component="fieldset">
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={exportFormat === 'csv'}
                            onChange={() => setExportFormat('csv')}
                          />
                        }
                        label="CSV"
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={exportFormat === 'json'}
                            onChange={() => setExportFormat('json')}
                          />
                        }
                        label="JSON"
                      />
                    </Grid>
                  </Grid>
                </FormControl>
              </Paper>
            </Grid>
            
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" fontWeight="500" sx={{ mb: 2 }}>
                Export Options
              </Typography>
              <Paper elevation={0} sx={{ p: 2, borderRadius: 2, mb: 3 }}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={includePasswords}
                      onChange={(e) => setIncludePasswords(e.target.checked)}
                    />
                  }
                  label="Include default passwords (if available)"
                />
                <Tooltip title="This will include default passwords for users who have not changed their password yet. Useful for system migration.">
                  <IconButton size="small" sx={{ ml: 1 }}>
                    <HelpIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Paper>
            </Grid>
            
            <Grid item xs={12}>
              <Typography variant="subtitle1" fontWeight="500" sx={{ mb: 2 }}>
                Filter by Role
              </Typography>
              <Paper elevation={0} sx={{ p: 2, borderRadius: 2, mb: 3 }}>
                <Grid container spacing={2}>
                  {['admin', 'doctor', 'staff', 'patient', 'kin', 'researcher'].map((role) => (
                    <Grid item xs={6} sm={4} key={role}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            checked={exportRoles.includes(role)}
                            onChange={() => handleRoleToggle(role)}
                          />
                        }
                        label={role.charAt(0).toUpperCase() + role.slice(1)}
                      />
                    </Grid>
                  ))}
                </Grid>
              </Paper>
            </Grid>
          </Grid>
          
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleExport}
              disabled={loading || exportRoles.length === 0}
              startIcon={loading ? <CircularProgress size={20} /> : <DownloadIcon />}
              size="large"
            >
              {loading ? 'Exporting...' : 'Export Users'}
            </Button>
          </Box>
        </TabPanel>
      </DialogContent>
      <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid #eee' }}>
        <Button onClick={handleClose} variant="outlined" disabled={loading}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default UserImportExport;
