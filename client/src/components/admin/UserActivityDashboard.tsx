import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Paper,
  Grid,
  CircularProgress,
  Alert,
  Chip,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  Button,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Person as PersonIcon,
  AccessTime as TimeIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Lock as LockIcon,
  LockOpen as UnlockIcon,
  Delete as DeleteIcon,
  Login as LoginIcon,
  Logout as LogoutIcon,
  Refresh as RefreshIcon,
  BarChart as ChartIcon,
  Warning as WarningIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { API_URL } from '../../config';

interface UserActivity {
  activity_id: number;
  user_id: number;
  username: string;
  activity_type: string;
  activity_details: string;
  ip_address: string;
  timestamp: string;
}

interface UserStats {
  user_id: number;
  username: string;
  email: string;
  role: string;
  login_count: number;
  last_login: string;
  created_at: string;
  is_locked: boolean;
  activity_count: number;
  inactive_days: number;
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`activity-tabpanel-${index}`}
      aria-labelledby={`activity-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const UserActivityDashboard: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activities, setActivities] = useState<UserActivity[]>([]);
  const [userStats, setUserStats] = useState<UserStats[]>([]);
  const [tabValue, setTabValue] = useState(0);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      // In a real implementation, these would be actual API calls
      // For now, we'll simulate the data
      
      // Simulate API call for user activities
      setTimeout(() => {
        const mockActivities: UserActivity[] = Array.from({ length: 20 }, (_, i) => ({
          activity_id: i + 1,
          user_id: Math.floor(Math.random() * 10) + 1,
          username: `user${Math.floor(Math.random() * 10) + 1}`,
          activity_type: ['login', 'logout', 'view', 'edit', 'create', 'delete'][Math.floor(Math.random() * 6)],
          activity_details: `Performed ${['login', 'logout', 'view', 'edit', 'create', 'delete'][Math.floor(Math.random() * 6)]} action`,
          ip_address: `192.168.1.${Math.floor(Math.random() * 255) + 1}`,
          timestamp: new Date(Date.now() - Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000)).toISOString()
        }));

        // Sort by timestamp (newest first)
        mockActivities.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
        
        setActivities(mockActivities);
        
        // Simulate API call for user stats
        const mockUserStats: UserStats[] = Array.from({ length: 10 }, (_, i) => ({
          user_id: i + 1,
          username: `user${i + 1}`,
          email: `user${i + 1}@example.com`,
          role: ['admin', 'doctor', 'staff', 'patient', 'kin'][Math.floor(Math.random() * 5)],
          login_count: Math.floor(Math.random() * 100),
          last_login: new Date(Date.now() - Math.floor(Math.random() * 30 * 24 * 60 * 60 * 1000)).toISOString(),
          created_at: new Date(Date.now() - Math.floor(Math.random() * 365 * 24 * 60 * 60 * 1000)).toISOString(),
          is_locked: Math.random() > 0.9,
          activity_count: Math.floor(Math.random() * 200),
          inactive_days: Math.floor(Math.random() * 60)
        }));
        
        setUserStats(mockUserStats);
        setLoading(false);
      }, 1000);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch activity data');
      console.error('Error fetching activity data:', err);
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'login':
        return <LoginIcon color="primary" />;
      case 'logout':
        return <LogoutIcon color="secondary" />;
      case 'view':
        return <ViewIcon color="info" />;
      case 'edit':
        return <EditIcon color="warning" />;
      case 'create':
        return <PersonIcon color="success" />;
      case 'delete':
        return <DeleteIcon color="error" />;
      default:
        return <InfoIcon color="action" />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'error';
      case 'doctor':
        return 'warning';
      case 'staff':
        return 'info';
      case 'patient':
        return 'success';
      case 'kin':
        return 'secondary';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 4,
          borderRadius: 2,
          background: 'linear-gradient(to right, #3f51b5, #5c6bc0)',
          color: 'white',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        <Box sx={{
          position: 'absolute',
          top: 0,
          right: 0,
          width: '30%',
          height: '100%',
          opacity: 0.1,
          background: `url('https://www.transparenttextures.com/patterns/cubes.png')`,
          backgroundSize: 'cover'
        }} />

        <Grid container spacing={2} alignItems="center">
          <Grid item>
            <Avatar
              sx={{
                width: 60,
                height: 60,
                bgcolor: 'white',
                color: '#3f51b5',
                boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
              }}
            >
              <ChartIcon fontSize="large" />
            </Avatar>
          </Grid>
          <Grid item xs>
            <Typography variant="h4" fontWeight="500">
              User Activity Dashboard
            </Typography>
            <Typography variant="subtitle1" sx={{ opacity: 0.9, mt: 0.5 }}>
              Monitor user activity and identify usage patterns
            </Typography>
          </Grid>
          <Grid item>
            <Button
              variant="contained"
              startIcon={<RefreshIcon />}
              onClick={fetchData}
              size="medium"
              sx={{
                bgcolor: 'rgba(255,255,255,0.9)',
                color: '#3f51b5',
                '&:hover': { bgcolor: 'white' }
              }}
            >
              Refresh
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Tabs Section */}
      <Paper sx={{ mb: 4, borderRadius: 2, overflow: 'hidden', boxShadow: '0 4px 12px rgba(0,0,0,0.05)' }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              py: 2,
              transition: 'all 0.2s ease',
              '&:hover': {
                bgcolor: 'rgba(0,0,0,0.02)'
              }
            },
            '& .Mui-selected': {
              fontWeight: 'bold'
            }
          }}
        >
          <Tab
            icon={<TimeIcon />}
            label="Recent Activity"
            id="activity-tab-0"
            aria-controls="activity-tabpanel-0"
            iconPosition="start"
          />
          <Tab
            icon={<PersonIcon />}
            label="User Metrics"
            id="activity-tab-1"
            aria-controls="activity-tabpanel-1"
            iconPosition="start"
          />
          <Tab
            icon={<WarningIcon />}
            label="Inactive Users"
            id="activity-tab-2"
            aria-controls="activity-tabpanel-2"
            iconPosition="start"
          />
        </Tabs>
      </Paper>

      <TabPanel value={tabValue} index={0}>
        <Typography variant="h6" sx={{ mb: 2 }}>Recent User Activity</Typography>
        <Paper
          elevation={0}
          sx={{
            width: '100%',
            mb: 2,
            borderRadius: 2,
            overflow: 'hidden',
            boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
          }}
        >
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                  <TableCell>User</TableCell>
                  <TableCell>Activity</TableCell>
                  <TableCell>Details</TableCell>
                  <TableCell>IP Address</TableCell>
                  <TableCell>Timestamp</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {activities.map((activity) => (
                  <TableRow key={activity.activity_id}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Avatar sx={{ width: 30, height: 30 }}>
                          {activity.username.charAt(0).toUpperCase()}
                        </Avatar>
                        <Typography variant="body2">{activity.username}</Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {getActivityIcon(activity.activity_type)}
                        <Chip
                          label={activity.activity_type.toUpperCase()}
                          size="small"
                          color={
                            activity.activity_type === 'login' ? 'primary' :
                            activity.activity_type === 'logout' ? 'secondary' :
                            activity.activity_type === 'view' ? 'info' :
                            activity.activity_type === 'edit' ? 'warning' :
                            activity.activity_type === 'create' ? 'success' :
                            'error'
                          }
                        />
                      </Box>
                    </TableCell>
                    <TableCell>{activity.activity_details}</TableCell>
                    <TableCell>{activity.ip_address}</TableCell>
                    <TableCell>{formatDate(activity.timestamp)}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      </TabPanel>

      <TabPanel value={tabValue} index={1}>
        <Typography variant="h6" sx={{ mb: 2 }}>User Activity Metrics</Typography>
        <Paper
          elevation={0}
          sx={{
            width: '100%',
            mb: 2,
            borderRadius: 2,
            overflow: 'hidden',
            boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
          }}
        >
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                  <TableCell>User</TableCell>
                  <TableCell>Role</TableCell>
                  <TableCell>Login Count</TableCell>
                  <TableCell>Activity Count</TableCell>
                  <TableCell>Last Login</TableCell>
                  <TableCell>Status</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {userStats.map((user) => (
                  <TableRow key={user.user_id}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Avatar sx={{ width: 30, height: 30, bgcolor: getRoleColor(user.role) + '.main' }}>
                          {user.username.charAt(0).toUpperCase()}
                        </Avatar>
                        <Box>
                          <Typography variant="body2" sx={{ fontWeight: 500 }}>
                            {user.username}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {user.email}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={user.role.toUpperCase()}
                        color={getRoleColor(user.role)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{user.login_count}</TableCell>
                    <TableCell>{user.activity_count}</TableCell>
                    <TableCell>{formatDate(user.last_login)}</TableCell>
                    <TableCell>
                      <Chip
                        label={user.is_locked ? 'LOCKED' : 'ACTIVE'}
                        color={user.is_locked ? 'error' : 'success'}
                        size="small"
                        variant={user.is_locked ? 'filled' : 'outlined'}
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      </TabPanel>

      <TabPanel value={tabValue} index={2}>
        <Typography variant="h6" sx={{ mb: 2 }}>Inactive Users</Typography>
        <Paper
          elevation={0}
          sx={{
            width: '100%',
            mb: 2,
            borderRadius: 2,
            overflow: 'hidden',
            boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
          }}
        >
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: '#f5f5f5' }}>
                  <TableCell>User</TableCell>
                  <TableCell>Role</TableCell>
                  <TableCell>Inactive Days</TableCell>
                  <TableCell>Last Login</TableCell>
                  <TableCell>Created</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {userStats
                  .filter(user => user.inactive_days > 30)
                  .sort((a, b) => b.inactive_days - a.inactive_days)
                  .map((user) => (
                    <TableRow key={user.user_id}>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Avatar sx={{ width: 30, height: 30, bgcolor: getRoleColor(user.role) + '.main' }}>
                            {user.username.charAt(0).toUpperCase()}
                          </Avatar>
                          <Box>
                            <Typography variant="body2" sx={{ fontWeight: 500 }}>
                              {user.username}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {user.email}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={user.role.toUpperCase()}
                          color={getRoleColor(user.role)}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={`${user.inactive_days} days`}
                          color={
                            user.inactive_days > 90 ? 'error' :
                            user.inactive_days > 60 ? 'warning' :
                            'default'
                          }
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{formatDate(user.last_login)}</TableCell>
                      <TableCell>{formatDate(user.created_at)}</TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Tooltip title="Send reminder email">
                            <IconButton size="small" color="primary">
                              <InfoIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          {user.is_locked ? (
                            <Tooltip title="Unlock account">
                              <IconButton size="small" color="success">
                                <UnlockIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          ) : (
                            <Tooltip title="Lock account">
                              <IconButton size="small" color="warning">
                                <LockIcon fontSize="small" />
                              </IconButton>
                            </Tooltip>
                          )}
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Paper>
      </TabPanel>
    </Box>
  );
};

export default UserActivityDashboard;
