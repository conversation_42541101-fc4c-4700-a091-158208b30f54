import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Avatar,
  Button,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab,
  TextField,
  Chip,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  useTheme,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Snackbar,
  Switch,
  FormHelperText,
  Card,
  CardContent,
  CardActions,
  CardHeader,
  Menu,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  alpha
} from '@mui/material';
import {
  Storage as StorageIcon,
  Refresh as RefreshIcon,
  Search as SearchIcon,
  Code as CodeIcon,
  TableChart as TableChartIcon,
  Link as LinkIcon,
  Key as KeyIcon,
  PlayArrow as PlayArrowIcon,
  Info as InfoIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  DeleteSweep as DeleteSweepIcon,
  AddCircle as AddCircleIcon,
  Settings as SettingsIcon,
  ViewList as ViewListIcon,
  Schema as SchemaIcon,
  DataObject as DataObjectIcon,
  Person as PersonIcon,
  EventNote as VisitIcon,
  Download as DownloadIcon,
  FileDownload as FileDownloadIcon,
  MedicalServices as MedicalServicesIcon
} from '@mui/icons-material';
import SamplePatientGenerator from './SamplePatientGenerator';
import VisitGenerator from './VisitGenerator';
import PatientDataSearch from './PatientDataSearch';
import BeersCriteriaManagement from '../beers/BeersCriteriaManagement';
import { API_URL } from '../../config';

interface TableInfo {
  table_name: string;
  table_schema: string;
}

interface ColumnInfo {
  column_name: string;
  data_type: string;
  character_maximum_length: number | null;
  column_default: string | null;
  is_nullable: string;
  udt_name: string;
}

interface ForeignKeyInfo {
  column_name: string;
  foreign_table_name: string;
  foreign_column_name: string;
}

interface IndexInfo {
  indexname: string;
  indexdef: string;
}

interface TableSchema {
  table: string;
  columns: ColumnInfo[];
  primaryKeys: string[];
  foreignKeys: ForeignKeyInfo[];
  indexes: IndexInfo[];
  rowCount: number;
  sampleData: any[];
}

interface AllSchemaData {
  [tableName: string]: {
    columns: ColumnInfo[];
    primaryKeys: string[];
    foreignKeys: ForeignKeyInfo[];
    rowCount: number;
  };
}

interface QueryResult {
  rows: any[];
  rowCount: number;
  fields: {
    name: string;
    dataTypeID: number;
  }[];
}

interface DataTypeInfo {
  name: string;
  description: string;
  needsLength?: boolean;
  needsPrecision?: boolean;
}

interface SnackbarState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'info' | 'warning';
}

const DatabaseDebug: React.FC = () => {
  const theme = useTheme();
  const [tables, setTables] = useState<TableInfo[]>([]);
  const [selectedTable, setSelectedTable] = useState<string | null>(null);
  const [tableSchema, setTableSchema] = useState<TableSchema | null>(null);
  const [allSchemaData, setAllSchemaData] = useState<AllSchemaData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<number>(0);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [columnSearchTerm, setColumnSearchTerm] = useState<string>('');
  const [sqlQuery, setSqlQuery] = useState<string>('SELECT * FROM users LIMIT 10');
  const [queryResult, setQueryResult] = useState<QueryResult | null>(null);
  const [queryLoading, setQueryLoading] = useState<boolean>(false);
  const [queryError, setQueryError] = useState<string | null>(null);
  const [showQueryDialog, setShowQueryDialog] = useState<boolean>(false);
  const [refreshing, setRefreshing] = useState<boolean>(false);

  // New state variables for field management
  const [dataTypes, setDataTypes] = useState<DataTypeInfo[]>([]);
  const [showAddColumnDialog, setShowAddColumnDialog] = useState<boolean>(false);
  const [showRemoveColumnDialog, setShowRemoveColumnDialog] = useState<boolean>(false);
  const [newColumnName, setNewColumnName] = useState<string>('');
  const [selectedDataType, setSelectedDataType] = useState<string>('');
  const [columnLength, setColumnLength] = useState<string>('');
  const [columnPrecision, setColumnPrecision] = useState<string>('');
  const [columnScale, setColumnScale] = useState<string>('');
  const [isNullable, setIsNullable] = useState<boolean>(true);
  const [defaultValue, setDefaultValue] = useState<string>('');
  const [columnToRemove, setColumnToRemove] = useState<string>('');
  const [addColumnLoading, setAddColumnLoading] = useState<boolean>(false);
  const [removeColumnLoading, setRemoveColumnLoading] = useState<boolean>(false);

  // New state variables for sample data management
  const [showGenerateSampleDataDialog, setShowGenerateSampleDataDialog] = useState<boolean>(false);
  const [sampleDataCount, setSampleDataCount] = useState<string>('5');
  const [generateSampleDataLoading, setGenerateSampleDataLoading] = useState<boolean>(false);
  const [deleteAllDataLoading, setDeleteAllDataLoading] = useState<boolean>(false);
  const [showDeleteAllDataConfirmDialog, setShowDeleteAllDataConfirmDialog] = useState<boolean>(false);
  const [deletingRecordId, setDeletingRecordId] = useState<string | null>(null);
  const [showDeleteTableDataDialog, setShowDeleteTableDataDialog] = useState<boolean>(false);
  const [deleteTableDataLoading, setDeleteTableDataLoading] = useState<boolean>(false);

  // New state variables for altering column data types
  const [showAlterColumnTypeDialog, setShowAlterColumnTypeDialog] = useState<boolean>(false);
  const [columnToAlter, setColumnToAlter] = useState<string>('');
  const [newDataType, setNewDataType] = useState<string>('');
  const [alterColumnLoading, setAlterColumnLoading] = useState<boolean>(false);
  const [currentColumnType, setCurrentColumnType] = useState<string>('');

  // Navigation state
  const [mainSection, setMainSection] = useState<'schema' | 'data' | 'tools' | 'simple-generator' | 'visit-generator' | 'patient-search' | 'beers-criteria'>('schema');

  // Download menu state
  const [downloadMenuAnchor, setDownloadMenuAnchor] = useState<null | HTMLElement>(null);
  const [tableDownloadMenuAnchor, setTableDownloadMenuAnchor] = useState<null | HTMLElement>(null);

  // Notification state
  const [snackbar, setSnackbar] = useState<SnackbarState>({
    open: false,
    message: '',
    severity: 'info'
  });

  // Fetch all tables on component mount
  useEffect(() => {
    fetchTables();
    fetchDataTypes();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Fetch table schema when a table is selected
  useEffect(() => {
    if (selectedTable) {
      fetchTableSchema(selectedTable);
    }
  }, [selectedTable]);

  const fetchTables = async () => {
    try {
      setLoading(true);
      setRefreshing(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/debug/tables`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch database tables');
      }

      const data = await response.json();
      setTables(data);
      setError(null);

      // If we have tables, fetch all schema data
      if (data.length > 0) {
        fetchAllSchemaData();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error fetching database tables:', err);
    } finally {
      setLoading(false);
      setTimeout(() => setRefreshing(false), 500);
    }
  };

  const fetchTableSchema = async (tableName: string) => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/debug/schema/${tableName}`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch schema for table ${tableName}`);
      }

      const data = await response.json();
      setTableSchema(data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error(`Error fetching schema for table ${tableName}:`, err);
    } finally {
      setLoading(false);
    }
  };

  const fetchAllSchemaData = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/debug/all-schema`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch all schema data');
      }

      const data = await response.json();
      setAllSchemaData(data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error fetching all schema data:', err);
    } finally {
      setLoading(false);
    }
  };

  const executeQuery = async () => {
    try {
      setQueryLoading(true);
      setQueryError(null);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/debug/query`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: JSON.stringify({ query: sqlQuery })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to execute query');
      }

      const data = await response.json();
      setQueryResult(data);
    } catch (err) {
      setQueryError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error executing SQL query:', err);
    } finally {
      setQueryLoading(false);
    }
  };

  const fetchDataTypes = async () => {
    try {
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/debug/datatypes`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch data types');
      }

      const data = await response.json();
      setDataTypes(data);
    } catch (err) {
      console.error('Error fetching data types:', err);
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to fetch data types',
        severity: 'error'
      });
    }
  };

  const addColumn = async () => {
    try {
      if (!selectedTable) {
        throw new Error('No table selected');
      }

      if (!newColumnName.trim()) {
        throw new Error('Column name is required');
      }

      if (!selectedDataType) {
        throw new Error('Data type is required');
      }

      setAddColumnLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      // Prepare the request body
      const requestBody: any = {
        table: selectedTable,
        columnName: newColumnName,
        dataType: selectedDataType,
        nullable: isNullable
      };

      // Add optional parameters if they are provided
      if (selectedDataType === 'VARCHAR' && columnLength) {
        requestBody.length = parseInt(columnLength);
      }

      if ((selectedDataType === 'DECIMAL' || selectedDataType === 'NUMERIC') && columnPrecision) {
        requestBody.precision = parseInt(columnPrecision);
        if (columnScale) {
          requestBody.scale = parseInt(columnScale);
        }
      }

      if (defaultValue.trim()) {
        requestBody.defaultValue = defaultValue;
      }

      const response = await fetch(`${API_URL}/api/debug/add-column`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: JSON.stringify(requestBody)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.msg || 'Failed to add column');
      }

      // Show success message
      setSnackbar({
        open: true,
        message: data.msg || 'Column added successfully',
        severity: 'success'
      });

      // Reset form fields
      setNewColumnName('');
      setSelectedDataType('');
      setColumnLength('');
      setColumnPrecision('');
      setColumnScale('');
      setIsNullable(true);
      setDefaultValue('');

      // Close the dialog
      setShowAddColumnDialog(false);

      // Refresh the table schema
      if (selectedTable) {
        fetchTableSchema(selectedTable);
      }

      // Refresh all schema data
      fetchAllSchemaData();

    } catch (err) {
      console.error('Error adding column:', err);
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to add column',
        severity: 'error'
      });
    } finally {
      setAddColumnLoading(false);
    }
  };

  const removeColumn = async () => {
    try {
      if (!selectedTable) {
        throw new Error('No table selected');
      }

      if (!columnToRemove) {
        throw new Error('No column selected for removal');
      }

      setRemoveColumnLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/debug/remove-column`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: JSON.stringify({
          table: selectedTable,
          columnName: columnToRemove
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.msg || 'Failed to remove column');
      }

      // Show success message
      setSnackbar({
        open: true,
        message: data.msg || 'Column removed successfully',
        severity: 'success'
      });

      // Reset form field
      setColumnToRemove('');

      // Close the dialog
      setShowRemoveColumnDialog(false);

      // Refresh the table schema
      if (selectedTable) {
        fetchTableSchema(selectedTable);
      }

      // Refresh all schema data
      fetchAllSchemaData();

    } catch (err) {
      console.error('Error removing column:', err);
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to remove column',
        severity: 'error'
      });
    } finally {
      setRemoveColumnLoading(false);
    }
  };

  const deleteTableData = async () => {
    try {
      if (!selectedTable) {
        throw new Error('No table selected');
      }

      setDeleteTableDataLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/debug/delete-table-data`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: JSON.stringify({ table: selectedTable })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.msg || 'Failed to delete table data');
      }

      // Show success message
      setSnackbar({
        open: true,
        message: data.msg || `All data deleted from ${selectedTable}`,
        severity: 'success'
      });

      // Close the dialog
      setShowDeleteTableDataDialog(false);

      // Refresh the table schema
      if (selectedTable) {
        fetchTableSchema(selectedTable);
      }

    } catch (err) {
      console.error('Error deleting table data:', err);
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to delete table data',
        severity: 'error'
      });
    } finally {
      setDeleteTableDataLoading(false);
    }
  };

  const alterColumnType = async () => {
    try {
      if (!selectedTable) {
        throw new Error('No table selected');
      }

      if (!columnToAlter) {
        throw new Error('No column selected for type alteration');
      }

      if (!newDataType) {
        throw new Error('New data type is required');
      }

      setAlterColumnLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      // Prepare the request body
      const requestBody: any = {
        table: selectedTable,
        columnName: columnToAlter,
        newDataType: newDataType
      };

      // Add optional parameters if they are provided
      if (newDataType === 'VARCHAR' && columnLength) {
        requestBody.length = parseInt(columnLength);
      }

      if ((newDataType === 'DECIMAL' || newDataType === 'NUMERIC') && columnPrecision) {
        requestBody.precision = parseInt(columnPrecision);
        if (columnScale) {
          requestBody.scale = parseInt(columnScale);
        }
      }

      const response = await fetch(`${API_URL}/api/debug/alter-column-type`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: JSON.stringify(requestBody)
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.msg || 'Failed to alter column type');
      }

      // Show success message
      setSnackbar({
        open: true,
        message: data.msg || 'Column type altered successfully',
        severity: 'success'
      });

      // Reset form fields
      setColumnToAlter('');
      setNewDataType('');
      setColumnLength('');
      setColumnPrecision('');
      setColumnScale('');
      setCurrentColumnType('');

      // Close the dialog
      setShowAlterColumnTypeDialog(false);

      // Refresh the table schema
      if (selectedTable) {
        fetchTableSchema(selectedTable);
      }

      // Refresh all schema data
      fetchAllSchemaData();

    } catch (err) {
      console.error('Error altering column type:', err);
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to alter column type',
        severity: 'error'
      });
    } finally {
      setAlterColumnLoading(false);
    }
  };

  // Sample data management functions
  const generateSampleData = async () => {
    try {
      if (!selectedTable) {
        throw new Error('No table selected');
      }

      setGenerateSampleDataLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/debug/generate-sample-data/${selectedTable}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: JSON.stringify({
          count: parseInt(sampleDataCount)
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.msg || 'Failed to generate sample data');
      }

      // Show success message
      setSnackbar({
        open: true,
        message: data.msg || `Generated ${data.generatedCount} sample records`,
        severity: 'success'
      });

      // Reset form field
      setSampleDataCount('5');

      // Close the dialog
      setShowGenerateSampleDataDialog(false);

      // Refresh the table schema to update row count
      if (selectedTable) {
        fetchTableSchema(selectedTable);
      }

      // Refresh all schema data
      fetchAllSchemaData();

    } catch (err) {
      console.error('Error generating sample data:', err);
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to generate sample data',
        severity: 'error'
      });
    } finally {
      setGenerateSampleDataLoading(false);
    }
  };

  const deleteRecord = async (id: string) => {
    try {
      if (!selectedTable) {
        throw new Error('No table selected');
      }

      setDeletingRecordId(id);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/debug/sample-data/${selectedTable}/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.msg || 'Failed to delete record');
      }

      // Show success message
      setSnackbar({
        open: true,
        message: data.msg || `Record deleted successfully`,
        severity: 'success'
      });

      // Refresh the table schema to update row count
      if (selectedTable) {
        fetchTableSchema(selectedTable);
      }

      // Refresh all schema data
      fetchAllSchemaData();

    } catch (err) {
      console.error('Error deleting record:', err);
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to delete record',
        severity: 'error'
      });
    } finally {
      setDeletingRecordId(null);
    }
  };

  const deleteAllData = async () => {
    try {
      if (!selectedTable) {
        throw new Error('No table selected');
      }

      setDeleteAllDataLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/debug/all-sample-data/${selectedTable}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.msg || 'Failed to delete all data');
      }

      // Show success message
      setSnackbar({
        open: true,
        message: data.msg || `All data deleted successfully`,
        severity: 'success'
      });

      // Close the dialog
      setShowDeleteAllDataConfirmDialog(false);

      // Refresh the table schema to update row count
      if (selectedTable) {
        fetchTableSchema(selectedTable);
      }

      // Refresh all schema data
      fetchAllSchemaData();

    } catch (err) {
      console.error('Error deleting all data:', err);
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to delete all data',
        severity: 'error'
      });
    } finally {
      setDeleteAllDataLoading(false);
    }
  };

  // Download schema functions
  const downloadTableSchema = async (format: 'json' | 'csv' = 'csv') => {
    try {
      if (!selectedTable) {
        throw new Error('No table selected');
      }

      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/debug/download-schema/${selectedTable}`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch schema data for download');
      }

      const data = await response.json();

      if (format === 'json') {
        // Download as JSON
        const jsonContent = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonContent], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `${selectedTable}_schema.json`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      } else {
        // Download as CSV
        // Create CSV header
        const csvHeader = ['column_name', 'data_type', 'character_maximum_length', 'is_nullable', 'column_default', 'is_primary_key', 'references_table', 'references_column'];

        // Create CSV rows
        const csvRows = data.columns.map((column: any) => [
          column.column_name,
          column.data_type,
          column.character_maximum_length || '',
          column.is_nullable,
          column.column_default || '',
          column.is_primary_key ? 'YES' : 'NO',
          column.foreign_key ? column.foreign_key.references_table : '',
          column.foreign_key ? column.foreign_key.references_column : ''
        ]);

        // Combine header and rows
        const csvContent = [
          csvHeader.join(','),
          ...csvRows.map((row: string[]) => row.join(','))
        ].join('\n');

        // Create and download the file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `${selectedTable}_schema.csv`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }

      // Show success message
      setSnackbar({
        open: true,
        message: `Schema for ${selectedTable} downloaded successfully`,
        severity: 'success'
      });
    } catch (err) {
      console.error('Error downloading schema:', err);
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to download schema',
        severity: 'error'
      });
    }
  };

  const downloadAllSchemas = async (format: 'json' | 'csv' = 'csv') => {
    try {
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/debug/download-all-schema`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch all schema data for download');
      }

      const data = await response.json();

      if (format === 'json') {
        // Download as JSON
        const jsonContent = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonContent], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `database_schema.json`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      } else {
        // Download as CSV
        // Create CSV header
        const csvHeader = ['table_name', 'column_name', 'data_type', 'character_maximum_length', 'is_nullable', 'column_default', 'is_primary_key', 'references_table', 'references_column'];

        // Create CSV rows
        let csvRows: string[][] = [];

        Object.entries(data).forEach(([tableName, tableData]: [string, any]) => {
          tableData.columns.forEach((column: any) => {
            csvRows.push([
              tableName,
              column.column_name,
              column.data_type,
              column.character_maximum_length || '',
              column.is_nullable,
              column.column_default || '',
              column.is_primary_key ? 'YES' : 'NO',
              column.foreign_key ? column.foreign_key.references_table : '',
              column.foreign_key ? column.foreign_key.references_column : ''
            ]);
          });
        });

        // Combine header and rows
        const csvContent = [
          csvHeader.join(','),
          ...csvRows.map((row: string[]) => row.join(','))
        ].join('\n');

        // Create and download the file
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `database_schema.csv`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
      }

      // Show success message
      setSnackbar({
        open: true,
        message: `All database schemas downloaded successfully`,
        severity: 'success'
      });
    } catch (err) {
      console.error('Error downloading all schemas:', err);
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to download all schemas',
        severity: 'error'
      });
    }
  };



  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
    // Reset column search when changing tabs
    if (newValue !== 0) {
      setColumnSearchTerm('');
    }
  };

  const filteredTables = tables.filter(table =>
    table.table_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Find tables that contain a column matching the search term
  const findTablesWithColumn = (columnName: string) => {
    if (!allSchemaData || !columnName.trim()) return [];

    const results = Object.entries(allSchemaData)
      .filter(([tableName, schema]) =>
        schema.columns.some(column =>
          column.column_name.toLowerCase().includes(columnName.toLowerCase())
        )
      )
      .map(([tableName, schema]) => ({
        tableName,
        columns: schema.columns.filter(column =>
          column.column_name.toLowerCase().includes(columnName.toLowerCase())
        )
      }));

    return results;
  };

  const handleTableClick = (tableName: string) => {
    setSelectedTable(tableName);
    setActiveTab(0);
  };

  const renderTableList = () => {
    return (
      <Box sx={{ mt: 2 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Search tables..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: <SearchIcon sx={{ mr: 1.5, color: theme.palette.primary.main }} />,
            sx: {
              borderRadius: 2,
              '&.MuiOutlinedInput-root': {
                transition: 'all 0.2s ease',
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: theme.palette.primary.main,
                  borderWidth: '1px'
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: theme.palette.primary.main,
                  borderWidth: '2px'
                }
              }
            }
          }}
          sx={{ mb: 3 }}
          size="small"
        />

        <List
          sx={{
            width: '100%',
            maxHeight: 500,
            overflow: 'auto',
            border: 1,
            borderColor: 'divider',
            borderRadius: 2,
            boxShadow: `inset 0 0 6px ${alpha(theme.palette.common.black, 0.05)}`,
            bgcolor: alpha(theme.palette.background.paper, 0.5),
            '& .MuiListItem-root': {
              borderBottom: 1,
              borderColor: 'divider',
              '&:last-child': {
                borderBottom: 0
              }
            }
          }}
          component="nav"
          dense
        >
          {filteredTables.length > 0 ? (
            filteredTables.map((table) => (
              <ListItem key={table.table_name} disablePadding>
                <ListItemButton
                  selected={selectedTable === table.table_name}
                  onClick={() => handleTableClick(table.table_name)}
                  sx={{
                    py: 1.5,
                    px: 2,
                    transition: 'all 0.2s ease',
                    '&.Mui-selected': {
                      bgcolor: alpha(theme.palette.primary.main, 0.1),
                      borderLeft: `4px solid ${theme.palette.primary.main}`,
                      '&:hover': {
                        bgcolor: alpha(theme.palette.primary.main, 0.15),
                      }
                    },
                    '&:hover': {
                      bgcolor: alpha(theme.palette.primary.main, 0.05),
                    }
                  }}
                >
                  <ListItemIcon sx={{ minWidth: 36 }}>
                    <TableChartIcon
                      fontSize="small"
                      color={selectedTable === table.table_name ? "primary" : "action"}
                      sx={{
                        transition: 'transform 0.2s ease',
                        transform: selectedTable === table.table_name ? 'scale(1.2)' : 'scale(1)'
                      }}
                    />
                  </ListItemIcon>
                  <ListItemText
                    primary={table.table_name}
                    primaryTypographyProps={{
                      noWrap: false,
                      sx: {
                        wordBreak: 'break-word',
                        fontWeight: selectedTable === table.table_name ? 600 : 400,
                        color: selectedTable === table.table_name ? theme.palette.primary.main : 'inherit'
                      }
                    }}
                    secondary={
                      <Box sx={{ display: 'flex', mt: 0.8, gap: 1, flexWrap: 'wrap' }}>
                        <Chip
                          size="small"
                          label={allSchemaData && allSchemaData[table.table_name]
                            ? `${allSchemaData[table.table_name].rowCount} rows`
                            : '...'}
                          sx={{
                            height: 20,
                            bgcolor: alpha(theme.palette.info.main, 0.1),
                            color: theme.palette.info.dark,
                            fontWeight: 500,
                            border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
                          }}
                        />
                        <Chip
                          size="small"
                          label={allSchemaData && allSchemaData[table.table_name]
                            ? `${allSchemaData[table.table_name].columns.length} cols`
                            : '...'}
                          sx={{
                            height: 20,
                            bgcolor: alpha(theme.palette.success.main, 0.1),
                            color: theme.palette.success.dark,
                            fontWeight: 500,
                            border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`
                          }}
                        />
                      </Box>
                    }
                  />
                </ListItemButton>
              </ListItem>
            ))
          ) : (
            <Box sx={{ p: 3, textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                No tables match your search
              </Typography>
            </Box>
          )}
        </List>
      </Box>
    );
  };

  const renderTableSchema = () => {
    if (!tableSchema) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', mt: 4, height: 300 }}>
          <CircularProgress size={40} thickness={4} />
        </Box>
      );
    }

    return (
      <Box sx={{ mt: 2 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          sx={{
            mb: 3,
            borderBottom: 1,
            borderColor: 'divider',
            '& .MuiTab-root': {
              minHeight: '48px',
              fontWeight: 500,
              fontSize: '0.9rem',
              transition: 'all 0.2s ease',
              mx: 0.5,
              '&:hover': {
                bgcolor: alpha(theme.palette.primary.main, 0.05),
              },
              '&.Mui-selected': {
                color: theme.palette.primary.main,
                fontWeight: 600
              }
            },
            '& .MuiTabs-indicator': {
              height: 3,
              borderRadius: '3px 3px 0 0'
            }
          }}
          variant="scrollable"
          scrollButtons="auto"
          allowScrollButtonsMobile
        >
          <Tab label="Columns" icon={<ViewListIcon fontSize="small" />} iconPosition="start" />
          <Tab label="Relationships" icon={<LinkIcon fontSize="small" />} iconPosition="start" />
          <Tab label="Indexes" icon={<KeyIcon fontSize="small" />} iconPosition="start" />
          <Tab label="Sample Data" icon={<DataObjectIcon fontSize="small" />} iconPosition="start" />
          <Tab label="Find Column" icon={<SearchIcon fontSize="small" />} iconPosition="start" />
          <Tab label="Manage Fields" icon={<SettingsIcon fontSize="small" />} iconPosition="start" />
        </Tabs>

        {activeTab === 0 && (
          <Box>
            <TextField
              fullWidth
              variant="outlined"
              placeholder="Search columns..."
              value={columnSearchTerm}
              onChange={(e) => setColumnSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1.5, color: theme.palette.primary.main }} />,
                sx: {
                  borderRadius: 2,
                  '&.MuiOutlinedInput-root': {
                    transition: 'all 0.2s ease',
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: theme.palette.primary.main,
                      borderWidth: '1px'
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: theme.palette.primary.main,
                      borderWidth: '2px'
                    }
                  }
                }
              }}
              sx={{ mb: 3 }}
            />

            <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="subtitle1" sx={{ fontWeight: 500, color: theme.palette.text.primary }}>
                {tableSchema.columns.length} Columns
              </Typography>
              <Box>
                <Chip
                  icon={<KeyIcon fontSize="small" />}
                  label={`${tableSchema.primaryKeys.length} Primary Keys`}
                  size="small"
                  color="primary"
                  sx={{ mr: 1, fontWeight: 500 }}
                />
                <Chip
                  icon={<LinkIcon fontSize="small" />}
                  label={`${tableSchema.foreignKeys.length} Foreign Keys`}
                  size="small"
                  color="secondary"
                  sx={{ fontWeight: 500 }}
                />
              </Box>
            </Box>

            <TableContainer
              component={Paper}
              sx={{
                maxHeight: 500,
                overflow: 'auto',
                borderRadius: 2,
                boxShadow: theme.shadows[2],
                '&::-webkit-scrollbar': {
                  width: '8px',
                  height: '8px',
                },
                '&::-webkit-scrollbar-thumb': {
                  backgroundColor: alpha(theme.palette.primary.main, 0.2),
                  borderRadius: '4px',
                },
                '&::-webkit-scrollbar-track': {
                  backgroundColor: alpha(theme.palette.common.black, 0.05),
                  borderRadius: '4px',
                }
              }}
            >
              <Table stickyHeader size="small">
                <TableHead>
                  <TableRow>
                    <TableCell
                      sx={{
                        fontWeight: 'bold',
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                        color: theme.palette.primary.main,
                        borderBottom: `2px solid ${theme.palette.primary.main}`
                      }}
                    >
                      Column Name
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 'bold',
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                        color: theme.palette.primary.main,
                        borderBottom: `2px solid ${theme.palette.primary.main}`
                      }}
                    >
                      Data Type
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 'bold',
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                        color: theme.palette.primary.main,
                        borderBottom: `2px solid ${theme.palette.primary.main}`
                      }}
                    >
                      Nullable
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 'bold',
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                        color: theme.palette.primary.main,
                        borderBottom: `2px solid ${theme.palette.primary.main}`
                      }}
                    >
                      Default
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 'bold',
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                        color: theme.palette.primary.main,
                        borderBottom: `2px solid ${theme.palette.primary.main}`
                      }}
                    >
                      Key
                    </TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {tableSchema.columns
                    .filter(column =>
                      columnSearchTerm === '' ||
                      column.column_name.toLowerCase().includes(columnSearchTerm.toLowerCase())
                    )
                    .map((column, index) => (
                    <TableRow
                      key={column.column_name}
                      sx={{
                        '&:nth-of-type(odd)': {
                          bgcolor: alpha(theme.palette.background.default, 0.5),
                        },
                        '&:hover': {
                          bgcolor: alpha(theme.palette.primary.main, 0.05),
                        },
                        transition: 'background-color 0.2s ease'
                      }}
                    >
                      <TableCell sx={{ py: 1.5 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          {column.column_name}
                          {tableSchema.primaryKeys.includes(column.column_name) && (
                            <Chip
                              size="small"
                              label="PK"
                              color="primary"
                              sx={{ ml: 1, height: 20, fontWeight: 600 }}
                            />
                          )}
                          {tableSchema.foreignKeys.some(fk => fk.column_name === column.column_name) && (
                            <Chip
                              size="small"
                              label="FK"
                              color="secondary"
                              sx={{ ml: 1, height: 20, fontWeight: 600 }}
                            />
                          )}
                        </Box>
                      </TableCell>
                      <TableCell sx={{ py: 1.5 }}>
                        <Chip
                          size="small"
                          label={`${column.data_type}${column.character_maximum_length ? `(${column.character_maximum_length})` : ''}`}
                          sx={{
                            height: 22,
                            bgcolor: alpha(theme.palette.info.main, 0.1),
                            color: theme.palette.info.dark,
                            fontWeight: 500,
                            border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
                          }}
                        />
                      </TableCell>
                      <TableCell sx={{ py: 1.5 }}>
                        {column.is_nullable === 'YES' ? (
                          <Chip
                            size="small"
                            label="Yes"
                            sx={{
                              height: 22,
                              bgcolor: alpha(theme.palette.success.main, 0.1),
                              color: theme.palette.success.dark,
                              fontWeight: 500,
                              border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`
                            }}
                          />
                        ) : (
                          <Chip
                            size="small"
                            label="No"
                            sx={{
                              height: 22,
                              bgcolor: alpha(theme.palette.error.main, 0.1),
                              color: theme.palette.error.dark,
                              fontWeight: 500,
                              border: `1px solid ${alpha(theme.palette.error.main, 0.2)}`
                            }}
                          />
                        )}
                      </TableCell>
                      <TableCell sx={{ py: 1.5 }}>
                        {column.column_default ? (
                          <Typography
                            variant="body2"
                            sx={{
                              fontFamily: 'monospace',
                              bgcolor: alpha(theme.palette.background.default, 0.7),
                              p: 0.5,
                              borderRadius: 1,
                              border: `1px solid ${alpha(theme.palette.divider, 0.5)}`
                            }}
                          >
                            {column.column_default}
                          </Typography>
                        ) : (
                          <Typography variant="body2" color="text.secondary" fontStyle="italic">
                            -
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell sx={{ py: 1.5 }}>
                        {tableSchema.primaryKeys.includes(column.column_name) ? (
                          <Tooltip title="Primary Key">
                            <KeyIcon color="primary" fontSize="small" />
                          </Tooltip>
                        ) : tableSchema.foreignKeys.some(fk => fk.column_name === column.column_name) ? (
                          <Tooltip title={`Foreign Key to ${
                            tableSchema.foreignKeys.find(fk => fk.column_name === column.column_name)?.foreign_table_name
                          }`}>
                            <LinkIcon color="secondary" fontSize="small" />
                          </Tooltip>
                        ) : (
                          <Typography variant="body2" color="text.secondary" fontStyle="italic">
                            -
                          </Typography>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        )}

        {activeTab === 1 && (
          <Box>
            <Box sx={{ mb: 4 }}>
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                mb: 2,
                pb: 1,
                borderBottom: `1px solid ${theme.palette.divider}`
              }}>
                <LinkIcon sx={{ mr: 1.5, color: theme.palette.secondary.main }} />
                <Typography variant="h6" sx={{ fontWeight: 500, color: theme.palette.secondary.main }}>
                  Foreign Keys
                </Typography>
                <Chip
                  label={`${tableSchema.foreignKeys.length} relationships`}
                  size="small"
                  color="secondary"
                  sx={{ ml: 1.5, fontWeight: 500 }}
                />
              </Box>

              {tableSchema.foreignKeys.length === 0 ? (
                <Paper
                  sx={{
                    p: 3,
                    textAlign: 'center',
                    bgcolor: alpha(theme.palette.background.default, 0.7),
                    borderRadius: 2,
                    border: `1px dashed ${theme.palette.divider}`
                  }}
                >
                  <Typography variant="body1" color="text.secondary">
                    No foreign key relationships found for this table
                  </Typography>
                </Paper>
              ) : (
                <TableContainer
                  component={Paper}
                  sx={{
                    maxHeight: 300,
                    overflow: 'auto',
                    borderRadius: 2,
                    boxShadow: theme.shadows[2],
                    '&::-webkit-scrollbar': {
                      width: '8px',
                      height: '8px',
                    },
                    '&::-webkit-scrollbar-thumb': {
                      backgroundColor: alpha(theme.palette.secondary.main, 0.2),
                      borderRadius: '4px',
                    },
                    '&::-webkit-scrollbar-track': {
                      backgroundColor: alpha(theme.palette.common.black, 0.05),
                      borderRadius: '4px',
                    }
                  }}
                >
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell
                          sx={{
                            fontWeight: 'bold',
                            bgcolor: alpha(theme.palette.secondary.main, 0.1),
                            color: theme.palette.secondary.main,
                            borderBottom: `2px solid ${theme.palette.secondary.main}`
                          }}
                        >
                          Column
                        </TableCell>
                        <TableCell
                          sx={{
                            fontWeight: 'bold',
                            bgcolor: alpha(theme.palette.secondary.main, 0.1),
                            color: theme.palette.secondary.main,
                            borderBottom: `2px solid ${theme.palette.secondary.main}`
                          }}
                        >
                          References
                        </TableCell>
                        <TableCell
                          sx={{
                            fontWeight: 'bold',
                            bgcolor: alpha(theme.palette.secondary.main, 0.1),
                            color: theme.palette.secondary.main,
                            borderBottom: `2px solid ${theme.palette.secondary.main}`
                          }}
                        >
                          Foreign Column
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {tableSchema.foreignKeys.map((fk, index) => (
                        <TableRow
                          key={index}
                          sx={{
                            '&:nth-of-type(odd)': {
                              bgcolor: alpha(theme.palette.background.default, 0.5),
                            },
                            '&:hover': {
                              bgcolor: alpha(theme.palette.secondary.main, 0.05),
                            },
                            transition: 'background-color 0.2s ease'
                          }}
                        >
                          <TableCell sx={{ py: 1.5 }}>
                            <Chip
                              size="small"
                              label={fk.column_name}
                              sx={{
                                fontWeight: 500,
                                bgcolor: alpha(theme.palette.secondary.main, 0.1),
                                color: theme.palette.secondary.dark,
                                border: `1px solid ${alpha(theme.palette.secondary.main, 0.2)}`
                              }}
                            />
                          </TableCell>
                          <TableCell sx={{ py: 1.5 }}>
                            <Button
                              size="small"
                              variant="outlined"
                              color="secondary"
                              startIcon={<TableChartIcon />}
                              onClick={() => handleTableClick(fk.foreign_table_name)}
                              sx={{
                                borderRadius: 1.5,
                                textTransform: 'none',
                                fontWeight: 500
                              }}
                            >
                              {fk.foreign_table_name}
                            </Button>
                          </TableCell>
                          <TableCell sx={{ py: 1.5 }}>
                            <Typography
                              variant="body2"
                              sx={{
                                fontFamily: 'monospace',
                                bgcolor: alpha(theme.palette.background.default, 0.7),
                                p: 0.5,
                                borderRadius: 1,
                                border: `1px solid ${alpha(theme.palette.divider, 0.5)}`
                              }}
                            >
                              {fk.foreign_column_name}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </Box>

            <Box>
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                mb: 2,
                pb: 1,
                borderBottom: `1px solid ${theme.palette.divider}`
              }}>
                <LinkIcon sx={{ mr: 1.5, color: theme.palette.info.main, transform: 'rotate(180deg)' }} />
                <Typography variant="h6" sx={{ fontWeight: 500, color: theme.palette.info.main }}>
                  Referenced By
                </Typography>
                {allSchemaData && (
                  <Chip
                    label={`${Object.entries(allSchemaData)
                      .filter(([tableName, schema]) =>
                        schema.foreignKeys.some(fk =>
                          fk.foreign_table_name === tableSchema.table
                        )
                      ).length} tables`}
                    size="small"
                    color="info"
                    sx={{ ml: 1.5, fontWeight: 500 }}
                  />
                )}
              </Box>

              {allSchemaData && Object.entries(allSchemaData)
                .filter(([tableName, schema]) =>
                  schema.foreignKeys.some(fk =>
                    fk.foreign_table_name === tableSchema.table
                  )
                ).length === 0 ? (
                <Paper
                  sx={{
                    p: 3,
                    textAlign: 'center',
                    bgcolor: alpha(theme.palette.background.default, 0.7),
                    borderRadius: 2,
                    border: `1px dashed ${theme.palette.divider}`
                  }}
                >
                  <Typography variant="body1" color="text.secondary">
                    No tables reference this table
                  </Typography>
                </Paper>
              ) : (
                <TableContainer
                  component={Paper}
                  sx={{
                    maxHeight: 300,
                    overflow: 'auto',
                    borderRadius: 2,
                    boxShadow: theme.shadows[2],
                    '&::-webkit-scrollbar': {
                      width: '8px',
                      height: '8px',
                    },
                    '&::-webkit-scrollbar-thumb': {
                      backgroundColor: alpha(theme.palette.info.main, 0.2),
                      borderRadius: '4px',
                    },
                    '&::-webkit-scrollbar-track': {
                      backgroundColor: alpha(theme.palette.common.black, 0.05),
                      borderRadius: '4px',
                    }
                  }}
                >
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell
                          sx={{
                            fontWeight: 'bold',
                            bgcolor: alpha(theme.palette.info.main, 0.1),
                            color: theme.palette.info.main,
                            borderBottom: `2px solid ${theme.palette.info.main}`
                          }}
                        >
                          Table
                        </TableCell>
                        <TableCell
                          sx={{
                            fontWeight: 'bold',
                            bgcolor: alpha(theme.palette.info.main, 0.1),
                            color: theme.palette.info.main,
                            borderBottom: `2px solid ${theme.palette.info.main}`
                          }}
                        >
                          Column
                        </TableCell>
                        <TableCell
                          sx={{
                            fontWeight: 'bold',
                            bgcolor: alpha(theme.palette.info.main, 0.1),
                            color: theme.palette.info.main,
                            borderBottom: `2px solid ${theme.palette.info.main}`
                          }}
                        >
                          References Column
                        </TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {allSchemaData && Object.entries(allSchemaData)
                        .filter(([tableName, schema]) =>
                          schema.foreignKeys.some(fk =>
                            fk.foreign_table_name === tableSchema.table
                          )
                        )
                        .map(([tableName, schema]) =>
                          schema.foreignKeys
                            .filter(fk => fk.foreign_table_name === tableSchema.table)
                            .map((fk, index) => (
                              <TableRow
                                key={`${tableName}-${index}`}
                                sx={{
                                  '&:nth-of-type(odd)': {
                                    bgcolor: alpha(theme.palette.background.default, 0.5),
                                  },
                                  '&:hover': {
                                    bgcolor: alpha(theme.palette.info.main, 0.05),
                                  },
                                  transition: 'background-color 0.2s ease'
                                }}
                              >
                                <TableCell sx={{ py: 1.5 }}>
                                  <Button
                                    size="small"
                                    variant="outlined"
                                    color="info"
                                    startIcon={<TableChartIcon />}
                                    onClick={() => handleTableClick(tableName)}
                                    sx={{
                                      borderRadius: 1.5,
                                      textTransform: 'none',
                                      fontWeight: 500
                                    }}
                                  >
                                    {tableName}
                                  </Button>
                                </TableCell>
                                <TableCell sx={{ py: 1.5 }}>
                                  <Chip
                                    size="small"
                                    label={fk.column_name}
                                    sx={{
                                      fontWeight: 500,
                                      bgcolor: alpha(theme.palette.info.main, 0.1),
                                      color: theme.palette.info.dark,
                                      border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
                                    }}
                                  />
                                </TableCell>
                                <TableCell sx={{ py: 1.5 }}>
                                  <Typography
                                    variant="body2"
                                    sx={{
                                      fontFamily: 'monospace',
                                      bgcolor: alpha(theme.palette.background.default, 0.7),
                                      p: 0.5,
                                      borderRadius: 1,
                                      border: `1px solid ${alpha(theme.palette.divider, 0.5)}`
                                    }}
                                  >
                                    {fk.foreign_column_name}
                                  </Typography>
                                </TableCell>
                              </TableRow>
                            ))
                        ).flat()}
                    </TableBody>
                  </Table>
                </TableContainer>
              )}
            </Box>
          </Box>
        )}

        {activeTab === 2 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Indexes
            </Typography>
            {tableSchema.indexes.length === 0 ? (
              <Alert severity="info">No indexes found</Alert>
            ) : (
              <TableContainer component={Paper} sx={{ maxHeight: 400, overflow: 'auto' }}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell sx={{ fontWeight: 'bold' }}>Index Name</TableCell>
                      <TableCell sx={{ fontWeight: 'bold' }}>Definition</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {tableSchema.indexes.map((index, idx) => (
                      <TableRow key={idx}>
                        <TableCell>{index.indexname}</TableCell>
                        <TableCell>
                          <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                            {index.indexdef}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </Box>
        )}

        {activeTab === 3 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Sample Data (First 5 rows)
            </Typography>
            {tableSchema.sampleData.length === 0 ? (
              <Alert severity="info">No data found in this table</Alert>
            ) : (
              <TableContainer component={Paper} sx={{ maxHeight: 400, overflow: 'auto' }}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      {Object.keys(tableSchema.sampleData[0]).map((key) => (
                        <TableCell key={key} sx={{ fontWeight: 'bold' }}>
                          {key}
                        </TableCell>
                      ))}
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {tableSchema.sampleData.map((row, rowIndex) => (
                      <TableRow key={rowIndex}>
                        {Object.values(row).map((value: any, colIndex) => (
                          <TableCell key={colIndex}>
                            {value === null ? (
                              <Typography variant="body2" color="text.secondary" fontStyle="italic">
                                NULL
                              </Typography>
                            ) : typeof value === 'object' ? (
                              JSON.stringify(value)
                            ) : (
                              String(value)
                            )}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </Box>
        )}

        {activeTab === 4 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Find Column Across All Tables
            </Typography>
            <TextField
              fullWidth
              variant="outlined"
              placeholder="Search for column name across all tables..."
              value={columnSearchTerm}
              onChange={(e) => setColumnSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
              sx={{ mb: 2 }}
            />

            {!columnSearchTerm ? (
              <Alert severity="info" sx={{ mb: 2 }}>
                Enter a column name to search across all tables
              </Alert>
            ) : (
              <>
                {findTablesWithColumn(columnSearchTerm).length === 0 ? (
                  <Alert severity="info">No columns found matching "{columnSearchTerm}"</Alert>
                ) : (
                  <>
                    <Typography variant="subtitle1" gutterBottom>
                      Found in {findTablesWithColumn(columnSearchTerm).length} tables
                    </Typography>
                    <TableContainer component={Paper} sx={{ maxHeight: 400, overflow: 'auto' }}>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell sx={{ fontWeight: 'bold' }}>Table</TableCell>
                            <TableCell sx={{ fontWeight: 'bold' }}>Column</TableCell>
                            <TableCell sx={{ fontWeight: 'bold' }}>Data Type</TableCell>
                            <TableCell sx={{ fontWeight: 'bold' }}>Nullable</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {findTablesWithColumn(columnSearchTerm).map((result) =>
                            result.columns.map((column, idx) => (
                              <TableRow key={`${result.tableName}-${column.column_name}`}>
                                <TableCell>
                                  <Button
                                    size="small"
                                    startIcon={<TableChartIcon />}
                                    onClick={() => handleTableClick(result.tableName)}
                                  >
                                    {result.tableName}
                                  </Button>
                                </TableCell>
                                <TableCell>
                                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    {column.column_name}
                                    {allSchemaData &&
                                     allSchemaData[result.tableName] &&
                                     allSchemaData[result.tableName].primaryKeys.includes(column.column_name) && (
                                      <Chip
                                        size="small"
                                        label="PK"
                                        color="primary"
                                        sx={{ ml: 1, height: 20 }}
                                      />
                                    )}
                                  </Box>
                                </TableCell>
                                <TableCell>
                                  {column.data_type}
                                  {column.character_maximum_length && `(${column.character_maximum_length})`}
                                </TableCell>
                                <TableCell>{column.is_nullable === 'YES' ? 'Yes' : 'No'}</TableCell>
                              </TableRow>
                            ))
                          )}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </>
                )}
              </>
            )}
          </Box>
        )}

        {activeTab === 5 && (
          <Box>
            <Typography variant="h6" gutterBottom>
              Manage Database Fields
            </Typography>

            {!selectedTable ? (
              <Alert severity="info" sx={{ mb: 2 }}>
                Please select a table to manage its fields
              </Alert>
            ) : (
              <>
                <Alert severity="info" sx={{ mb: 2 }}>
                  You can add new columns or remove existing columns from the <strong>{selectedTable}</strong> table.
                  Be careful when removing columns as this operation cannot be undone and may result in data loss.
                </Alert>

                <Grid container spacing={2} sx={{ mb: 3 }}>
                  <Grid item xs={12} sm={6}>
                    <Paper sx={{ p: 2, height: '100%' }}>
                      <Typography variant="subtitle1" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                        <AddIcon sx={{ mr: 1, color: theme.palette.success.main }} />
                        Add New Column
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        Add a new column to the {selectedTable} table with your specified data type and constraints.
                      </Typography>
                      <Button
                        variant="contained"
                        color="success"
                        startIcon={<AddIcon />}
                        fullWidth
                        onClick={() => setShowAddColumnDialog(true)}
                      >
                        Add Column
                      </Button>
                    </Paper>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Paper sx={{ p: 2, height: '100%' }}>
                      <Typography variant="subtitle1" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                        <DeleteIcon sx={{ mr: 1, color: theme.palette.error.main }} />
                        Remove Existing Column
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        Remove an existing column from the {selectedTable} table. Primary keys and columns referenced by foreign keys cannot be removed.
                      </Typography>
                      <Button
                        variant="contained"
                        color="error"
                        startIcon={<DeleteIcon />}
                        fullWidth
                        onClick={() => setShowRemoveColumnDialog(true)}
                      >
                        Remove Column
                      </Button>
                    </Paper>
                  </Grid>
                </Grid>

                <Typography variant="h6" gutterBottom>
                  Current Columns
                </Typography>

                {tableSchema && (
                  <TableContainer component={Paper} sx={{ maxHeight: 400, overflow: 'auto' }}>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell sx={{ fontWeight: 'bold' }}>Column Name</TableCell>
                          <TableCell sx={{ fontWeight: 'bold' }}>Data Type</TableCell>
                          <TableCell sx={{ fontWeight: 'bold' }}>Nullable</TableCell>
                          <TableCell sx={{ fontWeight: 'bold' }}>Default</TableCell>
                          <TableCell sx={{ fontWeight: 'bold' }}>Key</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {tableSchema.columns.map((column) => (
                          <TableRow key={column.column_name}>
                            <TableCell>
                              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                {column.column_name}
                                {tableSchema.primaryKeys.includes(column.column_name) && (
                                  <Chip
                                    size="small"
                                    label="PK"
                                    color="primary"
                                    sx={{ ml: 1, height: 20 }}
                                  />
                                )}
                              </Box>
                            </TableCell>
                            <TableCell>
                              {column.data_type}
                              {column.character_maximum_length && `(${column.character_maximum_length})`}
                            </TableCell>
                            <TableCell>{column.is_nullable === 'YES' ? 'Yes' : 'No'}</TableCell>
                            <TableCell>{column.column_default || '-'}</TableCell>
                            <TableCell>
                              {tableSchema.primaryKeys.includes(column.column_name) ? (
                                <Tooltip title="Primary Key">
                                  <KeyIcon color="primary" fontSize="small" />
                                </Tooltip>
                              ) : tableSchema.foreignKeys.some(fk => fk.column_name === column.column_name) ? (
                                <Tooltip title={`Foreign Key to ${
                                  tableSchema.foreignKeys.find(fk => fk.column_name === column.column_name)?.foreign_table_name
                                }`}>
                                  <LinkIcon color="secondary" fontSize="small" />
                                </Tooltip>
                              ) : null}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                )}
              </>
            )}
          </Box>
        )}
      </Box>
    );
  };

  const renderQueryInterface = () => {
    return (
      <Dialog
        open={showQueryDialog}
        onClose={() => setShowQueryDialog(false)}
        fullWidth
        maxWidth="lg"
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <CodeIcon sx={{ mr: 1 }} />
            SQL Query Interface
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mb: 2 }}>
            <TextField
              fullWidth
              multiline
              rows={4}
              variant="outlined"
              label="SQL Query (SELECT only)"
              value={sqlQuery}
              onChange={(e) => setSqlQuery(e.target.value)}
              sx={{ fontFamily: 'monospace' }}
            />
            <Box sx={{ mt: 1, display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="contained"
                color="primary"
                startIcon={<PlayArrowIcon />}
                onClick={executeQuery}
                disabled={queryLoading}
                sx={{ mt: 1 }}
              >
                {queryLoading ? 'Executing...' : 'Execute Query'}
              </Button>
            </Box>
          </Box>

          {queryError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {queryError}
            </Alert>
          )}

          {queryResult && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle1" gutterBottom>
                Results: {queryResult.rowCount} rows
              </Typography>
              <TableContainer component={Paper} sx={{ maxHeight: 400, overflow: 'auto' }}>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      {queryResult.fields.map((field) => (
                        <TableCell key={field.name} sx={{ fontWeight: 'bold' }}>
                          {field.name}
                        </TableCell>
                      ))}
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {queryResult.rows.map((row, rowIndex) => (
                      <TableRow key={rowIndex}>
                        {queryResult.fields.map((field) => (
                          <TableCell key={field.name}>
                            {row[field.name] === null ? (
                              <Typography variant="body2" color="text.secondary" fontStyle="italic">
                                NULL
                              </Typography>
                            ) : typeof row[field.name] === 'object' ? (
                              JSON.stringify(row[field.name])
                            ) : (
                              String(row[field.name])
                            )}
                          </TableCell>
                        ))}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowQueryDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    );
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 3,
          borderRadius: 2,
          background: `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.primary.dark} 90%)`,
          color: 'white'
        }}
      >
        <Grid container spacing={2} alignItems="center">
          <Grid item>
            <Avatar
              sx={{
                width: 60,
                height: 60,
                bgcolor: 'white',
                color: theme.palette.primary.main,
                boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
              }}
            >
              <StorageIcon fontSize="large" />
            </Avatar>
          </Grid>
          <Grid item xs>
            <Typography variant="h4" fontWeight="500">
              Database Schema Debug
            </Typography>
            <Typography variant="subtitle1" sx={{ opacity: 0.9, mt: 0.5 }}>
              Explore and analyze the database structure and relationships
            </Typography>
          </Grid>
          <Grid item sx={{ display: { xs: 'block', md: 'flex' }, flexWrap: 'wrap', gap: 1 }}>
            <Box sx={{ display: 'flex', mb: { xs: 1, md: 0 } }}>
              <Tooltip title="Refresh schema data">
                <IconButton
                  color="inherit"
                  onClick={fetchTables}
                  disabled={refreshing}
                  sx={{ bgcolor: 'rgba(255,255,255,0.1)', '&:hover': { bgcolor: 'rgba(255,255,255,0.2)' } }}
                >
                  {refreshing ? <CircularProgress size={24} color="inherit" /> : <RefreshIcon />}
                </IconButton>
              </Tooltip>
            </Box>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              <Button
                variant="contained"
                startIcon={<SearchIcon />}
                onClick={() => {
                  setSelectedTable(null);
                  setActiveTab(4);
                }}
                size="medium"
                sx={{
                  bgcolor: 'white',
                  color: theme.palette.primary.main,
                  '&:hover': {
                    bgcolor: 'rgba(255,255,255,0.9)'
                  }
                }}
              >
                Find Column
              </Button>
              <Button
                variant="contained"
                startIcon={<CodeIcon />}
                onClick={() => setShowQueryDialog(true)}
                size="medium"
                sx={{
                  bgcolor: 'white',
                  color: theme.palette.primary.main,
                  '&:hover': {
                    bgcolor: 'rgba(255,255,255,0.9)'
                  }
                }}
              >
                SQL Query
              </Button>
              <Button
                variant="contained"
                startIcon={<DownloadIcon />}
                onClick={(e) => setDownloadMenuAnchor(e.currentTarget)}
                size="medium"
                sx={{
                  bgcolor: 'white',
                  color: theme.palette.primary.main,
                  '&:hover': {
                    bgcolor: 'rgba(255,255,255,0.9)'
                  }
                }}
              >
                Download
              </Button>
              <Menu
                anchorEl={downloadMenuAnchor}
                open={Boolean(downloadMenuAnchor)}
                onClose={() => setDownloadMenuAnchor(null)}
              >
                <MenuItem onClick={() => {
                  downloadAllSchemas('csv');
                  setDownloadMenuAnchor(null);
                }}>
                  <FileDownloadIcon fontSize="small" sx={{ mr: 1 }} />
                  CSV Format
                </MenuItem>
                <MenuItem onClick={() => {
                  downloadAllSchemas('json');
                  setDownloadMenuAnchor(null);
                }}>
                  <FileDownloadIcon fontSize="small" sx={{ mr: 1 }} />
                  JSON Format
                </MenuItem>
              </Menu>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Main navigation tabs */}
      <Box sx={{ mb: 3 }}>
        <Paper sx={{ mb: 2 }}>
          <Tabs
            value={mainSection}
            onChange={(e, newValue) => setMainSection(newValue)}
            variant="scrollable"
            scrollButtons="auto"
            allowScrollButtonsMobile
            sx={{
              borderBottom: 1,
              borderColor: 'divider',
              minHeight: '48px',
              '& .MuiTab-root': {
                minHeight: '48px',
                py: 1.5
              }
            }}
          >
            <Tab
              icon={<SchemaIcon />}
              label="Schema Explorer"
              value="schema"
              iconPosition="start"
            />
            <Tab
              icon={<DataObjectIcon />}
              label="Sample Data Management"
              value="data"
              iconPosition="start"
            />
            <Tab
              icon={<SettingsIcon />}
              label="Database Tools"
              value="tools"
              iconPosition="start"
            />
            <Tab
              icon={<PersonIcon />}
              label="Sample Patient Generator"
              value="simple-generator"
              iconPosition="start"
            />
            <Tab
              icon={<VisitIcon />}
              label="Visit Generator"
              value="visit-generator"
              iconPosition="start"
            />
            <Tab
              icon={<SearchIcon />}
              label="Patient Data Search"
              value="patient-search"
              iconPosition="start"
            />
            <Tab
              icon={<MedicalServicesIcon />}
              label="BEERS Criteria Management"
              value="beers-criteria"
              iconPosition="start"
            />
          </Tabs>
        </Paper>
      </Box>

      {loading && !tables.length ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          {/* Schema Explorer Section */}
          {mainSection === 'schema' && (
            <Grid container spacing={4}>
              <Grid item xs={12} md={4} lg={3.5} xl={3}>
                <Paper
                  sx={{
                    p: 3,
                    height: '100%',
                    borderRadius: 2,
                    boxShadow: theme.shadows[2],
                    transition: 'box-shadow 0.3s ease',
                    '&:hover': {
                      boxShadow: theme.shadows[4],
                    }
                  }}
                >
                  <Typography
                    variant="h6"
                    gutterBottom
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      pb: 1.5,
                      borderBottom: `1px solid ${theme.palette.divider}`,
                      mb: 2
                    }}
                  >
                    <TableChartIcon sx={{ mr: 1.5, color: theme.palette.primary.main }} />
                    <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}>
                      Database Tables
                      <Chip
                        label={`${tables.length} tables`}
                        size="small"
                        color="primary"
                        sx={{
                          ml: 1.5,
                          fontWeight: 500,
                          height: 24,
                          '& .MuiChip-label': { px: 1 },
                          bgcolor: alpha(theme.palette.primary.main, 0.9)
                        }}
                      />
                    </Box>
                  </Typography>
                  {renderTableList()}
                </Paper>
              </Grid>
              <Grid item xs={12} md={8} lg={8.5} xl={9}>
                {selectedTable ? (
                  <Paper
                    sx={{
                      p: 3,
                      height: '100%',
                      borderRadius: 2,
                      boxShadow: theme.shadows[2],
                      transition: 'box-shadow 0.3s ease',
                      '&:hover': {
                        boxShadow: theme.shadows[4],
                      }
                    }}
                  >
                    <Box
                      sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        mb: 3,
                        pb: 1.5,
                        borderBottom: `1px solid ${theme.palette.divider}`
                      }}
                    >
                      <Typography
                        variant="h6"
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          flexWrap: 'wrap',
                          color: theme.palette.primary.main
                        }}
                      >
                        <InfoIcon sx={{ mr: 1.5, color: theme.palette.primary.main }} />
                        <Box component="span" sx={{ wordBreak: 'break-word', fontWeight: 500 }}>
                          Table: {selectedTable}
                        </Box>
                        {tableSchema && (
                          <Chip
                            label={`${tableSchema.rowCount} rows`}
                            size="small"
                            color="primary"
                            sx={{ ml: 1.5, mt: { xs: 0.5, sm: 0 }, fontWeight: 500 }}
                          />
                        )}
                      </Typography>
                      <Box>
                        <Tooltip title="Download table schema">
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<FileDownloadIcon />}
                            onClick={(e) => setTableDownloadMenuAnchor(e.currentTarget)}
                            sx={{
                              borderRadius: 1.5,
                              px: 2,
                              py: 0.75
                            }}
                          >
                            Download Schema
                          </Button>
                        </Tooltip>
                        <Menu
                          anchorEl={tableDownloadMenuAnchor}
                          open={Boolean(tableDownloadMenuAnchor)}
                          onClose={() => setTableDownloadMenuAnchor(null)}
                          PaperProps={{
                            elevation: 3,
                            sx: { borderRadius: 2, mt: 0.5 }
                          }}
                        >
                          <MenuItem onClick={() => {
                            downloadTableSchema('csv');
                            setTableDownloadMenuAnchor(null);
                          }}>
                            <FileDownloadIcon fontSize="small" sx={{ mr: 1.5 }} />
                            CSV Format
                          </MenuItem>
                          <MenuItem onClick={() => {
                            downloadTableSchema('json');
                            setTableDownloadMenuAnchor(null);
                          }}>
                            <FileDownloadIcon fontSize="small" sx={{ mr: 1.5 }} />
                            JSON Format
                          </MenuItem>
                        </Menu>
                      </Box>
                    </Box>
                    {renderTableSchema()}
                  </Paper>
                ) : (
                  <Paper
                    sx={{
                      p: 4,
                      textAlign: 'center',
                      bgcolor: alpha(theme.palette.background.default, 0.7),
                      height: '100%',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      borderRadius: 2,
                      boxShadow: theme.shadows[2],
                      border: `1px dashed ${theme.palette.divider}`
                    }}
                  >
                    <Box>
                      <InfoIcon sx={{ fontSize: 70, color: alpha(theme.palette.primary.main, 0.3), mb: 3, display: 'block', mx: 'auto' }} />
                      <Typography variant="h6" color="text.secondary" gutterBottom>
                        Select a table to view its schema
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ maxWidth: 400, mx: 'auto' }}>
                        Choose a table from the list on the left to explore its structure, relationships, and data
                      </Typography>
                    </Box>
                  </Paper>
                )}
              </Grid>
            </Grid>
          )}

          {/* Sample Data Management Section */}
          {mainSection === 'data' && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={4} lg={3.5} xl={3}>
                <Paper sx={{ p: 2, height: '100%' }}>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', pb: 1.5, borderBottom: `1px solid ${theme.palette.divider}`, mb: 2 }}>
                    <TableChartIcon sx={{ mr: 1.5, color: theme.palette.primary.main }} />
                    <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}>
                      Database Tables
                      <Chip
                        label={`${tables.length} tables`}
                        size="small"
                        color="primary"
                        sx={{
                          ml: 1.5,
                          fontWeight: 500,
                          height: 24,
                          '& .MuiChip-label': { px: 1 },
                          bgcolor: alpha(theme.palette.primary.main, 0.9)
                        }}
                      />
                    </Box>
                  </Typography>
                  {renderTableList()}
                </Paper>
              </Grid>
              <Grid item xs={12} md={8} lg={8.5} xl={9}>
                {selectedTable ? (
                  <Box>
                    <Paper sx={{ p: 2, mb: 3 }}>
                      <Typography variant="h6" gutterBottom>
                        Sample Data Management for {selectedTable}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" paragraph>
                        This section allows you to manage sample data in the {selectedTable} table.
                        You can generate random sample data, delete individual records, or delete all data from the table.
                      </Typography>

                      <Grid container spacing={2} sx={{ mt: 2 }}>
                        <Grid item xs={12} sm={6} md={4}>
                          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                            <CardHeader
                              avatar={<AddCircleIcon color="success" />}
                              title="Generate Sample Data"
                              titleTypographyProps={{ variant: 'subtitle1' }}
                            />
                            <CardContent sx={{ flexGrow: 1 }}>
                              <Typography variant="body2" color="text.secondary">
                                Generate random sample data for testing purposes.
                              </Typography>
                            </CardContent>
                            <CardActions>
                              <Button
                                fullWidth
                                variant="contained"
                                color="success"
                                startIcon={<AddCircleIcon />}
                                onClick={() => setShowGenerateSampleDataDialog(true)}
                              >
                                Generate Data
                              </Button>
                            </CardActions>
                          </Card>
                        </Grid>

                        <Grid item xs={12} sm={6} md={4}>
                          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                            <CardHeader
                              avatar={<DeleteIcon color="warning" />}
                              title="Delete Records"
                              titleTypographyProps={{ variant: 'subtitle1' }}
                            />
                            <CardContent sx={{ flexGrow: 1 }}>
                              <Typography variant="body2" color="text.secondary">
                                Delete individual records from the table.
                              </Typography>
                            </CardContent>
                            <CardActions>
                              <Button
                                fullWidth
                                variant="contained"
                                color="warning"
                                startIcon={<ViewListIcon />}
                                onClick={() => setActiveTab(3)}
                              >
                                View Records
                              </Button>
                            </CardActions>
                          </Card>
                        </Grid>

                        <Grid item xs={12} sm={6} md={4}>
                          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                            <CardHeader
                              avatar={<DeleteSweepIcon color="error" />}
                              title="Delete All Data"
                              titleTypographyProps={{ variant: 'subtitle1' }}
                            />
                            <CardContent sx={{ flexGrow: 1 }}>
                              <Typography variant="body2" color="text.secondary">
                                Delete all records from the table.
                              </Typography>
                            </CardContent>
                            <CardActions>
                              <Button
                                fullWidth
                                variant="contained"
                                color="error"
                                startIcon={<DeleteSweepIcon />}
                                onClick={() => setShowDeleteTableDataDialog(true)}
                                disabled={tableSchema?.rowCount === 0}
                              >
                                Delete All
                              </Button>
                            </CardActions>
                          </Card>
                        </Grid>
                      </Grid>
                    </Paper>

                    <Paper sx={{ p: 2 }}>
                      <Typography variant="h6" gutterBottom>
                        Current Data in {selectedTable}
                      </Typography>

                      {tableSchema?.sampleData.length === 0 ? (
                        <Alert severity="info">No data found in this table</Alert>
                      ) : (
                        <TableContainer sx={{ maxHeight: 500, overflow: 'auto' }}>
                          <Table size="small" stickyHeader>
                            <TableHead>
                              <TableRow>
                                <TableCell sx={{ fontWeight: 'bold', bgcolor: theme.palette.background.paper }}>Actions</TableCell>
                                {tableSchema && tableSchema.sampleData.length > 0 &&
                                  Object.keys(tableSchema.sampleData[0]).map((key) => (
                                    <TableCell key={key} sx={{ fontWeight: 'bold', bgcolor: theme.palette.background.paper }}>
                                      {key}
                                    </TableCell>
                                  ))
                                }
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {tableSchema?.sampleData.map((row, rowIndex) => {
                                // Find the primary key column and value
                                const primaryKeyColumn = tableSchema.primaryKeys[0];
                                const primaryKeyValue = row[primaryKeyColumn];

                                return (
                                  <TableRow key={rowIndex} hover>
                                    <TableCell>
                                      <IconButton
                                        color="error"
                                        size="small"
                                        onClick={() => deleteRecord(primaryKeyValue)}
                                        disabled={deletingRecordId === primaryKeyValue}
                                      >
                                        {deletingRecordId === primaryKeyValue ? (
                                          <CircularProgress size={20} />
                                        ) : (
                                          <DeleteIcon fontSize="small" />
                                        )}
                                      </IconButton>
                                    </TableCell>
                                    {Object.values(row).map((value: any, colIndex) => (
                                      <TableCell key={colIndex}>
                                        {value === null ? (
                                          <Typography variant="body2" color="text.secondary" fontStyle="italic">
                                            NULL
                                          </Typography>
                                        ) : typeof value === 'object' ? (
                                          JSON.stringify(value)
                                        ) : (
                                          String(value)
                                        )}
                                      </TableCell>
                                    ))}
                                  </TableRow>
                                );
                              })}
                            </TableBody>
                          </Table>
                        </TableContainer>
                      )}
                    </Paper>
                  </Box>
                ) : (
                  <Paper sx={{ p: 4, textAlign: 'center', height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <Box>
                      <InfoIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2, display: 'block', mx: 'auto' }} />
                      <Typography variant="h6" color="text.secondary">
                        Select a table to manage its sample data
                      </Typography>
                    </Box>
                  </Paper>
                )}
              </Grid>
            </Grid>
          )}

          {/* Database Tools Section */}
          {mainSection === 'tools' && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={4} lg={3.5} xl={3}>
                <Paper sx={{ p: 2, height: '100%' }}>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', pb: 1.5, borderBottom: `1px solid ${theme.palette.divider}`, mb: 2 }}>
                    <TableChartIcon sx={{ mr: 1.5, color: theme.palette.primary.main }} />
                    <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap' }}>
                      Database Tables
                      <Chip
                        label={`${tables.length} tables`}
                        size="small"
                        color="primary"
                        sx={{
                          ml: 1.5,
                          fontWeight: 500,
                          height: 24,
                          '& .MuiChip-label': { px: 1 },
                          bgcolor: alpha(theme.palette.primary.main, 0.9)
                        }}
                      />
                    </Box>
                  </Typography>
                  {renderTableList()}
                </Paper>
              </Grid>
              <Grid item xs={12} md={8} lg={8.5} xl={9}>
                {selectedTable ? (
                  <Box>
                    <Paper sx={{ p: 2, mb: 3 }}>
                      <Typography variant="h6" gutterBottom>
                        Database Tools for {selectedTable}
                      </Typography>

                      <Grid container spacing={2} sx={{ mt: 2 }}>
                        <Grid item xs={12} sm={6} md={4}>
                          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                            <CardHeader
                              avatar={<AddIcon color="primary" />}
                              title="Add Column"
                              titleTypographyProps={{ variant: 'subtitle1' }}
                            />
                            <CardContent sx={{ flexGrow: 1 }}>
                              <Typography variant="body2" color="text.secondary">
                                Add a new column to the {selectedTable} table.
                              </Typography>
                            </CardContent>
                            <CardActions>
                              <Button
                                fullWidth
                                variant="contained"
                                color="primary"
                                startIcon={<AddIcon />}
                                onClick={() => setShowAddColumnDialog(true)}
                              >
                                Add Column
                              </Button>
                            </CardActions>
                          </Card>
                        </Grid>

                        <Grid item xs={12} sm={6} md={4}>
                          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                            <CardHeader
                              avatar={<EditIcon color="warning" />}
                              title="Alter Column Type"
                              titleTypographyProps={{ variant: 'subtitle1' }}
                            />
                            <CardContent sx={{ flexGrow: 1 }}>
                              <Typography variant="body2" color="text.secondary">
                                Change the data type of an existing column in the {selectedTable} table.
                              </Typography>
                            </CardContent>
                            <CardActions>
                              <Button
                                fullWidth
                                variant="contained"
                                color="warning"
                                startIcon={<EditIcon />}
                                onClick={() => setShowAlterColumnTypeDialog(true)}
                              >
                                Alter Column Type
                              </Button>
                            </CardActions>
                          </Card>
                        </Grid>

                        <Grid item xs={12} sm={6} md={4}>
                          <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                            <CardHeader
                              avatar={<DeleteIcon color="error" />}
                              title="Remove Column"
                              titleTypographyProps={{ variant: 'subtitle1' }}
                            />
                            <CardContent sx={{ flexGrow: 1 }}>
                              <Typography variant="body2" color="text.secondary">
                                Remove an existing column from the {selectedTable} table.
                              </Typography>
                            </CardContent>
                            <CardActions>
                              <Button
                                fullWidth
                                variant="contained"
                                color="error"
                                startIcon={<DeleteIcon />}
                                onClick={() => setShowRemoveColumnDialog(true)}
                              >
                                Remove Column
                              </Button>
                            </CardActions>
                          </Card>
                        </Grid>
                      </Grid>
                    </Paper>

                    <Paper sx={{ p: 2 }}>
                      <Typography variant="h6" gutterBottom>
                        Current Columns in {selectedTable}
                      </Typography>

                      {tableSchema && (
                        <TableContainer sx={{ maxHeight: 500, overflow: 'auto' }}>
                          <Table size="small" stickyHeader>
                            <TableHead>
                              <TableRow>
                                <TableCell sx={{ fontWeight: 'bold', bgcolor: theme.palette.background.paper }}>Column Name</TableCell>
                                <TableCell sx={{ fontWeight: 'bold', bgcolor: theme.palette.background.paper }}>Data Type</TableCell>
                                <TableCell sx={{ fontWeight: 'bold', bgcolor: theme.palette.background.paper }}>Nullable</TableCell>
                                <TableCell sx={{ fontWeight: 'bold', bgcolor: theme.palette.background.paper }}>Default</TableCell>
                                <TableCell sx={{ fontWeight: 'bold', bgcolor: theme.palette.background.paper }}>Key</TableCell>
                              </TableRow>
                            </TableHead>
                            <TableBody>
                              {tableSchema.columns.map((column) => (
                                <TableRow key={column.column_name} hover>
                                  <TableCell>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                      {column.column_name}
                                      {tableSchema.primaryKeys.includes(column.column_name) && (
                                        <Chip
                                          size="small"
                                          label="PK"
                                          color="primary"
                                          sx={{ ml: 1, height: 20 }}
                                        />
                                      )}
                                    </Box>
                                  </TableCell>
                                  <TableCell>
                                    {column.data_type}
                                    {column.character_maximum_length && `(${column.character_maximum_length})`}
                                  </TableCell>
                                  <TableCell>{column.is_nullable === 'YES' ? 'Yes' : 'No'}</TableCell>
                                  <TableCell>{column.column_default || '-'}</TableCell>
                                  <TableCell>
                                    {tableSchema.primaryKeys.includes(column.column_name) ? (
                                      <Tooltip title="Primary Key">
                                        <KeyIcon color="primary" fontSize="small" />
                                      </Tooltip>
                                    ) : tableSchema.foreignKeys.some(fk => fk.column_name === column.column_name) ? (
                                      <Tooltip title={`Foreign Key to ${
                                        tableSchema.foreignKeys.find(fk => fk.column_name === column.column_name)?.foreign_table_name
                                      }`}>
                                        <LinkIcon color="secondary" fontSize="small" />
                                      </Tooltip>
                                    ) : null}
                                  </TableCell>
                                </TableRow>
                              ))}
                            </TableBody>
                          </Table>
                        </TableContainer>
                      )}
                    </Paper>
                  </Box>
                ) : (
                  <Paper sx={{ p: 4, textAlign: 'center', height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <Box>
                      <InfoIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2, display: 'block', mx: 'auto' }} />
                      <Typography variant="h6" color="text.secondary">
                        Select a table to use database tools
                      </Typography>
                    </Box>
                  </Paper>
                )}
              </Grid>
            </Grid>
          )}

          {/* Sample Patient Generator Section */}
          {mainSection === 'simple-generator' && (
            <SamplePatientGenerator />
          )}

          {/* Visit Generator Section */}
          {mainSection === 'visit-generator' && (
            <VisitGenerator />
          )}

          {/* Patient Data Search Section */}
          {mainSection === 'patient-search' && (
            <PatientDataSearch />
          )}

          {/* BEERS Criteria Management Section */}
          {mainSection === 'beers-criteria' && (
            <Box>
              <BeersCriteriaManagement />
            </Box>
          )}
        </>
      )}

      {renderQueryInterface()}

      {/* Add Column Dialog */}
      <Dialog
        open={showAddColumnDialog}
        onClose={() => setShowAddColumnDialog(false)}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <AddIcon sx={{ mr: 1, color: theme.palette.success.main }} />
            Add New Column to {selectedTable}
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 1 }}>
            <TextField
              fullWidth
              label="Column Name"
              variant="outlined"
              value={newColumnName}
              onChange={(e) => setNewColumnName(e.target.value)}
              sx={{ mb: 2, mt: 1 }}
              required
              helperText="Enter a name for the new column (letters, numbers, underscores only)"
            />

            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel id="data-type-label">Data Type</InputLabel>
              <Select
                labelId="data-type-label"
                value={selectedDataType}
                onChange={(e) => setSelectedDataType(e.target.value)}
                label="Data Type"
                required
              >
                {dataTypes.map((type) => (
                  <MenuItem key={type.name} value={type.name}>
                    {type.name} - {type.description}
                  </MenuItem>
                ))}
              </Select>
              <FormHelperText>Select the data type for the new column</FormHelperText>
            </FormControl>

            {selectedDataType === 'VARCHAR' && (
              <TextField
                fullWidth
                label="Length"
                variant="outlined"
                type="number"
                value={columnLength}
                onChange={(e) => setColumnLength(e.target.value)}
                sx={{ mb: 2 }}
                required
                helperText="Maximum length for VARCHAR (e.g., 50, 100, 255)"
              />
            )}

            {(selectedDataType === 'DECIMAL' || selectedDataType === 'NUMERIC') && (
              <Grid container spacing={2} sx={{ mb: 2 }}>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Precision"
                    variant="outlined"
                    type="number"
                    value={columnPrecision}
                    onChange={(e) => setColumnPrecision(e.target.value)}
                    required
                    helperText="Total digits (e.g., 10)"
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Scale"
                    variant="outlined"
                    type="number"
                    value={columnScale}
                    onChange={(e) => setColumnScale(e.target.value)}
                    helperText="Decimal places (e.g., 2)"
                  />
                </Grid>
              </Grid>
            )}

            <TextField
              fullWidth
              label="Default Value (optional)"
              variant="outlined"
              value={defaultValue}
              onChange={(e) => setDefaultValue(e.target.value)}
              sx={{ mb: 2 }}
              helperText="Default value for the column (leave empty for no default)"
            />

            <FormControlLabel
              control={
                <Switch
                  checked={!isNullable}
                  onChange={(e) => setIsNullable(!e.target.checked)}
                  color="primary"
                />
              }
              label="NOT NULL"
              sx={{ mb: 1 }}
            />
            <FormHelperText sx={{ mb: 2 }}>
              If enabled, the column will not allow NULL values
            </FormHelperText>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowAddColumnDialog(false)}>Cancel</Button>
          <Button
            variant="contained"
            color="success"
            onClick={addColumn}
            disabled={addColumnLoading || !newColumnName.trim() || !selectedDataType}
            startIcon={addColumnLoading ? <CircularProgress size={20} /> : <AddIcon />}
          >
            {addColumnLoading ? 'Adding...' : 'Add Column'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Remove Column Dialog */}
      <Dialog
        open={showRemoveColumnDialog}
        onClose={() => setShowRemoveColumnDialog(false)}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <DeleteIcon sx={{ mr: 1, color: theme.palette.error.main }} />
            Remove Column from {selectedTable}
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 1 }}>
            <Alert severity="warning" sx={{ mb: 2 }}>
              Warning: Removing a column will permanently delete all data stored in that column.
              This action cannot be undone.
            </Alert>

            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel id="column-to-remove-label">Column to Remove</InputLabel>
              <Select
                labelId="column-to-remove-label"
                value={columnToRemove}
                onChange={(e) => setColumnToRemove(e.target.value)}
                label="Column to Remove"
                required
              >
                {tableSchema?.columns
                  .filter(column => !tableSchema.primaryKeys.includes(column.column_name))
                  .map((column) => (
                    <MenuItem key={column.column_name} value={column.column_name}>
                      {column.column_name} ({column.data_type})
                      {tableSchema.foreignKeys.some(fk => fk.column_name === column.column_name) &&
                        ' - Foreign Key'}
                    </MenuItem>
                  ))}
              </Select>
              <FormHelperText>
                Select the column you want to remove. Primary keys cannot be removed.
              </FormHelperText>
            </FormControl>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowRemoveColumnDialog(false)}>Cancel</Button>
          <Button
            variant="contained"
            color="error"
            onClick={removeColumn}
            disabled={removeColumnLoading || !columnToRemove}
            startIcon={removeColumnLoading ? <CircularProgress size={20} /> : <DeleteIcon />}
          >
            {removeColumnLoading ? 'Removing...' : 'Remove Column'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>

      {/* Generate Sample Data Dialog */}
      <Dialog
        open={showGenerateSampleDataDialog}
        onClose={() => setShowGenerateSampleDataDialog(false)}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <AddCircleIcon sx={{ mr: 1, color: theme.palette.success.main }} />
            Generate Sample Data for {selectedTable}
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 1 }}>
            <Alert severity="info" sx={{ mb: 2 }}>
              This will generate random sample data for the {selectedTable} table.
              Currently, this feature is only available for the patient_visits table.
            </Alert>

            <TextField
              fullWidth
              label="Number of Records"
              variant="outlined"
              type="number"
              value={sampleDataCount}
              onChange={(e) => setSampleDataCount(e.target.value)}
              sx={{ mb: 2, mt: 1 }}
              required
              helperText="Enter the number of sample records to generate (max 50)"
              InputProps={{
                inputProps: { min: 1, max: 50 }
              }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowGenerateSampleDataDialog(false)}>Cancel</Button>
          <Button
            variant="contained"
            color="success"
            onClick={generateSampleData}
            disabled={generateSampleDataLoading || !sampleDataCount || parseInt(sampleDataCount) < 1}
            startIcon={generateSampleDataLoading ? <CircularProgress size={20} /> : <AddCircleIcon />}
          >
            {generateSampleDataLoading ? 'Generating...' : 'Generate Data'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete All Data Confirmation Dialog */}
      <Dialog
        open={showDeleteAllDataConfirmDialog}
        onClose={() => setShowDeleteAllDataConfirmDialog(false)}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <DeleteSweepIcon sx={{ mr: 1, color: theme.palette.error.main }} />
            Delete All Data from {selectedTable}
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 1 }}>
            <Alert severity="error" sx={{ mb: 2 }}>
              Warning: This action will permanently delete ALL data from the {selectedTable} table.
              This action cannot be undone.
            </Alert>

            <Typography variant="body1" sx={{ mb: 2 }}>
              Are you sure you want to delete all {tableSchema?.rowCount} records from the {selectedTable} table?
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowDeleteAllDataConfirmDialog(false)}>Cancel</Button>
          <Button
            variant="contained"
            color="error"
            onClick={deleteAllData}
            disabled={deleteAllDataLoading}
            startIcon={deleteAllDataLoading ? <CircularProgress size={20} /> : <DeleteSweepIcon />}
          >
            {deleteAllDataLoading ? 'Deleting...' : 'Delete All Data'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Alter Column Type Dialog */}
      <Dialog
        open={showAlterColumnTypeDialog}
        onClose={() => setShowAlterColumnTypeDialog(false)}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <EditIcon sx={{ mr: 1, color: theme.palette.warning.main }} />
            Alter Column Data Type in {selectedTable}
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 1 }}>
            <Alert severity="warning" sx={{ mb: 2 }}>
              Warning: Changing a column's data type can lead to data loss if the new type is incompatible with existing values.
              Make sure to back up your data before proceeding.
            </Alert>

            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel id="column-to-alter-label">Column to Alter</InputLabel>
              <Select
                labelId="column-to-alter-label"
                value={columnToAlter}
                onChange={(e) => {
                  const selectedColumn = e.target.value;
                  setColumnToAlter(selectedColumn);

                  // Find the current data type of the selected column
                  if (tableSchema) {
                    const column = tableSchema.columns.find(col => col.column_name === selectedColumn);
                    if (column) {
                      setCurrentColumnType(column.data_type);
                    }
                  }
                }}
                label="Column to Alter"
                required
              >
                {tableSchema?.columns.map((column) => (
                  <MenuItem key={column.column_name} value={column.column_name}>
                    {column.column_name} ({column.data_type})
                  </MenuItem>
                ))}
              </Select>
              <FormHelperText>
                Select the column whose data type you want to change
              </FormHelperText>
            </FormControl>

            {currentColumnType && (
              <Alert severity="info" sx={{ mb: 2 }}>
                Current data type: <strong>{currentColumnType}</strong>
              </Alert>
            )}

            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel id="new-data-type-label">New Data Type</InputLabel>
              <Select
                labelId="new-data-type-label"
                value={newDataType}
                onChange={(e) => setNewDataType(e.target.value)}
                label="New Data Type"
                required
              >
                {dataTypes.map((type) => (
                  <MenuItem key={type.name} value={type.name}>
                    {type.name} - {type.description}
                  </MenuItem>
                ))}
              </Select>
              <FormHelperText>Select the new data type for the column</FormHelperText>
            </FormControl>

            {newDataType === 'VARCHAR' && (
              <TextField
                fullWidth
                label="Length"
                variant="outlined"
                type="number"
                value={columnLength}
                onChange={(e) => setColumnLength(e.target.value)}
                sx={{ mb: 2 }}
                required
                helperText="Maximum length for VARCHAR (e.g., 50, 100, 255)"
              />
            )}

            {(newDataType === 'DECIMAL' || newDataType === 'NUMERIC') && (
              <Grid container spacing={2} sx={{ mb: 2 }}>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Precision"
                    variant="outlined"
                    type="number"
                    value={columnPrecision}
                    onChange={(e) => setColumnPrecision(e.target.value)}
                    required
                    helperText="Total digits (e.g., 10)"
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Scale"
                    variant="outlined"
                    type="number"
                    value={columnScale}
                    onChange={(e) => setColumnScale(e.target.value)}
                    helperText="Decimal places (e.g., 2)"
                  />
                </Grid>
              </Grid>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowAlterColumnTypeDialog(false)}>Cancel</Button>
          <Button
            variant="contained"
            color="warning"
            onClick={alterColumnType}
            disabled={alterColumnLoading || !columnToAlter || !newDataType}
            startIcon={alterColumnLoading ? <CircularProgress size={20} /> : <EditIcon />}
          >
            {alterColumnLoading ? 'Altering...' : 'Alter Column Type'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Table Data Dialog */}
      <Dialog
        open={showDeleteTableDataDialog}
        onClose={() => setShowDeleteTableDataDialog(false)}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <DeleteSweepIcon sx={{ mr: 1, color: theme.palette.error.main }} />
            Delete All Data from {selectedTable}
          </Box>
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 1 }}>
            <Alert severity="error" sx={{ mb: 2 }}>
              Warning: This action will permanently delete ALL data from the {selectedTable} table.
              This action cannot be undone.
            </Alert>

            <Typography variant="body1" sx={{ mb: 2 }}>
              Are you sure you want to delete all data from the {selectedTable} table?
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowDeleteTableDataDialog(false)}>Cancel</Button>
          <Button
            variant="contained"
            color="error"
            onClick={deleteTableData}
            disabled={deleteTableDataLoading}
            startIcon={deleteTableDataLoading ? <CircularProgress size={20} /> : <DeleteSweepIcon />}
          >
            {deleteTableDataLoading ? 'Deleting...' : 'Delete All Data'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DatabaseDebug;
