import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Chip,
  IconButton,
  Tabs,
  Tab,
  Divider,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  CircularProgress,
  Alert,
  useTheme,
  alpha
} from '@mui/material';
import {
  Close as CloseIcon,
  Edit as EditIcon,
  History as HistoryIcon,
  Security as SecurityIcon,
  Category as CategoryIcon,
  LocalOffer as TagIcon,
  Publish as PublishIcon,
  Unpublished as UnpublishedIcon,
  Person as PersonIcon,
  Event as EventIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { 
  ClinicalGuidance,
  GuidanceVersion,
  GuidanceAudit,
  GuidanceAccess,
  getVersionHistory,
  getAuditTrail,
  getAccess
} from '../../../services/clinicalGuidanceService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index, ...other }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`guidance-viewer-tabpanel-${index}`}
      aria-labelledby={`guidance-viewer-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 2 }}>{children}</Box>}
    </div>
  );
};

interface GuidanceViewerProps {
  open: boolean;
  onClose: () => void;
  guidance: ClinicalGuidance;
  onEdit: () => void;
}

const GuidanceViewer: React.FC<GuidanceViewerProps> = ({
  open,
  onClose,
  guidance,
  onEdit
}) => {
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);
  const [versions, setVersions] = useState<GuidanceVersion[]>([]);
  const [audit, setAudit] = useState<GuidanceAudit[]>([]);
  const [access, setAccess] = useState<GuidanceAccess[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch additional data when guidance changes
  useEffect(() => {
    const fetchData = async () => {
      if (!guidance || !open) return;
      
      try {
        setLoading(true);
        setError(null);

        // Fetch version history
        const versionsData = await getVersionHistory(guidance.guidance_id);
        setVersions(versionsData);

        // Fetch audit trail
        const auditData = await getAuditTrail(guidance.guidance_id);
        setAudit(auditData);

        // Fetch access control
        const accessData = await getAccess(guidance.guidance_id);
        setAccess(accessData);
      } catch (err) {
        console.error('Error fetching guidance details:', err);
        setError('Failed to load guidance details. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [guidance, open]);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Get action color
  const getActionColor = (action: string) => {
    switch (action) {
      case 'create':
        return theme.palette.success.main;
      case 'update':
        return theme.palette.info.main;
      case 'delete':
        return theme.palette.error.main;
      case 'publish':
        return theme.palette.success.main;
      case 'unpublish':
        return theme.palette.warning.main;
      case 'update_access':
        return theme.palette.secondary.main;
      default:
        return theme.palette.text.primary;
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          maxHeight: '90vh'
        }
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        bgcolor: theme.palette.primary.main,
        color: 'white',
        p: 2
      }}>
        <Typography variant="h6">
          Guidance Details
        </Typography>
        <IconButton onClick={onClose} sx={{ color: 'white' }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 0 }}>
        {/* Error Alert */}
        {error && (
          <Alert severity="error" sx={{ m: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}

        {/* Header Section */}
        <Box sx={{ p: 3, borderBottom: '1px solid', borderColor: 'divider' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
            <Chip
              icon={guidance.is_published ? <PublishIcon /> : <UnpublishedIcon />}
              label={guidance.is_published ? 'Published' : 'Draft'}
              color={guidance.is_published ? 'success' : 'warning'}
              size="small"
              sx={{ mr: 1 }}
            />
            <Typography variant="caption" color="text.secondary">
              ID: {guidance.guidance_id}
            </Typography>
          </Box>
          <Typography variant="h5" gutterBottom>
            {guidance.title}
          </Typography>
          {guidance.summary && (
            <Typography variant="body1" color="text.secondary" paragraph>
              {guidance.summary}
            </Typography>
          )}
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
            {guidance.category_name && (
              <Chip
                size="small"
                icon={<CategoryIcon fontSize="small" />}
                label={guidance.category_name}
              />
            )}
            {guidance.context_key && (
              <Chip
                size="small"
                label={`Context: ${guidance.context_key}`}
              />
            )}
            {guidance.tags &&
              guidance.tags.map(
                (tag, index) =>
                  tag && (
                    <Chip
                      key={index}
                      size="small"
                      icon={<TagIcon fontSize="small" />}
                      label={tag}
                    />
                  )
              )}
          </Box>
          <Box sx={{ display: 'flex', mt: 2, color: 'text.secondary', fontSize: '0.875rem' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mr: 3 }}>
              <PersonIcon fontSize="small" sx={{ mr: 0.5 }} />
              Created by: {guidance.created_by_username || 'Unknown'}
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', mr: 3 }}>
              <EventIcon fontSize="small" sx={{ mr: 0.5 }} />
              Created: {formatDate(guidance.created_at)}
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <EventIcon fontSize="small" sx={{ mr: 0.5 }} />
              Updated: {formatDate(guidance.updated_at)}
            </Box>
          </Box>
        </Box>

        {/* Tabs */}
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab label="Content" />
          <Tab label="Version History" />
          <Tab label="Audit Trail" />
          <Tab label="Access Control" />
        </Tabs>

        {/* Content Tab */}
        <TabPanel value={tabValue} index={0}>
          <Box 
            sx={{ 
              p: 2,
              minHeight: 300,
              maxHeight: 500,
              overflow: 'auto'
            }}
            dangerouslySetInnerHTML={{ __html: guidance.content }}
          />
        </TabPanel>

        {/* Version History Tab */}
        <TabPanel value={tabValue} index={1}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : versions.length === 0 ? (
            <Box sx={{ textAlign: 'center', p: 4 }}>
              <InfoIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                No version history available
              </Typography>
            </Box>
          ) : (
            <List>
              {versions.map((version) => (
                <React.Fragment key={version.version_id}>
                  <ListItem
                    alignItems="flex-start"
                    sx={{
                      borderRadius: 1,
                      mb: 1,
                      '&:hover': {
                        bgcolor: alpha(theme.palette.primary.main, 0.05)
                      }
                    }}
                  >
                    <ListItemIcon>
                      <Box
                        sx={{
                          width: 32,
                          height: 32,
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          bgcolor: alpha(theme.palette.primary.main, 0.1),
                          color: theme.palette.primary.main,
                          fontWeight: 'bold'
                        }}
                      >
                        {version.version_number}
                      </Box>
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography variant="subtitle1" fontWeight="medium">
                          {version.title}
                        </Typography>
                      }
                      secondary={
                        <Box sx={{ mt: 0.5 }}>
                          <Typography variant="body2" color="text.secondary">
                            Created by {version.created_by_username || 'Unknown'} on {formatDate(version.created_at)}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                  <Divider component="li" />
                </React.Fragment>
              ))}
            </List>
          )}
        </TabPanel>

        {/* Audit Trail Tab */}
        <TabPanel value={tabValue} index={2}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : audit.length === 0 ? (
            <Box sx={{ textAlign: 'center', p: 4 }}>
              <InfoIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                No audit trail available
              </Typography>
            </Box>
          ) : (
            <List>
              {audit.map((entry) => (
                <React.Fragment key={entry.audit_id}>
                  <ListItem
                    alignItems="flex-start"
                    sx={{
                      borderRadius: 1,
                      mb: 1,
                      '&:hover': {
                        bgcolor: alpha(theme.palette.primary.main, 0.05)
                      }
                    }}
                  >
                    <ListItemIcon>
                      <Box
                        sx={{
                          width: 32,
                          height: 32,
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          bgcolor: alpha(getActionColor(entry.action), 0.1),
                          color: getActionColor(entry.action)
                        }}
                      >
                        <HistoryIcon fontSize="small" />
                      </Box>
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography variant="subtitle1" fontWeight="medium">
                          {entry.action.charAt(0).toUpperCase() + entry.action.slice(1).replace('_', ' ')}
                        </Typography>
                      }
                      secondary={
                        <Box sx={{ mt: 0.5 }}>
                          <Typography variant="body2" color="text.secondary">
                            By {entry.performed_by_username || 'Unknown'} on {formatDate(entry.performed_at)}
                          </Typography>
                          {entry.details && (
                            <Typography variant="body2" sx={{ mt: 0.5 }}>
                              {JSON.stringify(entry.details, null, 2)}
                            </Typography>
                          )}
                        </Box>
                      }
                    />
                  </ListItem>
                  <Divider component="li" />
                </React.Fragment>
              ))}
            </List>
          )}
        </TabPanel>

        {/* Access Control Tab */}
        <TabPanel value={tabValue} index={3}>
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : access.length === 0 ? (
            <Box sx={{ textAlign: 'center', p: 4 }}>
              <InfoIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                No access control settings available
              </Typography>
              <Typography variant="body2" color="text.secondary">
                This guidance is accessible to all users by default
              </Typography>
            </Box>
          ) : (
            <List>
              {access.map((entry) => (
                <React.Fragment key={entry.access_id}>
                  <ListItem
                    alignItems="flex-start"
                    sx={{
                      borderRadius: 1,
                      mb: 1,
                      '&:hover': {
                        bgcolor: alpha(theme.palette.primary.main, 0.05)
                      }
                    }}
                  >
                    <ListItemIcon>
                      <Box
                        sx={{
                          width: 32,
                          height: 32,
                          borderRadius: '50%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          bgcolor: alpha(theme.palette.secondary.main, 0.1),
                          color: theme.palette.secondary.main
                        }}
                      >
                        <SecurityIcon fontSize="small" />
                      </Box>
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Typography variant="subtitle1" fontWeight="medium">
                          {entry.role.charAt(0).toUpperCase() + entry.role.slice(1)}
                        </Typography>
                      }
                      secondary={
                        <Box sx={{ mt: 0.5 }}>
                          <Typography variant="body2" color="text.secondary">
                            Can view: {entry.can_view ? 'Yes' : 'No'}, Can edit: {entry.can_edit ? 'Yes' : 'No'}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Last updated: {formatDate(entry.updated_at)}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                  <Divider component="li" />
                </React.Fragment>
              ))}
            </List>
          )}
        </TabPanel>
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        <Button onClick={onClose}>
          Close
        </Button>
        <Button
          variant="contained"
          color="primary"
          startIcon={<EditIcon />}
          onClick={onEdit}
        >
          Edit
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default GuidanceViewer;
