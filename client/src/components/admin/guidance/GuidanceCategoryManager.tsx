import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  IconButton,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Divider,
  CircularProgress,
  Alert,
  useTheme,
  alpha,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent
} from '@mui/material';
import {
  Close as CloseIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Category as CategoryIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Folder as FolderIcon,
  FolderOpen as FolderOpenIcon
} from '@mui/icons-material';
import {
  GuidanceCategory,
  getAllCategories
} from '../../../services/clinicalGuidanceService';

interface GuidanceCategoryManagerProps {
  open: boolean;
  onClose: (refreshNeeded?: boolean) => void;
  categories: GuidanceCategory[];
}

const GuidanceCategoryManager: React.FC<GuidanceCategoryManagerProps> = ({
  open,
  onClose,
  categories: initialCategories
}) => {
  const theme = useTheme();
  const [categories, setCategories] = useState<GuidanceCategory[]>(initialCategories);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [editMode, setEditMode] = useState<number | null>(null);
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [formData, setFormData] = useState<Partial<GuidanceCategory>>({
    name: '',
    description: '',
    parent_id: null
  });
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // Fetch categories when component mounts or refreshTrigger changes
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await getAllCategories();
        setCategories(data);
      } catch (err) {
        console.error('Error fetching categories:', err);
        setError('Failed to load categories. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, [refreshTrigger]);

  // Handle text input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle select changes
  const handleSelectChange = (e: SelectChangeEvent<number | null>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value === '' ? null : value }));
  };

  // Handle add new category
  const handleAddNew = () => {
    setFormData({
      name: '',
      description: '',
      parent_id: null
    });
    setIsAddingNew(true);
    setEditMode(null);
  };

  // Handle edit category
  const handleEdit = (category: GuidanceCategory) => {
    setFormData({
      name: category.name,
      description: category.description || '',
      parent_id: category.parent_id
    });
    setEditMode(category.category_id);
    setIsAddingNew(false);
  };

  // Handle cancel edit/add
  const handleCancel = () => {
    setEditMode(null);
    setIsAddingNew(false);
    setFormData({
      name: '',
      description: '',
      parent_id: null
    });
  };

  // Handle save category
  const handleSave = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      // Validate required fields
      if (!formData.name) {
        setError('Category name is required');
        setLoading(false);
        return;
      }

      // Prepare data for submission
      const submissionData = {
        name: formData.name,
        description: formData.description || null,
        parentId: formData.parent_id
      };

      // Make API call to create or update category
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      let response;
      if (isAddingNew) {
        // Create new category
        response = await fetch(`${process.env.REACT_APP_API_URL || ''}/api/clinical-guidance/categories`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          },
          body: JSON.stringify(submissionData)
        });
      } else if (editMode !== null) {
        // Update existing category
        response = await fetch(`${process.env.REACT_APP_API_URL || ''}/api/clinical-guidance/categories/${editMode}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          },
          body: JSON.stringify(submissionData)
        });
      } else {
        throw new Error('Invalid operation');
      }

      if (!response.ok) {
        throw new Error(`Error ${isAddingNew ? 'creating' : 'updating'} category: ${response.statusText}`);
      }

      // Reset form and refresh categories
      setSuccess(`Category ${isAddingNew ? 'created' : 'updated'} successfully`);
      setEditMode(null);
      setIsAddingNew(false);
      setFormData({
        name: '',
        description: '',
        parent_id: null
      });
      setRefreshTrigger(prev => prev + 1);
    } catch (err) {
      console.error('Error saving category:', err);
      setError(`Failed to ${isAddingNew ? 'create' : 'update'} category. Please try again.`);
    } finally {
      setLoading(false);
    }
  };

  // Handle delete category
  const handleDelete = async (categoryId: number) => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      // Make API call to delete category
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${process.env.REACT_APP_API_URL || ''}/api/clinical-guidance/categories/${categoryId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error(`Error deleting category: ${response.statusText}`);
      }

      // Refresh categories
      setSuccess('Category deleted successfully');
      setRefreshTrigger(prev => prev + 1);
    } catch (err) {
      console.error('Error deleting category:', err);
      setError('Failed to delete category. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={() => onClose()}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2
        }
      }}
    >
      <DialogTitle sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        bgcolor: theme.palette.primary.main,
        color: 'white',
        p: 2
      }}>
        <Typography variant="h6">
          Manage Guidance Categories
        </Typography>
        <IconButton onClick={() => onClose()} sx={{ color: 'white' }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        {/* Error and Success Alerts */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}
        {success && (
          <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        )}

        {/* Add/Edit Form */}
        {(isAddingNew || editMode !== null) && (
          <Box sx={{ mb: 3, p: 2, border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
            <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
              {isAddingNew ? 'Add New Category' : 'Edit Category'}
            </Typography>
            <TextField
              name="name"
              label="Category Name"
              value={formData.name || ''}
              onChange={handleInputChange}
              fullWidth
              margin="normal"
              required
              disabled={loading}
            />
            <TextField
              name="description"
              label="Description"
              value={formData.description || ''}
              onChange={handleInputChange}
              fullWidth
              margin="normal"
              multiline
              rows={2}
              disabled={loading}
            />
            <FormControl fullWidth margin="normal">
              <InputLabel id="parent-category-label">Parent Category</InputLabel>
              <Select
                labelId="parent-category-label"
                name="parent_id"
                value={formData.parent_id || ''}
                onChange={handleSelectChange}
                label="Parent Category"
                disabled={loading}
              >
                <MenuItem value="">
                  <em>None (Top Level)</em>
                </MenuItem>
                {categories
                  .filter(cat => cat.category_id !== editMode) // Prevent circular references
                  .map((category) => (
                    <MenuItem key={category.category_id} value={category.category_id}>
                      {category.name}
                    </MenuItem>
                  ))}
              </Select>
            </FormControl>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
              <Button
                variant="outlined"
                startIcon={<CancelIcon />}
                onClick={handleCancel}
                disabled={loading}
                sx={{ mr: 1 }}
              >
                Cancel
              </Button>
              <Button
                variant="contained"
                color="primary"
                startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
                onClick={handleSave}
                disabled={loading}
              >
                Save
              </Button>
            </Box>
          </Box>
        )}

        {/* Categories List */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="subtitle1" fontWeight="medium">
            Categories
          </Typography>
          <Button
            size="small"
            startIcon={<AddIcon />}
            onClick={handleAddNew}
            disabled={loading || isAddingNew || editMode !== null}
          >
            Add Category
          </Button>
        </Box>

        {loading && categories.length === 0 ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        ) : categories.length === 0 ? (
          <Box sx={{ textAlign: 'center', p: 4 }}>
            <CategoryIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              No categories found
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAddNew}
              disabled={loading || isAddingNew || editMode !== null}
              sx={{ mt: 2 }}
            >
              Add First Category
            </Button>
          </Box>
        ) : (
          <List sx={{ width: '100%' }}>
            {categories.map((category) => (
              <React.Fragment key={category.category_id}>
                <ListItem
                  sx={{
                    borderRadius: 1,
                    mb: 1,
                    '&:hover': {
                      bgcolor: alpha(theme.palette.primary.main, 0.05)
                    }
                  }}
                >
                  <ListItemIcon>
                    {category.parent_id ? (
                      <FolderOpenIcon color="primary" />
                    ) : (
                      <FolderIcon color="primary" />
                    )}
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Typography variant="subtitle1" fontWeight="medium">
                        {category.name}
                      </Typography>
                    }
                    secondary={
                      <Box sx={{ mt: 0.5 }}>
                        {category.description && (
                          <Typography variant="body2" color="text.secondary">
                            {category.description}
                          </Typography>
                        )}
                        <Box sx={{ display: 'flex', mt: 0.5 }}>
                          {category.parent_name && (
                            <Typography variant="caption" color="text.secondary" sx={{ mr: 2 }}>
                              Parent: {category.parent_name}
                            </Typography>
                          )}
                          <Typography variant="caption" color="text.secondary">
                            Guidance: {category.guidance_count || 0}
                          </Typography>
                        </Box>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <Box sx={{ display: 'flex' }}>
                      <IconButton
                        edge="end"
                        aria-label="edit"
                        onClick={() => handleEdit(category)}
                        size="small"
                        sx={{ mr: 1 }}
                        disabled={loading || isAddingNew || editMode !== null}
                      >
                        <EditIcon fontSize="small" />
                      </IconButton>
                      <IconButton
                        edge="end"
                        aria-label="delete"
                        onClick={() => handleDelete(category.category_id)}
                        size="small"
                        disabled={loading || isAddingNew || editMode !== null || Boolean(category.guidance_count && category.guidance_count > 0)}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  </ListItemSecondaryAction>
                </ListItem>
                <Divider component="li" />
              </React.Fragment>
            ))}
          </List>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 2 }}>
        <Button onClick={() => onClose(true)}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default GuidanceCategoryManager;
