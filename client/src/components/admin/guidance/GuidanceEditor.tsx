import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Typography,
  Box,
  Chip,
  IconButton,
  Grid,
  Autocomplete,
  CircularProgress,
  Alert,
  Divider,
  useTheme,
  SelectChangeEvent,
  Paper,
  Tooltip
} from '@mui/material';
import {
  Close as CloseIcon,
  Save as SaveIcon,
  Publish as PublishIcon,
  Visibility as VisibilityIcon,
  Code as CodeIcon
} from '@mui/icons-material';
import {
  ClinicalGuidance,
  GuidanceCategory,
  createGuidance,
  updateGuidance
} from '../../../services/clinicalGuidanceService';



interface GuidanceEditorProps {
  open: boolean;
  onClose: (refreshNeeded?: boolean) => void;
  guidance: ClinicalGuidance | null;
  isNew: boolean;
  categories: GuidanceCategory[];
  availableTags: string[];
}

const GuidanceEditor: React.FC<GuidanceEditorProps> = ({
  open,
  onClose,
  guidance,
  isNew,
  categories,
  availableTags
}) => {
  const theme = useTheme();
  const [formData, setFormData] = useState<Partial<ClinicalGuidance>>({
    title: '',
    content: '',
    summary: '',
    category_id: null,
    context_key: '',
    is_published: false,
    tags: []
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [previewMode, setPreviewMode] = useState(false);
  const [showHtmlSource, setShowHtmlSource] = useState(false);
  const [autoSaveTimer, setAutoSaveTimer] = useState<NodeJS.Timeout | null>(null);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Initialize form data when guidance changes
  useEffect(() => {
    if (guidance) {
      setFormData({
        title: guidance.title || '',
        content: guidance.content || '',
        summary: guidance.summary || '',
        category_id: guidance.category_id || null,
        context_key: guidance.context_key || '',
        is_published: guidance.is_published || false
      });
      setSelectedTags(guidance.tags || []);
    } else {
      setFormData({
        title: '',
        content: '',
        summary: '',
        category_id: null,
        context_key: '',
        is_published: false
      });
      setSelectedTags([]);
    }
  }, [guidance]);

  // Handle text input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Setup autosave for content field
    if (name === 'content') {
      if (autoSaveTimer) {
        clearTimeout(autoSaveTimer);
      }

      // Only autosave if not a new guidance and has an ID
      if (!isNew && guidance?.guidance_id) {
        const timer = setTimeout(() => {
          handleAutoSave();
        }, 10000); // Autosave after 10 seconds of inactivity

        setAutoSaveTimer(timer);
      }
    }
  };

  // Handle select changes
  const handleSelectChange = (e: SelectChangeEvent<number | null>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle switch changes
  const handleSwitchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: checked }));
  };



  // Handle autosave
  const handleAutoSave = async () => {
    if (isNew || !guidance?.guidance_id || !formData.title || !formData.content) {
      return;
    }

    try {
      // Prepare data for submission
      const submissionData = {
        ...formData,
        tags: selectedTags
      };

      // Update existing guidance
      await updateGuidance(guidance.guidance_id, submissionData);
      setLastSaved(new Date());
    } catch (err) {
      console.error('Error auto-saving guidance:', err);
      // Don't show error to user for autosave
    }
  };

  // Handle tag changes
  const handleTagsChange = (event: React.SyntheticEvent, newValue: string[]) => {
    setSelectedTags(newValue);
  };

  // Toggle preview mode
  const togglePreviewMode = () => {
    setPreviewMode(!previewMode);
  };

  // Toggle HTML source view
  const toggleHtmlSource = () => {
    setShowHtmlSource(!showHtmlSource);
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setLoading(true);
      setError(null);
      setSuccess(null);

      // Validate required fields
      if (!formData.title || !formData.content) {
        setError('Title and content are required');
        setLoading(false);
        return;
      }

      // Prepare data for submission
      const submissionData = {
        ...formData,
        tags: selectedTags
      };

      if (isNew) {
        // Create new guidance
        await createGuidance(submissionData);
        setSuccess('Guidance created successfully');
      } else if (guidance) {
        // Update existing guidance
        await updateGuidance(guidance.guidance_id, submissionData);
        setSuccess('Guidance updated successfully');
      }

      // Auto-close after success
      setTimeout(() => {
        onClose(true);
      }, 1500);
    } catch (err) {
      console.error('Error saving guidance:', err);
      setError('Failed to save guidance. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // TODO: Uncomment when ReactQuill is installed
  // Quill editor modules and formats
  // const modules = {
  //   toolbar: [
  //     [{ header: [1, 2, 3, 4, 5, 6, false] }],
  //     ['bold', 'italic', 'underline', 'strike'],
  //     [{ list: 'ordered' }, { list: 'bullet' }],
  //     [{ indent: '-1' }, { indent: '+1' }],
  //     [{ color: [] }, { background: [] }],
  //     ['link', 'image'],
  //     ['clean']
  //   ]
  // };

  // const formats = [
  //   'header',
  //   'bold', 'italic', 'underline', 'strike',
  //   'list', 'bullet', 'indent',
  //   'link', 'image', 'color', 'background'
  // ];

  return (
    <Dialog
      open={open}
      onClose={() => onClose()}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          maxHeight: '90vh'
        }
      }}
    >
      <DialogTitle sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        bgcolor: theme.palette.primary.main,
        color: 'white',
        p: 2
      }}>
        <Typography variant="h6">
          {isNew ? 'Create New Guidance' : 'Edit Guidance'}
        </Typography>
        <IconButton onClick={() => onClose()} sx={{ color: 'white' }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ p: 3 }}>
        {/* Error and Success Alerts */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}
        {success && (
          <Alert severity="success" sx={{ mb: 3 }} onClose={() => setSuccess(null)}>
            {success}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Left Column - Basic Info */}
          <Grid item xs={12} md={4}>
            <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
              Basic Information
            </Typography>
            <TextField
              name="title"
              label="Title"
              value={formData.title || ''}
              onChange={handleInputChange}
              fullWidth
              margin="normal"
              required
              disabled={loading}
            />
            <TextField
              name="summary"
              label="Summary"
              value={formData.summary || ''}
              onChange={handleInputChange}
              fullWidth
              margin="normal"
              multiline
              rows={3}
              disabled={loading}
              helperText="Brief summary of the guidance content"
            />
            <FormControl fullWidth margin="normal">
              <InputLabel id="category-label">Category</InputLabel>
              <Select
                labelId="category-label"
                name="category_id"
                value={formData.category_id || ''}
                onChange={handleSelectChange}
                label="Category"
                disabled={loading}
              >
                <MenuItem value="">
                  <em>None</em>
                </MenuItem>
                {categories.map((category) => (
                  <MenuItem key={category.category_id} value={category.category_id}>
                    {category.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
            <TextField
              name="context_key"
              label="Context Key"
              value={formData.context_key || ''}
              onChange={handleInputChange}
              fullWidth
              margin="normal"
              disabled={loading}
              helperText="Key for contextual linking (e.g., lab_results, frailty_assessment)"
            />
            <Autocomplete
              multiple
              id="tags"
              options={availableTags}
              value={selectedTags}
              onChange={handleTagsChange}
              freeSolo
              renderTags={(value, getTagProps) =>
                value.map((option, index) => (
                  <Chip
                    label={option}
                    {...getTagProps({ index })}
                    size="small"
                    sx={{ m: 0.5 }}
                  />
                ))
              }
              renderInput={(params) => (
                <TextField
                  {...params}
                  label="Tags"
                  margin="normal"
                  helperText="Press Enter to add new tags"
                  disabled={loading}
                />
              )}
            />
            <FormControlLabel
              control={
                <Switch
                  name="is_published"
                  checked={formData.is_published || false}
                  onChange={handleSwitchChange}
                  disabled={loading}
                />
              }
              label="Publish Guidance"
              sx={{ mt: 2 }}
            />
          </Grid>

          {/* Right Column - Content Editor */}
          <Grid item xs={12} md={8}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="subtitle1" fontWeight="medium">
                Guidance Content
              </Typography>
              <Box>
                {!previewMode && (
                  <Tooltip title="View HTML Source">
                    <IconButton
                      color={showHtmlSource ? "primary" : "default"}
                      onClick={toggleHtmlSource}
                      size="small"
                      sx={{ mr: 1 }}
                    >
                      <CodeIcon />
                    </IconButton>
                  </Tooltip>
                )}
                <Button
                  size="small"
                  startIcon={<VisibilityIcon />}
                  onClick={togglePreviewMode}
                >
                  {previewMode ? 'Edit' : 'Preview'}
                </Button>
              </Box>
            </Box>

            {previewMode ? (
              <Paper
                elevation={2}
                sx={{
                  p: 3,
                  minHeight: 400,
                  maxHeight: 600,
                  overflow: 'auto',
                  borderRadius: 1
                }}
              >
                <Typography variant="h4" gutterBottom>{formData.title}</Typography>
                {formData.summary && (
                  <Typography variant="subtitle1" color="text.secondary" paragraph>
                    {formData.summary}
                  </Typography>
                )}
                <Box
                  sx={{
                    '& h1, & h2, & h3': {
                      color: theme.palette.primary.main,
                      mt: 2,
                      mb: 1
                    },
                    '& ul, & ol': { pl: 4 },
                    '& img': { maxWidth: '100%' }
                  }}
                  dangerouslySetInnerHTML={{ __html: formData.content || '' }}
                />
              </Paper>
            ) : (
              <Box sx={{ border: '1px solid', borderColor: 'divider', borderRadius: 1 }}>
                <TextField
                  multiline
                  fullWidth
                  name="content"
                  rows={15}
                  value={formData.content || ''}
                  onChange={handleInputChange}
                  disabled={loading}
                  sx={{
                    p: 1,
                    '& .MuiInputBase-root': {
                      fontFamily: showHtmlSource ? 'monospace' : 'inherit'
                    }
                  }}
                  placeholder="Enter guidance content here. You can use HTML tags for formatting."
                />
                {lastSaved && (
                  <Typography variant="caption" color="text.secondary" sx={{ display: 'block', p: 1 }}>
                    Last autosaved: {lastSaved.toLocaleTimeString()}
                  </Typography>
                )}
              </Box>
            )}
          </Grid>
        </Grid>
      </DialogContent>

      <Divider />

      <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>
        <Button onClick={() => onClose()} disabled={loading}>
          Cancel
        </Button>
        <Box>
          {!isNew && formData.is_published === false && (
            <Button
              variant="contained"
              color="success"
              startIcon={<PublishIcon />}
              onClick={() => {
                setFormData(prev => ({ ...prev, is_published: true }));
                handleSubmit();
              }}
              disabled={loading}
              sx={{ mr: 1 }}
            >
              Publish
            </Button>
          )}
          <Button
            variant="contained"
            color="primary"
            startIcon={loading ? <CircularProgress size={20} /> : <SaveIcon />}
            onClick={handleSubmit}
            disabled={loading}
          >
            {isNew ? 'Create' : 'Save Changes'}
          </Button>
        </Box>
      </DialogActions>
    </Dialog>
  );
};

export default GuidanceEditor;
