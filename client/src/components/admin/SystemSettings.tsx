import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  // Card - Unused import
  // CardContent - Unused import
  // CardHeader - Unused import
  Switch,
  FormControlLabel,
  TextField,
  Button,
  Divider,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  Snackbar,
  Tabs,
  Tab,
  CircularProgress,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  Settings as SettingsIcon,
  Security as SecurityIcon,
  Notifications as NotificationsIcon,
  Storage as StorageIcon,
  Email as EmailIcon,
  Language as LanguageIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Help as HelpIcon,
  // Backup as BackupIcon - Unused import
  // RestoreOutlined as RestoreIcon - Unused import
  Tune as TuneIcon,
  Schedule as ScheduleIcon,
  Translate as TranslateIcon
} from '@mui/icons-material';
import axios from 'axios';
import { API_URL } from '../../config';
// TranslationManager - Unused import

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

interface SystemSettingsData {
  setting_id?: number;
  system_name: string;
  timezone: string;
  date_format: string;
  maintenance_mode: boolean;
  maintenance_message?: string;
  session_timeout: number;
  two_factor_auth: boolean;
  ip_restriction: boolean;

  // Email settings
  smtp_server?: string;
  smtp_port?: number;
  smtp_username?: string;
  smtp_password?: string;
  email_from?: string;
  email_enabled: boolean;

  // Notification settings
  email_notifications: boolean;
  sms_notifications: boolean;

  // Backup settings
  auto_backup: boolean;
  backup_frequency: string;
  backup_retention: number;

  // Localization settings
  language: string;
  currency: string;

  // System fields
  created_at?: string;
  updated_at?: string;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const SystemSettings: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [notification, setNotification] = useState<{type: 'success' | 'error', message: string} | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [testingEmail, setTestingEmail] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Default settings state
  const [settings, setSettings] = useState<SystemSettingsData>({
    system_name: 'Medical Records System',
    timezone: 'UTC',
    date_format: 'MM/DD/YYYY',
    maintenance_mode: false,
    session_timeout: 30,
    two_factor_auth: false,
    ip_restriction: false,

    // Email settings
    smtp_server: 'smtp.medapp.com',
    smtp_port: 587,
    smtp_username: 'smtp_user',
    email_from: '<EMAIL>',
    email_enabled: true,

    // Notification settings
    email_notifications: true,
    sms_notifications: false,

    // Backup settings
    auto_backup: true,
    backup_frequency: 'daily',
    backup_retention: 30,

    // Localization settings
    language: 'en',
    currency: 'USD'
  });

  // Fetch settings on component mount
  useEffect(() => {
    fetchSettings();
  }, []);

  // Fetch settings from the API
  const fetchSettings = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found. Please log in again.');
      }

      const response = await axios.get(`${API_URL}/api/admin/system-settings`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      setSettings(response.data);
      console.log('Fetched system settings:', response.data);
    } catch (err) {
      console.error('Error fetching system settings:', err);
      setError(err instanceof Error ? err.message : 'Failed to load system settings');
    } finally {
      setLoading(false);
    }
  };

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value, type, checked } = e.target as HTMLInputElement;

    setSettings(prev => ({
      ...prev,
      [name as string]: type === 'checkbox' ? checked : value
    }));
  };

  // Handle select changes
  const handleSelectChange = (name: string, value: unknown) => {
    setSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Save settings to the API
  const handleSaveSettings = async () => {
    try {
      setSaving(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found. Please log in again.');
      }

      const response = await axios.put(
        `${API_URL}/api/admin/system-settings`,
        settings,
        {
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          }
        }
      );

      console.log('Settings saved successfully:', response.data);
      setNotification({
        type: 'success',
        message: 'Settings saved successfully'
      });
    } catch (err) {
      console.error('Error saving system settings:', err);
      setError(err instanceof Error ? err.message : 'Failed to save system settings');
      setNotification({
        type: 'error',
        message: err instanceof Error ? err.message : 'Failed to save system settings'
      });
    } finally {
      setSaving(false);
    }
  };

  // Test email configuration
  const handleTestEmail = async () => {
    try {
      setTestingEmail(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found. Please log in again.');
      }

      const emailConfig = {
        smtp_server: settings.smtp_server,
        smtp_port: settings.smtp_port,
        smtp_username: settings.smtp_username,
        smtp_password: settings.smtp_password,
        email_from: settings.email_from
      };

      const response = await axios.post(
        `${API_URL}/api/admin/system-settings/test-email`,
        emailConfig,
        {
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          }
        }
      );

      console.log('Email test result:', response.data);
      setNotification({
        type: 'success',
        message: 'Test email sent successfully'
      });
    } catch (err) {
      console.error('Error testing email configuration:', err);
      setError(err instanceof Error ? err.message : 'Failed to test email configuration');
      setNotification({
        type: 'error',
        message: err instanceof Error ? err.message : 'Failed to test email configuration'
      });
    } finally {
      setTestingEmail(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleCloseNotification = () => {
    setNotification(null);
  };

  return (
    <Box sx={{ p: { xs: 2, md: 4 }, maxWidth: 1200, mx: 'auto' }}>
      <Paper
        elevation={2}
        sx={{
          p: { xs: 2, md: 3 },
          mb: 4,
          borderRadius: 2,
          background: 'linear-gradient(to right, #1976d2, #2196f3)',
          color: 'white'
        }}
      >
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, justifyContent: 'space-between', alignItems: { xs: 'flex-start', md: 'center' } }}>
          <Box>
            <Typography variant="h4" fontWeight="500" sx={{ display: 'flex', alignItems: 'center' }}>
              <SettingsIcon sx={{ mr: 1.5, fontSize: 32 }} />
              System Settings
            </Typography>
            <Typography variant="subtitle1" sx={{ opacity: 0.9, mt: 0.5 }}>
              Configure system-wide settings and preferences for the Medical Records application.
            </Typography>
          </Box>
          <Box sx={{ mt: { xs: 2, md: 0 }, display: 'flex' }}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={fetchSettings}
              disabled={loading}
              sx={{
                mr: 1.5,
                color: 'white',
                borderColor: 'rgba(255, 255, 255, 0.5)',
                '&:hover': {
                  borderColor: 'white',
                  backgroundColor: 'rgba(255, 255, 255, 0.1)'
                }
              }}
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              startIcon={<SaveIcon />}
              onClick={handleSaveSettings}
              disabled={loading || saving}
              sx={{
                bgcolor: 'white',
                color: '#1976d2',
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.9)'
                }
              }}
            >
              {saving ? 'Saving...' : 'Save Settings'}
            </Button>
          </Box>
        </Box>
      </Paper>

      {error && (
        <Alert
          severity="error"
          sx={{
            mb: 3,
            borderRadius: 2,
            '& .MuiAlert-icon': {
              fontSize: 24
            }
          }}
        >
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', my: 10 }}>
          <CircularProgress size={60} thickness={4} />
          <Typography variant="h6" sx={{ mt: 2, color: 'text.secondary' }}>
            Loading settings...
          </Typography>
        </Box>
      ) : (
        <>
          <Paper elevation={1} sx={{ borderRadius: 2, mb: 3, overflow: 'hidden' }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              aria-label="settings tabs"
              variant="scrollable"
              scrollButtons="auto"
              sx={{
                borderBottom: 1,
                borderColor: 'divider',
                '& .MuiTab-root': {
                  py: 2,
                  fontSize: '0.95rem',
                  fontWeight: 500,
                  transition: 'all 0.2s',
                  minHeight: 64,
                  '&.Mui-selected': {
                    color: 'primary.main',
                    fontWeight: 600
                  }
                },
                '& .MuiTabs-indicator': {
                  height: 3,
                  borderTopLeftRadius: 3,
                  borderTopRightRadius: 3
                }
              }}
            >
              <Tab label="General" icon={<SettingsIcon />} iconPosition="start" />
              <Tab label="Security" icon={<SecurityIcon />} iconPosition="start" />
              <Tab label="Notifications" icon={<NotificationsIcon />} iconPosition="start" />
              <Tab label="Backup & Storage" icon={<StorageIcon />} iconPosition="start" />
              <Tab label="Email" icon={<EmailIcon />} iconPosition="start" />
              <Tab label="Localization" icon={<LanguageIcon />} iconPosition="start" />
            </Tabs>
          </Paper>
        </>
      )}

      {/* General Settings */}
      <TabPanel value={tabValue} index={0}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Paper
              elevation={1}
              sx={{
                p: 3,
                borderRadius: 2,
                height: '100%',
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  boxShadow: '0 8px 16px 0 rgba(0,0,0,0.1)',
                  transform: 'translateY(-2px)'
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <SettingsIcon color="primary" sx={{ mr: 1.5, fontSize: 28 }} />
                <Typography variant="h6" fontWeight="500">
                  Application Settings
                </Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />

              <TextField
                name="system_name"
                label="System Name"
                fullWidth
                margin="normal"
                value={settings.system_name}
                onChange={handleChange}
                disabled={loading || saving}
                variant="outlined"
                InputProps={{
                  sx: { borderRadius: 1.5 }
                }}
                helperText="The name of your medical records system"
              />

              <FormControl fullWidth margin="normal" variant="outlined">
                <InputLabel>Time Zone</InputLabel>
                <Select
                  name="timezone"
                  value={settings.timezone}
                  label="Time Zone"
                  onChange={(e) => handleSelectChange('timezone', e.target.value)}
                  disabled={loading || saving}
                  sx={{ borderRadius: 1.5 }}
                >
                  <MenuItem value="UTC">UTC</MenuItem>
                  <MenuItem value="EST">Eastern Standard Time (EST)</MenuItem>
                  <MenuItem value="CST">Central Standard Time (CST)</MenuItem>
                  <MenuItem value="MST">Mountain Standard Time (MST)</MenuItem>
                  <MenuItem value="PST">Pacific Standard Time (PST)</MenuItem>
                  <MenuItem value="Asia/Kathmandu">Nepal Time (NPT)</MenuItem>
                </Select>
                <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, ml: 1.5 }}>
                  All dates and times will be displayed in this time zone
                </Typography>
              </FormControl>

              <FormControl fullWidth margin="normal" variant="outlined" sx={{ mt: 3 }}>
                <InputLabel>Date Format</InputLabel>
                <Select
                  name="date_format"
                  value={settings.date_format}
                  label="Date Format"
                  onChange={(e) => handleSelectChange('date_format', e.target.value)}
                  disabled={loading || saving}
                  sx={{ borderRadius: 1.5 }}
                >
                  <MenuItem value="MM/DD/YYYY">MM/DD/YYYY (e.g., 12/31/2023)</MenuItem>
                  <MenuItem value="DD/MM/YYYY">DD/MM/YYYY (e.g., 31/12/2023)</MenuItem>
                  <MenuItem value="YYYY-MM-DD">YYYY-MM-DD (e.g., 2023-12-31)</MenuItem>
                </Select>
                <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, ml: 1.5 }}>
                  Format used for displaying dates throughout the application
                </Typography>
              </FormControl>
            </Paper>
          </Grid>

          <Grid item xs={12} md={6}>
            <Paper
              elevation={1}
              sx={{
                p: 3,
                borderRadius: 2,
                height: '100%',
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  boxShadow: '0 8px 16px 0 rgba(0,0,0,0.1)',
                  transform: 'translateY(-2px)'
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <HelpIcon color="primary" sx={{ mr: 1.5, fontSize: 28 }} />
                <Typography variant="h6" fontWeight="500">
                  System Status
                </Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />

              <Box sx={{
                p: 2,
                borderRadius: 2,
                bgcolor: settings.maintenance_mode ? 'warning.light' : 'success.light',
                mb: 3
              }}>
                <FormControlLabel
                  control={
                    <Switch
                      name="maintenance_mode"
                      checked={settings.maintenance_mode}
                      onChange={handleChange}
                      color="primary"
                      disabled={loading || saving}
                    />
                  }
                  label={
                    <Typography fontWeight="500">
                      {settings.maintenance_mode ? "Maintenance Mode Enabled" : "System Online"}
                    </Typography>
                  }
                />
                <Typography variant="body2" sx={{ mt: 0.5, color: settings.maintenance_mode ? 'warning.dark' : 'success.dark' }}>
                  {settings.maintenance_mode
                    ? "When enabled, only administrators can access the system."
                    : "The system is currently online and accessible to all users."}
                </Typography>
              </Box>

              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Paper
                    variant="outlined"
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      borderColor: 'divider'
                    }}
                  >
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      System Version
                    </Typography>
                    <Typography variant="h6" fontWeight="500">2.4.1</Typography>
                  </Paper>
                </Grid>
                <Grid item xs={6}>
                  <Paper
                    variant="outlined"
                    sx={{
                      p: 2,
                      borderRadius: 2,
                      borderColor: 'divider'
                    }}
                  >
                    <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                      Last Update
                    </Typography>
                    <Typography variant="h6" fontWeight="500">
                      {settings.updated_at
                        ? new Date(settings.updated_at).toLocaleString()
                        : 'Not available'}
                    </Typography>
                  </Paper>
                </Grid>
              </Grid>
            </Paper>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Security Settings */}
      <TabPanel value={tabValue} index={1}>
        <Paper
          elevation={1}
          sx={{
            p: 3,
            borderRadius: 2,
            transition: 'transform 0.2s, box-shadow 0.2s',
            '&:hover': {
              boxShadow: '0 8px 16px 0 rgba(0,0,0,0.1)',
              transform: 'translateY(-2px)'
            }
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <SecurityIcon color="primary" sx={{ mr: 1.5, fontSize: 28 }} />
            <Typography variant="h6" fontWeight="500">
              Security Settings
            </Typography>
          </Box>
          <Divider sx={{ mb: 3 }} />

          <Paper
            variant="outlined"
            sx={{
              p: 3,
              mb: 4,
              borderRadius: 2,
              borderColor: 'primary.light',
              bgcolor: 'primary.lightest',
              position: 'relative',
              overflow: 'hidden'
            }}
          >
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Box>
                <Typography variant="h6" color="primary.dark" fontWeight="500">
                  Password Policy
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Configure password requirements and security settings
                </Typography>
              </Box>
              <Button
                variant="contained"
                color="primary"
                href="/admin/password-policy"
                disabled={loading || saving}
                sx={{
                  borderRadius: 2,
                  px: 2,
                  py: 1,
                  fontWeight: 500
                }}
                startIcon={<SecurityIcon />}
              >
                Advanced Password Settings
              </Button>
            </Box>

            <Alert
              severity="info"
              sx={{
                borderRadius: 1.5,
                '& .MuiAlert-icon': {
                  fontSize: 20
                }
              }}
            >
              Password policy settings have been moved to a dedicated page for more comprehensive management.
              Click the button above to access the full password policy configuration.
            </Alert>
          </Paper>

          <Typography variant="h6" color="primary.dark" fontWeight="500" sx={{ mb: 2, mt: 4 }}>
            Authentication Settings
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Paper
                variant="outlined"
                sx={{
                  p: 2.5,
                  borderRadius: 2,
                  height: '100%',
                  borderColor: 'divider'
                }}
              >
                <Typography variant="subtitle1" fontWeight="500" gutterBottom>
                  Session Management
                </Typography>
                <TextField
                  name="session_timeout"
                  label="Session Timeout (minutes)"
                  type="number"
                  fullWidth
                  margin="normal"
                  value={settings.session_timeout}
                  onChange={handleChange}
                  disabled={loading || saving}
                  variant="outlined"
                  InputProps={{
                    sx: { borderRadius: 1.5 },
                    endAdornment: (
                      <Tooltip title="The amount of time in minutes before an inactive user is automatically logged out">
                        <IconButton edge="end" size="small">
                          <HelpIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )
                  }}
                />
                <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 1 }}>
                  Users will be automatically logged out after this period of inactivity
                </Typography>
              </Paper>
            </Grid>

            <Grid item xs={12} md={6}>
              <Paper
                variant="outlined"
                sx={{
                  p: 2.5,
                  borderRadius: 2,
                  height: '100%',
                  borderColor: 'divider'
                }}
              >
                <Typography variant="subtitle1" fontWeight="500" gutterBottom>
                  Access Security
                </Typography>

                <Box sx={{ mt: 1, mb: 2 }}>
                  <FormControlLabel
                    control={
                      <Switch
                        name="two_factor_auth"
                        checked={settings.two_factor_auth}
                        onChange={handleChange}
                        color="primary"
                        disabled={loading || saving}
                      />
                    }
                    label={
                      <Typography fontWeight="500">
                        Two-Factor Authentication
                      </Typography>
                    }
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5, ml: 4 }}>
                    Require users to verify their identity using a second factor when logging in
                  </Typography>
                </Box>

                <Box sx={{ mt: 3 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <FormControlLabel
                      control={
                        <Switch
                          name="ip_restriction"
                          checked={settings.ip_restriction}
                          onChange={handleChange}
                          color="primary"
                          disabled={loading || saving}
                        />
                      }
                      label={
                        <Typography fontWeight="500">
                          IP Restriction
                        </Typography>
                      }
                    />
                    <Button
                      variant="outlined"
                      size="small"
                      href="/admin/ip-restrictions"
                      sx={{ ml: 2 }}
                    >
                      Manage IP List
                    </Button>
                  </Box>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5, ml: 4 }}>
                    Restrict access to the system based on IP address
                  </Typography>
                </Box>
              </Paper>
            </Grid>
          </Grid>
        </Paper>
      </TabPanel>

      {/* Notifications Settings */}
      <TabPanel value={tabValue} index={2}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Paper
              elevation={1}
              sx={{
                p: 3,
                borderRadius: 2,
                height: '100%',
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  boxShadow: '0 8px 16px 0 rgba(0,0,0,0.1)',
                  transform: 'translateY(-2px)'
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <NotificationsIcon color="primary" sx={{ mr: 1.5, fontSize: 28 }} />
                <Typography variant="h6" fontWeight="500">
                  Notification Channels
                </Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />

              <Paper
                variant="outlined"
                sx={{
                  p: 2.5,
                  mb: 3,
                  borderRadius: 2,
                  borderColor: settings.email_notifications ? 'primary.light' : 'divider',
                  bgcolor: settings.email_notifications ? 'primary.lightest' : 'background.paper',
                  transition: 'all 0.2s'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <EmailIcon
                      sx={{
                        mr: 1.5,
                        color: settings.email_notifications ? 'primary.main' : 'text.secondary',
                        fontSize: 24
                      }}
                    />
                    <Box>
                      <Typography variant="subtitle1" fontWeight="500">
                        Email Notifications
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Send email notifications for important system events
                      </Typography>
                    </Box>
                  </Box>
                  <Switch
                    name="email_notifications"
                    checked={settings.email_notifications}
                    onChange={handleChange}
                    color="primary"
                    disabled={loading || saving || !settings.email_enabled}
                  />
                </Box>
                {!settings.email_enabled && (
                  <Alert
                    severity="warning"
                    sx={{
                      mt: 2,
                      borderRadius: 1.5,
                      '& .MuiAlert-icon': {
                        fontSize: 20
                      }
                    }}
                  >
                    Email service is disabled. Enable it in the Email tab first.
                  </Alert>
                )}
              </Paper>

              <Paper
                variant="outlined"
                sx={{
                  p: 2.5,
                  borderRadius: 2,
                  borderColor: settings.sms_notifications ? 'primary.light' : 'divider',
                  bgcolor: settings.sms_notifications ? 'primary.lightest' : 'background.paper',
                  transition: 'all 0.2s'
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box
                      component="span"
                      sx={{
                        mr: 1.5,
                        color: settings.sms_notifications ? 'primary.main' : 'text.secondary',
                        fontSize: 24,
                        display: 'flex',
                        alignItems: 'center'
                      }}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M17 2H7c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 18H7V4h10v16z"/>
                        <path d="M12 18c.55 0 1-.45 1-1s-.45-1-1-1-1 .45-1 1 .45 1 1 1zm2-14H10v1h4V4z"/>
                      </svg>
                    </Box>
                    <Box>
                      <Typography variant="subtitle1" fontWeight="500">
                        SMS Notifications
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Send SMS notifications for critical alerts
                      </Typography>
                    </Box>
                  </Box>
                  <Switch
                    name="sms_notifications"
                    checked={settings.sms_notifications}
                    onChange={handleChange}
                    color="primary"
                    disabled={loading || saving}
                  />
                </Box>
              </Paper>
            </Paper>
          </Grid>

          <Grid item xs={12} md={6}>
            <Paper
              elevation={1}
              sx={{
                p: 3,
                borderRadius: 2,
                height: '100%',
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  boxShadow: '0 8px 16px 0 rgba(0,0,0,0.1)',
                  transform: 'translateY(-2px)'
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <HelpIcon color="primary" sx={{ mr: 1.5, fontSize: 28 }} />
                <Typography variant="h6" fontWeight="500">
                  Notification Information
                </Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />

              <Alert
                severity="info"
                sx={{
                  mb: 3,
                  borderRadius: 1.5,
                  '& .MuiAlert-icon': {
                    fontSize: 20
                  }
                }}
              >
                <Typography variant="subtitle2" fontWeight="500" gutterBottom>
                  Email Configuration Required
                </Typography>
                <Typography variant="body2">
                  Email notifications require a properly configured email server. Please configure your email settings in the Email tab.
                </Typography>
              </Alert>

              <Typography variant="subtitle1" fontWeight="500" gutterBottom>
                Notification Types
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                  <Box component="span" sx={{ color: 'success.main', mr: 1, display: 'flex' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                  </Box>
                  <strong>System Alerts:</strong> Notifications about system status and maintenance
                </Typography>
                <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                  <Box component="span" sx={{ color: 'success.main', mr: 1, display: 'flex' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                  </Box>
                  <strong>Security Alerts:</strong> Notifications about security events and login attempts
                </Typography>
                <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                  <Box component="span" sx={{ color: 'success.main', mr: 1, display: 'flex' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                  </Box>
                  <strong>User Notifications:</strong> Notifications about account changes and updates
                </Typography>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Backup Settings */}
      <TabPanel value={tabValue} index={3}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={7}>
            <Paper
              elevation={1}
              sx={{
                p: 3,
                borderRadius: 2,
                height: '100%',
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  boxShadow: '0 8px 16px 0 rgba(0,0,0,0.1)',
                  transform: 'translateY(-2px)'
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <StorageIcon color="primary" sx={{ mr: 1.5, fontSize: 28 }} />
                <Typography variant="h6" fontWeight="500">
                  Backup Configuration
                </Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />

              <Box sx={{
                p: 2.5,
                borderRadius: 2,
                bgcolor: settings.auto_backup ? 'success.lightest' : 'background.paper',
                border: 1,
                borderColor: settings.auto_backup ? 'success.light' : 'divider',
                mb: 3,
                transition: 'all 0.2s'
              }}>
                <FormControlLabel
                  control={
                    <Switch
                      name="auto_backup"
                      checked={settings.auto_backup}
                      onChange={handleChange}
                      color="success"
                      disabled={loading || saving}
                    />
                  }
                  label={
                    <Typography fontWeight="500">
                      Automatic Backups
                    </Typography>
                  }
                />
                <Typography variant="body2" sx={{ mt: 0.5, ml: 4, color: settings.auto_backup ? 'success.dark' : 'text.secondary' }}>
                  Automatically backup the database according to the schedule
                </Typography>
              </Box>

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <FormControl
                    fullWidth
                    variant="outlined"
                    sx={{ mb: 1 }}
                  >
                    <InputLabel>Backup Frequency</InputLabel>
                    <Select
                      name="backup_frequency"
                      value={settings.backup_frequency}
                      label="Backup Frequency"
                      onChange={(e) => handleSelectChange('backup_frequency', e.target.value)}
                      disabled={!settings.auto_backup || loading || saving}
                      sx={{ borderRadius: 1.5 }}
                    >
                      <MenuItem value="hourly">Hourly</MenuItem>
                      <MenuItem value="daily">Daily</MenuItem>
                      <MenuItem value="weekly">Weekly</MenuItem>
                      <MenuItem value="monthly">Monthly</MenuItem>
                    </Select>
                    <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, ml: 1.5 }}>
                      How often backups should be created
                    </Typography>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    name="backup_retention"
                    label="Retention Period (days)"
                    type="number"
                    fullWidth
                    variant="outlined"
                    value={settings.backup_retention}
                    onChange={handleChange}
                    disabled={!settings.auto_backup || loading || saving}
                    InputProps={{
                      sx: { borderRadius: 1.5 },
                      endAdornment: (
                        <Tooltip title="Number of days to keep backups before they are automatically deleted">
                          <IconButton edge="end" size="small">
                            <HelpIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )
                    }}
                  />
                  <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5, ml: 1.5 }}>
                    How long to keep backups before deletion
                  </Typography>
                </Grid>
              </Grid>

              <Box sx={{ mt: 4, display: 'flex', gap: 2 }}>
                <Button
                  variant="contained"
                  color="primary"
                  disabled={loading || saving}
                  startIcon={<StorageIcon />}
                  sx={{
                    borderRadius: 2,
                    px: 3,
                    py: 1,
                    fontWeight: 500
                  }}
                  onClick={() => alert('This feature requires server-side implementation')}
                >
                  Backup Now
                </Button>
                <Button
                  variant="outlined"
                  disabled={loading || saving}
                  sx={{
                    borderRadius: 2,
                    px: 3,
                    py: 1,
                    fontWeight: 500
                  }}
                  onClick={() => alert('This feature requires server-side implementation')}
                >
                  Restore from Backup
                </Button>
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12} md={5}>
            <Paper
              elevation={1}
              sx={{
                p: 3,
                borderRadius: 2,
                height: '100%',
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  boxShadow: '0 8px 16px 0 rgba(0,0,0,0.1)',
                  transform: 'translateY(-2px)'
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <HelpIcon color="primary" sx={{ mr: 1.5, fontSize: 28 }} />
                <Typography variant="h6" fontWeight="500">
                  Backup Status
                </Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />

              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Last Backup
                </Typography>
                <Typography variant="h6" fontWeight="500">
                  Today, 03:15 AM
                </Typography>
                <Typography variant="body2" color="success.main" sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                  <Box component="span" sx={{ color: 'success.main', mr: 1, display: 'flex' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                  </Box>
                  Completed successfully
                </Typography>
              </Box>

              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Next Scheduled Backup
                </Typography>
                <Typography variant="h6" fontWeight="500">
                  {settings.auto_backup ? 'Tomorrow, 03:00 AM' : 'Not scheduled'}
                </Typography>
                {settings.auto_backup && (
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                    Based on {settings.backup_frequency} frequency
                  </Typography>
                )}
              </Box>

              <Box>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Storage Usage
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Box sx={{ flexGrow: 1, mr: 2 }}>
                    <Box
                      sx={{
                        height: 8,
                        borderRadius: 4,
                        bgcolor: 'grey.200',
                        overflow: 'hidden'
                      }}
                    >
                      <Box
                        sx={{
                          width: '35%',
                          height: '100%',
                          bgcolor: 'primary.main',
                          borderRadius: 4
                        }}
                      />
                    </Box>
                  </Box>
                  <Typography variant="body2" fontWeight="500">35%</Typography>
                </Box>
                <Typography variant="body2" color="text.secondary">
                  3.5 GB used of 10 GB allocated
                </Typography>
              </Box>
            </Paper>
          </Grid>
          <Grid item xs={12}>
            <Paper
              elevation={1}
              sx={{
                p: 3,
                borderRadius: 2,
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  boxShadow: '0 8px 16px 0 rgba(0,0,0,0.1)',
                  transform: 'translateY(-2px)'
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TuneIcon color="primary" sx={{ mr: 1.5, fontSize: 28 }} />
                <Typography variant="h6" fontWeight="500">
                  Database Optimization
                </Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />

              <Typography variant="body1" paragraph>
                Optimize database performance by reclaiming space, updating statistics, and rebuilding indexes.
                Regular optimization helps maintain system performance as your database grows.
              </Typography>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                <Box>
                  <Typography variant="subtitle1" fontWeight="500" gutterBottom>
                    Database Health
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box
                      sx={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        bgcolor: 'success.main',
                        mr: 1
                      }}
                    />
                    <Typography variant="body2">Good condition</Typography>
                  </Box>
                </Box>

                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<TuneIcon />}
                  onClick={() => alert('This feature requires server-side implementation')}
                  sx={{ borderRadius: 2 }}
                >
                  Optimize Database
                </Button>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Last Optimization
                </Typography>
                <Typography variant="body1">
                  3 days ago (May 12, 2023)
                </Typography>
              </Box>

              <Box>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Recommended Actions
                </Typography>
                <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Box component="span" sx={{ color: 'primary.main', mr: 1, display: 'flex' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                  </Box>
                  Run VACUUM on patient_visits table (25% fragmentation)
                </Typography>
                <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box component="span" sx={{ color: 'primary.main', mr: 1, display: 'flex' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                  </Box>
                  Update statistics for medical_records table
                </Typography>
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12}>
            <Paper
              elevation={1}
              sx={{
                p: 3,
                borderRadius: 2,
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  boxShadow: '0 8px 16px 0 rgba(0,0,0,0.1)',
                  transform: 'translateY(-2px)'
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <ScheduleIcon color="primary" sx={{ mr: 1.5, fontSize: 28 }} />
                <Typography variant="h6" fontWeight="500">
                  Maintenance Scheduler
                </Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />

              <Typography variant="body1" paragraph>
                Schedule maintenance windows to perform system updates and maintenance tasks with minimal disruption to users.
              </Typography>

              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Next Scheduled Maintenance
                </Typography>
                <Typography variant="body1" fontWeight="500">
                  Sunday, May 21, 2023 (2:00 AM - 4:00 AM)
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Weekly database maintenance
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.maintenance_mode}
                      onChange={handleChange}
                      name="maintenance_mode"
                      color="primary"
                      disabled={loading || saving}
                    />
                  }
                  label="Enable Maintenance Mode"
                />

                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<ScheduleIcon />}
                  onClick={() => alert('This feature requires server-side implementation')}
                  sx={{ borderRadius: 2 }}
                >
                  Schedule Maintenance
                </Button>
              </Box>

              {settings.maintenance_mode && (
                <TextField
                  fullWidth
                  margin="normal"
                  label="Maintenance Message"
                  multiline
                  rows={2}
                  name="maintenance_message"
                  value={settings.maintenance_message || ''}
                  onChange={handleChange}
                  disabled={loading || saving}
                  placeholder="The system is currently undergoing scheduled maintenance. Please check back later."
                />
              )}
            </Paper>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Email Settings */}
      <TabPanel value={tabValue} index={4}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Paper
              elevation={1}
              sx={{
                p: 3,
                borderRadius: 2,
                height: '100%',
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  boxShadow: '0 8px 16px 0 rgba(0,0,0,0.1)',
                  transform: 'translateY(-2px)'
                }
              }}
            >
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <EmailIcon color="primary" sx={{ mr: 1.5, fontSize: 28 }} />
                  <Typography variant="h6" fontWeight="500">
                    Email Configuration
                  </Typography>
                </Box>
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  bgcolor: settings.email_enabled ? 'success.lightest' : 'grey.100',
                  px: 2,
                  py: 0.5,
                  borderRadius: 10,
                  border: 1,
                  borderColor: settings.email_enabled ? 'success.light' : 'grey.300',
                  transition: 'all 0.2s'
                }}>
                  <Typography
                    variant="body2"
                    fontWeight="500"
                    sx={{
                      mr: 1,
                      color: settings.email_enabled ? 'success.dark' : 'text.secondary'
                    }}
                  >
                    {settings.email_enabled ? 'Enabled' : 'Disabled'}
                  </Typography>
                  <Switch
                    name="email_enabled"
                    checked={settings.email_enabled}
                    onChange={handleChange}
                    color="success"
                    disabled={loading || saving}
                    size="small"
                  />
                </Box>
              </Box>
              <Divider sx={{ mb: 3 }} />

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TextField
                    name="smtp_server"
                    label="SMTP Server"
                    fullWidth
                    variant="outlined"
                    value={settings.smtp_server || ''}
                    onChange={handleChange}
                    disabled={!settings.email_enabled || loading || saving}
                    placeholder="e.g., smtp.gmail.com"
                    InputProps={{
                      sx: { borderRadius: 1.5 }
                    }}
                    helperText="The address of your email server"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    name="smtp_port"
                    label="SMTP Port"
                    type="number"
                    fullWidth
                    variant="outlined"
                    value={settings.smtp_port || ''}
                    onChange={handleChange}
                    disabled={!settings.email_enabled || loading || saving}
                    placeholder="e.g., 587"
                    InputProps={{
                      sx: { borderRadius: 1.5 }
                    }}
                    helperText="Common ports: 25, 465, 587, 2525"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    name="email_from"
                    label="Email From Address"
                    fullWidth
                    variant="outlined"
                    value={settings.email_from || ''}
                    onChange={handleChange}
                    disabled={!settings.email_enabled || loading || saving}
                    placeholder="e.g., <EMAIL>"
                    InputProps={{
                      sx: { borderRadius: 1.5 }
                    }}
                    helperText="The email address that will appear in the 'From' field"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    name="smtp_username"
                    label="SMTP Username"
                    fullWidth
                    variant="outlined"
                    value={settings.smtp_username || ''}
                    onChange={handleChange}
                    disabled={!settings.email_enabled || loading || saving}
                    placeholder="e.g., <EMAIL>"
                    InputProps={{
                      sx: { borderRadius: 1.5 }
                    }}
                    helperText="Username for authenticating with the SMTP server"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    name="smtp_password"
                    label="SMTP Password"
                    type="password"
                    fullWidth
                    variant="outlined"
                    value={settings.smtp_password || ''}
                    onChange={handleChange}
                    disabled={!settings.email_enabled || loading || saving}
                    placeholder="Enter password"
                    InputProps={{
                      sx: { borderRadius: 1.5 }
                    }}
                    helperText="Password for authenticating with the SMTP server"
                  />
                </Grid>
              </Grid>

              <Box sx={{ mt: 4, display: 'flex', gap: 2 }}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleTestEmail}
                  disabled={!settings.email_enabled || loading || saving || testingEmail || !settings.smtp_server}
                  startIcon={<EmailIcon />}
                  sx={{
                    borderRadius: 2,
                    px: 3,
                    py: 1.2,
                    fontWeight: 500
                  }}
                >
                  {testingEmail ? (
                    <>
                      <CircularProgress size={20} color="inherit" sx={{ mr: 1 }} />
                      Testing...
                    </>
                  ) : 'Test Email Configuration'}
                </Button>
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12} md={4}>
            <Paper
              elevation={1}
              sx={{
                p: 3,
                borderRadius: 2,
                height: '100%',
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  boxShadow: '0 8px 16px 0 rgba(0,0,0,0.1)',
                  transform: 'translateY(-2px)'
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <HelpIcon color="primary" sx={{ mr: 1.5, fontSize: 28 }} />
                <Typography variant="h6" fontWeight="500">
                  Email Information
                </Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />

              <Alert
                severity="info"
                sx={{
                  mb: 3,
                  borderRadius: 1.5,
                  '& .MuiAlert-icon': {
                    fontSize: 20
                  }
                }}
              >
                <Typography variant="subtitle2" fontWeight="500" gutterBottom>
                  Why Configure Email?
                </Typography>
                <Typography variant="body2">
                  Email configuration is required for sending notifications, password reset emails, and other system communications.
                </Typography>
              </Alert>

              <Typography variant="subtitle1" fontWeight="500" gutterBottom>
                Common SMTP Providers
              </Typography>
              <Box sx={{ mt: 1 }}>
                <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                  <Box component="span" sx={{ color: 'primary.main', mr: 1, display: 'flex' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                  </Box>
                  <strong>Gmail:</strong> smtp.gmail.com, Port: 587
                </Typography>
                <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                  <Box component="span" sx={{ color: 'primary.main', mr: 1, display: 'flex' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                  </Box>
                  <strong>Outlook:</strong> smtp.office365.com, Port: 587
                </Typography>
                <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                  <Box component="span" sx={{ color: 'primary.main', mr: 1, display: 'flex' }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                  </Box>
                  <strong>Yahoo:</strong> smtp.mail.yahoo.com, Port: 587
                </Typography>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </TabPanel>

      {/* Localization Settings */}
      <TabPanel value={tabValue} index={5}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={7}>
            <Paper
              elevation={1}
              sx={{
                p: 3,
                borderRadius: 2,
                height: '100%',
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  boxShadow: '0 8px 16px 0 rgba(0,0,0,0.1)',
                  transform: 'translateY(-2px)'
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <LanguageIcon color="primary" sx={{ mr: 1.5, fontSize: 28 }} />
                <Typography variant="h6" fontWeight="500">
                  Localization Settings
                </Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="subtitle1" fontWeight="500" gutterBottom>
                      Language
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Select the default language for the application interface
                    </Typography>

                    <FormControl
                      fullWidth
                      variant="outlined"
                    >
                      <InputLabel>Default Language</InputLabel>
                      <Select
                        name="language"
                        value={settings.language}
                        label="Default Language"
                        onChange={(e) => handleSelectChange('language', e.target.value)}
                        disabled={loading || saving}
                        sx={{ borderRadius: 1.5 }}
                      >
                        <MenuItem value="en">
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Box component="span" sx={{ width: 24, height: 16, mr: 1.5, borderRadius: 1, overflow: 'hidden', display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: '#f5f5f5' }}>
                              🇺🇸
                            </Box>
                            English
                          </Box>
                        </MenuItem>
                        <MenuItem value="es">
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Box component="span" sx={{ width: 24, height: 16, mr: 1.5, borderRadius: 1, overflow: 'hidden', display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: '#f5f5f5' }}>
                              🇪🇸
                            </Box>
                            Spanish (Español)
                          </Box>
                        </MenuItem>
                        <MenuItem value="fr">
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Box component="span" sx={{ width: 24, height: 16, mr: 1.5, borderRadius: 1, overflow: 'hidden', display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: '#f5f5f5' }}>
                              🇫🇷
                            </Box>
                            French (Français)
                          </Box>
                        </MenuItem>
                        <MenuItem value="de">
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Box component="span" sx={{ width: 24, height: 16, mr: 1.5, borderRadius: 1, overflow: 'hidden', display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: '#f5f5f5' }}>
                              🇩🇪
                            </Box>
                            German (Deutsch)
                          </Box>
                        </MenuItem>
                        <MenuItem value="zh">
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Box component="span" sx={{ width: 24, height: 16, mr: 1.5, borderRadius: 1, overflow: 'hidden', display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: '#f5f5f5' }}>
                              🇨🇳
                            </Box>
                            Chinese (中文)
                          </Box>
                        </MenuItem>
                        <MenuItem value="ne">
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Box component="span" sx={{ width: 24, height: 16, mr: 1.5, borderRadius: 1, overflow: 'hidden', display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: '#f5f5f5' }}>
                              🇳🇵
                            </Box>
                            Nepali (नेपाली)
                          </Box>
                        </MenuItem>
                      </Select>
                    </FormControl>
                  </Box>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Box>
                    <Typography variant="subtitle1" fontWeight="500" gutterBottom>
                      Currency
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Select the default currency for financial values
                    </Typography>

                    <FormControl
                      fullWidth
                      variant="outlined"
                    >
                      <InputLabel>Currency</InputLabel>
                      <Select
                        name="currency"
                        value={settings.currency}
                        label="Currency"
                        onChange={(e) => handleSelectChange('currency', e.target.value)}
                        disabled={loading || saving}
                        sx={{ borderRadius: 1.5 }}
                      >
                        <MenuItem value="USD">
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                            <span>US Dollar</span>
                            <Typography variant="body2" sx={{ color: 'primary.main', fontWeight: 'bold' }}>$</Typography>
                          </Box>
                        </MenuItem>
                        <MenuItem value="EUR">
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                            <span>Euro</span>
                            <Typography variant="body2" sx={{ color: 'primary.main', fontWeight: 'bold' }}>€</Typography>
                          </Box>
                        </MenuItem>
                        <MenuItem value="GBP">
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                            <span>British Pound</span>
                            <Typography variant="body2" sx={{ color: 'primary.main', fontWeight: 'bold' }}>£</Typography>
                          </Box>
                        </MenuItem>
                        <MenuItem value="JPY">
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                            <span>Japanese Yen</span>
                            <Typography variant="body2" sx={{ color: 'primary.main', fontWeight: 'bold' }}>¥</Typography>
                          </Box>
                        </MenuItem>
                        <MenuItem value="CAD">
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                            <span>Canadian Dollar</span>
                            <Typography variant="body2" sx={{ color: 'primary.main', fontWeight: 'bold' }}>C$</Typography>
                          </Box>
                        </MenuItem>
                        <MenuItem value="NPR">
                          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                            <span>Nepalese Rupee</span>
                            <Typography variant="body2" sx={{ color: 'primary.main', fontWeight: 'bold' }}>रू</Typography>
                          </Box>
                        </MenuItem>
                      </Select>
                    </FormControl>
                  </Box>
                </Grid>
              </Grid>

              <Alert
                severity="info"
                sx={{
                  mt: 4,
                  borderRadius: 1.5,
                  '& .MuiAlert-icon': {
                    fontSize: 20
                  }
                }}
              >
                <Typography variant="subtitle2" fontWeight="500" gutterBottom>
                  Localization Impact
                </Typography>
                <Typography variant="body2">
                  These settings affect how dates, numbers, and currencies are displayed throughout the application.
                </Typography>
              </Alert>
            </Paper>
          </Grid>

          <Grid item xs={12} md={5}>
            <Paper
              elevation={1}
              sx={{
                p: 3,
                borderRadius: 2,
                height: '100%',
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  boxShadow: '0 8px 16px 0 rgba(0,0,0,0.1)',
                  transform: 'translateY(-2px)'
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <HelpIcon color="primary" sx={{ mr: 1.5, fontSize: 28 }} />
                <Typography variant="h6" fontWeight="500">
                  Preview
                </Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />

              <Box sx={{ mb: 4 }}>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Date Format
                </Typography>
                <Paper
                  variant="outlined"
                  sx={{
                    p: 2,
                    borderRadius: 2,
                    borderColor: 'primary.light',
                    bgcolor: 'primary.lightest'
                  }}
                >
                  <Typography variant="h6" fontWeight="500">
                    {settings.date_format === 'MM/DD/YYYY' && '05/15/2023'}
                    {settings.date_format === 'DD/MM/YYYY' && '15/05/2023'}
                    {settings.date_format === 'YYYY-MM-DD' && '2023-05-15'}
                  </Typography>
                </Paper>
              </Box>

              <Box sx={{ mb: 4 }}>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Currency Format
                </Typography>
                <Paper
                  variant="outlined"
                  sx={{
                    p: 2,
                    borderRadius: 2,
                    borderColor: 'primary.light',
                    bgcolor: 'primary.lightest'
                  }}
                >
                  <Typography variant="h6" fontWeight="500">
                    {settings.currency === 'USD' && '$1,234.56'}
                    {settings.currency === 'EUR' && '€1.234,56'}
                    {settings.currency === 'GBP' && '£1,234.56'}
                    {settings.currency === 'JPY' && '¥1,235'}
                    {settings.currency === 'CAD' && 'C$1,234.56'}
                    {settings.currency === 'NPR' && 'रू 1,234.56'}
                  </Typography>
                </Paper>
              </Box>

              <Box>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Language Sample
                </Typography>
                <Paper
                  variant="outlined"
                  sx={{
                    p: 2,
                    borderRadius: 2,
                    borderColor: 'primary.light',
                    bgcolor: 'primary.lightest'
                  }}
                >
                  <Typography variant="body1" fontWeight="500">
                    {settings.language === 'en' && 'Welcome to the Medical Records System'}
                    {settings.language === 'es' && 'Bienvenido al Sistema de Registros Médicos'}
                    {settings.language === 'fr' && 'Bienvenue dans le Système de Dossiers Médicaux'}
                    {settings.language === 'de' && 'Willkommen im Medizinischen Aufzeichnungssystem'}
                    {settings.language === 'zh' && '欢迎使用医疗记录系统'}
                    {settings.language === 'ne' && 'मेडिकल रेकर्ड्स सिस्टममा स्वागत छ'}
                  </Typography>
                </Paper>
              </Box>
            </Paper>
          </Grid>

          <Grid item xs={12}>
            <Paper
              elevation={1}
              sx={{
                p: 3,
                borderRadius: 2,
                transition: 'transform 0.2s, box-shadow 0.2s',
                '&:hover': {
                  boxShadow: '0 8px 16px 0 rgba(0,0,0,0.1)',
                  transform: 'translateY(-2px)'
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TranslateIcon color="primary" sx={{ mr: 1.5, fontSize: 28 }} />
                <Typography variant="h6" fontWeight="500">
                  Translation Management
                </Typography>
              </Box>
              <Divider sx={{ mb: 3 }} />

              <Typography variant="body1" paragraph>
                Manage translations for all text in the application. Add, edit, or delete translations for different languages.
              </Typography>

              {/* Import TranslationManager component */}
              {/* This is a placeholder - the actual API endpoints need to be implemented */}
              <Alert severity="info" sx={{ mb: 3 }}>
                The translation management feature is ready for use in the UI, but requires server-side implementation to be fully functional.
              </Alert>

              <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                <Button
                  variant="contained"
                  color="primary"
                  startIcon={<TranslateIcon />}
                  onClick={() => alert('This feature requires server-side implementation')}
                  sx={{ borderRadius: 2, px: 3 }}
                >
                  Manage Translations
                </Button>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </TabPanel>

      {loading || saving ? null : (
        <Box
          sx={{
            mt: 4,
            display: 'flex',
            justifyContent: 'flex-end',
            position: 'sticky',
            bottom: 16,
            right: 16,
            zIndex: 10
          }}
        >
          <Paper
            elevation={3}
            sx={{
              p: 2,
              borderRadius: 3,
              display: 'flex',
              gap: 2
            }}
          >
            <Button
              variant="outlined"
              onClick={() => fetchSettings()}
              disabled={loading || saving}
              startIcon={<RefreshIcon />}
              sx={{
                borderRadius: 2,
                px: 3,
                py: 1,
                fontWeight: 500
              }}
            >
              Reset Changes
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={handleSaveSettings}
              disabled={loading || saving}
              startIcon={<SaveIcon />}
              sx={{
                borderRadius: 2,
                px: 3,
                py: 1,
                fontWeight: 500
              }}
            >
              Save All Settings
            </Button>
          </Paper>
        </Box>
      )}

      <Snackbar
        open={notification !== null}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseNotification} severity={notification?.type || 'info'} sx={{ width: '100%' }}>
          {notification?.message || ''}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default SystemSettings;