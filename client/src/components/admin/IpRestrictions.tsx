import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  Switch,
  FormControlLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Alert,
  CircularProgress,
  Tooltip,
  Chip,
  Grid
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Refresh as RefreshIcon,
  Security as SecurityIcon,
  Check as CheckIcon,
  Block as BlockIcon
} from '@mui/icons-material';
import axios from 'axios';
import { API_URL } from '../../config';

interface IpRestriction {
  restriction_id: number;
  ip_address: string;
  description: string;
  is_allowed: boolean;
  created_at: string;
  updated_at: string;
}

const IpRestrictions: React.FC = () => {
  const [restrictions, setRestrictions] = useState<IpRestriction[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [currentRestriction, setCurrentRestriction] = useState<IpRestriction | null>(null);
  const [formData, setFormData] = useState({
    ip_address: '',
    description: '',
    is_allowed: true
  });
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [restrictionToDelete, setRestrictionToDelete] = useState<IpRestriction | null>(null);
  const [testIp, setTestIp] = useState('');
  const [testResult, setTestResult] = useState<{ ip_address: string; is_allowed: boolean } | null>(null);
  const [testLoading, setTestLoading] = useState(false);

  useEffect(() => {
    fetchRestrictions();
  }, []);

  const fetchRestrictions = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found. Please log in again.');
      }

      const response = await axios.get(`${API_URL}/api/admin/ip-restrictions`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      setRestrictions(response.data);
    } catch (err) {
      console.error('Error fetching IP restrictions:', err);
      setError(err instanceof Error ? err.message : 'Failed to load IP restrictions');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (restriction?: IpRestriction) => {
    if (restriction) {
      setEditMode(true);
      setCurrentRestriction(restriction);
      setFormData({
        ip_address: restriction.ip_address,
        description: restriction.description || '',
        is_allowed: restriction.is_allowed
      });
    } else {
      setEditMode(false);
      setCurrentRestriction(null);
      setFormData({
        ip_address: '',
        description: '',
        is_allowed: true
      });
    }
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked, type } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found. Please log in again.');
      }

      if (editMode && currentRestriction) {
        // Update existing restriction
        await axios.put(
          `${API_URL}/api/admin/ip-restrictions/${currentRestriction.restriction_id}`,
          formData,
          {
            headers: {
              'Content-Type': 'application/json',
              'x-auth-token': token
            }
          }
        );
      } else {
        // Create new restriction
        await axios.post(
          `${API_URL}/api/admin/ip-restrictions`,
          formData,
          {
            headers: {
              'Content-Type': 'application/json',
              'x-auth-token': token
            }
          }
        );
      }

      // Refresh the list
      fetchRestrictions();
      handleCloseDialog();
    } catch (err) {
      console.error('Error saving IP restriction:', err);
      setError(err instanceof Error ? err.message : 'Failed to save IP restriction');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClick = (restriction: IpRestriction) => {
    setRestrictionToDelete(restriction);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!restrictionToDelete) return;

    try {
      setLoading(true);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found. Please log in again.');
      }

      await axios.delete(
        `${API_URL}/api/admin/ip-restrictions/${restrictionToDelete.restriction_id}`,
        {
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          }
        }
      );

      // Refresh the list
      fetchRestrictions();
      setDeleteDialogOpen(false);
    } catch (err) {
      console.error('Error deleting IP restriction:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete IP restriction');
    } finally {
      setLoading(false);
    }
  };

  const handleTestIp = async () => {
    if (!testIp) return;

    try {
      setTestLoading(true);
      setTestResult(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found. Please log in again.');
      }

      const response = await axios.get(
        `${API_URL}/api/admin/ip-restrictions/check/${testIp}`,
        {
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          }
        }
      );

      setTestResult(response.data);
    } catch (err) {
      console.error('Error testing IP:', err);
      setError(err instanceof Error ? err.message : 'Failed to test IP');
    } finally {
      setTestLoading(false);
    }
  };

  return (
    <Box sx={{ p: { xs: 2, md: 4 }, maxWidth: 1200, mx: 'auto' }}>
      <Paper
        elevation={2}
        sx={{
          p: { xs: 2, md: 3 },
          mb: 4,
          borderRadius: 2,
          background: 'linear-gradient(to right, #1976d2, #2196f3)',
          color: 'white'
        }}
      >
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, justifyContent: 'space-between', alignItems: { xs: 'flex-start', md: 'center' } }}>
          <Box>
            <Typography variant="h4" fontWeight="500" sx={{ display: 'flex', alignItems: 'center' }}>
              <SecurityIcon sx={{ mr: 1.5, fontSize: 32 }} />
              IP Restrictions
            </Typography>
            <Typography variant="subtitle1" sx={{ opacity: 0.9, mt: 0.5 }}>
              Manage IP addresses that can access the system
            </Typography>
          </Box>
          <Box sx={{ mt: { xs: 2, md: 0 }, display: 'flex' }}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={fetchRestrictions}
              disabled={loading}
              sx={{
                mr: 1.5,
                color: 'white',
                borderColor: 'rgba(255, 255, 255, 0.5)',
                '&:hover': {
                  borderColor: 'white',
                  backgroundColor: 'rgba(255, 255, 255, 0.1)'
                }
              }}
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => handleOpenDialog()}
              disabled={loading}
              sx={{
                bgcolor: 'white',
                color: '#1976d2',
                '&:hover': {
                  bgcolor: 'rgba(255, 255, 255, 0.9)'
                }
              }}
            >
              Add IP
            </Button>
          </Box>
        </Box>
      </Paper>

      {error && (
        <Alert
          severity="error"
          sx={{
            mb: 3,
            borderRadius: 2,
            '& .MuiAlert-icon': {
              fontSize: 24
            }
          }}
        >
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper
            elevation={1}
            sx={{
              borderRadius: 2,
              overflow: 'hidden',
              height: '100%'
            }}
          >
            {loading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 300 }}>
                <CircularProgress />
              </Box>
            ) : (
              <TableContainer>
                <Table>
                  <TableHead sx={{ bgcolor: 'grey.100' }}>
                    <TableRow>
                      <TableCell sx={{ fontWeight: 'bold' }}>IP Address</TableCell>
                      <TableCell sx={{ fontWeight: 'bold' }}>Description</TableCell>
                      <TableCell sx={{ fontWeight: 'bold' }}>Status</TableCell>
                      <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {restrictions.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={4} align="center">
                          <Typography variant="body1" sx={{ py: 3 }}>
                            No IP restrictions found. Add one to get started.
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ) : (
                      restrictions.map((restriction) => (
                        <TableRow key={restriction.restriction_id}>
                          <TableCell>
                            <Typography variant="body1" fontWeight="500">
                              {restriction.ip_address}
                            </Typography>
                          </TableCell>
                          <TableCell>{restriction.description || '-'}</TableCell>
                          <TableCell>
                            <Chip
                              icon={restriction.is_allowed ? <CheckIcon /> : <BlockIcon />}
                              label={restriction.is_allowed ? 'Allowed' : 'Blocked'}
                              color={restriction.is_allowed ? 'success' : 'error'}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <Tooltip title="Edit">
                              <IconButton
                                onClick={() => handleOpenDialog(restriction)}
                                disabled={restriction.ip_address === '127.0.0.1'}
                              >
                                <EditIcon />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Delete">
                              <span>
                                <IconButton
                                  onClick={() => handleDeleteClick(restriction)}
                                  disabled={restriction.ip_address === '127.0.0.1'}
                                  color="error"
                                >
                                  <DeleteIcon />
                                </IconButton>
                              </span>
                            </Tooltip>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper
            elevation={1}
            sx={{
              p: 3,
              borderRadius: 2,
              height: '100%'
            }}
          >
            <Typography variant="h6" fontWeight="500" gutterBottom>
              Test IP Address
            </Typography>
            <Typography variant="body2" color="text.secondary" paragraph>
              Check if an IP address would be allowed to access the system.
            </Typography>

            <Box sx={{ display: 'flex', mb: 2 }}>
              <TextField
                label="IP Address"
                value={testIp}
                onChange={(e) => setTestIp(e.target.value)}
                fullWidth
                variant="outlined"
                placeholder="e.g., ***********"
                sx={{ mr: 1 }}
              />
              <Button
                variant="contained"
                onClick={handleTestIp}
                disabled={!testIp || testLoading}
              >
                {testLoading ? <CircularProgress size={24} /> : 'Test'}
              </Button>
            </Box>

            {testResult && (
              <Alert
                severity={testResult.is_allowed ? 'success' : 'error'}
                sx={{ mt: 2 }}
              >
                <Typography variant="subtitle2">
                  {testResult.ip_address} is {testResult.is_allowed ? 'allowed' : 'blocked'}
                </Typography>
              </Alert>
            )}

            <Box sx={{ mt: 4 }}>
              <Typography variant="h6" fontWeight="500" gutterBottom>
                How IP Restriction Works
              </Typography>
              <Typography variant="body2" paragraph>
                When IP restriction is enabled in System Settings, only IP addresses in the allowed list can access the system.
              </Typography>
              <Typography variant="body2">
                <strong>Note:</strong> Localhost (127.0.0.1) is always allowed and cannot be removed to prevent lockouts.
              </Typography>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Add/Edit Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editMode ? 'Edit IP Restriction' : 'Add IP Restriction'}
        </DialogTitle>
        <DialogContent>
          <TextField
            name="ip_address"
            label="IP Address"
            value={formData.ip_address}
            onChange={handleInputChange}
            fullWidth
            margin="normal"
            required
            placeholder="e.g., ***********"
          />
          <TextField
            name="description"
            label="Description"
            value={formData.description}
            onChange={handleInputChange}
            fullWidth
            margin="normal"
            placeholder="e.g., Office Network"
          />
          <FormControlLabel
            control={
              <Switch
                name="is_allowed"
                checked={formData.is_allowed}
                onChange={handleInputChange}
                color="primary"
              />
            }
            label={formData.is_allowed ? 'Allowed' : 'Blocked'}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained" color="primary">
            {editMode ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Deletion</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the IP restriction for {restrictionToDelete?.ip_address}?
            This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteConfirm} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default IpRestrictions;
