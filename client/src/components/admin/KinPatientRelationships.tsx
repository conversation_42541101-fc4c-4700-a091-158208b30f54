import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Checkbox,
  FormControlLabel,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  SelectChangeEvent
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import PersonAddIcon from '@mui/icons-material/PersonAdd';
import { API_URL } from '../../config';

interface Patient {
  patient_id: number;
  first_name: string;
  last_name: string;
  unique_id: string;
}

interface Relationship {
  relationship_id: number;
  patient_id: number;
  first_name: string;
  last_name: string;
  relationship_type: string;
  is_primary: boolean;
  date_of_birth?: string;
  gender?: string;
}

interface KinPatientRelationshipsProps {
  kinUserId: number;
  kinUsername: string;
  onRelationshipsUpdated?: () => void;
}

const KinPatientRelationships: React.FC<KinPatientRelationshipsProps> = ({
  kinUserId,
  kinUsername,
  onRelationshipsUpdated
}) => {
  const [relationships, setRelationships] = useState<Relationship[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loadingPatients, setLoadingPatients] = useState(false);
  const [formData, setFormData] = useState({
    patient_id: '',
    relationship_type: '',
    is_primary: false
  });
  const [formError, setFormError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [selectedRelationship, setSelectedRelationship] = useState<Relationship | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);

  // Fetch relationships for this kin user
  const fetchRelationships = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(`${API_URL}/api/kin/relationships/${kinUserId}`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        // If the endpoint doesn't exist yet, show an empty list
        if (response.status === 404) {
          setRelationships([]);
          return;
        }
        throw new Error(`Failed to fetch relationships: ${response.statusText}`);
      }

      const data = await response.json();
      setRelationships(data);
    } catch (err) {
      console.error('Error fetching relationships:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  }, [kinUserId]);

  // Fetch all patients for the dropdown
  const fetchPatients = async () => {
    try {
      setLoadingPatients(true);
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(`${API_URL}/api/patients`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch patients: ${response.statusText}`);
      }

      const data = await response.json();

      // Filter out patients that are already related to this kin
      const filteredPatients = data.filter(
        (patient: Patient) => !relationships.some(rel => rel.patient_id === patient.patient_id)
      );

      setPatients(filteredPatients);
    } catch (err) {
      console.error('Error fetching patients:', err);
      setFormError(err instanceof Error ? err.message : 'Failed to fetch patients');
    } finally {
      setLoadingPatients(false);
    }
  };

  // Load relationships when component mounts
  useEffect(() => {
    fetchRelationships();
  }, [kinUserId, fetchRelationships]);

  // Open add relationship dialog
  const handleOpenDialog = () => {
    setDialogOpen(true);
    fetchPatients();
    setFormData({
      patient_id: '',
      relationship_type: '',
      is_primary: false
    });
    setFormError(null);
  };

  // Close add relationship dialog
  const handleCloseDialog = () => {
    setDialogOpen(false);
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked } = e.target;
    if (name === 'is_primary') {
      setFormData({ ...formData, [name]: checked });
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  // Handle select changes
  const handleSelectChange = (e: SelectChangeEvent) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  // Add a new relationship
  const handleAddRelationship = async () => {
    try {
      setSubmitting(true);
      setFormError(null);

      // Validate form
      if (!formData.patient_id) {
        setFormError('Please select a patient');
        return;
      }

      if (!formData.relationship_type) {
        setFormError('Please specify the relationship type');
        return;
      }

      const token = localStorage.getItem('token');
      if (!token) {
        setFormError('Authentication token not found');
        return;
      }

      // First, try to create the kin_patient_relationships table if it doesn't exist
      try {
        const createTableResponse = await fetch(`${API_URL}/api/admin/create-kin-relationships-table`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          }
        });

        if (createTableResponse.ok) {
          console.log('Created kin_patient_relationships table or it already exists');
        } else {
          console.warn('Failed to create kin_patient_relationships table, but continuing anyway');
        }
      } catch (tableErr) {
        console.warn('Error creating kin_patient_relationships table:', tableErr);
        // Continue anyway, as the table might already exist
      }

      // Now try to add the relationship
      const response = await fetch(`${API_URL}/api/kin/patients`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: JSON.stringify({
          kin_user_id: kinUserId,
          patient_id: parseInt(formData.patient_id),
          relationship_type: formData.relationship_type,
          is_primary: formData.is_primary
        })
      });

      if (!response.ok) {
        // If we get a 404, it might be because the endpoint doesn't exist yet
        // In that case, try to update the user's patient_id directly
        if (response.status === 404 || response.status === 500) {
          console.warn('Failed to add relationship using new API, falling back to legacy method');

          // Legacy method: Update the user's patient_id directly
          const legacyResponse = await fetch(`${API_URL}/api/admin/users/${kinUserId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'x-auth-token': token
            },
            body: JSON.stringify({
              patient_id: parseInt(formData.patient_id)
            })
          });

          if (!legacyResponse.ok) {
            throw new Error(`Failed to update user's patient_id: ${legacyResponse.statusText}`);
          }

          // Close dialog and refresh relationships
          handleCloseDialog();
          fetchRelationships();
          if (onRelationshipsUpdated) {
            onRelationshipsUpdated();
          }

          return;
        }

        throw new Error(`Failed to add relationship: ${response.statusText}`);
      }

      // Close dialog and refresh relationships
      handleCloseDialog();
      fetchRelationships();
      if (onRelationshipsUpdated) {
        onRelationshipsUpdated();
      }
    } catch (err) {
      console.error('Error adding relationship:', err);
      setFormError(err instanceof Error ? err.message : 'Failed to add relationship');
    } finally {
      setSubmitting(false);
    }
  };

  // Open delete relationship dialog
  const handleOpenDeleteDialog = (relationship: Relationship) => {
    setSelectedRelationship(relationship);
    setDeleteDialogOpen(true);
  };

  // Close delete relationship dialog
  const handleCloseDeleteDialog = () => {
    setDeleteDialogOpen(false);
    setSelectedRelationship(null);
  };

  // Delete a relationship
  const handleDeleteRelationship = async () => {
    if (!selectedRelationship) return;

    try {
      setDeleteLoading(true);
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(`${API_URL}/api/kin/patients/${selectedRelationship.relationship_id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to delete relationship: ${response.statusText}`);
      }

      // Close dialog and refresh relationships
      handleCloseDeleteDialog();
      fetchRelationships();
      if (onRelationshipsUpdated) {
        onRelationshipsUpdated();
      }
    } catch (err) {
      console.error('Error deleting relationship:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete relationship');
    } finally {
      setDeleteLoading(false);
    }
  };

  // Set a relationship as primary
  const handleSetPrimary = async (relationshipId: number) => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(`${API_URL}/api/kin/patients/${relationshipId}/primary`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to set primary relationship: ${response.statusText}`);
      }

      // Refresh relationships
      fetchRelationships();
      if (onRelationshipsUpdated) {
        onRelationshipsUpdated();
      }
    } catch (err) {
      console.error('Error setting primary relationship:', err);
      setError(err instanceof Error ? err.message : 'Failed to set primary relationship');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          Patient Relationships for {kinUsername}
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<PersonAddIcon />}
          onClick={handleOpenDialog}
        >
          Add Relationship
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress />
        </Box>
      ) : relationships.length === 0 ? (
        <Alert severity="info">
          No patient relationships found for this kin user. Add a relationship to allow the kin user to view patient data.
        </Alert>
      ) : (
        <TableContainer component={Paper} sx={{ mb: 3 }}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Patient Name</TableCell>
                <TableCell>Relationship</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {relationships.map((relationship) => (
                <TableRow key={relationship.relationship_id}>
                  <TableCell>
                    {relationship.first_name} {relationship.last_name}
                  </TableCell>
                  <TableCell>{relationship.relationship_type}</TableCell>
                  <TableCell>
                    {relationship.is_primary ? (
                      <Chip label="Primary" color="primary" size="small" />
                    ) : (
                      <Button
                        size="small"
                        variant="outlined"
                        onClick={() => handleSetPrimary(relationship.relationship_id)}
                      >
                        Set as Primary
                      </Button>
                    )}
                  </TableCell>
                  <TableCell>
                    <IconButton
                      color="error"
                      onClick={() => handleOpenDeleteDialog(relationship)}
                      disabled={loading}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Add Relationship Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>Add Patient Relationship</DialogTitle>
        <DialogContent>
          {formError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {formError}
            </Alert>
          )}

          <FormControl fullWidth margin="normal" required disabled={submitting || loadingPatients}>
            <InputLabel id="patient-label">Select Patient</InputLabel>
            <Select
              labelId="patient-label"
              name="patient_id"
              value={formData.patient_id}
              label="Select Patient"
              onChange={handleSelectChange}
              startAdornment={loadingPatients ? (
                <CircularProgress size={20} color="inherit" sx={{ ml: 1, mr: 1 }} />
              ) : null}
            >
              {patients.map((patient) => (
                <MenuItem key={patient.patient_id} value={String(patient.patient_id)}>
                  {patient.first_name} {patient.last_name} ({patient.unique_id})
                </MenuItem>
              ))}
            </Select>
            <FormHelperText>
              Select the patient to link to this kin user
            </FormHelperText>
          </FormControl>

          <FormControl fullWidth margin="normal" required disabled={submitting}>
            <InputLabel id="relationship-type-label">Relationship Type</InputLabel>
            <Select
              labelId="relationship-type-label"
              name="relationship_type"
              value={formData.relationship_type}
              label="Relationship Type"
              onChange={handleSelectChange}
            >
              <MenuItem value="spouse">Spouse</MenuItem>
              <MenuItem value="parent">Parent</MenuItem>
              <MenuItem value="child">Child</MenuItem>
              <MenuItem value="sibling">Sibling</MenuItem>
              <MenuItem value="grandparent">Grandparent</MenuItem>
              <MenuItem value="grandchild">Grandchild</MenuItem>
              <MenuItem value="caregiver">Caregiver</MenuItem>
              <MenuItem value="other">Other</MenuItem>
            </Select>
            <FormHelperText>
              Specify the relationship between the kin user and the patient
            </FormHelperText>
          </FormControl>

          <FormControlLabel
            control={
              <Checkbox
                checked={formData.is_primary}
                onChange={handleInputChange}
                name="is_primary"
                disabled={submitting}
              />
            }
            label="Set as primary relationship (this patient will be shown by default)"
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} disabled={submitting}>
            Cancel
          </Button>
          <Button
            onClick={handleAddRelationship}
            color="primary"
            variant="contained"
            disabled={submitting}
            startIcon={submitting ? <CircularProgress size={20} /> : null}
          >
            Add Relationship
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Relationship Dialog */}
      <Dialog open={deleteDialogOpen} onClose={handleCloseDeleteDialog}>
        <DialogTitle>Confirm Deletion</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to remove the relationship between {kinUsername} and{' '}
            {selectedRelationship ? `${selectedRelationship.first_name} ${selectedRelationship.last_name}` : ''}?
          </Typography>
          <Typography variant="body2" color="error" sx={{ mt: 2 }}>
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDeleteDialog} disabled={deleteLoading}>
            Cancel
          </Button>
          <Button
            onClick={handleDeleteRelationship}
            color="error"
            variant="contained"
            disabled={deleteLoading}
            startIcon={deleteLoading ? <CircularProgress size={20} /> : <DeleteIcon />}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default KinPatientRelationships;
