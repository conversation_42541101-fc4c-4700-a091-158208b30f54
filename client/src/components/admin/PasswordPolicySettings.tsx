import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Text<PERSON>ield,
  Typography,
  Paper,
  CircularProgress,
  Alert,
  Container,
  FormControlLabel,
  Switch,
  Grid,
  <PERSON>lider,
  Divider,
  Card,
  CardContent,
  InputAdornment,
  Tooltip,
  IconButton,
  useTheme
} from '@mui/material';
import {
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Help as HelpIcon,
  Security as SecurityIcon
} from '@mui/icons-material';
import axios from 'axios';
import { API_URL } from '../../config';

interface PasswordPolicy {
  min_length: number;
  max_length: number;
  require_uppercase: boolean;
  require_lowercase: boolean;
  require_numbers: boolean;
  require_special_chars: boolean;
  password_expiry_days: number;
  password_history_count: number;
  max_failed_attempts: number;
  default_password_pattern: string;
}

const PasswordPolicySettings: React.FC = () => {
  const theme = useTheme();
  const [policy, setPolicy] = useState<PasswordPolicy>({
    min_length: 8,
    max_length: 30,
    require_uppercase: true,
    require_lowercase: true,
    require_numbers: true,
    require_special_chars: true,
    password_expiry_days: 90,
    password_history_count: 3,
    max_failed_attempts: 5,
    default_password_pattern: '{username}123'
  });

  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Fetch current password policy
  useEffect(() => {
    fetchPolicy();
  }, []);

  const fetchPolicy = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        setError('Authentication token not found. Please log in again.');
        setLoading(false);
        return;
      }

      const response = await axios.get(`${API_URL}/api/admin/password-policy`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      setPolicy(response.data);
    } catch (err: any) {
      console.error('Error fetching password policy:', err);
      setError(err.response?.data?.msg || 'Failed to load password policy. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;

    setPolicy({
      ...policy,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleSliderChange = (name: string) => (_: Event, value: number | number[]) => {
    setPolicy({
      ...policy,
      [name]: value as number
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);
    setSaving(true);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        setError('Authentication token not found. Please log in again.');
        setSaving(false);
        return;
      }

      // Validate inputs
      if (policy.min_length > policy.max_length) {
        setError('Minimum length cannot be greater than maximum length');
        setSaving(false);
        return;
      }

      if (!policy.default_password_pattern.includes('{username}')) {
        setError('Default password pattern must include {username} placeholder');
        setSaving(false);
        return;
      }

      // Make API request to update policy
      const response = await axios.put(
        `${API_URL}/api/admin/password-policy`,
        policy,
        {
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          }
        }
      );

      setSuccess('Password policy updated successfully');

      // Update policy with response data
      setPolicy(response.data.policy);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err: any) {
      console.error('Error updating password policy:', err);
      setError(err.response?.data?.msg || 'Failed to update password policy. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="lg">
      <Paper
        elevation={2}
        sx={{
          p: 3,
          mb: 4,
          borderRadius: 2,
          backgroundColor: theme.palette.primary.dark,
          color: 'white'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <SecurityIcon sx={{ fontSize: 36, mr: 2 }} />
          <Box>
            <Typography variant="h4" fontWeight="500">
              Password Policy Settings
            </Typography>
            <Typography variant="subtitle1" sx={{ opacity: 0.9, mt: 0.5 }}>
              Configure system-wide password requirements and security settings
            </Typography>
          </Box>
        </Box>
      </Paper>

      {error && <Alert severity="error" sx={{ mb: 3 }}>{error}</Alert>}
      {success && <Alert severity="success" sx={{ mb: 3 }}>{success}</Alert>}

      <form onSubmit={handleSubmit}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Password Complexity Requirements
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Box sx={{ mb: 3 }}>
                  <Typography gutterBottom>
                    Password Length
                  </Typography>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs={3}>
                      <TextField
                        name="min_length"
                        label="Minimum"
                        type="number"
                        value={policy.min_length}
                        onChange={handleChange}
                        fullWidth
                        inputProps={{ min: 4, max: 50 }}
                      />
                    </Grid>
                    <Grid item xs={6}>
                      <Slider
                        value={[policy.min_length, policy.max_length]}
                        onChange={(_, value) => {
                          const [min, max] = value as number[];
                          setPolicy({
                            ...policy,
                            min_length: min,
                            max_length: max
                          });
                        }}
                        min={4}
                        max={50}
                        step={1}
                        marks={[
                          { value: 4, label: '4' },
                          { value: 20, label: '20' },
                          { value: 50, label: '50' }
                        ]}
                        valueLabelDisplay="auto"
                      />
                    </Grid>
                    <Grid item xs={3}>
                      <TextField
                        name="max_length"
                        label="Maximum"
                        type="number"
                        value={policy.max_length}
                        onChange={handleChange}
                        fullWidth
                        inputProps={{ min: policy.min_length, max: 100 }}
                      />
                    </Grid>
                  </Grid>
                </Box>

                <Box sx={{ mb: 2 }}>
                  <Typography gutterBottom>
                    Character Requirements
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            name="require_uppercase"
                            checked={policy.require_uppercase}
                            onChange={handleChange}
                            color="primary"
                          />
                        }
                        label="Require uppercase letters"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            name="require_lowercase"
                            checked={policy.require_lowercase}
                            onChange={handleChange}
                            color="primary"
                          />
                        }
                        label="Require lowercase letters"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            name="require_numbers"
                            checked={policy.require_numbers}
                            onChange={handleChange}
                            color="primary"
                          />
                        }
                        label="Require numbers"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <FormControlLabel
                        control={
                          <Switch
                            name="require_special_chars"
                            checked={policy.require_special_chars}
                            onChange={handleChange}
                            color="primary"
                          />
                        }
                        label="Require special characters"
                      />
                    </Grid>
                  </Grid>
                </Box>
              </CardContent>
            </Card>

            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Default Password Settings
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Box sx={{ mb: 2 }}>
                  <TextField
                    name="default_password_pattern"
                    label="Default Password Pattern"
                    value={policy.default_password_pattern}
                    onChange={handleChange}
                    fullWidth
                    margin="normal"
                    helperText="Use {username} as a placeholder for the user's username"
                    InputProps={{
                      endAdornment: (
                        <InputAdornment position="end">
                          <Tooltip title="This pattern is used to generate default passwords for new users. The {username} placeholder will be replaced with the user's actual username.">
                            <IconButton edge="end">
                              <HelpIcon />
                            </IconButton>
                          </Tooltip>
                        </InputAdornment>
                      )
                    }}
                  />
                </Box>

                <Alert severity="info" sx={{ mt: 2 }}>
                  <Typography variant="body2">
                    Example: For a user with username "john.doe" and pattern "{policy.default_password_pattern}",
                    the default password would be "{policy.default_password_pattern.replace('{username}', 'john.doe')}"
                  </Typography>
                </Alert>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card variant="outlined" sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Password Lifecycle
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Box sx={{ mb: 3 }}>
                  <Typography gutterBottom>
                    Password Expiration (days)
                  </Typography>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs={9}>
                      <Slider
                        value={policy.password_expiry_days}
                        onChange={handleSliderChange('password_expiry_days')}
                        min={0}
                        max={365}
                        step={1}
                        marks={[
                          { value: 0, label: 'Never' },
                          { value: 90, label: '90' },
                          { value: 180, label: '180' },
                          { value: 365, label: '365' }
                        ]}
                        valueLabelDisplay="auto"
                      />
                    </Grid>
                    <Grid item xs={3}>
                      <TextField
                        name="password_expiry_days"
                        type="number"
                        value={policy.password_expiry_days}
                        onChange={handleChange}
                        fullWidth
                        inputProps={{ min: 0, max: 365 }}
                      />
                    </Grid>
                  </Grid>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    {policy.password_expiry_days === 0
                      ? 'Passwords never expire'
                      : `Passwords expire after ${policy.password_expiry_days} days`}
                  </Typography>
                </Box>

                <Box sx={{ mb: 3 }}>
                  <Typography gutterBottom>
                    Password History (prevent reuse)
                  </Typography>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs={9}>
                      <Slider
                        value={policy.password_history_count}
                        onChange={handleSliderChange('password_history_count')}
                        min={0}
                        max={20}
                        step={1}
                        marks={[
                          { value: 0, label: 'None' },
                          { value: 5, label: '5' },
                          { value: 10, label: '10' },
                          { value: 20, label: '20' }
                        ]}
                        valueLabelDisplay="auto"
                      />
                    </Grid>
                    <Grid item xs={3}>
                      <TextField
                        name="password_history_count"
                        type="number"
                        value={policy.password_history_count}
                        onChange={handleChange}
                        fullWidth
                        inputProps={{ min: 0, max: 20 }}
                      />
                    </Grid>
                  </Grid>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    {policy.password_history_count === 0
                      ? 'Password history not enforced'
                      : `Users cannot reuse their last ${policy.password_history_count} passwords`}
                  </Typography>
                </Box>
              </CardContent>
            </Card>

            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Account Security
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Box sx={{ mb: 3 }}>
                  <Typography gutterBottom>
                    Maximum Failed Login Attempts
                  </Typography>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs={9}>
                      <Slider
                        value={policy.max_failed_attempts}
                        onChange={handleSliderChange('max_failed_attempts')}
                        min={1}
                        max={10}
                        step={1}
                        marks={[
                          { value: 1, label: '1' },
                          { value: 5, label: '5' },
                          { value: 10, label: '10' }
                        ]}
                        valueLabelDisplay="auto"
                      />
                    </Grid>
                    <Grid item xs={3}>
                      <TextField
                        name="max_failed_attempts"
                        type="number"
                        value={policy.max_failed_attempts}
                        onChange={handleChange}
                        fullWidth
                        inputProps={{ min: 1, max: 10 }}
                      />
                    </Grid>
                  </Grid>
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    Accounts will be locked after {policy.max_failed_attempts} failed login attempts
                  </Typography>
                </Box>

                <Alert severity="warning" sx={{ mt: 2 }}>
                  <Typography variant="body2">
                    Changing these settings will apply to all users in the system.
                    Users with expired passwords will be required to change their password on next login.
                  </Typography>
                </Alert>
              </CardContent>
            </Card>
          </Grid>
        </Grid>

        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={fetchPolicy}
            disabled={loading || saving}
          >
            Reset to Current
          </Button>
          <Button
            type="submit"
            variant="contained"
            color="primary"
            startIcon={saving ? <CircularProgress size={24} /> : <SaveIcon />}
            disabled={loading || saving}
          >
            Save Policy
          </Button>
        </Box>
      </form>
    </Container>
  );
};

export default PasswordPolicySettings;
