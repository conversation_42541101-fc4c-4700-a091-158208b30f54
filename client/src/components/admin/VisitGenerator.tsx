import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  TextField,
  CircularProgress,
  Alert,
  Snackbar,
  Card,
  CardContent,
  CardActions,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  Person as PersonIcon,
  EventNote as VisitIcon,
  Check as CheckIcon,
  Search as SearchIcon,
  CalendarToday as CalendarIcon
} from '@mui/icons-material';
import { API_URL } from '../../config';

interface Patient {
  patient_id: string;
  first_name: string;
  last_name: string;
  date_of_birth: string;
  doctor_id: string;
  unique_id?: string;
}

interface Visit {
  visit_id: string;
  patient_id: string;
  doctor_id: string;
  visit_date: string;
  visit_reason: string;
  status: string;
}

interface SnackbarState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'info' | 'warning';
}

const VisitGenerator: React.FC = () => {
  // State for form fields
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedPatientId, setSelectedPatientId] = useState<string>('');
  const [selectedDate, setSelectedDate] = useState<string>(new Date().toISOString().split('T')[0]);

  // State for data
  const [patients, setPatients] = useState<Patient[]>([]);
  const [filteredPatients, setFilteredPatients] = useState<Patient[]>([]);
  const [generatedVisit, setGeneratedVisit] = useState<Visit | null>(null);

  // State for UI
  const [patientsLoading, setPatientsLoading] = useState<boolean>(true);
  const [visitLoading, setVisitLoading] = useState<boolean>(false);
  const [showGenerateVisitDialog, setShowGenerateVisitDialog] = useState<boolean>(false);

  // Notification state
  const [snackbar, setSnackbar] = useState<SnackbarState>({
    open: false,
    message: '',
    severity: 'info'
  });

  // Fetch patients on component mount
  useEffect(() => {
    fetchPatients();
  }, []);

  // Filter patients when search term changes
  useEffect(() => {
    if (searchTerm.trim() === '') {
      setFilteredPatients(patients);
    } else {
      const filtered = patients.filter(
        patient =>
          patient.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          patient.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (patient.unique_id && patient.unique_id.toLowerCase().includes(searchTerm.toLowerCase()))
      );
      setFilteredPatients(filtered);
    }
  }, [searchTerm, patients]);

  const fetchPatients = async () => {
    try {
      setPatientsLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/patients`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch patients');
      }

      const data = await response.json();
      setPatients(data);
      setFilteredPatients(data);
    } catch (err) {
      console.error('Error fetching patients:', err);
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to fetch patients',
        severity: 'error'
      });
    } finally {
      setPatientsLoading(false);
    }
  };

  const generateVisit = async () => {
    try {
      if (!selectedPatientId) {
        throw new Error('Please select a patient');
      }

      setVisitLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/debug/generate-visit-for-patient`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: JSON.stringify({
          patientId: selectedPatientId,
          visitDate: selectedDate || null
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || 'Failed to generate visit');
      }

      // Show success message
      setSnackbar({
        open: true,
        message: data.msg || 'Visit generated successfully',
        severity: 'success'
      });

      // Store the generated visit
      setGeneratedVisit(data.visit);

      // Close the dialog
      setShowGenerateVisitDialog(false);

    } catch (err) {
      console.error('Error generating visit:', err);
      setSnackbar({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to generate visit',
        severity: 'error'
      });
    } finally {
      setVisitLoading(false);
    }
  };

  const handlePatientSelect = (patientId: string) => {
    setSelectedPatientId(patientId);
    setShowGenerateVisitDialog(true);
  };

  const resetForm = () => {
    setSearchTerm('');
    setSelectedPatientId('');
    setSelectedDate(new Date().toISOString().split('T')[0]);
    setGeneratedVisit(null);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
        <VisitIcon sx={{ mr: 1 }} />
        Visit Generator
      </Typography>

      <Typography variant="body1" paragraph color="text.secondary">
        This tool allows you to generate visits for existing patients. Select a patient and a date, and the system will generate a visit with random medical data.
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Select a Patient
            </Typography>

            <TextField
              fullWidth
              label="Search Patients"
              variant="outlined"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              sx={{ mb: 3 }}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
            />

            {patientsLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
              </Box>
            ) : filteredPatients.length === 0 ? (
              <Alert severity="info">No patients found. Please create a patient first.</Alert>
            ) : (
              <TableContainer sx={{ maxHeight: 400 }}>
                <Table stickyHeader>
                  <TableHead>
                    <TableRow>
                      <TableCell>ID</TableCell>
                      <TableCell>Name</TableCell>
                      <TableCell>Date of Birth</TableCell>
                      <TableCell>Action</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {filteredPatients.map((patient) => (
                      <TableRow key={patient.patient_id} hover>
                        <TableCell>{patient.unique_id || patient.patient_id}</TableCell>
                        <TableCell>{patient.first_name} {patient.last_name}</TableCell>
                        <TableCell>
                          {new Date(patient.date_of_birth).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="contained"
                            size="small"
                            color="primary"
                            onClick={() => handlePatientSelect(patient.patient_id)}
                            startIcon={<VisitIcon />}
                          >
                            Generate Visit
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          {generatedVisit ? (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                  <CheckIcon sx={{ mr: 1, color: 'success.main' }} />
                  Visit Generated Successfully
                </Typography>

                <Divider sx={{ my: 2 }} />

                <Typography variant="subtitle1" gutterBottom>
                  Basic Information
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemIcon>
                      <PersonIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Patient"
                      secondary={patients.find(p => p.patient_id === generatedVisit.patient_id)
                        ? `${patients.find(p => p.patient_id === generatedVisit.patient_id)?.first_name} ${patients.find(p => p.patient_id === generatedVisit.patient_id)?.last_name}`
                        : `Patient ID: ${generatedVisit.patient_id}`
                      }
                    />
                  </ListItem>

                  <ListItem>
                    <ListItemIcon>
                      <CalendarIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Visit Date"
                      secondary={new Date(generatedVisit.visit_date).toLocaleString()}
                    />
                  </ListItem>

                  <ListItem>
                    <ListItemText
                      primary="Visit Reason"
                      secondary={generatedVisit.visit_reason}
                    />
                  </ListItem>

                  <ListItem>
                    <ListItemText
                      primary="Status"
                      secondary={generatedVisit.status}
                    />
                  </ListItem>
                </List>

                <Divider sx={{ my: 2 }} />

                <Typography variant="subtitle1" gutterBottom>
                  Comprehensive Data Generated
                </Typography>
                <Alert severity="success" sx={{ mb: 2 }}>
                  A comprehensive visit record has been created with the following data:
                </Alert>

                <Grid container spacing={1}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      • Vital signs (BP, heart rate, etc.)
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      • Physical measurements
                    </Typography>
                  </Grid>

                  {/* Lab Results Section */}
                  <Grid item xs={12} sx={{ mt: 1 }}>
                    <Typography variant="subtitle2" color="primary">
                      Comprehensive Lab Results:
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      • Complete Blood Count (CBC)
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      • Comprehensive Metabolic Panel
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      • Lipid Panel
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      • Thyroid Function Tests
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      • Liver Function Tests
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      • Kidney Function Tests
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      • Diabetes Markers
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      • Inflammation Markers
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      • Vitamin Levels
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      • Bone Health Markers
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      • Iron Studies
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      • Hormone Levels
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      • Urinalysis
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      • Cardiac Markers
                    </Typography>
                  </Grid>

                  {/* Other Assessments */}
                  <Grid item xs={12} sx={{ mt: 1 }}>
                    <Typography variant="subtitle2" color="primary">
                      Health Assessments:
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      • Pain and cognitive assessments
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      • Mental health scores
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      • Social determinants of health
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      • Medical conditions and allergies
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">
                      • Detailed clinical notes
                    </Typography>
                  </Grid>
                </Grid>

                <Box sx={{ mt: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    Visit ID: {generatedVisit.visit_id}
                  </Typography>
                </Box>
              </CardContent>

              <CardActions>
                <Button
                  fullWidth
                  variant="outlined"
                  onClick={resetForm}
                >
                  Generate Another Visit
                </Button>
              </CardActions>
            </Card>
          ) : (
            <Paper sx={{ p: 4, height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center' }}>
              <VisitIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary" align="center">
                No Visit Generated Yet
              </Typography>
              <Typography variant="body2" color="text.secondary" align="center" sx={{ mt: 1 }}>
                Select a patient from the list and click "Generate Visit" to create a new visit with random medical data.
              </Typography>
            </Paper>
          )}
        </Grid>
      </Grid>

      {/* Generate Visit Dialog */}
      <Dialog
        open={showGenerateVisitDialog}
        onClose={() => setShowGenerateVisitDialog(false)}
        fullWidth
        maxWidth="sm"
      >
        <DialogTitle>
          Generate Visit for {patients.find(p => p.patient_id === selectedPatientId)?.first_name} {patients.find(p => p.patient_id === selectedPatientId)?.last_name}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <TextField
              fullWidth
              label="Visit Date"
              type="date"
              value={selectedDate}
              onChange={(e) => setSelectedDate(e.target.value)}
              margin="normal"
              InputLabelProps={{
                shrink: true,
              }}
            />

            <Alert severity="info" sx={{ mt: 2 }}>
              This will generate a comprehensive visit record for the selected patient on the specified date with automatically generated data including:
            </Alert>

            <Box sx={{ mt: 2, ml: 2 }}>
              <Typography variant="body2" paragraph>
                <strong>• Medical Data:</strong> Vital signs, physical measurements
              </Typography>

              <Typography variant="subtitle2" color="primary" sx={{ mt: 1 }}>
                Comprehensive Lab Tests:
              </Typography>
              <Grid container spacing={1} sx={{ ml: 1 }}>
                <Grid item xs={6}>
                  <Typography variant="body2">• Complete Blood Count (CBC)</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2">• Comprehensive Metabolic Panel</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2">• Lipid Panel</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2">• Thyroid Function Tests</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2">• Liver Function Tests</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2">• Kidney Function Tests</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2">• Diabetes Markers</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2">• Inflammation Markers</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2">• Vitamin Levels</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2">• Bone Health Markers</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2">• Iron Studies</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2">• Hormone Levels</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2">• Urinalysis</Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2">• Cardiac Markers</Typography>
                </Grid>
              </Grid>

              <Typography variant="body2" paragraph sx={{ mt: 2 }}>
                <strong>• Health Assessments:</strong> Pain level, cognitive scores, depression/anxiety scores, sleep quality
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>• Social Factors:</strong> Transportation access, financial concerns, social support
              </Typography>
              <Typography variant="body2" paragraph>
                <strong>• Clinical Information:</strong> Medical conditions, allergies, detailed visit notes
              </Typography>
            </Box>

            <Alert severity="success" sx={{ mt: 2 }}>
              All data will be automatically generated with realistic values, saving you time and effort.
            </Alert>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowGenerateVisitDialog(false)}>Cancel</Button>
          <Button
            variant="contained"
            color="primary"
            startIcon={visitLoading ? <CircularProgress size={20} /> : <VisitIcon />}
            onClick={generateVisit}
            disabled={visitLoading}
          >
            {visitLoading ? 'Generating...' : 'Generate Visit'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert
          onClose={() => setSnackbar({ ...snackbar, open: false })}
          severity={snackbar.severity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default VisitGenerator;
