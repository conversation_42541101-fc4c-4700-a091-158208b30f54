import React, { useState } from 'react';
import {
  Box,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Typography,
  Paper,
  Grid,
  Alert,
  CircularProgress,
  FormHelperText,
  Divider,
  SelectChangeEvent
} from '@mui/material';
import { PersonAdd as PersonAddIcon } from '@mui/icons-material';
import axios from 'axios';
import { API_URL } from '../../config';

interface UserCreateFormProps {
  onUserCreated: () => void;
}

interface FormData {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  role: string;
  patient_id?: string | null; // Changed to string to work with MUI Select
  relationship_type?: string;
}

interface Patient {
  patient_id: number;
  first_name: string;
  last_name: string;
  unique_id: string;
}

const UserCreateForm: React.FC<UserCreateFormProps> = ({ onUserCreated }) => {
  const [formData, setFormData] = useState<FormData>({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    role: 'staff',
    patient_id: null,
    relationship_type: ''
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loadingPatients, setLoadingPatients] = useState(false);

  const { username, email, password, confirmPassword, role, patient_id, relationship_type } = formData;

  const handleTextFieldChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    if (name) {
      setFormData({ ...formData, [name]: value });
    }
  };

  const handleSelectChange = (e: SelectChangeEvent) => {
    const { name, value } = e.target;
    if (name) {
      // If changing to kin role, fetch patients
      if (name === 'role' && value === 'kin') {
        fetchPatients();
      }

      // If changing from kin role to something else, clear patient_id
      if (name === 'role' && value !== 'kin') {
        setFormData({ ...formData, [name]: value, patient_id: null });
      } else {
        setFormData({ ...formData, [name]: value });
      }
    }
  };

  const fetchPatients = async () => {
    try {
      setLoadingPatients(true);
      const token = localStorage.getItem('token');

      if (!token) {
        setError('Authentication token not found. Please log in again.');
        return;
      }

      const response = await axios.get(`${API_URL}/api/patients`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      setPatients(response.data);
    } catch (err) {
      console.error('Error fetching patients:', err);
      setError('Failed to load patients. Please try again.');
    } finally {
      setLoadingPatients(false);
    }
  };

  const validateForm = () => {
    if (!username || !email || !password || !confirmPassword || !role) {
      setError('All fields are required');
      return false;
    }

    // If role is kin, patient_id and relationship_type are required
    if (role === 'kin') {
      if (!patient_id) {
        setError('Please select a patient for the kin user');
        return false;
      }
      if (!relationship_type) {
        setError('Please select a relationship type for the kin user');
        return false;
      }
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return false;
    }

    if (password.length < 6) {
      setError('Password must be at least 6 characters');
      return false;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setSuccess(null);

    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        setError('Authentication token not found. Please log in again.');
        setLoading(false);
        return;
      }

      // Make sure we're using the correct API URL
      const apiUrl = `${API_URL}/api/admin/users`;
      console.log('Making API request to:', apiUrl);
      console.log('Request data:', { username, email, role });

      // Create axios instance with proper configuration
      const axiosInstance = axios.create({
        baseURL: API_URL,
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        // Add withCredentials for CORS with credentials
        withCredentials: false
      });

      // Prepare request data
      const requestData = {
        username,
        email,
        password,
        role
      };

      // Add patient_id and relationship_type if role is kin
      if (role === 'kin' && patient_id) {
        // Convert patient_id to number for the API
        Object.assign(requestData, {
          patient_id: parseInt(patient_id, 10),
          relationship_type: relationship_type
        });
      }

      console.log('Sending request data:', { ...requestData, password: '***' });

      // Make the request
      const response = await axiosInstance.post('/api/admin/users', requestData);

      const data = response.data;

      // Reset form
      setFormData({
        username: '',
        email: '',
        password: '',
        confirmPassword: '',
        role: 'staff',
        patient_id: null,
        relationship_type: ''
      });

      setSuccess(`User ${username} created successfully with role: ${role}`);
      onUserCreated(); // Notify parent component to refresh user list
    } catch (err) {
      if (axios.isAxiosError(err)) {
        // Handle Axios errors
        if (err.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          const errorMessage = err.response.data?.msg ||
                              (typeof err.response.data === 'string' ? err.response.data : 'Server error');
          setError(`Server error (${err.response.status}): ${errorMessage}`);
          console.error('Server error response:', {
            status: err.response.status,
            data: err.response.data,
            headers: err.response.headers
          });
        } else if (err.request) {
          // The request was made but no response was received
          setError('No response received from server. Please check your network connection.');
          console.error('No response received:', err.request);
        } else {
          // Something happened in setting up the request that triggered an Error
          setError(`Request configuration error: ${err.message}`);
          console.error('Request setup error:', err.message);
        }
      } else {
        // Handle other errors
        setError(err instanceof Error ? err.message : 'An error occurred while creating the user');
        console.error('Error creating user:', err);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Paper elevation={2} sx={{ p: 3, mb: 4, borderRadius: 2 }}>
      <Typography variant="h5" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <PersonAddIcon color="primary" />
        Create New User
      </Typography>
      <Divider sx={{ mb: 3 }} />

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {success}
        </Alert>
      )}

      <Box component="form" onSubmit={handleSubmit}>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Username"
              name="username"
              value={username}
              onChange={handleTextFieldChange}
              variant="outlined"
              required
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Email"
              name="email"
              type="email"
              value={email}
              onChange={handleTextFieldChange}
              variant="outlined"
              required
              disabled={loading}
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Password"
              name="password"
              type="password"
              value={password}
              onChange={handleTextFieldChange}
              variant="outlined"
              required
              disabled={loading}
              helperText="Minimum 6 characters"
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Confirm Password"
              name="confirmPassword"
              type="password"
              value={confirmPassword}
              onChange={handleTextFieldChange}
              variant="outlined"
              required
              disabled={loading}
              error={password !== confirmPassword && confirmPassword !== ''}
              helperText={
                password !== confirmPassword && confirmPassword !== ''
                  ? 'Passwords do not match'
                  : ''
              }
            />
          </Grid>

          <Grid item xs={12} sm={role === 'kin' ? 6 : 12}>
            <FormControl fullWidth required disabled={loading}>
              <InputLabel id="role-label">User Role</InputLabel>
              <Select
                labelId="role-label"
                name="role"
                value={role}
                label="User Role"
                onChange={handleSelectChange}
              >
                <MenuItem value="admin">Administrator</MenuItem>
                <MenuItem value="doctor">Doctor</MenuItem>
                <MenuItem value="staff">Staff</MenuItem>
                <MenuItem value="patient">Patient</MenuItem>
                <MenuItem value="kin">Kin</MenuItem>
                <MenuItem value="researcher">Researcher</MenuItem>
              </Select>
              <FormHelperText>
                Select the appropriate role for this user
              </FormHelperText>
            </FormControl>
          </Grid>

          {role === 'kin' && (
            <>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth required disabled={loading || loadingPatients}>
                  <InputLabel id="patient-label">Primary Patient</InputLabel>
                  <Select
                    labelId="patient-label"
                    name="patient_id"
                    value={patient_id || ''}
                    label="Primary Patient"
                    onChange={handleSelectChange}
                    startAdornment={loadingPatients ? (
                      <CircularProgress size={20} color="inherit" sx={{ ml: 1, mr: 1 }} />
                    ) : null}
                  >
                    {patients.map((patient) => (
                      <MenuItem key={patient.patient_id} value={String(patient.patient_id)}>
                        {patient.first_name} {patient.last_name} ({patient.unique_id})
                      </MenuItem>
                    ))}
                  </Select>
                  <FormHelperText>
                    Select the primary patient for this kin user
                  </FormHelperText>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth required disabled={loading}>
                  <InputLabel id="relationship-label">Relationship Type</InputLabel>
                  <Select
                    labelId="relationship-label"
                    name="relationship_type"
                    value={relationship_type || ''}
                    label="Relationship Type"
                    onChange={handleSelectChange}
                  >
                    <MenuItem value="spouse">Spouse</MenuItem>
                    <MenuItem value="parent">Parent</MenuItem>
                    <MenuItem value="child">Child</MenuItem>
                    <MenuItem value="sibling">Sibling</MenuItem>
                    <MenuItem value="grandparent">Grandparent</MenuItem>
                    <MenuItem value="grandchild">Grandchild</MenuItem>
                    <MenuItem value="guardian">Legal Guardian</MenuItem>
                    <MenuItem value="caregiver">Caregiver</MenuItem>
                    <MenuItem value="other">Other</MenuItem>
                  </Select>
                  <FormHelperText>
                    Select the relationship to the patient
                  </FormHelperText>
                </FormControl>
              </Grid>
              <Grid item xs={12}>
                <Alert severity="info" sx={{ mt: 1 }}>
                  <Typography variant="body2">
                    <strong>Note:</strong> After creating the kin user, you can add additional patient relationships by editing the user and going to the "Patient Relationships" tab.
                  </Typography>
                </Alert>
              </Grid>
            </>
          )}

          <Grid item xs={12}>
            <Button
              type="submit"
              variant="contained"
              color="primary"
              fullWidth
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : <PersonAddIcon />}
              sx={{ mt: 1 }}
            >
              {loading ? 'Creating User...' : 'Create User'}
            </Button>
          </Grid>
        </Grid>
      </Box>
    </Paper>
  );
};

export default UserCreateForm;
