import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  Switch,
  FormControlLabel,
  TextField,
  Divider,
  Alert,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Tooltip,
  useTheme,
  alpha,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle
} from '@mui/material';
import {
  Security as SecurityIcon,
  Refresh as RefreshIcon,
  // Add as AddIcon - Unused import
  // Delete as DeleteIcon - Unused import
  // Edit as EditIcon - Unused import
  Check as CheckIcon,
  Close as CloseIcon,
  VerifiedUser as VerifiedUserIcon,
  PhoneAndroid as PhoneIcon,
  Email as EmailIcon,
  Key as KeyIcon
} from '@mui/icons-material';
// axios - Unused import
// API_URL - Unused import

interface User {
  user_id: number;
  username: string;
  email: string;
  role: string;
  mfa_enabled: boolean;
  mfa_method: string | null;
  last_login: string;
}

const MfaManagement: React.FC = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [users, setUsers] = useState<User[]>([]);
  const [settings, setSettings] = useState({
    mfa_required_for_admins: true,
    mfa_required_for_doctors: false,
    mfa_required_for_staff: false,
    mfa_required_for_patients: false,
    mfa_methods_available: ['email', 'app'],
    mfa_setup_on_first_login: true,
    mfa_grace_period_days: 7
  });
  const [openDialog, setOpenDialog] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  // Define fetchData function before using it in useEffect
  const fetchData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      // This is a placeholder - in a real implementation, you would fetch actual data from your API
      // Fetch MFA settings
      // const settingsResponse = await axios.get(`${API_URL}/api/admin/mfa-settings`, {
      //   headers: {
      //     'Content-Type': 'application/json',
      //     'x-auth-token': token
      //   }
      // });
      // setSettings(settingsResponse.data);

      // Fetch users with MFA status
      // const usersResponse = await axios.get(`${API_URL}/api/admin/users-mfa-status`, {
      //   headers: {
      //     'Content-Type': 'application/json',
      //     'x-auth-token': token
      //   }
      // });
      // setUsers(usersResponse.data);

      // For now, we'll use mock data
      setUsers(generateMockUsers());
    } catch (err) {
      console.error('Error fetching MFA data:', err);
      setError('Failed to fetch MFA data. Please try again later.');
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch MFA settings and users with MFA status
  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const generateMockUsers = (): User[] => {
    const roles = ['admin', 'doctor', 'staff', 'patient'];
    const mfaMethods = ['email', 'app', null];

    return Array.from({ length: 15 }, (_, i) => {
      const role = roles[Math.floor(Math.random() * roles.length)];
      const mfaEnabled = Math.random() > 0.5;

      return {
        user_id: i + 1,
        username: `user_${i + 1}`,
        email: `user${i + 1}@example.com`,
        role,
        mfa_enabled: mfaEnabled,
        mfa_method: mfaEnabled ? mfaMethods[Math.floor(Math.random() * (mfaMethods.length - 1))] : null,
        last_login: new Date(Date.now() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000).toISOString()
      };
    });
  };

  const handleSaveSettings = async () => {
    try {
      setSaving(true);
      setError(null);
      setSuccess(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      // This is a placeholder - in a real implementation, you would save the settings to your API
      // await axios.put(
      //   `${API_URL}/api/admin/mfa-settings`,
      //   settings,
      //   {
      //     headers: {
      //       'Content-Type': 'application/json',
      //       'x-auth-token': token
      //     }
      //   }
      // );

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      setSuccess('MFA settings saved successfully');
    } catch (err) {
      console.error('Error saving MFA settings:', err);
      setError('Failed to save MFA settings. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleToggleUserMfa = (user: User) => {
    setSelectedUser(user);
    setOpenDialog(true);
  };

  const handleConfirmToggleMfa = async () => {
    if (!selectedUser) return;

    try {
      setSaving(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      // This is a placeholder - in a real implementation, you would toggle MFA for the user via your API
      // await axios.put(
      //   `${API_URL}/api/admin/users/${selectedUser.user_id}/toggle-mfa`,
      //   { enabled: !selectedUser.mfa_enabled },
      //   {
      //     headers: {
      //       'Content-Type': 'application/json',
      //       'x-auth-token': token
      //     }
      //   }
      // );

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Update local state
      setUsers(users.map(user =>
        user.user_id === selectedUser.user_id
          ? {
              ...user,
              mfa_enabled: !user.mfa_enabled,
              mfa_method: !user.mfa_enabled ? 'email' : null
            }
          : user
      ));

      setSuccess(`MFA ${selectedUser.mfa_enabled ? 'disabled' : 'enabled'} for user ${selectedUser.username}`);
    } catch (err) {
      console.error('Error toggling MFA for user:', err);
      setError('Failed to toggle MFA for user. Please try again.');
    } finally {
      setSaving(false);
      setOpenDialog(false);
      setSelectedUser(null);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;

    setSettings({
      ...settings,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const filteredUsers = users.filter(user =>
    user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    user.role.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: 400 }}>
        <CircularProgress size={60} thickness={4} />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 4,
          borderRadius: 2,
          background: 'linear-gradient(to right, #1a237e, #3949ab)',
          color: 'white',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        <Box sx={{
          position: 'absolute',
          top: 0,
          right: 0,
          width: '30%',
          height: '100%',
          opacity: 0.1,
          background: `url('https://www.transparenttextures.com/patterns/cubes.png')`,
          backgroundSize: 'cover'
        }} />

        <Grid container spacing={2} alignItems="center">
          <Grid item>
            <SecurityIcon sx={{ fontSize: 40 }} />
          </Grid>
          <Grid item xs>
            <Typography variant="h4" fontWeight="500">
              Multi-Factor Authentication
            </Typography>
            <Typography variant="subtitle1" sx={{ opacity: 0.9, mt: 0.5 }}>
              Manage MFA settings and user enrollment
            </Typography>
          </Grid>
          <Grid item>
            <Tooltip title="Refresh data">
              <IconButton
                color="inherit"
                onClick={fetchData}
                disabled={loading}
                sx={{
                  bgcolor: 'rgba(255,255,255,0.1)',
                  '&:hover': { bgcolor: 'rgba(255,255,255,0.2)' }
                }}
              >
                {loading ? <CircularProgress size={24} color="inherit" /> : <RefreshIcon />}
              </IconButton>
            </Tooltip>
          </Grid>
        </Grid>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* MFA Settings */}
        <Grid item xs={12} md={5}>
          <Paper
            elevation={0}
            sx={{
              p: 3,
              borderRadius: 2,
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
              height: '100%'
            }}
          >
            <Typography variant="h6" fontWeight="500" gutterBottom>
              MFA Settings
            </Typography>
            <Divider sx={{ mb: 3 }} />

            <Box component="form" sx={{ mb: 3 }}>
              <Typography variant="subtitle2" fontWeight="500" gutterBottom>
                Required for User Roles
              </Typography>
              <Box sx={{ ml: 2, mb: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      name="mfa_required_for_admins"
                      checked={settings.mfa_required_for_admins}
                      onChange={handleChange}
                      color="primary"
                    />
                  }
                  label="Administrators"
                />
                <FormControlLabel
                  control={
                    <Switch
                      name="mfa_required_for_doctors"
                      checked={settings.mfa_required_for_doctors}
                      onChange={handleChange}
                      color="primary"
                    />
                  }
                  label="Doctors"
                />
                <FormControlLabel
                  control={
                    <Switch
                      name="mfa_required_for_staff"
                      checked={settings.mfa_required_for_staff}
                      onChange={handleChange}
                      color="primary"
                    />
                  }
                  label="Staff"
                />
                <FormControlLabel
                  control={
                    <Switch
                      name="mfa_required_for_patients"
                      checked={settings.mfa_required_for_patients}
                      onChange={handleChange}
                      color="primary"
                    />
                  }
                  label="Patients"
                />
              </Box>

              <Typography variant="subtitle2" fontWeight="500" gutterBottom>
                MFA Setup
              </Typography>
              <Box sx={{ ml: 2, mb: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      name="mfa_setup_on_first_login"
                      checked={settings.mfa_setup_on_first_login}
                      onChange={handleChange}
                      color="primary"
                    />
                  }
                  label="Require MFA setup on first login"
                />
                <TextField
                  name="mfa_grace_period_days"
                  label="Grace Period (days)"
                  type="number"
                  value={settings.mfa_grace_period_days}
                  onChange={handleChange}
                  fullWidth
                  margin="normal"
                  inputProps={{ min: 0, max: 30 }}
                  helperText="Days allowed before MFA setup is enforced"
                />
              </Box>

              <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleSaveSettings}
                  disabled={saving}
                  startIcon={saving ? <CircularProgress size={20} /> : <CheckIcon />}
                >
                  Save Settings
                </Button>
              </Box>
            </Box>
          </Paper>
        </Grid>

        {/* User MFA Status */}
        <Grid item xs={12} md={7}>
          <Paper
            elevation={0}
            sx={{
              p: 3,
              borderRadius: 2,
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
            }}
          >
            <Typography variant="h6" fontWeight="500" gutterBottom>
              User MFA Status
            </Typography>
            <Divider sx={{ mb: 3 }} />

            <Box sx={{ mb: 3 }}>
              <TextField
                fullWidth
                placeholder="Search users by name, email, or role"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                variant="outlined"
                size="small"
              />
            </Box>

            <TableContainer>
              <Table size="small">
                <TableHead sx={{ bgcolor: alpha(theme.palette.primary.main, 0.05) }}>
                  <TableRow>
                    <TableCell sx={{ fontWeight: 600 }}>Username</TableCell>
                    <TableCell sx={{ fontWeight: 600 }}>Role</TableCell>
                    <TableCell sx={{ fontWeight: 600 }}>MFA Status</TableCell>
                    <TableCell sx={{ fontWeight: 600 }}>Method</TableCell>
                    <TableCell sx={{ fontWeight: 600 }}>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredUsers.map((user) => (
                    <TableRow key={user.user_id}>
                      <TableCell>
                        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                          <Typography variant="body2" fontWeight="500">
                            {user.username}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {user.email}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={user.role.charAt(0).toUpperCase() + user.role.slice(1)}
                          size="small"
                          color={
                            user.role === 'admin' ? 'primary' :
                            user.role === 'doctor' ? 'success' :
                            user.role === 'staff' ? 'info' : 'default'
                          }
                          sx={{ fontWeight: 500 }}
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          icon={user.mfa_enabled ? <VerifiedUserIcon /> : <CloseIcon />}
                          label={user.mfa_enabled ? 'Enabled' : 'Disabled'}
                          size="small"
                          color={user.mfa_enabled ? 'success' : 'default'}
                          sx={{ fontWeight: 500 }}
                        />
                      </TableCell>
                      <TableCell>
                        {user.mfa_method ? (
                          <Chip
                            icon={user.mfa_method === 'email' ? <EmailIcon /> : <PhoneIcon />}
                            label={user.mfa_method === 'email' ? 'Email' : 'Authenticator App'}
                            size="small"
                            variant="outlined"
                            sx={{ fontWeight: 500 }}
                          />
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            -
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Button
                          size="small"
                          variant="outlined"
                          color={user.mfa_enabled ? 'error' : 'primary'}
                          onClick={() => handleToggleUserMfa(user)}
                          startIcon={user.mfa_enabled ? <CloseIcon /> : <KeyIcon />}
                        >
                          {user.mfa_enabled ? 'Disable' : 'Enable'}
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Paper>
        </Grid>
      </Grid>

      {/* Confirmation Dialog */}
      <Dialog
        open={openDialog}
        onClose={() => setOpenDialog(false)}
      >
        <DialogTitle>
          {selectedUser?.mfa_enabled ? 'Disable' : 'Enable'} MFA for {selectedUser?.username}
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            {selectedUser?.mfa_enabled
              ? `Are you sure you want to disable Multi-Factor Authentication for ${selectedUser?.username}? This will reduce the security of their account.`
              : `Are you sure you want to enable Multi-Factor Authentication for ${selectedUser?.username}? They will need to set it up on their next login.`}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)} color="inherit">
            Cancel
          </Button>
          <Button
            onClick={handleConfirmToggleMfa}
            color={selectedUser?.mfa_enabled ? 'error' : 'primary'}
            variant="contained"
            disabled={saving}
            startIcon={saving ? <CircularProgress size={20} /> : null}
          >
            {selectedUser?.mfa_enabled ? 'Disable MFA' : 'Enable MFA'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MfaManagement;
