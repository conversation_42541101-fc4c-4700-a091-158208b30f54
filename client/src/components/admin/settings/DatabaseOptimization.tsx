import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Paper,
  <PERSON>ton,
  Card,
  CardContent,
  CardActions,
  Grid,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Tooltip,
  Divider,
  LinearProgress,
  Snackbar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Checkbox,
  IconButton,
  SelectChangeEvent
} from '@mui/material';
import {
  Storage as StorageIcon,
  Speed as SpeedIcon,
  Tune as TuneIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Refresh as RefreshIcon,
  PlayArrow as PlayArrowIcon,
  History as HistoryIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import axios from 'axios';
import { API_URL } from '../../../config';

interface OptimizationHistory {
  optimization_id: number;
  optimization_type: string;
  tables_affected: string[];
  start_time: string;
  end_time: string | null;
  status: string;
  details: any;
  performance_impact: any;
  created_by_username: string;
}

interface DatabaseTable {
  table_name: string;
  row_count: number;
  size_bytes: number;
  last_vacuum: string | null;
  last_analyze: string | null;
  fragmentation: number;
}

const DatabaseOptimization: React.FC = () => {
  const [history, setHistory] = useState<OptimizationHistory[]>([]);
  const [tables, setTables] = useState<DatabaseTable[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [optimizing, setOptimizing] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const [openHistoryDialog, setOpenHistoryDialog] = useState<boolean>(false);
  const [selectedTables, setSelectedTables] = useState<string[]>([]);
  const [optimizationType, setOptimizationType] = useState<string>('vacuum');
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'info' | 'warning'
  });

  // Fetch optimization history and database tables on component mount
  useEffect(() => {
    fetchData();
  }, []);

  // Fetch data from API
  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('token');

      // Fetch optimization history
      const historyResponse = await axios.get(`${API_URL}/api/admin/db-optimization/history`, {
        headers: {
          'x-auth-token': token
        }
      });
      setHistory(historyResponse.data);

      // Fetch database tables
      const tablesResponse = await axios.get(`${API_URL}/api/admin/db-optimization/tables`, {
        headers: {
          'x-auth-token': token
        }
      });
      setTables(tablesResponse.data);
    } catch (err) {
      console.error('Error fetching database optimization data:', err);
      setError('Failed to load database optimization data');

      // For demo purposes, set some mock data
      setHistory([
        {
          optimization_id: 1,
          optimization_type: 'vacuum',
          tables_affected: ['patients', 'doctors', 'users'],
          start_time: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          end_time: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000 + 10 * 60 * 1000).toISOString(),
          status: 'completed',
          details: { rows_processed: 15000 },
          performance_impact: { before: { query_time_ms: 250 }, after: { query_time_ms: 120 } },
          created_by_username: 'admin'
        },
        {
          optimization_id: 2,
          optimization_type: 'analyze',
          tables_affected: ['patient_visits', 'medical_records'],
          start_time: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          end_time: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 5 * 60 * 1000).toISOString(),
          status: 'completed',
          details: { statistics_updated: true },
          performance_impact: { before: { query_time_ms: 180 }, after: { query_time_ms: 90 } },
          created_by_username: 'admin'
        }
      ]);

      setTables([
        {
          table_name: 'patients',
          row_count: 5000,
          size_bytes: 25000000,
          last_vacuum: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          last_analyze: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          fragmentation: 15
        },
        {
          table_name: 'doctors',
          row_count: 200,
          size_bytes: 1500000,
          last_vacuum: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          last_analyze: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          fragmentation: 5
        },
        {
          table_name: 'patient_visits',
          row_count: 15000,
          size_bytes: 75000000,
          last_vacuum: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          last_analyze: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          fragmentation: 25
        },
        {
          table_name: 'users',
          row_count: 5500,
          size_bytes: 12000000,
          last_vacuum: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
          last_analyze: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
          fragmentation: 30
        },
        {
          table_name: 'medical_records',
          row_count: 20000,
          size_bytes: 100000000,
          last_vacuum: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
          last_analyze: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
          fragmentation: 40
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  // Handle dialog open for optimization
  const handleOptimizeClick = () => {
    setSelectedTables([]);
    setOptimizationType('vacuum');
    setOpenDialog(true);
  };

  // Handle dialog open for history
  const handleHistoryClick = () => {
    setOpenHistoryDialog(true);
  };

  // Handle table selection
  const handleTableToggle = (tableName: string) => {
    const currentIndex = selectedTables.indexOf(tableName);
    const newSelectedTables = [...selectedTables];

    if (currentIndex === -1) {
      newSelectedTables.push(tableName);
    } else {
      newSelectedTables.splice(currentIndex, 1);
    }

    setSelectedTables(newSelectedTables);
  };

  // Handle select all tables
  const handleSelectAllTables = () => {
    if (selectedTables.length === tables.length) {
      setSelectedTables([]);
    } else {
      setSelectedTables(tables.map(table => table.table_name));
    }
  };

  // Handle optimization type change
  const handleOptimizationTypeChange = (event: SelectChangeEvent) => {
    setOptimizationType(event.target.value);
  };

  // Run database optimization
  const handleRunOptimization = async () => {
    if (selectedTables.length === 0) {
      setSnackbar({
        open: true,
        message: 'Please select at least one table to optimize',
        severity: 'warning'
      });
      return;
    }

    setOptimizing(true);
    try {
      const token = localStorage.getItem('token');
      await axios.post(
        `${API_URL}/api/admin/db-optimization/run`,
        {
          optimization_type: optimizationType,
          tables: selectedTables
        },
        {
          headers: {
            'x-auth-token': token
          }
        }
      );

      setSnackbar({
        open: true,
        message: 'Database optimization started successfully',
        severity: 'success'
      });

      setOpenDialog(false);
      fetchData();
    } catch (err) {
      console.error('Error running database optimization:', err);
      setSnackbar({
        open: true,
        message: `Error: ${err instanceof Error ? err.message : 'Failed to run database optimization'}`,
        severity: 'error'
      });

      // For demo purposes, simulate success
      setOpenDialog(false);
      setTimeout(() => fetchData(), 1000);
    } finally {
      setOptimizing(false);
    }
  };

  // Format file size for display
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format date for display
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  // Get fragmentation level color
  const getFragmentationColor = (level: number) => {
    if (level < 10) return 'success';
    if (level < 25) return 'info';
    if (level < 40) return 'warning';
    return 'error';
  };

  // Get optimization type display name
  const getOptimizationTypeDisplay = (type: string) => {
    switch (type) {
      case 'vacuum':
        return 'VACUUM (Reclaim Space)';
      case 'analyze':
        return 'ANALYZE (Update Statistics)';
      case 'reindex':
        return 'REINDEX (Rebuild Indexes)';
      case 'vacuum_analyze':
        return 'VACUUM ANALYZE (Reclaim Space & Update Statistics)';
      default:
        return type.charAt(0).toUpperCase() + type.slice(1);
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6" component="h2">
          Database Optimization
        </Typography>
        <Box>
          <Button
            variant="outlined"
            color="primary"
            startIcon={<HistoryIcon />}
            onClick={handleHistoryClick}
            sx={{ borderRadius: 2, mr: 2 }}
          >
            Optimization History
          </Button>
          <Button
            variant="contained"
            color="primary"
            startIcon={<TuneIcon />}
            onClick={handleOptimizeClick}
            sx={{ borderRadius: 2 }}
          >
            Optimize Database
          </Button>
        </Box>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Paper sx={{ p: 3, borderRadius: 2 }}>
              <Typography variant="h6" gutterBottom>
                Database Tables
              </Typography>
              <Typography variant="body2" color="text.secondary" paragraph>
                The table below shows the current state of your database tables. Tables with high fragmentation levels may benefit from optimization.
              </Typography>

              <Box sx={{ overflowX: 'auto' }}>
                <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                  <thead>
                    <tr>
                      <th style={{ textAlign: 'left', padding: '12px 16px', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>Table Name</th>
                      <th style={{ textAlign: 'right', padding: '12px 16px', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>Rows</th>
                      <th style={{ textAlign: 'right', padding: '12px 16px', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>Size</th>
                      <th style={{ textAlign: 'right', padding: '12px 16px', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>Fragmentation</th>
                      <th style={{ textAlign: 'left', padding: '12px 16px', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>Last VACUUM</th>
                      <th style={{ textAlign: 'left', padding: '12px 16px', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>Last ANALYZE</th>
                    </tr>
                  </thead>
                  <tbody>
                    {tables.map((table) => (
                      <tr key={table.table_name}>
                        <td style={{ padding: '12px 16px', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>
                          <Typography variant="body2" fontWeight="500">
                            {table.table_name}
                          </Typography>
                        </td>
                        <td style={{ textAlign: 'right', padding: '12px 16px', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>
                          {table.row_count.toLocaleString()}
                        </td>
                        <td style={{ textAlign: 'right', padding: '12px 16px', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>
                          {formatFileSize(table.size_bytes)}
                        </td>
                        <td style={{ textAlign: 'right', padding: '12px 16px', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>
                          <Chip
                            size="small"
                            label={`${table.fragmentation}%`}
                            color={getFragmentationColor(table.fragmentation) as any}
                          />
                        </td>
                        <td style={{ padding: '12px 16px', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>
                          {formatDate(table.last_vacuum)}
                        </td>
                        <td style={{ padding: '12px 16px', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>
                          {formatDate(table.last_analyze)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </Box>
            </Paper>
          </Grid>
        </Grid>
      )}

      {/* Optimization Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Optimize Database</DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 3 }}>
            Database optimization can improve performance but may temporarily increase server load. Consider scheduling during off-peak hours.
          </Alert>

          <FormControl fullWidth margin="normal" variant="outlined" sx={{ mb: 3 }}>
            <InputLabel>Optimization Type</InputLabel>
            <Select
              value={optimizationType}
              onChange={handleOptimizationTypeChange}
              label="Optimization Type"
            >
              <MenuItem value="vacuum">VACUUM (Reclaim Space)</MenuItem>
              <MenuItem value="analyze">ANALYZE (Update Statistics)</MenuItem>
              <MenuItem value="reindex">REINDEX (Rebuild Indexes)</MenuItem>
              <MenuItem value="vacuum_analyze">VACUUM ANALYZE (Reclaim Space & Update Statistics)</MenuItem>
            </Select>
          </FormControl>

          <Typography variant="subtitle1" gutterBottom>
            Select Tables to Optimize
          </Typography>

          <Paper variant="outlined" sx={{ maxHeight: 300, overflow: 'auto', mb: 2 }}>
            <List dense>
              <ListItem onClick={handleSelectAllTables}>
                <ListItemIcon>
                  <Checkbox
                    edge="start"
                    checked={selectedTables.length === tables.length && tables.length > 0}
                    indeterminate={selectedTables.length > 0 && selectedTables.length < tables.length}
                    tabIndex={-1}
                    disableRipple
                  />
                </ListItemIcon>
                <ListItemText primary="Select All Tables" />
              </ListItem>
              <Divider />
              {tables.map((table) => (
                <ListItem key={table.table_name} onClick={() => handleTableToggle(table.table_name)}>
                  <ListItemIcon>
                    <Checkbox
                      edge="start"
                      checked={selectedTables.indexOf(table.table_name) !== -1}
                      tabIndex={-1}
                      disableRipple
                    />
                  </ListItemIcon>
                  <ListItemText
                    primary={table.table_name}
                    secondary={`${table.row_count.toLocaleString()} rows, ${formatFileSize(table.size_bytes)}, ${table.fragmentation}% fragmentation`}
                  />
                </ListItem>
              ))}
            </List>
          </Paper>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button onClick={() => setOpenDialog(false)} color="inherit">
            Cancel
          </Button>
          <Button
            onClick={handleRunOptimization}
            variant="contained"
            color="primary"
            startIcon={<PlayArrowIcon />}
            disabled={optimizing || selectedTables.length === 0}
          >
            {optimizing ? 'Running...' : 'Run Optimization'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* History Dialog */}
      <Dialog open={openHistoryDialog} onClose={() => setOpenHistoryDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Optimization History</DialogTitle>
        <DialogContent>
          {history.length === 0 ? (
            <Typography variant="body1" color="text.secondary" align="center" sx={{ py: 4 }}>
              No optimization history found
            </Typography>
          ) : (
            <Box sx={{ overflowX: 'auto' }}>
              <table style={{ width: '100%', borderCollapse: 'collapse' }}>
                <thead>
                  <tr>
                    <th style={{ textAlign: 'left', padding: '12px 16px', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>Type</th>
                    <th style={{ textAlign: 'left', padding: '12px 16px', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>Tables</th>
                    <th style={{ textAlign: 'left', padding: '12px 16px', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>Started</th>
                    <th style={{ textAlign: 'left', padding: '12px 16px', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>Duration</th>
                    <th style={{ textAlign: 'left', padding: '12px 16px', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>Status</th>
                    <th style={{ textAlign: 'left', padding: '12px 16px', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>Performance</th>
                  </tr>
                </thead>
                <tbody>
                  {history.map((item) => {
                    // Calculate duration
                    let duration = 'In progress';
                    if (item.end_time) {
                      const start = new Date(item.start_time).getTime();
                      const end = new Date(item.end_time).getTime();
                      const durationMs = end - start;
                      const minutes = Math.floor(durationMs / 60000);
                      const seconds = Math.floor((durationMs % 60000) / 1000);
                      duration = `${minutes}m ${seconds}s`;
                    }

                    // Calculate performance improvement if available
                    let performanceText = 'N/A';
                    if (item.performance_impact &&
                        item.performance_impact.before &&
                        item.performance_impact.after &&
                        item.performance_impact.before.query_time_ms &&
                        item.performance_impact.after.query_time_ms) {
                      const before = item.performance_impact.before.query_time_ms;
                      const after = item.performance_impact.after.query_time_ms;
                      const improvement = ((before - after) / before * 100).toFixed(1);
                      performanceText = `${improvement}% faster`;
                    }

                    return (
                      <tr key={item.optimization_id}>
                        <td style={{ padding: '12px 16px', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>
                          {getOptimizationTypeDisplay(item.optimization_type)}
                        </td>
                        <td style={{ padding: '12px 16px', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>
                          {item.tables_affected.length > 3
                            ? `${item.tables_affected.slice(0, 3).join(', ')} +${item.tables_affected.length - 3} more`
                            : item.tables_affected.join(', ')}
                        </td>
                        <td style={{ padding: '12px 16px', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>
                          {formatDate(item.start_time)}
                        </td>
                        <td style={{ padding: '12px 16px', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>
                          {duration}
                        </td>
                        <td style={{ padding: '12px 16px', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>
                          <Chip
                            size="small"
                            label={item.status}
                            color={item.status === 'completed' ? 'success' : item.status === 'in_progress' ? 'info' : 'error'}
                          />
                        </td>
                        <td style={{ padding: '12px 16px', borderBottom: '1px solid rgba(224, 224, 224, 1)' }}>
                          {performanceText}
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button onClick={() => setOpenHistoryDialog(false)} color="primary">
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default DatabaseOptimization;
