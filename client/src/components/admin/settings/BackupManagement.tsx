import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Tooltip,
  Divider,
  Grid,
  Snackbar,
  SelectChangeEvent
} from '@mui/material';
import {
  Backup as BackupIcon,
  RestoreOutlined as RestoreIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  Schedule as ScheduleIcon,
  Info as InfoIcon,
  Check as CheckIcon,
  Error as ErrorIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import axios from 'axios';
import { API_URL } from '../../../config';

interface BackupHistoryItem {
  backup_id: number;
  backup_name: string;
  backup_path: string;
  backup_size: number;
  backup_type: string;
  status: string;
  is_encrypted: boolean;
  retention_date: string | null;
  notes: string | null;
  created_at: string;
  created_by_username: string;
}

interface BackupManagementProps {
  onBackupCreated?: () => void;
  onBackupRestored?: () => void;
}

const BackupManagement: React.FC<BackupManagementProps> = ({
  onBackupCreated,
  onBackupRestored
}) => {
  const [backups, setBackups] = useState<BackupHistoryItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [creatingBackup, setCreatingBackup] = useState<boolean>(false);
  const [restoringBackup, setRestoringBackup] = useState<boolean>(false);
  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const [selectedBackup, setSelectedBackup] = useState<BackupHistoryItem | null>(null);
  const [formData, setFormData] = useState({
    backup_name: '',
    backup_type: 'manual',
    is_encrypted: false,
    retention_days: 30,
    notes: ''
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'info' | 'warning'
  });

  // Fetch backup history on component mount
  useEffect(() => {
    fetchBackupHistory();
  }, []);

  // Fetch backup history from API
  const fetchBackupHistory = async () => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/admin/backup-history`, {
        headers: {
          'x-auth-token': token
        }
      });
      setBackups(response.data);
    } catch (err) {
      console.error('Error fetching backup history:', err);
      setError('Failed to load backup history');
    } finally {
      setLoading(false);
    }
  };

  // Handle dialog open for creating a new backup
  const handleCreateBackupClick = () => {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').substring(0, 19);
    setFormData({
      backup_name: `backup_${timestamp}`,
      backup_type: 'manual',
      is_encrypted: false,
      retention_days: 30,
      notes: ''
    });
    setOpenDialog(true);
  };

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked, type } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle select changes
  const handleSelectChange = (e: SelectChangeEvent) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name as string]: value
    });
  };

  // Create a new backup
  const handleCreateBackup = async () => {
    setCreatingBackup(true);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(
        `${API_URL}/api/admin/backups`,
        {
          backup_name: formData.backup_name,
          backup_type: formData.backup_type,
          is_encrypted: formData.is_encrypted,
          retention_days: formData.retention_days,
          notes: formData.notes
        },
        {
          headers: {
            'x-auth-token': token
          }
        }
      );

      setSnackbar({
        open: true,
        message: 'Backup created successfully',
        severity: 'success'
      });

      setOpenDialog(false);
      fetchBackupHistory();

      if (onBackupCreated) {
        onBackupCreated();
      }
    } catch (err) {
      console.error('Error creating backup:', err);
      setSnackbar({
        open: true,
        message: `Error: ${err instanceof Error ? err.message : 'Failed to create backup'}`,
        severity: 'error'
      });
    } finally {
      setCreatingBackup(false);
    }
  };

  // Restore from a backup
  const handleRestoreBackup = async (backupId: number) => {
    if (!window.confirm('Are you sure you want to restore from this backup? This will overwrite your current database.')) {
      return;
    }

    setRestoringBackup(true);
    try {
      const token = localStorage.getItem('token');
      await axios.post(
        `${API_URL}/api/admin/backups/${backupId}/restore`,
        {},
        {
          headers: {
            'x-auth-token': token
          }
        }
      );

      setSnackbar({
        open: true,
        message: 'Database restored successfully',
        severity: 'success'
      });

      if (onBackupRestored) {
        onBackupRestored();
      }
    } catch (err) {
      console.error('Error restoring backup:', err);
      setSnackbar({
        open: true,
        message: `Error: ${err instanceof Error ? err.message : 'Failed to restore backup'}`,
        severity: 'error'
      });
    } finally {
      setRestoringBackup(false);
    }
  };

  // Delete a backup
  const handleDeleteBackup = async (backupId: number) => {
    if (!window.confirm('Are you sure you want to delete this backup? This action cannot be undone.')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      await axios.delete(`${API_URL}/api/admin/backups/${backupId}`, {
        headers: {
          'x-auth-token': token
        }
      });

      setSnackbar({
        open: true,
        message: 'Backup deleted successfully',
        severity: 'success'
      });

      fetchBackupHistory();
    } catch (err) {
      console.error('Error deleting backup:', err);
      setSnackbar({
        open: true,
        message: `Error: ${err instanceof Error ? err.message : 'Failed to delete backup'}`,
        severity: 'error'
      });
    }
  };

  // Format file size for display
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  // Get status chip color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'in_progress':
        return 'info';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckIcon fontSize="small" />;
      case 'in_progress':
        return <CircularProgress size={16} />;
      case 'failed':
        return <ErrorIcon fontSize="small" />;
      default:
        return <InfoIcon fontSize="small" />;
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6" component="h2">
          Backup History
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<BackupIcon />}
          onClick={handleCreateBackupClick}
          disabled={creatingBackup}
          sx={{ borderRadius: 2 }}
        >
          {creatingBackup ? 'Creating Backup...' : 'Create New Backup'}
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : backups.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center', borderRadius: 2 }}>
          <Typography variant="body1" color="text.secondary">
            No backup history found. Create your first backup to get started.
          </Typography>
        </Paper>
      ) : (
        <TableContainer component={Paper} sx={{ borderRadius: 2, mb: 4 }}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Backup Name</TableCell>
                <TableCell>Created</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Size</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {backups.map((backup) => (
                <TableRow key={backup.backup_id}>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <BackupIcon sx={{ mr: 1, color: 'primary.main' }} />
                      <Typography variant="body2" fontWeight="500">
                        {backup.backup_name}
                      </Typography>
                    </Box>
                    {backup.notes && (
                      <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
                        {backup.notes}
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">{formatDate(backup.created_at)}</Typography>
                    <Typography variant="caption" color="text.secondary">
                      by {backup.created_by_username}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Chip
                      size="small"
                      label={backup.backup_type}
                      color={backup.backup_type === 'auto' ? 'primary' : 'default'}
                      variant={backup.backup_type === 'manual' ? 'outlined' : 'filled'}
                    />
                  </TableCell>
                  <TableCell>{formatFileSize(backup.backup_size)}</TableCell>
                  <TableCell>
                    <Chip
                      size="small"
                      icon={getStatusIcon(backup.status)}
                      label={backup.status}
                      color={getStatusColor(backup.status) as any}
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex' }}>
                      <Tooltip title="Restore from this backup">
                        <IconButton
                          color="primary"
                          onClick={() => handleRestoreBackup(backup.backup_id)}
                          disabled={backup.status !== 'completed' || restoringBackup}
                          size="small"
                        >
                          <RestoreIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete backup">
                        <IconButton
                          color="error"
                          onClick={() => handleDeleteBackup(backup.backup_id)}
                          size="small"
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Create Backup Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Create New Backup</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            name="backup_name"
            label="Backup Name"
            type="text"
            fullWidth
            variant="outlined"
            value={formData.backup_name}
            onChange={handleChange}
            required
            sx={{ mb: 2, mt: 1 }}
          />
          <FormControl fullWidth margin="dense" variant="outlined" sx={{ mb: 2 }}>
            <InputLabel>Backup Type</InputLabel>
            <Select
              name="backup_type"
              value={formData.backup_type}
              label="Backup Type"
              onChange={handleSelectChange}
            >
              <MenuItem value="manual">Manual</MenuItem>
              <MenuItem value="scheduled">Scheduled</MenuItem>
            </Select>
          </FormControl>
          <TextField
            margin="dense"
            name="retention_days"
            label="Retention Period (days)"
            type="number"
            fullWidth
            variant="outlined"
            value={formData.retention_days}
            onChange={handleChange}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            name="notes"
            label="Notes"
            type="text"
            fullWidth
            variant="outlined"
            value={formData.notes}
            onChange={handleChange}
            multiline
            rows={3}
            sx={{ mb: 2 }}
          />
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button onClick={() => setOpenDialog(false)} color="inherit">
            Cancel
          </Button>
          <Button
            onClick={handleCreateBackup}
            variant="contained"
            color="primary"
            startIcon={<BackupIcon />}
            disabled={creatingBackup || !formData.backup_name}
          >
            {creatingBackup ? 'Creating...' : 'Create Backup'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default BackupManagement;
