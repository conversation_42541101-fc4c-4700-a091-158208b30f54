import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Tooltip,
  Divider,
  Grid,
  Snackbar,
  Switch,
  FormControlLabel,
  SelectChangeEvent
} from '@mui/material';
import {
  Schedule as ScheduleIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Check as CheckIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Notifications as NotificationsIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import axios from 'axios';
import { API_URL } from '../../../config';

interface MaintenanceSchedule {
  schedule_id: number;
  schedule_name: string;
  start_time: string;
  end_time: string;
  is_recurring: boolean;
  recurrence_pattern: string | null;
  recurrence_config: any;
  notification_minutes: number;
  status: string;
  notes: string | null;
  created_at: string;
  updated_at: string;
  created_by_username: string;
}

const MaintenanceScheduler: React.FC = () => {
  const [schedules, setSchedules] = useState<MaintenanceSchedule[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const [selectedSchedule, setSelectedSchedule] = useState<MaintenanceSchedule | null>(null);
  const [formData, setFormData] = useState({
    schedule_name: '',
    start_time: '',
    end_time: '',
    is_recurring: false,
    recurrence_pattern: 'none',
    notification_minutes: 60,
    notes: ''
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'info' | 'warning'
  });

  // Fetch maintenance schedules on component mount
  useEffect(() => {
    fetchSchedules();
  }, []);

  // Fetch maintenance schedules from API
  const fetchSchedules = async () => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/admin/maintenance-schedules`, {
        headers: {
          'x-auth-token': token
        }
      });
      setSchedules(response.data);
    } catch (err) {
      console.error('Error fetching maintenance schedules:', err);
      setError('Failed to load maintenance schedules');

      // For demo purposes, set some mock data
      setSchedules([
        {
          schedule_id: 1,
          schedule_name: 'Weekly Database Maintenance',
          start_time: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          end_time: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000).toISOString(),
          is_recurring: true,
          recurrence_pattern: 'weekly',
          recurrence_config: { day: 'Sunday' },
          notification_minutes: 60,
          status: 'scheduled',
          notes: 'Regular weekly maintenance for database optimization',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          created_by_username: 'admin'
        },
        {
          schedule_id: 2,
          schedule_name: 'Emergency Patch Deployment',
          start_time: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
          end_time: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000 + 1 * 60 * 60 * 1000).toISOString(),
          is_recurring: false,
          recurrence_pattern: null,
          recurrence_config: null,
          notification_minutes: 120,
          status: 'scheduled',
          notes: 'Security patch deployment',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          created_by_username: 'admin'
        }
      ]);
    } finally {
      setLoading(false);
    }
  };

  // Handle dialog open for creating a new schedule
  const handleCreateScheduleClick = () => {
    setSelectedSchedule(null);

    // Set default start time to tomorrow at 2:00 AM
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(2, 0, 0, 0);

    // Set default end time to tomorrow at 4:00 AM
    const tomorrowEnd = new Date(tomorrow);
    tomorrowEnd.setHours(4, 0, 0, 0);

    setFormData({
      schedule_name: 'Maintenance Window',
      start_time: tomorrow.toISOString().substring(0, 16),
      end_time: tomorrowEnd.toISOString().substring(0, 16),
      is_recurring: false,
      recurrence_pattern: 'none',
      notification_minutes: 60,
      notes: ''
    });
    setOpenDialog(true);
  };

  // Handle dialog open for editing an existing schedule
  const handleEditSchedule = (schedule: MaintenanceSchedule) => {
    setSelectedSchedule(schedule);
    setFormData({
      schedule_name: schedule.schedule_name,
      start_time: new Date(schedule.start_time).toISOString().substring(0, 16),
      end_time: new Date(schedule.end_time).toISOString().substring(0, 16),
      is_recurring: schedule.is_recurring,
      recurrence_pattern: schedule.recurrence_pattern || 'none',
      notification_minutes: schedule.notification_minutes,
      notes: schedule.notes || ''
    });
    setOpenDialog(true);
  };

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, checked, type } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Handle select changes
  const handleSelectChange = (e: SelectChangeEvent) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name as string]: value
    });
  };

  // Save maintenance schedule
  const handleSaveSchedule = async () => {
    try {
      const token = localStorage.getItem('token');

      const scheduleData = {
        ...formData,
        recurrence_config: formData.recurrence_pattern === 'none' ? null : {
          day: formData.recurrence_pattern === 'weekly' ? 'Sunday' : null,
          date: formData.recurrence_pattern === 'monthly' ? 1 : null
        }
      };

      if (selectedSchedule) {
        // Update existing schedule
        await axios.put(
          `${API_URL}/api/admin/maintenance-schedules/${selectedSchedule.schedule_id}`,
          scheduleData,
          {
            headers: {
              'x-auth-token': token
            }
          }
        );

        setSnackbar({
          open: true,
          message: 'Maintenance schedule updated successfully',
          severity: 'success'
        });
      } else {
        // Create new schedule
        await axios.post(
          `${API_URL}/api/admin/maintenance-schedules`,
          scheduleData,
          {
            headers: {
              'x-auth-token': token
            }
          }
        );

        setSnackbar({
          open: true,
          message: 'Maintenance schedule created successfully',
          severity: 'success'
        });
      }

      setOpenDialog(false);
      fetchSchedules();
    } catch (err) {
      console.error('Error saving maintenance schedule:', err);
      setSnackbar({
        open: true,
        message: `Error: ${err instanceof Error ? err.message : 'Failed to save maintenance schedule'}`,
        severity: 'error'
      });

      // For demo purposes, simulate success
      setOpenDialog(false);
      fetchSchedules();
    }
  };

  // Cancel a scheduled maintenance
  const handleCancelSchedule = async (scheduleId: number) => {
    if (!window.confirm('Are you sure you want to cancel this maintenance window?')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      await axios.post(
        `${API_URL}/api/admin/maintenance-schedules/${scheduleId}/cancel`,
        {},
        {
          headers: {
            'x-auth-token': token
          }
        }
      );

      setSnackbar({
        open: true,
        message: 'Maintenance schedule cancelled successfully',
        severity: 'success'
      });

      fetchSchedules();
    } catch (err) {
      console.error('Error cancelling maintenance schedule:', err);
      setSnackbar({
        open: true,
        message: `Error: ${err instanceof Error ? err.message : 'Failed to cancel maintenance schedule'}`,
        severity: 'error'
      });

      // For demo purposes, simulate success
      fetchSchedules();
    }
  };

  // Delete a maintenance schedule
  const handleDeleteSchedule = async (scheduleId: number) => {
    if (!window.confirm('Are you sure you want to delete this maintenance schedule? This action cannot be undone.')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      await axios.delete(`${API_URL}/api/admin/maintenance-schedules/${scheduleId}`, {
        headers: {
          'x-auth-token': token
        }
      });

      setSnackbar({
        open: true,
        message: 'Maintenance schedule deleted successfully',
        severity: 'success'
      });

      fetchSchedules();
    } catch (err) {
      console.error('Error deleting maintenance schedule:', err);
      setSnackbar({
        open: true,
        message: `Error: ${err instanceof Error ? err.message : 'Failed to delete maintenance schedule'}`,
        severity: 'error'
      });

      // For demo purposes, simulate success
      fetchSchedules();
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  // Get status chip color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'success';
      case 'in_progress':
        return 'warning';
      case 'scheduled':
        return 'info';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6" component="h2">
          Maintenance Schedule
        </Typography>
        <Button
          variant="contained"
          color="primary"
          startIcon={<ScheduleIcon />}
          onClick={handleCreateScheduleClick}
          sx={{ borderRadius: 2 }}
        >
          Schedule Maintenance
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : schedules.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center', borderRadius: 2 }}>
          <Typography variant="body1" color="text.secondary">
            No maintenance schedules found. Create your first maintenance window to get started.
          </Typography>
        </Paper>
      ) : (
        <TableContainer component={Paper} sx={{ borderRadius: 2, mb: 4 }}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Maintenance Name</TableCell>
                <TableCell>Start Time</TableCell>
                <TableCell>End Time</TableCell>
                <TableCell>Recurring</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {schedules.map((schedule) => (
                <TableRow key={schedule.schedule_id}>
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <ScheduleIcon sx={{ mr: 1, color: 'primary.main' }} />
                      <Typography variant="body2" fontWeight="500">
                        {schedule.schedule_name}
                      </Typography>
                    </Box>
                    {schedule.notes && (
                      <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
                        {schedule.notes}
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>{formatDate(schedule.start_time)}</TableCell>
                  <TableCell>{formatDate(schedule.end_time)}</TableCell>
                  <TableCell>
                    {schedule.is_recurring ? (
                      <Chip
                        size="small"
                        label={schedule.recurrence_pattern || 'Custom'}
                        color="primary"
                      />
                    ) : (
                      <Typography variant="body2">One-time</Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    <Chip
                      size="small"
                      label={schedule.status}
                      color={getStatusColor(schedule.status) as any}
                    />
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex' }}>
                      <Tooltip title="Edit schedule">
                        <IconButton
                          color="primary"
                          onClick={() => handleEditSchedule(schedule)}
                          disabled={schedule.status === 'completed' || schedule.status === 'cancelled'}
                          size="small"
                        >
                          <EditIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      {schedule.status === 'scheduled' && (
                        <Tooltip title="Cancel maintenance">
                          <IconButton
                            color="warning"
                            onClick={() => handleCancelSchedule(schedule.schedule_id)}
                            size="small"
                          >
                            <CancelIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                      <Tooltip title="Delete schedule">
                        <IconButton
                          color="error"
                          onClick={() => handleDeleteSchedule(schedule.schedule_id)}
                          size="small"
                        >
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Create/Edit Schedule Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>{selectedSchedule ? 'Edit Maintenance Schedule' : 'Schedule New Maintenance'}</DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 0 }}>
            <Grid item xs={12}>
              <TextField
                autoFocus
                margin="dense"
                name="schedule_name"
                label="Maintenance Name"
                type="text"
                fullWidth
                variant="outlined"
                value={formData.schedule_name}
                onChange={handleChange}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="dense"
                name="start_time"
                label="Start Time"
                type="datetime-local"
                fullWidth
                variant="outlined"
                value={formData.start_time}
                onChange={handleChange}
                required
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                margin="dense"
                name="end_time"
                label="End Time"
                type="datetime-local"
                fullWidth
                variant="outlined"
                value={formData.end_time}
                onChange={handleChange}
                required
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    name="is_recurring"
                    checked={formData.is_recurring}
                    onChange={handleChange}
                    color="primary"
                  />
                }
                label="Recurring Maintenance"
              />
            </Grid>
            {formData.is_recurring && (
              <Grid item xs={12} md={6}>
                <FormControl fullWidth margin="dense" variant="outlined">
                  <InputLabel>Recurrence Pattern</InputLabel>
                  <Select
                    name="recurrence_pattern"
                    value={formData.recurrence_pattern}
                    label="Recurrence Pattern"
                    onChange={handleSelectChange}
                  >
                    <MenuItem value="none">None</MenuItem>
                    <MenuItem value="daily">Daily</MenuItem>
                    <MenuItem value="weekly">Weekly</MenuItem>
                    <MenuItem value="monthly">Monthly</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            )}
            <Grid item xs={12} md={6}>
              <TextField
                margin="dense"
                name="notification_minutes"
                label="Notification Time (minutes before)"
                type="number"
                fullWidth
                variant="outlined"
                value={formData.notification_minutes}
                onChange={handleChange}
                InputProps={{
                  endAdornment: <NotificationsIcon color="action" />
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                margin="dense"
                name="notes"
                label="Notes"
                type="text"
                fullWidth
                variant="outlined"
                value={formData.notes}
                onChange={handleChange}
                multiline
                rows={3}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button onClick={() => setOpenDialog(false)} color="inherit">
            Cancel
          </Button>
          <Button
            onClick={handleSaveSchedule}
            variant="contained"
            color="primary"
            startIcon={<ScheduleIcon />}
            disabled={!formData.schedule_name || !formData.start_time || !formData.end_time}
          >
            {selectedSchedule ? 'Update Schedule' : 'Create Schedule'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default MaintenanceScheduler;
