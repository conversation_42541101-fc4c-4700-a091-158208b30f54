import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Tooltip,
  Divider,
  Grid,
  Snackbar,
  InputAdornment,
  Tabs,
  Tab,
  TablePagination,
  SelectChangeEvent
} from '@mui/material';
import {
  Language as LanguageIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  Save as SaveIcon,
  Upload as UploadIcon,
  Download as DownloadIcon,
  Translate as TranslateIcon,
  Check as CheckIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import axios from 'axios';
import { API_URL } from '../../../config';

interface Translation {
  translation_id: number;
  language_code: string;
  key: string;
  value: string;
  context: string | null;
  created_at: string;
  updated_at: string;
}

interface Language {
  code: string;
  name: string;
  flag: string;
}

const LANGUAGES: Language[] = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'es', name: 'Spanish (Español)', flag: '🇪🇸' },
  { code: 'fr', name: 'French (Français)', flag: '🇫🇷' },
  { code: 'de', name: 'German (Deutsch)', flag: '🇩🇪' },
  { code: 'zh', name: 'Chinese (中文)', flag: '🇨🇳' },
  { code: 'ne', name: 'Nepali (नेपाली)', flag: '🇳🇵' }
];

const TranslationManager: React.FC = () => {
  const [translations, setTranslations] = useState<Translation[]>([]);
  const [filteredTranslations, setFilteredTranslations] = useState<Translation[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedLanguage, setSelectedLanguage] = useState<string>('en');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [openDialog, setOpenDialog] = useState<boolean>(false);
  const [editingTranslation, setEditingTranslation] = useState<Translation | null>(null);
  const [formData, setFormData] = useState({
    key: '',
    value: '',
    context: ''
  });
  const [snackbar, setSnackbar] = useState({
    open: false,
    message: '',
    severity: 'success' as 'success' | 'error' | 'info' | 'warning'
  });
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Fetch translations on component mount and when language changes
  useEffect(() => {
    fetchTranslations();
  }, [selectedLanguage]);

  // Filter translations when search query changes
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredTranslations(translations);
    } else {
      const query = searchQuery.toLowerCase();
      setFilteredTranslations(
        translations.filter(
          (translation) =>
            translation.key.toLowerCase().includes(query) ||
            translation.value.toLowerCase().includes(query) ||
            (translation.context && translation.context.toLowerCase().includes(query))
        )
      );
    }
  }, [searchQuery, translations]);

  // Fetch translations from API
  const fetchTranslations = async () => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/admin/translations/${selectedLanguage}`, {
        headers: {
          'x-auth-token': token
        }
      });
      setTranslations(response.data);
      setFilteredTranslations(response.data);
    } catch (err) {
      console.error('Error fetching translations:', err);
      setError('Failed to load translations');

      // For demo purposes, set some mock data
      const mockTranslations = [
        {
          translation_id: 1,
          language_code: selectedLanguage,
          key: 'common.save',
          value: selectedLanguage === 'en' ? 'Save' : 'Guardar',
          context: 'Used for save buttons',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          translation_id: 2,
          language_code: selectedLanguage,
          key: 'common.cancel',
          value: selectedLanguage === 'en' ? 'Cancel' : 'Cancelar',
          context: 'Used for cancel buttons',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          translation_id: 3,
          language_code: selectedLanguage,
          key: 'common.delete',
          value: selectedLanguage === 'en' ? 'Delete' : 'Eliminar',
          context: 'Used for delete buttons',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          translation_id: 4,
          language_code: selectedLanguage,
          key: 'nav.dashboard',
          value: selectedLanguage === 'en' ? 'Dashboard' : 'Panel de control',
          context: 'Main navigation item',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          translation_id: 5,
          language_code: selectedLanguage,
          key: 'nav.patients',
          value: selectedLanguage === 'en' ? 'Patients' : 'Pacientes',
          context: 'Main navigation item',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ];
      setTranslations(mockTranslations);
      setFilteredTranslations(mockTranslations);
    } finally {
      setLoading(false);
    }
  };

  // Handle language change
  const handleLanguageChange = (event: SelectChangeEvent) => {
    setSelectedLanguage(event.target.value);
  };

  // Handle search query change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  // Handle dialog open for creating a new translation
  const handleCreateTranslation = () => {
    setEditingTranslation(null);
    setFormData({
      key: '',
      value: '',
      context: ''
    });
    setOpenDialog(true);
  };

  // Handle dialog open for editing an existing translation
  const handleEditTranslation = (translation: Translation) => {
    setEditingTranslation(translation);
    setFormData({
      key: translation.key,
      value: translation.value,
      context: translation.context || ''
    });
    setOpenDialog(true);
  };

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Save translation
  const handleSaveTranslation = async () => {
    try {
      const token = localStorage.getItem('token');

      if (editingTranslation) {
        // Update existing translation
        await axios.put(
          `${API_URL}/api/admin/translations/${editingTranslation.translation_id}`,
          {
            ...formData,
            language_code: selectedLanguage
          },
          {
            headers: {
              'x-auth-token': token
            }
          }
        );

        setSnackbar({
          open: true,
          message: 'Translation updated successfully',
          severity: 'success'
        });
      } else {
        // Create new translation
        await axios.post(
          `${API_URL}/api/admin/translations`,
          {
            ...formData,
            language_code: selectedLanguage
          },
          {
            headers: {
              'x-auth-token': token
            }
          }
        );

        setSnackbar({
          open: true,
          message: 'Translation created successfully',
          severity: 'success'
        });
      }

      setOpenDialog(false);
      fetchTranslations();
    } catch (err) {
      console.error('Error saving translation:', err);
      setSnackbar({
        open: true,
        message: `Error: ${err instanceof Error ? err.message : 'Failed to save translation'}`,
        severity: 'error'
      });

      // For demo purposes, simulate success
      setOpenDialog(false);

      if (editingTranslation) {
        // Update in the local state
        setTranslations(prevTranslations =>
          prevTranslations.map(t =>
            t.translation_id === editingTranslation.translation_id
              ? { ...t, ...formData }
              : t
          )
        );
      } else {
        // Add to the local state
        const newTranslation = {
          translation_id: Math.max(0, ...translations.map(t => t.translation_id)) + 1,
          language_code: selectedLanguage,
          key: formData.key,
          value: formData.value,
          context: formData.context || null,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };
        setTranslations(prevTranslations => [...prevTranslations, newTranslation]);
      }
    }
  };

  // Delete translation
  const handleDeleteTranslation = async (translationId: number) => {
    if (!window.confirm('Are you sure you want to delete this translation? This action cannot be undone.')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      await axios.delete(`${API_URL}/api/admin/translations/${translationId}`, {
        headers: {
          'x-auth-token': token
        }
      });

      setSnackbar({
        open: true,
        message: 'Translation deleted successfully',
        severity: 'success'
      });

      fetchTranslations();
    } catch (err) {
      console.error('Error deleting translation:', err);
      setSnackbar({
        open: true,
        message: `Error: ${err instanceof Error ? err.message : 'Failed to delete translation'}`,
        severity: 'error'
      });

      // For demo purposes, simulate success
      setTranslations(prevTranslations =>
        prevTranslations.filter(t => t.translation_id !== translationId)
      );
    }
  };

  // Import translations from file
  const handleImportTranslations = () => {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.json';
    fileInput.onchange = async (e) => {
      const target = e.target as HTMLInputElement;
      if (!target.files || target.files.length === 0) return;

      const file = target.files[0];
      const reader = new FileReader();

      reader.onload = async (event) => {
        try {
          const content = event.target?.result as string;
          const translations = JSON.parse(content);

          const token = localStorage.getItem('token');
          await axios.post(
            `${API_URL}/api/admin/translations/import/${selectedLanguage}`,
            { translations },
            {
              headers: {
                'x-auth-token': token
              }
            }
          );

          setSnackbar({
            open: true,
            message: 'Translations imported successfully',
            severity: 'success'
          });

          fetchTranslations();
        } catch (err) {
          console.error('Error importing translations:', err);
          setSnackbar({
            open: true,
            message: `Error: ${err instanceof Error ? err.message : 'Failed to import translations'}`,
            severity: 'error'
          });
        }
      };

      reader.readAsText(file);
    };

    fileInput.click();
  };

  // Export translations to file
  const handleExportTranslations = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await axios.get(`${API_URL}/api/admin/translations/export/${selectedLanguage}`, {
        headers: {
          'x-auth-token': token
        }
      });

      const data = JSON.stringify(response.data, null, 2);
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = `translations_${selectedLanguage}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      setSnackbar({
        open: true,
        message: 'Translations exported successfully',
        severity: 'success'
      });
    } catch (err) {
      console.error('Error exporting translations:', err);
      setSnackbar({
        open: true,
        message: `Error: ${err instanceof Error ? err.message : 'Failed to export translations'}`,
        severity: 'error'
      });

      // For demo purposes, simulate success
      const mockExport: Record<string, string> = {};
      translations.forEach(t => {
        mockExport[t.key] = t.value;
      });

      const data = JSON.stringify(mockExport, null, 2);
      const blob = new Blob([data], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = `translations_${selectedLanguage}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  // Handle page change
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle snackbar close
  const handleSnackbarClose = () => {
    setSnackbar({ ...snackbar, open: false });
  };

  // Get language name from code
  const getLanguageName = (code: string) => {
    const language = LANGUAGES.find(lang => lang.code === code);
    return language ? language.name : code;
  };

  // Get language flag from code
  const getLanguageFlag = (code: string) => {
    const language = LANGUAGES.find(lang => lang.code === code);
    return language ? language.flag : '🌐';
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6" component="h2">
          Translation Manager
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<UploadIcon />}
            onClick={handleImportTranslations}
            sx={{ borderRadius: 2 }}
          >
            Import
          </Button>
          <Button
            variant="outlined"
            startIcon={<DownloadIcon />}
            onClick={handleExportTranslations}
            sx={{ borderRadius: 2 }}
          >
            Export
          </Button>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleCreateTranslation}
            sx={{ borderRadius: 2 }}
          >
            Add Translation
          </Button>
        </Box>
      </Box>

      <Paper sx={{ p: 2, mb: 3, borderRadius: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <FormControl fullWidth variant="outlined" size="small">
              <InputLabel>Language</InputLabel>
              <Select
                value={selectedLanguage}
                onChange={handleLanguageChange}
                label="Language"
              >
                {LANGUAGES.map((language) => (
                  <MenuItem key={language.code} value={language.code}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Box component="span" sx={{ mr: 1 }}>
                        {language.flag}
                      </Box>
                      {language.name}
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={8}>
            <TextField
              fullWidth
              variant="outlined"
              placeholder="Search translations..."
              value={searchQuery}
              onChange={handleSearchChange}
              size="small"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                ),
                endAdornment: searchQuery && (
                  <InputAdornment position="end">
                    <IconButton
                      size="small"
                      onClick={() => setSearchQuery('')}
                      edge="end"
                    >
                      <ClearIcon fontSize="small" />
                    </IconButton>
                  </InputAdornment>
                )
              }}
            />
          </Grid>
        </Grid>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      ) : filteredTranslations.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center', borderRadius: 2 }}>
          <Typography variant="body1" color="text.secondary">
            No translations found. {searchQuery ? 'Try a different search query.' : 'Add your first translation to get started.'}
          </Typography>
        </Paper>
      ) : (
        <Paper sx={{ borderRadius: 2, overflow: 'hidden' }}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Key</TableCell>
                  <TableCell>Value</TableCell>
                  <TableCell>Context</TableCell>
                  <TableCell>Last Updated</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {filteredTranslations
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((translation) => (
                    <TableRow key={translation.translation_id}>
                      <TableCell>
                        <Typography variant="body2" fontWeight="500">
                          {translation.key}
                        </Typography>
                      </TableCell>
                      <TableCell>{translation.value}</TableCell>
                      <TableCell>{translation.context || '-'}</TableCell>
                      <TableCell>
                        {new Date(translation.updated_at).toLocaleString()}
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex' }}>
                          <Tooltip title="Edit translation">
                            <IconButton
                              color="primary"
                              onClick={() => handleEditTranslation(translation)}
                              size="small"
                            >
                              <EditIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete translation">
                            <IconButton
                              color="error"
                              onClick={() => handleDeleteTranslation(translation.translation_id)}
                              size="small"
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </TableContainer>
          <TablePagination
            rowsPerPageOptions={[5, 10, 25, 50]}
            component="div"
            count={filteredTranslations.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </Paper>
      )}

      {/* Create/Edit Translation Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingTranslation ? 'Edit Translation' : 'Add New Translation'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={3} sx={{ mt: 0 }}>
            <Grid item xs={12}>
              <TextField
                autoFocus
                margin="dense"
                name="key"
                label="Translation Key"
                type="text"
                fullWidth
                variant="outlined"
                value={formData.key}
                onChange={handleChange}
                required
                disabled={!!editingTranslation}
                helperText="Unique identifier for this translation (e.g., 'common.save')"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                margin="dense"
                name="value"
                label={`Translation Value (${getLanguageName(selectedLanguage)})`}
                type="text"
                fullWidth
                variant="outlined"
                value={formData.value}
                onChange={handleChange}
                required
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      {getLanguageFlag(selectedLanguage)}
                    </InputAdornment>
                  )
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                margin="dense"
                name="context"
                label="Context (Optional)"
                type="text"
                fullWidth
                variant="outlined"
                value={formData.context}
                onChange={handleChange}
                multiline
                rows={2}
                helperText="Provide context to help translators understand where and how this text is used"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 2 }}>
          <Button onClick={() => setOpenDialog(false)} color="inherit">
            Cancel
          </Button>
          <Button
            onClick={handleSaveTranslation}
            variant="contained"
            color="primary"
            startIcon={<SaveIcon />}
            disabled={!formData.key || !formData.value}
          >
            Save
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default TranslationManager;
