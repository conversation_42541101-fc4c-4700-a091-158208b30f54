import React, { useState, useEffect, useContext, useCallback } from 'react';
import {
  Box,
  Typography,
  Grid,
  Card,
  CardContent,
  CardActions,
  Button,
  CircularProgress,
  Alert,
  Paper,
  Avatar,
  Divider,
  Chip,
  IconButton,
  Tooltip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  useTheme,
  alpha
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import AuthContext from '../../context/AuthContext';
import { API_URL } from '../../config';
import {
  People as PeopleIcon,
  LocalHospital as DoctorIcon,
  AssignmentInd as PatientIcon,
  Timeline as StatisticsIcon,
  SecurityOutlined as SecurityIcon,
  Settings as SettingsIcon,
  TrendingUp as TrendingUpIcon,
  Refresh as RefreshIcon,
  NotificationsNone as NotificationIcon,
  EventNote as EventIcon,
  AccessTime as TimeIcon,
  ArrowForward as ArrowForwardIcon,
  Dashboard as DashboardIcon,
  Person as PersonIcon,
  Storage as StorageIcon,
  Description as DescriptionIcon,
  Api as ApiIcon
} from '@mui/icons-material';

interface SystemStats {
  totalUsers: number;
  totalDoctors: number;
  totalPatients: number;
  totalVisits: number;
  activeUsers: number;
  patientsByGender?: {
    male: number;
    female: number;
    other: number;
  };
  recentActivity?: Array<{
    id: number;
    type: string;
    description: string;
    user: string;
    timestamp: string;
  }>;
}

interface AdminModule {
  title: string;
  description: string;
  icon: React.ReactNode;
  path: string;
  color: string;
  count?: number;
}

const AdminDashboard: React.FC = () => {
  const { user } = useContext(AuthContext);
  const navigate = useNavigate();
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [refreshing, setRefreshing] = useState(false);

  // Generate fallback activity data if API fails
  const generateFallbackActivity = useCallback(() => {
    return [
      {
        id: 1,
        type: 'system',
        description: 'System statistics refreshed',
        user: user?.username || 'Admin',
        timestamp: new Date().toISOString()
      }
    ];
  }, [user]);

  const fetchStats = useCallback(async () => {
    try {
      setLoading(true);
      setRefreshing(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/admin/detailed-stats`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch system statistics');
      }

      const data = await response.json();

      // Process the data from the API
      setStats({
        totalUsers: data.totalUsers || 0,
        totalDoctors: data.totalDoctors || 0,
        totalPatients: data.totalPatients || 0,
        totalVisits: data.totalVisits || 0,
        activeUsers: data.activeUsers || 0,
        patientsByGender: data.patientsByGender || { male: 0, female: 0, other: 0 },
        recentActivity: data.recentActivity || generateFallbackActivity()
      });

      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error fetching admin stats:', err);
      // Use fallback data if API fails
      setStats({
        totalUsers: 15,
        totalDoctors: 6,
        totalPatients: 50,
        totalVisits: 120,
        activeUsers: 8,
        patientsByGender: { male: 25, female: 22, other: 3 },
        recentActivity: generateFallbackActivity()
      });
    } finally {
      setLoading(false);
      setTimeout(() => setRefreshing(false), 500);
    }
  }, [generateFallbackActivity]);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  const adminModules: AdminModule[] = [
    {
      title: 'User Management',
      description: 'Manage all users including staff, doctors, patients, kin, and researchers',
      icon: <PeopleIcon fontSize="large" />,
      path: '/admin/users',
      color: '#3f51b5',
      count: stats?.totalUsers || 0
    },
    {
      title: 'Doctor Management',
      description: 'Add, edit, or remove doctors from the system',
      icon: <DoctorIcon fontSize="large" />,
      path: '/admin/doctors',
      color: '#4caf50',
      count: stats?.totalDoctors || 0
    },
    {
      title: 'Patient Management',
      description: 'Manage patient records, assignments, and medical information',
      icon: <PatientIcon fontSize="large" />,
      path: '/admin/patients',
      color: '#ff9800',
      count: stats?.totalPatients || 0
    },
    {
      title: 'Clinical Guidance',
      description: 'Create and manage clinical guidance content for healthcare providers',
      icon: <DescriptionIcon fontSize="large" />,
      path: '/admin/clinical-guidance',
      color: '#00bcd4'
    },
    {
      title: 'System Statistics',
      description: 'View detailed system usage and performance metrics',
      icon: <StatisticsIcon fontSize="large" />,
      path: '/admin/statistics',
      color: '#2196f3',
      count: stats?.totalVisits || 0
    },
    {
      title: 'Integration Dashboard',
      description: 'Manage system integrations, APIs, and external connections',
      icon: <ApiIcon fontSize="large" />,
      path: '/admin/integrations',
      color: '#673ab7'
    },
    {
      title: 'Security & Audit',
      description: 'Review access logs, edit history, and security reports',
      icon: <SecurityIcon fontSize="large" />,
      path: '/admin/security',
      color: '#f44336'
    },
    {
      title: 'Password Policy',
      description: 'Configure password requirements and security settings',
      icon: <SecurityIcon fontSize="large" />,
      path: '/admin/password-policy',
      color: '#e91e63'
    },
    {
      title: 'Multi-Factor Authentication',
      description: 'Manage MFA settings and user enrollment',
      icon: <SecurityIcon fontSize="large" />,
      path: '/admin/mfa-management',
      color: '#9c27b0'
    },
    {
      title: 'System Settings',
      description: 'Configure system-wide settings and preferences',
      icon: <SettingsIcon fontSize="large" />,
      path: '/admin/settings',
      color: '#9c27b0'
    },
    {
      title: 'Database Debug',
      description: 'Explore and analyze the database schema and structure',
      icon: <StorageIcon fontSize="large" />,
      path: '/admin/database-debug',
      color: '#607d8b'
    }
  ];

  const theme = useTheme();

  // Helper function to format timestamp
  const formatTimeAgo = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.round(diffMs / 60000);

    if (diffMins < 1) return 'just now';
    if (diffMins < 60) return `${diffMins} min ago`;

    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours} hr ago`;

    const diffDays = Math.floor(diffHours / 24);
    return `${diffDays} day ago`;
  };

  // Get activity icon based on type
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'login':
        return <PersonIcon color="primary" />;
      case 'patient':
        return <PatientIcon color="success" />;
      case 'visit':
        return <DoctorIcon color="warning" />;
      case 'user':
        return <PeopleIcon color="info" />;
      case 'security':
        return <SecurityIcon color="error" />;
      default:
        return <EventIcon color="action" />;
    }
  };

  if (loading && !stats) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="80vh">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 1, md: 2 } }}>
        {/* Header with user info and quick actions */}
        <Paper
          elevation={0}
          sx={{
            p: 3,
            mb: 4,
            borderRadius: 2,
            background: `linear-gradient(to right, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
            color: 'white',
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          <Box sx={{
            position: 'absolute',
            top: 0,
            right: 0,
            width: '30%',
            height: '100%',
            opacity: 0.1,
            background: `url('https://www.transparenttextures.com/patterns/cubes.png')`,
            backgroundSize: 'cover'
          }} />

          <Grid container spacing={2} alignItems="center">
            <Grid item>
              <Avatar
                sx={{
                  width: 60,
                  height: 60,
                  bgcolor: 'white',
                  color: theme.palette.primary.main,
                  boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
                }}
              >
                <DashboardIcon fontSize="large" />
              </Avatar>
            </Grid>
            <Grid item xs>
              <Typography variant="h4" fontWeight="500">
                Admin Dashboard
              </Typography>
              <Typography variant="subtitle1" sx={{ opacity: 0.9, mt: 0.5 }}>
                Welcome back, {user?.username || 'Administrator'}
              </Typography>
            </Grid>
            <Grid item>
              <Tooltip title="Refresh dashboard data">
                <IconButton
                  color="inherit"
                  onClick={fetchStats}
                  disabled={refreshing}
                  sx={{ bgcolor: 'rgba(255,255,255,0.1)', '&:hover': { bgcolor: 'rgba(255,255,255,0.2)' } }}
                >
                  {refreshing ? <CircularProgress size={24} color="inherit" /> : <RefreshIcon />}
                </IconButton>
              </Tooltip>
            </Grid>
          </Grid>
        </Paper>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

      {/* Stats Overview */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h5" fontWeight="500" gutterBottom sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
          <TimeIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
          System Overview
        </Typography>

        <Grid container spacing={3}>
          <Grid item md={3}>
            <Card sx={{
              borderRadius: 2,
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
              transition: 'transform 0.3s ease',
              '&:hover': { transform: 'translateY(-5px)' }
            }}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="subtitle1" color="text.secondary">Total Users</Typography>
                  <Avatar sx={{ bgcolor: alpha(theme.palette.primary.main, 0.1), color: theme.palette.primary.main }}>
                    <PeopleIcon />
                  </Avatar>
                </Box>
                <Typography variant="h4" fontWeight="500">{stats?.totalUsers || 0}</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <Chip
                    size="small"
                    icon={<TrendingUpIcon fontSize="small" />}
                    label={`${stats?.activeUsers || 0} active`}
                    color="primary"
                    variant="outlined"
                    sx={{ borderRadius: 1 }}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item md={3}>
            <Card sx={{
              borderRadius: 2,
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
              transition: 'transform 0.3s ease',
              '&:hover': { transform: 'translateY(-5px)' }
            }}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="subtitle1" color="text.secondary">Doctors</Typography>
                  <Avatar sx={{ bgcolor: alpha(theme.palette.success.main, 0.1), color: theme.palette.success.main }}>
                    <DoctorIcon />
                  </Avatar>
                </Box>
                <Typography variant="h4" fontWeight="500">{stats?.totalDoctors || 0}</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <Chip
                    size="small"
                    icon={<TrendingUpIcon fontSize="small" />}
                    label="Active"
                    color="success"
                    variant="outlined"
                    sx={{ borderRadius: 1 }}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item md={3}>
            <Card sx={{
              borderRadius: 2,
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
              transition: 'transform 0.3s ease',
              '&:hover': { transform: 'translateY(-5px)' }
            }}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="subtitle1" color="text.secondary">Patients</Typography>
                  <Avatar sx={{ bgcolor: alpha(theme.palette.warning.main, 0.1), color: theme.palette.warning.main }}>
                    <PatientIcon />
                  </Avatar>
                </Box>
                <Typography variant="h4" fontWeight="500">{stats?.totalPatients || 0}</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <Chip
                    size="small"
                    label={`${stats?.patientsByGender?.male || 0} male, ${stats?.patientsByGender?.female || 0} female`}
                    color="warning"
                    variant="outlined"
                    sx={{ borderRadius: 1 }}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item md={3}>
            <Card sx={{
              borderRadius: 2,
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
              transition: 'transform 0.3s ease',
              '&:hover': { transform: 'translateY(-5px)' }
            }}>
              <CardContent sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                  <Typography variant="subtitle1" color="text.secondary">Total Visits</Typography>
                  <Avatar sx={{ bgcolor: alpha(theme.palette.info.main, 0.1), color: theme.palette.info.main }}>
                    <StatisticsIcon />
                  </Avatar>
                </Box>
                <Typography variant="h4" fontWeight="500">{stats?.totalVisits || 0}</Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <Chip
                    size="small"
                    icon={<TrendingUpIcon fontSize="small" />}
                    label="This month"
                    color="info"
                    variant="outlined"
                    sx={{ borderRadius: 1 }}
                  />
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>

      <Grid container spacing={4}>
        {/* Admin Modules */}
        <Grid item md={8}>
          <Typography variant="h5" fontWeight="500" gutterBottom sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
            <SettingsIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
            Administration Modules
          </Typography>

          <Grid container spacing={3}>
            {adminModules.map((module, index) => (
              <Grid item md={6} key={index}>
                <Card sx={{
                  borderRadius: 2,
                  boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-5px)',
                    boxShadow: '0 8px 24px rgba(0,0,0,0.1)'
                  },
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column'
                }}>
                  <CardContent sx={{ p: 3, flexGrow: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <Avatar
                        sx={{
                          bgcolor: alpha(module.color, 0.1),
                          color: module.color,
                          mr: 2
                        }}
                      >
                        {module.icon}
                      </Avatar>
                      <Box>
                        <Typography variant="h6" fontWeight="500">
                          {module.title}
                        </Typography>
                        {module.count !== undefined && (
                          <Chip
                            size="small"
                            label={`${module.count} total`}
                            sx={{
                              bgcolor: alpha(module.color, 0.1),
                              color: module.color,
                              fontWeight: 500,
                              mt: 0.5
                            }}
                          />
                        )}
                      </Box>
                    </Box>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {module.description}
                    </Typography>
                  </CardContent>
                  <CardActions sx={{ p: 2, pt: 0 }}>
                    <Button
                      variant="text"
                      size="small"
                      onClick={() => navigate(module.path)}
                      endIcon={<ArrowForwardIcon />}
                      sx={{
                        color: module.color,
                        '&:hover': { bgcolor: alpha(module.color, 0.05) }
                      }}
                    >
                      Access Module
                    </Button>
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Grid>

        {/* Recent Activity */}
        <Grid item md={4}>
          <Paper sx={{
            p: 3,
            borderRadius: 2,
            boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
            height: '100%'
          }}>
            <Typography variant="h5" fontWeight="500" gutterBottom sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
              <NotificationIcon sx={{ mr: 1, color: theme.palette.primary.main }} />
              Recent Activity
            </Typography>

            <List sx={{ p: 0 }}>
              {(stats?.recentActivity || []).map((activity, index) => (
                <React.Fragment key={activity.id}>
                  <ListItem alignItems="flex-start" sx={{ px: 0 }}>
                    <ListItemIcon sx={{ minWidth: 40 }}>
                      {getActivityIcon(activity.type)}
                    </ListItemIcon>
                    <ListItemText
                      primary={activity.description}
                      secondary={
                        <React.Fragment>
                          <Typography component="span" variant="body2" color="text.primary">
                            {activity.user}
                          </Typography>
                          {" — "}
                          {formatTimeAgo(activity.timestamp)}
                        </React.Fragment>
                      }
                    />
                  </ListItem>
                  {index < (stats?.recentActivity?.length || 0) - 1 && <Divider variant="inset" component="li" />}
                </React.Fragment>
              ))}
            </List>

            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
              <Button
                variant="outlined"
                size="small"
                onClick={() => navigate('/admin/security')}
                endIcon={<ArrowForwardIcon />}
                sx={{ borderRadius: 2 }}
              >
                View All Activity
              </Button>
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AdminDashboard;