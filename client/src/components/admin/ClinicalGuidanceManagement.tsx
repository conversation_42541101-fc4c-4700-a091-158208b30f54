import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  TextField,
  IconButton,
  Chip,
  Avatar,
  Tabs,
  Tab,
  // Divider - Unused import
  // List - Unused import
  // ListItem - Unused import
  // ListItemText - Unused import
  // ListItemIcon - Unused import
  // ListItemSecondaryAction - Unused import
  CircularProgress,
  Alert,
  useTheme,
  alpha,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Category as CategoryIcon,
  LocalOffer as TagIcon,
  Description as DescriptionIcon,
  Publish as PublishIcon,
  Unpublished as UnpublishedIcon,
  FilterList as FilterListIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import {
  ClinicalGuidance,
  GuidanceCategory,
  getAllGuidance,
  getAllCategories,
  getAllTags,
  deleteGuidance
} from '../../services/clinicalGuidanceService';
import GuidanceEditor from './guidance/GuidanceEditor';
import GuidanceViewer from './guidance/GuidanceViewer';
import GuidanceCategoryManager from './guidance/GuidanceCategoryManager';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index, ...other }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`guidance-tabpanel-${index}`}
      aria-labelledby={`guidance-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

const ClinicalGuidanceManagement: React.FC = () => {
  const theme = useTheme();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [guidance, setGuidance] = useState<ClinicalGuidance[]>([]);
  const [categories, setCategories] = useState<GuidanceCategory[]>([]);
  const [tags, setTags] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategoryId, setSelectedCategoryId] = useState<number | null>(null);
  const [selectedGuidance, setSelectedGuidance] = useState<ClinicalGuidance | null>(null);
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [isViewerOpen, setIsViewerOpen] = useState(false);
  const [isCategoryManagerOpen, setIsCategoryManagerOpen] = useState(false);
  const [isCreatingNew, setIsCreatingNew] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [guidanceToDelete, setGuidanceToDelete] = useState<ClinicalGuidance | null>(null);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [deleteSuccess, setDeleteSuccess] = useState<string | null>(null);

  // Fetch guidance, categories, and tags
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch guidance
        const guidanceData = await getAllGuidance({
          search: searchTerm || undefined,
          categoryId: selectedCategoryId || undefined,
          publishedOnly: false
        });
        setGuidance(guidanceData);

        // Fetch categories
        const categoriesData = await getAllCategories();
        setCategories(categoriesData);

        // Fetch tags
        const tagsData = await getAllTags();
        setTags(tagsData.map(tag => tag.name));
      } catch (err) {
        console.error('Error fetching guidance data:', err);
        setError('Failed to load guidance data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [searchTerm, selectedCategoryId, refreshTrigger]);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle search
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Handle category filter
  const handleCategoryFilter = (categoryId: number | null) => {
    setSelectedCategoryId(categoryId === selectedCategoryId ? null : categoryId);
  };

  // Handle refresh
  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  // Handle create new guidance
  const handleCreateNew = () => {
    setSelectedGuidance(null);
    setIsCreatingNew(true);
    setIsEditorOpen(true);
  };

  // Handle edit guidance
  const handleEdit = (guidance: ClinicalGuidance) => {
    setSelectedGuidance(guidance);
    setIsCreatingNew(false);
    setIsEditorOpen(true);
  };

  // Handle view guidance
  const handleView = (guidance: ClinicalGuidance) => {
    setSelectedGuidance(guidance);
    setIsViewerOpen(true);
  };

  // Handle delete guidance
  const handleDelete = (guidance: ClinicalGuidance) => {
    setGuidanceToDelete(guidance);
    setIsDeleteDialogOpen(true);
    setDeleteError(null);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    if (!guidanceToDelete) return;

    try {
      setDeleteLoading(true);
      setDeleteError(null);

      await deleteGuidance(guidanceToDelete.guidance_id);

      setDeleteSuccess(`"${guidanceToDelete.title}" has been deleted successfully.`);
      setIsDeleteDialogOpen(false);
      handleRefresh();
    } catch (err) {
      console.error('Error deleting guidance:', err);
      setDeleteError(err instanceof Error ? err.message : 'Failed to delete guidance');
    } finally {
      setDeleteLoading(false);
    }
  };

  // Handle delete dialog close
  const handleDeleteDialogClose = () => {
    setIsDeleteDialogOpen(false);
    setGuidanceToDelete(null);
    setDeleteError(null);
  };

  // Handle editor close
  const handleEditorClose = (refreshNeeded: boolean = false) => {
    setIsEditorOpen(false);
    if (refreshNeeded) {
      handleRefresh();
    }
  };

  // Handle viewer close
  const handleViewerClose = () => {
    setIsViewerOpen(false);
  };

  // Handle category manager close
  const handleCategoryManagerClose = (refreshNeeded: boolean = false) => {
    setIsCategoryManagerOpen(false);
    if (refreshNeeded) {
      handleRefresh();
    }
  };

  // Render guidance list
  const renderGuidanceList = () => {
    if (loading && guidance.length === 0) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      );
    }

    if (guidance.length === 0) {
      return (
        <Box sx={{ textAlign: 'center', p: 4 }}>
          <InfoIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No guidance found
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {searchTerm || selectedCategoryId
              ? 'Try adjusting your search or filters'
              : 'Get started by creating your first clinical guidance'}
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateNew}
            sx={{ mt: 2 }}
          >
            Create New Guidance
          </Button>
        </Box>
      );
    }

    return (
      <Grid container spacing={2}>
        {guidance.map((item) => (
          <Grid item xs={12} sm={6} md={4} key={item.guidance_id}>
            <Paper
              elevation={1}
              sx={{
                borderRadius: 2,
                overflow: 'hidden',
                height: '100%',
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  transform: 'translateY(-4px)',
                  boxShadow: '0 8px 16px rgba(0,0,0,0.1)',
                  '& .guidance-card-actions': {
                    opacity: 1
                  }
                }
              }}
            >
              {/* Card Header */}
              <Box
                sx={{
                  p: 2,
                  display: 'flex',
                  alignItems: 'center',
                  borderBottom: '1px solid',
                  borderColor: 'divider',
                  bgcolor: item.is_published
                    ? alpha(theme.palette.success.main, 0.05)
                    : alpha(theme.palette.warning.main, 0.05)
                }}
              >
                <Avatar
                  sx={{
                    mr: 1.5,
                    bgcolor: item.is_published
                      ? alpha(theme.palette.success.main, 0.1)
                      : alpha(theme.palette.warning.main, 0.1),
                    color: item.is_published
                      ? theme.palette.success.main
                      : theme.palette.warning.main
                  }}
                >
                  {item.is_published ? <PublishIcon /> : <UnpublishedIcon />}
                </Avatar>
                <Typography
                  variant="subtitle1"
                  fontWeight="medium"
                  sx={{
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap'
                  }}
                >
                  {item.title}
                </Typography>
              </Box>

              {/* Card Content */}
              <Box sx={{ p: 2 }}>
                {item.summary && (
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{
                      display: '-webkit-box',
                      WebkitLineClamp: 2,
                      WebkitBoxOrient: 'vertical',
                      overflow: 'hidden',
                      mb: 2,
                      height: '40px'
                    }}
                  >
                    {item.summary}
                  </Typography>
                )}

                {/* Tags */}
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2, minHeight: '28px' }}>
                  {item.category_name && (
                    <Chip
                      size="small"
                      icon={<CategoryIcon fontSize="small" />}
                      label={item.category_name}
                      sx={{ height: 24 }}
                    />
                  )}
                  {item.context_key && (
                    <Chip
                      size="small"
                      label={`Context: ${item.context_key}`}
                      sx={{ height: 24 }}
                    />
                  )}
                </Box>

                {/* Additional Tags (if any) */}
                {item.tags && item.tags.length > 0 && (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1, minHeight: '28px' }}>
                    {item.tags.map(
                      (tag, index) =>
                        tag && (
                          <Chip
                            key={index}
                            size="small"
                            icon={<TagIcon fontSize="small" />}
                            label={tag}
                            sx={{ height: 24 }}
                          />
                        )
                    )}
                  </Box>
                )}
              </Box>

              {/* Card Actions */}
              <Box
                className="guidance-card-actions"
                sx={{
                  p: 1.5,
                  display: 'flex',
                  justifyContent: 'space-between',
                  borderTop: '1px solid',
                  borderColor: 'divider',
                  opacity: 0.7,
                  transition: 'opacity 0.2s ease-in-out'
                }}
              >
                <Button
                  size="small"
                  startIcon={<ViewIcon />}
                  onClick={() => handleView(item)}
                >
                  View
                </Button>
                <Box>
                  <IconButton
                    aria-label="edit"
                    onClick={() => handleEdit(item)}
                    size="small"
                    sx={{ mr: 0.5 }}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                  <IconButton
                    aria-label="delete"
                    onClick={() => handleDelete(item)}
                    size="small"
                    color="error"
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Box>
              </Box>
            </Paper>
          </Grid>
        ))}
      </Grid>
    );
  };

  // Render category list
  const renderCategoryList = () => {
    return (
      <Box>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            p: 1.5,
            borderRadius: 1,
            cursor: 'pointer',
            mb: 1,
            transition: 'all 0.2s',
            bgcolor: selectedCategoryId === null
              ? alpha(theme.palette.primary.main, 0.1)
              : alpha(theme.palette.background.paper, 0.5),
            border: '1px solid',
            borderColor: selectedCategoryId === null
              ? theme.palette.primary.main
              : alpha(theme.palette.divider, 0.5),
            '&:hover': {
              bgcolor: selectedCategoryId === null
                ? alpha(theme.palette.primary.main, 0.15)
                : alpha(theme.palette.background.paper, 0.8),
            }
          }}
          onClick={() => handleCategoryFilter(null)}
        >
          <Box
            sx={{
              width: 32,
              height: 32,
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              bgcolor: selectedCategoryId === null
                ? theme.palette.primary.main
                : alpha(theme.palette.grey[300], 0.5),
              color: selectedCategoryId === null
                ? 'white'
                : theme.palette.text.secondary,
              mr: 1.5
            }}
          >
            <CategoryIcon fontSize="small" />
          </Box>
          <Typography
            variant="body1"
            sx={{
              fontWeight: selectedCategoryId === null ? 500 : 400,
              color: selectedCategoryId === null
                ? theme.palette.primary.main
                : theme.palette.text.primary,
              flexGrow: 1
            }}
          >
            All Categories
          </Typography>
          <Chip
            label={guidance.length}
            size="small"
            color={selectedCategoryId === null ? 'primary' : 'default'}
            sx={{
              height: 24,
              minWidth: 30,
              fontWeight: selectedCategoryId === null ? 500 : 400
            }}
          />
        </Box>

        {categories.map((category) => (
          <Box
            key={category.category_id}
            sx={{
              display: 'flex',
              alignItems: 'center',
              p: 1.5,
              borderRadius: 1,
              cursor: 'pointer',
              mb: 1,
              transition: 'all 0.2s',
              bgcolor: selectedCategoryId === category.category_id
                ? alpha(theme.palette.primary.main, 0.1)
                : alpha(theme.palette.background.paper, 0.5),
              border: '1px solid',
              borderColor: selectedCategoryId === category.category_id
                ? theme.palette.primary.main
                : alpha(theme.palette.divider, 0.5),
              '&:hover': {
                bgcolor: selectedCategoryId === category.category_id
                  ? alpha(theme.palette.primary.main, 0.15)
                  : alpha(theme.palette.background.paper, 0.8),
              }
            }}
            onClick={() => handleCategoryFilter(category.category_id)}
          >
            <Box
              sx={{
                width: 32,
                height: 32,
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                bgcolor: selectedCategoryId === category.category_id
                  ? theme.palette.primary.main
                  : alpha(theme.palette.grey[300], 0.5),
                color: selectedCategoryId === category.category_id
                  ? 'white'
                  : theme.palette.text.secondary,
                mr: 1.5
              }}
            >
              {category.name.charAt(0).toUpperCase()}
            </Box>
            <Typography
              variant="body1"
              sx={{
                fontWeight: selectedCategoryId === category.category_id ? 500 : 400,
                color: selectedCategoryId === category.category_id
                  ? theme.palette.primary.main
                  : theme.palette.text.primary,
                flexGrow: 1
              }}
            >
              {category.name}
            </Typography>
            <Chip
              label={category.guidance_count || 0}
              size="small"
              color={selectedCategoryId === category.category_id ? 'primary' : 'default'}
              sx={{
                height: 24,
                minWidth: 30,
                fontWeight: selectedCategoryId === category.category_id ? 500 : 400
              }}
            />
          </Box>
        ))}
      </Box>
    );
  };

  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      {/* Header */}
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 4,
          borderRadius: 2,
          background: `linear-gradient(to right, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
          color: 'white',
          position: 'relative',
          overflow: 'hidden'
        }}
      >
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            right: 0,
            width: '30%',
            height: '100%',
            opacity: 0.1,
            background: `url('https://www.transparenttextures.com/patterns/cubes.png')`,
            backgroundSize: 'cover'
          }}
        />

        <Grid container spacing={2} alignItems="center">
          <Grid item>
            <Avatar
              sx={{
                width: 60,
                height: 60,
                bgcolor: 'white',
                color: theme.palette.primary.main,
                boxShadow: '0 4px 8px rgba(0,0,0,0.1)'
              }}
            >
              <DescriptionIcon fontSize="large" />
            </Avatar>
          </Grid>
          <Grid item xs>
            <Typography variant="h4" fontWeight="500">
              Clinical Guidance Management
            </Typography>
            <Typography variant="subtitle1" sx={{ opacity: 0.9, mt: 0.5 }}>
              Create, edit, and manage clinical guidance content for healthcare providers
            </Typography>
          </Grid>
          <Grid item>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <Button
                variant="contained"
                startIcon={<CategoryIcon />}
                onClick={() => setIsCategoryManagerOpen(true)}
                sx={{
                  bgcolor: 'rgba(255,255,255,0.2)',
                  color: 'white',
                  '&:hover': { bgcolor: 'rgba(255,255,255,0.3)' }
                }}
              >
                Manage Categories
              </Button>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleCreateNew}
                sx={{
                  bgcolor: 'white',
                  color: theme.palette.primary.main,
                  '&:hover': { bgcolor: 'rgba(255,255,255,0.9)' }
                }}
              >
                Create New
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Success Alert */}
      {deleteSuccess && (
        <Alert severity="success" sx={{ mb: 3 }} onClose={() => setDeleteSuccess(null)}>
          {deleteSuccess}
        </Alert>
      )}

      {/* Main Content */}
      <Grid container spacing={3}>
        {/* Sidebar */}
        <Grid item xs={12} md={3}>
          <Grid container spacing={2}>
            {/* Statistics Card */}
            <Grid item xs={12}>
              <Paper
                sx={{
                  p: 2,
                  borderRadius: 2,
                  background: `linear-gradient(135deg, ${alpha(theme.palette.primary.light, 0.2)}, ${alpha(theme.palette.primary.main, 0.1)})`,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.05)'
                }}
              >
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', color: theme.palette.primary.main }}>
                  <InfoIcon sx={{ mr: 1, fontSize: 20 }} />
                  Statistics
                </Typography>

                <Grid container spacing={2} sx={{ mt: 1 }}>
                  <Grid item xs={6}>
                    <Paper
                      elevation={0}
                      sx={{
                        p: 1.5,
                        textAlign: 'center',
                        bgcolor: alpha(theme.palette.background.paper, 0.7),
                        borderRadius: 1
                      }}
                    >
                      <Typography variant="h4" color="primary" fontWeight="medium">
                        {guidance.length}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Guidance
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6}>
                    <Paper
                      elevation={0}
                      sx={{
                        p: 1.5,
                        textAlign: 'center',
                        bgcolor: alpha(theme.palette.background.paper, 0.7),
                        borderRadius: 1
                      }}
                    >
                      <Typography variant="h4" color="success.main" fontWeight="medium">
                        {guidance.filter(item => item.is_published).length}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Published
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6}>
                    <Paper
                      elevation={0}
                      sx={{
                        p: 1.5,
                        textAlign: 'center',
                        bgcolor: alpha(theme.palette.background.paper, 0.7),
                        borderRadius: 1
                      }}
                    >
                      <Typography variant="h4" color="warning.main" fontWeight="medium">
                        {guidance.filter(item => !item.is_published).length}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Drafts
                      </Typography>
                    </Paper>
                  </Grid>
                  <Grid item xs={6}>
                    <Paper
                      elevation={0}
                      sx={{
                        p: 1.5,
                        textAlign: 'center',
                        bgcolor: alpha(theme.palette.background.paper, 0.7),
                        borderRadius: 1
                      }}
                    >
                      <Typography variant="h4" color="text.primary" fontWeight="medium">
                        {categories.length}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Categories
                      </Typography>
                    </Paper>
                  </Grid>
                </Grid>
              </Paper>
            </Grid>

            {/* Filters Card */}
            <Grid item xs={12}>
              <Paper sx={{ p: 2, borderRadius: 2 }}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                  <FilterListIcon sx={{ mr: 1 }} />
                  Filters
                </Typography>
                <TextField
                  fullWidth
                  placeholder="Search guidance..."
                  value={searchTerm}
                  onChange={handleSearch}
                  margin="normal"
                  variant="outlined"
                  size="small"
                  InputProps={{
                    startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  }}
                />
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    mt: 2,
                    mb: 1
                  }}
                >
                  <Typography variant="subtitle2">Categories</Typography>
                  <IconButton size="small" onClick={handleRefresh}>
                    <RefreshIcon fontSize="small" />
                  </IconButton>
                </Box>
                {renderCategoryList()}
              </Paper>
            </Grid>
          </Grid>
        </Grid>

        {/* Main Content */}
        <Grid item xs={12} md={9}>
          <Paper sx={{ borderRadius: 2 }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              sx={{
                borderBottom: 1,
                borderColor: 'divider',
                '& .MuiTabs-indicator': {
                  height: 3,
                  borderRadius: '3px 3px 0 0'
                },
                '& .MuiTab-root': {
                  minHeight: 48,
                  fontWeight: 500,
                  transition: 'all 0.2s',
                  '&:hover': {
                    color: theme.palette.primary.main,
                    opacity: 1
                  }
                }
              }}
              variant="fullWidth"
            >
              <Tab
                label="All Guidance"
                icon={<DescriptionIcon />}
                iconPosition="start"
                sx={{
                  borderBottom: tabValue === 0 ? `3px solid ${theme.palette.primary.main}` : 'none',
                  borderRadius: '4px 4px 0 0',
                  mb: '-1px'
                }}
              />
              <Tab
                label="Published"
                icon={<PublishIcon />}
                iconPosition="start"
                sx={{
                  borderBottom: tabValue === 1 ? `3px solid ${theme.palette.success.main}` : 'none',
                  borderRadius: '4px 4px 0 0',
                  mb: '-1px',
                  color: tabValue === 1 ? theme.palette.success.main : 'inherit',
                  '& .MuiTabs-indicator': {
                    backgroundColor: theme.palette.success.main
                  }
                }}
              />
              <Tab
                label="Drafts"
                icon={<UnpublishedIcon />}
                iconPosition="start"
                sx={{
                  borderBottom: tabValue === 2 ? `3px solid ${theme.palette.warning.main}` : 'none',
                  borderRadius: '4px 4px 0 0',
                  mb: '-1px',
                  color: tabValue === 2 ? theme.palette.warning.main : 'inherit',
                  '& .MuiTabs-indicator': {
                    backgroundColor: theme.palette.warning.main
                  }
                }}
              />
            </Tabs>

            <TabPanel value={tabValue} index={0}>
              {renderGuidanceList()}
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              {guidance.filter(item => item.is_published).length === 0 ? (
                <Box sx={{ textAlign: 'center', p: 4 }}>
                  <InfoIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    No published guidance found
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {searchTerm || selectedCategoryId
                      ? 'Try adjusting your search or filters'
                      : 'Publish some guidance to see it here'}
                  </Typography>
                </Box>
              ) : (
                <Grid container spacing={2}>
                  {guidance
                    .filter(item => item.is_published)
                    .map((item) => (
                      <Grid item xs={12} sm={6} md={4} key={item.guidance_id}>
                        <Paper
                          elevation={1}
                          sx={{
                            borderRadius: 2,
                            overflow: 'hidden',
                            height: '100%',
                            transition: 'all 0.2s ease-in-out',
                            '&:hover': {
                              transform: 'translateY(-4px)',
                              boxShadow: '0 8px 16px rgba(0,0,0,0.1)',
                              '& .guidance-card-actions': {
                                opacity: 1
                              }
                            }
                          }}
                        >
                          {/* Card Header */}
                          <Box
                            sx={{
                              p: 2,
                              display: 'flex',
                              alignItems: 'center',
                              borderBottom: '1px solid',
                              borderColor: 'divider',
                              bgcolor: alpha(theme.palette.success.main, 0.05)
                            }}
                          >
                            <Avatar
                              sx={{
                                mr: 1.5,
                                bgcolor: alpha(theme.palette.success.main, 0.1),
                                color: theme.palette.success.main
                              }}
                            >
                              <PublishIcon />
                            </Avatar>
                            <Typography
                              variant="subtitle1"
                              fontWeight="medium"
                              sx={{
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap'
                              }}
                            >
                              {item.title}
                            </Typography>
                          </Box>

                          {/* Card Content */}
                          <Box sx={{ p: 2 }}>
                            {item.summary && (
                              <Typography
                                variant="body2"
                                color="text.secondary"
                                sx={{
                                  display: '-webkit-box',
                                  WebkitLineClamp: 2,
                                  WebkitBoxOrient: 'vertical',
                                  overflow: 'hidden',
                                  mb: 2,
                                  height: '40px'
                                }}
                              >
                                {item.summary}
                              </Typography>
                            )}

                            {/* Tags */}
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2, minHeight: '28px' }}>
                              {item.category_name && (
                                <Chip
                                  size="small"
                                  icon={<CategoryIcon fontSize="small" />}
                                  label={item.category_name}
                                  sx={{ height: 24 }}
                                />
                              )}
                              {item.context_key && (
                                <Chip
                                  size="small"
                                  label={`Context: ${item.context_key}`}
                                  sx={{ height: 24 }}
                                />
                              )}
                            </Box>

                            {/* Additional Tags (if any) */}
                            {item.tags && item.tags.length > 0 && (
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1, minHeight: '28px' }}>
                                {item.tags.map(
                                  (tag, index) =>
                                    tag && (
                                      <Chip
                                        key={index}
                                        size="small"
                                        icon={<TagIcon fontSize="small" />}
                                        label={tag}
                                        sx={{ height: 24 }}
                                      />
                                    )
                                )}
                              </Box>
                            )}
                          </Box>

                          {/* Card Actions */}
                          <Box
                            className="guidance-card-actions"
                            sx={{
                              p: 1.5,
                              display: 'flex',
                              justifyContent: 'space-between',
                              borderTop: '1px solid',
                              borderColor: 'divider',
                              opacity: 0.7,
                              transition: 'opacity 0.2s ease-in-out'
                            }}
                          >
                            <Button
                              size="small"
                              startIcon={<ViewIcon />}
                              onClick={() => handleView(item)}
                            >
                              View
                            </Button>
                            <Box>
                              <IconButton
                                aria-label="edit"
                                onClick={() => handleEdit(item)}
                                size="small"
                                sx={{ mr: 0.5 }}
                              >
                                <EditIcon fontSize="small" />
                              </IconButton>
                              <IconButton
                                aria-label="delete"
                                onClick={() => handleDelete(item)}
                                size="small"
                                color="error"
                              >
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </Box>
                          </Box>
                        </Paper>
                      </Grid>
                    ))}
                </Grid>
              )}
            </TabPanel>

            <TabPanel value={tabValue} index={2}>
              {guidance.filter(item => !item.is_published).length === 0 ? (
                <Box sx={{ textAlign: 'center', p: 4 }}>
                  <InfoIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary" gutterBottom>
                    No draft guidance found
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {searchTerm || selectedCategoryId
                      ? 'Try adjusting your search or filters'
                      : 'Create some drafts to see them here'}
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={handleCreateNew}
                    sx={{ mt: 2 }}
                  >
                    Create New Draft
                  </Button>
                </Box>
              ) : (
                <Grid container spacing={2}>
                  {guidance
                    .filter(item => !item.is_published)
                    .map((item) => (
                      <Grid item xs={12} sm={6} md={4} key={item.guidance_id}>
                        <Paper
                          elevation={1}
                          sx={{
                            borderRadius: 2,
                            overflow: 'hidden',
                            height: '100%',
                            transition: 'all 0.2s ease-in-out',
                            '&:hover': {
                              transform: 'translateY(-4px)',
                              boxShadow: '0 8px 16px rgba(0,0,0,0.1)',
                              '& .guidance-card-actions': {
                                opacity: 1
                              }
                            }
                          }}
                        >
                          {/* Card Header */}
                          <Box
                            sx={{
                              p: 2,
                              display: 'flex',
                              alignItems: 'center',
                              borderBottom: '1px solid',
                              borderColor: 'divider',
                              bgcolor: alpha(theme.palette.warning.main, 0.05)
                            }}
                          >
                            <Avatar
                              sx={{
                                mr: 1.5,
                                bgcolor: alpha(theme.palette.warning.main, 0.1),
                                color: theme.palette.warning.main
                              }}
                            >
                              <UnpublishedIcon />
                            </Avatar>
                            <Typography
                              variant="subtitle1"
                              fontWeight="medium"
                              sx={{
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap'
                              }}
                            >
                              {item.title}
                            </Typography>
                          </Box>

                          {/* Card Content */}
                          <Box sx={{ p: 2 }}>
                            {item.summary && (
                              <Typography
                                variant="body2"
                                color="text.secondary"
                                sx={{
                                  display: '-webkit-box',
                                  WebkitLineClamp: 2,
                                  WebkitBoxOrient: 'vertical',
                                  overflow: 'hidden',
                                  mb: 2,
                                  height: '40px'
                                }}
                              >
                                {item.summary}
                              </Typography>
                            )}

                            {/* Tags */}
                            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 2, minHeight: '28px' }}>
                              {item.category_name && (
                                <Chip
                                  size="small"
                                  icon={<CategoryIcon fontSize="small" />}
                                  label={item.category_name}
                                  sx={{ height: 24 }}
                                />
                              )}
                              {item.context_key && (
                                <Chip
                                  size="small"
                                  label={`Context: ${item.context_key}`}
                                  sx={{ height: 24 }}
                                />
                              )}
                            </Box>

                            {/* Additional Tags (if any) */}
                            {item.tags && item.tags.length > 0 && (
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mb: 1, minHeight: '28px' }}>
                                {item.tags.map(
                                  (tag, index) =>
                                    tag && (
                                      <Chip
                                        key={index}
                                        size="small"
                                        icon={<TagIcon fontSize="small" />}
                                        label={tag}
                                        sx={{ height: 24 }}
                                      />
                                    )
                                )}
                              </Box>
                            )}
                          </Box>

                          {/* Card Actions */}
                          <Box
                            className="guidance-card-actions"
                            sx={{
                              p: 1.5,
                              display: 'flex',
                              justifyContent: 'space-between',
                              borderTop: '1px solid',
                              borderColor: 'divider',
                              opacity: 0.7,
                              transition: 'opacity 0.2s ease-in-out'
                            }}
                          >
                            <Button
                              size="small"
                              startIcon={<ViewIcon />}
                              onClick={() => handleView(item)}
                            >
                              View
                            </Button>
                            <Box>
                              <IconButton
                                aria-label="edit"
                                onClick={() => handleEdit(item)}
                                size="small"
                                sx={{ mr: 0.5 }}
                              >
                                <EditIcon fontSize="small" />
                              </IconButton>
                              <IconButton
                                aria-label="delete"
                                onClick={() => handleDelete(item)}
                                size="small"
                                color="error"
                              >
                                <DeleteIcon fontSize="small" />
                              </IconButton>
                            </Box>
                          </Box>
                        </Paper>
                      </Grid>
                    ))}
                </Grid>
              )}
            </TabPanel>
          </Paper>
        </Grid>
      </Grid>

      {/* Guidance Editor Dialog */}
      {isEditorOpen && (
        <GuidanceEditor
          open={isEditorOpen}
          onClose={handleEditorClose}
          guidance={selectedGuidance}
          isNew={isCreatingNew}
          categories={categories}
          availableTags={tags}
        />
      )}

      {/* Guidance Viewer Dialog */}
      {isViewerOpen && selectedGuidance && (
        <GuidanceViewer
          open={isViewerOpen}
          onClose={handleViewerClose}
          guidance={selectedGuidance}
          onEdit={() => {
            handleViewerClose();
            handleEdit(selectedGuidance);
          }}
        />
      )}

      {/* Category Manager Dialog */}
      {isCategoryManagerOpen && (
        <GuidanceCategoryManager
          open={isCategoryManagerOpen}
          onClose={handleCategoryManagerClose}
          categories={categories}
        />
      )}

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={isDeleteDialogOpen}
        onClose={handleDeleteDialogClose}
        aria-labelledby="delete-guidance-dialog-title"
      >
        <DialogTitle id="delete-guidance-dialog-title">
          Delete Clinical Guidance
        </DialogTitle>
        <DialogContent>
          {deleteError && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {deleteError}
            </Alert>
          )}
          <DialogContentText>
            Are you sure you want to delete "{guidanceToDelete?.title}"? This action cannot be undone.
          </DialogContentText>
          {guidanceToDelete?.context_key && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              <Typography variant="body2">
                <strong>Warning:</strong> This guidance is linked to context key "{guidanceToDelete.context_key}".
                Deleting it may affect parts of the application that use this guidance.
              </Typography>
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={handleDeleteDialogClose}
            color="primary"
            disabled={deleteLoading}
          >
            Cancel
          </Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
            disabled={deleteLoading}
            startIcon={deleteLoading ? <CircularProgress size={20} /> : <DeleteIcon />}
          >
            {deleteLoading ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ClinicalGuidanceManagement;
