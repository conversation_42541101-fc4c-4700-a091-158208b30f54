import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Grid,
  SelectChangeEvent,
  FormHelperText,
  Box,
  Tab,
  Tabs
} from '@mui/material';
import { API_URL } from '../../config';
import axios from 'axios';
import KinPatientRelationships from './KinPatientRelationships';

interface User {
  user_id: number;
  username: string;
  email: string;
  role: string;
  is_locked: boolean;
  created_at: string;
  last_login: string | null;
  patient_id?: number | null;
  doctor_id?: number | null;
}

interface Patient {
  patient_id: number;
  first_name: string;
  last_name: string;
  unique_id: string;
}

interface EditUserDialogProps {
  open: boolean;
  onClose: () => void;
  user: User | null;
  onUserUpdated: () => void;
}

interface FormData {
  username: string;
  email: string;
  role: string;
  password?: string;
  patient_id?: string | null;
}

// TabPanel component for tabs
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`user-tabpanel-${index}`}
      aria-labelledby={`user-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 2 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const EditUserDialog: React.FC<EditUserDialogProps> = ({ open, onClose, user, onUserUpdated }) => {
  const [formData, setFormData] = useState<FormData>({
    username: '',
    email: '',
    role: '',
    password: '',
    patient_id: null
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loadingPatients, setLoadingPatients] = useState(false);
  const [tabValue, setTabValue] = useState(0);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Fetch patients when the dialog opens or when role changes to 'kin'
  useEffect(() => {
    if (open && user && (user.role === 'kin' || formData.role === 'kin')) {
      fetchPatients();
    }
  }, [open, user, formData.role]);

  // Update form data when user changes
  useEffect(() => {
    if (user) {
      setFormData({
        username: user.username,
        email: user.email,
        role: user.role,
        password: '',
        patient_id: user.patient_id ? String(user.patient_id) : null
      });
    }
  }, [user]);

  // Fetch patients for dropdown
  const fetchPatients = async () => {
    try {
      setLoadingPatients(true);
      const token = localStorage.getItem('token');

      if (!token) {
        setError('Authentication token not found. Please log in again.');
        return;
      }

      const response = await axios.get(`${API_URL}/api/patients`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      setPatients(response.data);
    } catch (err) {
      console.error('Error fetching patients:', err);
      setError('Failed to load patients. Please try again.');
    } finally {
      setLoadingPatients(false);
    }
  };

  const handleTextFieldChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSelectChange = (e: SelectChangeEvent) => {
    const { name, value } = e.target;

    // If changing to kin role, fetch patients if not already loaded
    if (name === 'role' && value === 'kin' && patients.length === 0) {
      fetchPatients();
    }

    // If changing from kin role to something else, clear patient_id
    if (name === 'role' && value !== 'kin') {
      setFormData({ ...formData, [name]: value, patient_id: null });
    } else {
      setFormData({ ...formData, [name]: value });
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        setError('Authentication token not found. Please log in again.');
        return;
      }

      // Create request data - only include password if it's not empty
      const requestData: any = {
        username: formData.username,
        email: formData.email,
        role: formData.role
      };

      if (formData.password && formData.password.trim() !== '') {
        requestData.password = formData.password;
      }

      // Add patient_id if role is kin
      if (formData.role === 'kin' && formData.patient_id) {
        requestData.patient_id = parseInt(formData.patient_id, 10);
      } else if (formData.role !== 'kin') {
        // If not a kin user, set patient_id to null
        requestData.patient_id = null;
      }

      // Make the API request
      await axios.put(
        `${API_URL}/api/admin/users/${user.user_id}`,
        requestData,
        {
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          }
        }
      );

      onUserUpdated();
      onClose();
    } catch (err) {
      if (axios.isAxiosError(err)) {
        setError(err.response?.data?.msg || 'Failed to update user');
      } else {
        setError('An error occurred while updating the user');
      }
      console.error('Error updating user:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Edit User</DialogTitle>
      <DialogContent>
        {error && <Alert severity="error" sx={{ mb: 2 }}>{error}</Alert>}

        {/* Show tabs only for kin users */}
        {user && user.role === 'kin' && (
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 2 }}>
            <Tabs value={tabValue} onChange={handleTabChange} aria-label="user edit tabs">
              <Tab label="User Details" id="user-tab-0" aria-controls="user-tabpanel-0" />
              <Tab label="Patient Relationships" id="user-tab-1" aria-controls="user-tabpanel-1" />
            </Tabs>
          </Box>
        )}

        <TabPanel value={tabValue} index={0}>
          <form onSubmit={handleSubmit}>
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <TextField
                  name="username"
                  label="Username"
                  value={formData.username}
                  onChange={handleTextFieldChange}
                  fullWidth
                  required
                  disabled={loading}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12}>
                <TextField
                  name="email"
                  label="Email"
                  type="email"
                  value={formData.email}
                  onChange={handleTextFieldChange}
                  fullWidth
                  required
                  disabled={loading}
                  margin="normal"
                />
              </Grid>
              <Grid item xs={12} sm={formData.role === 'kin' ? 6 : 12}>
                <FormControl fullWidth margin="normal">
                  <InputLabel id="role-label">Role</InputLabel>
                  <Select
                    labelId="role-label"
                    name="role"
                    value={formData.role}
                    label="Role"
                    onChange={handleSelectChange}
                    disabled={loading}
                  >
                    <MenuItem value="admin">Administrator</MenuItem>
                    <MenuItem value="doctor">Doctor</MenuItem>
                    <MenuItem value="staff">Staff</MenuItem>
                    <MenuItem value="patient">Patient</MenuItem>
                    <MenuItem value="kin">Kin</MenuItem>
                    <MenuItem value="researcher">Researcher</MenuItem>
                  </Select>
                  <FormHelperText>
                    Select the appropriate role for this user
                  </FormHelperText>
                </FormControl>
              </Grid>

              {formData.role === 'kin' && (
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth margin="normal" disabled={loading || loadingPatients}>
                    <InputLabel id="patient-label">Primary Patient</InputLabel>
                    <Select
                      labelId="patient-label"
                      name="patient_id"
                      value={formData.patient_id || ''}
                      label="Primary Patient"
                      onChange={handleSelectChange}
                      startAdornment={loadingPatients ? (
                        <CircularProgress size={20} color="inherit" sx={{ ml: 1, mr: 1 }} />
                      ) : null}
                    >
                      {patients.map((patient) => (
                        <MenuItem key={patient.patient_id} value={String(patient.patient_id)}>
                          {patient.first_name} {patient.last_name} ({patient.unique_id})
                        </MenuItem>
                      ))}
                    </Select>
                    <FormHelperText>
                      Select the primary patient for this kin user (optional - can be managed in the Relationships tab)
                    </FormHelperText>
                  </FormControl>
                </Grid>
              )}
              <Grid item xs={12}>
                <TextField
                  name="password"
                  label="New Password (leave blank to keep current)"
                  type="password"
                  value={formData.password}
                  onChange={handleTextFieldChange}
                  fullWidth
                  disabled={loading}
                  margin="normal"
                  helperText="Leave blank if you don't want to change the password"
                />
              </Grid>
            </Grid>

            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
              <Button onClick={onClose} disabled={loading} sx={{ mr: 1 }}>
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : null}
              >
                {loading ? 'Updating...' : 'Update User'}
              </Button>
            </Box>
          </form>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          {user && user.role === 'kin' && (
            <KinPatientRelationships
              kinUserId={user.user_id}
              kinUsername={user.username}
              onRelationshipsUpdated={onUserUpdated}
            />
          )}
        </TabPanel>
      </DialogContent>
    </Dialog>
  );
};

export default EditUserDialog;
