/* Accessibility Styles */

/* Font size scaling */
html {
  font-size: calc(16px * var(--accessibility-font-scale, 1));
}

/* High contrast mode - Dark */
.high-contrast-mode.dark-mode {
  --text-color: #FFFFFF !important;
  --text-secondary-color: #EEEEEE !important;
  --background-color: #000000 !important;
  --card-bg-color: #121212 !important;
  --primary-color: #4CC2FF !important;
  --secondary-color: #FFD700 !important;
  --border-color: #FFFFFF !important;
}

/* High contrast mode - Light */
.high-contrast-mode:not(.dark-mode) {
  --text-color: #000000 !important;
  --text-secondary-color: #222222 !important;
  --background-color: #FFFFFF !important;
  --card-bg-color: #F8F8F8 !important;
  --primary-color: #0056B3 !important;
  --secondary-color: #7B0000 !important;
  --border-color: #000000 !important;
}

/* High contrast mode - Common styles */
.high-contrast-mode .MuiPaper-root {
  border: 1px solid var(--border-color) !important;
}

.high-contrast-mode .MuiButton-contained {
  border: 1px solid var(--border-color) !important;
}

.high-contrast-mode .MuiCard-root {
  border: 1px solid var(--border-color) !important;
}

.high-contrast-mode .MuiTableCell-head {
  background-color: var(--primary-color) !important;
  color: #FFFFFF !important;
}

.high-contrast-mode.dark-mode .MuiTableCell-body {
  border-bottom: 1px solid rgba(255, 255, 255, 0.3) !important;
}

.high-contrast-mode:not(.dark-mode) .MuiTableCell-body {
  border-bottom: 1px solid rgba(0, 0, 0, 0.3) !important;
}

.high-contrast-mode .MuiListItem-root {
  border-bottom: 1px solid var(--border-color) !important;
}

.high-contrast-mode .MuiDivider-root {
  border-color: var(--border-color) !important;
}

/* Reduced motion */
.reduced-motion * {
  animation-duration: 0.001s !important;
  transition-duration: 0.001s !important;
}

/* Screen reader optimizations */
.screen-reader-optimized .visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.screen-reader-optimized button:focus,
.screen-reader-optimized a:focus,
.screen-reader-optimized input:focus,
.screen-reader-optimized select:focus,
.screen-reader-optimized textarea:focus {
  outline: 2px solid var(--primary-color) !important;
  outline-offset: 2px !important;
}

/* Skip to content link */
.skip-to-content {
  position: absolute;
  top: -40px;
  left: 0;
  background: var(--primary-color);
  color: white;
  padding: 8px;
  z-index: 100;
  transition: top 0.3s;
}

.skip-to-content:focus {
  top: 0;
}

/* ARIA live regions */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

/* Focus indicators */
.screen-reader-optimized *:focus {
  outline: 2px solid var(--primary-color) !important;
  outline-offset: 2px !important;
}

/* Keyboard navigation indicators */
.keyboard-user *:focus {
  outline: 2px solid var(--primary-color) !important;
  outline-offset: 2px !important;
}

/* Improved form field accessibility */
.screen-reader-optimized label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.screen-reader-optimized input[aria-invalid="true"],
.screen-reader-optimized select[aria-invalid="true"],
.screen-reader-optimized textarea[aria-invalid="true"] {
  border: 2px solid #d32f2f !important;
}

.screen-reader-optimized .form-error-message {
  color: #d32f2f;
  margin-top: 4px;
  font-size: 0.875rem;
}

/* Improved table accessibility */
.screen-reader-optimized table {
  border-collapse: collapse;
  width: 100%;
}

.screen-reader-optimized th {
  text-align: left;
  font-weight: 600;
}

.screen-reader-optimized th,
.screen-reader-optimized td {
  padding: 12px;
}

/* Improved button accessibility */
.screen-reader-optimized button {
  cursor: pointer;
}

.screen-reader-optimized button[disabled] {
  cursor: not-allowed;
  opacity: 0.7;
}

/* Improved link accessibility */
.screen-reader-optimized a {
  text-decoration: underline;
}

.screen-reader-optimized a:hover,
.screen-reader-optimized a:focus {
  text-decoration: none;
}

/* Improved dialog accessibility */
.screen-reader-optimized [role="dialog"] {
  outline: none;
}

/* Improved tooltip accessibility */
.screen-reader-optimized [role="tooltip"] {
  z-index: 1500;
}

/* Improved menu accessibility */
.screen-reader-optimized [role="menu"] {
  outline: none;
}

.screen-reader-optimized [role="menuitem"] {
  cursor: pointer;
}

.screen-reader-optimized [role="menuitem"][aria-disabled="true"] {
  cursor: not-allowed;
  opacity: 0.7;
}

/* Improved tab accessibility */
.screen-reader-optimized [role="tablist"] {
  display: flex;
}

.screen-reader-optimized [role="tab"] {
  cursor: pointer;
}

.screen-reader-optimized [role="tab"][aria-selected="true"] {
  font-weight: 600;
}

.screen-reader-optimized [role="tabpanel"] {
  outline: none;
}

/* Improved alert accessibility */
.screen-reader-optimized [role="alert"] {
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 16px;
}

/* Improved progress indicator accessibility */
.screen-reader-optimized [role="progressbar"] {
  display: block;
}

/* Improved checkbox and radio accessibility */
.screen-reader-optimized [type="checkbox"],
.screen-reader-optimized [type="radio"] {
  margin-right: 8px;
}
