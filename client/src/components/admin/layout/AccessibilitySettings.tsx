import React, { useState, useContext, useEffect } from 'react';
import {
  <PERSON>,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  Slider,
  Switch,
  FormControlLabel,
  Divider,
  IconButton,
  Tooltip,
  useTheme,
  alpha,
  styled
} from '@mui/material';
import {
  Accessibility as AccessibilityIcon,
  TextFields as TextFieldsIcon,
  Contrast as ContrastIcon,
  Close as CloseIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import ThemeContext from '../../../context/ThemeContext';

// Create a context for accessibility settings
interface AccessibilityContextType {
  fontSize: number;
  highContrast: boolean;
  reducedMotion: boolean;
  screenReaderOptimized: boolean;
  toggleAccessibilityDialog: () => void;
}

export const AccessibilityContext = React.createContext<AccessibilityContextType>({
  fontSize: 1,
  highContrast: false,
  reducedMotion: false,
  screenReaderOptimized: false,
  toggleAccessibilityDialog: () => {}
});

// Styled components
const AccessibilityButton = styled(IconButton)(({ theme }) => ({
  position: 'fixed',
  bottom: theme.spacing(2),
  right: theme.spacing(2),
  backgroundColor: alpha(theme.palette.primary.main, 0.9),
  color: theme.palette.primary.contrastText,
  zIndex: 1000,
  boxShadow: theme.shadows[4],
  '&:hover': {
    backgroundColor: theme.palette.primary.main
  }
}));

const SettingSection = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(3)
}));

interface AccessibilitySettingsProps {
  children: React.ReactNode;
}

const AccessibilitySettings: React.FC<AccessibilitySettingsProps> = ({ children }) => {
  const theme = useTheme();
  const { mode } = useContext(ThemeContext);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [fontSize, setFontSize] = useState(1);
  const [highContrast, setHighContrast] = useState(false);
  const [reducedMotion, setReducedMotion] = useState(false);
  const [screenReaderOptimized, setScreenReaderOptimized] = useState(false);

  // Toggle dialog
  const toggleDialog = () => {
    setDialogOpen(!dialogOpen);
  };

  // Reset settings
  const resetSettings = () => {
    setFontSize(1);
    setHighContrast(false);
    setReducedMotion(false);
    setScreenReaderOptimized(false);
  };

  // Apply font size to document
  useEffect(() => {
    document.documentElement.style.setProperty('--accessibility-font-scale', fontSize.toString());
  }, [fontSize]);

  // Apply high contrast mode
  useEffect(() => {
    if (highContrast) {
      document.documentElement.classList.add('high-contrast-mode');
    } else {
      document.documentElement.classList.remove('high-contrast-mode');
    }
  }, [highContrast]);

  // Apply reduced motion
  useEffect(() => {
    if (reducedMotion) {
      document.documentElement.classList.add('reduced-motion');
    } else {
      document.documentElement.classList.remove('reduced-motion');
    }
  }, [reducedMotion]);

  // Apply screen reader optimizations
  useEffect(() => {
    if (screenReaderOptimized) {
      document.documentElement.classList.add('screen-reader-optimized');
    } else {
      document.documentElement.classList.remove('screen-reader-optimized');
    }
  }, [screenReaderOptimized]);

  // Format font size label
  const formatFontSizeLabel = (value: number) => {
    if (value === 1) return 'Normal';
    return value < 1 ? `${Math.round((1 - value) * 100)}% Smaller` : `${Math.round((value - 1) * 100)}% Larger`;
  };

  return (
    <AccessibilityContext.Provider
      value={{
        fontSize,
        highContrast,
        reducedMotion,
        screenReaderOptimized,
        toggleAccessibilityDialog: toggleDialog
      }}
    >
      {children}

      {/* Accessibility button */}
      <AccessibilityButton
        aria-label="Accessibility Settings"
        onClick={toggleDialog}
        size="large"
      >
        <AccessibilityIcon />
      </AccessibilityButton>

      {/* Accessibility dialog */}
      <Dialog
        open={dialogOpen}
        onClose={toggleDialog}
        maxWidth="sm"
        fullWidth
        aria-labelledby="accessibility-dialog-title"
      >
        <DialogTitle id="accessibility-dialog-title">
          <Box display="flex" alignItems="center" justifyContent="space-between">
            <Box display="flex" alignItems="center" gap={1}>
              <AccessibilityIcon color="primary" />
              <Typography variant="h6">Accessibility Settings</Typography>
            </Box>
            <IconButton
              aria-label="close"
              onClick={toggleDialog}
              edge="end"
            >
              <CloseIcon />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent dividers>
          <SettingSection>
            <Box display="flex" alignItems="center" gap={1} mb={1}>
              <TextFieldsIcon color="primary" />
              <Typography variant="subtitle1">Text Size</Typography>
            </Box>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Adjust the size of text throughout the application.
            </Typography>
            <Box px={1} mt={3}>
              <Slider
                value={fontSize}
                min={0.7}
                max={1.5}
                step={0.05}
                onChange={(_, value) => setFontSize(value as number)}
                valueLabelDisplay="auto"
                valueLabelFormat={formatFontSizeLabel}
                aria-labelledby="font-size-slider"
                marks={[
                  { value: 0.7, label: 'Smaller' },
                  { value: 1, label: 'Normal' },
                  { value: 1.5, label: 'Larger' }
                ]}
              />
            </Box>
          </SettingSection>

          <Divider sx={{ my: 2 }} />

          <SettingSection>
            <Box display="flex" alignItems="center" gap={1} mb={1}>
              <ContrastIcon color="primary" />
              <Typography variant="subtitle1">Display Options</Typography>
            </Box>
            <FormControlLabel
              control={
                <Switch
                  checked={highContrast}
                  onChange={(e) => setHighContrast(e.target.checked)}
                  color="primary"
                />
              }
              label={
                <Box>
                  <Typography variant="body1">High Contrast Mode</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Increases contrast for better readability
                  </Typography>
                </Box>
              }
              sx={{ display: 'flex', mb: 2 }}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={reducedMotion}
                  onChange={(e) => setReducedMotion(e.target.checked)}
                  color="primary"
                />
              }
              label={
                <Box>
                  <Typography variant="body1">Reduced Motion</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Minimizes animations and transitions
                  </Typography>
                </Box>
              }
              sx={{ display: 'flex', mb: 2 }}
            />
            <FormControlLabel
              control={
                <Switch
                  checked={screenReaderOptimized}
                  onChange={(e) => setScreenReaderOptimized(e.target.checked)}
                  color="primary"
                />
              }
              label={
                <Box>
                  <Typography variant="body1">Screen Reader Optimized</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Enhances compatibility with screen readers
                  </Typography>
                </Box>
              }
              sx={{ display: 'flex' }}
            />
          </SettingSection>
        </DialogContent>
        <DialogActions>
          <Button 
            startIcon={<RefreshIcon />} 
            onClick={resetSettings}
            color="inherit"
          >
            Reset to Default
          </Button>
          <Button 
            onClick={toggleDialog} 
            variant="contained" 
            color="primary"
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </AccessibilityContext.Provider>
  );
};

export default AccessibilitySettings;
