import React, { useState, useContext } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Divider,
  IconButton,
  Tooltip,
  Typography,
  Collapse,
  useTheme,
  alpha,
  styled
} from '@mui/material';
import {
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  Dashboard as DashboardIcon,
  People as PeopleIcon,
  LocalHospital as DoctorIcon,
  AssignmentInd as PatientIcon,
  Description as DescriptionIcon,
  Timeline as StatisticsIcon,
  SecurityOutlined as SecurityIcon,
  Settings as SettingsIcon,
  Storage as StorageIcon,
  ExpandLess,
  ExpandMore,
  Star as StarIcon,
  VpnKey as PasswordIcon,
  VerifiedUser as MfaIcon,
  Public as IpIcon,
  Api as ApiIcon,
  Cloud as CloudIcon,
  Link as LinkIcon
} from '@mui/icons-material';
import ThemeContext from '../../../context/ThemeContext';
import { useAdminLayout } from '../../../context/AdminLayoutContext';

// Styled components
const SidebarHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: theme.spacing(1, 1.5),
  backgroundColor: alpha(theme.palette.primary.main, 0.08),
  borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`
}));

const SidebarFooter = styled(Box)(({ theme }) => ({
  padding: theme.spacing(1.5),
  borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
  display: 'flex',
  justifyContent: 'center'
}));

// Interface for sidebar items
interface SidebarItem {
  title: string;
  path: string;
  icon: React.ReactNode;
  children?: SidebarItem[];
}

interface AdminSidebarProps {
  width: number;
}

const AdminSidebar: React.FC<AdminSidebarProps> = ({ width }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const location = useLocation();
  const { mode } = useContext(ThemeContext);
  const { sidebarOpen, toggleSidebar } = useAdminLayout();
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>({
    security: false,
    integrations: false,
  });

  // Toggle expanded state for items with children
  const toggleExpand = (key: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Check if a path is active
  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(`${path}/`);
  };

  // Sidebar items configuration
  const sidebarItems: SidebarItem[] = [
    {
      title: 'Dashboard',
      path: '/admin',
      icon: <DashboardIcon />
    },
    {
      title: 'Enhanced Dashboard',
      path: '/admin_exp',
      icon: <StarIcon />
    },
    {
      title: 'User Management',
      path: '/admin/users',
      icon: <PeopleIcon />
    },
    {
      title: 'Doctor Management',
      path: '/admin/doctors',
      icon: <DoctorIcon />
    },
    {
      title: 'Patient Management',
      path: '/admin/patients',
      icon: <PatientIcon />
    },
    {
      title: 'Clinical Guidance',
      path: '/admin/clinical-guidance',
      icon: <DescriptionIcon />
    },
    {
      title: 'System Statistics',
      path: '/admin/statistics',
      icon: <StatisticsIcon />
    },
    {
      title: 'Integrations',
      path: '#',
      icon: <ApiIcon />,
      children: [
        {
          title: 'Integration Dashboard',
          path: '/admin/integrations',
          icon: <CloudIcon />
        },
        {
          title: 'API Management',
          path: '/admin/integrations/api-management',
          icon: <ApiIcon />
        },
        {
          title: 'External Systems',
          path: '/admin/integrations/external-systems',
          icon: <LinkIcon />
        }
      ]
    },
    {
      title: 'Security & Settings',
      path: '#',
      icon: <SecurityIcon />,
      children: [
        {
          title: 'Security & Audit',
          path: '/admin/security',
          icon: <SecurityIcon />
        },
        {
          title: 'Password Policy',
          path: '/admin/password-policy',
          icon: <PasswordIcon />
        },
        {
          title: 'MFA Management',
          path: '/admin/mfa-management',
          icon: <MfaIcon />
        },
        {
          title: 'IP Restrictions',
          path: '/admin/ip-restrictions',
          icon: <IpIcon />
        }
      ]
    },
    {
      title: 'System Settings',
      path: '/admin/settings',
      icon: <SettingsIcon />
    },
    {
      title: 'Database Debug',
      path: '/admin/database-debug',
      icon: <StorageIcon />
    }
  ];

  // Render sidebar items
  const renderSidebarItems = (items: SidebarItem[]) => {
    return items.map((item) => {
      const hasChildren = item.children && item.children.length > 0;
      const isItemActive = isActive(item.path);

      // For items with children, check if any child is active
      const isGroupActive = hasChildren && item.children?.some(child => isActive(child.path));

      if (hasChildren) {
        return (
          <React.Fragment key={item.title}>
            <ListItem disablePadding>
              <ListItemButton
                onClick={() => toggleExpand(item.title.toLowerCase())}
                sx={{
                  pl: 2,
                  pr: 1,
                  py: 1,
                  bgcolor: isGroupActive ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
                  '&:hover': {
                    bgcolor: alpha(theme.palette.primary.main, 0.08)
                  }
                }}
              >
                <ListItemIcon sx={{ minWidth: 40, color: isGroupActive ? theme.palette.primary.main : 'inherit' }}>
                  {item.icon}
                </ListItemIcon>
                <ListItemText
                  primary={item.title}
                  primaryTypographyProps={{
                    fontSize: '0.9rem',
                    fontWeight: isGroupActive ? 500 : 400,
                    color: isGroupActive ? theme.palette.primary.main : 'inherit'
                  }}
                />
                {expandedItems[item.title.toLowerCase()] ? <ExpandLess /> : <ExpandMore />}
              </ListItemButton>
            </ListItem>
            <Collapse in={expandedItems[item.title.toLowerCase()]} timeout="auto" unmountOnExit>
              <List component="div" disablePadding>
                {item.children?.map((child) => (
                  <ListItemButton
                    key={child.title}
                    onClick={() => navigate(child.path)}
                    selected={isActive(child.path)}
                    sx={{
                      pl: 4,
                      py: 0.75,
                      '&.Mui-selected': {
                        bgcolor: alpha(theme.palette.primary.main, 0.15),
                        '&:hover': {
                          bgcolor: alpha(theme.palette.primary.main, 0.2)
                        }
                      }
                    }}
                  >
                    <ListItemIcon sx={{ minWidth: 36, color: isActive(child.path) ? theme.palette.primary.main : 'inherit' }}>
                      {child.icon}
                    </ListItemIcon>
                    <ListItemText
                      primary={child.title}
                      primaryTypographyProps={{
                        fontSize: '0.85rem',
                        fontWeight: isActive(child.path) ? 500 : 400,
                        color: isActive(child.path) ? theme.palette.primary.main : 'inherit'
                      }}
                    />
                  </ListItemButton>
                ))}
              </List>
            </Collapse>
          </React.Fragment>
        );
      }

      return (
        <ListItem key={item.title} disablePadding>
          <ListItemButton
            onClick={() => navigate(item.path)}
            selected={isItemActive}
            sx={{
              pl: 2,
              py: 1,
              '&.Mui-selected': {
                bgcolor: alpha(theme.palette.primary.main, 0.15),
                '&:hover': {
                  bgcolor: alpha(theme.palette.primary.main, 0.2)
                }
              }
            }}
          >
            <ListItemIcon sx={{ minWidth: 40, color: isItemActive ? theme.palette.primary.main : 'inherit' }}>
              {item.icon}
            </ListItemIcon>
            <ListItemText
              primary={item.title}
              primaryTypographyProps={{
                fontSize: '0.9rem',
                fontWeight: isItemActive ? 500 : 400,
                color: isItemActive ? theme.palette.primary.main : 'inherit'
              }}
            />
          </ListItemButton>
        </ListItem>
      );
    });
  };

  return (
    <Drawer
      variant="permanent"
      open={sidebarOpen}
      sx={{
        width: sidebarOpen ? width : theme.spacing(7),
        flexShrink: 0,
        position: 'fixed', // Change to fixed positioning
        top: '64px', // Position below the navbar
        left: 0,
        height: 'calc(100% - 64px)', // Adjust height to account for navbar
        zIndex: 1000, // Ensure proper z-index
        '& .MuiDrawer-paper': {
          position: 'static', // Change to static positioning
          width: sidebarOpen ? width : theme.spacing(7),
          boxSizing: 'border-box',
          bgcolor: mode === 'dark' ? alpha('#1A1A1A', 0.98) : alpha('#FFFFFF', 0.98),
          borderRight: `1px solid ${alpha(theme.palette.divider, mode === 'dark' ? 0.2 : 0.1)}`,
          overflowX: 'hidden',
          overflowY: 'auto',
          height: '100%',
          transition: theme.transitions.create(['width'], {
            easing: theme.transitions.easing.sharp,
            duration: theme.transitions.duration.enteringScreen,
          }),
          '& .MuiListItem-root': {
            py: 0.5,
          },
          '& .MuiListItemButton-root': {
            py: 0.75,
            minHeight: '40px',
          }
        },
      }}
    >
      <SidebarHeader>
        {sidebarOpen ? (
          <>
            <Typography variant="subtitle1" fontWeight="medium" color="primary">
              Admin Console
            </Typography>
            <IconButton onClick={toggleSidebar} size="small">
              {theme.direction === 'ltr' ? <ChevronLeftIcon /> : <ChevronRightIcon />}
            </IconButton>
          </>
        ) : (
          <Tooltip title="Expand Sidebar" placement="right">
            <IconButton onClick={toggleSidebar} size="small">
              {theme.direction === 'ltr' ? <ChevronRightIcon /> : <ChevronLeftIcon />}
            </IconButton>
          </Tooltip>
        )}
      </SidebarHeader>
      <Divider />
      <List sx={{ pt: 1 }}>
        {renderSidebarItems(sidebarItems)}
      </List>
      <Box sx={{ flexGrow: 1 }} />
      <SidebarFooter>
        {sidebarOpen && (
          <Typography variant="caption" color="text.secondary">
            WiseAge Wellness Admin v1.0
          </Typography>
        )}
      </SidebarFooter>
    </Drawer>
  );
};

export default AdminSidebar;
