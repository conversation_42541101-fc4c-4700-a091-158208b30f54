import React, { useContext } from 'react';
import { Box, useTheme, alpha, styled } from '@mui/material';
import AdminSidebar from './AdminSidebar';
import AdminBreadcrumbs from './AdminBreadcrumbs';
import AccessibilitySettings from './AccessibilitySettings';
import ThemeContext from '../../../context/ThemeContext';
import { useAdminLayout } from '../../../context/AdminLayoutContext';

// Styled components
const MainContent = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'open' && prop !== 'sidebarWidth'
})<{
  open?: boolean;
  sidebarWidth: number;
}>(({ theme, open, sidebarWidth }) => ({
  flexGrow: 1,
  padding: theme.spacing(2),
  paddingTop: theme.spacing(1), // Reduced top padding
  transition: theme.transitions.create('margin', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  marginLeft: open ? sidebarWidth : theme.spacing(7),
  width: `calc(100% - ${open ? sidebarWidth : theme.spacing(7)}px)`,
  maxWidth: '100%',
  overflowX: 'hidden',
}));

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const theme = useTheme();
  const { mode } = useContext(ThemeContext);
  const { sidebarOpen, toggleSidebar } = useAdminLayout();
  const sidebarWidth = 240;

  return (
    <AccessibilitySettings>
      <Box sx={{
        display: 'flex',
        minHeight: 'calc(100vh - 64px)', // Adjust height to account for navbar
        position: 'relative', // Add position relative
        marginTop: '64px' // Add margin to account for the navbar height
      }}>
        {/* Sidebar */}
        <AdminSidebar
          width={sidebarWidth}
        />

        {/* Main content */}
        <MainContent
          open={sidebarOpen}
          sidebarWidth={sidebarWidth}
          sx={{
            bgcolor: mode === 'dark' ? alpha('#121212', 0.98) : alpha('#F8FAF8', 0.98),
          }}
        >
          {/* Breadcrumbs */}
          <AdminBreadcrumbs />

          {/* Page content */}
          {children}
        </MainContent>
      </Box>
    </AccessibilitySettings>
  );
};

export default AdminLayout;
