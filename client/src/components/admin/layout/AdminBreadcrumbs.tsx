import React from 'react';
import { Link as RouterLink, useLocation } from 'react-router-dom';
import {
  Breadcrumbs,
  Link,
  Typography,
  Box,
  useTheme,
  alpha,
  styled
} from '@mui/material';
import {
  Home as HomeIcon,
  NavigateNext as NavigateNextIcon
} from '@mui/icons-material';

// Styled components
const BreadcrumbsContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(1, 2),
  marginBottom: theme.spacing(2),
  backgroundColor: alpha(theme.palette.background.paper, 0.6),
  borderRadius: theme.shape.borderRadius,
  boxShadow: `0 1px 3px ${alpha(theme.palette.divider, 0.1)}`,
  display: 'flex',
  alignItems: 'center'
}));

// Interface for route mapping
interface RouteMapping {
  [key: string]: {
    label: string;
    icon?: React.ReactNode;
  };
}

const AdminBreadcrumbs: React.FC = () => {
  const theme = useTheme();
  const location = useLocation();
  const pathnames = location.pathname.split('/').filter((x) => x);

  // Route mapping for breadcrumb labels
  const routeMapping: RouteMapping = {
    admin: { label: 'Admin Dashboard', icon: <HomeIcon fontSize="small" /> },
    admin_exp: { label: 'Enhanced Dashboard' },
    users: { label: 'User Management' },
    doctors: { label: 'Doctor Management' },
    patients: { label: 'Patient Management' },
    'clinical-guidance': { label: 'Clinical Guidance' },
    statistics: { label: 'System Statistics' },
    integrations: { label: 'Integration Dashboard' },
    'api-management': { label: 'API Management' },
    'external-systems': { label: 'External Systems' },
    security: { label: 'Security & Audit' },
    'password-policy': { label: 'Password Policy' },
    'mfa-management': { label: 'MFA Management' },
    'ip-restrictions': { label: 'IP Restrictions' },
    settings: { label: 'System Settings' },
    'database-debug': { label: 'Database Debug' }
  };

  // Generate breadcrumb items
  const breadcrumbItems = pathnames.map((path, index) => {
    const isLast = index === pathnames.length - 1;
    const routeKey = path === 'admin' ? path : pathnames.slice(0, index + 1).join('/');
    const routeInfo = routeMapping[routeKey] || routeMapping[path] || { label: path.charAt(0).toUpperCase() + path.slice(1) };
    const to = `/${pathnames.slice(0, index + 1).join('/')}`;

    if (isLast) {
      return (
        <Typography
          key={to}
          color="text.primary"
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 0.5,
            fontWeight: 500
          }}
        >
          {routeInfo.icon && routeInfo.icon}
          {routeInfo.label}
        </Typography>
      );
    }

    return (
      <Link
        key={to}
        component={RouterLink}
        to={to}
        color="inherit"
        sx={{
          display: 'flex',
          alignItems: 'center',
          gap: 0.5,
          textDecoration: 'none',
          '&:hover': {
            textDecoration: 'underline',
            color: theme.palette.primary.main
          }
        }}
      >
        {routeInfo.icon && routeInfo.icon}
        {routeInfo.label}
      </Link>
    );
  });

  // If we're at the root admin path, don't show breadcrumbs
  if (pathnames.length === 1 && pathnames[0] === 'admin') {
    return null;
  }

  return (
    <BreadcrumbsContainer>
      <Breadcrumbs
        separator={<NavigateNextIcon fontSize="small" />}
        aria-label="breadcrumb"
      >
        {/* Always include home link */}
        <Link
          component={RouterLink}
          to="/admin"
          color="inherit"
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 0.5,
            textDecoration: 'none',
            '&:hover': {
              textDecoration: 'underline',
              color: theme.palette.primary.main
            }
          }}
        >
          <HomeIcon fontSize="small" />
          Admin
        </Link>
        {breadcrumbItems}
      </Breadcrumbs>
    </BreadcrumbsContainer>
  );
};

export default AdminBreadcrumbs;
