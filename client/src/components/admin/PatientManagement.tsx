import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Typography,
  Box,
  TablePagination,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Snackbar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  SelectChangeEvent,
  InputAdornment,
  Chip,
  IconButton,
  Avatar,
  Tooltip,
  Divider
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Person as PersonIcon,
  GetApp as GetAppIcon,
  Search as SearchIcon,
  VpnKey as VpnKeyIcon,
  Sync as SyncIcon,
  Visibility as VisibilityIcon,
  AssignmentInd as AssignmentIndIcon,
  AccessibilityNew as PatientIcon,
  ArrowUpward as ArrowUpIcon,
  ArrowDownward as ArrowDownIcon
} from '@mui/icons-material';
import { API_URL } from '../../config';

interface Patient {
  patient_id: number;
  first_name: string;
  last_name: string;
  unique_id: string;
  date_of_birth: string | null;
  gender: string;
  phone: string;
  email: string | null;
  doctor_id: number | null;
  doctor_name?: string | null;
  blood_type?: string | null;
  allergies?: string | null;
  created_at?: string | null;
  user_id?: number | null;
}

interface Doctor {
  doctor_id: number;
  first_name: string;
  last_name: string;
  specialty: string | null;
}

// Helper function to calculate age
const calculateAge = (dateOfBirth: string | null | undefined): number => {
  if (!dateOfBirth) return 0;

  try {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);

    // Check if the date is valid
    if (isNaN(birthDate.getTime())) {
      console.error('Invalid birth date:', dateOfBirth);
      return 0;
    }

    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDifference = today.getMonth() - birthDate.getMonth();

    if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  } catch (error) {
    console.error('Error calculating age:', error, 'Input was:', dateOfBirth);
    return 0;
  }
};

const PatientManagement: React.FC = () => {
  const navigate = useNavigate();

  // Note: We're displaying MedID (unique_id) instead of patient_id in the UI
  // The patient_id is still used internally for API calls and is the database primary key
  // The unique_id (MedID) is a more user-friendly identifier for patients

  const [patients, setPatients] = useState<Patient[]>([]);
  const [filteredPatients, setFilteredPatients] = useState<Patient[]>([]);
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [assignDoctorDialogOpen, setAssignDoctorDialogOpen] = useState(false);
  const [createPatientDialogOpen, setCreatePatientDialogOpen] = useState(false);
  const [viewDetailsDialogOpen, setViewDetailsDialogOpen] = useState(false);
  const [generateMedIdDialogOpen, setGenerateMedIdDialogOpen] = useState(false);
  const [standardizeMedIdDialogOpen, setStandardizeMedIdDialogOpen] = useState(false);
  const [selectedDoctor, setSelectedDoctor] = useState<Doctor | null>(null);

  // Sorting state
  type SortField = 'age' | 'gender' | 'doctor';
  type SortDirection = 'asc' | 'desc';
  const [sortField, setSortField] = useState<SortField | null>(null);
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'info' | 'warning' | 'error';
  }>({
    open: false,
    message: '',
    severity: 'info'
  });
  const [newPatient, setNewPatient] = useState({
    first_name: '',
    last_name: '',
    date_of_birth: '',
    gender: 'male',
    phone: '',
    email: '',
    doctor_id: ''
  });

  // Fetch patients and doctors on component mount
  useEffect(() => {
    fetchPatients();
    fetchDoctors();
  }, []);

  // Handle sorting
  const handleSort = (field: SortField) => {
    // If clicking the same field, toggle direction
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // If clicking a new field, set it as the sort field and default to ascending
      setSortField(field);
      setSortDirection('asc');
    }

    // Reset to first page when sorting
    setPage(0);
  };

  // Apply sorting to patients
  const sortPatients = useCallback((patients: Patient[]) => {
    if (!sortField) return patients;

    return [...patients].sort((a, b) => {
      let comparison = 0;

      if (sortField === 'age') {
        const ageA = calculateAge(a.date_of_birth);
        const ageB = calculateAge(b.date_of_birth);
        comparison = ageA - ageB;
      }
      else if (sortField === 'gender') {
        const genderA = a.gender || '';
        const genderB = b.gender || '';
        comparison = genderA.localeCompare(genderB);
      }
      else if (sortField === 'doctor') {
        const doctorA = a.doctor_name || '';
        const doctorB = b.doctor_name || '';
        comparison = doctorA.localeCompare(doctorB);
      }

      // Reverse the comparison if sorting in descending order
      return sortDirection === 'asc' ? comparison : -comparison;
    });
    // calculateAge is an outer scope function and doesn't need to be in the dependency array
  }, [sortField, sortDirection]);

  // Filter patients based on search term and apply sorting
  useEffect(() => {
    let filtered = patients;

    // Apply search filter
    if (searchTerm.trim()) {
      const lowercasedTerm = searchTerm.toLowerCase();
      filtered = patients.filter(patient => {
        return (
          patient.first_name.toLowerCase().includes(lowercasedTerm) ||
          patient.last_name.toLowerCase().includes(lowercasedTerm) ||
          patient.unique_id.toLowerCase().includes(lowercasedTerm) ||
          (patient.email && patient.email.toLowerCase().includes(lowercasedTerm)) ||
          (patient.doctor_name && patient.doctor_name.toLowerCase().includes(lowercasedTerm))
        );
      });
    }

    // Apply sorting
    const sortedPatients = sortPatients(filtered);
    setFilteredPatients(sortedPatients);
  }, [searchTerm, patients, sortField, sortDirection, sortPatients]);

  // Fetch patients from API
  const fetchPatients = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(`${API_URL}/api/patients`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch patients');
      }

      const data = await response.json();
      setPatients(data);
      setFilteredPatients(data);

      // Check for patients without MedIDs
      const patientsWithoutMedId = data.filter((patient: Patient) => !patient.unique_id).length;
      if (patientsWithoutMedId > 0) {
        setNotification({
          open: true,
          message: `Found ${patientsWithoutMedId} patient(s) without MedID. Please click "Generate Missing MedIDs" to assign them.`,
          severity: 'warning'
        });
      }
    } catch (err) {
      console.error('Error fetching patients:', err);
      setError(err instanceof Error ? err.message : 'An error occurred while fetching patients');
    } finally {
      setLoading(false);
    }
  };

  // Fetch doctors from API
  const fetchDoctors = async () => {
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(`${API_URL}/api/doctors`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch doctors');
      }

      const data = await response.json();
      setDoctors(data);
    } catch (err) {
      console.error('Error fetching doctors:', err);
    }
  };

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Handle page change
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Open delete dialog
  const handleOpenDeleteDialog = (patient: Patient) => {
    setSelectedPatient(patient);
    setDeleteDialogOpen(true);
  };

  // Open assign doctor dialog
  const handleOpenAssignDialog = (patient: Patient) => {
    setSelectedPatient(patient);
    // Find the doctor object from the doctors array using the patient's doctor_id
    const doctorObj = patient.doctor_id ? doctors.find(d => d.doctor_id === patient.doctor_id) || null : null;
    setSelectedDoctor(doctorObj);
    setAssignDoctorDialogOpen(true);
  };



  // Open view details dialog
  const handleOpenDetailsDialog = (patient: Patient) => {
    setSelectedPatient(patient);
    setViewDetailsDialogOpen(true);
  };

  // Open generate MedID dialog
  const handleOpenGenerateMedIdDialog = () => {
    setGenerateMedIdDialogOpen(true);
  };

  // Open standardize MedID dialog
  const handleOpenStandardizeMedIdDialog = () => {
    setStandardizeMedIdDialogOpen(true);
  };

  // Close dialogs
  const handleCloseDialogs = () => {
    setDeleteDialogOpen(false);
    setAssignDoctorDialogOpen(false);
    setCreatePatientDialogOpen(false);
    setViewDetailsDialogOpen(false);
    setGenerateMedIdDialogOpen(false);
    setStandardizeMedIdDialogOpen(false);
    setSelectedPatient(null);
    setSelectedDoctor(null);
    setNewPatient({
      first_name: '',
      last_name: '',
      date_of_birth: '',
      gender: 'male',
      phone: '',
      email: '',
      doctor_id: ''
    });
  };

  // Delete patient
  const handleDeletePatient = async () => {
    if (!selectedPatient) return;

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('Authentication token not found');
      }

      const response = await fetch(`${API_URL}/api/patients/${selectedPatient.patient_id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error('Failed to delete patient');
      }

      // Remove patient from state
      setPatients(prevPatients =>
        prevPatients.filter(p => p.patient_id !== selectedPatient.patient_id)
      );

      setNotification({
        open: true,
        message: `Patient ${selectedPatient.first_name} ${selectedPatient.last_name} deleted successfully`,
        severity: 'success'
      });

      handleCloseDialogs();
    } catch (err) {
      console.error('Error deleting patient:', err);
      setNotification({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to delete patient',
        severity: 'error'
      });
    }
  };

  // Assign doctor to patient
  const handleAssignDoctor = async () => {
    if (!selectedPatient || !selectedDoctor) return;

    try {
      setLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/admin/patients/${selectedPatient.patient_id}/assign-doctor`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: JSON.stringify({ doctor_id: selectedDoctor.doctor_id })
      });

      if (!response.ok) {
        throw new Error('Failed to assign doctor');
      }

      // Update patient in state
      const updatedPatient = await response.json();
      setPatients(prevPatients =>
        prevPatients.map(p =>
          p.patient_id === selectedPatient.patient_id ? updatedPatient : p
        )
      );

      setNotification({
        open: true,
        message: `Doctor assigned to ${selectedPatient.first_name} ${selectedPatient.last_name} successfully`,
        severity: 'success'
      });

      handleCloseDialogs();
    } catch (err) {
      console.error('Error assigning doctor:', err);
      setNotification({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to assign doctor',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle input change for new patient form
  const handleNewPatientChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement> | SelectChangeEvent
  ) => {
    const { name, value } = e.target;
    setNewPatient({
      ...newPatient,
      [name as string]: value
    });
  };

  // Format date
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'N/A';

    try {
      // If the date string contains time information, extract just the date part
      let dateToFormat = dateString;

      if (dateString.includes(' ')) {
        // Handle format like "2025-04-28 09:00:00"
        dateToFormat = dateString.split(' ')[0];
      } else if (dateString.includes('T')) {
        // Handle ISO format like "2025-04-28T09:00:00Z"
        dateToFormat = dateString.split('T')[0];
      }

      const date = new Date(dateToFormat);

      // Check if the date is valid
      if (isNaN(date.getTime())) {
        console.error('Invalid date:', dateString);
        return 'Invalid date';
      }

      return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      }).format(date);
    } catch (error) {
      console.error('Error formatting date:', error, 'Input was:', dateString);
      return 'Invalid date';
    }
  };

  // Close notification
  const handleCloseNotification = () => {
    setNotification({
      ...notification,
      open: false
    });
  };

  // Add patient
  const handleCreatePatient = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      // Check for required fields
      if (!newPatient.first_name || !newPatient.last_name || !newPatient.date_of_birth) {
        throw new Error('First name, last name, and date of birth are required');
      }

      // Convert doctor_id to number if selected
      const patientData = {
        ...newPatient,
        doctor_id: newPatient.doctor_id ? parseInt(newPatient.doctor_id) : null
      };

      const response = await fetch(`${API_URL}/api/patients`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: JSON.stringify(patientData)
      });

      if (!response.ok) {
        throw new Error('Failed to create patient');
      }

      const createdPatient = await response.json();

      // Add to patients list
      setPatients([createdPatient, ...patients]);

      // Close dialog and reset form
      handleCloseDialogs();

      // Show success notification
      setNotification({
        open: true,
        message: `Patient ${createdPatient.first_name} ${createdPatient.last_name} created successfully with MedID: ${createdPatient.unique_id}`,
        severity: 'success'
      });
    } catch (err) {
      console.error('Error creating patient:', err);
      setNotification({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to create patient',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };



  // Export patients to CSV
  const exportPatientsToCSV = () => {
    // Define the headers
    const headers = [
      'MedID',
      'First Name',
      'Last Name',
      'Date of Birth',
      'Age',
      'Gender',
      'Phone',
      'Email',
      'Assigned Doctor'
    ];

    // Map the patient data to CSV format
    const data = patients.map(patient => [
      patient.unique_id,
      patient.first_name,
      patient.last_name,
      formatDate(patient.date_of_birth),
      calculateAge(patient.date_of_birth),
      patient.gender,
      patient.phone || '',
      patient.email || '',
      patient.doctor_name || ''
    ]);

    // Combine headers and data
    const csvContent = [
      headers.join(','),
      ...data.map(row => row.join(','))
    ].join('\n');

    // Create a blob and download link
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `patients_export_${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();

    // Clean up
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    // Show notification
    setNotification({
      open: true,
      message: `Exported ${patients.length} patients to CSV`,
      severity: 'success'
    });
  };

  // Generate MedIDs for patients who don't have them
  const generateMissingMedIDs = async () => {
    handleCloseDialogs();
    try {
      setLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/admin/patients/generate-missing-medids`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error('Failed to generate MedIDs');
      }

      const result = await response.json();

      // Show notification
      setNotification({
        open: true,
        message: result.msg,
        severity: 'success'
      });

      // Refresh the patient list
      if (result.count > 0) {
        await fetchPatients();
      }
    } catch (err) {
      console.error('Error generating MedIDs:', err);
      setNotification({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to generate MedIDs',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  // Standardize all patient MedIDs to P-format
  const standardizeMedIDs = async () => {
    handleCloseDialogs();
    try {
      setLoading(true);
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/admin/patients/standardize-medids`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });

      if (!response.ok) {
        throw new Error('Failed to standardize MedIDs');
      }

      const result = await response.json();

      // Show notification
      setNotification({
        open: true,
        message: result.msg,
        severity: 'success'
      });

      // Refresh the patient list
      if (result.count > 0) {
        await fetchPatients();
      }
    } catch (err) {
      console.error('Error standardizing MedIDs:', err);
      setNotification({
        open: true,
        message: err instanceof Error ? err.message : 'Failed to standardize MedIDs',
        severity: 'error'
      });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="60vh">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header Section */}
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 4,
          borderRadius: 2,
          background: 'linear-gradient(135deg, #ff9800, #ed6c02)',
          color: 'white',
          position: 'relative',
          overflow: 'hidden',
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)'
        }}
      >
        <Box sx={{
          position: 'absolute',
          top: 0,
          right: 0,
          width: '40%',
          height: '100%',
          opacity: 0.07,
          background: `url('https://www.transparenttextures.com/patterns/cubes.png')`,
          backgroundSize: 'cover'
        }} />

        <Grid container spacing={3} alignItems="center">
          <Grid item>
            <Avatar
              sx={{
                width: 64,
                height: 64,
                bgcolor: 'white',
                color: '#ff9800',
                boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
                border: '2px solid rgba(255,255,255,0.8)',
                transition: 'transform 0.2s ease-in-out',
                '&:hover': {
                  transform: 'scale(1.05)'
                }
              }}
            >
              <PatientIcon fontSize="large" />
            </Avatar>
          </Grid>
          <Grid item xs>
            <Typography variant="h4" fontWeight="600" sx={{
              textShadow: '0 1px 2px rgba(0,0,0,0.1)',
              letterSpacing: '0.5px'
            }}>
              Patient Management
            </Typography>
            <Typography variant="subtitle1" sx={{
              opacity: 0.95,
              mt: 0.5,
              maxWidth: '90%',
              lineHeight: 1.4
            }}>
              Manage all patients in the system, assign doctors, and view patient details.
            </Typography>
            <Box sx={{ mt: 1.5, display: 'flex', gap: 1.5 }}>
              <Chip
                label={`${patients.length} Total Patients`}
                size="small"
                sx={{
                  bgcolor: 'rgba(255,255,255,0.2)',
                  color: 'white',
                  fontWeight: 500,
                  '& .MuiChip-label': { px: 1.5 }
                }}
              />
              {patients.filter(p => !p.doctor_id).length > 0 && (
                <Chip
                  label={`${patients.filter(p => !p.doctor_id).length} Unassigned`}
                  size="small"
                  sx={{
                    bgcolor: 'rgba(255,255,255,0.15)',
                    color: 'white',
                    fontWeight: 500,
                    '& .MuiChip-label': { px: 1.5 }
                  }}
                />
              )}
            </Box>
          </Grid>
          <Grid item>
            <Tooltip title="Add a new patient to the system" arrow placement="left">
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => window.location.href = '/patients/new'}
                size="medium"
                sx={{
                  bgcolor: 'white',
                  color: '#ff9800',
                  fontWeight: 600,
                  px: 2.5,
                  py: 1,
                  boxShadow: '0 4px 8px rgba(0,0,0,0.15)',
                  '&:hover': {
                    bgcolor: 'rgba(255,255,255,0.9)',
                    boxShadow: '0 6px 12px rgba(0,0,0,0.2)'
                  },
                  transition: 'all 0.2s ease'
                }}
              >
                Add Patient
              </Button>
            </Tooltip>
          </Grid>
        </Grid>
      </Paper>

      {error && (
        <Alert
          severity="error"
          sx={{
            mb: 3,
            borderRadius: 2,
            boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
          }}
        >
          {error}
        </Alert>
      )}

      {/* Search and Actions Section */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={5}>
          <Paper
            elevation={0}
            sx={{
              p: 2.5,
              borderRadius: 2,
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
              height: '100%',
              display: 'flex',
              flexDirection: 'column'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="subtitle1" fontWeight="600" sx={{ color: 'text.primary' }}>
                Search Patients
              </Typography>
              <Tooltip title="Number of patients matching your search" arrow>
                <Chip
                  icon={<PersonIcon fontSize="small" />}
                  label={`${filteredPatients.length} patients`}
                  color="primary"
                  size="small"
                  variant="outlined"
                  sx={{
                    fontWeight: 500,
                    borderRadius: 1.5,
                    '& .MuiChip-icon': { fontSize: '0.875rem' }
                  }}
                />
              </Tooltip>
            </Box>

            <TextField
              placeholder="Search by name, MedID, or doctor"
              variant="outlined"
              size="small"
              fullWidth
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon color="action" />
                  </InputAdornment>
                ),
                endAdornment: searchTerm ? (
                  <InputAdornment position="end">
                    <IconButton
                      size="small"
                      onClick={() => setSearchTerm('')}
                      edge="end"
                      aria-label="clear search"
                      sx={{ mr: -0.5 }}
                    >
                      <RefreshIcon fontSize="small" />
                    </IconButton>
                  </InputAdornment>
                ) : null
              }}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  transition: 'all 0.2s ease',
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme => theme.palette.primary.main,
                    borderWidth: '1px'
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: theme => theme.palette.primary.main,
                    boxShadow: '0 0 0 3px rgba(25, 118, 210, 0.08)'
                  }
                }
              }}
            />

            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 2 }}>
              {searchTerm && (
                <Chip
                  label={`Searching: ${searchTerm}`}
                  onDelete={() => setSearchTerm('')}
                  size="small"
                  color="primary"
                  variant="outlined"
                  sx={{
                    borderRadius: 1.5,
                    '& .MuiChip-deleteIcon': {
                      color: 'primary.main',
                      '&:hover': {
                        color: 'primary.dark'
                      }
                    }
                  }}
                />
              )}

              {sortField && (
                <Chip
                  icon={sortDirection === 'asc' ? <ArrowUpIcon fontSize="small" /> : <ArrowDownIcon fontSize="small" />}
                  label={`Sorted by: ${sortField === 'age' ? 'Age' : sortField === 'gender' ? 'Gender' : 'Doctor'}`}
                  onDelete={() => {
                    setSortField(null);
                    setSortDirection('asc');
                  }}
                  size="small"
                  color="primary"
                  variant="outlined"
                  sx={{
                    borderRadius: 1.5,
                    '& .MuiChip-deleteIcon': {
                      color: 'primary.main',
                      '&:hover': {
                        color: 'primary.dark'
                      }
                    }
                  }}
                />
              )}
            </Box>

            <Box sx={{ mt: 'auto', pt: 2 }}>
              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
                Sort by:
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Chip
                  label="Age"
                  size="small"
                  variant={sortField === 'age' ? 'filled' : 'outlined'}
                  color={sortField === 'age' ? 'primary' : 'default'}
                  onClick={() => handleSort('age')}
                  icon={sortField === 'age' ? (sortDirection === 'asc' ? <ArrowUpIcon fontSize="small" /> : <ArrowDownIcon fontSize="small" />) : undefined}
                  sx={{
                    borderRadius: 1.5,
                    '& .MuiChip-label': { px: 1 }
                  }}
                />
                <Chip
                  label="Gender"
                  size="small"
                  variant={sortField === 'gender' ? 'filled' : 'outlined'}
                  color={sortField === 'gender' ? 'primary' : 'default'}
                  onClick={() => handleSort('gender')}
                  icon={sortField === 'gender' ? (sortDirection === 'asc' ? <ArrowUpIcon fontSize="small" /> : <ArrowDownIcon fontSize="small" />) : undefined}
                  sx={{
                    borderRadius: 1.5,
                    '& .MuiChip-label': { px: 1 }
                  }}
                />
                <Chip
                  label="Doctor"
                  size="small"
                  variant={sortField === 'doctor' ? 'filled' : 'outlined'}
                  color={sortField === 'doctor' ? 'primary' : 'default'}
                  onClick={() => handleSort('doctor')}
                  icon={sortField === 'doctor' ? (sortDirection === 'asc' ? <ArrowUpIcon fontSize="small" /> : <ArrowDownIcon fontSize="small" />) : undefined}
                  sx={{
                    borderRadius: 1.5,
                    '& .MuiChip-label': { px: 1 }
                  }}
                />
              </Box>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={7}>
          <Paper
            elevation={0}
            sx={{
              p: 2,
              borderRadius: 2,
              boxShadow: '0 4px 12px rgba(0,0,0,0.05)',
              height: '100%'
            }}
          >
            <Typography variant="subtitle1" fontWeight="500" sx={{ mb: 2 }}>
              Quick Actions
            </Typography>

            {/* Group 1: Data Management */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1, fontWeight: 500 }}>
                Data Management
              </Typography>
              <Grid container spacing={1.5}>
                <Grid item xs={6}>
                  <Tooltip title="Refresh patient data from the database" arrow placement="top">
                    <Button
                      variant="outlined"
                      startIcon={<RefreshIcon />}
                      onClick={fetchPatients}
                      size="small"
                      color="primary"
                      fullWidth
                      sx={{
                        borderRadius: 2,
                        justifyContent: 'flex-start',
                        textAlign: 'left',
                        py: 1,
                        boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
                        '&:hover': {
                          boxShadow: '0 2px 5px rgba(0,0,0,0.1)',
                        }
                      }}
                    >
                      Refresh Data
                    </Button>
                  </Tooltip>
                </Grid>
                <Grid item xs={6}>
                  <Tooltip title="Export patient data to CSV file for reporting or analysis" arrow placement="top">
                    <Button
                      variant="outlined"
                      startIcon={<GetAppIcon />}
                      onClick={exportPatientsToCSV}
                      size="small"
                      color="primary"
                      fullWidth
                      sx={{
                        borderRadius: 2,
                        justifyContent: 'flex-start',
                        textAlign: 'left',
                        py: 1,
                        boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
                        '&:hover': {
                          boxShadow: '0 2px 5px rgba(0,0,0,0.1)',
                        }
                      }}
                    >
                      Export to CSV
                    </Button>
                  </Tooltip>
                </Grid>
              </Grid>
            </Box>

            {/* Group 2: Patient ID Management */}
            <Box>
              <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1, fontWeight: 500 }}>
                Patient ID Management
              </Typography>
              <Grid container spacing={1.5}>
                <Grid item xs={6}>
                  <Tooltip title="Format all patient MedIDs to follow a consistent pattern" arrow placement="top">
                    <Button
                      variant="outlined"
                      startIcon={<SyncIcon />}
                      onClick={handleOpenStandardizeMedIdDialog}
                      size="small"
                      color="warning"
                      fullWidth
                      sx={{
                        borderRadius: 2,
                        justifyContent: 'flex-start',
                        textAlign: 'left',
                        py: 1,
                        boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
                        '&:hover': {
                          boxShadow: '0 2px 5px rgba(0,0,0,0.1)',
                        }
                      }}
                    >
                      Format MedIDs
                    </Button>
                  </Tooltip>
                </Grid>
                <Grid item xs={6}>
                  {patients.filter((p) => !p.unique_id).length > 0 ? (
                    <Tooltip title={`Generate MedIDs for ${patients.filter((p) => !p.unique_id).length} patients missing IDs`} arrow placement="top">
                      <Button
                        variant="contained"
                        startIcon={<VpnKeyIcon />}
                        onClick={handleOpenGenerateMedIdDialog}
                        size="small"
                        color="warning"
                        fullWidth
                        sx={{
                          borderRadius: 2,
                          justifyContent: 'flex-start',
                          textAlign: 'left',
                          py: 1,
                          boxShadow: '0 2px 5px rgba(0,0,0,0.1)',
                          '&:hover': {
                            boxShadow: '0 3px 8px rgba(0,0,0,0.15)',
                          }
                        }}
                      >
                        Generate Missing IDs ({patients.filter((p) => !p.unique_id).length})
                      </Button>
                    </Tooltip>
                  ) : (
                    <Tooltip title="All patients have valid MedIDs" arrow placement="top">
                      <span style={{ width: '100%' }}>
                        <Button
                          variant="outlined"
                          startIcon={<VpnKeyIcon />}
                          disabled
                          size="small"
                          color="success"
                          fullWidth
                          sx={{
                            borderRadius: 2,
                            justifyContent: 'flex-start',
                            textAlign: 'left',
                            py: 1,
                            opacity: 0.7
                          }}
                        >
                          All MedIDs Valid
                        </Button>
                      </span>
                    </Tooltip>
                  )}
                </Grid>
              </Grid>
            </Box>
          </Paper>
        </Grid>
      </Grid>

      {/* Patients Table */}
      <Paper
        elevation={0}
        sx={{
          width: '100%',
          mb: 2,
          borderRadius: 2,
          overflow: 'hidden',
          boxShadow: '0 4px 12px rgba(0,0,0,0.05)'
        }}
      >
        <TableContainer>
          <Table aria-label="patients table">
            <TableHead>
              <TableRow sx={{ backgroundColor: '#f9f9f9' }}>
                <TableCell sx={{ py: 2 }}>
                  <Typography variant="subtitle2" fontWeight="600">MedID</Typography>
                </TableCell>
                <TableCell sx={{ py: 2 }}>
                  <Typography variant="subtitle2" fontWeight="600">Patient</Typography>
                </TableCell>
                <TableCell sx={{ py: 2 }}>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      cursor: 'pointer'
                    }}
                    onClick={() => handleSort('age')}
                  >
                    <Typography variant="subtitle2" fontWeight="600" sx={{ mr: 0.5 }}>
                      Demographics
                    </Typography>
                    {sortField === 'age' && (
                      sortDirection === 'asc' ?
                        <ArrowUpIcon fontSize="small" color="primary" /> :
                        <ArrowDownIcon fontSize="small" color="primary" />
                    )}
                    {sortField === 'gender' && (
                      sortDirection === 'asc' ?
                        <ArrowUpIcon fontSize="small" color="primary" /> :
                        <ArrowDownIcon fontSize="small" color="primary" />
                    )}
                  </Box>
                  <Box sx={{ display: 'flex', mt: 0.5 }}>
                    <Chip
                      label="Age"
                      size="small"
                      variant={sortField === 'age' ? 'filled' : 'outlined'}
                      color={sortField === 'age' ? 'primary' : 'default'}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSort('age');
                      }}
                      sx={{ mr: 0.5, height: 20, fontSize: '0.7rem' }}
                    />
                    <Chip
                      label="Gender"
                      size="small"
                      variant={sortField === 'gender' ? 'filled' : 'outlined'}
                      color={sortField === 'gender' ? 'primary' : 'default'}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSort('gender');
                      }}
                      sx={{ height: 20, fontSize: '0.7rem' }}
                    />
                  </Box>
                </TableCell>
                <TableCell sx={{ py: 2 }}>
                  <Typography variant="subtitle2" fontWeight="600">Contact</Typography>
                </TableCell>
                <TableCell sx={{ py: 2 }}>
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      cursor: 'pointer'
                    }}
                    onClick={() => handleSort('doctor')}
                  >
                    <Typography variant="subtitle2" fontWeight="600" sx={{ mr: 0.5 }}>
                      Doctor
                    </Typography>
                    {sortField === 'doctor' && (
                      sortDirection === 'asc' ?
                        <ArrowUpIcon fontSize="small" color="primary" /> :
                        <ArrowDownIcon fontSize="small" color="primary" />
                    )}
                  </Box>
                </TableCell>
                <TableCell align="center" sx={{ py: 2 }}>
                  <Typography variant="subtitle2" fontWeight="600">Actions</Typography>
                </TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredPatients.length > 0 ? (
                filteredPatients
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((patient) => (
                    <TableRow
                      key={patient.patient_id}
                      sx={{
                        '&:hover': {
                          bgcolor: 'rgba(255, 152, 0, 0.04)'
                        },
                        transition: 'background-color 0.2s ease'
                      }}
                    >
                      <TableCell>
                        <Chip
                          label={patient.unique_id || 'No ID'}
                          color={patient.unique_id ? "warning" : "error"}
                          size="small"
                          sx={{
                            fontWeight: 600,
                            borderRadius: 1,
                            minWidth: 90
                          }}
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                          <Avatar
                            sx={{
                              width: 40,
                              height: 40,
                              bgcolor: patient.gender === 'male' ? 'info.light' : 'secondary.light',
                              color: patient.gender === 'male' ? 'info.dark' : 'secondary.dark',
                              fontWeight: 'bold',
                              fontSize: '1rem',
                              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                            }}
                          >
                            {patient.first_name.charAt(0)}{patient.last_name.charAt(0)}
                          </Avatar>
                          <Box>
                            <Typography variant="body1" fontWeight={600}>
                              {patient.first_name} {patient.last_name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              Created: {patient.created_at ? formatDate(patient.created_at) : 'N/A'}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Chip
                              label={`${calculateAge(patient.date_of_birth)} yrs`}
                              size="small"
                              color="default"
                              variant="outlined"
                              sx={{ minWidth: 60 }}
                            />
                            <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                              {patient.gender}
                            </Typography>
                          </Box>
                          <Typography variant="caption" color="text.secondary" sx={{ mt: 0.5 }}>
                            Born: {formatDate(patient.date_of_birth)}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography
                              variant="body2"
                              sx={{
                                display: 'inline-flex',
                                alignItems: 'center',
                                gap: 0.5
                              }}
                            >
                              <Box
                                component="span"
                                sx={{
                                  width: 8,
                                  height: 8,
                                  borderRadius: '50%',
                                  bgcolor: 'success.main',
                                  display: 'inline-block'
                                }}
                              />
                              {patient.phone}
                            </Typography>
                          </Box>
                          {patient.email && (
                            <Typography
                              variant="body2"
                              color="text.secondary"
                              sx={{
                                display: 'inline-flex',
                                alignItems: 'center',
                                gap: 0.5,
                                maxWidth: 200,
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                whiteSpace: 'nowrap'
                              }}
                            >
                              <Box
                                component="span"
                                sx={{
                                  width: 8,
                                  height: 8,
                                  borderRadius: '50%',
                                  bgcolor: 'info.main',
                                  display: 'inline-block'
                                }}
                              />
                              {patient.email}
                            </Typography>
                          )}
                        </Box>
                      </TableCell>
                      <TableCell>
                        {patient.doctor_name ? (
                          <Chip
                            label={patient.doctor_name}
                            color="success"
                            size="small"
                            variant="outlined"
                            sx={{ fontWeight: 500, borderRadius: 1 }}
                          />
                        ) : (
                          <Chip
                            label="Unassigned"
                            color="default"
                            size="small"
                            variant="outlined"
                            sx={{ fontWeight: 500, borderRadius: 1 }}
                          />
                        )}
                      </TableCell>
                      <TableCell align="center">
                        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 0.5 }}>
                          <Tooltip title="View Details" arrow>
                            <IconButton
                              size="small"
                              sx={{
                                color: 'info.main',
                                bgcolor: 'rgba(33, 150, 243, 0.08)',
                                '&:hover': { bgcolor: 'rgba(33, 150, 243, 0.15)' }
                              }}
                              onClick={() => handleOpenDetailsDialog(patient)}
                            >
                              <VisibilityIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Assign Doctor" arrow>
                            <IconButton
                              size="small"
                              sx={{
                                color: 'success.main',
                                bgcolor: 'rgba(76, 175, 80, 0.08)',
                                '&:hover': { bgcolor: 'rgba(76, 175, 80, 0.15)' }
                              }}
                              onClick={() => handleOpenAssignDialog(patient)}
                            >
                              <AssignmentIndIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Delete Patient" arrow>
                            <IconButton
                              size="small"
                              sx={{
                                color: 'error.main',
                                bgcolor: 'rgba(244, 67, 54, 0.08)',
                                '&:hover': { bgcolor: 'rgba(244, 67, 54, 0.15)' }
                              }}
                              onClick={() => handleOpenDeleteDialog(patient)}
                            >
                              <DeleteIcon fontSize="small" />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} align="center" sx={{ py: 4 }}>
                    <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 1 }}>
                      <PersonIcon sx={{ fontSize: 48, color: 'text.secondary', opacity: 0.5 }} />
                      <Typography variant="body1" color="text.secondary">
                        {searchTerm ? `No patients matching "${searchTerm}"` : 'No patients found'}
                      </Typography>
                      {searchTerm && (
                        <Button
                          variant="text"
                          color="warning"
                          startIcon={<RefreshIcon />}
                          onClick={() => setSearchTerm('')}
                          sx={{ mt: 1 }}
                        >
                          Clear search
                        </Button>
                      )}
                    </Box>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={filteredPatients.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          sx={{
            borderTop: '1px solid #eee',
            '.MuiTablePagination-selectLabel, .MuiTablePagination-displayedRows': {
              fontWeight: 500,
            },
            '.MuiTablePagination-select': {
              borderRadius: 1,
            },
            '.MuiTablePagination-actions': {
              '& .MuiIconButton-root': {
                padding: '4px',
                borderRadius: 1,
                backgroundColor: 'rgba(0, 0, 0, 0.04)',
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.08)',
                },
                '&.Mui-disabled': {
                  opacity: 0.4,
                },
                mx: 0.5
              }
            }
          }}
        />
      </Paper>

      {/* Delete Patient Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleCloseDialogs}
        maxWidth="sm"
        fullWidth
        PaperProps={{ sx: { borderRadius: 2 } }}
      >
        <DialogTitle sx={{ pb: 1, borderBottom: '1px solid #eee' }}>
          <Typography variant="h6">Delete Patient</Typography>
        </DialogTitle>
        <DialogContent sx={{ pt: 3, pb: 2, px: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <Avatar sx={{ bgcolor: 'error.main' }}>
              <DeleteIcon />
            </Avatar>
            <Typography variant="body1" sx={{ fontWeight: 500 }}>
              Are you sure you want to delete this patient?
            </Typography>
          </Box>
          <Typography color="text.secondary">
            You are about to delete the patient record for <strong>{selectedPatient?.first_name} {selectedPatient?.last_name}</strong>.
            This action cannot be undone and will remove all associated medical records.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid #eee' }}>
          <Button onClick={handleCloseDialogs} variant="outlined">Cancel</Button>
          <Button
            onClick={handleDeletePatient}
            color="error"
            variant="contained"
            startIcon={<DeleteIcon />}
          >
            Delete Patient
          </Button>
        </DialogActions>
      </Dialog>

      {/* Assign Doctor Dialog */}
      <Dialog
        open={assignDoctorDialogOpen}
        onClose={handleCloseDialogs}
        maxWidth="sm"
        fullWidth
        PaperProps={{ sx: { borderRadius: 2 } }}
      >
        <DialogTitle sx={{ pb: 1, borderBottom: '1px solid #eee' }}>
          <Typography variant="h6">Assign Doctor</Typography>
        </DialogTitle>
        <DialogContent sx={{ pt: 3, pb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
            <Avatar sx={{ bgcolor: 'primary.main' }}>
              <AssignmentIndIcon />
            </Avatar>
            <Typography variant="body1">
              Assign a doctor to <strong>{selectedPatient?.first_name} {selectedPatient?.last_name}</strong>
            </Typography>
          </Box>

          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel id="doctor-select-label">Doctor</InputLabel>
            <Select
              labelId="doctor-select-label"
              value={selectedDoctor?.doctor_id || ''}
              label="Doctor"
              onChange={(e) => {
                const doctorId = e.target.value === '' ? null : Number(e.target.value);
                setSelectedDoctor(doctors.find(d => d.doctor_id === doctorId) || null);
              }}
            >
              <MenuItem value="">
                <em>None (Unassign)</em>
              </MenuItem>
              {doctors.map((doctor) => (
                <MenuItem key={doctor.doctor_id} value={doctor.doctor_id}>
                  Dr. {doctor.first_name} {doctor.last_name} ({doctor.specialty || 'General'})
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid #eee' }}>
          <Button onClick={handleCloseDialogs} variant="outlined">Cancel</Button>
          <Button onClick={handleAssignDoctor} variant="contained" color="primary" startIcon={<AssignmentIndIcon />}>
            Assign Doctor
          </Button>
        </DialogActions>
      </Dialog>

      {/* Create Patient Dialog */}
      <Dialog open={createPatientDialogOpen} onClose={handleCloseDialogs} maxWidth="md">
        <DialogTitle>Create New Patient</DialogTitle>
        <DialogContent>
          <Box component="form" sx={{ mt: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="First Name"
                  name="first_name"
                  value={newPatient.first_name}
                  onChange={handleNewPatientChange}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Last Name"
                  name="last_name"
                  value={newPatient.last_name}
                  onChange={handleNewPatientChange}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Date of Birth"
                  name="date_of_birth"
                  type="date"
                  value={newPatient.date_of_birth}
                  onChange={handleNewPatientChange}
                  InputLabelProps={{ shrink: true }}
                  required
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <InputLabel id="gender-label">Gender</InputLabel>
                  <Select
                    labelId="gender-label"
                    name="gender"
                    value={newPatient.gender}
                    label="Gender"
                    onChange={handleNewPatientChange}
                  >
                    <MenuItem value="male">Male</MenuItem>
                    <MenuItem value="female">Female</MenuItem>
                    <MenuItem value="other">Other</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Phone"
                  name="phone"
                  value={newPatient.phone}
                  onChange={handleNewPatientChange}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <TextField
                  fullWidth
                  label="Email"
                  name="email"
                  type="email"
                  value={newPatient.email}
                  onChange={handleNewPatientChange}
                />
              </Grid>
              <Grid item xs={12}>
                <FormControl fullWidth>
                  <InputLabel id="doctor-label">Assign Doctor</InputLabel>
                  <Select
                    labelId="doctor-label"
                    name="doctor_id"
                    value={newPatient.doctor_id}
                    label="Assign Doctor"
                    onChange={handleNewPatientChange}
                  >
                    <MenuItem value="">
                      <em>None</em>
                    </MenuItem>
                    {doctors.map((doctor) => (
                      <MenuItem key={doctor.doctor_id} value={doctor.doctor_id}>
                        Dr. {doctor.first_name} {doctor.last_name} {doctor.specialty ? `(${doctor.specialty})` : ''}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialogs}>Cancel</Button>
          <Button
            onClick={handleCreatePatient}
            variant="contained"
            color="primary"
            disabled={loading || !newPatient.first_name || !newPatient.last_name || !newPatient.date_of_birth}
          >
            {loading ? <CircularProgress size={24} /> : 'Create Patient'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* View Patient Details Dialog */}
      <Dialog
        open={viewDetailsDialogOpen}
        onClose={handleCloseDialogs}
        maxWidth="md"
        fullWidth
      >
        {selectedPatient && (
          <>
            <DialogTitle>
              Patient Details: {selectedPatient.first_name} {selectedPatient.last_name}
            </DialogTitle>
            <DialogContent>
              <Box sx={{ py: 2 }}>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Typography variant="h6" color="primary" gutterBottom>
                      Basic Information
                    </Typography>
                    <Divider sx={{ mb: 2 }} />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      MedID
                    </Typography>
                    <Typography variant="body1">
                      {selectedPatient.unique_id}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Date of Birth
                    </Typography>
                    <Typography variant="body1">
                      {formatDate(selectedPatient.date_of_birth)} ({calculateAge(selectedPatient.date_of_birth)} years)
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Gender
                    </Typography>
                    <Typography variant="body1" sx={{ textTransform: 'capitalize' }}>
                      {selectedPatient.gender}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Contact Phone
                    </Typography>
                    <Typography variant="body1">
                      {selectedPatient.phone || 'Not provided'}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Email
                    </Typography>
                    <Typography variant="body1">
                      {selectedPatient.email || 'Not provided'}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">
                      Assigned Doctor
                    </Typography>
                    <Typography variant="body1">
                      {selectedPatient.doctor_name || 'None assigned'}
                    </Typography>
                  </Grid>

                  <Grid item xs={12} sx={{ mt: 2 }}>
                    <Typography variant="caption" color="text.secondary">
                      Created: {selectedPatient.created_at ? formatDate(selectedPatient.created_at) : 'Unknown'}
                    </Typography>
                  </Grid>
                </Grid>
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={handleCloseDialogs}>Close</Button>
              <Button
                variant="contained"
                color="primary"
                onClick={() => {
                  handleCloseDialogs();
                  navigate(`/patients/${selectedPatient.patient_id}`);
                }}
              >
                View Full Profile
              </Button>
            </DialogActions>
          </>
        )}
      </Dialog>

      {/* Generate MedID Confirmation Dialog */}
      <Dialog
        open={generateMedIdDialogOpen}
        onClose={handleCloseDialogs}
        maxWidth="sm"
        fullWidth
        PaperProps={{ sx: { borderRadius: 2 } }}
      >
        <DialogTitle sx={{ pb: 1, borderBottom: '1px solid #eee' }}>
          <Typography variant="h6">Generate Missing MedIDs</Typography>
        </DialogTitle>
        <DialogContent sx={{ pt: 3, pb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
            <Avatar sx={{ bgcolor: 'warning.main' }}>
              <VpnKeyIcon />
            </Avatar>
            <Typography variant="body1" sx={{ fontWeight: 500 }}>
              Generate MedIDs for {patients.filter((p) => !p.unique_id).length} patients
            </Typography>
          </Box>
          <Typography color="text.secondary">
            This will generate unique MedIDs for all patients that currently don't have one.
            The format will be standardized as "P" followed by 5 alphanumeric characters.
          </Typography>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid #eee' }}>
          <Button onClick={handleCloseDialogs} variant="outlined">Cancel</Button>
          <Button onClick={generateMissingMedIDs} color="primary" variant="contained" startIcon={<VpnKeyIcon />}>
            Generate MedIDs
          </Button>
        </DialogActions>
      </Dialog>

      {/* Standardize MedID Confirmation Dialog */}
      <Dialog
        open={standardizeMedIdDialogOpen}
        onClose={handleCloseDialogs}
        maxWidth="sm"
        fullWidth
        PaperProps={{ sx: { borderRadius: 2 } }}
      >
        <DialogTitle sx={{ pb: 1, borderBottom: '1px solid #eee' }}>
          <Typography variant="h6">Standardize Patient MedIDs</Typography>
        </DialogTitle>
        <DialogContent sx={{ pt: 3, pb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
            <Avatar sx={{ bgcolor: 'info.main' }}>
              <SyncIcon />
            </Avatar>
            <Typography variant="body1" sx={{ fontWeight: 500 }}>
              Standardize all patient MedIDs
            </Typography>
          </Box>
          <Typography color="text.secondary">
            This will convert all patient MedIDs to the standardized format starting with "P" followed by 5 alphanumeric characters.
            This is recommended for consistent data management. Do you want to proceed?
          </Typography>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, borderTop: '1px solid #eee' }}>
          <Button onClick={handleCloseDialogs} variant="outlined">Cancel</Button>
          <Button onClick={standardizeMedIDs} color="primary" variant="contained" startIcon={<SyncIcon />}>
            Standardize MedIDs
          </Button>
        </DialogActions>
      </Dialog>

      {/* Notification Snackbar */}
      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseNotification} severity={notification.severity} sx={{ width: '100%' }}>
          {notification.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default PatientManagement;