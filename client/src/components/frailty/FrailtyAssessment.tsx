import React from 'react';
import {
  Box,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  SelectChangeEvent,
  TextField,
  Typography,
  useTheme
} from '@mui/material';
import {
  DirectionsRun as FrailtyIcon,
  FitnessCenter as GripStrengthIcon,
  Straighten as MeasurementIcon,
  Assessment as AssessmentIcon,
  AccessibilityNew as MobilityIcon
} from '@mui/icons-material';
import CollapsibleGuidance from '../common/CollapsibleGuidance';

interface FrailtyAssessmentProps {
  formData: {
    // Physical measurements
    calf_circumference: string;
    grip_strength: string;

    // Mobility and falls
    fall_detection_incidents: string;
    mobility_aids_used: string;
    mobility_status: string;
  };
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleSelectChange: (e: SelectChangeEvent<string>) => void;
  handleRadioChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const FrailtyAssessment: React.FC<FrailtyAssessmentProps> = ({
  formData,
  onChange,
  handleSelectChange,
  handleRadioChange
}) => {
  const theme = useTheme();

  return (
    <>
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
        <FrailtyIcon color="primary" />
        <Typography variant="h6" color="primary" fontWeight={600}>
          Frailty Assessment
        </Typography>
      </Box>

      <CollapsibleGuidance
        title="Clinical Guidance - Frailty Assessment"
        contextKey="frailty_assessment"
      />

      {/* Physical Measurements Section */}
      <Paper
        elevation={2}
        sx={{
          p: 2,
          mb: 3,
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: 2
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <MeasurementIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="subtitle1" fontWeight={600}>
            Physical Measurements
          </Typography>
        </Box>

        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              id="calf_circumference"
              name="calf_circumference"
              label="Calf Circumference (cm)"
              type="number"
              value={formData.calf_circumference || ''}
              onChange={(e) => {
                // Ensure the value is properly converted to a number
                const value = e.target.value === '' ? '' : Number(e.target.value);
                onChange({
                  target: {
                    name: 'calf_circumference',
                    value: value
                  }
                } as React.ChangeEvent<HTMLInputElement>);
              }}
              variant="outlined"
              InputProps={{ inputProps: { min: 0, step: 0.1 } }}
              helperText="Measure at widest part of calf. <31 cm indicates sarcopenia risk."
            />
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              id="grip_strength"
              name="grip_strength"
              label="Grip Strength (kg)"
              type="number"
              value={formData.grip_strength || ''}
              onChange={(e) => {
                // Ensure the value is properly converted to a number
                const value = e.target.value === '' ? '' : Number(e.target.value);
                onChange({
                  target: {
                    name: 'grip_strength',
                    value: value
                  }
                } as React.ChangeEvent<HTMLInputElement>);
              }}
              variant="outlined"
              InputProps={{ inputProps: { min: 0, step: 0.1 } }}
              helperText="Measured using a dynamometer. Lower values indicate frailty."
            />
          </Grid>
        </Grid>
      </Paper>

      {/* Mobility and Falls Section */}
      <Paper
        elevation={2}
        sx={{
          p: 2,
          mb: 3,
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: 2
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <MobilityIcon color="primary" sx={{ mr: 1 }} />
          <Typography variant="subtitle1" fontWeight={600}>
            Mobility and Falls History
          </Typography>
        </Box>

        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              id="fall_detection_incidents"
              name="fall_detection_incidents"
              label="Fall Incidents (past 12 months)"
              type="number"
              value={formData.fall_detection_incidents || ''}
              onChange={(e) => {
                // Ensure the value is properly converted to a number
                const value = e.target.value === '' ? '' : Number(e.target.value);
                onChange({
                  target: {
                    name: 'fall_detection_incidents',
                    value: value
                  }
                } as React.ChangeEvent<HTMLInputElement>);
              }}
              variant="outlined"
              InputProps={{ inputProps: { min: 0, step: 1 } }}
              helperText="Number of falls in the past 12 months"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <FormControl fullWidth variant="outlined">
              <InputLabel id="mobility-status-label">Mobility Status</InputLabel>
              <Select
                labelId="mobility-status-label"
                id="mobility_status"
                name="mobility_status"
                value={formData.mobility_status}
                onChange={handleSelectChange}
                label="Mobility Status"
              >
                <MenuItem value="">Select Mobility Status</MenuItem>
                <MenuItem value="Independent">Independent (no assistance needed)</MenuItem>
                <MenuItem value="Independent with device">Independent with device (cane, walker, etc.)</MenuItem>
                <MenuItem value="Minimal assistance">Minimal assistance needed</MenuItem>
                <MenuItem value="Moderate assistance">Moderate assistance needed</MenuItem>
                <MenuItem value="Maximum assistance">Maximum assistance needed</MenuItem>
                <MenuItem value="Wheelchair dependent">Wheelchair dependent</MenuItem>
                <MenuItem value="Bedbound">Bedbound</MenuItem>
              </Select>
              <FormHelperText>Patient's current mobility status</FormHelperText>
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <FormControl fullWidth variant="outlined">
              <InputLabel id="mobility-aids-label">Mobility Aids Used</InputLabel>
              <Select
                labelId="mobility-aids-label"
                id="mobility_aids_used"
                name="mobility_aids_used"
                value={formData.mobility_aids_used}
                onChange={handleSelectChange}
                label="Mobility Aids Used"
              >
                <MenuItem value="">Select Mobility Aid</MenuItem>
                <MenuItem value="None">None</MenuItem>
                <MenuItem value="Cane">Cane</MenuItem>
                <MenuItem value="Walker">Walker</MenuItem>
                <MenuItem value="Rollator">Rollator</MenuItem>
                <MenuItem value="Wheelchair">Wheelchair</MenuItem>
                <MenuItem value="Scooter">Scooter</MenuItem>
                <MenuItem value="Multiple aids">Multiple aids</MenuItem>
              </Select>
              <FormHelperText>Assistive devices used for mobility</FormHelperText>
            </FormControl>
          </Grid>
        </Grid>
      </Paper>
    </>
  );
};

export default FrailtyAssessment;
