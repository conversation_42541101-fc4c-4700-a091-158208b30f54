import React, { useState, useEffect, useContext } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Alert,
  AlertTitle,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Snackbar,
  IconButton,
  Divider,
  useTheme,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent
} from '@mui/material';
import { styled, alpha } from '@mui/material/styles';
import SendIcon from '@mui/icons-material/Send';
import CloseIcon from '@mui/icons-material/Close';
import AuthContext from '../../context/AuthContext';
import { API_URL } from '../../config';

// Define User interface to match the actual user object structure
interface User {
  id?: number;
  user_id?: number; // Some parts of the app might use user_id instead of id
  username: string;
  role: string;
}

// Styled components
const MessagePaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  borderRadius: '16px',
  boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
  marginBottom: theme.spacing(4),
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  '& .MuiInputBase-root': {
    borderRadius: '12px',
  },
}));

interface MessageDoctorProps {
  open: boolean;
  onClose: () => void;
  patientId?: number;
  doctorName?: string;
  doctorId?: number;
  relatedPatients?: RelatedPatient[];
}

interface RelatedPatient {
  patient_id: number;
  first_name: string;
  last_name: string;
  date_of_birth: string;
  gender: string;
  relationship_type: string;
  is_primary: boolean;
  relationship_id: number;
  doctor_id?: number | null;
  doctor_name?: string;
}

interface Message {
  id: number;
  sender_id: number;
  sender_name: string;
  recipient_id: number;
  recipient_name: string;
  patient_id: number;
  content: string;
  timestamp: string;
  read: boolean;
}

const MessageDoctor: React.FC<MessageDoctorProps> = ({
  open,
  onClose,
  patientId,
  doctorName,
  doctorId,
  relatedPatients = []
}) => {
  const { user } = useContext(AuthContext) as { user: User | null };
  const theme = useTheme();
  const [messageText, setMessageText] = useState('');
  const [sending, setSending] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState<number | null>(patientId || null);
  const [selectedDoctor, setSelectedDoctor] = useState<string | null>(doctorName || null);
  const [selectedDoctorId, setSelectedDoctorId] = useState<number | null>(doctorId || null);

  // Set the selected patient when the component mounts or patientId changes
  useEffect(() => {
    if (patientId) {
      setSelectedPatient(patientId);
    } else if (relatedPatients.length > 0) {
      // Default to the primary patient if available
      const primaryPatient = relatedPatients.find(p => p.is_primary);
      setSelectedPatient(primaryPatient ? primaryPatient.patient_id : relatedPatients[0].patient_id);
    }
  }, [patientId, relatedPatients]);

  // Set the selected doctor when the selected patient changes
  useEffect(() => {
    if (selectedPatient) {
      const patient = relatedPatients.find(p => p.patient_id === selectedPatient);
      if (patient && patient.doctor_name) {
        setSelectedDoctor(patient.doctor_name);
        setSelectedDoctorId(patient.doctor_id || null);
      } else {
        setSelectedDoctor(null);
        setSelectedDoctorId(null);
      }
    }
  }, [selectedPatient, relatedPatients]);

  const handlePatientChange = (event: SelectChangeEvent<number>) => {
    setSelectedPatient(event.target.value as number);
  };

  const handleSendMessage = async () => {
    if (!messageText.trim() || !selectedPatient || !selectedDoctorId) {
      setError('Please select a patient and enter a message');
      return;
    }

    setSending(true);
    setError(null);

    try {
      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const response = await fetch(`${API_URL}/api/messages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        body: JSON.stringify({
          content: messageText,
          patient_id: selectedPatient,
          recipient_id: selectedDoctorId,
          sender_id: user?.id ?? user?.user_id ?? null
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.msg || 'Failed to send message');
      }

      // Clear the message text and show success message
      setMessageText('');
      setSuccess(true);

      // Close the dialog after a short delay
      setTimeout(() => {
        onClose();
      }, 1500);
    } catch (err) {
      console.error('Error sending message:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setSending(false);
    }
  };

  const handleCloseSnackbar = () => {
    setSuccess(false);
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '16px',
          padding: theme.spacing(2)
        }
      }}
    >
      <DialogTitle sx={{
        fontSize: '1.5rem',
        fontWeight: 600,
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        Message Doctor
        <IconButton onClick={onClose} size="large">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <Divider sx={{ mb: 2 }} />

      <DialogContent>
        {relatedPatients.length > 1 && (
          <FormControl fullWidth variant="outlined" sx={{ mb: 3 }}>
            <InputLabel id="patient-select-label">Select Patient</InputLabel>
            <Select
              labelId="patient-select-label"
              id="patient-select"
              value={selectedPatient || ''}
              onChange={handlePatientChange}
              label="Select Patient"
            >
              {relatedPatients.map((patient) => (
                <MenuItem key={patient.patient_id} value={patient.patient_id}>
                  {patient.first_name} {patient.last_name}
                  {patient.is_primary && ' (Primary)'}
                  {patient.relationship_type && ` - ${patient.relationship_type}`}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )}

        {selectedDoctor ? (
          <Box sx={{ mb: 3 }}>
            <Typography variant="body1" fontWeight={500}>
              To: Dr. {selectedDoctor}
            </Typography>
          </Box>
        ) : (
          <Alert severity="warning" sx={{ mb: 3 }}>
            <AlertTitle>No Doctor Assigned</AlertTitle>
            The selected patient does not have an assigned doctor. Please contact the clinic to assign a doctor.
          </Alert>
        )}

        <StyledTextField
          label="Your message"
          multiline
          rows={6}
          fullWidth
          value={messageText}
          onChange={(e) => setMessageText(e.target.value)}
          placeholder="Type your message here..."
          variant="outlined"
          disabled={!selectedDoctor || sending}
        />

        <Alert severity="info" sx={{ mb: 3 }}>
          <AlertTitle>Important Note</AlertTitle>
          For urgent medical concerns, please call your doctor's office directly or go to the nearest emergency room.
        </Alert>

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}
      </DialogContent>

      <DialogActions sx={{ padding: theme.spacing(2, 3) }}>
        <Button
          onClick={onClose}
          variant="outlined"
          sx={{ borderRadius: '8px' }}
        >
          Cancel
        </Button>
        <Button
          variant="contained"
          color="primary"
          endIcon={sending ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
          onClick={handleSendMessage}
          disabled={!messageText.trim() || sending || !selectedDoctor}
          sx={{ borderRadius: '8px' }}
        >
          {sending ? 'Sending...' : 'Send Message'}
        </Button>
      </DialogActions>

      <Snackbar
        open={success}
        autoHideDuration={3000}
        onClose={handleCloseSnackbar}
        message="Message sent successfully"
        action={
          <IconButton
            size="small"
            color="inherit"
            onClick={handleCloseSnackbar}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        }
      />
    </Dialog>
  );
};

export default MessageDoctor;
