import React, { useContext } from 'react';
import { Navigate } from 'react-router-dom'; // Outlet is unused
import AuthContext from '../../context/AuthContext';

interface PasswordChangeRequiredProps {
  children: React.ReactNode;
}

/**
 * Component that redirects to the change password page if a password change is required
 */
const PasswordChangeRequired: React.FC<PasswordChangeRequiredProps> = ({ children }) => {
  const { user, passwordChangeRequired } = useContext(AuthContext);

  // If password change is required, redirect to change password page
  if (user && passwordChangeRequired) {
    console.log('Password change required, redirecting to change password page');
    return <Navigate to="/change-password" replace />;
  }

  // Otherwise, render children
  return <>{children}</>;
};

export default PasswordChangeRequired;
