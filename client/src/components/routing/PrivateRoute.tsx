import React, { useContext } from 'react';
import { Navigate } from 'react-router-dom';
import AuthContext from '../../context/AuthContext';
import PasswordChangeRequired from './PasswordChangeRequired';
import LoadingSpinner from '../common/LoadingSpinner';

interface PrivateRouteProps {
  children: React.ReactNode;
  exemptFromPasswordChange?: boolean;
}

const PrivateRoute: React.FC<PrivateRouteProps> = ({
  children,
  exemptFromPasswordChange = false
}) => {
  const { isAuthenticated, loading, passwordChangeRequired } = useContext(AuthContext);

  if (loading) {
    return <LoadingSpinner size="medium" message="Loading..." />;
  }

  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    return <Navigate to="/login" />;
  }

  // If password change is required and this route is not exempt, wrap with PasswordChangeRequired
  if (passwordChangeRequired && !exemptFromPasswordChange) {
    return <PasswordChangeRequired>{children}</PasswordChangeRequired>;
  }

  // Otherwise, render children
  return <>{children}</>;
};

export default PrivateRoute;