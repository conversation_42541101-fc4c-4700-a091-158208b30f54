import React, { useContext } from 'react';
import { Navigate } from 'react-router-dom';
import AuthContext from '../../context/AuthContext';
import AdminLayout from '../admin/layout/AdminLayout';
import LoadingSpinner from '../common/LoadingSpinner';

interface AdminRouteProps {
  children: React.ReactNode;
}

const AdminRoute: React.FC<AdminRouteProps> = ({ children }) => {
  const { isAuthenticated, loading, user } = useContext(AuthContext);

  // Add debugging logs
  console.log('AdminRoute - Auth state:', { isAuthenticated, loading, userRole: user?.role });

  if (loading) {
    console.log('AdminRoute - Still loading authentication state...');
    return <LoadingSpinner size="medium" message="Loading admin dashboard..." />;
  }

  if (!isAuthenticated || !user || user.role !== 'admin') {
    console.log('AdminRoute - Access denied, redirecting to dashboard');
    console.log('isAuthenticated:', isAuthenticated);
    console.log('user:', user);
    console.log('user role:', user?.role);
    return <Navigate to="/dashboard" />;
  }

  console.log('AdminRoute - Access granted, rendering admin component');
  // Wrap all admin components with AdminLayout
  return <AdminLayout>{children}</AdminLayout>;
};

export default AdminRoute;