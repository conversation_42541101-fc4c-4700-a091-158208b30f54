import React, { useContext } from 'react';
import { Navigate } from 'react-router-dom';
import AuthContext from '../../context/AuthContext';
import LoadingSpinner from '../common/LoadingSpinner';

interface NonKinRouteProps {
  children: React.ReactNode;
}

const NonKinRoute: React.FC<NonKinRouteProps> = ({ children }) => {
  const { isAuthenticated, user, loading } = useContext(AuthContext);

  if (loading) {
    return <LoadingSpinner size="medium" message="Loading..." />;
  }

  // Not authenticated, redirect to login
  if (!isAuthenticated || !user) {
    return <Navigate to="/login" />;
  }

  // If user is a kin, redirect to kin dashboard
  if (user.role === 'kin') {
    return <Navigate to="/kin/dashboard" />;
  }

  // If user is an admin, redirect to admin dashboard
  if (user.role === 'admin') {
    return <Navigate to="/admin" />;
  }

  // Otherwise, render children
  return <>{children}</>;
};

export default NonKinRoute;
