import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON>,
  Divider,
  FormControl,
  FormControlLabel,
  FormHelperText,
  FormLabel,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Radio,
  RadioGroup,
  Select,
  SelectChangeEvent,
  TextField,
  Tooltip,
  Typography,
  useTheme
} from '@mui/material';
import {
  DirectionsRun as FallsIcon,
  History as HistoryIcon,
  Medication as MedicationIcon,
  Psychology as PsychologyIcon,
  Memory as CognitiveIcon,
  Visibility as VisionIcon,
  AccessibilityNew as MobilityIcon,
  Balance as BalanceIcon,
  Home as EnvironmentalIcon,
  Assessment as AssessmentIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import CollapsibleGuidance from '../common/CollapsibleGuidance';

interface FallsRiskAssessmentProps {
  formData: {
    // Fall history
    frat_fall_history: string;
    frat_fall_history_score: string;

    // Medications
    frat_medications: string;
    frat_medications_score: string;

    // Psychological
    frat_psychological: string;
    frat_psychological_score: string;

    // Cognitive
    frat_cognitive: string;
    frat_cognitive_score: string;

    // Total score
    frat_total_score: string;

    // Risk level
    frat_risk_level: string;

    // Additional notes
    frat_notes: string;
  };
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleSelectChange: (e: SelectChangeEvent<string>) => void;
  handleRadioChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

const FallsRiskAssessment: React.FC<FallsRiskAssessmentProps> = ({
  formData,
  onChange,
  handleSelectChange,
  handleRadioChange
}) => {
  const theme = useTheme();

  return (
    <>
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
        <FallsIcon color="primary" />
        <Typography variant="h6" color="primary" fontWeight={600}>
          Falls Risk Assessment Tool (FRAT)
        </Typography>
      </Box>

      <CollapsibleGuidance
        title="Clinical Guidance - Falls Risk Assessment"
        contextKey="falls_risk"
      />

      {/* Fall History Section */}
      <Card
        elevation={2}
        sx={{
          mb: 3,
          border: `1px solid ${theme.palette.primary.light}`,
          borderRadius: 2,
          overflow: 'visible'
        }}
      >
        <CardContent sx={{ p: 0 }}>
          <Box
            sx={{
              p: 2,
              display: 'flex',
              alignItems: 'center',
              bgcolor: 'primary.light',
              color: 'primary.contrastText',
              borderTopLeftRadius: 8,
              borderTopRightRadius: 8
            }}
          >
            <HistoryIcon sx={{ mr: 1 }} />
            <Typography variant="subtitle1" fontWeight={600}>
              Recent Falls
            </Typography>
            <Tooltip title="To score this, complete history of falls">
              <InfoIcon fontSize="small" sx={{ ml: 1, opacity: 0.7 }} />
            </Tooltip>
          </Box>

          <Box sx={{ p: 2 }}>
            <FormControl component="fieldset" fullWidth>
              <RadioGroup
                name="frat_fall_history"
                value={formData.frat_fall_history}
                onChange={handleRadioChange}
              >
                <Grid container spacing={1}>
                  <Grid item xs={12} sm={8}>
                    <FormControlLabel
                      value="1"
                      control={<Radio />}
                      label="None in last 12 months"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Chip
                      label="Score: 2"
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ mt: 1 }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={8}>
                    <FormControlLabel
                      value="2"
                      control={<Radio />}
                      label="One or more between 3 and 12 months ago"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Chip
                      label="Score: 4"
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ mt: 1 }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={8}>
                    <FormControlLabel
                      value="3"
                      control={<Radio />}
                      label="One or more in last 3 months"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Chip
                      label="Score: 6"
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ mt: 1 }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={8}>
                    <FormControlLabel
                      value="4"
                      control={<Radio />}
                      label="One or more in last 3 months whilst inpatient/resident"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Chip
                      label="Score: 8"
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ mt: 1 }}
                    />
                  </Grid>
                </Grid>
              </RadioGroup>
            </FormControl>
          </Box>
        </CardContent>
      </Card>

      {/* Medications Section */}
      <Card
        elevation={2}
        sx={{
          mb: 3,
          border: `1px solid ${theme.palette.primary.light}`,
          borderRadius: 2,
          overflow: 'visible'
        }}
      >
        <CardContent sx={{ p: 0 }}>
          <Box
            sx={{
              p: 2,
              display: 'flex',
              alignItems: 'center',
              bgcolor: 'primary.light',
              color: 'primary.contrastText',
              borderTopLeftRadius: 8,
              borderTopRightRadius: 8
            }}
          >
            <MedicationIcon sx={{ mr: 1 }} />
            <Typography variant="subtitle1" fontWeight={600}>
              Medications
            </Typography>
            <Tooltip title="Sedatives, Anti-depressants, Anti-Parkinson's, Diuretics, Anti-hypertensives, hypnotics">
              <InfoIcon fontSize="small" sx={{ ml: 1, opacity: 0.7 }} />
            </Tooltip>
          </Box>

          <Box sx={{ p: 2 }}>
            <FormControl component="fieldset" fullWidth>
              <RadioGroup
                name="frat_medications"
                value={formData.frat_medications}
                onChange={handleRadioChange}
              >
                <Grid container spacing={1}>
                  <Grid item xs={12} sm={8}>
                    <FormControlLabel
                      value="1"
                      control={<Radio />}
                      label="Not taking any of these"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Chip
                      label="Score: 1"
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ mt: 1 }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={8}>
                    <FormControlLabel
                      value="2"
                      control={<Radio />}
                      label="Taking one"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Chip
                      label="Score: 2"
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ mt: 1 }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={8}>
                    <FormControlLabel
                      value="3"
                      control={<Radio />}
                      label="Taking two"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Chip
                      label="Score: 3"
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ mt: 1 }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={8}>
                    <FormControlLabel
                      value="4"
                      control={<Radio />}
                      label="Taking more than two"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Chip
                      label="Score: 4"
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ mt: 1 }}
                    />
                  </Grid>
                </Grid>
              </RadioGroup>
            </FormControl>
          </Box>
        </CardContent>
      </Card>

      {/* Psychological Section */}
      <Card
        elevation={2}
        sx={{
          mb: 3,
          border: `1px solid ${theme.palette.primary.light}`,
          borderRadius: 2,
          overflow: 'visible'
        }}
      >
        <CardContent sx={{ p: 0 }}>
          <Box
            sx={{
              p: 2,
              display: 'flex',
              alignItems: 'center',
              bgcolor: 'primary.light',
              color: 'primary.contrastText',
              borderTopLeftRadius: 8,
              borderTopRightRadius: 8
            }}
          >
            <PsychologyIcon sx={{ mr: 1 }} />
            <Typography variant="subtitle1" fontWeight={600}>
              Psychological
            </Typography>
            <Tooltip title="Anxiety, Depression, ↓Cooperation, ↓Insight or ↓Judgement esp. re mobility">
              <InfoIcon fontSize="small" sx={{ ml: 1, opacity: 0.7 }} />
            </Tooltip>
          </Box>

          <Box sx={{ p: 2 }}>
            <FormControl component="fieldset" fullWidth>
              <RadioGroup
                name="frat_psychological"
                value={formData.frat_psychological}
                onChange={handleRadioChange}
              >
                <Grid container spacing={1}>
                  <Grid item xs={12} sm={8}>
                    <FormControlLabel
                      value="1"
                      control={<Radio />}
                      label="Does not appear to have any of these"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Chip
                      label="Score: 1"
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ mt: 1 }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={8}>
                    <FormControlLabel
                      value="2"
                      control={<Radio />}
                      label="Appears mildly affected by one or more"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Chip
                      label="Score: 2"
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ mt: 1 }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={8}>
                    <FormControlLabel
                      value="3"
                      control={<Radio />}
                      label="Appears moderately affected by one or more"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Chip
                      label="Score: 3"
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ mt: 1 }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={8}>
                    <FormControlLabel
                      value="4"
                      control={<Radio />}
                      label="Appears severely affected by one or more"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Chip
                      label="Score: 4"
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ mt: 1 }}
                    />
                  </Grid>
                </Grid>
              </RadioGroup>
            </FormControl>
          </Box>
        </CardContent>
      </Card>

      {/* Cognitive Section */}
      <Card
        elevation={2}
        sx={{
          mb: 3,
          border: `1px solid ${theme.palette.primary.light}`,
          borderRadius: 2,
          overflow: 'visible'
        }}
      >
        <CardContent sx={{ p: 0 }}>
          <Box
            sx={{
              p: 2,
              display: 'flex',
              alignItems: 'center',
              bgcolor: 'primary.light',
              color: 'primary.contrastText',
              borderTopLeftRadius: 8,
              borderTopRightRadius: 8
            }}
          >
            <CognitiveIcon sx={{ mr: 1 }} />
            <Typography variant="subtitle1" fontWeight={600}>
              Cognitive Status
            </Typography>
            <Tooltip title="AMTS: Abbreviated Mental Test Score">
              <InfoIcon fontSize="small" sx={{ ml: 1, opacity: 0.7 }} />
            </Tooltip>
          </Box>

          <Box sx={{ p: 2 }}>
            <FormControl component="fieldset" fullWidth>
              <RadioGroup
                name="frat_cognitive"
                value={formData.frat_cognitive}
                onChange={handleRadioChange}
              >
                <Grid container spacing={1}>
                  <Grid item xs={12} sm={8}>
                    <FormControlLabel
                      value="1"
                      control={<Radio />}
                      label="AMTS 9 or 10/10 OR intact"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Chip
                      label="Score: 1"
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ mt: 1 }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={8}>
                    <FormControlLabel
                      value="2"
                      control={<Radio />}
                      label="AMTS 7-8 OR mildly impaired"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Chip
                      label="Score: 2"
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ mt: 1 }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={8}>
                    <FormControlLabel
                      value="3"
                      control={<Radio />}
                      label="AMTS 5-6 OR moderately impaired"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Chip
                      label="Score: 3"
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ mt: 1 }}
                    />
                  </Grid>

                  <Grid item xs={12} sm={8}>
                    <FormControlLabel
                      value="4"
                      control={<Radio />}
                      label="AMTS 4 or less OR severely impaired"
                    />
                  </Grid>
                  <Grid item xs={12} sm={4}>
                    <Chip
                      label="Score: 4"
                      size="small"
                      color="primary"
                      variant="outlined"
                      sx={{ mt: 1 }}
                    />
                  </Grid>
                </Grid>
              </RadioGroup>
            </FormControl>
          </Box>
        </CardContent>
      </Card>

      {/* Results Section */}
      <Paper
        elevation={3}
        sx={{
          p: 3,
          mb: 3,
          border: `1px solid ${theme.palette.success.main}`,
          borderRadius: 2,
          bgcolor: 'background.paper'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <AssessmentIcon color="success" sx={{ mr: 1, fontSize: 28 }} />
          <Typography variant="h6" color="success.main" fontWeight={600}>
            Falls Risk Assessment Results
          </Typography>
        </Box>

        <Grid container spacing={3}>
          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              id="frat_total_score"
              name="frat_total_score"
              label="Total FRAT Score"
              value={formData.frat_total_score}
              disabled
              variant="outlined"
              InputProps={{
                endAdornment: <Typography variant="body2" color="textSecondary">/20</Typography>
              }}
              helperText="Sum of all risk factor scores (out of 20)"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={4}>
            <TextField
              fullWidth
              id="frat_risk_level"
              name="frat_risk_level"
              label="Falls Risk Level"
              value={formData.frat_risk_level}
              disabled
              variant="outlined"
              helperText="Low: 5-11, Medium: 12-15, High: 16-20"
            />
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              id="frat_notes"
              name="frat_notes"
              label="Additional Notes"
              value={formData.frat_notes}
              onChange={onChange}
              variant="outlined"
              multiline
              rows={3}
              helperText="Additional observations or notes about falls risk"
            />
          </Grid>
        </Grid>
      </Paper>
    </>
  );
};

export default FallsRiskAssessment;
