import React, { useState } from 'react';
import {
  Box,
  Button,
  // Card - Unused import
  // CardContent - Unused import
  Chip,
  Divider,
  // Grid - Unused import
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
  Alert
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import MedicationIcon from '@mui/icons-material/Medication';
import WarningIcon from '@mui/icons-material/Warning';
// InfoIcon - Unused import

import PrescriptionForm from './PrescriptionForm';

interface Prescription {
  id?: number;
  medication: string;
  dosage: string;
  frequency: string;
  duration: string;
  notes?: string;
  beers_criteria_id?: number;
  beers_criteria_name?: string;
  beers_criteria_category?: string;
  beers_criteria_recommendation?: string;
  beers_criteria_rationale?: string;
  beers_override_reason?: string;
  beers_overridden_at?: string;
  beers_overridden_by?: number;
  overridden_by_username?: string;
}

interface PrescriptionListProps {
  prescriptions: Prescription[];
  onAdd: (prescription: Prescription) => void;
  onEdit: (index: number, prescription: Prescription) => void;
  onDelete: (index: number) => void;
  title?: string;
  showAddButton?: boolean;
  patientId?: number;
  patientAge?: number;
}

const PrescriptionList: React.FC<PrescriptionListProps> = ({
  prescriptions,
  onAdd,
  onEdit,
  onDelete,
  title = 'Prescriptions',
  showAddButton = true,
  patientId,
  patientAge
}) => {
  const [formOpen, setFormOpen] = useState(false);
  const [editIndex, setEditIndex] = useState<number | null>(null);
  const [currentPrescription, setCurrentPrescription] = useState<Prescription | undefined>(undefined);

  const handleAddClick = () => {
    setEditIndex(null);
    setCurrentPrescription(undefined);
    setFormOpen(true);
  };

  const handleEditClick = (index: number) => {
    setEditIndex(index);
    // Make a copy of the prescription to avoid direct reference
    setCurrentPrescription({...prescriptions[index]});
    setFormOpen(true);
  };

  const handleDeleteClick = (index: number) => {
    if (window.confirm('Are you sure you want to remove this prescription?')) {
      onDelete(index);
    }
  };

  const handleFormClose = () => {
    setFormOpen(false);
  };

  const handleFormSave = (prescription: Prescription) => {
    if (editIndex !== null) {
      onEdit(editIndex, prescription);
    } else {
      onAdd(prescription);
    }
    setFormOpen(false);
  };

  return (
    <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
        <Box display="flex" alignItems="center">
          <MedicationIcon fontSize="large" sx={{ mr: 2 }} />
          <Typography variant="h5">{title}</Typography>
        </Box>

        <Box>
          {showAddButton && (
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddClick}
            >
              Add Prescription
            </Button>
          )}
        </Box>
      </Box>

      <Divider sx={{ mb: 3 }} />

      {prescriptions.length === 0 ? (
        <Alert severity="info" sx={{ mb: 2 }}>
          No prescriptions have been added yet.
        </Alert>
      ) : (
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell width="18%"><strong>Medication</strong></TableCell>
                <TableCell width="12%"><strong>Dosage</strong></TableCell>
                <TableCell width="12%"><strong>Frequency</strong></TableCell>
                <TableCell width="12%"><strong>Duration</strong></TableCell>
                <TableCell width="18%"><strong>Notes</strong></TableCell>
                <TableCell width="18%"><strong>BEERS Overrides</strong></TableCell>
                <TableCell width="10%" align="right"><strong>Actions</strong></TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {prescriptions.map((prescription, index) => (
                <TableRow key={index}>
                  <TableCell>{prescription.medication}</TableCell>
                  <TableCell>{prescription.dosage}</TableCell>
                  <TableCell>{prescription.frequency}</TableCell>
                  <TableCell>{prescription.duration}</TableCell>
                  <TableCell>
                    {prescription.notes ? (
                      <Typography variant="body2">
                        {prescription.notes}
                      </Typography>
                    ) : (
                      <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                        None
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell>
                    {prescription.beers_override_reason ? (
                      <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                        <Tooltip title="BEERS Criteria Override">
                          <Chip
                            icon={<WarningIcon fontSize="small" />}
                            label="Override"
                            size="small"
                            color="warning"
                            sx={{ mr: 1, mt: 0.3 }}
                          />
                        </Tooltip>
                        <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                          <strong>Reason:</strong> {prescription.beers_override_reason}
                        </Typography>
                      </Box>
                    ) : (
                      <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                        None
                      </Typography>
                    )}
                  </TableCell>
                  <TableCell align="right">
                    <IconButton
                      size="small"
                      color="primary"
                      onClick={() => handleEditClick(index)}
                    >
                      <EditIcon fontSize="small" />
                    </IconButton>
                    <IconButton
                      size="small"
                      color="error"
                      onClick={() => handleDeleteClick(index)}
                    >
                      <DeleteIcon fontSize="small" />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      <PrescriptionForm
        open={formOpen}
        onClose={handleFormClose}
        onSave={handleFormSave}
        initialData={currentPrescription}
        title={editIndex !== null ? 'Edit Prescription' : 'Add Prescription'}
        patientId={patientId}
        patientAge={patientAge}
      />
    </Paper>
  );
};

export default PrescriptionList;
