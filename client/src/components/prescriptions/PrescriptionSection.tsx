import React, { useState, useContext } from 'react';
import { Box } from '@mui/material';
import PrescriptionList from './PrescriptionList';
// PrescriptionForm - Unused import
import { deletePrescription } from '../../services/prescriptionService';
import AuthContext from '../../context/AuthContext';

interface Prescription {
  id?: number;
  medication: string;
  dosage: string;
  frequency: string;
  duration: string;
  notes?: string;
  beers_criteria_id?: number;
  beers_criteria_name?: string;
  beers_criteria_category?: string;
  beers_criteria_recommendation?: string;
  beers_criteria_rationale?: string;
  beers_override_reason?: string;
  beers_overridden_at?: string;
  beers_overridden_by?: number;
}

interface PrescriptionSectionProps {
  prescriptions: Prescription[];
  onPrescriptionsChange: (prescriptions: Prescription[]) => void;
  patientId?: number;
  patientAge?: number;
}



const PrescriptionSection: React.FC<PrescriptionSectionProps> = ({
  prescriptions,
  onPrescriptionsChange,
  patientId,
  patientAge
}) => {
  const auth = useContext(AuthContext);
  // const user = auth?.user; - Unused variable
  // const [formOpen, setFormOpen] = useState(false); - Unused variables
  // No BEERS dialog state needed

  const handleAddPrescription = (prescription: Prescription) => {
    // Generate a temporary unique ID for new prescriptions if not already present
    const newPrescription = {
      ...prescription,
      id: prescription.id || -Math.floor(Math.random() * 1000000) // Negative ID to avoid conflicts with DB IDs
    };
    const updatedPrescriptions = [...prescriptions, newPrescription];
    onPrescriptionsChange(updatedPrescriptions);
  };

  const handleEditPrescription = (index: number, prescription: Prescription) => {
    const updatedPrescriptions = [...prescriptions];
    // Preserve the existing ID when editing
    updatedPrescriptions[index] = {
      ...prescription,
      id: prescriptions[index].id || prescription.id
    };
    onPrescriptionsChange(updatedPrescriptions);
  };

  const handleDeletePrescription = async (index: number) => {
    const prescription = prescriptions[index];

    // If the prescription has a real ID (from the database), delete it from the server
    if (prescription.id && prescription.id > 0) {
      try {
        console.log(`Deleting prescription with ID ${prescription.id} from the server`);
        await deletePrescription(prescription.id);
        console.log(`Successfully deleted prescription with ID ${prescription.id}`);
      } catch (error) {
        console.error(`Error deleting prescription with ID ${prescription.id}:`, error);
        // Continue with UI update even if server deletion fails
      }
    } else {
      console.log(`Removing temporary prescription at index ${index} (no server call needed)`);
    }

    // Update the UI by removing the prescription from the local state
    const updatedPrescriptions = prescriptions.filter((_, i) => i !== index);
    onPrescriptionsChange(updatedPrescriptions);
  };

  // No BEERS override handlers needed

  return (
    <Box>
      <PrescriptionList
        prescriptions={prescriptions}
        onAdd={handleAddPrescription}
        onEdit={handleEditPrescription}
        onDelete={handleDeletePrescription}
        title="Current Prescriptions"
        patientId={patientId}
        patientAge={patientAge}
      />
    </Box>
  );
};

export default PrescriptionSection;
