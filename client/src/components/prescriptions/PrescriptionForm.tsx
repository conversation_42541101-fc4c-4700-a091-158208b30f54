import React, { useState, useEffect, useContext } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  TextField,
  Typography,
  // Alert - Unused import
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import AddIcon from '@mui/icons-material/Add';
// DeleteIcon - Unused import
// BeersCriteriaChecker - Unused import
import AuthContext from '../../context/AuthContext';

interface Prescription {
  id?: number;
  medication: string;
  dosage: string;
  frequency: string;
  duration: string;
  notes?: string;
  beers_criteria_id?: number;
  beers_criteria_name?: string;
  beers_criteria_category?: string;
  beers_criteria_recommendation?: string;
  beers_criteria_rationale?: string;
  beers_override_reason?: string;
  beers_overridden_at?: string;
  beers_overridden_by?: number;
}

interface PrescriptionFormProps {
  open: boolean;
  onClose: () => void;
  onSave: (prescription: Prescription) => void;
  initialData?: Prescription;
  title?: string;
  patientId?: number;
  patientAge?: number;
}

const frequencyOptions = [
  'Once daily',
  'Twice daily',
  'Three times daily',
  'Four times daily',
  'Every morning',
  'Every evening',
  'Every night',
  'Every 4 hours',
  'Every 6 hours',
  'Every 8 hours',
  'Every 12 hours',
  'As needed',
  'With meals',
  'Before meals',
  'After meals',
  'Weekly',
  'Twice weekly',
  'Monthly',
  'Other'
];

const durationOptions = [
  '1 day',
  '3 days',
  '5 days',
  '7 days',
  '10 days',
  '14 days',
  '1 month',
  '2 months',
  '3 months',
  '6 months',
  'Ongoing',
  'As directed',
  'Other'
];

const PrescriptionForm: React.FC<PrescriptionFormProps> = ({
  open,
  onClose,
  onSave,
  initialData,
  title = 'Add Prescription',
  patientId = 0,
  patientAge = 0
}) => {
  const auth = useContext(AuthContext);
  // const user = auth?.user; - Unused variable
  const [formData, setFormData] = useState<Prescription>({
    medication: '',
    dosage: '',
    frequency: '',
    duration: '',
    notes: '',
    beers_criteria_id: undefined,
    beers_criteria_name: undefined,
    beers_criteria_category: undefined,
    beers_criteria_recommendation: undefined,
    beers_criteria_rationale: undefined,
    beers_override_reason: undefined,
    beers_overridden_at: undefined,
    beers_overridden_by: undefined
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  // No BEERS criteria checking in this component

  useEffect(() => {
    if (initialData) {
      setFormData(initialData);
    } else {
      // Reset form when opened without initial data
      setFormData({
        medication: '',
        dosage: '',
        frequency: '',
        duration: '',
        notes: '',
        beers_criteria_id: undefined,
        beers_criteria_name: undefined,
        beers_criteria_category: undefined,
        beers_criteria_recommendation: undefined,
        beers_criteria_rationale: undefined,
        beers_override_reason: undefined,
        beers_overridden_at: undefined,
        beers_overridden_by: undefined
      });
    }
    setErrors({});
  }, [initialData, open]);

  // Handle text input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = e.target;
    if (name) {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));

      // Clear error when field is edited
      if (errors[name]) {
        setErrors(prev => ({
          ...prev,
          [name]: ''
        }));
      }
    }
  };

  // Handle select changes
  const handleSelectChange = (e: SelectChangeEvent<string>) => {
    const { name, value } = e.target;
    if (name) {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));

      // Clear error when field is edited
      if (errors[name]) {
        setErrors(prev => ({
          ...prev,
          [name]: ''
        }));
      }
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.medication.trim()) {
      newErrors.medication = 'Medication name is required';
    }

    if (!formData.dosage.trim()) {
      newErrors.dosage = 'Dosage is required';
    }

    if (!formData.frequency.trim()) {
      newErrors.frequency = 'Frequency is required';
    }

    if (!formData.duration.trim()) {
      newErrors.duration = 'Duration is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      onSave(formData);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        style: { borderRadius: '8px' }
      }}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Typography variant="h6">{title}</Typography>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent dividers>

        <Grid container spacing={3}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Medication Name"
              name="medication"
              value={formData.medication}
              onChange={handleChange}
              error={!!errors.medication}
              helperText={errors.medication}
              required
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Dosage"
              name="dosage"
              value={formData.dosage}
              onChange={handleChange}
              error={!!errors.dosage}
              helperText={errors.dosage || "e.g., 10mg, 500mg, 1 tablet"}
              required
            />
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth error={!!errors.frequency} required>
              <InputLabel>Frequency</InputLabel>
              <Select
                name="frequency"
                value={formData.frequency}
                onChange={handleSelectChange}
                label="Frequency"
              >
                {frequencyOptions.map(option => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </Select>
              {errors.frequency && <FormHelperText>{errors.frequency}</FormHelperText>}
            </FormControl>
          </Grid>

          <Grid item xs={12} sm={6}>
            <FormControl fullWidth error={!!errors.duration} required>
              <InputLabel>Duration</InputLabel>
              <Select
                name="duration"
                value={formData.duration}
                onChange={handleSelectChange}
                label="Duration"
              >
                {durationOptions.map(option => (
                  <MenuItem key={option} value={option}>
                    {option}
                  </MenuItem>
                ))}
              </Select>
              {errors.duration && <FormHelperText>{errors.duration}</FormHelperText>}
            </FormControl>
          </Grid>

          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Notes"
              name="notes"
              value={formData.notes}
              onChange={handleChange}
              multiline
              rows={3}
              helperText="Special instructions, warnings, or additional information"
            />
          </Grid>
        </Grid>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} color="inherit">
          Cancel
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
        >
          {initialData ? 'Update' : 'Add'} Prescription
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default PrescriptionForm;
