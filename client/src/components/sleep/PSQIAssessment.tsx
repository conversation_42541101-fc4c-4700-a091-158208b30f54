import React from 'react';
import {
  Box,
  Divider,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  TextField,
  Typography
} from '@mui/material';
import { Bed as BedIcon } from '@mui/icons-material';
import CollapsibleGuidance from '../common/CollapsibleGuidance';

interface PSQIAssessmentProps {
  formData: {
    psqi_subjective_sleep_quality: string;
    psqi_sleep_latency: string;
    psqi_sleep_duration: string;
    psqi_sleep_efficiency: string;
    psqi_sleep_disturbances: string;
    psqi_sleep_medication: string;
    psqi_daytime_dysfunction: string;
    psqi_total_score: string;
    psqi_assessment_date: string;
    psqi_bedtime: string;
    psqi_minutes_to_fall_asleep: string;
    psqi_wake_up_time: string;
    psqi_hours_of_sleep: string;
    psqi_notes: string;
  };
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleSelectChange: (e: SelectChangeEvent<string>) => void;
}

const PSQIAssessment: React.FC<PSQIAssessmentProps> = ({ formData, onChange, handleSelectChange }) => {
  return (
    <>
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
        <BedIcon color="primary" />
        <Typography variant="h6" color="primary" fontWeight={600}>
          Sleep Quality Assessment (PSQI)
        </Typography>
      </Box>

      <CollapsibleGuidance
        title="Clinical Guidance - Pittsburgh Sleep Quality Index (PSQI)"
        contextKey="sleep_assessment_psqi"
      />

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Typography variant="subtitle1" gutterBottom>
            Basic Sleep Information
          </Typography>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <TextField
            fullWidth
            id="psqi_bedtime"
            name="psqi_bedtime"
            label="Usual Bedtime"
            type="time"
            value={formData.psqi_bedtime}
            onChange={onChange}
            variant="outlined"
            InputLabelProps={{ shrink: true }}
            helperText="What time do you usually go to bed?"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <TextField
            fullWidth
            id="psqi_minutes_to_fall_asleep"
            name="psqi_minutes_to_fall_asleep"
            label="Minutes to Fall Asleep"
            type="number"
            value={formData.psqi_minutes_to_fall_asleep}
            onChange={onChange}
            variant="outlined"
            InputProps={{ inputProps: { min: 0, step: 1 } }}
            helperText="How long does it take to fall asleep?"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <TextField
            fullWidth
            id="psqi_wake_up_time"
            name="psqi_wake_up_time"
            label="Usual Wake-up Time"
            type="time"
            value={formData.psqi_wake_up_time}
            onChange={onChange}
            variant="outlined"
            InputLabelProps={{ shrink: true }}
            helperText="What time do you usually wake up?"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <TextField
            fullWidth
            id="psqi_hours_of_sleep"
            name="psqi_hours_of_sleep"
            label="Hours of Actual Sleep"
            type="number"
            value={formData.psqi_hours_of_sleep}
            onChange={onChange}
            variant="outlined"
            InputProps={{ inputProps: { min: 0, max: 24, step: 0.5 } }}
            helperText="How many hours of actual sleep do you get?"
          />
        </Grid>

        <Grid item xs={12}>
          <Divider sx={{ my: 3 }} />
          <Typography variant="subtitle1" gutterBottom>
            PSQI Component Scores (0-3 scale, higher is worse)
          </Typography>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <FormControl fullWidth variant="outlined">
            <InputLabel id="psqi-subjective-sleep-quality-label">Subjective Sleep Quality</InputLabel>
            <Select
              labelId="psqi-subjective-sleep-quality-label"
              id="psqi_subjective_sleep_quality"
              name="psqi_subjective_sleep_quality"
              value={formData.psqi_subjective_sleep_quality}
              onChange={handleSelectChange}
              label="Subjective Sleep Quality"
            >
              <MenuItem value="">Select</MenuItem>
              <MenuItem value="0">Very good (0)</MenuItem>
              <MenuItem value="1">Fairly good (1)</MenuItem>
              <MenuItem value="2">Fairly bad (2)</MenuItem>
              <MenuItem value="3">Very bad (3)</MenuItem>
            </Select>
            <FormHelperText>How would you rate your sleep quality overall?</FormHelperText>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <TextField
            fullWidth
            id="psqi_sleep_latency"
            name="psqi_sleep_latency"
            label="Sleep Latency Score"
            value={formData.psqi_sleep_latency}
            disabled
            variant="outlined"
            helperText="Auto-calculated from minutes to fall asleep"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <TextField
            fullWidth
            id="psqi_sleep_duration"
            name="psqi_sleep_duration"
            label="Sleep Duration Score"
            value={formData.psqi_sleep_duration}
            disabled
            variant="outlined"
            helperText="Auto-calculated from hours of sleep"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <TextField
            fullWidth
            id="psqi_sleep_efficiency"
            name="psqi_sleep_efficiency"
            label="Sleep Efficiency Score"
            value={formData.psqi_sleep_efficiency}
            disabled
            variant="outlined"
            helperText="Auto-calculated from sleep times"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <FormControl fullWidth variant="outlined">
            <InputLabel id="psqi-sleep-disturbances-label">Sleep Disturbances</InputLabel>
            <Select
              labelId="psqi-sleep-disturbances-label"
              id="psqi_sleep_disturbances"
              name="psqi_sleep_disturbances"
              value={formData.psqi_sleep_disturbances}
              onChange={handleSelectChange}
              label="Sleep Disturbances"
            >
              <MenuItem value="">Select</MenuItem>
              <MenuItem value="0">No disturbances (0)</MenuItem>
              <MenuItem value="1">Mild disturbances (1)</MenuItem>
              <MenuItem value="2">Moderate disturbances (2)</MenuItem>
              <MenuItem value="3">Severe disturbances (3)</MenuItem>
            </Select>
            <FormHelperText>How often have you had trouble sleeping because of various reasons?</FormHelperText>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <FormControl fullWidth variant="outlined">
            <InputLabel id="psqi-sleep-medication-label">Use of Sleep Medication</InputLabel>
            <Select
              labelId="psqi-sleep-medication-label"
              id="psqi_sleep_medication"
              name="psqi_sleep_medication"
              value={formData.psqi_sleep_medication}
              onChange={handleSelectChange}
              label="Use of Sleep Medication"
            >
              <MenuItem value="">Select</MenuItem>
              <MenuItem value="0">Not during the past month (0)</MenuItem>
              <MenuItem value="1">Less than once a week (1)</MenuItem>
              <MenuItem value="2">Once or twice a week (2)</MenuItem>
              <MenuItem value="3">Three or more times a week (3)</MenuItem>
            </Select>
            <FormHelperText>How often have you taken medicine to help you sleep?</FormHelperText>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <FormControl fullWidth variant="outlined">
            <InputLabel id="psqi-daytime-dysfunction-label">Daytime Dysfunction</InputLabel>
            <Select
              labelId="psqi-daytime-dysfunction-label"
              id="psqi_daytime_dysfunction"
              name="psqi_daytime_dysfunction"
              value={formData.psqi_daytime_dysfunction}
              onChange={handleSelectChange}
              label="Daytime Dysfunction"
            >
              <MenuItem value="">Select</MenuItem>
              <MenuItem value="0">No problem (0)</MenuItem>
              <MenuItem value="1">Slight problem (1)</MenuItem>
              <MenuItem value="2">Moderate problem (2)</MenuItem>
              <MenuItem value="3">Big problem (3)</MenuItem>
            </Select>
            <FormHelperText>How much of a problem has it been for you to keep up enthusiasm or stay awake during daily activities?</FormHelperText>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <TextField
            fullWidth
            id="psqi_total_score"
            name="psqi_total_score"
            label="Global PSQI Score"
            value={formData.psqi_total_score}
            disabled
            variant="outlined"
            helperText="Sum of 7 component scores (0-21)"
          />
          {formData.psqi_total_score && (
            <Box sx={{ mt: 1 }}>
              <Typography variant="body2" color={parseInt(formData.psqi_total_score) > 5 ? 'error' : 'success'}>
                {parseInt(formData.psqi_total_score) > 5 ? 'Poor sleep quality (>5)' : 'Good sleep quality (≤5)'}
              </Typography>
            </Box>
          )}
        </Grid>

        <Grid item xs={12}>
          <TextField
            fullWidth
            id="psqi_notes"
            name="psqi_notes"
            label="Additional Sleep Notes"
            value={formData.psqi_notes}
            onChange={onChange}
            variant="outlined"
            multiline
            rows={3}
            helperText="Additional observations or notes about sleep patterns"
          />
        </Grid>
      </Grid>
    </>
  );
};

export default PSQIAssessment;
