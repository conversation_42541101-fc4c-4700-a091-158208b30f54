import React from 'react';
import {
  Box,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  TextField,
  Typography
} from '@mui/material';
import { Bed as BedIcon } from '@mui/icons-material';
import CollapsibleGuidance from '../common/CollapsibleGuidance';

interface BasicSleepInfoProps {
  formData: {
    sleep_patterns: string;
    sleep_initiation_difficulties: string;
    sleep_quality_duration: string;
    sleep_duration: string;
    sleep_disturbances: string;
    sleep_quality: string;
  };
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleSelectChange: (e: SelectChangeEvent<string>) => void;
}

const BasicSleepInfo: React.FC<BasicSleepInfoProps> = ({ formData, onChange, handleSelectChange }) => {
  return (
    <>
      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', gap: 1 }}>
        <BedIcon color="primary" />
        <Typography variant="h6" color="primary" fontWeight={600}>
          Basic Sleep Information
        </Typography>
      </Box>

      <CollapsibleGuidance
        title="Clinical Guidance - Sleep in Older Adults"
        contextKey="sleep_assessment_general"
      />

      <Grid container spacing={3}>
        <Grid item xs={12} sm={6}>
          <FormControl fullWidth variant="outlined">
            <InputLabel id="sleep-quality-label">Sleep Quality</InputLabel>
            <Select
              labelId="sleep-quality-label"
              id="sleep_quality"
              name="sleep_quality"
              value={formData.sleep_quality}
              onChange={handleSelectChange}
              label="Sleep Quality"
            >
              <MenuItem value="">Select Sleep Quality</MenuItem>
              <MenuItem value="Very good">Very good</MenuItem>
              <MenuItem value="Fairly good">Fairly good</MenuItem>
              <MenuItem value="Fairly bad">Fairly bad</MenuItem>
              <MenuItem value="Very bad">Very bad</MenuItem>
            </Select>
            <FormHelperText>Patient's subjective sleep quality</FormHelperText>
          </FormControl>
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            id="sleep_duration"
            name="sleep_duration"
            label="Typical Sleep Duration"
            value={formData.sleep_duration}
            onChange={onChange}
            variant="outlined"
            helperText="Typical hours of sleep per night (e.g., '6-7 hours')"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            id="sleep_patterns"
            name="sleep_patterns"
            label="Sleep Patterns"
            value={formData.sleep_patterns}
            onChange={onChange}
            variant="outlined"
            multiline
            rows={2}
            helperText="Description of sleep patterns (e.g., early riser, night owl)"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            id="sleep_initiation_difficulties"
            name="sleep_initiation_difficulties"
            label="Sleep Initiation Difficulties"
            value={formData.sleep_initiation_difficulties}
            onChange={onChange}
            variant="outlined"
            multiline
            rows={2}
            helperText="Difficulties falling asleep"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            id="sleep_quality_duration"
            name="sleep_quality_duration"
            label="Sleep Quality and Duration"
            value={formData.sleep_quality_duration}
            onChange={onChange}
            variant="outlined"
            multiline
            rows={2}
            helperText="General notes about sleep quality and duration"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            id="sleep_disturbances"
            name="sleep_disturbances"
            label="Sleep Disturbances"
            value={formData.sleep_disturbances}
            onChange={onChange}
            variant="outlined"
            multiline
            rows={2}
            helperText="Common disturbances (e.g., waking up during night, snoring)"
          />
        </Grid>
      </Grid>
    </>
  );
};

export default BasicSleepInfo;
