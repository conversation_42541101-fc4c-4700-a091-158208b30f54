import React, { useState, useEffect, useRef } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { getPatientById } from '../../services/patientService';
import { API_URL } from '../../config';
import { Patient as PatientType } from '../../types';
import {
  Typography,
  CircularProgress,
  Tooltip,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Grid,
  Paper,
  Card,
  CardContent,
  useTheme
} from '@mui/material';
import LoadingSpinner from '../common/LoadingSpinner';
import PatientHealthCharts from './PatientHealthCharts';
import VitalSignsTab from './VitalSignsTab';
import LabResultsTab from './LabResultsTab';
import HealthStatusTab from './HealthStatusTab';
import FinalMentalHealthTab from './FinalMentalHealthTab';
import VaccinationTab from './VaccinationTab';
import SleepPainTab from './SleepPainTab';
import FrailtyAssessmentTab from './FrailtyAssessmentTab';
import DiagnosisPrescriptionsTab from './DiagnosisPrescriptionsTab';
import PatientSummaryTab from './PatientSummaryTab';
import './ModernPatientDetail.css'; // Import the consolidated modern CSS file

// Use the imported PatientType
type Patient = PatientType;

interface MedicalRecord {
  record_id: number;
  patient_id: number;
  record_date: string;
  diagnosis: string;
  treatment: string;
  notes: string;
  created_by: number;
  created_by_name: string;
  created_at: string;
}

const PatientDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const theme = useTheme();
  const [patient, setPatient] = useState<Patient | null>(null);
  const [records, setRecords] = useState<MedicalRecord[]>([]);
  const [visits, setVisits] = useState<any[]>([]);
  const [recentVisit, setRecentVisit] = useState<any | null>(null);
  const [previousVisit, setPreviousVisit] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('summary');
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const tabsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const fetchPatientData = async () => {
      try {
        setLoading(true);

        // Get patient details using the service function
        const patientId = parseInt(id as string);
        let patientData = await getPatientById(patientId);

        // Get patient's medical records
        const token = localStorage.getItem('token');
        const recordsRes = await axios.get(`${API_URL}/api/records/patient/${id}`, {
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          }
        });
        setRecords(recordsRes.data);

        // Get all patient's visits
        try {
          const allVisitsRes = await axios.get(`${API_URL}/api/visits/patient/${id}`, {
            headers: {
              'Content-Type': 'application/json',
              'x-auth-token': token
            }
          });

          if (allVisitsRes.data && allVisitsRes.data.length > 0) {
            // Sort visits by date (most recent first)
            const sortedVisits = allVisitsRes.data.sort((a: any, b: any) =>
              new Date(b.visit_date).getTime() - new Date(a.visit_date).getTime()
            );
            setVisits(sortedVisits);

            // Set the most recent visit
            const recentVisitData = sortedVisits[0];
            setRecentVisit(recentVisitData);

            // Set the previous visit if available
            if (sortedVisits.length > 1) {
              const previousVisitData = sortedVisits[1];
              setPreviousVisit(previousVisitData);
            }

            // Update patient data with health metrics from the most recent visit
            // This ensures we're always showing the most up-to-date health information

            // Health fields are used in their respective sections

            // Instead of trying to modify the patient data directly,
            // we'll just use the data as is from the API
            console.log('Using patient data from API with most recent visit data:', {
              patientId: patientData.patient_id,
              visitDate: recentVisitData.visit_date
            });
          }
        } catch (visitErr) {
          console.error('Error fetching visits:', visitErr);
          // Don't set an error for this - it's not critical
        }

        setPatient(patientData);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching patient data:', err);
        setError('Patient not found');
        setLoading(false);
      }
    };

    fetchPatientData();
  }, [id]);

  const handleDeletePatient = async () => {
    try {
      const token = localStorage.getItem('token');
      await axios.delete(`${API_URL}/api/patients/${id}`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        }
      });
      navigate('/patients');
    } catch (err) {
      console.error('Error deleting patient:', err);
      setError('Failed to delete patient');
    }
  };

  const openDeleteDialog = () => {
    setDeleteDialogOpen(true);
  };

  const closeDeleteDialog = () => {
    setDeleteDialogOpen(false);
  };

  // Function to handle tab scrolling or navigation
  const scrollToTab = (tabId: string) => {
    // Special case for visits tab - navigate to dedicated visits page
    if (tabId === 'visits' && patient) {
      navigate(`/patients/${patient.patient_id}/visits`);
      return;
    }

    // For other tabs, just switch the active tab
    setActiveTab(tabId);
    // Scroll to top of content
    window.scrollTo({
      top: tabsRef.current?.offsetTop || 0,
      behavior: 'smooth'
    });
  };

  // Function to check if a value is abnormal
  const isAbnormal = (value: number | null | undefined, min: number, max: number): boolean => {
    if (value === null || value === undefined) return false;
    return value < min || value > max;
  };

  // Helper function to format boolean values
  const formatBoolean = (value: boolean | undefined | null) => {
    if (value === undefined || value === null) return 'N/A';
    return value ? 'Yes' : 'No';
  };

  const formatNumber = (value: number | string | undefined | null, suffix: string = '', decimals: number = 0) => {
    if (value === undefined || value === null) return 'N/A';
    const num = Number(value);
    return isNaN(num) ? 'N/A' : `${decimals > 0 ? num.toFixed(decimals) : num}${suffix}`;
  };



  // Calculate age from date of birth
  const calculateAge = (dateOfBirth: string): number => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  const getBMIClassification = (bmi: number): string => {
    const bmiValue = Number(bmi);
    if (bmiValue < 18.5) return 'Underweight';
    if (bmiValue < 25) return 'Normal weight';
    if (bmiValue < 30) return 'Overweight';
    if (bmiValue < 35) return 'Obese Class I';
    if (bmiValue < 40) return 'Obese Class II';
    return 'Obese Class III';
  };

  if (loading) {
    return <LoadingSpinner size="large" message="Loading patient data..." />;
  }

  if (error || !patient) {
    return (
      <div className="error-container">
        <i className="fas fa-exclamation-circle"></i>
        {error}
        <Link to="/patients" className="error-back-button">
          <i className="fas fa-arrow-left"></i> Back to Patients
        </Link>
      </div>
    );
  }

  return (
    <Box className="patient-detail-container">
      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={closeDeleteDialog}
        aria-labelledby="delete-dialog-title"
        aria-describedby="delete-dialog-description"
      >
        <DialogTitle id="delete-dialog-title">
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <i className="fas fa-exclamation-triangle" style={{ color: theme.palette.error.main }}></i>
            Confirm Patient Deletion
          </Box>
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" id="delete-dialog-description" sx={{ mt: 1 }}>
            Are you sure you want to delete {patient.first_name} {patient.last_name}? This action cannot be undone and will remove all patient data including visits and medical records.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeDeleteDialog} color="primary">
            Cancel
          </Button>
          <Button onClick={handleDeletePatient} color="error" variant="contained">
            Delete Patient
          </Button>
        </DialogActions>
      </Dialog>

      {/* Modern Patient Header */}
      <Card
        elevation={0}
        sx={{
          mb: 3,
          borderRadius: 2,
          position: 'relative',
          overflow: 'visible',
          bgcolor: theme.palette.mode === 'dark' ? 'rgba(30, 30, 30, 0.95)' : '#ffffff',
          border: theme.palette.mode === 'dark'
            ? '1px solid rgba(255, 255, 255, 0.1)'
            : `1px solid ${theme.palette.grey[100]}`,
          boxShadow: theme.palette.mode === 'dark'
            ? '0 4px 20px rgba(0, 0, 0, 0.25)'
            : '0 4px 20px rgba(0, 0, 0, 0.05)'
        }}
      >
        <Box
          sx={{
            position: 'absolute',
            left: 0,
            top: 0,
            bottom: 0,
            width: '4px',
            borderRadius: '4px 0 0 4px',
            background: theme.palette.primary.main
          }}
        />

        <CardContent sx={{ p: 3 }}>
          {/* Back to Patients Link */}
          <Box sx={{ mb: 2 }}>
            <Button
              component={Link}
              to="/patients"
              startIcon={<i className="fas fa-arrow-left"></i>}
              variant="text"
              size="small"
              sx={{
                color: theme.palette.text.secondary,
                textTransform: 'none',
                fontWeight: 500,
                p: 0.5,
                '&:hover': {
                  backgroundColor: theme.palette.mode === 'dark'
                    ? 'rgba(255, 255, 255, 0.05)'
                    : 'rgba(0, 0, 0, 0.03)'
                }
              }}
            >
              Back to Patients
            </Button>
          </Box>

          <Grid container spacing={2} alignItems="flex-start">
            {/* Patient Info */}
            <Grid item xs={12} md={7}>
              <Box>
                {/* Patient Name and ID */}
                <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 1, mb: 1.5 }}>
                  <Typography
                    variant="h4"
                    component="h1"
                    sx={{
                      fontWeight: 600,
                      color: theme.palette.text.primary,
                      lineHeight: 1.2
                    }}
                  >
                    {patient.first_name} {patient.last_name}
                  </Typography>

                  {patient.unique_id && (
                    <Tooltip title="Patient ID" arrow placement="top">
                      <Chip
                        size="small"
                        icon={<i className="fas fa-fingerprint"></i>}
                        label={patient.unique_id}
                        sx={{
                          height: '24px',
                          fontSize: '0.75rem',
                          fontWeight: 500,
                          bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : theme.palette.grey[100],
                          color: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.8)' : theme.palette.text.secondary,
                          '& .MuiChip-icon': {
                            color: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.8)' : theme.palette.text.secondary
                          }
                        }}
                      />
                    </Tooltip>
                  )}
                </Box>

                {/* Patient Demographics */}
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1.5, mb: 2 }}>
                  <Chip
                    size="small"
                    icon={<i className="fas fa-user"></i>}
                    label={`${calculateAge(patient.date_of_birth)} years • ${patient.gender}`}
                    sx={{
                      height: '28px',
                      bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : theme.palette.grey[50],
                      color: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.8)' : theme.palette.text.secondary,
                      border: theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : `1px solid ${theme.palette.grey[200]}`,
                      '& .MuiChip-icon': {
                        color: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.8)' : theme.palette.text.secondary
                      }
                    }}
                  />

                  {patient.blood_type && (
                    <Chip
                      size="small"
                      icon={<i className="fas fa-tint"></i>}
                      label={`Blood Type: ${patient.blood_type}`}
                      sx={{
                        height: '28px',
                        bgcolor: theme.palette.mode === 'dark' ? 'rgba(239, 68, 68, 0.15)' : 'rgba(239, 68, 68, 0.1)',
                        color: theme.palette.mode === 'dark' ? theme.palette.error.light : theme.palette.error.main,
                        border: theme.palette.mode === 'dark' ? '1px solid rgba(239, 68, 68, 0.2)' : '1px solid rgba(239, 68, 68, 0.2)',
                        '& .MuiChip-icon': {
                          color: theme.palette.mode === 'dark' ? theme.palette.error.light : theme.palette.error.main
                        }
                      }}
                    />
                  )}

                  {patient.doctor_name && (
                    <Chip
                      size="small"
                      icon={<i className="fas fa-user-md"></i>}
                      label={`Dr. ${patient.doctor_name}`}
                      sx={{
                        height: '28px',
                        bgcolor: theme.palette.mode === 'dark' ? 'rgba(44, 75, 43, 0.15)' : theme.palette.primary.lightest,
                        color: theme.palette.mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.main,
                        border: theme.palette.mode === 'dark' ? '1px solid rgba(44, 75, 43, 0.2)' : `1px solid ${theme.palette.primary.light}`,
                        '& .MuiChip-icon': {
                          color: theme.palette.mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.main
                        }
                      }}
                    />
                  )}
                </Box>

                {/* Contact Information */}
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, mb: 1 }}>
                  {patient.phone && (
                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <i className="fas fa-phone" style={{ width: 16, textAlign: 'center', color: theme.palette.text.secondary }}></i>
                      {patient.phone}
                    </Typography>
                  )}
                  {patient.email && (
                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <i className="fas fa-envelope" style={{ width: 16, textAlign: 'center', color: theme.palette.text.secondary }}></i>
                      {patient.email}
                    </Typography>
                  )}
                  {patient.address && (
                    <Typography variant="body2" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <i className="fas fa-home" style={{ width: 16, textAlign: 'center', color: theme.palette.text.secondary }}></i>
                      {patient.address}
                    </Typography>
                  )}
                </Box>

                {/* Last Edited Info */}
                {patient.last_edited_at && (
                  <Typography
                    variant="caption"
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      color: theme.palette.text.secondary,
                      mt: 1,
                      opacity: 0.8
                    }}
                  >
                    <i className="fas fa-history" style={{ marginRight: '6px', fontSize: '0.75rem' }}></i>
                    Last edited by {patient.last_edited_by_username || 'Unknown'} on {new Date(patient.last_edited_at).toLocaleDateString('en-GB')}
                  </Typography>
                )}
              </Box>
            </Grid>

            {/* Action Buttons */}
            <Grid item xs={12} md={5}>
              <Box sx={{
                display: 'flex',
                gap: 1.5,
                justifyContent: { xs: 'flex-start', md: 'flex-end' },
                flexWrap: 'wrap',
                mt: { xs: 1, md: 0 }
              }}>
                <Button
                  component={Link}
                  to={`/patients/safe-edit/${patient.patient_id}`}
                  variant="outlined"
                  startIcon={<i className="fas fa-edit"></i>}
                  sx={{
                    borderRadius: 2,
                    textTransform: 'none',
                    fontWeight: 500,
                    px: 2,
                    py: 1,
                    color: theme.palette.mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.main,
                    borderColor: theme.palette.mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.main,
                    '&:hover': {
                      borderColor: theme.palette.primary.main,
                      backgroundColor: theme.palette.mode === 'dark'
                        ? 'rgba(44, 75, 43, 0.1)'
                        : 'rgba(44, 75, 43, 0.05)'
                    }
                  }}
                >
                  Edit Patient
                </Button>

                <Button
                  component={Link}
                  to={`/patients/${patient.patient_id}/visits/new`}
                  variant="contained"
                  color="primary"
                  startIcon={<i className="fas fa-plus"></i>}
                  sx={{
                    borderRadius: 2,
                    textTransform: 'none',
                    fontWeight: 500,
                    px: 2,
                    py: 1,
                    boxShadow: '0 2px 8px rgba(44, 75, 43, 0.25)',
                    '&:hover': {
                      boxShadow: '0 4px 12px rgba(44, 75, 43, 0.35)'
                    }
                  }}
                >
                  New Visit
                </Button>

                <Tooltip title="Delete Patient" arrow placement="top">
                  <Button
                    variant="outlined"
                    color="error"
                    onClick={openDeleteDialog}
                    sx={{
                      minWidth: '42px',
                      width: '42px',
                      height: '42px',
                      borderRadius: 2,
                      p: 0,
                      color: theme.palette.error.main,
                      borderColor: theme.palette.error.main,
                      '&:hover': {
                        backgroundColor: 'rgba(239, 68, 68, 0.05)',
                        borderColor: theme.palette.error.dark
                      }
                    }}
                  >
                    <i className="fas fa-trash"></i>
                  </Button>
                </Tooltip>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Health Data Source Indicator */}
      {recentVisit && (
        <Box
          sx={{
            p: 2,
            mb: 3,
            borderRadius: 2,
            bgcolor: theme.palette.mode === 'dark' ? 'rgba(14, 165, 233, 0.1)' : 'rgba(14, 165, 233, 0.05)',
            color: theme.palette.mode === 'dark' ? theme.palette.info.light : theme.palette.info.dark,
            border: theme.palette.mode === 'dark'
              ? '1px solid rgba(14, 165, 233, 0.2)'
              : `1px solid ${theme.palette.info.light}`,
            borderLeft: `4px solid ${theme.palette.info.main}`,
            display: 'flex',
            alignItems: 'center',
            gap: 1.5,
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'
          }}
        >
          <Box
            sx={{
              width: 32,
              height: 32,
              borderRadius: '50%',
              bgcolor: theme.palette.mode === 'dark' ? 'rgba(14, 165, 233, 0.2)' : 'rgba(14, 165, 233, 0.1)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexShrink: 0
            }}
          >
            <i className="fas fa-info" style={{
              color: theme.palette.mode === 'dark' ? theme.palette.info.light : theme.palette.info.main,
              fontSize: '0.9rem'
            }}></i>
          </Box>
          <Box>
            <Typography variant="subtitle2" sx={{ fontWeight: 600, mb: 0.5, color: theme.palette.mode === 'dark' ? theme.palette.info.light : theme.palette.info.dark }}>
              Health Data Source
            </Typography>
            <Typography variant="body2">
              Health metrics shown below are from the most recent visit on {new Date(recentVisit.visit_date).toLocaleDateString('en-GB')}
            </Typography>
          </Box>
          <Button
            component={Link}
            to={`/patients/${patient.patient_id}/visits/${recentVisit.visit_id}`}
            variant="text"
            size="small"
            sx={{
              ml: 'auto',
              color: theme.palette.mode === 'dark' ? theme.palette.info.light : theme.palette.info.main,
              textTransform: 'none',
              fontWeight: 500,
              '&:hover': {
                backgroundColor: theme.palette.mode === 'dark'
                  ? 'rgba(14, 165, 233, 0.15)'
                  : 'rgba(14, 165, 233, 0.1)'
              }
            }}
          >
            View Visit
            <i className="fas fa-chevron-right" style={{ marginLeft: '4px', fontSize: '0.75rem' }}></i>
          </Button>
        </Box>
      )}

      {/* Key Health Metrics */}
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        flexWrap: 'wrap',
        gap: 2,
        mb: 2
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
          <Box
            sx={{
              width: 40,
              height: 40,
              borderRadius: 1.5,
              bgcolor: theme.palette.mode === 'dark' ? 'rgba(44, 75, 43, 0.15)' : 'rgba(44, 75, 43, 0.1)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              flexShrink: 0
            }}
          >
            <i className="fas fa-heartbeat" style={{
              color: theme.palette.mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.main,
              fontSize: '1.1rem'
            }}></i>
          </Box>
          <Typography variant="h5" sx={{ fontWeight: 600, color: theme.palette.text.primary }}>
            Key Health Indicators
          </Typography>
        </Box>

        {recentVisit && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Chip
              size="small"
              icon={<i className="fas fa-calendar-alt"></i>}
              label={`From ${new Date(recentVisit.visit_date).toLocaleDateString('en-GB')}`}
              sx={{
                height: '28px',
                fontSize: '0.75rem',
                fontWeight: 500,
                bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : theme.palette.grey[100],
                color: theme.palette.text.secondary,
                '& .MuiChip-icon': { color: theme.palette.text.secondary }
              }}
            />
            <Button
              component={Link}
              to={`/patients/${patient.patient_id}/visits`}
              variant="text"
              size="small"
              sx={{
                color: theme.palette.primary.main,
                textTransform: 'none',
                fontWeight: 500,
                '&:hover': {
                  backgroundColor: theme.palette.mode === 'dark'
                    ? 'rgba(44, 75, 43, 0.1)'
                    : 'rgba(44, 75, 43, 0.05)'
                }
              }}
            >
              All Visits
              <i className="fas fa-chevron-right" style={{ marginLeft: '4px', fontSize: '0.75rem' }}></i>
            </Button>
          </Box>
        )}
      </Box>

      {/* Define styled components for metric cards */}
      <Box sx={{ mb: 4 }}>
        <Grid container spacing={2}>
          {/* Blood Glucose */}
          <Grid item xs={12} sm={6} md={4}>
            <Card
              elevation={0}
              sx={{
                height: '100%',
                p: 2.5,
                borderRadius: 2,
                border: theme.palette.mode === 'dark'
                  ? '1px solid rgba(255, 255, 255, 0.1)'
                  : `1px solid ${theme.palette.grey[100]}`,
                position: 'relative',
                overflow: 'hidden',
                transition: 'all 0.2s ease',
                boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
                bgcolor: theme.palette.mode === 'dark' ? 'rgba(30, 30, 30, 0.95)' : '#ffffff',
                '&:hover': {
                  transform: 'translateY(-3px)',
                  boxShadow: '0 6px 16px rgba(0,0,0,0.1)',
                  borderColor: theme.palette.mode === 'dark'
                    ? 'rgba(255, 255, 255, 0.2)'
                    : theme.palette.grey[200]
                }
              }}
            >
              {/* Accent color strip */}
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '4px',
                  height: '100%',
                  bgcolor: theme.palette.error.main
                }}
              />

              {/* Header with icon and title */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                  <Box
                    sx={{
                      width: 36,
                      height: 36,
                      borderRadius: 1.5,
                      bgcolor: theme.palette.mode === 'dark' ? 'rgba(239, 68, 68, 0.15)' : theme.palette.error.lightest,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <i className="fas fa-tint" style={{
                      color: theme.palette.mode === 'dark' ? theme.palette.error.light : theme.palette.error.main,
                      fontSize: '1rem'
                    }}></i>
                  </Box>
                  <Typography
                    variant="subtitle1"
                    sx={{
                      fontSize: '0.9rem',
                      fontWeight: 600,
                      color: theme.palette.text.primary
                    }}
                  >
                    Blood Glucose
                  </Typography>
                </Box>

                {/* Abnormal indicator */}
                {isAbnormal(patient.blood_glucose, 70, 140) && (
                  <Tooltip title="Outside normal range" arrow placement="top">
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 0.5,
                        bgcolor: theme.palette.mode === 'dark' ? 'rgba(239, 68, 68, 0.15)' : theme.palette.error.lightest,
                        color: theme.palette.mode === 'dark' ? theme.palette.error.light : theme.palette.error.main,
                        borderRadius: 1,
                        px: 1,
                        py: 0.5
                      }}
                    >
                      <i className="fas fa-exclamation-triangle" style={{ fontSize: '0.7rem' }}></i>
                      <Typography variant="caption" sx={{ fontWeight: 600, fontSize: '0.7rem' }}>
                        Abnormal
                      </Typography>
                    </Box>
                  </Tooltip>
                )}
              </Box>

              {/* Value display */}
              <Box sx={{ mt: 1 }}>
                <Typography
                  variant="h4"
                  sx={{
                    fontWeight: 700,
                    fontSize: '2rem',
                    color: isAbnormal(patient.blood_glucose, 70, 140)
                      ? theme.palette.mode === 'dark' ? theme.palette.error.light : theme.palette.error.main
                      : theme.palette.text.primary,
                    display: 'flex',
                    alignItems: 'baseline'
                  }}
                >
                  {patient.blood_glucose ? `${patient.blood_glucose}` : 'N/A'}
                  {patient.blood_glucose && (
                    <Typography component="span" variant="body2" color="textSecondary" sx={{ ml: 0.75, fontSize: '0.875rem', fontWeight: 500 }}>
                      mg/dL
                    </Typography>
                  )}
                </Typography>

                {/* Normal range info */}
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 0.75,
                    mt: 1,
                    color: theme.palette.text.secondary
                  }}
                >
                  <i className="fas fa-info-circle" style={{ fontSize: '0.75rem' }}></i>
                  <Typography variant="caption" sx={{ fontSize: '0.75rem' }}>
                    Normal range: 70-140 mg/dL
                  </Typography>
                </Box>
              </Box>
            </Card>
          </Grid>

          {/* Blood Pressure */}
          <Grid item xs={12} sm={6} md={4}>
            <Card
              elevation={0}
              sx={{
                height: '100%',
                p: 2.5,
                borderRadius: 2,
                border: theme.palette.mode === 'dark'
                  ? '1px solid rgba(255, 255, 255, 0.1)'
                  : `1px solid ${theme.palette.grey[100]}`,
                position: 'relative',
                overflow: 'hidden',
                transition: 'all 0.2s ease',
                boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
                bgcolor: theme.palette.mode === 'dark' ? 'rgba(30, 30, 30, 0.95)' : '#ffffff',
                '&:hover': {
                  transform: 'translateY(-3px)',
                  boxShadow: '0 6px 16px rgba(0,0,0,0.1)',
                  borderColor: theme.palette.mode === 'dark'
                    ? 'rgba(255, 255, 255, 0.2)'
                    : theme.palette.grey[200]
                }
              }}
            >
              {/* Accent color strip */}
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '4px',
                  height: '100%',
                  bgcolor: theme.palette.primary.main
                }}
              />

              {/* Header with icon and title */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                  <Box
                    sx={{
                      width: 36,
                      height: 36,
                      borderRadius: 1.5,
                      bgcolor: theme.palette.mode === 'dark' ? 'rgba(44, 75, 43, 0.15)' : theme.palette.primary.lightest,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <i className="fas fa-heart" style={{
                      color: theme.palette.mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.main,
                      fontSize: '1rem'
                    }}></i>
                  </Box>
                  <Typography
                    variant="subtitle1"
                    sx={{
                      fontSize: '0.9rem',
                      fontWeight: 600,
                      color: theme.palette.text.primary
                    }}
                  >
                    Blood Pressure
                  </Typography>
                </Box>

                {/* Abnormal indicator */}
                {(isAbnormal(patient.lying_bp_systolic, 90, 140) ||
                  isAbnormal(patient.lying_bp_diastolic, 60, 90) ||
                  isAbnormal(patient.standing_bp_systolic, 90, 140) ||
                  isAbnormal(patient.standing_bp_diastolic, 60, 90)) && (
                  <Tooltip title="Outside normal range" arrow placement="top">
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 0.5,
                        bgcolor: theme.palette.mode === 'dark' ? 'rgba(239, 68, 68, 0.15)' : theme.palette.error.lightest,
                        color: theme.palette.mode === 'dark' ? theme.palette.error.light : theme.palette.error.main,
                        borderRadius: 1,
                        px: 1,
                        py: 0.5
                      }}
                    >
                      <i className="fas fa-exclamation-triangle" style={{ fontSize: '0.7rem' }}></i>
                      <Typography variant="caption" sx={{ fontWeight: 600, fontSize: '0.7rem' }}>
                        Abnormal
                      </Typography>
                    </Box>
                  </Tooltip>
                )}
              </Box>

              {/* BP Values */}
              <Box sx={{ mt: 1 }}>
                <Grid container spacing={2}>
                  {/* Lying BP */}
                  <Grid item xs={6}>
                    <Box
                      sx={{
                        p: 1.5,
                        borderRadius: 1.5,
                        bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.03)' : 'rgba(0, 0, 0, 0.02)',
                        border: theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.05)' : '1px solid rgba(0, 0, 0, 0.03)'
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.75, mb: 1 }}>
                        <i className="fas fa-bed" style={{
                          color: theme.palette.text.secondary,
                          fontSize: '0.75rem'
                        }}></i>
                        <Typography variant="caption" sx={{
                          color: theme.palette.text.secondary,
                          fontWeight: 600,
                          fontSize: '0.75rem'
                        }}>
                          Lying
                        </Typography>
                      </Box>

                      <Typography
                        variant="h5"
                        sx={{
                          fontWeight: 700,
                          fontSize: '1.5rem',
                          color: isAbnormal(patient.lying_bp_systolic, 90, 140) || isAbnormal(patient.lying_bp_diastolic, 60, 90)
                            ? theme.palette.mode === 'dark' ? theme.palette.error.light : theme.palette.error.main
                            : theme.palette.text.primary,
                          display: 'flex',
                          alignItems: 'baseline'
                        }}
                      >
                        {patient.lying_bp_systolic && patient.lying_bp_diastolic
                          ? `${patient.lying_bp_systolic}/${patient.lying_bp_diastolic}`
                          : 'N/A'}
                        {patient.lying_bp_systolic && patient.lying_bp_diastolic && (
                          <Typography component="span" variant="body2" color="textSecondary" sx={{ ml: 0.5, fontSize: '0.75rem', fontWeight: 500 }}>
                            mmHg
                          </Typography>
                        )}
                      </Typography>
                    </Box>
                  </Grid>

                  {/* Standing BP */}
                  <Grid item xs={6}>
                    <Box
                      sx={{
                        p: 1.5,
                        borderRadius: 1.5,
                        bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.03)' : 'rgba(0, 0, 0, 0.02)',
                        border: theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.05)' : '1px solid rgba(0, 0, 0, 0.03)'
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.75, mb: 1 }}>
                        <i className="fas fa-walking" style={{
                          color: theme.palette.text.secondary,
                          fontSize: '0.75rem'
                        }}></i>
                        <Typography variant="caption" sx={{
                          color: theme.palette.text.secondary,
                          fontWeight: 600,
                          fontSize: '0.75rem'
                        }}>
                          Standing
                        </Typography>
                      </Box>

                      <Typography
                        variant="h5"
                        sx={{
                          fontWeight: 700,
                          fontSize: '1.5rem',
                          color: isAbnormal(patient.standing_bp_systolic, 90, 140) || isAbnormal(patient.standing_bp_diastolic, 60, 90)
                            ? theme.palette.mode === 'dark' ? theme.palette.error.light : theme.palette.error.main
                            : theme.palette.text.primary,
                          display: 'flex',
                          alignItems: 'baseline'
                        }}
                      >
                        {patient.standing_bp_systolic && patient.standing_bp_diastolic
                          ? `${patient.standing_bp_systolic}/${patient.standing_bp_diastolic}`
                          : 'N/A'}
                        {patient.standing_bp_systolic && patient.standing_bp_diastolic && (
                          <Typography component="span" variant="body2" color="textSecondary" sx={{ ml: 0.5, fontSize: '0.75rem', fontWeight: 500 }}>
                            mmHg
                          </Typography>
                        )}
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>

                {/* Normal range info */}
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 0.75,
                    mt: 1.5,
                    color: theme.palette.text.secondary
                  }}
                >
                  <i className="fas fa-info-circle" style={{ fontSize: '0.75rem' }}></i>
                  <Typography variant="caption" sx={{ fontSize: '0.75rem' }}>
                    Normal range: 90-140/60-90 mmHg
                  </Typography>
                </Box>
              </Box>
            </Card>
          </Grid>

          {/* Heart Rate */}
          <Grid item xs={12} sm={6} md={4}>
            <Card
              elevation={0}
              sx={{
                height: '100%',
                p: 2.5,
                borderRadius: 2,
                border: theme.palette.mode === 'dark'
                  ? '1px solid rgba(255, 255, 255, 0.1)'
                  : `1px solid ${theme.palette.grey[100]}`,
                position: 'relative',
                overflow: 'hidden',
                transition: 'all 0.2s ease',
                boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
                bgcolor: theme.palette.mode === 'dark' ? 'rgba(30, 30, 30, 0.95)' : '#ffffff',
                '&:hover': {
                  transform: 'translateY(-3px)',
                  boxShadow: '0 6px 16px rgba(0,0,0,0.1)',
                  borderColor: theme.palette.mode === 'dark'
                    ? 'rgba(255, 255, 255, 0.2)'
                    : theme.palette.grey[200]
                }
              }}
            >
              {/* Accent color strip */}
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '4px',
                  height: '100%',
                  bgcolor: theme.palette.warning.main
                }}
              />

              {/* Header with icon and title */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                  <Box
                    sx={{
                      width: 36,
                      height: 36,
                      borderRadius: 1.5,
                      bgcolor: theme.palette.mode === 'dark' ? 'rgba(246, 178, 26, 0.15)' : theme.palette.warning.lightest,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <i className="fas fa-heartbeat" style={{
                      color: theme.palette.mode === 'dark' ? theme.palette.warning.light : theme.palette.warning.main,
                      fontSize: '1rem'
                    }}></i>
                  </Box>
                  <Typography
                    variant="subtitle1"
                    sx={{
                      fontSize: '0.9rem',
                      fontWeight: 600,
                      color: theme.palette.text.primary
                    }}
                  >
                    Heart Rate
                  </Typography>
                </Box>

                {/* Abnormal indicator */}
                {(isAbnormal(patient.lying_heart_rate, 60, 100) || isAbnormal(patient.standing_heart_rate, 60, 100)) && (
                  <Tooltip title="Outside normal range" arrow placement="top">
                    <Box
                      sx={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: 0.5,
                        bgcolor: theme.palette.mode === 'dark' ? 'rgba(239, 68, 68, 0.15)' : theme.palette.error.lightest,
                        color: theme.palette.mode === 'dark' ? theme.palette.error.light : theme.palette.error.main,
                        borderRadius: 1,
                        px: 1,
                        py: 0.5
                      }}
                    >
                      <i className="fas fa-exclamation-triangle" style={{ fontSize: '0.7rem' }}></i>
                      <Typography variant="caption" sx={{ fontWeight: 600, fontSize: '0.7rem' }}>
                        Abnormal
                      </Typography>
                    </Box>
                  </Tooltip>
                )}
              </Box>

              {/* Heart Rate Values */}
              <Box sx={{ mt: 1 }}>
                <Grid container spacing={2}>
                  {/* Lying Heart Rate */}
                  <Grid item xs={6}>
                    <Box
                      sx={{
                        p: 1.5,
                        borderRadius: 1.5,
                        bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.03)' : 'rgba(0, 0, 0, 0.02)',
                        border: theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.05)' : '1px solid rgba(0, 0, 0, 0.03)'
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.75, mb: 1 }}>
                        <i className="fas fa-bed" style={{
                          color: theme.palette.text.secondary,
                          fontSize: '0.75rem'
                        }}></i>
                        <Typography variant="caption" sx={{
                          color: theme.palette.text.secondary,
                          fontWeight: 600,
                          fontSize: '0.75rem'
                        }}>
                          Lying
                        </Typography>
                      </Box>

                      <Typography
                        variant="h5"
                        sx={{
                          fontWeight: 700,
                          fontSize: '1.5rem',
                          color: isAbnormal(patient.lying_heart_rate, 60, 100)
                            ? theme.palette.mode === 'dark' ? theme.palette.error.light : theme.palette.error.main
                            : theme.palette.text.primary,
                          display: 'flex',
                          alignItems: 'baseline'
                        }}
                      >
                        {patient.lying_heart_rate ? `${patient.lying_heart_rate}` : 'N/A'}
                        {patient.lying_heart_rate && (
                          <Typography component="span" variant="body2" color="textSecondary" sx={{ ml: 0.5, fontSize: '0.75rem', fontWeight: 500 }}>
                            bpm
                          </Typography>
                        )}
                      </Typography>
                    </Box>
                  </Grid>

                  {/* Standing Heart Rate */}
                  <Grid item xs={6}>
                    <Box
                      sx={{
                        p: 1.5,
                        borderRadius: 1.5,
                        bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.03)' : 'rgba(0, 0, 0, 0.02)',
                        border: theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.05)' : '1px solid rgba(0, 0, 0, 0.03)'
                      }}
                    >
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.75, mb: 1 }}>
                        <i className="fas fa-walking" style={{
                          color: theme.palette.text.secondary,
                          fontSize: '0.75rem'
                        }}></i>
                        <Typography variant="caption" sx={{
                          color: theme.palette.text.secondary,
                          fontWeight: 600,
                          fontSize: '0.75rem'
                        }}>
                          Standing
                        </Typography>
                      </Box>

                      <Typography
                        variant="h5"
                        sx={{
                          fontWeight: 700,
                          fontSize: '1.5rem',
                          color: isAbnormal(patient.standing_heart_rate, 60, 100)
                            ? theme.palette.mode === 'dark' ? theme.palette.error.light : theme.palette.error.main
                            : theme.palette.text.primary,
                          display: 'flex',
                          alignItems: 'baseline'
                        }}
                      >
                        {patient.standing_heart_rate ? `${patient.standing_heart_rate}` : 'N/A'}
                        {patient.standing_heart_rate && (
                          <Typography component="span" variant="body2" color="textSecondary" sx={{ ml: 0.5, fontSize: '0.75rem', fontWeight: 500 }}>
                            bpm
                          </Typography>
                        )}
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>

                {/* Normal range info */}
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: 0.75,
                    mt: 1.5,
                    color: theme.palette.text.secondary
                  }}
                >
                  <i className="fas fa-info-circle" style={{ fontSize: '0.75rem' }}></i>
                  <Typography variant="caption" sx={{ fontSize: '0.75rem' }}>
                    Normal range: 60-100 bpm
                  </Typography>
                </Box>
              </Box>
            </Card>
          </Grid>

          {/* BMI */}
          <Grid item xs={12} sm={6} md={4}>
            <Card
              elevation={0}
              sx={{
                height: '100%',
                p: 1.75,
                borderRadius: 2,
                border: theme.palette.mode === 'dark'
                  ? '1px solid rgba(255, 255, 255, 0.1)'
                  : `1px solid ${theme.palette.grey[100]}`,
                position: 'relative',
                overflow: 'hidden',
                transition: 'all 0.2s ease',
                boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
                bgcolor: theme.palette.mode === 'dark' ? '#1e1e1e' : undefined,
                '&:hover': {
                  transform: 'translateY(-3px)',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
                  borderColor: theme.palette.mode === 'dark'
                    ? 'rgba(255, 255, 255, 0.2)'
                    : theme.palette.grey[200]
                }
              }}
            >
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '3px',
                  height: '100%',
                  bgcolor: theme.palette.secondary.main
                }}
              />

              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                  <Box
                    sx={{
                      width: 32,
                      height: 32,
                      borderRadius: 1,
                      bgcolor: theme.palette.secondary.lightest,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 1.25
                    }}
                  >
                    <i className="fas fa-weight" style={{ color: theme.palette.secondary.main, fontSize: '0.9rem' }}></i>
                  </Box>
                  <Typography
                    variant="subtitle2"
                    sx={{
                      textTransform: 'uppercase',
                      fontSize: '0.7rem',
                      letterSpacing: '0.5px',
                      fontWeight: 600,
                      color: theme.palette.text.secondary
                    }}
                  >
                    BMI
                  </Typography>
                </Box>

                {patient.bmi && (patient.bmi < 18.5 || patient.bmi >= 25) && (
                  <Tooltip title="Outside normal range" arrow placement="top">
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: theme.palette.error.lightest,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: theme.palette.error.main
                      }}
                    >
                      <i className="fas fa-exclamation" style={{ fontSize: '0.6rem' }}></i>
                    </Box>
                  </Tooltip>
                )}
              </Box>

              <Box sx={{ mt: 0.5 }}>
                <Box sx={{ display: 'flex', alignItems: 'baseline' }}>
                  <Typography
                    variant="h5"
                    sx={{
                      fontWeight: 700,
                      fontSize: '1.5rem',
                      color: patient.bmi && (patient.bmi < 18.5 || patient.bmi >= 25) ? theme.palette.error.main : theme.palette.text.primary,
                      display: 'flex',
                      alignItems: 'baseline'
                    }}
                  >
                    {patient.bmi ? formatNumber(patient.bmi, '', 1) : 'N/A'}
                  </Typography>

                  {patient.bmi && (
                    <Typography variant="body2" color="textSecondary" sx={{ ml: 1, fontSize: '0.8rem', fontWeight: 500 }}>
                      {getBMIClassification(Number(patient.bmi))}
                    </Typography>
                  )}
                </Box>

                <Grid container spacing={1} sx={{ mt: 0.75 }}>
                  <Grid item xs={6}>
                    <Typography variant="caption" sx={{ color: theme.palette.text.secondary, fontSize: '0.7rem', fontWeight: 600 }}>
                      Height
                    </Typography>
                    <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                      {patient.height ? `${patient.height} cm` : 'N/A'}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="caption" sx={{ color: theme.palette.text.secondary, fontSize: '0.7rem', fontWeight: 600 }}>
                      Weight
                    </Typography>
                    <Typography variant="body2" sx={{ fontSize: '0.85rem' }}>
                      {patient.weight ? `${patient.weight} kg` : 'N/A'}
                    </Typography>
                  </Grid>
                </Grid>

                <Typography variant="caption" sx={{ color: theme.palette.text.secondary, fontSize: '0.7rem', display: 'block', mt: 0.75 }}>
                  Normal range: 18.5-24.9
                </Typography>
              </Box>
            </Card>
          </Grid>

          {/* Temperature */}
          <Grid item xs={12} sm={6} md={4}>
            <Card
              elevation={0}
              sx={{
                height: '100%',
                p: 1.75,
                borderRadius: 2,
                border: `1px solid ${theme.palette.grey[100]}`,
                position: 'relative',
                overflow: 'hidden',
                transition: 'all 0.2s ease',
                boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
                '&:hover': {
                  transform: 'translateY(-3px)',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
                  borderColor: theme.palette.grey[200]
                }
              }}
            >
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '3px',
                  height: '100%',
                  bgcolor: theme.palette.warning.main
                }}
              />

              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                  <Box
                    sx={{
                      width: 32,
                      height: 32,
                      borderRadius: 1,
                      bgcolor: theme.palette.warning.lightest,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 1.25
                    }}
                  >
                    <i className="fas fa-thermometer-half" style={{ color: theme.palette.warning.main, fontSize: '0.9rem' }}></i>
                  </Box>
                  <Typography
                    variant="subtitle2"
                    sx={{
                      textTransform: 'uppercase',
                      fontSize: '0.7rem',
                      letterSpacing: '0.5px',
                      fontWeight: 600,
                      color: theme.palette.text.secondary
                    }}
                  >
                    Temperature
                  </Typography>
                </Box>

                {isAbnormal(patient.body_temperature, 36.1, 37.2) && (
                  <Tooltip title="Outside normal range" arrow placement="top">
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: theme.palette.error.lightest,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: theme.palette.error.main
                      }}
                    >
                      <i className="fas fa-exclamation" style={{ fontSize: '0.6rem' }}></i>
                    </Box>
                  </Tooltip>
                )}
              </Box>

              <Box sx={{ mt: 0.5 }}>
                <Typography
                  variant="h5"
                  sx={{
                    fontWeight: 700,
                    fontSize: '1.5rem',
                    color: isAbnormal(patient.body_temperature, 36.1, 37.2) ? theme.palette.error.main : theme.palette.text.primary,
                    display: 'flex',
                    alignItems: 'baseline'
                  }}
                >
                  {patient.body_temperature ? `${patient.body_temperature}` : 'N/A'}
                  {patient.body_temperature && (
                    <Typography component="span" variant="body2" color="textSecondary" sx={{ ml: 0.5, fontSize: '0.75rem', fontWeight: 500 }}>
                      °C
                    </Typography>
                  )}
                </Typography>

                <Typography variant="caption" sx={{ color: theme.palette.text.secondary, fontSize: '0.7rem', display: 'block', mt: 0.75 }}>
                  Normal range: 36.1-37.2 °C
                </Typography>
              </Box>
            </Card>
          </Grid>

          {/* Oxygen Saturation */}
          <Grid item xs={12} sm={6} md={4}>
            <Card
              elevation={0}
              sx={{
                height: '100%',
                p: 1.75,
                borderRadius: 2,
                border: `1px solid ${theme.palette.grey[100]}`,
                position: 'relative',
                overflow: 'hidden',
                transition: 'all 0.2s ease',
                boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
                '&:hover': {
                  transform: 'translateY(-3px)',
                  boxShadow: '0 4px 12px rgba(0,0,0,0.08)',
                  borderColor: theme.palette.grey[200]
                }
              }}
            >
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  width: '3px',
                  height: '100%',
                  bgcolor: theme.palette.info.main
                }}
              />

              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5 }}>
                  <Box
                    sx={{
                      width: 32,
                      height: 32,
                      borderRadius: 1,
                      bgcolor: theme.palette.info.lightest,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 1.25
                    }}
                  >
                    <i className="fas fa-lungs" style={{ color: theme.palette.info.main, fontSize: '0.9rem' }}></i>
                  </Box>
                  <Typography
                    variant="subtitle2"
                    sx={{
                      textTransform: 'uppercase',
                      fontSize: '0.7rem',
                      letterSpacing: '0.5px',
                      fontWeight: 600,
                      color: theme.palette.text.secondary
                    }}
                  >
                    Oxygen Saturation
                  </Typography>
                </Box>

                {isAbnormal(patient.pulse_oximetry, 95, 100) && (
                  <Tooltip title="Outside normal range" arrow placement="top">
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        borderRadius: '50%',
                        bgcolor: theme.palette.error.lightest,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: theme.palette.error.main
                      }}
                    >
                      <i className="fas fa-exclamation" style={{ fontSize: '0.6rem' }}></i>
                    </Box>
                  </Tooltip>
                )}
              </Box>

              <Box sx={{ mt: 0.5 }}>
                <Typography
                  variant="h5"
                  sx={{
                    fontWeight: 700,
                    fontSize: '1.5rem',
                    color: isAbnormal(patient.pulse_oximetry, 95, 100) ? theme.palette.error.main : theme.palette.text.primary,
                    display: 'flex',
                    alignItems: 'baseline'
                  }}
                >
                  {patient.pulse_oximetry ? `${patient.pulse_oximetry}` : 'N/A'}
                  {patient.pulse_oximetry && (
                    <Typography component="span" variant="body2" color="textSecondary" sx={{ ml: 0.5, fontSize: '0.75rem', fontWeight: 500 }}>
                      %
                    </Typography>
                  )}
                </Typography>

                <Typography variant="caption" sx={{ color: theme.palette.text.secondary, fontSize: '0.7rem', display: 'block', mt: 0.75 }}>
                  Normal range: 95-100 %
                </Typography>
              </Box>
            </Card>
          </Grid>
        </Grid>
      </Box>

      {/* Modern Tabs Navigation */}
      <Paper
        elevation={1}
        sx={{
          mb: 3,
          borderRadius: 2,
          position: 'sticky',
          top: 16,
          zIndex: 10,
          bgcolor: 'white'
        }}
        ref={tabsRef}
      >
        <Box
          sx={{
            display: 'flex',
            overflowX: 'auto',
            scrollbarWidth: 'none',
            '&::-webkit-scrollbar': { display: 'none' },
            px: 1
          }}
        >
          <Button
            onClick={() => scrollToTab('summary')}
            startIcon={<i className="fas fa-clipboard-list section-icon" style={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem', marginRight: '-4px' }}></i>}
            sx={{
              py: 1.5,
              px: 1.5,
              color: activeTab === 'summary' ? theme.palette.primary.main : theme.palette.text.secondary,
              fontWeight: activeTab === 'summary' ? 600 : 500,
              borderRadius: 0,
              borderBottom: activeTab === 'summary' ? `3px solid ${theme.palette.primary.main}` : '3px solid transparent',
              textTransform: 'none',
              minWidth: 'auto',
              whiteSpace: 'nowrap',
              '&:hover': {
                bgcolor: theme.palette.primary.lightest,
                borderBottomColor: activeTab !== 'summary' ? theme.palette.primary.lightest : theme.palette.primary.main
              }
            }}
          >
            Summary
          </Button>

          <Button
            onClick={() => scrollToTab('visits')}
            startIcon={<i className="fas fa-calendar-alt section-icon" style={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem', marginRight: '-4px' }}></i>}
            endIcon={visits.length > 0 && (
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 0.5
              }}>
                <Chip
                  label={visits.length}
                  size="small"
                  sx={{
                    height: 18,
                    fontSize: '0.7rem',
                    fontWeight: 600,
                    bgcolor: theme.palette.grey[200]
                  }}
                />
                <i className="fas fa-external-link-alt" style={{ fontSize: '0.7rem' }}></i>
              </Box>
            )}
            sx={{
              py: 1.5,
              px: 1.5,
              color: activeTab === 'visits' ? theme.palette.primary.main : theme.palette.text.secondary,
              fontWeight: activeTab === 'visits' ? 600 : 500,
              borderRadius: 0,
              borderBottom: activeTab === 'visits' ? `3px solid ${theme.palette.primary.main}` : '3px solid transparent',
              textTransform: 'none',
              minWidth: 'auto',
              whiteSpace: 'nowrap',
              '&:hover': {
                bgcolor: theme.palette.primary.lightest,
                borderBottomColor: activeTab !== 'visits' ? theme.palette.primary.lightest : theme.palette.primary.main
              }
            }}
          >
            Visits
          </Button>

          <Button
            onClick={() => scrollToTab('vitals')}
            startIcon={<i className="fas fa-heartbeat section-icon" style={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem', marginRight: '-4px' }}></i>}
            sx={{
              py: 1.5,
              px: 1.5,
              color: activeTab === 'vitals' ? theme.palette.primary.main : theme.palette.text.secondary,
              fontWeight: activeTab === 'vitals' ? 600 : 500,
              borderRadius: 0,
              borderBottom: activeTab === 'vitals' ? `3px solid ${theme.palette.primary.main}` : '3px solid transparent',
              textTransform: 'none',
              minWidth: 'auto',
              whiteSpace: 'nowrap',
              '&:hover': {
                bgcolor: theme.palette.primary.lightest,
                borderBottomColor: activeTab !== 'vitals' ? theme.palette.primary.lightest : theme.palette.primary.main
              }
            }}
          >
            Vital Signs
          </Button>

          <Button
            onClick={() => scrollToTab('lab')}
            startIcon={<i className="fas fa-flask section-icon" style={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem', marginRight: '-4px' }}></i>}
            sx={{
              py: 1.5,
              px: 1.5,
              color: activeTab === 'lab' ? theme.palette.primary.main : theme.palette.text.secondary,
              fontWeight: activeTab === 'lab' ? 600 : 500,
              borderRadius: 0,
              borderBottom: activeTab === 'lab' ? `3px solid ${theme.palette.primary.main}` : '3px solid transparent',
              textTransform: 'none',
              minWidth: 'auto',
              whiteSpace: 'nowrap',
              '&:hover': {
                bgcolor: theme.palette.primary.lightest,
                borderBottomColor: activeTab !== 'lab' ? theme.palette.primary.lightest : theme.palette.primary.main
              }
            }}
          >
            Lab Results
          </Button>

          <Button
            onClick={() => scrollToTab('health')}
            startIcon={<i className="fas fa-notes-medical section-icon" style={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem', marginRight: '-4px' }}></i>}
            sx={{
              py: 1.5,
              px: 1.5,
              color: activeTab === 'health' ? theme.palette.primary.main : theme.palette.text.secondary,
              fontWeight: activeTab === 'health' ? 600 : 500,
              borderRadius: 0,
              borderBottom: activeTab === 'health' ? `3px solid ${theme.palette.primary.main}` : '3px solid transparent',
              textTransform: 'none',
              minWidth: 'auto',
              whiteSpace: 'nowrap',
              '&:hover': {
                bgcolor: theme.palette.primary.lightest,
                borderBottomColor: activeTab !== 'health' ? theme.palette.primary.lightest : theme.palette.primary.main
              }
            }}
          >
            Health Status
          </Button>

          <Button
            onClick={() => scrollToTab('mental')}
            startIcon={<i className="fas fa-brain section-icon" style={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem', marginRight: '-4px' }}></i>}
            sx={{
              py: 1.5,
              px: 1.5,
              color: activeTab === 'mental' ? theme.palette.primary.main : theme.palette.text.secondary,
              fontWeight: activeTab === 'mental' ? 600 : 500,
              borderRadius: 0,
              borderBottom: activeTab === 'mental' ? `3px solid ${theme.palette.primary.main}` : '3px solid transparent',
              textTransform: 'none',
              minWidth: 'auto',
              whiteSpace: 'nowrap',
              '&:hover': {
                bgcolor: theme.palette.primary.lightest,
                borderBottomColor: activeTab !== 'mental' ? theme.palette.primary.lightest : theme.palette.primary.main
              }
            }}
          >
            Mental Health
          </Button>

          <Button
            onClick={() => scrollToTab('sleep')}
            startIcon={<i className="fas fa-bed section-icon" style={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem', marginRight: '-4px' }}></i>}
            sx={{
              py: 1.5,
              px: 1.5,
              color: activeTab === 'sleep' ? theme.palette.primary.main : theme.palette.text.secondary,
              fontWeight: activeTab === 'sleep' ? 600 : 500,
              borderRadius: 0,
              borderBottom: activeTab === 'sleep' ? `3px solid ${theme.palette.primary.main}` : '3px solid transparent',
              textTransform: 'none',
              minWidth: 'auto',
              whiteSpace: 'nowrap',
              '&:hover': {
                bgcolor: theme.palette.primary.lightest,
                borderBottomColor: activeTab !== 'sleep' ? theme.palette.primary.lightest : theme.palette.primary.main
              }
            }}
          >
            Sleep & Pain
          </Button>

          <Button
            onClick={() => scrollToTab('frailty')}
            startIcon={<i className="fas fa-walking section-icon" style={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem', marginRight: '-4px' }}></i>}
            sx={{
              py: 1.5,
              px: 1.5,
              color: activeTab === 'frailty' ? theme.palette.primary.main : theme.palette.text.secondary,
              fontWeight: activeTab === 'frailty' ? 600 : 500,
              borderRadius: 0,
              borderBottom: activeTab === 'frailty' ? `3px solid ${theme.palette.primary.main}` : '3px solid transparent',
              textTransform: 'none',
              minWidth: 'auto',
              whiteSpace: 'nowrap',
              '&:hover': {
                bgcolor: theme.palette.primary.lightest,
                borderBottomColor: activeTab !== 'frailty' ? theme.palette.primary.lightest : theme.palette.primary.main
              }
            }}
          >
            Frailty Assessment
          </Button>

          <Button
            onClick={() => scrollToTab('charts')}
            startIcon={<i className="fas fa-chart-line section-icon" style={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem', marginRight: '-4px' }}></i>}
            sx={{
              py: 1.5,
              px: 1.5,
              color: activeTab === 'charts' ? theme.palette.primary.main : theme.palette.text.secondary,
              fontWeight: activeTab === 'charts' ? 600 : 500,
              borderRadius: 0,
              borderBottom: activeTab === 'charts' ? `3px solid ${theme.palette.primary.main}` : '3px solid transparent',
              textTransform: 'none',
              minWidth: 'auto',
              whiteSpace: 'nowrap',
              '&:hover': {
                bgcolor: theme.palette.primary.lightest,
                borderBottomColor: activeTab !== 'charts' ? theme.palette.primary.lightest : theme.palette.primary.main
              }
            }}
          >
            Health Charts
          </Button>

          <Button
            onClick={() => scrollToTab('vaccinations')}
            startIcon={<i className="fas fa-syringe section-icon" style={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem', marginRight: '-4px' }}></i>}
            sx={{
              py: 1.5,
              px: 1.5,
              color: activeTab === 'vaccinations' ? theme.palette.primary.main : theme.palette.text.secondary,
              fontWeight: activeTab === 'vaccinations' ? 600 : 500,
              borderRadius: 0,
              borderBottom: activeTab === 'vaccinations' ? `3px solid ${theme.palette.primary.main}` : '3px solid transparent',
              textTransform: 'none',
              minWidth: 'auto',
              whiteSpace: 'nowrap',
              '&:hover': {
                bgcolor: theme.palette.primary.lightest,
                borderBottomColor: activeTab !== 'vaccinations' ? theme.palette.primary.lightest : theme.palette.primary.main
              }
            }}
          >
            Vaccinations
          </Button>

          <Button
            onClick={() => scrollToTab('prescriptions')}
            startIcon={<i className="fas fa-pills section-icon" style={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem', marginRight: '-4px' }}></i>}
            sx={{
              py: 1.5,
              px: 1.5,
              color: activeTab === 'prescriptions' ? theme.palette.primary.main : theme.palette.text.secondary,
              fontWeight: activeTab === 'prescriptions' ? 600 : 500,
              borderRadius: 0,
              borderBottom: activeTab === 'prescriptions' ? `3px solid ${theme.palette.primary.main}` : '3px solid transparent',
              textTransform: 'none',
              minWidth: 'auto',
              whiteSpace: 'nowrap',
              '&:hover': {
                bgcolor: theme.palette.primary.lightest,
                borderBottomColor: activeTab !== 'prescriptions' ? theme.palette.primary.lightest : theme.palette.primary.main
              }
            }}
          >
            Diagnosis & Prescriptions
          </Button>

          <Button
            onClick={() => scrollToTab('records')}
            startIcon={<i className="fas fa-file-medical section-icon" style={{ display: 'flex', alignItems: 'center', fontSize: '0.875rem', marginRight: '-4px' }}></i>}
            endIcon={records.length > 0 && (
              <Chip
                label={records.length}
                size="small"
                sx={{
                  height: 18,
                  fontSize: '0.7rem',
                  fontWeight: 600,
                  bgcolor: theme.palette.grey[200]
                }}
              />
            )}
            sx={{
              py: 1.5,
              px: 1.5,
              color: activeTab === 'records' ? theme.palette.primary.main : theme.palette.text.secondary,
              fontWeight: activeTab === 'records' ? 600 : 500,
              borderRadius: 0,
              borderBottom: activeTab === 'records' ? `3px solid ${theme.palette.primary.main}` : '3px solid transparent',
              textTransform: 'none',
              minWidth: 'auto',
              whiteSpace: 'nowrap',
              '&:hover': {
                bgcolor: theme.palette.primary.lightest,
                borderBottomColor: activeTab !== 'records' ? theme.palette.primary.lightest : theme.palette.primary.main
              }
            }}
          >
            Records
          </Button>
        </Box>
      </Paper>

      <Card elevation={1} sx={{ borderRadius: 2, mb: 3, overflow: 'hidden' }}>
        {/* Summary Tab Content */}
        <Box className={`tab-content ${activeTab === 'summary' ? 'active' : ''}`} sx={{ display: activeTab === 'summary' ? 'block' : 'none' }}>
          <Box sx={{
            p: 2,
            bgcolor: theme.palette.grey[50],
            borderBottom: `1px solid ${theme.palette.grey[200]}`,
            display: 'flex',
            alignItems: 'center',
            gap: 1.5
          }}>
            <Box
              sx={{
                width: 36,
                height: 36,
                borderRadius: 1,
                bgcolor: theme.palette.primary.lightest,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <i className="fas fa-clipboard-list" style={{ color: theme.palette.primary.main, fontSize: '1rem', display: 'flex', alignItems: 'center', justifyContent: 'center', width: '100%', height: '100%' }}></i>
            </Box>
            <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.text.primary }}>
              Patient Summary
            </Typography>
          </Box>

          <PatientSummaryTab patient={patient} recentVisit={recentVisit} previousVisit={previousVisit} visits={visits} />
        </Box>

        {/* Visits Tab Content */}
        <Box className={`tab-content ${activeTab === 'visits' ? 'active' : ''}`} sx={{ display: activeTab === 'visits' ? 'block' : 'none' }}>
          <Box sx={{
            p: 2,
            bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.03)' : theme.palette.grey[50],
            borderBottom: theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : `1px solid ${theme.palette.grey[200]}`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: 1.5
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
              <Box
                sx={{
                  width: 36,
                  height: 36,
                  borderRadius: 1.5,
                  bgcolor: theme.palette.mode === 'dark' ? 'rgba(44, 75, 43, 0.15)' : theme.palette.primary.lightest,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <i className="fas fa-calendar-alt" style={{
                  color: theme.palette.mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.main,
                  fontSize: '1rem'
                }}></i>
              </Box>
              <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.text.primary }}>
                Patient Visits
              </Typography>
            </Box>

            <Button
              component={Link}
              to={`/patients/${patient.patient_id}/visits/new`}
              variant="contained"
              color="primary"
              startIcon={<i className="fas fa-plus"></i>}
              sx={{
                borderRadius: 2,
                textTransform: 'none',
                fontWeight: 500,
                px: 2,
                py: 1,
                boxShadow: '0 2px 8px rgba(44, 75, 43, 0.25)',
                '&:hover': {
                  boxShadow: '0 4px 12px rgba(44, 75, 43, 0.35)'
                }
              }}
            >
              New Visit
            </Button>
          </Box>

          <Box sx={{ p: 3 }}>
            {visits.length === 0 ? (
              <Box sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                textAlign: 'center',
                py: 6,
                px: 3,
                bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.03)' : 'rgba(0, 0, 0, 0.02)',
                borderRadius: 2,
                border: theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid rgba(0, 0, 0, 0.05)'
              }}>
                <Box
                  sx={{
                    width: 64,
                    height: 64,
                    borderRadius: '50%',
                    bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.03)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mb: 2
                  }}
                >
                  <i className="fas fa-calendar-times" style={{
                    fontSize: '1.5rem',
                    color: theme.palette.text.secondary
                  }}></i>
                </Box>
                <Typography variant="h6" sx={{ mb: 1, fontWeight: 600 }}>
                  No Visits Recorded
                </Typography>
                <Typography variant="body1" sx={{ mb: 3, color: theme.palette.text.secondary }}>
                  This patient doesn't have any recorded visits yet.
                </Typography>
                <Button
                  component={Link}
                  to={`/patients/${patient.patient_id}/visits/new`}
                  variant="contained"
                  color="primary"
                  startIcon={<i className="fas fa-plus"></i>}
                  sx={{
                    borderRadius: 2,
                    textTransform: 'none',
                    fontWeight: 500,
                    px: 3,
                    py: 1.25
                  }}
                >
                  Record First Visit
                </Button>
              </Box>
            ) : (
              <Grid container spacing={2}>
                {visits.map((visit) => (
                  <Grid item xs={12} key={visit.visit_id}>
                    <Card
                      elevation={0}
                      sx={{
                        border: theme.palette.mode === 'dark'
                          ? '1px solid rgba(255, 255, 255, 0.1)'
                          : `1px solid ${theme.palette.grey[100]}`,
                        borderRadius: 2,
                        position: 'relative',
                        overflow: 'hidden',
                        transition: 'all 0.2s ease',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
                        bgcolor: theme.palette.mode === 'dark' ? 'rgba(30, 30, 30, 0.95)' : '#ffffff',
                        '&:hover': {
                          transform: 'translateY(-3px)',
                          boxShadow: '0 6px 16px rgba(0,0,0,0.1)',
                          borderColor: theme.palette.mode === 'dark'
                            ? 'rgba(255, 255, 255, 0.2)'
                            : theme.palette.grey[200]
                        }
                      }}
                    >
                      <Box
                        sx={{
                          position: 'absolute',
                          left: 0,
                          top: 0,
                          bottom: 0,
                          width: '4px',
                          borderRadius: '4px 0 0 4px',
                          background: theme.palette.primary.main
                        }}
                      />

                      <Box sx={{
                        p: 2.5,
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        borderBottom: theme.palette.mode === 'dark'
                          ? '1px solid rgba(255, 255, 255, 0.1)'
                          : `1px solid ${theme.palette.grey[100]}`
                      }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
                          <Box
                            sx={{
                              width: 40,
                              height: 40,
                              borderRadius: 1.5,
                              bgcolor: theme.palette.mode === 'dark' ? 'rgba(44, 75, 43, 0.15)' : theme.palette.primary.lightest,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center'
                            }}
                          >
                            <i className="fas fa-calendar-day" style={{
                              color: theme.palette.mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.main,
                              fontSize: '1rem'
                            }}></i>
                          </Box>
                          <Box>
                            <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                              {new Date(visit.visit_date).toLocaleDateString('en-GB')}
                            </Typography>
                            {visit.visit_time && (
                              <Typography variant="caption" sx={{ color: theme.palette.text.secondary }}>
                                {visit.visit_time}
                              </Typography>
                            )}
                          </Box>
                        </Box>

                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Tooltip title="Edit Visit" arrow placement="top">
                            <Button
                              component={Link}
                              to={`/patients/${patient.patient_id}/visits/${visit.visit_id}/edit`}
                              size="small"
                              variant="contained"
                              sx={{
                                minWidth: '32px',
                                width: '32px',
                                height: '32px',
                                padding: 0,
                                color: 'white',
                                bgcolor: theme.palette.primary.main,
                                '&:hover': {
                                  bgcolor: theme.palette.primary.dark
                                }
                              }}
                            >
                              <i className="fas fa-edit"></i>
                            </Button>
                          </Tooltip>

                          <Tooltip title="View Visit Details" arrow placement="top">
                            <Button
                              component={Link}
                              to={`/patients/${patient.patient_id}/visits/${visit.visit_id}`}
                              size="small"
                              variant="contained"
                              sx={{
                                minWidth: '32px',
                                width: '32px',
                                height: '32px',
                                padding: 0,
                                color: 'white',
                                bgcolor: theme.palette.info.main,
                                '&:hover': {
                                  bgcolor: theme.palette.info.dark
                                }
                              }}
                            >
                              <i className="fas fa-eye"></i>
                            </Button>
                          </Tooltip>
                        </Box>
                      </Box>

                      <Box sx={{ p: 2.5 }}>
                        <Grid container spacing={2}>
                          {visit.visit_reason && (
                            <Grid item xs={12} sm={6}>
                              <Box>
                                <Typography variant="caption" color="textSecondary" sx={{
                                  textTransform: 'uppercase',
                                  fontWeight: 600,
                                  fontSize: '0.7rem',
                                  letterSpacing: '0.5px'
                                }}>
                                  Reason for Visit
                                </Typography>
                                <Typography variant="body1" sx={{ mt: 0.5 }}>
                                  {visit.visit_reason}
                                </Typography>
                              </Box>
                            </Grid>
                          )}

                          {visit.diagnosis && (
                            <Grid item xs={12} sm={6}>
                              <Box>
                                <Typography variant="caption" color="textSecondary" sx={{
                                  textTransform: 'uppercase',
                                  fontWeight: 600,
                                  fontSize: '0.7rem',
                                  letterSpacing: '0.5px'
                                }}>
                                  Diagnosis
                                </Typography>
                                <Typography variant="body1" sx={{ mt: 0.5 }}>
                                  {visit.diagnosis}
                                </Typography>
                              </Box>
                            </Grid>
                          )}

                          <Grid item xs={12} sm={6}>
                            <Box>
                              <Typography variant="caption" color="textSecondary" sx={{
                                textTransform: 'uppercase',
                                fontWeight: 600,
                                fontSize: '0.7rem',
                                letterSpacing: '0.5px'
                              }}>
                                Doctor
                              </Typography>
                              <Typography variant="body1" sx={{ mt: 0.5 }}>
                                {visit.doctor_first_name && visit.doctor_last_name
                                  ? `Dr. ${visit.doctor_first_name} ${visit.doctor_last_name}`
                                  : 'N/A'}
                              </Typography>
                            </Box>
                          </Grid>

                          {visit.treatment_plan && (
                            <Grid item xs={12} sm={6}>
                              <Box>
                                <Typography variant="caption" color="textSecondary" sx={{
                                  textTransform: 'uppercase',
                                  fontWeight: 600,
                                  fontSize: '0.7rem',
                                  letterSpacing: '0.5px'
                                }}>
                                  Treatment Plan
                                </Typography>
                                <Typography variant="body1" sx={{ mt: 0.5 }}>
                                  {visit.treatment_plan}
                                </Typography>
                              </Box>
                            </Grid>
                          )}
                        </Grid>

                        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
                          <Button
                            component={Link}
                            to={`/patients/${patient.patient_id}/visits/${visit.visit_id}`}
                            variant="outlined"
                            color="info"
                            endIcon={<i className="fas fa-chevron-right" style={{ fontSize: '0.75rem' }}></i>}
                            sx={{
                              borderRadius: 2,
                              textTransform: 'none',
                              fontWeight: 500,
                              px: 2,
                              py: 0.75
                            }}
                          >
                            View Complete Details
                          </Button>
                        </Box>
                      </Box>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        </Box>

        {/* Basic Info Tab Content */}
        <Box className={`tab-content ${activeTab === 'basic' ? 'active' : ''}`} sx={{ display: activeTab === 'basic' ? 'block' : 'none' }}>
          <Box sx={{
            p: 2,
            bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.03)' : theme.palette.grey[50],
            borderBottom: theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : `1px solid ${theme.palette.grey[200]}`,
            display: 'flex',
            alignItems: 'center',
            gap: 1.5
          }}>
            <Box
              sx={{
                width: 36,
                height: 36,
                borderRadius: 1.5,
                bgcolor: theme.palette.mode === 'dark' ? 'rgba(14, 165, 233, 0.15)' : 'rgba(14, 165, 233, 0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <i className="fas fa-info-circle" style={{
                color: theme.palette.mode === 'dark' ? theme.palette.info.light : theme.palette.info.main,
                fontSize: '1rem'
              }}></i>
            </Box>
            <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.text.primary }}>
              Basic Information
            </Typography>
          </Box>

          <Box sx={{ p: 3 }}>
            <Grid container spacing={3}>
              {recentVisit && (
                <Grid item xs={12}>
                  <Card
                    elevation={0}
                    sx={{
                      border: theme.palette.mode === 'dark'
                        ? '1px solid rgba(255, 255, 255, 0.1)'
                        : `1px solid ${theme.palette.grey[100]}`,
                      borderRadius: 2,
                      position: 'relative',
                      overflow: 'hidden',
                      boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
                      bgcolor: theme.palette.mode === 'dark' ? 'rgba(30, 30, 30, 0.95)' : '#ffffff'
                    }}
                  >
                    <Box
                      sx={{
                        position: 'absolute',
                        left: 0,
                        top: 0,
                        bottom: 0,
                        width: '4px',
                        borderRadius: '4px 0 0 4px',
                        background: theme.palette.info.main
                      }}
                    />

                    <Box sx={{
                      p: 2.5,
                      borderBottom: theme.palette.mode === 'dark'
                        ? '1px solid rgba(255, 255, 255, 0.1)'
                        : `1px solid ${theme.palette.grey[100]}`,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1.5
                    }}>
                      <Box
                        sx={{
                          width: 36,
                          height: 36,
                          borderRadius: 1.5,
                          bgcolor: theme.palette.mode === 'dark' ? 'rgba(14, 165, 233, 0.15)' : theme.palette.info.lightest,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}
                      >
                        <i className="fas fa-calendar-check" style={{
                          color: theme.palette.mode === 'dark' ? theme.palette.info.light : theme.palette.info.main,
                          fontSize: '1rem'
                        }}></i>
                      </Box>
                      <Box>
                        <Typography variant="subtitle1" sx={{ fontWeight: 600 }}>
                          Most Recent Visit
                        </Typography>
                        <Typography variant="caption" sx={{ color: theme.palette.text.secondary }}>
                          {new Date(recentVisit.visit_date).toLocaleDateString('en-GB')}
                        </Typography>
                      </Box>
                    </Box>

                    <Box sx={{ p: 2.5 }}>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Box sx={{ mb: { xs: 2, sm: 0 } }}>
                            <Typography variant="caption" color="textSecondary" sx={{
                              textTransform: 'uppercase',
                              fontWeight: 600,
                              fontSize: '0.7rem',
                              letterSpacing: '0.5px'
                            }}>
                              Reason for Visit
                            </Typography>
                            <Typography variant="body1" sx={{ mt: 0.5, display: 'flex', alignItems: 'center', gap: 1 }}>
                              <i className="fas fa-clipboard-list" style={{ color: theme.palette.text.secondary, fontSize: '0.875rem' }}></i>
                              {recentVisit.reason || 'N/A'}
                            </Typography>
                          </Box>
                        </Grid>

                        {recentVisit.diagnosis && (
                          <Grid item xs={12} sm={6}>
                            <Box sx={{ mb: { xs: 2, sm: 0 } }}>
                              <Typography variant="caption" color="textSecondary" sx={{
                                textTransform: 'uppercase',
                                fontWeight: 600,
                                fontSize: '0.7rem',
                                letterSpacing: '0.5px'
                              }}>
                                Diagnosis
                              </Typography>
                              <Typography variant="body1" sx={{ mt: 0.5, display: 'flex', alignItems: 'center', gap: 1 }}>
                                <i className="fas fa-stethoscope" style={{ color: theme.palette.text.secondary, fontSize: '0.875rem' }}></i>
                                {recentVisit.diagnosis}
                              </Typography>
                            </Box>
                          </Grid>
                        )}

                        <Grid item xs={12} sm={6}>
                          <Box sx={{ mb: { xs: 2, sm: 0 } }}>
                            <Typography variant="caption" color="textSecondary" sx={{
                              textTransform: 'uppercase',
                              fontWeight: 600,
                              fontSize: '0.7rem',
                              letterSpacing: '0.5px'
                            }}>
                              Doctor
                            </Typography>
                            <Typography variant="body1" sx={{ mt: 0.5, display: 'flex', alignItems: 'center', gap: 1 }}>
                              <i className="fas fa-user-md" style={{ color: theme.palette.text.secondary, fontSize: '0.875rem' }}></i>
                              {recentVisit.doctor_first_name && recentVisit.doctor_last_name
                                ? `Dr. ${recentVisit.doctor_first_name} ${recentVisit.doctor_last_name}`
                                : 'N/A'}
                            </Typography>
                          </Box>
                        </Grid>

                        {recentVisit.treatment_plan && (
                          <Grid item xs={12} sm={6}>
                            <Box>
                              <Typography variant="caption" color="textSecondary" sx={{
                                textTransform: 'uppercase',
                                fontWeight: 600,
                                fontSize: '0.7rem',
                                letterSpacing: '0.5px'
                              }}>
                                Treatment Plan
                              </Typography>
                              <Typography variant="body1" sx={{ mt: 0.5, display: 'flex', alignItems: 'center', gap: 1 }}>
                                <i className="fas fa-clipboard-check" style={{ color: theme.palette.text.secondary, fontSize: '0.875rem' }}></i>
                                {recentVisit.treatment_plan}
                              </Typography>
                            </Box>
                          </Grid>
                        )}
                      </Grid>

                      <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
                        <Button
                          component={Link}
                          to={`/patients/${patient.patient_id}/visits/${recentVisit.visit_id}`}
                          variant="outlined"
                          color="info"
                          endIcon={<i className="fas fa-chevron-right" style={{ fontSize: '0.75rem' }}></i>}
                          sx={{
                            borderRadius: 2,
                            textTransform: 'none',
                            fontWeight: 500,
                            px: 2,
                            py: 0.75
                          }}
                        >
                          View Visit Details
                        </Button>
                      </Box>
                    </Box>
                  </Card>
                </Grid>
              )}
            </Grid>
          </Box>
        </Box>

        <Box className={`tab-content ${activeTab === 'vitals' ? 'active' : ''}`} sx={{ display: activeTab === 'vitals' ? 'block' : 'none' }}>
          <Box sx={{
            p: 2,
            bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.03)' : theme.palette.grey[50],
            borderBottom: theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : `1px solid ${theme.palette.grey[200]}`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: 1.5
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
              <Box
                sx={{
                  width: 36,
                  height: 36,
                  borderRadius: 1.5,
                  bgcolor: theme.palette.mode === 'dark' ? 'rgba(239, 68, 68, 0.15)' : 'rgba(239, 68, 68, 0.1)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <i className="fas fa-heartbeat" style={{
                  color: theme.palette.mode === 'dark' ? theme.palette.error.light : theme.palette.error.main,
                  fontSize: '1rem'
                }}></i>
              </Box>
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.text.primary }}>
                  Vital Signs
                </Typography>
                {recentVisit && (
                  <Typography variant="caption" sx={{ color: theme.palette.text.secondary }}>
                    Data from visit on {new Date(recentVisit.visit_date).toLocaleDateString('en-GB')}
                  </Typography>
                )}
              </Box>
            </Box>
          </Box>

          <VitalSignsTab patient={patient} recentVisit={recentVisit} />
        </Box>

        <Box className={`tab-content ${activeTab === 'lab' ? 'active' : ''}`} sx={{ display: activeTab === 'lab' ? 'block' : 'none' }}>
          <Box sx={{
            p: 2,
            bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.03)' : theme.palette.grey[50],
            borderBottom: theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : `1px solid ${theme.palette.grey[200]}`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: 1.5
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
              <Box
                sx={{
                  width: 36,
                  height: 36,
                  borderRadius: 1.5,
                  bgcolor: theme.palette.mode === 'dark' ? 'rgba(124, 58, 237, 0.15)' : 'rgba(124, 58, 237, 0.1)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <i className="fas fa-flask" style={{
                  color: theme.palette.mode === 'dark' ? '#a78bfa' : '#7c3aed',
                  fontSize: '1rem'
                }}></i>
              </Box>
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.text.primary }}>
                  Lab Results
                </Typography>
                {recentVisit && (
                  <Typography variant="caption" sx={{ color: theme.palette.text.secondary }}>
                    Data from visit on {new Date(recentVisit.visit_date).toLocaleDateString('en-GB')}
                  </Typography>
                )}
              </Box>
            </Box>
          </Box>

          <LabResultsTab patient={patient} recentVisit={recentVisit} />
        </Box>

        <Box className={`tab-content ${activeTab === 'health' ? 'active' : ''}`} sx={{ display: activeTab === 'health' ? 'block' : 'none' }}>
          <Box sx={{
            p: 2,
            bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.03)' : theme.palette.grey[50],
            borderBottom: theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : `1px solid ${theme.palette.grey[200]}`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: 1.5
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
              <Box
                sx={{
                  width: 36,
                  height: 36,
                  borderRadius: 1.5,
                  bgcolor: theme.palette.mode === 'dark' ? 'rgba(22, 163, 74, 0.15)' : 'rgba(22, 163, 74, 0.1)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <i className="fas fa-notes-medical" style={{
                  color: theme.palette.mode === 'dark' ? '#4ade80' : '#16a34a',
                  fontSize: '1rem'
                }}></i>
              </Box>
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.text.primary }}>
                  Health Status
                </Typography>
                {recentVisit && (
                  <Typography variant="caption" sx={{ color: theme.palette.text.secondary }}>
                    Data from visit on {new Date(recentVisit.visit_date).toLocaleDateString('en-GB')}
                  </Typography>
                )}
              </Box>
            </Box>
          </Box>

          <HealthStatusTab patient={patient} recentVisit={recentVisit} />
        </Box>

        <Box className={`tab-content ${activeTab === 'mental' ? 'active' : ''}`} sx={{ display: activeTab === 'mental' ? 'block' : 'none' }}>
          <Box sx={{
            p: 2,
            bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.03)' : theme.palette.grey[50],
            borderBottom: theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : `1px solid ${theme.palette.grey[200]}`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: 1.5
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
              <Box
                sx={{
                  width: 36,
                  height: 36,
                  borderRadius: 1.5,
                  bgcolor: theme.palette.mode === 'dark' ? 'rgba(124, 58, 237, 0.15)' : 'rgba(124, 58, 237, 0.1)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <i className="fas fa-brain" style={{
                  color: theme.palette.mode === 'dark' ? '#a78bfa' : '#7c3aed',
                  fontSize: '1rem'
                }}></i>
              </Box>
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.text.primary }}>
                  Mental Health
                </Typography>
                {recentVisit && (
                  <Typography variant="caption" sx={{ color: theme.palette.text.secondary }}>
                    Data from visit on {new Date(recentVisit.visit_date).toLocaleDateString('en-GB')}
                  </Typography>
                )}
              </Box>
            </Box>
          </Box>

          <FinalMentalHealthTab patient={patient} recentVisit={recentVisit} />
        </Box>

        <Box className={`tab-content ${activeTab === 'sleep' ? 'active' : ''}`} sx={{ display: activeTab === 'sleep' ? 'block' : 'none' }}>
          <Box sx={{
            p: 2,
            bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.03)' : theme.palette.grey[50],
            borderBottom: theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : `1px solid ${theme.palette.grey[200]}`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: 1.5
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
              <Box
                sx={{
                  width: 36,
                  height: 36,
                  borderRadius: 1.5,
                  bgcolor: theme.palette.mode === 'dark' ? 'rgba(59, 130, 246, 0.15)' : 'rgba(59, 130, 246, 0.1)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <i className="fas fa-bed" style={{
                  color: theme.palette.mode === 'dark' ? '#93c5fd' : '#3b82f6',
                  fontSize: '1rem'
                }}></i>
              </Box>
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.text.primary }}>
                  Sleep & Pain Assessment
                </Typography>
                {recentVisit && (
                  <Typography variant="caption" sx={{ color: theme.palette.text.secondary }}>
                    Data from visit on {new Date(recentVisit.visit_date).toLocaleDateString('en-GB')}
                  </Typography>
                )}
              </Box>
            </Box>
          </Box>

          <SleepPainTab patient={patient} recentVisit={recentVisit} />
        </Box>

        <Box className={`tab-content ${activeTab === 'frailty' ? 'active' : ''}`} sx={{ display: activeTab === 'frailty' ? 'block' : 'none' }}>
          <Box sx={{
            p: 2,
            bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.03)' : theme.palette.grey[50],
            borderBottom: theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : `1px solid ${theme.palette.grey[200]}`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: 1.5
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
              <Box
                sx={{
                  width: 36,
                  height: 36,
                  borderRadius: 1.5,
                  bgcolor: theme.palette.mode === 'dark' ? 'rgba(22, 163, 74, 0.15)' : 'rgba(22, 163, 74, 0.1)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <i className="fas fa-walking" style={{
                  color: theme.palette.mode === 'dark' ? '#4ade80' : '#16a34a',
                  fontSize: '1rem'
                }}></i>
              </Box>
              <Box>
                <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.text.primary }}>
                  Frailty Assessment
                </Typography>
                {recentVisit && (
                  <Typography variant="caption" sx={{ color: theme.palette.text.secondary }}>
                    Data from visit on {new Date(recentVisit.visit_date).toLocaleDateString('en-GB')}
                  </Typography>
                )}
              </Box>
            </Box>
          </Box>

          <FrailtyAssessmentTab patient={patient} recentVisit={recentVisit} />
        </Box>

        <Box className={`tab-content ${activeTab === 'social' ? 'active' : ''}`} sx={{ display: activeTab === 'social' ? 'block' : 'none' }}>
          <Box sx={{
            p: 2,
            bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.03)' : theme.palette.grey[50],
            borderBottom: theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : `1px solid ${theme.palette.grey[200]}`,
            display: 'flex',
            alignItems: 'center',
            gap: 1.5
          }}>
            <Box
              sx={{
                width: 36,
                height: 36,
                borderRadius: 1.5,
                bgcolor: theme.palette.mode === 'dark' ? 'rgba(22, 163, 74, 0.15)' : 'rgba(22, 163, 74, 0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <i className="fas fa-users" style={{
                color: theme.palette.mode === 'dark' ? '#4ade80' : '#16a34a',
                fontSize: '1rem'
              }}></i>
            </Box>
            <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.text.primary }}>
              Social & Safety
            </Typography>
          </Box>

          <Box sx={{ p: 3 }}>

          <div className="vitals-grid">
            <div className="vital-section">
              <div className="vital-header">
                <i className="fas fa-user-friends vital-icon icon-user-friends"></i>
                <h3>Social Interactions</h3>
              </div>
              <div className="vital-content">
                <div className="vital-item">
                  <div className="vital-label">SOCIAL INTERACTION LEVELS</div>
                  <div className="vital-value">{patient.social_interaction_levels || 'N/A'}</div>
                </div>
              </div>
            </div>

            <div className="vital-section">
              <div className="vital-header">
                <i className="fas fa-home vital-icon icon-home"></i>
                <h3>Living Environment</h3>
              </div>
              <div className="vital-content">
                <div className="vital-item">
                  <div className="vital-label">LIVING CONDITIONS</div>
                  <div className="vital-value">{patient.living_conditions || 'N/A'}</div>
                </div>
                <div className="vital-item">
                  <div className="vital-label">AGE-FRIENDLY ENVIRONMENT</div>
                  <div className="vital-value">{formatBoolean(patient.age_friendly_environment)}</div>
                </div>
              </div>
            </div>

            <div className="vital-section">
              <div className="vital-header">
                <i className="fas fa-shield-alt vital-icon icon-shield-alt"></i>
                <h3>Safety & Emergency</h3>
              </div>
              <div className="vital-content">
                <div className="vital-item">
                  <div className="vital-label">EMERGENCY CONTACT UPDATED</div>
                  <div className="vital-value">{formatBoolean(patient.emergency_contact_updated)}</div>
                </div>
                <div className="vital-item">
                  <div className="vital-label">SOS ALERTS</div>
                  <div className="vital-value">{formatBoolean(patient.sos_alerts)}</div>
                </div>
              </div>
            </div>

            <div className="vital-section">
              <div className="vital-header">
                <i className="fas fa-medkit vital-icon icon-medkit"></i>
                <h3>Preventive Care</h3>
              </div>
              <div className="vital-content">
                <div className="vital-item">
                  <div className="vital-label">HEALTH CHECKUP ADHERENCE (1-10)</div>
                  <div className="vital-value">{patient.health_checkup_adherence || 'N/A'}</div>
                </div>
                <div className="vital-item">
                  <div className="vital-label">VACCINATION UPDATED</div>
                  <div className="vital-value">{formatBoolean(patient.vaccination_updated)}</div>
                </div>
              </div>
            </div>

            <div className="vital-section">
              <div className="vital-header">
                <i className="fas fa-exclamation-circle vital-icon icon-exclamation-circle"></i>
                <h3>Additional Health Concerns</h3>
              </div>
              <div className="vital-content">
                <div className="vital-item">
                  <div className="vital-label">URINARY/BOWEL ISSUES</div>
                  <div className="vital-value">{patient.urinary_bowel_issues || 'None reported'}</div>
                </div>
                <div className="vital-item">
                  <div className="vital-label">SUBSTANCE ABUSE</div>
                  <div className="vital-value">{formatBoolean(patient.substance_abuse)}</div>
                </div>
              </div>
            </div>
          </div>
          </Box>
        </Box>

        <Box className={`tab-content ${activeTab === 'vaccinations' ? 'active' : ''}`} sx={{ display: activeTab === 'vaccinations' ? 'block' : 'none' }}>
          <Box sx={{
            p: 2,
            bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.03)' : theme.palette.grey[50],
            borderBottom: theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : `1px solid ${theme.palette.grey[200]}`,
            display: 'flex',
            alignItems: 'center',
            gap: 1.5
          }}>
            <Box
              sx={{
                width: 36,
                height: 36,
                borderRadius: 1.5,
                bgcolor: theme.palette.mode === 'dark' ? 'rgba(217, 119, 6, 0.15)' : 'rgba(217, 119, 6, 0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <i className="fas fa-syringe" style={{
                color: theme.palette.mode === 'dark' ? '#fbbf24' : '#d97706',
                fontSize: '1rem'
              }}></i>
            </Box>
            <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.text.primary }}>
              Vaccination Records
            </Typography>
          </Box>

          {patient && (
            <VaccinationTab patient={patient} recentVisit={recentVisit} />
          )}
        </Box>

        <Box className={`tab-content ${activeTab === 'charts' ? 'active' : ''}`} sx={{ display: activeTab === 'charts' ? 'block' : 'none' }}>
          <Box sx={{
            p: 2,
            bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.03)' : theme.palette.grey[50],
            borderBottom: theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : `1px solid ${theme.palette.grey[200]}`,
            display: 'flex',
            alignItems: 'center',
            gap: 1.5
          }}>
            <Box
              sx={{
                width: 36,
                height: 36,
                borderRadius: 1.5,
                bgcolor: theme.palette.mode === 'dark' ? 'rgba(14, 165, 233, 0.15)' : 'rgba(14, 165, 233, 0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <i className="fas fa-chart-line" style={{
                color: theme.palette.mode === 'dark' ? '#38bdf8' : '#0ea5e9',
                fontSize: '1rem'
              }}></i>
            </Box>
            <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.text.primary }}>
              Health Charts
            </Typography>
          </Box>

          <Box sx={{ p: 3 }}>
          {patient && (
            <PatientHealthCharts patientId={patient.patient_id} />
          )}
          </Box>
        </Box>

        <Box className={`tab-content ${activeTab === 'prescriptions' ? 'active' : ''}`} sx={{ display: activeTab === 'prescriptions' ? 'block' : 'none' }}>
          <Box sx={{
            p: 2,
            bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.03)' : theme.palette.grey[50],
            borderBottom: theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : `1px solid ${theme.palette.grey[200]}`,
            display: 'flex',
            alignItems: 'center',
            gap: 1.5
          }}>
            <Box
              sx={{
                width: 36,
                height: 36,
                borderRadius: 1.5,
                bgcolor: theme.palette.mode === 'dark' ? 'rgba(236, 72, 153, 0.15)' : 'rgba(236, 72, 153, 0.1)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <i className="fas fa-pills" style={{
                color: theme.palette.mode === 'dark' ? '#f9a8d4' : '#ec4899',
                fontSize: '1rem'
              }}></i>
            </Box>
            <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.text.primary }}>
              Diagnosis & Prescriptions
            </Typography>
          </Box>

          {patient && (
            <DiagnosisPrescriptionsTab patient={patient} recentVisit={recentVisit} />
          )}
        </Box>

        <Box className={`tab-content ${activeTab === 'records' ? 'active' : ''}`} sx={{ display: activeTab === 'records' ? 'block' : 'none' }}>
          <Box sx={{
            p: 2,
            bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.03)' : theme.palette.grey[50],
            borderBottom: theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.1)' : `1px solid ${theme.palette.grey[200]}`,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            gap: 1.5
          }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
              <Box
                sx={{
                  width: 36,
                  height: 36,
                  borderRadius: 1.5,
                  bgcolor: theme.palette.mode === 'dark' ? 'rgba(220, 38, 38, 0.15)' : 'rgba(220, 38, 38, 0.1)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                <i className="fas fa-file-medical" style={{
                  color: theme.palette.mode === 'dark' ? '#f87171' : '#dc2626',
                  fontSize: '1rem'
                }}></i>
              </Box>
              <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.text.primary }}>
                Medical Records
              </Typography>
            </Box>

            {records.length > 0 && (
              <Chip
                label={`Showing ${records.length} medical records`}
                size="small"
                sx={{
                  height: '28px',
                  bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : theme.palette.grey[100],
                  color: theme.palette.text.secondary
                }}
              />
            )}
          </Box>

          <Box sx={{ p: 3 }}>

          {records.length === 0 ? (
            <div className="alert alert-info">
              <i className="fas fa-info-circle"></i> No medical records found for this patient.
            </div>
          ) : (
            <>
              {records.map((record) => (
                <div key={record.record_id} className="record-card">
                  <div className="record-header">
                    <div className="record-date">
                      <i className="fas fa-calendar-alt"></i> {new Date(record.record_date).toLocaleDateString()}
                    </div>
                    <div className="record-meta">
                      Added by {record.created_by_name} on {new Date(record.created_at).toLocaleDateString()}
                    </div>
                  </div>

                  <div className="info-card">
                    <div className="info-label">Diagnosis</div>
                    <div className="info-value">{record.diagnosis}</div>
                  </div>

                  <div className="info-card">
                    <div className="info-label">Treatment</div>
                    <div className="info-value">{record.treatment}</div>
                  </div>

                  <div className="info-card">
                    <div className="info-label">Notes</div>
                    <div className="info-value">{record.notes}</div>
                  </div>
                </div>
              ))}
            </>
          )}
          </Box>
        </Box>
      </Card>
    </Box>
  );
};

export default PatientDetail;