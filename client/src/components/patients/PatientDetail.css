/* Modern, Clean, Minimalist Patient Detail Styles */

:root {
  --text-color: #374151;
  --text-secondary-color: #6b7280;
  --bg-color: #f9fafb;
  --card-bg-color: white;
  --border-color: #f3f4f6;
  --shadow-color: rgba(0, 0, 0, 0.05);
  --primary-color: #2C4B2B;
  --light-primary: rgba(44, 75, 43, 0.1);
  --success-color: #10b981;
  --error-color: #ef4444;
  --info-color: #0ea5e9;
  --light-info: #f0f9ff;
  --warning-color: #f59e0b;
}

.dark-mode {
  --text-color: #f9fafb;
  --text-secondary-color: #9ca3af;
  --bg-color: #1e1e1e;
  --card-bg-color: #2d2d2d;
  --border-color: rgba(255, 255, 255, 0.1);
  --shadow-color: rgba(0, 0, 0, 0.2);
  --primary-color: #90caf9;
  --light-primary: rgba(144, 202, 249, 0.1);
  --success-color: #4ade80;
  --error-color: #f87171;
  --info-color: #38bdf8;
  --light-info: rgba(56, 189, 248, 0.1);
  --warning-color: #fbbf24;
}

/* Fix for black box behind patient name in dark mode */
.dark-mode .MuiTypography-h5,
.dark-mode .MuiTypography-h5[component="h1"],
.dark-mode .MuiTypography-root[variant="h5"],
.dark-mode .MuiCard-root .MuiCardContent-root .MuiBox-root .MuiBox-root .MuiTypography-h5,
.dark-mode .MuiCard-root .MuiCardContent-root .MuiBox-root .MuiBox-root .MuiTypography-root {
  background-color: transparent !important;
  background: transparent !important;
}

.patient-detail-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
  color: var(--text-color, #374151);
  background-color: var(--bg-color, #f9fafb);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.5;
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 300px;
  gap: 1.25rem;
}

.loading-text {
  color: #6b7280;
  font-size: 0.9rem;
  font-weight: 500;
  margin: 0;
  letter-spacing: 0.01em;
}

/* Error State */
.error-container {
  background-color: #fef2f2;
  color: #b91c1c;
  padding: 2rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 1.25rem;
  border: 1px solid rgba(185, 28, 28, 0.1);
}

.error-container i {
  font-size: 2.25rem;
  opacity: 0.9;
}

.error-back-button {
  display: inline-flex;
  align-items: center;
  background-color: #ffffff;
  color: #b91c1c;
  font-size: 0.9rem;
  font-weight: 500;
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  text-decoration: none;
  transition: all 0.2s ease;
  border: 1px solid rgba(185, 28, 28, 0.15);
  margin-top: 0.5rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.error-back-button:hover {
  background-color: #fef2f2;
  transform: translateY(-1px);
}

.error-back-button i {
  margin-right: 0.5rem;
  font-size: 0.9rem;
}

/* Header Section */
.patient-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  background-color: var(--card-bg-color, #ffffff);
  border-radius: 12px;
  box-shadow: 0 4px 12px var(--shadow-color, rgba(0, 0, 0, 0.05));
  position: relative;
  overflow: hidden;
}

.patient-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6px;
  height: 100%;
  background: linear-gradient(to bottom, #2C4B2B, #4A7A48);
}

.header-left {
  max-width: 70%;
}

.back-button {
  display: inline-flex;
  align-items: center;
  color: #6b7280;
  font-size: 0.875rem;
  margin-bottom: 0.75rem;
  text-decoration: none;
  transition: all 0.2s ease;
  font-weight: 500;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  background-color: #f3f4f6;
}

.back-button:hover {
  color: #2C4B2B;
  background-color: rgba(44, 75, 43, 0.1);
}

.back-button i {
  margin-right: 0.375rem;
  font-size: 0.75rem;
}

.patient-title-container {
  margin-bottom: 0.5rem;
}

.patient-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-color, #111827);
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
  letter-spacing: -0.01em;
  line-height: 1.2;
}

.patient-subtitle {
  display: flex;
  align-items: center;
  gap: 1.25rem;
  color: var(--text-secondary-color, #6b7280);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.patient-age-gender, .patient-doctor {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  background-color: #f9fafb;
  padding: 0.25rem 0.625rem;
  border-radius: 4px;
  border: 1px solid #f3f4f6;
}

.patient-age-gender {
  color: #4b5563;
}

.patient-doctor {
  color: #2C4B2B;
  background-color: rgba(44, 75, 43, 0.1);
  border-color: rgba(44, 75, 43, 0.2);
}

.patient-id-badge {
  display: inline-flex;
  align-items: center;
  background-color: #f3f4f6;
  color: #4b5563;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  cursor: help;
  border: 1px solid #e5e7eb;
  letter-spacing: 0.02em;
}

.patient-id-badge i {
  margin-right: 0.25rem;
  font-size: 0.75rem;
  opacity: 0.8;
}

.last-edited {
  margin-top: 0.75rem;
  font-size: 0.75rem;
  color: #9ca3af;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background-color: #f9fafb;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  width: fit-content;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

/* Health Data Source Indicator */
.health-data-source {
  background-color: #f0f9ff;
  color: #0369a1;
  padding: 0.625rem 1rem;
  border-radius: 6px;
  margin-bottom: 1.25rem;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border-left: 3px solid #0ea5e9;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.health-data-source i {
  font-size: 0.875rem;
  color: #0284c7;
}

.action-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 0.875rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  letter-spacing: 0.01em;
}

.action-button i {
  margin-right: 0.375rem;
  font-size: 0.75rem;
}

.action-button.primary {
  background-color: #2C4B2B;
  color: white;
}

.action-button.primary:hover {
  background-color: #4A7A48;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.action-button.secondary {
  background-color: #ffffff;
  color: #4b5563;
  border: 1px solid #e5e7eb;
}

.action-button.secondary:hover {
  background-color: #f9fafb;
  color: #374151;
  transform: translateY(-1px);
}

.action-button.success {
  background-color: #D97B3A;
  color: white;
}

.action-button.success:hover {
  background-color: #C06A29;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.action-button.danger {
  background-color: #ffffff;
  color: #ef4444;
  border: 1px solid #fecaca;
  padding: 0.5rem;
  min-width: 32px;
}

.action-button.danger:hover {
  background-color: #fee2e2;
  transform: translateY(-1px);
}

/* Overview Cards */
.overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.overview-card {
  background-color: var(--card-bg-color, white);
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  align-items: center;
  box-shadow: 0 1px 3px var(--shadow-color, rgba(0, 0, 0, 0.05));
  border: 1px solid var(--border-color, #f3f4f6);
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.overview-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background-color: #3b82f6;
  opacity: 1;
  transition: opacity 0.2s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
}

.overview-card:hover::after {
  opacity: 1;
}

/* Color coding for different metrics */
.overview-card.weight::after { background-color: #2C4B2B; }
.overview-card.height::after { background-color: #4A7A48; }
.overview-card.bmi::after { background-color: #A1A43A; }
.overview-card.temperature::after { background-color: #F6B21A; }
.overview-card.blood-pressure::after { background-color: #D97B3A; }
.overview-card.heart-rate::after { background-color: #C06A29; }
.overview-card.oxygen::after { background-color: #4A7A48; }

.overview-icon {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  font-size: 0.875rem;
  flex-shrink: 0;
}

/* Color coding for icons */
.overview-card.weight .overview-icon {
  background-color: rgba(44, 75, 43, 0.1);
  color: #2C4B2B;
}

.overview-card.height .overview-icon {
  background-color: rgba(74, 122, 72, 0.1);
  color: #4A7A48;
}

.overview-card.bmi .overview-icon {
  background-color: rgba(161, 164, 58, 0.1);
  color: #A1A43A;
}

.overview-card.temperature .overview-icon {
  background-color: rgba(246, 178, 26, 0.1);
  color: #F6B21A;
}

.overview-card.blood-pressure .overview-icon {
  background-color: rgba(217, 123, 58, 0.1);
  color: #D97B3A;
}

.overview-card.heart-rate .overview-icon {
  background-color: rgba(192, 106, 41, 0.1);
  color: #C06A29;
}

.overview-card.oxygen .overview-icon {
  background-color: rgba(74, 122, 72, 0.1);
  color: #4A7A48;
}

.overview-content {
  flex: 1;
}

.overview-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-secondary-color, #6b7280);
  margin: 0;
  letter-spacing: 0.02em;
  text-transform: uppercase;
}

.overview-value {
  font-size: 1rem;
  font-weight: 700;
  color: var(--text-color, #111827);
  margin: 0.25rem 0 0;
  line-height: 1.2;
  letter-spacing: -0.01em;
  display: flex;
  align-items: center;
}

.overview-unit {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-secondary-color, #6b7280);
  margin-left: 0.25rem;
}

/* Blood Pressure Overview Card Styles */
.bp-overview-card {
  grid-column: span 2;
}

.bp-overview-card .overview-content {
  width: 100%;
}

.bp-readings {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

.bp-reading {
  display: flex;
  flex-direction: column;
  background-color: #f9fafb;
  padding: 0.625rem;
  border-radius: 6px;
  flex: 1;
}

.bp-position {
  font-size: 0.75rem;
  font-weight: 600;
  color: #4b5563;
  margin-bottom: 0.125rem;
  display: flex;
  align-items: center;
}

.bp-position i {
  margin-right: 0.375rem;
  font-size: 0.75rem;
  opacity: 0.8;
}

.bp-reading .overview-value {
  margin-top: 0.125rem;
  font-size: 1rem;
}

/* Blood Pressure Info Card Styles */
.bp-info-card {
  grid-column: span 2;
}

.bp-info-values {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
  flex-wrap: wrap;
}

.bp-info-reading {
  display: flex;
  flex-direction: column;
  background-color: #f9fafb;
  padding: 0.75rem;
  border-radius: 6px;
  min-width: 120px;
  flex: 1;
}

.bp-info-position {
  font-size: 0.875rem;
  font-weight: 600;
  color: #4b5563;
  margin-bottom: 0.25rem;
  display: flex;
  align-items: center;
}

.bp-info-position i {
  margin-right: 0.375rem;
  font-size: 0.75rem;
  opacity: 0.8;
}

.abnormal-value {
  color: #ef4444;
  position: relative;
  display: inline-flex;
  align-items: center;
}

.abnormal-value::after {
  content: '!';
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border-radius: 50%;
  font-size: 0.65rem;
  font-weight: 700;
  margin-left: 0.375rem;
}

/* Tabs */
.tabs-container {
  display: flex;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  margin-bottom: 1.25rem;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  background-color: var(--card-bg-color, #ffffff);
  border-radius: 8px;
  padding: 0 0.5rem;
  box-shadow: 0 1px 3px var(--shadow-color, rgba(0, 0, 0, 0.05));
  position: sticky;
  top: 0;
  z-index: 10;
}

.tabs-container::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.tab {
  display: inline-flex;
  align-items: center;
  padding: 0.875rem 1rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary-color, #6b7280);
  border: none;
  background: none;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
  white-space: nowrap;
  position: relative;
  letter-spacing: 0.01em;
}

.tab:hover {
  color: #2C4B2B;
  background-color: rgba(44, 75, 43, 0.04);
}

.tab:hover i.fas.fa-external-link-alt {
  opacity: 1;
}

.tab.active {
  color: #2C4B2B;
  font-weight: 700;
}

.tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #2C4B2B;
}

.tab i {
  margin-right: 0.5rem;
  font-size: 0.875rem;
  opacity: 0.9;
}

.tab i.fas.fa-external-link-alt {
  margin-right: 0;
  margin-left: 0.5rem;
  font-size: 0.75rem;
  opacity: 0.7;
}

.tab-count {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
  height: 20px;
  background-color: #f3f4f6;
  color: #4b5563;
  border-radius: 10px;
  font-size: 0.75rem;
  font-weight: 700;
  padding: 0 0.375rem;
  margin-left: 0.375rem;
}

/* Content Card */
.content-container {
  background-color: var(--card-bg-color, white);
  border-radius: 8px;
  box-shadow: 0 1px 3px var(--shadow-color, rgba(0, 0, 0, 0.05));
  overflow: hidden;
  border: 1px solid var(--border-color, #f3f4f6);
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

.tab-content {
  display: none;
  padding: 0;
}

.tab-content.active {
  display: block;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from { opacity: 0.9; }
  to { opacity: 1; }
}

/* Section Header with Action */
.section-header-with-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.section-action-button {
  display: inline-flex;
  align-items: center;
  padding: 0.7rem 1.25rem;
  background-color: #10b981;
  color: white;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.section-action-button:hover {
  background-color: #059669;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.section-action-button i {
  margin-right: 0.5rem;
}

/* Summary Tab Styles */
.summary-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 2rem;
}

.summary-section {
  background-color: var(--card-bg-color, #ffffff);
  border-radius: 16px;
  overflow: hidden;
  border: 1px solid var(--border-color, #f3f4f6);
  box-shadow: 0 1px 2px var(--shadow-color, rgba(0, 0, 0, 0.03));
}

.recent-visit-card {
  padding: 1.75rem;
  background-color: var(--bg-color, #f9fafb);
  border-radius: 12px;
  margin: 1.25rem;
  border: 1px solid var(--border-color, #e5e7eb);
  transition: all 0.2s ease;
}

.recent-visit-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.04);
}

.visit-date {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #0ea5e9;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.visit-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.25rem;
}

.visit-reason, .visit-diagnosis, .visit-doctor {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.detail-label {
  font-weight: 600;
  color: #4b5563;
  min-width: 80px;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.03em;
}

.detail-value {
  color: #111827;
  font-size: 1rem;
  font-weight: 500;
}

.view-visit-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.6rem 1rem;
  background-color: #f1f5f9;
  color: #0ea5e9;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
}

.view-visit-button:hover {
  background-color: #e2e8f0;
  transform: translateY(-2px);
}

/* Visits Tab Styles */
.visits-timeline {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.visit-card {
  background-color: var(--card-bg-color, #ffffff);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--border-color, #f1f5f9);
  box-shadow: 0 1px 3px var(--shadow-color, rgba(0, 0, 0, 0.05));
  transition: all 0.2s ease;
}

.visit-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
}

.visit-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  background-color: var(--card-header-bg, #f8fafc);
  border-bottom: 1px solid var(--border-color, #f1f5f9);
}

.visit-actions {
  display: flex;
  gap: 0.5rem;
}

.visit-action {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  color: #64748b;
  transition: all 0.2s ease;
}

.visit-action.edit {
  background-color: #f1f5f9;
}

.visit-action.edit:hover {
  background-color: #e2e8f0;
  color: #0ea5e9;
}

.visit-action.view {
  background-color: #f0f9ff;
  color: #0ea5e9;
}

.visit-action.view:hover {
  background-color: #e0f2fe;
}

.visit-card-content {
  padding: 1.25rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.visit-detail {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.visit-detail-label {
  font-weight: 600;
  color: var(--text-secondary-color, #64748b);
  min-width: 80px;
}

.visit-detail-value {
  color: var(--text-color, #0f172a);
  flex: 1;
}

.visit-card-footer {
  display: block;
  padding: 0.75rem 1.25rem;
  background-color: var(--card-header-bg, #f8fafc);
  color: #0ea5e9;
  text-align: center;
  text-decoration: none;
  border-top: 1px solid var(--border-color, #f1f5f9);
  font-weight: 500;
  transition: all 0.2s ease;
}

.visit-card-footer:hover {
  background-color: #f0f9ff;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 1.5rem;
  text-align: center;
  background-color: var(--bg-color, #f9fafb);
  border-radius: 16px;
  border: 1px dashed var(--border-color, #e5e7eb);
}

.empty-icon {
  font-size: 3.25rem;
  color: #d1d5db;
  margin-bottom: 1.5rem;
  opacity: 0.8;
}

.empty-state h3 {
  font-size: 1.375rem;
  font-weight: 600;
  color: var(--text-color, #111827);
  margin: 0 0 0.75rem;
  letter-spacing: -0.01em;
}

.empty-state p {
  color: var(--text-secondary-color, #6b7280);
  margin: 0 0 2rem;
  max-width: 420px;
  font-size: 1.0625rem;
  line-height: 1.6;
}

.empty-action-button {
  display: inline-flex;
  align-items: center;
  gap: 0.625rem;
  padding: 0.875rem 1.75rem;
  background-color: #D97B3A;
  color: white;
  border-radius: 8px;
  font-size: 0.9375rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.empty-action-button:hover {
  background-color: #C06A29;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color, #111827) !important;
  margin: 0 0 1.5rem;
  display: flex;
  align-items: center;
  background-color: var(--bg-color, #f9fafb);
  padding: 1rem 1.25rem;
  border-radius: 8px;
  border: 1px solid var(--border-color, #f3f4f6);
  box-shadow: 0 1px 2px var(--shadow-color, rgba(0, 0, 0, 0.02));
  letter-spacing: -0.01em;
  position: relative;
  line-height: 1;
}

.section-title span {
  display: flex;
  align-items: center;
  line-height: 1;
}

.section-title i {
  margin-right: 0.75rem;
  color: var(--primary-color, #2C4B2B);
  font-size: 1rem;
  width: 26px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.9;
  background-color: var(--light-primary, rgba(44, 75, 43, 0.1));
  border-radius: 6px;
  padding: 6px;
  line-height: 1;
}

.section-title-note {
  position: absolute;
  right: 1.5rem;
  font-size: 0.8125rem;
  color: var(--text-secondary-color, #6b7280);
  font-weight: 500;
  background-color: var(--bg-color, #f3f4f6);
  padding: 0.375rem 0.75rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

/* Info Grid */
.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 1rem;
  padding: 1rem;
  background-color: var(--card-bg-color, white);
  border-radius: 0 0 8px 8px;
  border: 1px solid var(--border-color, #f3f4f6);
  border-top: none;
}

.bp-glucose-row {
  display: flex;
  gap: 1rem;
  grid-column: 1 / -1;
}

.bp-glucose-row .info-card {
  flex: 1;
  min-width: 0;
}

.info-card {
  background-color: var(--bg-color, #f9fafb);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid var(--border-color, #f3f4f6);
  transition: all 0.2s ease;
}

.info-card:hover {
  background-color: #f3f4f6;
}

.info-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary-color, #4b5563);
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.03em;
  display: block;
}

.info-value {
  font-size: 1rem;
  color: var(--text-color, #111827);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  line-height: 1.4;
  word-break: break-word;
  font-weight: 500;
}

.info-value i {
  color: var(--primary-color, #2C4B2B);
  flex-shrink: 0;
  font-size: 0.875rem;
  opacity: 0.9;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--light-primary, rgba(44, 75, 43, 0.1));
  border-radius: 6px;
}

/* Section */
.section {
  background-color: var(--card-bg-color, white);
  border-radius: 10px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid var(--border-color, #f1f5f9);
  box-shadow: 0 1px 2px var(--shadow-color, rgba(0, 0, 0, 0.03));
}

.section-header {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color, #111827) !important;
  margin: 0 0 1rem;
  display: flex;
  align-items: center;
  padding: 1rem 1.25rem;
  border-bottom: 1px solid var(--border-color, #f3f4f6);
  background-color: var(--bg-color, #f9fafb);
  letter-spacing: -0.01em;
  position: relative;
  border-radius: 8px 8px 0 0;
}

.section-header h3 {
  margin: 0;
  padding: 0;
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 1;
  display: flex;
  align-items: center;
  color: var(--text-color, #111827) !important;
}

.section-header i {
  margin-right: 0.75rem;
  color: var(--primary-color, #2C4B2B);
  font-size: 1rem;
  opacity: 0.9;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  background-color: var(--light-primary, rgba(44, 75, 43, 0.1));
  border-radius: 6px;
  flex-shrink: 0;
}

.section-header-note {
  margin-left: auto;
  font-size: 0.875rem;
  color: var(--text-secondary-color, #6b7280);
  font-weight: 500;
  background-color: var(--bg-color, #f3f4f6);
  padding: 0.375rem 0.75rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  gap: 0.375rem;
}

/* Vital Signs Styling */
.vitals-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.75rem;
}

.vital-section {
  background-color: var(--card-bg-color, white);
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid var(--border-color, #f3f4f6);
  box-shadow: 0 1px 2px var(--shadow-color, rgba(0, 0, 0, 0.03));
  transition: all 0.2s ease;
}

.vital-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.04);
}

.vital-header {
  display: flex;
  align-items: flex-start;
  padding: 0.875rem 1.25rem;
  background-color: var(--bg-color, #f9fafb);
  border-bottom: 1px solid var(--border-color, #f3f4f6);
}

.vital-icon {
  font-size: 0.9rem;
  margin-right: 0.75rem;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  flex-shrink: 0;
  margin-top: 1px;
}

.vital-header h3 {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color, #111827) !important;
  margin: 0;
  letter-spacing: -0.01em;
  line-height: 1.4;
}

.vital-content {
  padding: 0;
}

.vital-item {
  padding: 0.875rem 1.25rem;
  border-bottom: 1px solid var(--border-color, #f3f4f6);
}

.vital-item:last-child {
  border-bottom: none;
}

.vital-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary-color, #4b5563);
  margin-bottom: 0.625rem;
  text-transform: uppercase;
  letter-spacing: 0.03em;
  display: block;
}

.vital-value {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-color, #111827);
  line-height: 1.4;
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
}

.vital-value i {
  margin-top: 3px;
}

/* Mental Health Section Styles */
.mental-health-section {
  background-color: var(--bg-color, #f8f9ff);
  border-left: 4px solid var(--primary-color, #4a6da7);
}

.mental-health-section .vital-header {
  position: relative;
}

.assessment-date {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.85rem;
  color: var(--text-secondary-color, #6c757d);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.cognitive-item, .mental-item {
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  background-color: var(--card-bg-color, white);
  box-shadow: 0 2px 4px var(--shadow-color, rgba(0, 0, 0, 0.05));
}

.cognitive-item .vital-label, .mental-item .vital-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  color: var(--primary-color, #4a6da7);
  font-weight: 600;
}

.no-data-message {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-secondary-color, #6c757d);
  font-style: italic;
  padding: 0.5rem 0;
}

.assessment-action {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.assessment-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background-color: var(--primary-color, #2C4B2B);
  color: white;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.2s ease;
}

.assessment-button:hover {
  background-color: var(--success-color, #4A7A48);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--shadow-color, rgba(0, 0, 0, 0.1));
}

/* Vaccination Section Styles */
.vaccination-section {
  background-color: var(--bg-color, #f8fafc);
}

.vaccination-section .vital-header {
  background-color: var(--light-info, #f0f7ff);
}

.vaccination-complete {
  color: var(--success-color, #10b981);
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

.vaccination-missing {
  color: var(--error-color, #ef4444);
  margin-right: 0.5rem;
  font-size: 1.1rem;
}

.grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

/* Medical Records */
.records-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.records-count {
  font-size: 0.9rem;
  color: var(--text-secondary-color, #64748b);
  background-color: var(--bg-color, #f1f5f9);
  padding: 0.4rem 0.8rem;
  border-radius: 9999px;
  font-weight: 500;
}

.record-card {
  background-color: var(--card-bg-color, white);
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid var(--border-color, #f1f5f9);
  box-shadow: 0 2px 4px var(--shadow-color, rgba(0, 0, 0, 0.03));
  transition: all 0.2s ease;
}

.record-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px var(--shadow-color, rgba(0, 0, 0, 0.05));
  border-color: var(--border-color, #e2e8f0);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.25rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color, #f1f5f9);
}

.record-date {
  font-weight: 600;
  color: var(--info-color, #0ea5e9);
  display: flex;
  align-items: center;
  font-size: 1.05rem;
}

.record-date i {
  margin-right: 0.75rem;
  font-size: 1rem;
}

.record-meta {
  font-size: 0.85rem;
  color: var(--text-secondary-color, #64748b);
  background-color: var(--bg-color, #f8fafc);
  padding: 0.35rem 0.75rem;
  border-radius: 6px;
}

.alert {
  padding: 1.25rem;
  border-radius: 10px;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  box-shadow: 0 1px 2px var(--shadow-color, rgba(0, 0, 0, 0.03));
  border: 1px solid transparent;
}

.alert i {
  margin-right: 1rem;
  font-size: 1.1rem;
  flex-shrink: 0;
}

.alert-info {
  background-color: var(--light-info, #f0f9ff);
  color: var(--info-color, #0369a1);
  border-color: var(--info-color, #bae6fd);
}

/* Loading State */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f1f5f9;
  border-top: 3px solid #0ea5e9;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.error-container {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
}

.error-container i {
  margin-right: 0.75rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .patient-detail-container {
    padding: 1.5rem;
    border-radius: 12px;
  }

  .patient-header {
    flex-direction: column;
    padding: 1.5rem;
    margin-bottom: 2rem;
  }

  .header-left {
    max-width: 100%;
    margin-bottom: 1.5rem;
  }

  .patient-title {
    font-size: 1.625rem;
    flex-wrap: wrap;
    margin-bottom: 0.625rem;
  }

  .patient-subtitle {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.625rem;
  }

  .patient-id-badge {
    margin-top: 0.625rem;
    margin-left: 0;
  }

  .action-buttons {
    width: 100%;
    flex-wrap: wrap;
    gap: 0.75rem;
  }

  .action-button {
    flex: 1;
    min-width: calc(50% - 0.375rem);
    padding: 0.75rem 0.75rem;
    font-size: 0.875rem;
    justify-content: center;
  }

  .overview-cards {
    grid-template-columns: 1fr;
    gap: 1.25rem;
    margin-bottom: 2rem;
  }

  .grid-2,
  .grid-3,
  .vitals-grid,
  .info-grid,
  .summary-grid {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }

  .tabs-container {
    flex-wrap: nowrap;
    overflow-x: auto;
    padding: 0 0.5rem;
    margin-bottom: 1.5rem;
  }

  .tab {
    padding: 1rem 1.25rem;
    font-size: 0.875rem;
  }

  .tab i {
    margin-right: 0.625rem;
  }

  .content-container {
    padding: 1.5rem;
  }

  .section-title {
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
    padding: 1rem 1.25rem;
  }

  .section-title-note {
    position: static;
    margin-top: 0.5rem;
    margin-left: 2.25rem;
    font-size: 0.75rem;
  }

  .section-header-note {
    position: static;
    margin-top: 0.5rem;
    margin-left: 2.25rem;
    font-size: 0.75rem;
  }

  .health-data-source {
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
  }

  .section-header-with-action {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .section-action-button {
    width: 100%;
    justify-content: center;
  }

  .section {
    padding: 1.25rem;
  }

  .info-card {
    padding: 1.25rem;
  }

  .vital-section {
    margin-bottom: 1.25rem;
  }

  .vital-header {
    padding: 1rem 1.25rem;
  }

  .vital-content {
    padding: 0;
  }

  .vital-item {
    padding: 1rem 1.25rem;
  }

  .vital-value {
    font-size: 1.0625rem;
  }

  .record-card {
    padding: 1.25rem;
  }

  .record-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .record-meta {
    margin-top: 0.625rem;
  }

  .visit-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.875rem;
  }

  .visit-actions {
    align-self: flex-end;
  }

  .visit-detail {
    flex-direction: column;
    gap: 0.375rem;
  }

  .visit-detail-label {
    min-width: auto;
  }

  .recent-visit-card {
    margin: 1rem;
    padding: 1.25rem;
  }

  .visit-reason, .visit-diagnosis, .visit-doctor {
    flex-direction: column;
    gap: 0.375rem;
  }
}

@media (max-width: 480px) {
  .patient-detail-container {
    padding: 1rem;
    border-radius: 8px;
  }

  .patient-header {
    padding: 1.25rem;
  }

  .action-button {
    min-width: 100%;
    margin-bottom: 0.625rem;
  }

  .overview-cards {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1.75rem;
  }

  .overview-card {
    padding: 1.5rem;
  }

  .overview-icon {
    width: 44px;
    height: 44px;
    font-size: 1.125rem;
    margin-right: 1.125rem;
  }

  .overview-value {
    font-size: 1.25rem;
  }

  .bp-overview-card {
    grid-column: 1 / -1;
  }

  .bp-readings {
    flex-direction: row;
    justify-content: space-between;
    gap: 1rem;
  }

  .bp-reading {
    flex: 1;
  }

  .bp-info-card {
    grid-column: 1 / -1;
  }

  .bp-info-values {
    flex-direction: row;
    justify-content: space-between;
    gap: 1rem;
  }

  .bp-info-reading {
    flex: 1;
  }

  .section-header {
    font-size: 1.0625rem;
    padding: 1rem 1.25rem;
  }

  .empty-state {
    padding: 2.5rem 1.25rem;
  }

  .empty-icon {
    font-size: 2.75rem;
  }

  .empty-state h3 {
    font-size: 1.25rem;
  }

  .empty-action-button {
    width: 100%;
    justify-content: center;
  }
}
