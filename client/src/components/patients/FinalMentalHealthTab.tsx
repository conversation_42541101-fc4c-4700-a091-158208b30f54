import React from 'react';
import { Typo<PERSON>, <PERSON>, Grid, Card, Button, Paper, alpha } from '@mui/material';
import { Link } from 'react-router-dom';
import { useTheme } from '@mui/material/styles';

// Import icons
import PsychologyIcon from '@mui/icons-material/Psychology';
import SentimentVeryDissatisfiedIcon from '@mui/icons-material/SentimentVeryDissatisfied';
import SentimentDissatisfiedIcon from '@mui/icons-material/SentimentDissatisfied';
import WarningIcon from '@mui/icons-material/Warning';
import InfoIcon from '@mui/icons-material/Info';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import MoodIcon from '@mui/icons-material/Mood';
import MoodBadIcon from '@mui/icons-material/MoodBad';
import PsychologyAltIcon from '@mui/icons-material/PsychologyAlt';
import MemoryIcon from '@mui/icons-material/Memory';
import TimerIcon from '@mui/icons-material/Timer';
import AssignmentIcon from '@mui/icons-material/Assignment';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import HelpIcon from '@mui/icons-material/Help';

interface MentalHealthTabProps {
  patient: any;
  recentVisit: any;
}

// Helper functions for interpretations
const getMiniCogInterpretation = (score: number): string => {
  if (score <= 2) {
    return "Positive screen for cognitive impairment";
  } else {
    return "Negative screen for cognitive impairment";
  }
};

const getPhq9Interpretation = (score: number): string => {
  if (score >= 0 && score <= 4) {
    return "Minimal or no depression";
  } else if (score >= 5 && score <= 9) {
    return "Mild depression";
  } else if (score >= 10 && score <= 14) {
    return "Moderate depression";
  } else if (score >= 15 && score <= 19) {
    return "Moderately severe depression";
  } else if (score >= 20) {
    return "Severe depression";
  } else {
    return "Invalid score";
  }
};

const getGad7Interpretation = (score: number): string => {
  if (score >= 0 && score <= 4) {
    return "Minimal anxiety";
  } else if (score >= 5 && score <= 9) {
    return "Mild anxiety";
  } else if (score >= 10 && score <= 14) {
    return "Moderate anxiety";
  } else if (score >= 15) {
    return "Severe anxiety";
  } else {
    return "Invalid score";
  }
};

// Helper functions for treatment recommendations
const getPhq9TreatmentRecommendation = (score: number): string => {
  if (score >= 0 && score <= 4) {
    return "No treatment recommended. Monitor and reassess if symptoms worsen.";
  } else if (score >= 5 && score <= 9) {
    return "Watchful waiting; consider counseling, self-help resources, or follow-up within 1 month.";
  } else if (score >= 10 && score <= 14) {
    return "Consider psychotherapy, counseling, or pharmacotherapy; regular follow-up recommended.";
  } else if (score >= 15 && score <= 19) {
    return "Active treatment with pharmacotherapy and/or psychotherapy strongly recommended.";
  } else if (score >= 20) {
    return "Immediate initiation of pharmacotherapy and expedited referral to mental health specialist. Consider hospitalization if safety concerns exist.";
  } else {
    return "Unable to provide recommendation due to invalid score.";
  }
};

const getGad7TreatmentRecommendation = (score: number): string => {
  if (score >= 0 && score <= 4) {
    return "No treatment recommended. Monitor and reassess if symptoms worsen.";
  } else if (score >= 5 && score <= 9) {
    return "Consider supportive counseling, self-help resources, or follow-up within 1 month.";
  } else if (score >= 10 && score <= 14) {
    return "Consider cognitive behavioral therapy, other psychotherapy, or pharmacotherapy; regular follow-up recommended.";
  } else if (score >= 15) {
    return "Active treatment with pharmacotherapy and/or psychotherapy strongly recommended. Consider referral to mental health specialist.";
  } else {
    return "Unable to provide recommendation due to invalid score.";
  }
};

// Helper functions for status indicators
const getMiniCogStatus = (score: number | null): 'normal' | 'warning' | 'abnormal' | null => {
  if (score === null) return null;
  if (score <= 2) return 'abnormal';
  return 'normal';
};

const getPhq9Status = (score: number | null): 'normal' | 'warning' | 'abnormal' | null => {
  if (score === null) return null;
  if (score >= 0 && score <= 4) return 'normal';
  if (score >= 5 && score <= 9) return 'warning';
  if (score >= 10) return 'abnormal';
  return null;
};

const getGad7Status = (score: number | null): 'normal' | 'warning' | 'abnormal' | null => {
  if (score === null) return null;
  if (score >= 0 && score <= 4) return 'normal';
  if (score >= 5 && score <= 9) return 'warning';
  if (score >= 10) return 'abnormal';
  return null;
};

// Status indicator component
const StatusIndicator = ({ status }: { status: 'normal' | 'warning' | 'abnormal' | null | undefined }) => {
  const theme = useTheme();
  if (!status) return null;

  const getStatusColor = () => {
    switch (status) {
      case 'abnormal':
        return theme.palette.error.main;
      case 'warning':
        return theme.palette.warning.main;
      default:
        return theme.palette.success.main;
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'abnormal':
        return <WarningIcon fontSize="small" />;
      case 'warning':
        return <InfoIcon fontSize="small" />;
      default:
        return <CheckCircleIcon fontSize="small" />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'abnormal':
        return 'Needs attention';
      case 'warning':
        return 'Monitor';
      default:
        return 'Normal';
    }
  };

  return (
    <Box sx={{
      display: 'flex',
      alignItems: 'center',
      gap: 0.5,
      color: getStatusColor(),
      fontSize: '0.875rem',
      mt: 0.5
    }}>
      {getStatusIcon()}
      <Typography variant="body2" sx={{ color: getStatusColor() }}>
        {getStatusText()}
      </Typography>
    </Box>
  );
};

// Metric card component for consistent styling
const MetricCard = ({
  title,
  value,
  icon,
  status,
  additionalInfo,
  dataSource = 'patient'
}: {
  title: string;
  value: any;
  icon: React.ReactNode;
  status?: 'normal' | 'warning' | 'abnormal' | null;
  additionalInfo?: string;
  dataSource?: 'visit' | 'patient';
}) => {
  const theme = useTheme();

  return (
    <Paper
      elevation={0}
      sx={{
        p: 2,
        height: '100%',
        display: 'flex',
        alignItems: 'flex-start',
        gap: 2,
        borderRadius: 2,
        bgcolor: theme.palette.background.paper,
        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        '&:hover': {
          boxShadow: `0 4px 8px ${alpha(theme.palette.primary.main, 0.1)}`,
          borderColor: alpha(theme.palette.primary.main, 0.2),
        }
      }}
    >
      <Box
        sx={{
          width: 40,
          height: 40,
          borderRadius: 1,
          bgcolor: alpha(theme.palette.primary.main, 0.1),
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: theme.palette.primary.main
        }}
      >
        {icon}
      </Box>

      <Box sx={{ flex: 1 }}>
        <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1rem' }}>
          {title}
        </Typography>

        <Typography variant="body1" sx={{ mb: 0.5, fontSize: '1.125rem', fontWeight: 500 }}>
          {value !== null && value !== undefined ? value : (dataSource === 'visit' ? 'Not assessed' : 'Not available')}
        </Typography>

        {status && <StatusIndicator status={status} />}

        {additionalInfo && (
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1, fontStyle: 'italic' }}>
            {additionalInfo}
          </Typography>
        )}
      </Box>
    </Paper>
  );
};

// Assessment Results Card - A more elegant way to display assessment information
const AssessmentResultsCard = ({
  title,
  score,
  maxScore,
  interpretation,
  clinicalNotes,
  treatmentRecommendation,
  icon,
  status,
  dataSource = 'patient',
  color = 'primary'
}: {
  title: string;
  score: number | null;
  maxScore: number;
  interpretation?: string;
  clinicalNotes?: string;
  treatmentRecommendation?: string;
  icon: React.ReactNode;
  status?: 'normal' | 'warning' | 'abnormal' | null;
  dataSource?: 'visit' | 'patient';
  color?: string;
}) => {
  const theme = useTheme();

  // Get color based on status
  const getStatusColor = () => {
    if (!status) return theme.palette.grey[500];
    switch (status) {
      case 'abnormal':
        return theme.palette.error.main;
      case 'warning':
        return theme.palette.warning.main;
      default:
        return theme.palette.success.main;
    }
  };

  // Get icon color based on color prop
  const getIconColor = () => {
    switch (color) {
      case 'primary':
        return theme.palette.primary.main;
      case 'secondary':
        return theme.palette.secondary.main;
      case 'error':
        return theme.palette.error.main;
      case 'warning':
        return theme.palette.warning.main;
      case 'info':
        return theme.palette.info.main;
      case 'success':
        return theme.palette.success.main;
      default:
        return theme.palette.primary.main;
    }
  };

  // Get background color based on color prop
  const getBgColor = () => {
    switch (color) {
      case 'primary':
        return alpha(theme.palette.primary.main, 0.05);
      case 'secondary':
        return alpha(theme.palette.secondary.main, 0.05);
      case 'error':
        return alpha(theme.palette.error.main, 0.05);
      case 'warning':
        return alpha(theme.palette.warning.main, 0.05);
      case 'info':
        return alpha(theme.palette.info.main, 0.05);
      case 'success':
        return alpha(theme.palette.success.main, 0.05);
      default:
        return alpha(theme.palette.primary.main, 0.05);
    }
  };

  return (
    <Paper
      elevation={0}
      sx={{
        height: '100%',
        borderRadius: 2,
        bgcolor: theme.palette.background.paper,
        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        overflow: 'hidden',
        '&:hover': {
          boxShadow: `0 4px 8px ${alpha(theme.palette.primary.main, 0.1)}`,
          borderColor: alpha(theme.palette.primary.main, 0.2),
        }
      }}
    >
      {/* Header */}
      <Box sx={{
        p: 2,
        display: 'flex',
        alignItems: 'center',
        gap: 1.5,
        borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        bgcolor: alpha(theme.palette.background.default, 0.5)
      }}>
        <Box
          sx={{
            width: 40,
            height: 40,
            borderRadius: 1,
            bgcolor: alpha(getIconColor(), 0.1),
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: getIconColor()
          }}
        >
          {icon}
        </Box>
        <Typography variant="h6" sx={{ fontWeight: 600, fontSize: '1rem' }}>
          {title}
        </Typography>
      </Box>

      {score !== null ? (
        <Box sx={{ p: 2 }}>
          {/* Score and Interpretation Section */}
          <Grid container spacing={2}>
            {/* Score */}
            <Grid item xs={12} sm={6}>
              <Box sx={{
                p: 2,
                bgcolor: getBgColor(),
                borderRadius: 2,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%'
              }}>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1, textTransform: 'uppercase', fontSize: '0.75rem', letterSpacing: '0.5px' }}>
                  Score
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'baseline', gap: 0.5 }}>
                  <Typography variant="h3" sx={{ fontWeight: 700, color: getIconColor() }}>
                    {score}
                  </Typography>
                  <Typography variant="body1" color="text.secondary">
                    /{maxScore}
                  </Typography>
                </Box>
                {status && (
                  <Box sx={{ mt: 1 }}>
                    <StatusIndicator status={status} />
                  </Box>
                )}
              </Box>
            </Grid>

            {/* Interpretation */}
            <Grid item xs={12} sm={6}>
              <Box sx={{
                p: 2,
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center'
              }}>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1, textTransform: 'uppercase', fontSize: '0.75rem', letterSpacing: '0.5px' }}>
                  Interpretation
                </Typography>
                <Typography variant="body1" sx={{ fontWeight: 500 }}>
                  {interpretation || 'Not available'}
                </Typography>
              </Box>
            </Grid>
          </Grid>

          {/* Clinical Notes and Treatment Recommendations */}
          {(clinicalNotes || treatmentRecommendation) && (
            <Box sx={{ mt: 2, pt: 2, borderTop: `1px dashed ${alpha(theme.palette.divider, 0.2)}` }}>
              {clinicalNotes && (
                <Box sx={{ mb: treatmentRecommendation ? 2 : 0 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1, textTransform: 'uppercase', fontSize: '0.75rem', letterSpacing: '0.5px' }}>
                    Clinical Notes
                  </Typography>
                  <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                    {clinicalNotes}
                  </Typography>
                </Box>
              )}

              {treatmentRecommendation && (
                <Box sx={{ mt: clinicalNotes ? 2 : 0 }}>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1, textTransform: 'uppercase', fontSize: '0.75rem', letterSpacing: '0.5px' }}>
                    Treatment Recommendation
                  </Typography>
                  <Typography variant="body2">
                    {treatmentRecommendation}
                  </Typography>
                </Box>
              )}
            </Box>
          )}
        </Box>
      ) : (
        <Box sx={{ p: 3, display: 'flex', alignItems: 'center', gap: 1, color: 'text.secondary' }}>
          <InfoIcon sx={{ fontSize: '1rem' }} />
          <Typography variant="body1">
            {dataSource === 'visit' ? 'Not measured in this visit' : 'No assessment data available'}
          </Typography>
        </Box>
      )}
    </Paper>
  );
};

const FinalMentalHealthTab: React.FC<MentalHealthTabProps> = ({ patient, recentVisit }) => {
  const theme = useTheme();

  // Determine data source - use recent visit data if available, otherwise use patient data
  const dataSource = recentVisit ? 'visit' : 'patient';
  const data = recentVisit || patient;

  // Check if we have any mental health data
  const hasMentalHealthData =
    data.cognitive_test_results ||
    data.mini_cog_word_recall_score ||
    data.depression_score ||
    data.phq9_interest_pleasure ||
    data.anxiety_score ||
    data.gad7_feeling_nervous;

  // Get assessment date if available
  const assessmentDate = recentVisit ? new Date(recentVisit.visit_date).toLocaleDateString() : '';

  // Calculate scores
  const miniCogWordRecallScore = data.mini_cog_word_recall_score ? parseInt(data.mini_cog_word_recall_score) : null;
  const miniCogClockDrawingScore = data.mini_cog_clock_drawing_score ? parseInt(data.mini_cog_clock_drawing_score) : null;
  const miniCogTotalScore = (miniCogWordRecallScore !== null && miniCogClockDrawingScore !== null)
    ? miniCogWordRecallScore + miniCogClockDrawingScore
    : null;

  const depressionScore = data.depression_score ? parseInt(data.depression_score) : null;
  const anxietyScore = data.anxiety_score ? parseInt(data.anxiety_score) : null;

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="body1" sx={{ mb: 3 }}>
        This tab displays mental health assessment results for the patient, including cognitive function, depression, and anxiety screenings.
        The most recent values are shown, prioritizing data from the latest visit when available.
      </Typography>

      {/* Cognitive Assessment Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Cognitive Assessment
      </Typography>
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <AssessmentResultsCard
            title="Mini-Cog Assessment"
            score={miniCogTotalScore}
            maxScore={5}
            icon={<PsychologyIcon />}
            status={getMiniCogStatus(miniCogTotalScore)}
            interpretation={miniCogTotalScore !== null ? getMiniCogInterpretation(miniCogTotalScore) : undefined}
            clinicalNotes={data.mini_cog_notes}
            dataSource={dataSource}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={6}>
          <MetricCard
            title="Word Recall Score"
            value={miniCogWordRecallScore !== null ? `${miniCogWordRecallScore}/3` : undefined}
            icon={<MemoryIcon />}
            additionalInfo="Ability to recall three words after a brief delay"
            dataSource={dataSource}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={6}>
          <MetricCard
            title="Clock Drawing Score"
            value={miniCogClockDrawingScore !== null ? `${miniCogClockDrawingScore}/2` : undefined}
            icon={<TimerIcon />}
            additionalInfo="Ability to draw a clock showing a specific time"
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* Depression Assessment Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Depression Assessment
      </Typography>
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <AssessmentResultsCard
            title="PHQ-9 Depression Screening"
            score={depressionScore}
            maxScore={27}
            icon={<SentimentVeryDissatisfiedIcon />}
            status={getPhq9Status(depressionScore)}
            interpretation={depressionScore !== null ? getPhq9Interpretation(depressionScore) : undefined}
            clinicalNotes={data.phq9_notes}
            treatmentRecommendation={depressionScore !== null ? getPhq9TreatmentRecommendation(depressionScore) : undefined}
            dataSource={dataSource}
            color="error"
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <MetricCard
            title="Depression Severity"
            value={depressionScore !== null ? getPhq9Interpretation(depressionScore) : undefined}
            icon={<MoodBadIcon />}
            status={getPhq9Status(depressionScore)}
            dataSource={dataSource}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <MetricCard
            title="Suicidal Ideation"
            value={data.phq9_suicidal_ideation}
            icon={<WarningIcon />}
            status={data.phq9_suicidal_ideation === 'Yes' ? 'abnormal' : null}
            additionalInfo="Based on PHQ-9 question about thoughts of self-harm"
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* Anxiety Assessment Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Anxiety Assessment
      </Typography>
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12}>
          <AssessmentResultsCard
            title="GAD-7 Anxiety Screening"
            score={anxietyScore}
            maxScore={21}
            icon={<SentimentDissatisfiedIcon />}
            status={getGad7Status(anxietyScore)}
            interpretation={anxietyScore !== null ? getGad7Interpretation(anxietyScore) : undefined}
            clinicalNotes={data.gad7_notes}
            treatmentRecommendation={anxietyScore !== null ? getGad7TreatmentRecommendation(anxietyScore) : undefined}
            dataSource={dataSource}
            color="warning"
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <MetricCard
            title="Anxiety Severity"
            value={anxietyScore !== null ? getGad7Interpretation(anxietyScore) : undefined}
            icon={<SentimentDissatisfiedIcon />}
            status={getGad7Status(anxietyScore)}
            dataSource={dataSource}
          />
        </Grid>
        <Grid item xs={12} sm={6}>
          <MetricCard
            title="Difficulty in Daily Life"
            value={data.gad7_difficulty}
            icon={<InfoIcon />}
            additionalInfo="How difficult anxiety symptoms make it to function"
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* Add New Assessment Button */}
      {!hasMentalHealthData && patient && (
        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
          <Button
            component={Link}
            to={`/patients/${patient.patient_id}/visits/new`}
            variant="contained"
            color="primary"
            startIcon={<i className="fas fa-plus-circle"></i>}
            sx={{ borderRadius: 2 }}
          >
            Perform New Assessment
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default FinalMentalHealthTab;
