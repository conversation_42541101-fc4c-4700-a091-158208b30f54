/* Modern Patient Detail Styles - Consolidated CSS */

/* Base Variables */
:root {
  --primary-color: #2C4B2B;
  --primary-light: rgba(44, 75, 43, 0.1);
  --primary-border: rgba(44, 75, 43, 0.2);
  
  --error-color: #ef4444;
  --error-light: rgba(239, 68, 68, 0.1);
  --error-border: rgba(239, 68, 68, 0.2);
  
  --warning-color: #f59e0b;
  --warning-light: rgba(245, 158, 11, 0.1);
  --warning-border: rgba(245, 158, 11, 0.2);
  
  --info-color: #0ea5e9;
  --info-light: rgba(14, 165, 233, 0.1);
  --info-border: rgba(14, 165, 233, 0.2);
  
  --success-color: #10b981;
  --success-light: rgba(16, 185, 129, 0.1);
  --success-border: rgba(16, 185, 129, 0.2);
  
  --text-color: #374151;
  --text-secondary: #6b7280;
  --bg-color: #f9fafb;
  --card-bg: #ffffff;
  --border-color: #f3f4f6;
  --shadow-color: rgba(0, 0, 0, 0.05);
}

/* Dark Mode Variables */
.dark-mode {
  --primary-color: #4A7A48;
  --primary-light: rgba(74, 122, 72, 0.15);
  --primary-border: rgba(74, 122, 72, 0.25);
  
  --error-color: #f87171;
  --error-light: rgba(248, 113, 113, 0.15);
  --error-border: rgba(248, 113, 113, 0.25);
  
  --warning-color: #fbbf24;
  --warning-light: rgba(251, 191, 36, 0.15);
  --warning-border: rgba(251, 191, 36, 0.25);
  
  --info-color: #38bdf8;
  --info-light: rgba(56, 189, 248, 0.15);
  --info-border: rgba(56, 189, 248, 0.25);
  
  --success-color: #34d399;
  --success-light: rgba(52, 211, 153, 0.15);
  --success-border: rgba(52, 211, 153, 0.25);
  
  --text-color: #f3f4f6;
  --text-secondary: #d1d5db;
  --bg-color: #111827;
  --card-bg: #1e1e1e;
  --border-color: rgba(255, 255, 255, 0.1);
  --shadow-color: rgba(0, 0, 0, 0.2);
}

/* Container Styles */
.patient-detail-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
  color: var(--text-color);
  background-color: var(--bg-color);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.5;
}

/* Dark Mode Fixes - Consolidated */
.dark-mode .MuiCard-root,
.dark-mode .MuiPaper-root,
.dark-mode .MuiBox-root,
.dark-mode .MuiGrid-container,
.dark-mode .MuiGrid-item,
.dark-mode .MuiTypography-root {
  background-color: transparent !important;
}

/* Card Backgrounds in Dark Mode */
.dark-mode .patient-detail-container .MuiCard-root {
  background-color: var(--card-bg) !important;
}

/* Chip Backgrounds in Dark Mode */
.dark-mode .patient-detail-container .MuiChip-root {
  background-color: rgba(255, 255, 255, 0.15) !important;
}

/* Health Metric Cards */
.health-metric-card {
  height: 100%;
  padding: 1.5rem;
  border-radius: 0.5rem;
  border: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px var(--shadow-color);
  background-color: var(--card-bg);
}

.health-metric-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px var(--shadow-color);
  border-color: rgba(0, 0, 0, 0.1);
}

.dark-mode .health-metric-card:hover {
  border-color: rgba(255, 255, 255, 0.2);
}

.health-metric-card .accent-strip {
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
}

.health-metric-card .card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.health-metric-card .icon-container {
  width: 36px;
  height: 36px;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.health-metric-card .metric-title {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-color);
}

.health-metric-card .metric-value {
  font-weight: 700;
  font-size: 1.5rem;
  display: flex;
  align-items: baseline;
}

.health-metric-card .metric-unit {
  margin-left: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-secondary);
}

.health-metric-card .normal-range {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  margin-top: 0.75rem;
  color: var(--text-secondary);
  font-size: 0.75rem;
}

/* Abnormal Indicator */
.abnormal-indicator {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background-color: var(--error-light);
  color: var(--error-color);
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
}

.abnormal-indicator i {
  font-size: 0.7rem;
}

.abnormal-indicator span {
  font-weight: 600;
  font-size: 0.7rem;
}

/* Position Indicators (Lying/Standing) */
.position-container {
  padding: 0.75rem;
  border-radius: 0.375rem;
  background-color: rgba(0, 0, 0, 0.02);
  border: 1px solid rgba(0, 0, 0, 0.03);
}

.dark-mode .position-container {
  background-color: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.05);
}

.position-label {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  margin-bottom: 0.5rem;
  color: var(--text-secondary);
  font-size: 0.75rem;
  font-weight: 600;
}

/* Health Data Source Box */
.health-data-source {
  padding: 1rem;
  margin-bottom: 1.5rem;
  border-radius: 0.5rem;
  background-color: var(--info-light);
  color: var(--info-color);
  border: 1px solid var(--info-border);
  border-left: 4px solid var(--info-color);
  display: flex;
  align-items: center;
  gap: 0.75rem;
  box-shadow: 0 2px 8px var(--shadow-color);
}

.health-data-source .icon-circle {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.dark-mode .health-data-source .icon-circle {
  background-color: rgba(0, 0, 0, 0.2);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .patient-detail-container {
    padding: 1rem;
  }
  
  .health-metric-card {
    padding: 1.25rem;
  }
}
