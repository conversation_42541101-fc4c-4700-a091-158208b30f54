/* Direct fix for the black box behind "Back to Patients" link in dark mode */

/* Target the link directly */
.dark-mode .patient-detail-container a[style*="display: inline-flex"] {
  background-color: transparent !important;
  background: transparent !important;
}

/* Target the parent container */
.dark-mode .patient-detail-container .MuiBox-root a[style*="display: inline-flex"] {
  background-color: transparent !important;
  background: transparent !important;
}

/* Target the icon inside the link */
.dark-mode .patient-detail-container a[style*="display: inline-flex"] i {
  background-color: transparent !important;
  background: transparent !important;
}

/* Target the text inside the link */
.dark-mode .patient-detail-container a[style*="display: inline-flex"] span {
  background-color: transparent !important;
  background: transparent !important;
}

/* Target the parent Box component */
.dark-mode .patient-detail-container .MuiCard-root .MuiCardContent-root .MuiBox-root .MuiBox-root {
  background-color: transparent !important;
  background: transparent !important;
}

/* Target the patient name and ID section */
.dark-mode .patient-detail-container .MuiCard-root .MuiCardContent-root .MuiBox-root .MuiBox-root .MuiBox-root {
  background-color: transparent !important;
  background: transparent !important;
}

/* Target the patient chips */
.dark-mode .patient-detail-container .MuiCard-root .MuiCardContent-root .MuiBox-root .MuiBox-root .MuiChip-root {
  background-color: rgba(255, 255, 255, 0.15) !important;
}

/* Nuclear option for the entire header area */
.dark-mode .patient-detail-container .MuiCard-root:first-of-type {
  background-color: #1e1e1e !important;
}

.dark-mode .patient-detail-container .MuiCard-root:first-of-type .MuiCardContent-root * {
  background-color: transparent !important;
}
