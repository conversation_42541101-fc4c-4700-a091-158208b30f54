import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  useTheme,
  alpha
} from '@mui/material';
import { Patient, Visit } from '../../types';
import DirectionsRunIcon from '@mui/icons-material/DirectionsRun';
import RestaurantIcon from '@mui/icons-material/Restaurant';
import LocalDrinkIcon from '@mui/icons-material/LocalDrink';
import VitaminIcon from '@mui/icons-material/Medication';
import VisibilityIcon from '@mui/icons-material/Visibility';
import HearingIcon from '@mui/icons-material/Hearing';
import WarningIcon from '@mui/icons-material/Warning';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import InfoIcon from '@mui/icons-material/Info';
import HomeIcon from '@mui/icons-material/Home';
import PeopleIcon from '@mui/icons-material/People';
import PersonIcon from '@mui/icons-material/Person';
import AttachMoneyIcon from '@mui/icons-material/AttachMoney';
import DirectionsCarIcon from '@mui/icons-material/DirectionsCar';
import SecurityIcon from '@mui/icons-material/Security';
import ApartmentIcon from '@mui/icons-material/Apartment';
import ElderlyIcon from '@mui/icons-material/Elderly';

// Define types for the health status fields we need to access
interface HealthStatusFields {
  // Activity & Nutrition
  activity_level?: string;
  nutritional_status?: string;
  hydration_status?: string;
  supplements?: string;

  // Sensory Status
  vision_status?: string;
  hearing_status?: string;
  use_of_aid_vision?: string;
  use_of_aid_hearing?: string;

  // Social & Environmental Factors
  social_interaction_levels?: string;
  social_support?: string;
  social_support_network?: string;
  living_situation?: string;
  living_conditions?: string;
  environmental_risks?: string;
  transportation_access?: string;
  financial_concern?: string;
  home_safety_evaluation?: string;

  // Other fields (kept for compatibility)
  exercise_frequency?: string;
  dietary_intake_quality?: string;
  fall_risk?: string;
  mobility_status?: string;
  assistive_devices_used?: string;
}

// Interface for the component props
interface HealthStatusTabProps {
  patient: Patient;
  recentVisit: Visit | null;
}

// Helper function to convert boolean to string
const formatBoolean = (value: boolean | string | null | undefined): string => {
  if (value === null || value === undefined) return '';
  if (typeof value === 'boolean') return value ? 'Yes' : 'No';
  return value;
};

// Helper function to determine status based on field type and value
const getStatusByFieldAndValue = (field: string, value: string): 'normal' | 'warning' | 'abnormal' | null => {
  // Common status mappings
  const commonMappings: Record<string, 'normal' | 'warning' | 'abnormal'> = {
    'Normal': 'normal',
    'Mild Impairment': 'normal',
    'Moderate Impairment': 'warning',
    'Severe Impairment': 'abnormal',
    'Legally Blind': 'abnormal',
    'Deaf': 'abnormal',
    'Low': 'normal',
    'Medium': 'warning',
    'High': 'abnormal',
    'Independent': 'normal',
    'Needs Assistance': 'warning',
    'Dependent': 'abnormal',
    'Bedbound': 'abnormal',
    'Safe': 'normal',
    'Minor Issues': 'normal',
    'Moderate Issues': 'warning',
    'Major Issues': 'abnormal'
  };

  // Check if value exists in common mappings
  if (commonMappings[value]) {
    return commonMappings[value];
  }

  // Field-specific mappings
  switch (field) {
    case 'activity_level':
      // Normalize the value to handle case sensitivity and format variations
      const normalizedValue = value.toLowerCase();
      if (normalizedValue.includes('sedentary')) return 'warning';
      if (normalizedValue.includes('light')) return 'normal';
      if (normalizedValue.includes('moderate')) return 'normal';
      if (normalizedValue.includes('active')) return 'normal';
      break;

    case 'nutritional_status':
      const normalizedNutrition = value.toLowerCase();
      if (normalizedNutrition.includes('poor') || normalizedNutrition.includes('malnourish')) return 'abnormal';
      if (normalizedNutrition.includes('fair') || normalizedNutrition.includes('risk')) return 'warning';
      if (normalizedNutrition.includes('good') || normalizedNutrition.includes('well') || normalizedNutrition.includes('excellent')) return 'normal';
      break;

    case 'hydration_status':
      const normalizedHydration = value.toLowerCase();
      if (normalizedHydration.includes('dehydrat') && (normalizedHydration.includes('severe') || !normalizedHydration.includes('mild') && !normalizedHydration.includes('slight'))) return 'abnormal';
      if (normalizedHydration === 'dehydrated') return 'abnormal';
      if (normalizedHydration.includes('mild') || normalizedHydration.includes('slight') || normalizedHydration.includes('over')) return 'warning';
      if (normalizedHydration.includes('well') || normalizedHydration.includes('adequat')) return 'normal';
      break;

    case 'social_interaction_levels':
      const normalizedSocial = value.toLowerCase();
      if (normalizedSocial.includes('isolat')) return 'abnormal';
      if (normalizedSocial.includes('limit')) return 'warning';
      if (normalizedSocial.includes('moderate') || normalizedSocial.includes('active') || normalizedSocial.includes('regular')) return 'normal';
      break;

    case 'social_support':
      const normalizedSupport = value.toLowerCase();
      if (normalizedSupport === 'none' || normalizedSupport.includes('no support')) return 'abnormal';
      if (normalizedSupport.includes('limit')) return 'warning';
      if (normalizedSupport.includes('adequat') || normalizedSupport.includes('strong')) return 'normal';
      break;

    case 'living_conditions':
      const normalizedLiving = value.toLowerCase();
      if (normalizedLiving.includes('unsafe') || normalizedLiving.includes('poor')) return 'abnormal';
      if (normalizedLiving.includes('fair')) return 'warning';
      if (normalizedLiving.includes('good') || normalizedLiving.includes('excellent')) return 'normal';
      break;

    case 'environmental_risks':
      const normalizedRisks = value.toLowerCase();
      if (normalizedRisks.includes('severe') || normalizedRisks.includes('significant')) return 'abnormal';
      if (normalizedRisks.includes('moderate')) return 'warning';
      if (normalizedRisks.includes('minimal') || normalizedRisks.includes('none') || normalizedRisks === 'no') return 'normal';
      break;

    case 'transportation_access':
      const normalizedTransport = value.toLowerCase();
      if (normalizedTransport.includes('none') || normalizedTransport.includes('no access') || normalizedTransport.includes('very limited')) return 'abnormal';
      if (normalizedTransport.includes('limited')) return 'warning';
      if (normalizedTransport.includes('good') || normalizedTransport.includes('excellent') ||
          normalizedTransport.includes('independent') || normalizedTransport.includes('public') ||
          normalizedTransport.includes('medical') || normalizedTransport.includes('relies')) return 'normal';
      break;

    case 'financial_concern':
      const normalizedFinancial = value.toLowerCase();
      if (normalizedFinancial.includes('severe') || normalizedFinancial.includes('significant')) return 'abnormal';
      if (normalizedFinancial.includes('moderate')) return 'warning';
      if (normalizedFinancial.includes('minimal') || normalizedFinancial.includes('minor') ||
          normalizedFinancial.includes('mild') || normalizedFinancial.includes('none') ||
          normalizedFinancial === 'no') return 'normal';
      break;

    case 'home_safety_evaluation':
      const normalizedSafety = value.toLowerCase();
      if (normalizedSafety.includes('unsafe') || normalizedSafety.includes('major')) return 'abnormal';
      if (normalizedSafety.includes('moderate')) return 'warning';
      if (normalizedSafety.includes('minor') || normalizedSafety.includes('safe')) return 'normal';
      break;
  }

  return null;
};

// Helper function to determine status indicator
const getStatusIndicator = (field: string, value: string | null | undefined): 'normal' | 'warning' | 'abnormal' | null => {
  if (!value) return null;
  return getStatusByFieldAndValue(field, value);
};

// Status indicator component
const StatusIndicator = ({ status }: { status: 'normal' | 'warning' | 'abnormal' | null | undefined }) => {
  const theme = useTheme();
  if (!status) return null;

  const getStatusColor = () => {
    switch (status) {
      case 'abnormal':
        return theme.palette.error.main;
      case 'warning':
        return theme.palette.warning.main;
      default:
        return theme.palette.success.main;
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'abnormal':
        return <WarningIcon fontSize="small" />;
      case 'warning':
        return <InfoIcon fontSize="small" />;
      default:
        return <CheckCircleIcon fontSize="small" />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'abnormal':
        return 'Needs attention';
      case 'warning':
        return 'Monitor';
      default:
        return 'Normal';
    }
  };

  return (
    <Box sx={{
      display: 'flex',
      alignItems: 'center',
      gap: 0.5,
      color: getStatusColor(),
      fontSize: '0.875rem',
      mt: 0.5
    }}>
      {getStatusIcon()}
      <Typography variant="body2" sx={{ color: getStatusColor() }}>
        {getStatusText()}
      </Typography>
    </Box>
  );
};

// Metric card component for consistent styling
const MetricCard = ({
  title,
  value,
  icon,
  status,
  additionalInfo,
  dataSource = 'patient'
}: {
  title: string;
  value: string | null | undefined;
  icon: React.ReactNode;
  status?: 'normal' | 'warning' | 'abnormal' | null;
  additionalInfo?: string;
  dataSource?: 'visit' | 'patient';
}) => {
  const theme = useTheme();

  return (
    <Paper
      elevation={0}
      sx={{
        p: 2,
        height: '100%',
        display: 'flex',
        alignItems: 'flex-start',
        gap: 2,
        borderRadius: 2,
        bgcolor: theme.palette.background.paper,
        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        '&:hover': {
          boxShadow: `0 4px 8px ${alpha(theme.palette.primary.main, 0.1)}`,
          borderColor: alpha(theme.palette.primary.main, 0.2),
        }
      }}
    >
      <Box
        sx={{
          width: 40,
          height: 40,
          borderRadius: 1,
          bgcolor: alpha(theme.palette.primary.main, 0.1),
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: theme.palette.primary.main
        }}
      >
        {icon}
      </Box>

      <Box sx={{ flex: 1 }}>
        <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1rem' }}>
          {title}
        </Typography>

        <Typography variant="body1" sx={{ mb: 0.5, fontSize: '1.125rem', fontWeight: 500 }}>
          {value !== null && value !== undefined ? value : (dataSource === 'visit' ? 'Not assessed' : 'Not available')}
        </Typography>

        {status && <StatusIndicator status={status} />}

        {additionalInfo && (
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1, fontStyle: 'italic' }}>
            {additionalInfo}
          </Typography>
        )}
      </Box>
    </Paper>
  );
};

const HealthStatusTab: React.FC<HealthStatusTabProps> = ({ patient, recentVisit }) => {
  const theme = useTheme();

  // Determine data source - use recent visit data if available, otherwise use patient data
  const dataSource = recentVisit ? 'visit' : 'patient';
  console.log(`Using data from ${dataSource} table`);

  // Helper function to safely access properties that might not exist in the type
  const getField = (field: string): string | boolean | undefined => {
    let value;
    if (dataSource === 'visit' && recentVisit) {
      value = (recentVisit as any)[field];
    } else {
      value = (patient as any)[field];
    }

    // Special handling for fields to ensure consistent display
    if (value) {
      const normalizedValue = String(value).toLowerCase();

      // Activity Level normalization
      if (field === 'activity_level') {
        if (normalizedValue.includes('sedentary')) return 'Sedentary';
        if (normalizedValue.includes('light')) return 'Light';
        if (normalizedValue.includes('moderate')) return 'Moderate';
        if (normalizedValue.includes('active') && normalizedValue.includes('very')) return 'Very Active';
        if (normalizedValue.includes('active')) return 'Active';
      }

      // Nutritional Status normalization
      if (field === 'nutritional_status') {
        if (normalizedValue.includes('well') && normalizedValue.includes('nourish')) return 'Good';
        if (normalizedValue.includes('risk') || normalizedValue.includes('fair')) return 'Fair';
        if (normalizedValue.includes('malnourish') || normalizedValue.includes('poor')) return 'Poor';
        if (normalizedValue === 'good') return 'Good';
        if (normalizedValue === 'fair') return 'Fair';
        if (normalizedValue === 'poor') return 'Poor';
      }

      // Hydration Status normalization
      if (field === 'hydration_status') {
        if (normalizedValue.includes('well') && normalizedValue.includes('hydrat')) return 'Well Hydrated';
        if (normalizedValue.includes('adequately') || normalizedValue.includes('adequate')) return 'Adequately Hydrated';
        if (normalizedValue.includes('mild') && normalizedValue.includes('dehydrat')) return 'Mildly Dehydrated';
        if (normalizedValue.includes('moderate') && normalizedValue.includes('dehydrat')) return 'Moderately Dehydrated';
        if (normalizedValue.includes('severe') && normalizedValue.includes('dehydrat')) return 'Severely Dehydrated';
        if (normalizedValue === 'dehydrated') return 'Dehydrated';
      }

      // Hearing Status normalization
      if (field === 'hearing_status') {
        if (normalizedValue === 'normal') return 'Normal';
        if (normalizedValue.includes('mild') && normalizedValue.includes('impair')) return 'Mild Impairment';
        if (normalizedValue.includes('moderate') && normalizedValue.includes('impair')) return 'Moderate Impairment';
        if (normalizedValue.includes('severe') && normalizedValue.includes('impair')) return 'Severe Impairment';
        if (normalizedValue.includes('profound') || normalizedValue.includes('deaf')) return 'Profound Loss';
        if (normalizedValue.includes('uses') && normalizedValue.includes('hearing') && normalizedValue.includes('aid')) return 'Moderate Impairment';
      }

      // Social Interaction Levels normalization
      if (field === 'social_interaction_levels') {
        if (normalizedValue.includes('very') && normalizedValue.includes('active')) return 'Very active';
        if (normalizedValue.includes('active') && !normalizedValue.includes('very')) return 'Active';
        if (normalizedValue.includes('moderate')) return 'Moderate';
        if (normalizedValue.includes('regular')) return 'Active';
        if (normalizedValue.includes('limited')) return 'Limited';
        if (normalizedValue.includes('isolated')) return 'Isolated';
      }

      // Transportation Access normalization
      if (field === 'transportation_access') {
        if (normalizedValue.includes('independent')) return 'Independent driver';
        if (normalizedValue.includes('relies') && normalizedValue.includes('family')) return 'Relies on family';
        if (normalizedValue.includes('relies')) return 'Relies on others';
        if (normalizedValue.includes('public')) return 'Public transportation';
        if (normalizedValue.includes('medical')) return 'Medical transportation';
        if (normalizedValue.includes('limited')) return 'Limited access';
        if (normalizedValue.includes('no access') || normalizedValue === 'none') return 'No access';
      }

      // Financial Concern normalization
      if (field === 'financial_concern') {
        if (normalizedValue === 'none' || normalizedValue.includes('minimal')) return 'None';
        if (normalizedValue.includes('minor') || normalizedValue.includes('mild')) return 'Minor';
        if (normalizedValue.includes('moderate')) return 'Moderate';
        if (normalizedValue.includes('significant')) return 'Significant';
        if (normalizedValue.includes('severe')) return 'Severe';
      }
    }

    return value;
  };

  // Create a health status object with all the fields we need
  const healthStatus = {
    // Activity & Nutrition
    activity_level: getField('activity_level') as string,
    nutritional_status: getField('nutritional_status') as string,
    hydration_status: getField('hydration_status') as string,
    supplements: getField('supplements') as string,

    // Sensory Status
    vision_status: getField('vision_status') as string,
    hearing_status: getField('hearing_status') as string,
    use_of_aid_vision: getField('use_of_aid_vision') as string,
    use_of_aid_hearing: getField('use_of_aid_hearing') as string,

    // Social & Environmental Factors
    social_interaction_levels: getField('social_interaction_levels') as string,
    social_support: getField('social_support') as string,
    social_support_network: getField('social_support_network') as string,
    living_situation: getField('living_situation') as string,
    living_conditions: getField('living_conditions') as string,
    environmental_risks: getField('environmental_risks') as string,
    age_friendly_environment: getField('age_friendly_environment'),
    transportation_access: getField('transportation_access') as string,
    financial_concern: getField('financial_concern') as string,
    home_safety_evaluation: getField('home_safety_evaluation') as string,

    // Other fields (kept for compatibility)
    exercise_frequency: getField('exercise_frequency') as string,
    dietary_intake_quality: getField('dietary_intake_quality') as string,
    fall_risk: getField('fall_risk') as string,
    mobility_status: getField('mobility_status') as string,
    assistive_devices_used: getField('assistive_devices_used') as string,
  };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="body1" sx={{ mb: 3 }}>
        This tab displays the patient's overall health status, including activity & nutrition, sensory status, and social & environmental factors.
        The most recent values are shown, prioritizing data from the latest visit when available.
      </Typography>

      {/* Activity & Nutrition Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Activity & Nutrition
      </Typography>
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Activity Level"
            value={healthStatus.activity_level}
            icon={<DirectionsRunIcon />}
            status={getStatusIndicator('activity_level', healthStatus.activity_level)}
            additionalInfo="Regular physical activity is important for maintaining health in older adults."
            dataSource={dataSource}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Nutritional Status"
            value={healthStatus.nutritional_status}
            icon={<RestaurantIcon />}
            status={getStatusIndicator('nutritional_status', healthStatus.nutritional_status)}
            dataSource={dataSource}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Hydration Status"
            value={healthStatus.hydration_status}
            icon={<LocalDrinkIcon />}
            status={getStatusIndicator('hydration_status', healthStatus.hydration_status)}
            dataSource={dataSource}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Supplements"
            value={healthStatus.supplements}
            icon={<VitaminIcon />}
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* Sensory Status Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Sensory Status
      </Typography>
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Vision Status"
            value={healthStatus.vision_status}
            icon={<VisibilityIcon />}
            status={getStatusIndicator('vision_status', healthStatus.vision_status)}
            dataSource={dataSource}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Vision Aid Used"
            value={healthStatus.use_of_aid_vision}
            icon={<VisibilityIcon />}
            dataSource={dataSource}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Hearing Status"
            value={healthStatus.hearing_status}
            icon={<HearingIcon />}
            status={getStatusIndicator('hearing_status', healthStatus.hearing_status)}
            dataSource={dataSource}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Hearing Aid Used"
            value={healthStatus.use_of_aid_hearing}
            icon={<HearingIcon />}
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* Social & Environmental Factors Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Social & Environmental Factors
      </Typography>
      <Grid container spacing={3}>
        {/* Social Interaction & Support */}
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Social Interaction Levels"
            value={healthStatus.social_interaction_levels}
            icon={<PeopleIcon />}
            status={getStatusIndicator('social_interaction_levels', healthStatus.social_interaction_levels)}
            dataSource={dataSource}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Social Support"
            value={healthStatus.social_support}
            icon={<PersonIcon />}
            status={getStatusIndicator('social_support', healthStatus.social_support)}
            dataSource={dataSource}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Social Support Network"
            value={healthStatus.social_support_network}
            icon={<PeopleIcon />}
            dataSource={dataSource}
          />
        </Grid>

        {/* Living Situation & Environment */}
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Living Situation"
            value={healthStatus.living_situation}
            icon={<HomeIcon />}
            dataSource={dataSource}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Living Conditions"
            value={healthStatus.living_conditions}
            icon={<ApartmentIcon />}
            status={getStatusIndicator('living_conditions', healthStatus.living_conditions)}
            dataSource={dataSource}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Environmental Risks"
            value={healthStatus.environmental_risks}
            icon={<WarningIcon />}
            status={getStatusIndicator('environmental_risks', healthStatus.environmental_risks)}
            dataSource={dataSource}
          />
        </Grid>

        {/* Age-Friendly Environment & Access */}
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Age-Friendly Environment"
            value={formatBoolean(healthStatus.age_friendly_environment)}
            icon={<ElderlyIcon />}
            dataSource={dataSource}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Transportation Access"
            value={healthStatus.transportation_access}
            icon={<DirectionsCarIcon />}
            status={getStatusIndicator('transportation_access', healthStatus.transportation_access)}
            dataSource={dataSource}
          />
        </Grid>

        {/* Financial & Safety */}
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Financial Concerns"
            value={healthStatus.financial_concern}
            icon={<AttachMoneyIcon />}
            status={getStatusIndicator('financial_concern', healthStatus.financial_concern)}
            dataSource={dataSource}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Home Safety Evaluation"
            value={healthStatus.home_safety_evaluation}
            icon={<SecurityIcon />}
            status={getStatusIndicator('home_safety_evaluation', healthStatus.home_safety_evaluation)}
            dataSource={dataSource}
          />
        </Grid>
      </Grid>
    </Box>
  );
};

export default HealthStatusTab;
