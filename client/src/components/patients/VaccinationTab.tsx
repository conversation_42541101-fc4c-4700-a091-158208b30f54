import React from 'react';
import { <PERSON>, Grid, Paper, Typography, Button, alpha } from '@mui/material';
import { Link } from 'react-router-dom';
import { useTheme } from '@mui/material/styles';
import { Patient, Visit } from '../../types';

// Icons
import VaccinesIcon from '@mui/icons-material/Vaccines';
import MedicalInformationIcon from '@mui/icons-material/MedicalInformation';
import CoronavirusIcon from '@mui/icons-material/Coronavirus';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';

interface VaccinationTabProps {
  patient: Patient;
  recentVisit: Visit | null;
}

// Status indicator component
const StatusIndicator = ({ status }: { status: 'administered' | 'not-administered' | null | undefined }) => {
  const theme = useTheme();
  if (!status) return null;

  const getStatusColor = () => {
    switch (status) {
      case 'administered':
        return theme.palette.success.main;
      case 'not-administered':
        return theme.palette.error.main;
      default:
        return theme.palette.text.secondary;
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'administered':
        return <CheckCircleIcon fontSize="small" />;
      case 'not-administered':
        return <ErrorIcon fontSize="small" />;
      default:
        return null;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'administered':
        return 'Administered';
      case 'not-administered':
        return 'Not Administered';
      default:
        return '';
    }
  };

  return (
    <Box sx={{
      display: 'flex',
      alignItems: 'center',
      gap: 0.5,
      color: getStatusColor(),
      fontSize: '0.875rem',
      mt: 0.5
    }}>
      {getStatusIcon()}
      <Typography variant="body2" sx={{ color: getStatusColor() }}>
        {getStatusText()}
      </Typography>
    </Box>
  );
};

// Metric card component for consistent styling
const MetricCard = ({
  title,
  date,
  icon,
  additionalInfo,
  dataSource = 'patient'
}: {
  title: string;
  date: string | null | undefined;
  icon: React.ReactNode;
  additionalInfo?: string;
  dataSource?: 'visit' | 'patient';
}) => {
  const theme = useTheme();
  const status = date ? 'administered' : 'not-administered';

  return (
    <Paper
      elevation={0}
      sx={{
        p: 2,
        height: '100%',
        display: 'flex',
        alignItems: 'flex-start',
        gap: 2,
        borderRadius: 2,
        bgcolor: theme.palette.background.paper,
        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        '&:hover': {
          boxShadow: `0 4px 8px ${alpha(theme.palette.primary.main, 0.1)}`,
          borderColor: alpha(theme.palette.primary.main, 0.2),
        }
      }}
    >
      <Box
        sx={{
          width: 40,
          height: 40,
          borderRadius: 1,
          bgcolor: alpha(theme.palette.primary.main, 0.1),
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: theme.palette.primary.main
        }}
      >
        {icon}
      </Box>

      <Box sx={{ flex: 1 }}>
        <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1rem' }}>
          {title}
        </Typography>

        <Typography variant="body1" sx={{ mb: 0.5, fontSize: '1.125rem', fontWeight: 500 }}>
          {date ? new Date(date).toLocaleDateString() : (dataSource === 'visit' ? 'Not assessed' : 'Not available')}
        </Typography>

        <StatusIndicator status={status} />

        {additionalInfo && (
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1, fontStyle: 'italic' }}>
            {additionalInfo}
          </Typography>
        )}
      </Box>
    </Paper>
  );
};

// Vaccination category card component
const VaccinationCategoryCard = ({
  title,
  description,
  icon,
  color = 'primary',
  dataSource = 'patient'
}: {
  title: string;
  description: string;
  icon: React.ReactNode;
  color?: string;
  dataSource?: 'visit' | 'patient';
}) => {
  const theme = useTheme();

  // Get icon color based on color prop
  const getIconColor = () => {
    switch (color) {
      case 'primary':
        return theme.palette.primary.main;
      case 'secondary':
        return theme.palette.secondary.main;
      case 'error':
        return theme.palette.error.main;
      case 'warning':
        return theme.palette.warning.main;
      case 'info':
        return theme.palette.info.main;
      case 'success':
        return theme.palette.success.main;
      default:
        return theme.palette.primary.main;
    }
  };



  return (
    <Paper
      elevation={0}
      sx={{
        height: '100%',
        borderRadius: 2,
        bgcolor: theme.palette.background.paper,
        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        overflow: 'hidden',
        '&:hover': {
          boxShadow: `0 4px 8px ${alpha(theme.palette.primary.main, 0.1)}`,
          borderColor: alpha(theme.palette.primary.main, 0.2),
        }
      }}
    >
      {/* Header */}
      <Box sx={{
        p: 2,
        display: 'flex',
        alignItems: 'center',
        gap: 1.5,
        borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        bgcolor: alpha(theme.palette.background.default, 0.5)
      }}>
        <Box
          sx={{
            width: 40,
            height: 40,
            borderRadius: 1,
            bgcolor: alpha(getIconColor(), 0.1),
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: getIconColor()
          }}
        >
          {icon}
        </Box>
        <Typography variant="h6" sx={{ fontWeight: 600, fontSize: '1rem' }}>
          {title}
        </Typography>
      </Box>

      <Box sx={{ p: 2 }}>
        <Typography variant="body2" sx={{ color: theme.palette.text.secondary }}>
          {description}
        </Typography>
      </Box>
    </Paper>
  );
};

const VaccinationTab: React.FC<VaccinationTabProps> = ({ patient, recentVisit }) => {
  const theme = useTheme();

  // Determine data source - use recent visit data if available, otherwise use patient data
  const dataSource = recentVisit ? 'visit' : 'patient';
  console.log(`Using vaccination data from ${dataSource} table`);

  // Create vaccinations object based on data source
  const vaccinations = dataSource === 'visit'
    ? {
        // Routine Vaccinations
        influenza_vaccination_date: recentVisit?.influenza_vaccination_date,
        pneumococcal_vaccination_date: recentVisit?.pneumococcal_vaccination_date,
        zoster_vaccination_date: recentVisit?.zoster_vaccination_date,
        tdap_vaccination_date: recentVisit?.tdap_vaccination_date,

        // COVID-19 Vaccinations
        covid19_vaccination_date: recentVisit?.covid19_vaccination_date,
        covid19_booster_date: recentVisit?.covid19_booster_date,

        // Other Vaccinations
        hepatitis_a_vaccination_date: recentVisit?.hepatitis_a_vaccination_date,
        hepatitis_b_vaccination_date: recentVisit?.hepatitis_b_vaccination_date,
        mmr_vaccination_date: recentVisit?.mmr_vaccination_date,
        varicella_vaccination_date: recentVisit?.varicella_vaccination_date,
        other_vaccinations: recentVisit?.other_vaccinations
      }
    : {
        // Routine Vaccinations
        influenza_vaccination_date: patient.influenza_vaccination_date,
        pneumococcal_vaccination_date: patient.pneumococcal_vaccination_date,
        zoster_vaccination_date: patient.zoster_vaccination_date,
        tdap_vaccination_date: patient.tdap_vaccination_date,

        // COVID-19 Vaccinations
        covid19_vaccination_date: patient.covid19_vaccination_date,
        covid19_booster_date: patient.covid19_booster_date,

        // Other Vaccinations
        hepatitis_a_vaccination_date: patient.hepatitis_a_vaccination_date,
        hepatitis_b_vaccination_date: patient.hepatitis_b_vaccination_date,
        mmr_vaccination_date: patient.mmr_vaccination_date,
        varicella_vaccination_date: patient.varicella_vaccination_date,
        other_vaccinations: patient.other_vaccinations
      };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="body1" sx={{ mb: 3 }}>
        This tab displays all vaccination records for the patient, organized by category.
        The most recent values are shown, prioritizing data from the latest visit when available.
      </Typography>

      {/* Vaccination Categories Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={4}>
          <VaccinationCategoryCard
            title="Routine Vaccinations"
            description="Standard vaccinations recommended for older adults including influenza, pneumococcal, zoster, and Tdap."
            icon={<VaccinesIcon />}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <VaccinationCategoryCard
            title="COVID-19 Vaccinations"
            description="COVID-19 primary series and booster vaccinations to protect against severe illness."
            icon={<CoronavirusIcon />}
            color="info"
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <VaccinationCategoryCard
            title="Other Vaccinations"
            description="Additional vaccinations that may be recommended based on individual risk factors."
            icon={<MedicalInformationIcon />}
            color="warning"
          />
        </Grid>
      </Grid>

      {/* Routine Vaccinations Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Routine Vaccinations
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Influenza (Flu)"
            date={vaccinations.influenza_vaccination_date}
            icon={<VaccinesIcon />}
            additionalInfo="Annual vaccination recommended for all adults, especially those 65 and older."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Pneumococcal"
            date={vaccinations.pneumococcal_vaccination_date}
            icon={<VaccinesIcon />}
            additionalInfo="Two types of pneumococcal vaccines are recommended for adults 65 and older: PCV13 and PPSV23."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Zoster (Shingles)"
            date={vaccinations.zoster_vaccination_date}
            icon={<VaccinesIcon />}
            additionalInfo="Recommended for adults 50 and older to prevent shingles and related complications."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Tdap"
            date={vaccinations.tdap_vaccination_date}
            icon={<VaccinesIcon />}
            additionalInfo="One dose of Tdap, then a Td or Tdap booster every 10 years."
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* COVID-19 Vaccinations Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        COVID-19 Vaccinations
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6}>
          <MetricCard
            title="Primary Series"
            date={vaccinations.covid19_vaccination_date}
            icon={<CoronavirusIcon />}
            additionalInfo="Initial COVID-19 vaccination series."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <MetricCard
            title="Booster"
            date={vaccinations.covid19_booster_date}
            icon={<CoronavirusIcon />}
            additionalInfo="COVID-19 booster dose for enhanced protection."
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* Other Vaccinations Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Other Vaccinations
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Hepatitis A"
            date={vaccinations.hepatitis_a_vaccination_date}
            icon={<MedicalInformationIcon />}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Hepatitis B"
            date={vaccinations.hepatitis_b_vaccination_date}
            icon={<MedicalInformationIcon />}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="MMR"
            date={vaccinations.mmr_vaccination_date}
            icon={<MedicalInformationIcon />}
            additionalInfo="Measles, Mumps, Rubella"
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Varicella"
            date={vaccinations.varicella_vaccination_date}
            icon={<MedicalInformationIcon />}
            additionalInfo="Chickenpox"
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* Other Vaccinations Notes */}
      {vaccinations.other_vaccinations && (
        <>
          <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
            Additional Vaccination Notes
          </Typography>

          <Paper
            elevation={0}
            sx={{
              p: 3,
              border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
              borderRadius: 2,
              mb: 4,
              boxShadow: `0 4px 8px ${alpha(theme.palette.primary.main, 0.1)}`,
            }}
          >
            <Typography variant="body1">
              {vaccinations.other_vaccinations}
            </Typography>
          </Paper>
        </>
      )}

      {/* Add New Vaccination Button */}
      <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
        <Button
          component={Link}
          to={`/patients/${patient.patient_id}/visits/new`}
          variant="contained"
          color="primary"
          startIcon={<i className="fas fa-plus-circle"></i>}
          sx={{ borderRadius: 2 }}
        >
          Update Vaccination Records
        </Button>
      </Box>
    </Box>
  );
};

export default VaccinationTab;
