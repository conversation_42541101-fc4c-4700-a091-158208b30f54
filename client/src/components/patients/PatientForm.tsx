import React, { useState, useEffect, useContext } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import axios from 'axios';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Container,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  LinearProgress,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  MenuItem,
  Paper,
  Select,
  SelectChangeEvent,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Tooltip,
  Typography,
  Alert,
  AlertTitle,
  styled
} from '@mui/material';
import CollapsibleGuidance from '../common/CollapsibleGuidance';
import {
  Person as PersonIcon,
  Favorite as FavoriteIcon,
  Science as ScienceIcon,
  ArrowBack as BackIcon,
  ArrowForward as NextIcon,
  Save as SaveIcon,
  Psychology as PsychologyIcon,
  Vaccines as VaccinesIcon,
  Phone as PhoneIcon,
  MedicalInformation as MedicalIcon,
  HealthAndSafety as HealthIcon,
  Assessment as AssessmentIcon,
  Bed as BedIcon,
  DirectionsRun as FallsIcon,
  Visibility as VisibilityIcon,
  Restaurant as RestaurantIcon,
  LocalHospital as PainIcon,
  Edit as EditIcon,
  Mood as MoodIcon,
  Medication as MedicationIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  ArrowForward as ArrowForwardIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  OpenInNew as JumpIcon,
  Info as InfoIcon,
  Assignment as AssignmentIcon
} from '@mui/icons-material';
import { API_URL } from '../../config';
import CognitiveHealthAssessment from '../visits/CognitiveHealthAssessment';
import DepressionAssessment from '../visits/DepressionAssessment';
import AnxietyAssessment from '../visits/AnxietyAssessment';
//import MentalHealthAssessment from '../visits/MentalHealthAssessment';
import PSQIAssessment from '../sleep/PSQIAssessment';
import BasicSleepInfo from '../sleep/BasicSleepInfo';
import FrailtyAssessment from '../frailty/FrailtyAssessment';
import FallsRiskAssessment from '../falls/FallsRiskAssessment';
import PrescriptionSection from '../prescriptions/PrescriptionSection';
import BeersCriteriaChecker from '../beers/BeersCriteriaChecker';
import { savePrescriptionsForPatient } from '../../services/patientService';
import AuthContext from '../../context/AuthContext';

interface Prescription {
  id?: number;
  medication: string;
  dosage: string;
  frequency: string;
  duration: string;
  notes?: string;
  beers_criteria_id?: number;
  beers_criteria_name?: string;
  beers_criteria_category?: string;
  beers_criteria_recommendation?: string;
  beers_criteria_rationale?: string;
  beers_override_reason?: string;
  beers_overridden_at?: string;
  beers_overridden_by?: number;
  overridden_by_username?: string;
}

interface PatientFormData {
  // Basic Information
  first_name: string;
  last_name: string;
  unique_id: string;
  date_of_birth: string;
  gender: string;
  blood_type: string;
  phone: string;
  email: string;
  address: string;
  doctor_id: string;
  emergency_contact_name: string;
  emergency_contact_phone: string;
  medical_history: string;
  allergies: string;
  medical_allergies: string;

  // Prescriptions
  prescriptions: Prescription[];

  // Vital Signs
  height: string;
  weight: string;
  bmi: string;
  lying_bp_systolic: string;
  lying_bp_diastolic: string;
  lying_heart_rate: string;
  sitting_bp_systolic: string;
  sitting_bp_diastolic: string;
  sitting_heart_rate: string;
  standing_bp_systolic: string;
  standing_bp_diastolic: string;
  standing_heart_rate: string;
  heart_rhythm: string;
  body_temperature: string;
  respiratory_rate: string;
  pulse_oximetry: string;
  temperature: string;

  // Lab Results
  blood_glucose: string;
  hba1c: string;
  cholesterol_total: string;
  hdl_cholesterol: string;
  ldl_cholesterol: string;
  vldl: string;
  triglycerides: string;
  creatinine: string;
  egfr: string;
  blood_urea_nitrogen: string;
  uric_acid: string;
  urine_albumin_creatinine_ratio: string;
  cancer_screening_results: string;

  // Urinalysis
  urine_color: string;
  urine_transparency: string;
  urine_ph: string;
  urine_sugar: string;
  urine_pus_cells: string;
  urine_rbcs: string;
  urine_epithelial_cells: string;
  urine_crystals: string;
  urine_casts: string;
  urine_protein: string;

  // Electrolytes
  sodium: string;
  potassium: string;
  calcium: string;
  magnesium: string;
  phosphorus: string;
  chloride: string;

  // Liver Function
  alt: string;
  ast: string;
  alp: string;
  ggt: string;
  bilirubin_t: string; // Total bilirubin
  bilirubin_d: string; // Direct bilirubin
  bilirubin_total: string; // Legacy field - will be replaced by bilirubin_t
  albumin: string;
  sgpt: string; // Same as ALT but different name
  sgot: string; // Same as AST but different name
  alkaline_phosphatase: string; // Same as ALP but different name
  total_protein: string;

  // Bone Health
  vitamin_d: string;
  parathyroid_hormone: string;
  alkaline_phosphatase_bone: string;

  // Thyroid Function
  tsh: string;
  t4: string;
  t3: string;

  // Inflammation & Anemia
  crp: string;
  esr: string;
  hemoglobin: string;
  hematocrit: string;
  ferritin: string;
  iron: string;
  vitamin_b12: string;
  folate: string;

  // Complete Blood Count
  wbc: string;
  rbc: string;
  platelets: string;
  mcv: string;
  mch: string;
  mchc: string;
  rdw: string;
  neutrophils: string;
  lymphocytes: string;
  monocytes: string;
  eosinophils: string;
  basophils: string;

  // Miscellaneous Screening
  psa: string;
  ca125: string;

  // Medication
  current_medications: string;
  medication_adherence: string;
  medication_side_effects: string;
  medication_allergies: string;
  pill_burden: string;
  medication_changes: string;

  // Cognitive & Mental Health
  cognitive_status: string;
  cognitive_impairment_score: string;
  mental_health_assessment: string;
  depression_screening: string;
  depression_score: string;
  anxiety_screening: string;
  anxiety_score: string;

  // Mini-Cog Assessment
  mini_cog_word_recall_score: string;
  mini_cog_clock_drawing_score: string;
  mini_cog_words_used: string;
  mini_cog_words_recalled: string;
  mini_cog_notes: string;

  // PHQ-9 Depression Assessment
  phq9_interest_pleasure: string;
  phq9_feeling_down: string;
  phq9_sleep_issues: string;
  phq9_tired: string;
  phq9_appetite: string;
  phq9_feeling_bad: string;
  phq9_concentration: string;
  phq9_moving_speaking: string;
  phq9_thoughts_hurting: string;
  phq9_difficulty_level: string;
  phq9_notes: string;

  // GAD-7 Anxiety Assessment
  gad7_feeling_nervous: string;
  gad7_stop_worrying: string;
  gad7_worrying_much: string;
  gad7_trouble_relaxing: string;
  gad7_restless: string;
  gad7_annoyed: string;
  gad7_feeling_afraid: string;
  gad7_difficulty_level: string;
  gad7_notes: string;

  // Pain Management
  pain_level: string;
  pain_location: string;
  pain_character: string;
  safe_pain_medications: string;

  // Physical Activity & Lifestyle
  activity_level: string;
  daily_activity_levels: string;
  exercise_frequency: string;
  fall_detection_incidents: string;
  fall_risk_assessment: string;
  fall_risk: string;
  mobility_status: string;
  mobility_aids_used: string;
  assistive_devices_used: string;
  calf_circumference: string;
  grip_strength: string;

  // Nutrition & Hydration
  nutritional_status: string;
  nutrition_diet: string;
  dietary_intake_quality: string;
  hydration_status: string;
  hydration_levels: string;
  vitamin_mineral_levels: string;
  supplements: string;

  // Sleep
  sleep_patterns: string;
  sleep_initiation_difficulties: string;
  sleep_quality_duration: string;
  sleep_duration: string;
  sleep_disturbances: string;
  sleep_quality: string;

  // Pittsburgh Sleep Quality Index (PSQI)
  psqi_subjective_sleep_quality: string;
  psqi_sleep_latency: string;
  psqi_sleep_duration: string;
  psqi_sleep_efficiency: string;
  psqi_sleep_disturbances: string;
  psqi_sleep_medication: string;
  psqi_daytime_dysfunction: string;
  psqi_total_score: string;
  psqi_assessment_date: string;
  psqi_bedtime: string;
  psqi_minutes_to_fall_asleep: string;
  psqi_wake_up_time: string;
  psqi_hours_of_sleep: string;
  psqi_notes: string;

  // Falls Risk Assessment Tool (FRAT)
  frat_fall_history: string;
  frat_fall_history_score: string;
  frat_medications: string;
  frat_medications_score: string;
  frat_psychological: string;
  frat_psychological_score: string;
  frat_cognitive: string;
  frat_cognitive_score: string;
  frat_total_score: string;
  frat_risk_level: string;
  frat_notes: string;

  // Sensory
  vision_status: string;
  hearing_status: string;
  use_of_aid_vision: string;
  use_of_aid_hearing: string;

  // Social & Environmental
  social_interaction_levels: string;
  social_support: string;
  social_support_network: string;
  living_conditions: string;
  living_situation: string;
  environmental_risks: string;
  age_friendly_environment: string;
  transportation_access: string;
  financial_concern: string;
  home_safety_evaluation: string;

  // Safety & Emergency
  emergency_contact_relationship: string;
  emergency_contact_updated: string;
  sos_alerts: string;

  // Preventive Care & Vaccinations
  health_checkup_adherence: string;
  vaccination_updated: string;
  influenza_vaccination_date: string;
  pneumococcal_vaccination_date: string;
  zoster_vaccination_date: string;
  tdap_vaccination_date: string;
  covid19_vaccination_date: string;
  covid19_booster_date: string;
  hepatitis_a_vaccination_date: string;
  hepatitis_b_vaccination_date: string;
  mmr_vaccination_date: string;
  varicella_vaccination_date: string;
  other_vaccinations: string;

  // Additional Health Concerns
  urinary_bowel_issues: string;
  substance_abuse: string;

  // Visit & Treatment
  diagnosis: string;
  treatment_plan: string;
  follow_up_instructions: string;
  referrals: string;
  notes: string;
}

interface Doctor {
  doctor_id: number;
  first_name: string;
  last_name: string;
  specialty: string;
}

// Styled components
const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  marginBottom: theme.spacing(3),
  borderRadius: theme.shape.borderRadius * 2,
  boxShadow: '0 6px 20px rgba(0, 0, 0, 0.05)',
  overflow: 'hidden',
  border: '1px solid rgba(0, 0, 0, 0.04)'
}));

const FormSection = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(4)
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  color: theme.palette.primary.main,
  fontWeight: 600,
  '& svg': {
    fontSize: '1.2rem'
  }
}));



// Enhanced Card styling
const EnhancedCard = styled(Card)(({ theme }) => ({
  borderRadius: theme.shape.borderRadius * 1.5,
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
  transition: 'transform 0.2s ease, box-shadow 0.2s ease',
  overflow: 'hidden',
  border: '1px solid rgba(0, 0, 0, 0.04)',
  marginBottom: theme.spacing(3),
  '&:hover': {
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
    transform: 'translateY(-2px)'
  }
}));

// Enhanced CardContent with better spacing
const EnhancedCardContent = styled(CardContent)(({ theme }) => ({
  padding: theme.spacing(3),
  '&:last-child': {
    paddingBottom: theme.spacing(3)
  }
}));

// Card title styling
const CardTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.125rem',
  fontWeight: 600,
  color: theme.palette.primary.main,
  marginBottom: theme.spacing(1.5),
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  '& svg': {
    fontSize: '1.25rem'
  }
}));



// Normal range indicator component
interface NormalRangeProps {
  value: string;
  min: number;
  max: number;
  unit: string;
}

const NormalRangeIndicator = ({ value, min, max, unit }: NormalRangeProps) => {
  const numValue = parseFloat(value);

  if (isNaN(numValue)) return null;

  let color = 'success.main';
  let message = 'Normal';

  if (numValue < min) {
    color = 'warning.main';
    message = 'Low';
  } else if (numValue > max) {
    color = 'error.main';
    message = 'High';
  }

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
      <Box
        component="span"
        sx={{
          width: 8,
          height: 8,
          borderRadius: '50%',
          bgcolor: color,
          mr: 1
        }}
      />
      <Typography variant="caption" color={color}>
        {message} (Normal range: {min}-{max} {unit})
      </Typography>
    </Box>
  );
};

// Custom tab panel component
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`patient-edit-tabpanel-${index}`}
      aria-labelledby={`patient-edit-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};



const PatientForm: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const isEditMode = !!id;

  // State for the current step
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(isEditMode);

  // State for form data
  const [formData, setFormData] = useState<PatientFormData>({
    // Basic Information
    first_name: '',
    last_name: '',
    unique_id: 'Auto-generated',
    date_of_birth: '',
    gender: '',
    blood_type: '',
    phone: '',
    email: '',
    address: '',
    doctor_id: '',
    emergency_contact_name: '',
    emergency_contact_phone: '',
    medical_history: '',
    allergies: '',
    medical_allergies: '',

    // Prescriptions
    prescriptions: [],

    // Vital Signs
    height: '',
    weight: '',
    bmi: '',
    lying_bp_systolic: '',
    lying_bp_diastolic: '',
    lying_heart_rate: '',
    sitting_bp_systolic: '',
    sitting_bp_diastolic: '',
    sitting_heart_rate: '',
    standing_bp_systolic: '',
    standing_bp_diastolic: '',
    standing_heart_rate: '',
    heart_rhythm: '',
    body_temperature: '',
    respiratory_rate: '',
    pulse_oximetry: '',
    temperature: '',

    // Lab Results
    blood_glucose: '',
    hba1c: '',
    cholesterol_total: '',
    hdl_cholesterol: '',
    ldl_cholesterol: '',
    vldl: '',
    triglycerides: '',
    creatinine: '',
    egfr: '',
    blood_urea_nitrogen: '',
    uric_acid: '',
    urine_albumin_creatinine_ratio: '',
    cancer_screening_results: '',

    // Urinalysis
    urine_color: '',
    urine_transparency: '',
    urine_ph: '',
    urine_sugar: '',
    urine_pus_cells: '',
    urine_rbcs: '',
    urine_epithelial_cells: '',
    urine_crystals: '',
    urine_casts: '',
    urine_protein: '',

    // Electrolytes
    sodium: '',
    potassium: '',
    calcium: '',
    magnesium: '',
    phosphorus: '',
    chloride: '',

    // Liver Function
    alt: '',
    ast: '',
    alp: '',
    ggt: '',
    bilirubin_t: '',
    bilirubin_d: '',
    bilirubin_total: '',
    albumin: '',
    sgpt: '',
    sgot: '',
    alkaline_phosphatase: '',
    total_protein: '',

    // Bone Health
    vitamin_d: '',
    parathyroid_hormone: '',
    alkaline_phosphatase_bone: '',

    // Thyroid Function
    tsh: '',
    t4: '',
    t3: '',

    // Inflammation & Anemia
    crp: '',
    esr: '',
    hemoglobin: '',
    hematocrit: '',
    ferritin: '',
    iron: '',
    vitamin_b12: '',
    folate: '',

    // Complete Blood Count
    wbc: '',
    rbc: '',
    platelets: '',
    mcv: '',
    mch: '',
    mchc: '',
    rdw: '',
    neutrophils: '',
    lymphocytes: '',
    monocytes: '',
    eosinophils: '',
    basophils: '',

    // Miscellaneous Screening
    psa: '',
    ca125: '',

    // Medication
    current_medications: '',
    medication_adherence: '',
    medication_side_effects: '',
    medication_allergies: '',
    pill_burden: '',
    medication_changes: '',

    // Cognitive & Mental Health
    cognitive_status: '',
    cognitive_impairment_score: '',
    mental_health_assessment: '',
    depression_screening: '',
    depression_score: '',
    anxiety_screening: '',
    anxiety_score: '',

    // Mini-Cog Assessment
    mini_cog_word_recall_score: '',
    mini_cog_clock_drawing_score: '',
    mini_cog_words_used: '',
    mini_cog_words_recalled: '',
    mini_cog_notes: '',

    // PHQ-9 Depression Assessment
    phq9_interest_pleasure: '',
    phq9_feeling_down: '',
    phq9_sleep_issues: '',
    phq9_tired: '',
    phq9_appetite: '',
    phq9_feeling_bad: '',
    phq9_concentration: '',
    phq9_moving_speaking: '',
    phq9_thoughts_hurting: '',
    phq9_difficulty_level: '',
    phq9_notes: '',

    // GAD-7 Anxiety Assessment
    gad7_feeling_nervous: '',
    gad7_stop_worrying: '',
    gad7_worrying_much: '',
    gad7_trouble_relaxing: '',
    gad7_restless: '',
    gad7_annoyed: '',
    gad7_feeling_afraid: '',
    gad7_difficulty_level: '',
    gad7_notes: '',

    // Pain Management
    pain_level: '',
    pain_location: '',
    pain_character: '',
    safe_pain_medications: '',

    // Physical Activity & Lifestyle
    activity_level: '',
    daily_activity_levels: '',
    exercise_frequency: '',
    fall_detection_incidents: '',
    fall_risk_assessment: '',
    fall_risk: '',
    mobility_status: '',
    mobility_aids_used: '',
    assistive_devices_used: '',
    calf_circumference: '',
    grip_strength: '',

    // Nutrition & Hydration
    nutritional_status: '',
    nutrition_diet: '',
    dietary_intake_quality: '',
    hydration_status: '',
    hydration_levels: '',
    vitamin_mineral_levels: '',
    supplements: '',

    // Sleep
    sleep_patterns: '',
    sleep_initiation_difficulties: '',
    sleep_quality_duration: '',
    sleep_duration: '',
    sleep_disturbances: '',
    sleep_quality: '',

    // Pittsburgh Sleep Quality Index (PSQI)
    psqi_subjective_sleep_quality: '',
    psqi_sleep_latency: '',
    psqi_sleep_duration: '',
    psqi_sleep_efficiency: '',
    psqi_sleep_disturbances: '',
    psqi_sleep_medication: '',
    psqi_daytime_dysfunction: '',
    psqi_total_score: '',
    psqi_assessment_date: '',
    psqi_bedtime: '',
    psqi_minutes_to_fall_asleep: '',
    psqi_wake_up_time: '',
    psqi_hours_of_sleep: '',
    psqi_notes: '',

    // Falls Risk Assessment Tool (FRAT)
    frat_fall_history: '',
    frat_fall_history_score: '',
    frat_medications: '',
    frat_medications_score: '',
    frat_psychological: '',
    frat_psychological_score: '',
    frat_cognitive: '',
    frat_cognitive_score: '',
    frat_total_score: '',
    frat_risk_level: '',
    frat_notes: '',

    // Sensory
    vision_status: '',
    hearing_status: '',
    use_of_aid_vision: '',
    use_of_aid_hearing: '',

    // Social & Environmental
    social_interaction_levels: '',
    social_support: '',
    social_support_network: '',
    living_conditions: '',
    living_situation: '',
    environmental_risks: '',
    age_friendly_environment: '',
    transportation_access: '',
    financial_concern: '',
    home_safety_evaluation: '',

    // Safety & Emergency
    emergency_contact_relationship: '',
    emergency_contact_updated: '',
    sos_alerts: '',

    // Preventive Care & Vaccinations
    health_checkup_adherence: '',
    vaccination_updated: '',
    influenza_vaccination_date: '',
    pneumococcal_vaccination_date: '',
    zoster_vaccination_date: '',
    tdap_vaccination_date: '',
    covid19_vaccination_date: '',
    covid19_booster_date: '',
    hepatitis_a_vaccination_date: '',
    hepatitis_b_vaccination_date: '',
    mmr_vaccination_date: '',
    varicella_vaccination_date: '',
    other_vaccinations: '',

    // Additional Health Concerns
    urinary_bowel_issues: '',
    substance_abuse: '',

    // Visit & Treatment
    diagnosis: '',
    treatment_plan: '',
    follow_up_instructions: '',
    referrals: '',
    notes: '',
  });

  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [loadingDoctors, setLoadingDoctors] = useState(false);
  const [originalData, setOriginalData] = useState<PatientFormData | null>(null);

  // State for expanded sections in review tab
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    basicInfo: true,
    contactInfo: true,
    emergencyContact: true,
    vitalSigns: true,
    medicalInfo: true,
    mentalHealth: true,
    assessments: true,
    diagnosis: true,
    labResults: true,
    healthStatus: true,
    vaccinations: true,
    preSubmissionChecklist: true
  });

  // State for expanded detail sections in review tab
  const [expandedDetailSections, setExpandedDetailSections] = useState<Record<string, boolean>>({});

  // State for visualization type in review tab
  const [visualizationType, setVisualizationType] = useState<'wheel' | 'heatmap'>('wheel');

  // Helper function to delete prescriptions that match BEERS criteria but don't have an override
  const deleteUnresolvedBeersPrescriptions = (alerts: any[]) => {
    if (!formData.prescriptions) return;

    // Get the medications that match BEERS criteria but don't have an override
    const medicationsToDelete = alerts.filter(alert => {
      const prescription = formData.prescriptions?.find(
        p => p.medication.toLowerCase() === alert.medication.toLowerCase()
      );
      return prescription && !prescription.beers_override_reason;
    }).map(alert => alert.medication.toLowerCase());

    // Filter out prescriptions that match BEERS criteria but don't have an override
    const updatedPrescriptions = formData.prescriptions.filter(prescription => {
      return !medicationsToDelete.includes(prescription.medication.toLowerCase());
    });

    // Update the form data with the filtered prescriptions
    setFormData(prev => ({
      ...prev,
      prescriptions: updatedPrescriptions
    }));

    // Show a message about the deleted prescriptions
    if (medicationsToDelete.length > 0) {
      alert(`The following medications were removed because they match BEERS criteria and don't have an override reason: ${medicationsToDelete.join(', ')}`);
    }
  };

  // Toggle section expansion in review tab
  const toggleSectionExpansion = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Toggle detail section expansion in review tab
  const toggleDetailSectionExpansion = (sectionId: string) => {
    setExpandedDetailSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  // Validate data for a specific section
  const validateSectionData = (sectionName: string): string[] => {
    const errors: string[] = [];
    const allErrors = validatePatientData();

    // Map section names to related field prefixes
    const sectionFieldMap: Record<string, string[]> = {
      'Basic Information': ['first_name', 'last_name', 'date_of_birth', 'gender', 'blood_type'],
      'Medical Assignment': ['doctor_id'],
      'Contact Details': ['phone', 'email', 'address'],
      'Emergency Contact': ['emergency_contact'],
      'Medical Information': ['allergies', 'medical_history'],
      'Vital Signs': ['height', 'weight', 'temperature', 'bp', 'heart_rate'],
      'Lab Results': ['blood_glucose', 'cholesterol'],
      'Cognitive Health': ['mini_cog'],
      'Depression': ['phq9'],
      'Anxiety': ['gad7'],
      'Sleep Assessment': ['sleep'],
      'Pain Assessment': ['pain'],
      'Frailty Assessment': ['frailty', 'grip', 'calf'],
      'Vaccinations': ['vaccination'],
      'Health Status': [
        // Sensory Status
        'vision_status', 'hearing_status', 'use_of_aid_vision', 'use_of_aid_hearing',

        // Activity & Nutrition
        'activity_level', 'nutritional_status', 'hydration_status', 'supplements', 'exercise_frequency', 'dietary_intake_quality',

        // Social & Environmental
        'social_interaction_levels', 'social_support', 'living_situation', 'living_conditions',
        'environmental_risks', 'transportation_access', 'financial_concern'
      ],
      'Diagnosis & Prescriptions': ['diagnosis', 'prescription']
    };

    // Get field prefixes for this section
    const fieldPrefixes = sectionFieldMap[sectionName] || [];

    // Filter errors related to this section
    return allErrors.filter(error => {
      return fieldPrefixes.some(prefix =>
        error.toLowerCase().includes(prefix.toLowerCase())
      );
    });
  };

  // Navigate to a specific field in a specific tab
  const navigateToField = (tabIndex: number, fieldId?: string) => {
    setActiveStep(tabIndex);

    // If a specific field ID is provided, scroll to it after the tab renders
    if (fieldId) {
      setTimeout(() => {
        const element = document.getElementById(fieldId);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' });
          element.focus();
          // Add a temporary highlight effect
          element.style.transition = 'background-color 0.5s ease';
          element.style.backgroundColor = '#fff9c4'; // Light yellow highlight
          setTimeout(() => {
            element.style.backgroundColor = '';
          }, 2000);
        }
      }, 300);
    }
  };

  // Smart validation suggestion function
  const getSmartValidationSuggestion = (error: string, fieldName: string): string | null => {
    // Email format suggestions
    if (error.includes('Email address format is invalid')) {
      return "Make sure your email follows the format '<EMAIL>'";
    }

    // Height suggestions
    if (error.includes('Height value seems unusually high')) {
      return "Please verify the height is in centimeters, not inches. For example, 175 cm (5'9\") is a typical adult height.";
    }

    // Weight suggestions
    if (error.includes('Weight value seems unusually high')) {
      return "Please verify the weight is in kilograms, not pounds. For example, 70 kg (154 lbs) is a typical adult weight.";
    }

    // Temperature suggestions
    if (error.includes('Temperature is outside normal human range')) {
      return "Normal body temperature is around 37°C (98.6°F). Please verify the temperature is in Celsius.";
    }

    // Blood pressure suggestions
    if (error.includes('Systolic blood pressure value seems unusually high')) {
      return "Normal systolic blood pressure is typically between 90-140 mmHg. Please verify the reading.";
    }

    if (error.includes('Diastolic blood pressure value seems unusually high')) {
      return "Normal diastolic blood pressure is typically between 60-90 mmHg. Please verify the reading.";
    }

    // Heart rate suggestions
    if (error.includes('Heart rate is outside normal human range')) {
      return "Normal resting heart rate for adults is typically between 60-100 beats per minute.";
    }

    // Required field suggestions
    if (error.includes('is required')) {
      if (fieldName === 'first_name' || fieldName === 'last_name') {
        return "Patient's full name is needed for identification purposes.";
      }
      if (fieldName === 'date_of_birth') {
        return "Date of birth is essential for age calculation and medical assessments.";
      }
      if (fieldName === 'gender') {
        return "Gender information helps with appropriate medical assessments and treatments.";
      }
      if (fieldName === 'phone') {
        return "A contact phone number is needed to reach the patient for appointments and follow-ups.";
      }
      if (fieldName === 'blood_type') {
        return "Blood type is important for medical procedures and emergencies.";
      }
    }

    // Emergency contact suggestions
    if (error.includes('Both emergency contact name and phone must be provided')) {
      return "If you provide emergency contact information, both name and phone number are required.";
    }

    // Blood pressure validation
    if (error.includes('Both systolic and diastolic blood pressure values must be provided')) {
      return "Blood pressure readings require both systolic (upper) and diastolic (lower) values.";
    }

    // No specific suggestion available
    return null;
  };

  // Handle step navigation
  const handleNext = () => {
    // If we're on the Prescriptions tab (index 12), check for unresolved BEERS criteria alerts
    if (activeStep === 12 && formData.prescriptions && formData.prescriptions.length > 0 && formData.date_of_birth) {
      // Check if patient is 65 or older
      const patientAge = calculateAge(formData.date_of_birth);
      if (patientAge >= 65) {
        // Check if there are any prescriptions with BEERS criteria alerts but no override
        const { checkMedications } = require('../../services/beersCriteriaService');

        // Get all medications
        const medications = formData.prescriptions.map(p => p.medication);

        // Check for BEERS criteria alerts
        checkMedications(medications, parseInt(id || '0'))
          .then((alerts: any[]) => {
            if (alerts.length > 0) {
              // Find prescriptions that have BEERS alerts but no override
              const unresolvedAlerts = alerts.filter((alert: any) => {
                const prescription = formData.prescriptions?.find(
                  p => p.medication.toLowerCase() === alert.medication.toLowerCase()
                );
                return prescription && !prescription.beers_override_reason;
              });

              if (unresolvedAlerts.length > 0) {
                // Show warning about unresolved BEERS alerts
                const medicationNames = unresolvedAlerts.map((alert: any) => alert.medication).join(", ");
                // Show alert about the unresolved BEERS criteria
                const confirmDelete = window.confirm(
                  `The following medications have BEERS criteria alerts but no override reasons: ${medicationNames}. \n\n` +
                  `You must either provide an override reason or delete these medications before proceeding. \n\n` +
                  `Click OK to automatically delete these medications, or Cancel to go back and provide override reasons.`
                );

                if (confirmDelete) {
                  // User chose to delete the medications
                  deleteUnresolvedBeersPrescriptions(alerts);
                  // Now proceed to next step
                  setActiveStep((prevStep) => Math.min(prevStep + 1, steps.length - 1));
                } else {
                  // User chose to go back and provide override reasons
                  return;
                }
              } else {
                // No unresolved alerts, proceed to next step
                setActiveStep((prevStep) => Math.min(prevStep + 1, steps.length - 1));
              }
            } else {
              // No alerts, proceed to next step
              setActiveStep((prevStep) => Math.min(prevStep + 1, steps.length - 1));
            }
          })
          .catch((error: any) => {
            console.error('Error checking BEERS criteria:', error);
            // Proceed to next step even if check fails
            setActiveStep((prevStep) => Math.min(prevStep + 1, steps.length - 1));
          });
      } else {
        // Patient is under 65, no need to check BEERS criteria
        setActiveStep((prevStep) => Math.min(prevStep + 1, steps.length - 1));
      }
    } else {
      // Not on prescriptions tab or no prescriptions, proceed to next step
      setActiveStep((prevStep) => Math.min(prevStep + 1, steps.length - 1));
    }
  };

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1);
  };

  const handleStepChange = (step: number) => {
    setActiveStep(step);
  };

  // Fetch patient data when in edit mode
  useEffect(() => {
    const fetchPatientData = async () => {
      if (isEditMode && id) {
        setLoading(true);
        try {
          // Fetch patient data
          const res = await axios.get(`${API_URL}/api/patients/${id}`);

          // Fetch prescriptions separately
          const prescriptionsRes = await axios.get(`${API_URL}/api/prescriptions/patient/${id}`);
          const prescriptions = prescriptionsRes.data || [];

          console.log('Fetched prescriptions:', prescriptions);

          // Convert all values to strings for the form
          const patientData = Object.keys(res.data).reduce<PatientFormData>((acc, key) => {
            // Only process keys that exist in our form data type
            if (key in formData) {
              // Skip null or undefined values
              if (res.data[key] === null || res.data[key] === undefined) {
                // Use type assertion to handle the specific key
                if (key !== 'prescriptions') {
                  (acc as any)[key] = '';
                }
              } else {
                // Special handling for boolean fields that use yes/no in the form
                if (key === 'emergency_contact_updated') {
                  // Convert boolean to yes/no string
                  (acc as any)[key] = res.data[key] === true ? 'yes' : 'no';
                } else if (key !== 'prescriptions') {
                  // Convert all other values to strings (except prescriptions)
                  (acc as any)[key] = res.data[key].toString();
                }
              }
            }
            return acc;
          }, { ...formData });

          // Add prescriptions to the form data
          patientData.prescriptions = prescriptions.map((p: any) => ({
            id: p.prescription_id,
            medication: p.medication,
            dosage: p.dosage,
            frequency: p.frequency,
            duration: p.duration,
            notes: p.notes || '',
            beers_criteria_id: p.beers_criteria_id,
            beers_criteria_name: p.beers_criteria_name,
            beers_criteria_category: p.beers_criteria_category,
            beers_criteria_recommendation: p.beers_criteria_recommendation,
            beers_criteria_rationale: p.beers_criteria_rationale,
            beers_override_reason: p.beers_override_reason,
            beers_overridden_at: p.beers_overridden_at,
            beers_overridden_by: p.beers_overridden_by,
            overridden_by_username: p.overridden_by_username
          }));

          // Special handling for financial_concern
          if ('financial_concern' in res.data) {
            patientData.financial_concern = res.data.financial_concern ? res.data.financial_concern.toString() : '';
          }

          // Special handling for home_safety_evaluation
          if ('home_safety_evaluation' in res.data) {
            patientData.home_safety_evaluation = res.data.home_safety_evaluation ? res.data.home_safety_evaluation.toString() : '';
          }

          // Store the original data for comparison in the review section
          setOriginalData({ ...patientData });

          // Set the form data
          setFormData(patientData);
          setError(null);
        } catch (err) {
          console.error('Error fetching patient data:', err);
          setError('Failed to load patient data. Please try again.');
        } finally {
          setLoading(false);
        }
      }
    };

    fetchPatientData();
  }, [id, isEditMode]); // Removed formData from dependencies

  // Fetch doctors when component mounts
  useEffect(() => {
    const fetchDoctors = async () => {
      setLoadingDoctors(true);
      try {
        const res = await axios.get(`${API_URL}/api/doctors`);
        setDoctors(res.data);
      } catch (err) {
        console.error('Error fetching doctors:', err);
      } finally {
        setLoadingDoctors(false);
      }
    };

    fetchDoctors();
  }, []);

  // Calculate BMI
  const calculateBMI = (weight: string, height: string): string => {
    if (!weight || !height) return '';

    const weightKg = parseFloat(weight);
    const heightCm = parseFloat(height);

    if (isNaN(weightKg) || isNaN(heightCm) || heightCm === 0) return '';

    const heightM = heightCm / 100;
    const bmi = weightKg / (heightM * heightM);

    return bmi.toFixed(1);
  };

  // Calculate PSQI (Pittsburgh Sleep Quality Index) score
  const calculatePSQI = (
    subjective_sleep_quality: string,
    sleep_latency: string,
    sleep_duration: string,
    sleep_efficiency: string,
    sleep_disturbances: string,
    sleep_medication: string,
    daytime_dysfunction: string
  ): string => {
    // Convert all inputs to numbers
    const c1 = parseInt(subjective_sleep_quality) || 0;
    const c2 = parseInt(sleep_latency) || 0;
    const c3 = parseInt(sleep_duration) || 0;
    const c4 = parseInt(sleep_efficiency) || 0;
    const c5 = parseInt(sleep_disturbances) || 0;
    const c6 = parseInt(sleep_medication) || 0;
    const c7 = parseInt(daytime_dysfunction) || 0;

    // Calculate total PSQI score (sum of 7 component scores)
    const totalScore = c1 + c2 + c3 + c4 + c5 + c6 + c7;

    return totalScore.toString();
  };

  // Calculate sleep efficiency for PSQI
  const calculateSleepEfficiency = (
    hoursOfSleep: string,
    bedtime: string,
    wakeUpTime: string
  ): string => {
    if (!hoursOfSleep || !bedtime || !wakeUpTime) return '';

    try {
      // Parse hours of sleep
      const actualSleepHours = parseFloat(hoursOfSleep);

      // Parse bedtime and wake-up time
      const [bedHours, bedMinutes] = bedtime.split(':').map(Number);
      const [wakeHours, wakeMinutes] = wakeUpTime.split(':').map(Number);

      // Calculate total time in bed (in hours)
      let timeInBed = 0;

      // Convert to minutes first
      const bedTimeMinutes = bedHours * 60 + bedMinutes;
      const wakeTimeMinutes = wakeHours * 60 + wakeMinutes;

      // Calculate time in bed (handling overnight sleep)
      if (wakeTimeMinutes >= bedTimeMinutes) {
        timeInBed = (wakeTimeMinutes - bedTimeMinutes) / 60;
      } else {
        // If wake time is earlier than bedtime, assume overnight sleep
        timeInBed = (wakeTimeMinutes + 24 * 60 - bedTimeMinutes) / 60;
      }

      // Calculate sleep efficiency (actual sleep hours / time in bed) * 100%
      const sleepEfficiency = (actualSleepHours / timeInBed) * 100;

      // Convert sleep efficiency to PSQI component score (0-3)
      let sleepEfficiencyScore = 0;
      if (sleepEfficiency >= 85) {
        sleepEfficiencyScore = 0; // Very good
      } else if (sleepEfficiency >= 75) {
        sleepEfficiencyScore = 1; // Fairly good
      } else if (sleepEfficiency >= 65) {
        sleepEfficiencyScore = 2; // Fairly bad
      } else {
        sleepEfficiencyScore = 3; // Very bad
      }

      return sleepEfficiencyScore.toString();
    } catch (error) {
      return '';
    }
  };

  // Calculate sleep duration component score for PSQI
  const calculateSleepDurationScore = (hoursOfSleep: string): string => {
    if (!hoursOfSleep) return '';

    const hours = parseFloat(hoursOfSleep);
    if (isNaN(hours)) return '';

    // PSQI scoring for sleep duration
    if (hours > 7) return '0'; // Very good
    if (hours >= 6) return '1'; // Fairly good
    if (hours >= 5) return '2'; // Fairly bad
    return '3'; // Very bad
  };

  // Calculate sleep latency component score for PSQI
  const calculateSleepLatencyScore = (minutesToFallAsleep: string): string => {
    if (!minutesToFallAsleep) return '';

    const minutes = parseInt(minutesToFallAsleep);
    if (isNaN(minutes)) return '';

    // PSQI scoring for sleep latency
    if (minutes <= 15) return '0'; // Very good
    if (minutes <= 30) return '1'; // Fairly good
    if (minutes <= 60) return '2'; // Fairly bad
    return '3'; // Very bad
  };



  // Determine FRAT risk level based on total score
  const determineFRATRiskLevel = (totalScore: string): string => {
    if (!totalScore) return '';

    const score = parseInt(totalScore);
    if (isNaN(score)) return '';

    if (score >= 5 && score <= 11) return 'Low Risk';
    if (score >= 12 && score <= 15) return 'Medium Risk';
    if (score >= 16) return 'High Risk';
    return 'Minimal Risk';
  };

  // Calculate orthostatic hypotension
  const calculateOrthostaticHypotension = (
    lyingSystolic: string,
    standingSystolic: string,
    lyingDiastolic: string,
    standingDiastolic: string,
    lyingHeartRate: string,
    standingHeartRate: string
  ) => {
    // Calculate drops from lying to standing
    const systolicDrop = parseFloat(lyingSystolic) - parseFloat(standingSystolic);
    const diastolicDrop = parseFloat(lyingDiastolic) - parseFloat(standingDiastolic);
    const heartRateIncrease = parseFloat(standingHeartRate) - parseFloat(lyingHeartRate);

    let result = {
      hasSystolicDrop: false,
      hasDiastolicDrop: false,
      hasHeartRateIncrease: false,
      hasOrthostaticHypotension: false,
      hasOrthostaticTachycardia: false,
      message: ''
    };

    // Check for orthostatic hypotension (systolic drop ≥ 20 mmHg or diastolic drop ≥ 10 mmHg)
    if (!isNaN(systolicDrop) && systolicDrop >= 20) {
      result.hasSystolicDrop = true;
      result.hasOrthostaticHypotension = true;
    }

    if (!isNaN(diastolicDrop) && diastolicDrop >= 10) {
      result.hasDiastolicDrop = true;
      result.hasOrthostaticHypotension = true;
    }

    // Check for orthostatic tachycardia (heart rate increase ≥ 30 bpm)
    if (!isNaN(heartRateIncrease) && heartRateIncrease >= 30) {
      result.hasHeartRateIncrease = true;
      result.hasOrthostaticTachycardia = true;
    }

    // Generate message
    if (result.hasOrthostaticHypotension && result.hasOrthostaticTachycardia) {
      result.message = 'Patient shows signs of both orthostatic hypotension and orthostatic tachycardia.';
    } else if (result.hasOrthostaticHypotension) {
      result.message = 'Patient shows signs of orthostatic hypotension.';
    } else if (result.hasOrthostaticTachycardia) {
      result.message = 'Patient shows signs of orthostatic tachycardia.';
    } else if (!isNaN(systolicDrop) && !isNaN(diastolicDrop) && !isNaN(heartRateIncrease)) {
      result.message = 'No significant orthostatic changes detected.';
    }

    return result;
  };

  // State for orthostatic hypotension result
  const [orthostaticResult, setOrthostaticResult] = useState<{
    hasSystolicDrop: boolean;
    hasDiastolicDrop: boolean;
    hasHeartRateIncrease: boolean;
    hasOrthostaticHypotension: boolean;
    hasOrthostaticTachycardia: boolean;
    message: string;
  }>({
    hasSystolicDrop: false,
    hasDiastolicDrop: false,
    hasHeartRateIncrease: false,
    hasOrthostaticHypotension: false,
    hasOrthostaticTachycardia: false,
    message: ''
  });

  // Handle input changes
  const onChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    const updatedFormData = {
      ...formData,
      [name]: value,
    };

    // Auto-calculate BMI when height or weight changes
    if (name === 'height' || name === 'weight') {
      updatedFormData.bmi = calculateBMI(updatedFormData.weight, updatedFormData.height);
    }

    // Auto-calculate PSQI sleep duration score
    if (name === 'psqi_hours_of_sleep') {
      updatedFormData.psqi_sleep_duration = calculateSleepDurationScore(updatedFormData.psqi_hours_of_sleep);
    }

    // Auto-calculate PSQI sleep latency score
    if (name === 'psqi_minutes_to_fall_asleep') {
      updatedFormData.psqi_sleep_latency = calculateSleepLatencyScore(updatedFormData.psqi_minutes_to_fall_asleep);
    }

    // Auto-calculate PSQI sleep efficiency score
    if (name === 'psqi_hours_of_sleep' || name === 'psqi_bedtime' || name === 'psqi_wake_up_time') {
      if (updatedFormData.psqi_hours_of_sleep && updatedFormData.psqi_bedtime && updatedFormData.psqi_wake_up_time) {
        updatedFormData.psqi_sleep_efficiency = calculateSleepEfficiency(
          updatedFormData.psqi_hours_of_sleep,
          updatedFormData.psqi_bedtime,
          updatedFormData.psqi_wake_up_time
        );
      }
    }

    // Auto-calculate total PSQI score when any component changes
    if (
      name === 'psqi_subjective_sleep_quality' ||
      name === 'psqi_sleep_latency' ||
      name === 'psqi_sleep_duration' ||
      name === 'psqi_sleep_efficiency' ||
      name === 'psqi_sleep_disturbances' ||
      name === 'psqi_sleep_medication' ||
      name === 'psqi_daytime_dysfunction' ||
      name === 'psqi_hours_of_sleep' ||
      name === 'psqi_minutes_to_fall_asleep' ||
      name === 'psqi_bedtime' ||
      name === 'psqi_wake_up_time'
    ) {
      if (
        updatedFormData.psqi_subjective_sleep_quality &&
        updatedFormData.psqi_sleep_latency &&
        updatedFormData.psqi_sleep_duration &&
        updatedFormData.psqi_sleep_efficiency &&
        updatedFormData.psqi_sleep_disturbances &&
        updatedFormData.psqi_sleep_medication &&
        updatedFormData.psqi_daytime_dysfunction
      ) {
        updatedFormData.psqi_total_score = calculatePSQI(
          updatedFormData.psqi_subjective_sleep_quality,
          updatedFormData.psqi_sleep_latency,
          updatedFormData.psqi_sleep_duration,
          updatedFormData.psqi_sleep_efficiency,
          updatedFormData.psqi_sleep_disturbances,
          updatedFormData.psqi_sleep_medication,
          updatedFormData.psqi_daytime_dysfunction
        );
      }
    }

    // Auto-calculate orthostatic hypotension when relevant fields change
    if (
      name === 'lying_bp_systolic' ||
      name === 'standing_bp_systolic' ||
      name === 'lying_bp_diastolic' ||
      name === 'standing_bp_diastolic' ||
      name === 'lying_heart_rate' ||
      name === 'standing_heart_rate'
    ) {
      const result = calculateOrthostaticHypotension(
        updatedFormData.lying_bp_systolic,
        updatedFormData.standing_bp_systolic,
        updatedFormData.lying_bp_diastolic,
        updatedFormData.standing_bp_diastolic,
        updatedFormData.lying_heart_rate,
        updatedFormData.standing_heart_rate
      );
      setOrthostaticResult(result);
    }

    setFormData(updatedFormData);
  };

  // Handle select changes
  const handleSelectChange = (e: SelectChangeEvent<string>) => {
    const { name, value } = e.target;
    const updatedFormData = {
      ...formData,
      [name]: value,
    };

    // Auto-calculate total PSQI score when any component changes via select
    if (
      name === 'psqi_subjective_sleep_quality' ||
      name === 'psqi_sleep_disturbances' ||
      name === 'psqi_sleep_medication' ||
      name === 'psqi_daytime_dysfunction'
    ) {
      if (
        updatedFormData.psqi_subjective_sleep_quality &&
        updatedFormData.psqi_sleep_latency &&
        updatedFormData.psqi_sleep_duration &&
        updatedFormData.psqi_sleep_efficiency &&
        updatedFormData.psqi_sleep_disturbances &&
        updatedFormData.psqi_sleep_medication &&
        updatedFormData.psqi_daytime_dysfunction
      ) {
        updatedFormData.psqi_total_score = calculatePSQI(
          updatedFormData.psqi_subjective_sleep_quality,
          updatedFormData.psqi_sleep_latency,
          updatedFormData.psqi_sleep_duration,
          updatedFormData.psqi_sleep_efficiency,
          updatedFormData.psqi_sleep_disturbances,
          updatedFormData.psqi_sleep_medication,
          updatedFormData.psqi_daytime_dysfunction
        );
      }
    }

    setFormData(updatedFormData);
  };

  // Handle radio button changes
  const handleRadioChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const updatedFormData = {
      ...formData,
      [name]: value,
    };

    // Auto-update individual FRAT scores based on standard FRAT scoring
    if (name === 'frat_fall_history') {
      // Fall history scoring: 1=2, 2=4, 3=6, 4=8
      const fallScores = ['0', '2', '4', '6', '8'];
      updatedFormData.frat_fall_history_score = fallScores[parseInt(value)] || '0';
    } else if (name === 'frat_medications') {
      // Medications scoring: 1=1, 2=2, 3=3, 4=4
      const medScores = ['0', '1', '2', '3', '4'];
      updatedFormData.frat_medications_score = medScores[parseInt(value)] || '0';
    } else if (name === 'frat_psychological') {
      // Psychological scoring: 1=1, 2=2, 3=3, 4=4
      const psychScores = ['0', '1', '2', '3', '4'];
      updatedFormData.frat_psychological_score = psychScores[parseInt(value)] || '0';
    } else if (name === 'frat_cognitive') {
      // Cognitive scoring: 1=1, 2=2, 3=3, 4=4
      const cogScores = ['0', '1', '2', '3', '4'];
      updatedFormData.frat_cognitive_score = cogScores[parseInt(value)] || '0';
    }

    // Auto-calculate total FRAT score when any component changes
    if (
      name === 'frat_fall_history' ||
      name === 'frat_medications' ||
      name === 'frat_psychological' ||
      name === 'frat_cognitive'
    ) {
      // Update the total score if all components are filled
      if (
        updatedFormData.frat_fall_history &&
        updatedFormData.frat_medications &&
        updatedFormData.frat_psychological &&
        updatedFormData.frat_cognitive
      ) {
        // Calculate total score using the individual component scores
        const fallScore = parseInt(updatedFormData.frat_fall_history_score) || 0;
        const medScore = parseInt(updatedFormData.frat_medications_score) || 0;
        const psychScore = parseInt(updatedFormData.frat_psychological_score) || 0;
        const cogScore = parseInt(updatedFormData.frat_cognitive_score) || 0;

        const totalScore = fallScore + medScore + psychScore + cogScore;
        updatedFormData.frat_total_score = totalScore.toString();

        // Determine risk level based on total score
        updatedFormData.frat_risk_level = determineFRATRiskLevel(updatedFormData.frat_total_score);
      }
    }

    setFormData(updatedFormData);
  };

  // Get the auth context for user information
  const auth = useContext(AuthContext);
  const user = auth?.user;

  // Handle form submission
  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);
    setSuccess(null);

    // Validate form data before submission
    const validationErrors = validatePatientData();
    if (validationErrors.length > 0) {
      // If we're not on the review tab, navigate to it to show errors
      if (activeStep !== 13) {
        setActiveStep(13);
      }
      setError('Please fix the validation errors before submitting.');
      setSubmitting(false);
      return;
    }

    try {
      // Define the response type
      interface PatientResponse {
        data: {
          patient_id: string | number;
          [key: string]: any;
        };
      }

      // Process form data before submission
      const processedFormData = { ...formData };

      // Log treatment plan fields for debugging
      console.log('DETAILED DEBUG: Treatment Plan Fields:', {
        treatment_plan: processedFormData.treatment_plan,
        follow_up_instructions: processedFormData.follow_up_instructions,
        referrals: processedFormData.referrals
      });

      // Store prescriptions for saving after patient is created/updated
      const prescriptionsToSave = formData.prescriptions || [];

      // Log prescriptions before saving
      console.log('DETAILED DEBUG: Prescriptions before saving:', {
        count: prescriptionsToSave.length,
        prescriptions: JSON.stringify(prescriptionsToSave, null, 2)
      });

      // Ensure prescriptions is an array
      if (!processedFormData.prescriptions) {
        processedFormData.prescriptions = [];
      }

      // Convert yes/no strings to boolean values for boolean fields
      if (processedFormData.emergency_contact_updated === 'yes') {
        (processedFormData as any).emergency_contact_updated = true;
      } else if (processedFormData.emergency_contact_updated === 'no') {
        (processedFormData as any).emergency_contact_updated = false;
      }

      // Remove prescriptions from patientData as they'll be saved separately
      const { prescriptions, ...dataWithoutPrescriptions } = processedFormData;

      let res: PatientResponse;

      if (isEditMode && id) {
        // Update existing patient
        res = await axios.put<any>(`${API_URL}/api/patients/${id}`, dataWithoutPrescriptions);

        // Save prescriptions for the updated patient if any exist
        if (prescriptionsToSave.length > 0) {
          try {
            console.log('DETAILED DEBUG: About to save prescriptions for updated patient:', {
              patientId: parseInt(id),
              prescriptionsCount: prescriptionsToSave.length,
              prescriptions: JSON.stringify(prescriptionsToSave, null, 2)
            });

            const savedPrescriptions = await savePrescriptionsForPatient(parseInt(id), prescriptionsToSave);
            console.log('Prescriptions saved successfully for updated patient:', savedPrescriptions);
          } catch (err) {
            console.error('Error saving prescriptions for updated patient:', err);
            // Don't show error to user as the patient was updated successfully
          }
        }

        setSuccess('Patient updated successfully!');
      } else {
        // Create new patient
        res = await axios.post<any>(`${API_URL}/api/patients`, dataWithoutPrescriptions);

        // Save prescriptions for the new patient if any exist
        if (prescriptionsToSave.length > 0 && res && res.data.patient_id) {
          try {
            console.log('DETAILED DEBUG: About to save prescriptions for new patient:', {
              patientId: res.data.patient_id,
              prescriptionsCount: prescriptionsToSave.length,
              prescriptions: JSON.stringify(prescriptionsToSave, null, 2)
            });

            const savedPrescriptions = await savePrescriptionsForPatient(parseInt(res.data.patient_id.toString()), prescriptionsToSave);
            console.log('Prescriptions saved successfully for new patient:', savedPrescriptions);
          } catch (err) {
            console.error('Error saving prescriptions for new patient:', err);
            // Don't show error to user as the patient was created successfully
          }
        }

        setSuccess('Patient created successfully!');
      }

      // Wait 1.5 seconds before redirecting to give user time to see success message
      setTimeout(() => {
        navigate(`/patients/${res.data.patient_id}`);
      }, 1500);
    } catch (err: any) {
      // Handle API error responses
      let errorMessage = `Failed to ${isEditMode ? 'update' : 'create'} patient. Please try again.`;

      if (err.response) {
        if (err.response.status === 400) {
          errorMessage = 'Invalid patient data. Please check your entries.';
        } else if (err.response.status === 409) {
          errorMessage = 'A patient with this information already exists.';
        } else if (err.response.status === 401) {
          errorMessage = `You are not authorized to ${isEditMode ? 'update' : 'create'} patients.`;
        } else if (err.response.status === 404) {
          errorMessage = 'Patient not found.';
        }
      }

      setError(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  // Validate patient data for the review section
  const validatePatientData = (): string[] => {
    const errors: string[] = [];

    // Required fields validation
    if (!formData.first_name) errors.push('First name is required');
    if (!formData.last_name) errors.push('Last name is required');
    if (!formData.date_of_birth) errors.push('Date of birth is required');
    if (!formData.gender) errors.push('Gender is required');
    if (!formData.phone) errors.push('Phone number is required');

    // Email validation if provided
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.push('Email address format is invalid');
    }

    // Validate numeric fields
    if (formData.height && isNaN(parseFloat(formData.height))) {
      errors.push('Height must be a valid number');
    }

    if (formData.weight && isNaN(parseFloat(formData.weight))) {
      errors.push('Weight must be a valid number');
    }

    if (formData.temperature && isNaN(parseFloat(formData.temperature))) {
      errors.push('Temperature must be a valid number');
    }

    // Validate blood pressure values if provided
    if ((formData.sitting_bp_systolic && !formData.sitting_bp_diastolic) ||
        (!formData.sitting_bp_systolic && formData.sitting_bp_diastolic)) {
      errors.push('Both systolic and diastolic blood pressure values must be provided');
    }

    // Validate heart rate if provided
    if (formData.sitting_heart_rate && isNaN(parseFloat(formData.sitting_heart_rate))) {
      errors.push('Heart rate must be a valid number');
    }

    // Validate emergency contact information
    if ((formData.emergency_contact_name && !formData.emergency_contact_phone) ||
        (!formData.emergency_contact_name && formData.emergency_contact_phone)) {
      errors.push('Both emergency contact name and phone must be provided');
    }

    // Check for reasonable values
    if (formData.height && parseFloat(formData.height) > 250) {
      errors.push('Height value seems unusually high (> 250 cm)');
    }

    if (formData.weight && parseFloat(formData.weight) > 300) {
      errors.push('Weight value seems unusually high (> 300 kg)');
    }

    if (formData.temperature &&
        (parseFloat(formData.temperature) < 35 || parseFloat(formData.temperature) > 42)) {
      errors.push('Temperature is outside normal human range (35-42°C)');
    }

    if (formData.sitting_bp_systolic && parseFloat(formData.sitting_bp_systolic) > 250) {
      errors.push('Systolic blood pressure value seems unusually high (> 250 mmHg)');
    }

    if (formData.sitting_bp_diastolic && parseFloat(formData.sitting_bp_diastolic) > 150) {
      errors.push('Diastolic blood pressure value seems unusually high (> 150 mmHg)');
    }

    if (formData.sitting_heart_rate &&
        (parseFloat(formData.sitting_heart_rate) < 30 || parseFloat(formData.sitting_heart_rate) > 220)) {
      errors.push('Heart rate is outside normal human range (30-220 bpm)');
    }

    return errors;
  };

  // Calculate completion percentage for each logical section
  const calculateSectionCompletion = (section: string): {
    isComplete: boolean;
    percentage: number;
    missingFields: string[];
    tabIndex: number;
  } => {
    let sectionFields: string[] = [];
    let tabIndex = 0;

    // Define fields for each logical section
    switch(section) {
      case 'Basic Information':
        sectionFields = ['first_name', 'last_name', 'date_of_birth', 'gender', 'blood_type'];
        tabIndex = 0;
        break;
      case 'Medical Assignment':
        sectionFields = ['doctor_id', 'medical_history', 'allergies', 'medical_allergies'];
        tabIndex = 0;
        break;
      case 'Contact Details':
        sectionFields = ['phone', 'email', 'address'];
        tabIndex = 1;
        break;
      case 'Emergency Contact':
        sectionFields = ['emergency_contact_name', 'emergency_contact_phone', 'emergency_contact_relationship', 'emergency_contact_updated'];
        tabIndex = 1;
        break;
      case 'Medical Information':
        sectionFields = ['current_medications', 'medication_adherence', 'medication_side_effects', 'pill_burden'];
        tabIndex = 2;
        break;
      case 'Vital Signs':
        sectionFields = ['height', 'weight', 'bmi', 'temperature', 'sitting_bp_systolic', 'sitting_bp_diastolic', 'sitting_heart_rate', 'respiratory_rate'];
        tabIndex = 3;
        break;
      case 'Lab Results':
        // Comprehensive list of lab result fields organized by category
        // Exclude fields that don't exist in the database or are optional
        sectionFields = [
          // Diabetes and Metabolic Markers
          'blood_glucose', 'hba1c',

          // Lipid Panel
          'cholesterol_total', 'hdl_cholesterol', 'ldl_cholesterol', 'triglycerides', 'vldl',

          // Kidney Function
          'creatinine', 'egfr', 'blood_urea_nitrogen', 'urine_albumin_creatinine_ratio',

          // Liver Function
          'alt', 'ast', 'alp', 'ggt', 'bilirubin_t', 'bilirubin_d', 'albumin', 'total_protein',

          // Thyroid Function
          'tsh', 't4', 't3', // Use t4 and t3 instead of free_t4 and free_t3

          // Inflammatory Markers
          'crp', 'esr',

          // Anemia and Blood Health
          'hemoglobin', 'hematocrit', 'ferritin', 'iron', 'vitamin_b12', 'folate',
          // Removed 'inr' as it's not in the database

          // Complete Blood Count
          'rbc', 'wbc', 'platelets', 'mcv', 'mch', 'mchc', 'rdw',

          // White Blood Cell Differential
          'neutrophils', 'lymphocytes', 'monocytes', 'eosinophils', 'basophils',

          // Urinalysis
          'urine_color', 'urine_transparency', 'urine_ph', 'urine_sugar', 'urine_protein',
          'urine_rbcs', 'urine_pus_cells', 'urine_epithelial_cells', 'urine_crystals', 'urine_casts',

          // Bone Health
          'vitamin_d', 'calcium', 'magnesium', 'phosphorus',
          // Removed 'parathyroid_hormone' and 'alkaline_phosphatase_bone' as they're not in the database

          // Other Electrolytes
          'sodium', 'potassium',

          // Other
          'uric_acid'
        ];
        tabIndex = 4;
        break;
      case 'Cognitive Health':
        sectionFields = ['mini_cog_word_recall_score', 'mini_cog_clock_drawing_score', 'cognitive_impairment_score', 'mini_cog_notes'];
        tabIndex = 5;
        break;
      case 'Depression':
        sectionFields = ['depression_score', 'phq9_interest_pleasure', 'phq9_feeling_down', 'phq9_sleep_issues', 'phq9_tired', 'phq9_appetite', 'phq9_feeling_bad', 'phq9_concentration', 'phq9_moving_speaking', 'phq9_thoughts_hurting', 'phq9_difficulty_level', 'phq9_notes'];
        tabIndex = 6;
        break;
      case 'Anxiety':
        sectionFields = ['anxiety_score', 'gad7_feeling_nervous', 'gad7_stop_worrying', 'gad7_worrying_much', 'gad7_trouble_relaxing', 'gad7_restless', 'gad7_annoyed', 'gad7_feeling_afraid', 'gad7_difficulty_level', 'gad7_notes', 'mental_health_assessment'];
        tabIndex = 7;
        break;
      case 'Sleep Assessment':
        // Use the correct field names that exist in the form and database
        sectionFields = [
          // Basic sleep information
          'sleep_quality', 'sleep_duration', 'sleep_disturbances',
          'sleep_patterns', 'sleep_initiation_difficulties',

          // PSQI fields - only include the most important ones for completion calculation
          'psqi_total_score', 'psqi_subjective_sleep_quality',
          'psqi_sleep_medication', 'psqi_sleep_disturbances'
        ];
        tabIndex = 8;
        break;
      case 'Pain Assessment':
        // Only include the four required pain assessment fields
        sectionFields = ['pain_level', 'pain_character', 'pain_location', 'safe_pain_medications'];
        tabIndex = 8;
        break;
      case 'Frailty Assessment':
        // Use the correct field names that exist in the form and database
        sectionFields = [
          // Physical measurements
          'grip_strength', 'calf_circumference',

          // Mobility and falls
          'fall_detection_incidents', 'mobility_aids_used', 'mobility_status',

          // FRAT scores
          'frat_fall_history', 'frat_medications', 'frat_psychological', 'frat_cognitive',
          'frat_total_score', 'frat_risk_level', 'frat_notes'
        ];
        tabIndex = 9;
        break;
      case 'Vaccinations':
        sectionFields = ['influenza_vaccination_date', 'pneumococcal_vaccination_date', 'zoster_vaccination_date', 'tdap_vaccination_date', 'covid19_vaccination_date', 'covid19_booster_date', 'hepatitis_a_vaccination_date', 'hepatitis_b_vaccination_date', 'mmr_vaccination_date', 'varicella_vaccination_date', 'other_vaccinations'];
        tabIndex = 10;
        break;
      case 'Health Status':
        // Use the correct field names that exist in the form and database
        sectionFields = [
          // Sensory Status
          'vision_status', 'hearing_status', 'use_of_aid_vision', 'use_of_aid_hearing',

          // Activity & Nutrition
          'activity_level', 'nutritional_status', 'hydration_status', 'supplements', 'exercise_frequency', 'dietary_intake_quality',

          // Social & Environmental
          'social_interaction_levels', 'social_support', 'living_situation', 'living_conditions',
          'environmental_risks', 'transportation_access', 'financial_concern', 'home_safety_evaluation', 'age_friendly_environment'
        ];
        tabIndex = 11;
        break;
      case 'Diagnosis & Prescriptions':
        sectionFields = ['diagnosis', 'treatment_plan', 'follow_up_instructions', 'referrals'];
        tabIndex = 12;
        break;
      default:
        sectionFields = [];
        tabIndex = 0;
    }

    // If no fields defined for this section, return 0%
    if (sectionFields.length === 0) {
      return { isComplete: false, percentage: 0, missingFields: [], tabIndex };
    }

    const missingFields: string[] = [];
    let filledCount = 0;

    // Check each field in the section
    for (const field of sectionFields) {
      const value = formData[field as keyof PatientFormData];
      if (value && value !== '') {
        filledCount++;
      } else {
        missingFields.push(field);
      }
    }

    const percentage = Math.round((filledCount / sectionFields.length) * 100);
    const isComplete = percentage === 100;

    return { isComplete, percentage, missingFields, tabIndex };
  };

  // Get changes between original and current form data
  const getChangedFields = (): { field: string, oldValue: any, newValue: any }[] => {
    if (!isEditMode || !originalData) {
      // If not in edit mode or no original data, all fields are considered new
      return Object.entries(formData)
        .filter(([_, value]) => value && value !== '')
        .map(([key, value]) => {
          // Special handling for prescriptions array
          if (key === 'prescriptions' && Array.isArray(value)) {
            return {
              field: key,
              oldValue: 'None',
              newValue: `${value.length} prescription(s)`
            };
          }
          return {
            field: key,
            oldValue: '',
            newValue: value
          };
        });
    }

    // Compare original data with current form data
    return Object.entries(formData)
      .filter(([key, value]) => {
        // Skip empty values and fields that haven't changed
        const originalValue = originalData[key as keyof PatientFormData];

        // Special handling for prescriptions array
        if (key === 'prescriptions') {
          if (Array.isArray(value) && Array.isArray(originalValue)) {
            // Compare arrays by length for simplicity
            return value.length !== originalValue.length;
          }
          // If one is array and other isn't, they're different
          return true;
        }

        return value && value !== '' && value !== originalValue;
      })
      .map(([key, value]) => {
        // Special handling for prescriptions in the display
        if (key === 'prescriptions') {
          const originalPrescriptions = originalData[key as keyof PatientFormData];
          return {
            field: key,
            oldValue: Array.isArray(originalPrescriptions)
              ? `${originalPrescriptions.length} prescription(s)`
              : 'None',
            newValue: Array.isArray(value)
              ? `${value.length} prescription(s)`
              : 'None'
          };
        }

        return {
          field: key,
          oldValue: originalData[key as keyof PatientFormData] || '',
          newValue: value
        };
      });
  };

  // Calculate age from date of birth
  const calculateAge = (dateOfBirth: string): number => {
    const dob = new Date(dateOfBirth);
    const today = new Date();
    let age = today.getFullYear() - dob.getFullYear();
    const monthDiff = today.getMonth() - dob.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
      age--;
    }

    return age;
  };

  // Get a human-readable field name
  const getFieldLabel = (fieldName: string): string => {
    const fieldLabels: Record<string, string> = {
      // Basic Information
      first_name: 'First Name',
      last_name: 'Last Name',
      date_of_birth: 'Date of Birth',
      gender: 'Gender',
      blood_type: 'Blood Type',
      phone: 'Phone',
      email: 'Email',
      address: 'Address',
      doctor_id: 'Doctor',

      // Emergency Contact
      emergency_contact_name: 'Emergency Contact Name',
      emergency_contact_phone: 'Emergency Contact Phone',
      emergency_contact_relationship: 'Emergency Contact Relationship',
      emergency_contact_updated: 'Emergency Contact Updated',

      // Medical Information
      medical_history: 'Medical History',
      allergies: 'Allergies',
      medical_allergies: 'Medical Allergies',

      // Vital Signs
      height: 'Height (cm)',
      weight: 'Weight (kg)',
      bmi: 'BMI',
      temperature: 'Temperature (°C)',
      respiratory_rate: 'Respiratory Rate',
      sitting_bp_systolic: 'Blood Pressure Systolic (Sitting)',
      sitting_bp_diastolic: 'Blood Pressure Diastolic (Sitting)',
      sitting_heart_rate: 'Heart Rate (Sitting)',
      lying_bp_systolic: 'Blood Pressure Systolic (Lying)',
      lying_bp_diastolic: 'Blood Pressure Diastolic (Lying)',
      lying_heart_rate: 'Heart Rate (Lying)',
      standing_bp_systolic: 'Blood Pressure Systolic (Standing)',
      standing_bp_diastolic: 'Blood Pressure Diastolic (Standing)',
      standing_heart_rate: 'Heart Rate (Standing)',
      heart_rhythm: 'Heart Rhythm',

      // Lab Results
      // Diabetes and Metabolic Markers
      blood_glucose: 'Blood Glucose',
      hba1c: 'HbA1c',

      // Lipid Panel
      cholesterol_total: 'Total Cholesterol',
      hdl_cholesterol: 'HDL Cholesterol',
      ldl_cholesterol: 'LDL Cholesterol',
      triglycerides: 'Triglycerides',
      vldl: 'VLDL Cholesterol',

      // Kidney Function
      creatinine: 'Creatinine',
      egfr: 'eGFR',
      blood_urea_nitrogen: 'Blood Urea Nitrogen',
      urine_albumin_creatinine_ratio: 'Urine Albumin/Creatinine Ratio',

      // Liver Function
      alt: 'ALT',
      ast: 'AST',
      alp: 'ALP',
      ggt: 'GGT',
      bilirubin_t: 'Total Bilirubin',
      bilirubin_d: 'Direct Bilirubin',
      albumin: 'Albumin',
      total_protein: 'Total Protein',

      // Thyroid Function
      tsh: 'TSH',
      t3: 'T3',
      t4: 'T4',

      // Vitamin Status & Iron Studies
      vitamin_d: 'Vitamin D',
      vitamin_b12: 'Vitamin B12',
      folate: 'Folate',
      ferritin: 'Ferritin',
      iron: 'Iron',

      // Complete Blood Count
      hemoglobin: 'Hemoglobin',
      hematocrit: 'Hematocrit',
      rbc: 'Red Blood Cell Count',
      wbc: 'White Blood Cell Count',
      platelets: 'Platelets',

      // Red Blood Cell Indices
      mcv: 'MCV',
      mch: 'MCH',
      mchc: 'MCHC',
      rdw: 'RDW',

      // White Blood Cell Differential
      neutrophils: 'Neutrophils',
      lymphocytes: 'Lymphocytes',
      monocytes: 'Monocytes',
      eosinophils: 'Eosinophils',
      basophils: 'Basophils',

      // Coagulation
      inr: 'INR',

      // Inflammatory Markers
      crp: 'CRP',
      esr: 'ESR',

      // Urinalysis
      urine_color: 'Urine Color',
      urine_transparency: 'Urine Transparency',
      urine_ph: 'Urine pH',
      urine_sugar: 'Urine Sugar',
      urine_protein: 'Urine Protein',
      urine_rbcs: 'Urine RBCs',
      urine_pus_cells: 'Urine WBCs',
      urine_epithelial_cells: 'Urine Epithelial Cells',
      urine_crystals: 'Urine Crystals',
      urine_casts: 'Urine Casts',

      // Electrolytes and Minerals
      calcium: 'Calcium',
      magnesium: 'Magnesium',
      phosphorus: 'Phosphorus',
      sodium: 'Sodium',
      potassium: 'Potassium',

      // Other
      uric_acid: 'Uric Acid',

      // Cognitive Health
      cognitive_impairment_score: 'Cognitive Score',
      mini_cog_word_recall_score: 'Word Recall Score',
      mini_cog_clock_drawing_score: 'Clock Drawing Score',
      mini_cog_notes: 'Mini-Cog Notes',

      // Depression
      depression_screening: 'Depression Assessment',
      depression_score: 'Depression Score',
      phq9_interest_pleasure: 'Little Interest or Pleasure',
      phq9_feeling_down: 'Feeling Down or Depressed',
      phq9_sleep_issues: 'Sleep Issues',
      phq9_tired: 'Feeling Tired',
      phq9_appetite: 'Poor Appetite or Overeating',
      phq9_feeling_bad: 'Feeling Bad About Yourself',
      phq9_concentration: 'Trouble Concentrating',
      phq9_moving_speaking: 'Moving or Speaking Slowly',
      phq9_thoughts_hurting: 'Thoughts of Hurting Yourself',
      phq9_difficulty_level: 'Difficulty Level',
      phq9_notes: 'PHQ-9 Notes',

      // Anxiety
      anxiety_screening: 'Anxiety Assessment',
      anxiety_score: 'Anxiety Score',
      gad7_feeling_nervous: 'Feeling Nervous or Anxious',
      gad7_stop_worrying: 'Cannot Stop Worrying',
      gad7_worrying_much: 'Worrying Too Much',
      gad7_trouble_relaxing: 'Trouble Relaxing',
      gad7_restless: 'Being Restless',
      gad7_annoyed: 'Becoming Easily Annoyed',
      gad7_feeling_afraid: 'Feeling Afraid',
      gad7_difficulty_level: 'Difficulty Level',
      gad7_notes: 'GAD-7 Notes',
      mental_health_assessment: 'Mental Health Assessment',

      // Sleep Assessment
      sleep_quality: 'Sleep Quality',
      sleep_duration: 'Sleep Duration',
      sleep_disturbances: 'Sleep Disturbances',
      sleep_patterns: 'Sleep Patterns',
      sleep_initiation_difficulties: 'Sleep Initiation Difficulties',
      sleep_quality_duration: 'Sleep Quality Duration',
      psqi_total_score: 'PSQI Total Score',
      psqi_subjective_sleep_quality: 'Subjective Sleep Quality',
      psqi_sleep_latency: 'Sleep Latency',
      psqi_sleep_duration: 'Sleep Duration',
      psqi_sleep_efficiency: 'Sleep Efficiency',
      psqi_sleep_disturbances: 'Sleep Disturbances',
      psqi_sleep_medication: 'Sleep Medication Use',
      psqi_daytime_dysfunction: 'Daytime Dysfunction',
      psqi_hours_of_sleep: 'Hours of Sleep',
      psqi_minutes_to_fall_asleep: 'Minutes to Fall Asleep',
      psqi_bedtime: 'Bedtime',
      psqi_wake_up_time: 'Wake-up Time',

      // Pain Assessment
      pain_level: 'Pain Level',
      pain_character: 'Pain Character',
      pain_location: 'Pain Location',
      pain_frequency: 'Pain Frequency',
      pain_management: 'Pain Management',
      safe_pain_medications: 'Safe Pain Medications',

      // Frailty Assessment
      frat_total_score: 'FRAT Total Score',
      frat_risk_level: 'Frailty Risk Level',
      frat_fall_history: 'Fall History',
      frat_medications: 'Medications',
      frat_psychological: 'Psychological',
      frat_cognitive: 'Cognitive',
      frat_fall_history_score: 'Fall History Score',
      frat_medications_score: 'Medications Score',
      frat_psychological_score: 'Psychological Score',
      frat_cognitive_score: 'Cognitive Score',
      frat_notes: 'FRAT Notes',
      grip_strength: 'Grip Strength',
      calf_circumference: 'Calf Circumference',
      fall_detection_incidents: 'Falls in Past Year',
      mobility_aids_used: 'Mobility Aids Used',
      mobility_status: 'Mobility Status',

      // Vaccinations
      influenza_vaccination_date: 'Influenza Vaccination Date',
      pneumococcal_vaccination_date: 'Pneumococcal Vaccination Date',
      zoster_vaccination_date: 'Zoster Vaccination Date',
      tdap_vaccination_date: 'Tdap Vaccination Date',
      covid19_vaccination_date: 'COVID-19 Vaccination Date',
      covid19_booster_date: 'COVID-19 Booster Date',
      hepatitis_a_vaccination_date: 'Hepatitis A Vaccination Date',
      hepatitis_b_vaccination_date: 'Hepatitis B Vaccination Date',
      mmr_vaccination_date: 'MMR Vaccination Date',
      varicella_vaccination_date: 'Varicella Vaccination Date',
      other_vaccinations: 'Other Vaccinations',

      // Health Status
      vision_status: 'Vision Status',
      hearing_status: 'Hearing Status',
      use_of_aid_vision: 'Vision Aid',
      use_of_aid_hearing: 'Hearing Aid',
      dietary_intake_quality: 'Dietary Intake Quality',
      nutritional_status: 'Nutritional Status',
      hydration_status: 'Hydration Status',
      supplements: 'Supplements',
      exercise_frequency: 'Exercise Frequency',
      social_interaction_levels: 'Social Interaction Levels',
      social_support: 'Social Support',
      social_support_network: 'Social Support Network',
      living_situation: 'Living Situation',
      living_conditions: 'Living Conditions',
      environmental_risks: 'Environmental Risks',
      age_friendly_environment: 'Age-Friendly Environment',
      transportation_access: 'Transportation Access',
      financial_concern: 'Financial Concerns',
      home_safety_evaluation: 'Home Safety Evaluation',

      // Diagnosis & Prescriptions
      diagnosis: 'Diagnosis',
      treatment_plan: 'Treatment Plan',
      follow_up_instructions: 'Follow-up Instructions',
      referrals: 'Referrals'
    };

    return fieldLabels[fieldName] || fieldName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  // Format field value for display
  const formatFieldValue = (fieldName: string, value: string): string => {
    // Handle special cases
    if (fieldName === 'doctor_id' && value) {
      const doctor = doctors.find(d => d.doctor_id.toString() === value);
      return doctor ? `Dr. ${doctor.first_name} ${doctor.last_name}` : value;
    }

    if (fieldName === 'emergency_contact_updated') {
      return value === 'yes' ? 'Yes' : value === 'no' ? 'No' : value;
    }

    if (fieldName.includes('_bp_') && value) {
      return `${value} mmHg`;
    }

    if (fieldName.includes('heart_rate') && value) {
      return `${value} bpm`;
    }

    if (fieldName === 'temperature' && value) {
      return `${value} °C`;
    }

    if (fieldName === 'height' && value) {
      return `${value} cm`;
    }

    if (fieldName === 'weight' && value) {
      return `${value} kg`;
    }

    if (fieldName === 'pain_level' && value) {
      return `${value}/10`;
    }

    return value;
  };

  // Define steps
  const steps = [
    { label: 'Basic Information', icon: <PersonIcon /> },
    { label: 'Contact Details', icon: <PhoneIcon /> },
    { label: 'Medical Information', icon: <MedicalIcon /> },
    { label: 'Vital Signs', icon: <FavoriteIcon /> },
    { label: 'Lab Results', icon: <ScienceIcon /> },
    { label: 'Cognitive Health', icon: <PsychologyIcon /> },
    { label: 'Depression', icon: <MoodIcon /> },
    { label: 'Anxiety', icon: <MoodIcon /> },
    { label: 'Sleep & Pain', icon: <BedIcon /> },
    { label: 'Frailty Assessment', icon: <FallsIcon /> },
    { label: 'Vaccinations', icon: <VaccinesIcon /> },
    { label: 'Health Status', icon: <HealthIcon /> },
    { label: 'Diagnosis & Prescriptions', icon: <MedicationIcon /> },
    { label: 'Review & Submit', icon: <AssessmentIcon /> }
  ];

  // Show loading indicator while fetching patient data
  if (loading) {
    return (
      <Container maxWidth="lg">
        <StyledPaper>
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', py: 5 }}>
            <CircularProgress size={60} />
            <Typography variant="h6" sx={{ mt: 3 }}>
              Loading patient data...
            </Typography>
          </Box>
        </StyledPaper>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <StyledPaper>
        {/* Header */}
        <Box sx={{ mb: 4 }}>
          <Button
            component={Link}
            to="/patients"
            variant="outlined"
            sx={{ mb: 2 }}
            startIcon={<PersonIcon />}
          >
            Back to Patients
          </Button>

          <Typography variant="h4" component="h1" gutterBottom>
            {isEditMode ? 'Edit Patient' : 'Add New Patient'}
          </Typography>

          <Typography variant="subtitle1" color="text.secondary">
            {isEditMode ? 'Update patient information' : 'Enter patient information'}
          </Typography>
        </Box>

        {/* Alert Messages */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 3 }}>
            {success}
          </Alert>
        )}

        {/* Step Progress */}
        <Box sx={{ width: '100%', mb: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2" color="text.secondary">
              Step {activeStep + 1} of {steps.length}
            </Typography>
            <Typography variant="body2" color="primary">
              {steps[activeStep].label}
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={(activeStep / (steps.length - 1)) * 100}
            sx={{ height: 8, borderRadius: 4 }}
          />
        </Box>

        {/* Step Navigation */}
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 4 }}>
          {steps.map((step, index) => (
            <Button
              key={index}
              variant={activeStep === index ? 'contained' : 'outlined'}
              size="small"
              startIcon={step.icon}
              onClick={() => handleStepChange(index)}
              sx={{
                borderRadius: 2,
                mb: 1,
                textTransform: 'none',
                fontSize: '0.85rem'
              }}
            >
              {step.label}
            </Button>
          ))}
        </Box>

        {/* Form */}
        <form onSubmit={onSubmit}>
          {/* Step Content */}
          <TabPanel value={activeStep} index={0}>
            <FormSection>
              <SectionTitle variant="h6">
                <PersonIcon /> Basic Information
              </SectionTitle>

              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    required
                    id="first_name"
                    name="first_name"
                    label="First Name"
                    value={formData.first_name}
                    onChange={onChange}
                    variant="outlined"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    required
                    id="last_name"
                    name="last_name"
                    label="Last Name"
                    value={formData.last_name}
                    onChange={onChange}
                    variant="outlined"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="unique_id"
                    name="unique_id"
                    label="Patient ID"
                    value={formData.unique_id}
                    disabled
                    variant="outlined"
                    helperText="Will be auto-generated on save"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    required
                    id="date_of_birth"
                    name="date_of_birth"
                    label="Date of Birth"
                    type="date"
                    value={formData.date_of_birth}
                    onChange={onChange}
                    variant="outlined"
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth required variant="outlined">
                    <InputLabel id="gender-label">Gender</InputLabel>
                    <Select
                      labelId="gender-label"
                      id="gender"
                      name="gender"
                      value={formData.gender}
                      onChange={handleSelectChange}
                      label="Gender"
                    >
                      <MenuItem value="">Select Gender</MenuItem>
                      <MenuItem value="Male">Male</MenuItem>
                      <MenuItem value="Female">Female</MenuItem>
                      <MenuItem value="Other">Other</MenuItem>
                      <MenuItem value="Prefer not to say">Prefer not to say</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="blood-type-label">Blood Type</InputLabel>
                    <Select
                      labelId="blood-type-label"
                      id="blood_type"
                      name="blood_type"
                      value={formData.blood_type}
                      onChange={handleSelectChange}
                      label="Blood Type"
                    >
                      <MenuItem value="">Select Blood Type</MenuItem>
                      <MenuItem value="A+">A+</MenuItem>
                      <MenuItem value="A-">A-</MenuItem>
                      <MenuItem value="B+">B+</MenuItem>
                      <MenuItem value="B-">B-</MenuItem>
                      <MenuItem value="AB+">AB+</MenuItem>
                      <MenuItem value="AB-">AB-</MenuItem>
                      <MenuItem value="O+">O+</MenuItem>
                      <MenuItem value="O-">O-</MenuItem>
                      <MenuItem value="Unknown">Unknown</MenuItem>
                    </Select>
                    <FormHelperText>Important for transfusions and medical emergencies</FormHelperText>
                  </FormControl>
                </Grid>
              </Grid>
            </FormSection>



            <FormSection>
              <SectionTitle variant="h6">
                <PersonIcon /> Medical Assignment
              </SectionTitle>

              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="doctor-label">Attending Doctor</InputLabel>
                    <Select
                      labelId="doctor-label"
                      id="doctor_id"
                      name="doctor_id"
                      value={formData.doctor_id}
                      onChange={handleSelectChange}
                      label="Attending Doctor"
                    >
                      <MenuItem value="">Select Doctor</MenuItem>
                      {doctors.map((doctor) => (
                        <MenuItem key={doctor.doctor_id} value={doctor.doctor_id.toString()}>
                          Dr. {doctor.first_name} {doctor.last_name} {doctor.specialty ? `(${doctor.specialty})` : ''}
                        </MenuItem>
                      ))}
                    </Select>
                    {loadingDoctors && (
                      <FormHelperText>
                        <CircularProgress size={16} /> Loading doctors...
                      </FormHelperText>
                    )}
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="medical_history"
                    name="medical_history"
                    label="Medical History"
                    value={formData.medical_history}
                    onChange={onChange}
                    variant="outlined"
                    multiline
                    rows={4}
                    helperText="Brief summary of patient's medical history"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="allergies"
                    name="allergies"
                    label="General Allergies"
                    value={formData.allergies}
                    onChange={onChange}
                    multiline
                    rows={2}
                    variant="outlined"
                    helperText="List any known allergies (food, environmental, etc.)"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="medical_allergies"
                    name="medical_allergies"
                    label="Medical Allergies"
                    value={formData.medical_allergies}
                    onChange={onChange}
                    multiline
                    rows={2}
                    variant="outlined"
                    helperText="List any known medication allergies"
                  />
                </Grid>
              </Grid>
            </FormSection>
          </TabPanel>

          {/* Contact Details Tab */}
          <TabPanel value={activeStep} index={1}>
            <FormSection>
              <SectionTitle variant="h6">
                <PhoneIcon /> Contact Details
              </SectionTitle>

              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    required
                    id="phone"
                    name="phone"
                    label="Phone Number"
                    value={formData.phone}
                    onChange={onChange}
                    variant="outlined"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="email"
                    name="email"
                    label="Email Address"
                    type="email"
                    value={formData.email}
                    onChange={onChange}
                    variant="outlined"
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="address"
                    name="address"
                    label="Address"
                    value={formData.address}
                    onChange={onChange}
                    variant="outlined"
                    multiline
                    rows={3}
                  />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <PhoneIcon /> Emergency Contact Information
              </SectionTitle>

              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="emergency_contact_name"
                    name="emergency_contact_name"
                    label="Emergency Contact Name"
                    value={formData.emergency_contact_name}
                    onChange={onChange}
                    variant="outlined"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="emergency_contact_phone"
                    name="emergency_contact_phone"
                    label="Emergency Contact Phone"
                    value={formData.emergency_contact_phone}
                    onChange={onChange}
                    variant="outlined"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="emergency_contact_relationship"
                    name="emergency_contact_relationship"
                    label="Emergency Contact Relationship"
                    value={formData.emergency_contact_relationship}
                    onChange={onChange}
                    variant="outlined"
                    helperText="Relationship to the patient (e.g., spouse, child, friend)"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="emergency-contact-updated-label">Emergency Contact Updated</InputLabel>
                    <Select
                      labelId="emergency-contact-updated-label"
                      id="emergency_contact_updated"
                      name="emergency_contact_updated"
                      value={formData.emergency_contact_updated}
                      onChange={handleSelectChange}
                      label="Emergency Contact Updated"
                    >
                      <MenuItem value="">Select</MenuItem>
                      <MenuItem value="yes">Yes</MenuItem>
                      <MenuItem value="no">No</MenuItem>
                    </Select>
                    <FormHelperText>Has the emergency contact information been recently verified?</FormHelperText>
                  </FormControl>
                </Grid>
              </Grid>
            </FormSection>
          </TabPanel>

          {/* Medical Information Tab */}
          <TabPanel value={activeStep} index={2}>
            <FormSection>
              <SectionTitle variant="h6">
                <MedicalIcon /> Medication Information
              </SectionTitle>

              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="current_medications"
                    name="current_medications"
                    label="Current Medications"
                    value={formData.current_medications}
                    onChange={onChange}
                    variant="outlined"
                    multiline
                    rows={3}
                    helperText="List all current medications with dosages"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="medication_adherence"
                    name="medication_adherence"
                    label="Medication Adherence"
                    value={formData.medication_adherence}
                    onChange={onChange}
                    variant="outlined"
                    helperText="How well does the patient adhere to medication schedule?"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="medication_side_effects"
                    name="medication_side_effects"
                    label="Medication Side Effects"
                    value={formData.medication_side_effects}
                    onChange={onChange}
                    variant="outlined"
                    helperText="Any reported side effects from medications"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="pill_burden"
                    name="pill_burden"
                    label="Pill Burden"
                    value={formData.pill_burden}
                    onChange={onChange}
                    variant="outlined"
                    helperText="Number of pills taken daily"
                  />
                </Grid>
              </Grid>
            </FormSection>


          </TabPanel>

          {/* Vital Signs Tab */}
          <TabPanel value={activeStep} index={3}>
            <FormSection>
              <SectionTitle variant="h6">
                <FavoriteIcon /> Basic Measurements
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Basic Measurements"
                contextKey="vital_signs_basic"
              />
              <Grid container spacing={3}>
                <Grid item xs={12} sm={4}>
                  <Tooltip
                    title={
                      <Box>
                        <Typography variant="subtitle2">Height Measurement Guidelines</Typography>
                        <Typography variant="body2">
                          • Normal range for adults: 150-190 cm<br />
                          • Measure without shoes<br />
                          • For reference: 5'0" = 152 cm, 5'6" = 168 cm, 6'0" = 183 cm<br />
                          • Values outside normal range may require verification
                        </Typography>
                      </Box>
                    }
                    placement="top"
                    arrow
                  >
                    <TextField
                      fullWidth
                      id="height"
                      name="height"
                      label="Height (cm)"
                      type="number"
                      value={formData.height}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{
                        inputProps: { min: 0, step: 0.1 },
                        endAdornment: <InfoIcon color="action" fontSize="small" sx={{ opacity: 0.5 }} />
                      }}
                    />
                  </Tooltip>
                  <NormalRangeIndicator value={formData.height} min={150} max={190} unit="cm" />
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Tooltip
                    title={
                      <Box>
                        <Typography variant="subtitle2">Weight Measurement Guidelines</Typography>
                        <Typography variant="body2">
                          • Normal range for adults: 45-100 kg<br />
                          • Measure in light clothing<br />
                          • For reference: 110 lbs = 50 kg, 154 lbs = 70 kg, 220 lbs = 100 kg<br />
                          • Values outside normal range may require verification
                        </Typography>
                      </Box>
                    }
                    placement="top"
                    arrow
                  >
                    <TextField
                      fullWidth
                      id="weight"
                      name="weight"
                      label="Weight (kg)"
                      type="number"
                      value={formData.weight}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{
                        inputProps: { min: 0, step: 0.1 },
                        endAdornment: <InfoIcon color="action" fontSize="small" sx={{ opacity: 0.5 }} />
                      }}
                    />
                  </Tooltip>
                  <NormalRangeIndicator value={formData.weight} min={45} max={100} unit="kg" />
                </Grid>

                <Grid item xs={12} sm={4}>
                  <Tooltip
                    title={
                      <Box>
                        <Typography variant="subtitle2">BMI Classification</Typography>
                        <Typography variant="body2">
                          • Underweight: Below 18.5<br />
                          • Normal weight: 18.5-24.9<br />
                          • Overweight: 25-29.9<br />
                          • Obesity: 30 or higher<br />
                          • BMI may not be accurate for very muscular individuals or the elderly
                        </Typography>
                      </Box>
                    }
                    placement="top"
                    arrow
                  >
                    <TextField
                      fullWidth
                      id="bmi"
                      name="bmi"
                      label="BMI"
                      value={formData.bmi}
                      disabled
                      variant="outlined"
                      helperText="Auto-calculated from height and weight"
                      InputProps={{
                        endAdornment: <InfoIcon color="action" fontSize="small" sx={{ opacity: 0.5 }} />
                      }}
                    />
                  </Tooltip>
                  <NormalRangeIndicator value={formData.bmi} min={18.5} max={24.9} unit="kg/m²" />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Tooltip
                    title={
                      <Box>
                        <Typography variant="subtitle2">Temperature Guidelines</Typography>
                        <Typography variant="body2">
                          • Normal range: 36.1-37.2°C (97-99°F)<br />
                          • Fever: Above 38°C (100.4°F)<br />
                          • Hypothermia: Below 35°C (95°F)<br />
                          • Temperature may vary slightly throughout the day<br />
                          • Ensure measurement is in Celsius, not Fahrenheit
                        </Typography>
                      </Box>
                    }
                    placement="top"
                    arrow
                  >
                    <TextField
                      fullWidth
                      id="temperature"
                      name="temperature"
                      label="Temperature (°C)"
                      type="number"
                      value={formData.temperature}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{
                        inputProps: { min: 30, max: 45, step: 0.1 },
                        endAdornment: <InfoIcon color="action" fontSize="small" sx={{ opacity: 0.5 }} />
                      }}
                    />
                  </Tooltip>
                  <NormalRangeIndicator value={formData.temperature} min={36.1} max={37.2} unit="°C" />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Tooltip
                    title={
                      <Box>
                        <Typography variant="subtitle2">Respiratory Rate Guidelines</Typography>
                        <Typography variant="body2">
                          • Normal range for adults: 12-20 breaths per minute<br />
                          • Measure when patient is at rest<br />
                          • Count for a full minute for accuracy<br />
                          • Elevated rates may indicate respiratory distress or anxiety<br />
                          • Low rates may indicate respiratory depression
                        </Typography>
                      </Box>
                    }
                    placement="top"
                    arrow
                  >
                    <TextField
                      fullWidth
                      id="respiratory_rate"
                      name="respiratory_rate"
                      label="Respiratory Rate (breaths/min)"
                      type="number"
                      value={formData.respiratory_rate}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{
                        inputProps: { min: 0, step: 1 },
                        endAdornment: <InfoIcon color="action" fontSize="small" sx={{ opacity: 0.5 }} />
                      }}
                    />
                  </Tooltip>
                  <NormalRangeIndicator value={formData.respiratory_rate} min={12} max={20} unit="breaths/min" />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <FavoriteIcon /> Blood Pressure & Heart Rate
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Blood Pressure & Heart Rate"
                contextKey="vital_signs_bp_hr"
              />
              <Grid container spacing={3}>
                {/* Sitting Position (Row 1) */}
                <Grid item xs={12}>
                  <Typography variant="subtitle1" gutterBottom>
                    Sitting Position
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={4}>
                      <Tooltip
                        title={
                          <Box>
                            <Typography variant="subtitle2">Systolic Blood Pressure Guidelines</Typography>
                            <Typography variant="body2">
                              • Normal range: 100-140 mmHg<br />
                              • Hypertension: Above 140 mmHg<br />
                              • Hypotension: Below 90 mmHg<br />
                              • Systolic is the top number in BP reading<br />
                              • Represents pressure when heart contracts
                            </Typography>
                          </Box>
                        }
                        placement="top"
                        arrow
                      >
                        <TextField
                          fullWidth
                          id="sitting_bp_systolic"
                          name="sitting_bp_systolic"
                          label="Systolic (mmHg)"
                          type="number"
                          value={formData.sitting_bp_systolic}
                          onChange={onChange}
                          variant="outlined"
                          InputProps={{
                            inputProps: { min: 0, step: 1 },
                            endAdornment: <InfoIcon color="action" fontSize="small" sx={{ opacity: 0.5 }} />
                          }}
                        />
                      </Tooltip>
                      <NormalRangeIndicator value={formData.sitting_bp_systolic} min={100} max={140} unit="mmHg" />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Tooltip
                        title={
                          <Box>
                            <Typography variant="subtitle2">Diastolic Blood Pressure Guidelines</Typography>
                            <Typography variant="body2">
                              • Normal range: 60-90 mmHg<br />
                              • Hypertension: Above 90 mmHg<br />
                              • Hypotension: Below 60 mmHg<br />
                              • Diastolic is the bottom number in BP reading<br />
                              • Represents pressure when heart relaxes
                            </Typography>
                          </Box>
                        }
                        placement="top"
                        arrow
                      >
                        <TextField
                          fullWidth
                          id="sitting_bp_diastolic"
                          name="sitting_bp_diastolic"
                          label="Diastolic (mmHg)"
                          type="number"
                          value={formData.sitting_bp_diastolic}
                          onChange={onChange}
                          variant="outlined"
                          InputProps={{
                            inputProps: { min: 0, step: 1 },
                            endAdornment: <InfoIcon color="action" fontSize="small" sx={{ opacity: 0.5 }} />
                          }}
                        />
                      </Tooltip>
                      <NormalRangeIndicator value={formData.sitting_bp_diastolic} min={60} max={90} unit="mmHg" />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <Tooltip
                        title={
                          <Box>
                            <Typography variant="subtitle2">Heart Rate Guidelines</Typography>
                            <Typography variant="body2">
                              • Normal resting range: 60-100 bpm<br />
                              • Bradycardia: Below 60 bpm (may be normal in athletes)<br />
                              • Tachycardia: Above 100 bpm<br />
                              • Measure after 5 minutes of rest<br />
                              • Count for a full minute for accuracy
                            </Typography>
                          </Box>
                        }
                        placement="top"
                        arrow
                      >
                        <TextField
                          fullWidth
                          id="sitting_heart_rate"
                          name="sitting_heart_rate"
                          label="Heart Rate (bpm)"
                          type="number"
                          value={formData.sitting_heart_rate}
                          onChange={onChange}
                          variant="outlined"
                          InputProps={{
                            inputProps: { min: 0, step: 1 },
                            endAdornment: <InfoIcon color="action" fontSize="small" sx={{ opacity: 0.5 }} />
                          }}
                        />
                      </Tooltip>
                      <NormalRangeIndicator value={formData.sitting_heart_rate} min={60} max={100} unit="bpm" />
                    </Grid>
                  </Grid>
                </Grid>

                {/* Lying and Standing Positions (Row 2) */}
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle1" gutterBottom>
                    Lying Position
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        id="lying_bp_systolic"
                        name="lying_bp_systolic"
                        label="Systolic (mmHg)"
                        type="number"
                        value={formData.lying_bp_systolic}
                        onChange={onChange}
                        variant="outlined"
                        InputProps={{ inputProps: { min: 0, step: 1 } }}
                      />
                      <NormalRangeIndicator value={formData.lying_bp_systolic} min={100} max={140} unit="mmHg" />
                    </Grid>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        id="lying_bp_diastolic"
                        name="lying_bp_diastolic"
                        label="Diastolic (mmHg)"
                        type="number"
                        value={formData.lying_bp_diastolic}
                        onChange={onChange}
                        variant="outlined"
                        InputProps={{ inputProps: { min: 0, step: 1 } }}
                      />
                      <NormalRangeIndicator value={formData.lying_bp_diastolic} min={60} max={90} unit="mmHg" />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        id="lying_heart_rate"
                        name="lying_heart_rate"
                        label="Heart Rate (bpm)"
                        type="number"
                        value={formData.lying_heart_rate}
                        onChange={onChange}
                        variant="outlined"
                        InputProps={{ inputProps: { min: 0, step: 1 } }}
                      />
                      <NormalRangeIndicator value={formData.lying_heart_rate} min={60} max={100} unit="bpm" />
                    </Grid>
                  </Grid>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle1" gutterBottom>
                    Standing Position
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        id="standing_bp_systolic"
                        name="standing_bp_systolic"
                        label="Systolic (mmHg)"
                        type="number"
                        value={formData.standing_bp_systolic}
                        onChange={onChange}
                        variant="outlined"
                        InputProps={{ inputProps: { min: 0, step: 1 } }}
                      />
                      <NormalRangeIndicator value={formData.standing_bp_systolic} min={100} max={140} unit="mmHg" />
                    </Grid>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        id="standing_bp_diastolic"
                        name="standing_bp_diastolic"
                        label="Diastolic (mmHg)"
                        type="number"
                        value={formData.standing_bp_diastolic}
                        onChange={onChange}
                        variant="outlined"
                        InputProps={{ inputProps: { min: 0, step: 1 } }}
                      />
                      <NormalRangeIndicator value={formData.standing_bp_diastolic} min={60} max={90} unit="mmHg" />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        id="standing_heart_rate"
                        name="standing_heart_rate"
                        label="Heart Rate (bpm)"
                        type="number"
                        value={formData.standing_heart_rate}
                        onChange={onChange}
                        variant="outlined"
                        InputProps={{ inputProps: { min: 0, step: 1 } }}
                      />
                      <NormalRangeIndicator value={formData.standing_heart_rate} min={60} max={100} unit="bpm" />
                    </Grid>
                  </Grid>
                </Grid>

                {/* Orthostatic Hypotension Assessment */}
                {(formData.lying_bp_systolic && formData.standing_bp_systolic &&
                  formData.lying_bp_diastolic && formData.standing_bp_diastolic &&
                  formData.lying_heart_rate && formData.standing_heart_rate) && (
                  <Grid item xs={12}>
                    <Box
                      sx={{
                        mt: 2,
                        p: 2,
                        bgcolor: orthostaticResult.hasOrthostaticHypotension || orthostaticResult.hasOrthostaticTachycardia
                          ? 'rgba(255, 0, 0, 0.05)'
                          : 'rgba(0, 255, 0, 0.05)',
                        borderRadius: 1,
                        border: '1px solid',
                        borderColor: orthostaticResult.hasOrthostaticHypotension || orthostaticResult.hasOrthostaticTachycardia
                          ? 'error.light'
                          : 'success.light'
                      }}
                    >
                      <Typography variant="subtitle1" gutterBottom fontWeight="medium">
                        Orthostatic Assessment
                      </Typography>
                      <Typography variant="body2">
                        {orthostaticResult.message}
                      </Typography>
                      {orthostaticResult.hasSystolicDrop && (
                        <Typography variant="body2" color="error">
                          • Systolic drop: {parseFloat(formData.lying_bp_systolic) - parseFloat(formData.standing_bp_systolic)} mmHg (≥20 mmHg indicates orthostatic hypotension)
                        </Typography>
                      )}
                      {orthostaticResult.hasDiastolicDrop && (
                        <Typography variant="body2" color="error">
                          • Diastolic drop: {parseFloat(formData.lying_bp_diastolic) - parseFloat(formData.standing_bp_diastolic)} mmHg (≥10 mmHg indicates orthostatic hypotension)
                        </Typography>
                      )}
                      {orthostaticResult.hasHeartRateIncrease && (
                        <Typography variant="body2" color="error">
                          • Heart rate increase: {parseFloat(formData.standing_heart_rate) - parseFloat(formData.lying_heart_rate)} bpm (≥30 bpm indicates orthostatic tachycardia)
                        </Typography>
                      )}
                    </Box>
                  </Grid>
                )}

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="heart-rhythm-label">Heart Rhythm</InputLabel>
                    <Select
                      labelId="heart-rhythm-label"
                      id="heart_rhythm"
                      name="heart_rhythm"
                      value={formData.heart_rhythm}
                      onChange={handleSelectChange}
                      label="Heart Rhythm"
                    >
                      <MenuItem value="">Select Heart Rhythm</MenuItem>
                      <MenuItem value="Regular">Regular</MenuItem>
                      <MenuItem value="Irregular">Irregular</MenuItem>
                      <MenuItem value="Sinus Rhythm">Sinus Rhythm</MenuItem>
                      <MenuItem value="Sinus Bradycardia">Sinus Bradycardia</MenuItem>
                      <MenuItem value="Sinus Tachycardia">Sinus Tachycardia</MenuItem>
                      <MenuItem value="Atrial Fibrillation">Atrial Fibrillation</MenuItem>
                      <MenuItem value="Atrial Flutter">Atrial Flutter</MenuItem>
                      <MenuItem value="Ventricular Tachycardia">Ventricular Tachycardia</MenuItem>
                      <MenuItem value="Heart Block">Heart Block</MenuItem>
                      <MenuItem value="Premature Ventricular Contractions">Premature Ventricular Contractions</MenuItem>
                      <MenuItem value="Premature Atrial Contractions">Premature Atrial Contractions</MenuItem>
                    </Select>
                    <FormHelperText>Select the patient's heart rhythm</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="pulse_oximetry"
                    name="pulse_oximetry"
                    label="Oxygen Saturation (%)"
                    type="number"
                    value={formData.pulse_oximetry}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, max: 100, step: 1 } }}
                  />
                  <NormalRangeIndicator value={formData.pulse_oximetry} min={95} max={100} unit="%" />
                </Grid>
              </Grid>
            </FormSection>
          </TabPanel>

          {/* Lab Results Tab */}
          <TabPanel value={activeStep} index={4}>
            <FormSection>
              <SectionTitle variant="h6">
                <ScienceIcon /> Diabetes and Metabolic Markers
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Diabetes Markers"
                contextKey="lab_results_diabetes"
              />
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="blood_glucose"
                    name="blood_glucose"
                    label="Blood Glucose (mg/dL)"
                    type="number"
                    value={formData.blood_glucose}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 1 } }}
                    helperText="Fasting blood glucose level"
                  />
                  <NormalRangeIndicator value={formData.blood_glucose} min={70} max={99} unit="mg/dL" />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="hba1c"
                    name="hba1c"
                    label="HbA1c (%)"
                    type="number"
                    value={formData.hba1c}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, max: 20, step: 0.1 } }}
                    helperText="Glycated hemoglobin"
                  />
                  <NormalRangeIndicator value={formData.hba1c} min={4.0} max={5.6} unit="%" />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <ScienceIcon /> Lipid Profile
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Lipid Profile"
                contextKey="lab_results_lipids"
              />
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="cholesterol_total"
                    name="cholesterol_total"
                    label="Total Cholesterol (mg/dL)"
                    type="number"
                    value={formData.cholesterol_total}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 1 } }}
                  />
                  <NormalRangeIndicator value={formData.cholesterol_total} min={125} max={200} unit="mg/dL" />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="ldl_cholesterol"
                    name="ldl_cholesterol"
                    label="LDL Cholesterol (mg/dL)"
                    type="number"
                    value={formData.ldl_cholesterol}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 1 } }}
                    helperText="Low-density lipoprotein"
                  />
                  <NormalRangeIndicator value={formData.ldl_cholesterol} min={0} max={100} unit="mg/dL" />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="hdl_cholesterol"
                    name="hdl_cholesterol"
                    label="HDL Cholesterol (mg/dL)"
                    type="number"
                    value={formData.hdl_cholesterol}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 1 } }}
                    helperText="High-density lipoprotein"
                  />
                  <NormalRangeIndicator value={formData.hdl_cholesterol} min={40} max={60} unit="mg/dL" />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="vldl"
                    name="vldl"
                    label="VLDL (mg/dL)"
                    type="number"
                    value={formData.vldl}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 1 } }}
                    helperText="Very low-density lipoprotein"
                  />
                  <NormalRangeIndicator value={formData.vldl} min={5} max={40} unit="mg/dL" />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="triglycerides"
                    name="triglycerides"
                    label="Triglycerides (mg/dL)"
                    type="number"
                    value={formData.triglycerides}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 1 } }}
                  />
                  <NormalRangeIndicator value={formData.triglycerides} min={0} max={150} unit="mg/dL" />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <ScienceIcon /> Kidney Function Tests
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Kidney Function"
                contextKey="lab_results_kidney"
              />
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="creatinine"
                    name="creatinine"
                    label="Creatinine (mg/dL)"
                    type="number"
                    value={formData.creatinine}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.creatinine} min={0.7} max={1.3} unit="mg/dL" />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="egfr"
                    name="egfr"
                    label="eGFR (mL/min)"
                    type="number"
                    value={formData.egfr}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 1 } }}
                    helperText="Estimated glomerular filtration rate"
                  />
                  <NormalRangeIndicator value={formData.egfr} min={90} max={120} unit="mL/min" />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="blood_urea_nitrogen"
                    name="blood_urea_nitrogen"
                    label="BUN (mg/dL)"
                    type="number"
                    value={formData.blood_urea_nitrogen}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    helperText="Blood urea nitrogen"
                  />
                  <NormalRangeIndicator value={formData.blood_urea_nitrogen} min={7} max={20} unit="mg/dL" />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="uric_acid"
                    name="uric_acid"
                    label="Uric Acid (mg/dL)"
                    type="number"
                    value={formData.uric_acid}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.uric_acid} min={3.5} max={7.2} unit="mg/dL" />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="urine_albumin_creatinine_ratio"
                    name="urine_albumin_creatinine_ratio"
                    label="Urine Albumin-Creatinine Ratio (mg/g)"
                    type="number"
                    value={formData.urine_albumin_creatinine_ratio}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    helperText="Marker for kidney damage"
                  />
                  <NormalRangeIndicator value={formData.urine_albumin_creatinine_ratio} min={0} max={30} unit="mg/g" />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <ScienceIcon /> Liver Function Tests
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Liver Function"
                contextKey="lab_results_liver"
              />
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'medium', mb: 1 }}>Liver Enzymes</Typography>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="alt"
                    name="alt"
                    label="ALT/SGPT (U/L)"
                    type="number"
                    value={formData.alt}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 1 } }}
                    helperText="Alanine transaminase"
                  />
                  <NormalRangeIndicator value={formData.alt} min={7} max={56} unit="U/L" />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="ast"
                    name="ast"
                    label="AST/SGOT (U/L)"
                    type="number"
                    value={formData.ast}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 1 } }}
                    helperText="Aspartate transaminase"
                  />
                  <NormalRangeIndicator value={formData.ast} min={8} max={48} unit="U/L" />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="alp"
                    name="alp"
                    label="ALP (U/L)"
                    type="number"
                    value={formData.alp}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 1 } }}
                    helperText="Alkaline phosphatase"
                  />
                  <NormalRangeIndicator value={formData.alp} min={40} max={129} unit="U/L" />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="ggt"
                    name="ggt"
                    label="GGT (U/L)"
                    type="number"
                    value={formData.ggt}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 1 } }}
                    helperText="Gamma-glutamyl transferase"
                  />
                  <NormalRangeIndicator value={formData.ggt} min={8} max={61} unit="U/L" />
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'medium', mb: 1, mt: 2 }}>Liver Function</Typography>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="bilirubin_t"
                    name="bilirubin_t"
                    label="Total Bilirubin (mg/dL)"
                    type="number"
                    value={formData.bilirubin_t}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.bilirubin_t} min={0.1} max={1.2} unit="mg/dL" />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="bilirubin_d"
                    name="bilirubin_d"
                    label="Direct Bilirubin (mg/dL)"
                    type="number"
                    value={formData.bilirubin_d}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.bilirubin_d} min={0.0} max={0.3} unit="mg/dL" />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="albumin"
                    name="albumin"
                    label="Albumin (g/dL)"
                    type="number"
                    value={formData.albumin}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.albumin} min={3.5} max={5.0} unit="g/dL" />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="total_protein"
                    name="total_protein"
                    label="Total Protein (g/dL)"
                    type="number"
                    value={formData.total_protein}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.total_protein} min={6.0} max={8.3} unit="g/dL" />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <ScienceIcon /> Electrolytes and Minerals
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Electrolytes"
                contextKey="lab_results_electrolytes"
              />
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="sodium"
                    name="sodium"
                    label="Sodium (mEq/L)"
                    type="number"
                    value={formData.sodium}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 1 } }}
                  />
                  <NormalRangeIndicator value={formData.sodium} min={135} max={145} unit="mEq/L" />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="potassium"
                    name="potassium"
                    label="Potassium (mEq/L)"
                    type="number"
                    value={formData.potassium}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.potassium} min={3.5} max={5.0} unit="mEq/L" />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="calcium"
                    name="calcium"
                    label="Calcium (mg/dL)"
                    type="number"
                    value={formData.calcium}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.calcium} min={8.5} max={10.5} unit="mg/dL" />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="magnesium"
                    name="magnesium"
                    label="Magnesium (mg/dL)"
                    type="number"
                    value={formData.magnesium}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.magnesium} min={1.7} max={2.2} unit="mg/dL" />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <ScienceIcon /> Complete Blood Count and Iron Studies
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - CBC & Iron"
                contextKey="lab_results_cbc"
              />
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'medium', mb: 1 }}>Red Blood Cell Parameters</Typography>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="rbc"
                    name="rbc"
                    label="RBC (million/μL)"
                    type="number"
                    value={formData.rbc}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.rbc} min={4.5} max={5.9} unit="million/μL" />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="hemoglobin"
                    name="hemoglobin"
                    label="Hemoglobin (g/dL)"
                    type="number"
                    value={formData.hemoglobin}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.hemoglobin} min={13.5} max={17.5} unit="g/dL" />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="hematocrit"
                    name="hematocrit"
                    label="Hematocrit (%)"
                    type="number"
                    value={formData.hematocrit}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.hematocrit} min={41} max={50} unit="%" />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="platelets"
                    name="platelets"
                    label="Platelets (K/μL)"
                    type="number"
                    value={formData.platelets}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 1 } }}
                  />
                  <NormalRangeIndicator value={formData.platelets} min={150} max={450} unit="K/μL" />
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'medium', mb: 1, mt: 2 }}>Red Blood Cell Indices</Typography>
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="mcv"
                    name="mcv"
                    label="MCV (fL)"
                    type="number"
                    value={formData.mcv}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    helperText="Mean Corpuscular Volume"
                  />
                  <NormalRangeIndicator value={formData.mcv} min={80} max={100} unit="fL" />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="mch"
                    name="mch"
                    label="MCH (pg)"
                    type="number"
                    value={formData.mch}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    helperText="Mean Corpuscular Hemoglobin"
                  />
                  <NormalRangeIndicator value={formData.mch} min={27} max={33} unit="pg" />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="mchc"
                    name="mchc"
                    label="MCHC (g/dL)"
                    type="number"
                    value={formData.mchc}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    helperText="Mean Corpuscular Hemoglobin Concentration"
                  />
                  <NormalRangeIndicator value={formData.mchc} min={32} max={36} unit="g/dL" />
                </Grid>
                <Grid item xs={12} sm={6} md={3}>
                  <TextField
                    fullWidth
                    id="rdw"
                    name="rdw"
                    label="RDW (%)"
                    type="number"
                    value={formData.rdw}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    helperText="Red Cell Distribution Width"
                  />
                  <NormalRangeIndicator value={formData.rdw} min={11.5} max={14.5} unit="%" />
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'medium', mb: 1, mt: 2 }}>White Blood Cell Count & Differential</Typography>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="wbc"
                    name="wbc"
                    label="WBC (K/μL)"
                    type="number"
                    value={formData.wbc}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    helperText="White Blood Cell Count"
                  />
                  <NormalRangeIndicator value={formData.wbc} min={4.5} max={11.0} unit="K/μL" />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="neutrophils"
                    name="neutrophils"
                    label="Neutrophils (%)"
                    type="number"
                    value={formData.neutrophils}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, max: 100, step: 1 } }}
                  />
                  <NormalRangeIndicator value={formData.neutrophils} min={40} max={70} unit="%" />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="lymphocytes"
                    name="lymphocytes"
                    label="Lymphocytes (%)"
                    type="number"
                    value={formData.lymphocytes}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, max: 100, step: 1 } }}
                  />
                  <NormalRangeIndicator value={formData.lymphocytes} min={20} max={40} unit="%" />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="monocytes"
                    name="monocytes"
                    label="Monocytes (%)"
                    type="number"
                    value={formData.monocytes}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, max: 100, step: 1 } }}
                  />
                  <NormalRangeIndicator value={formData.monocytes} min={2} max={8} unit="%" />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="eosinophils"
                    name="eosinophils"
                    label="Eosinophils (%)"
                    type="number"
                    value={formData.eosinophils}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, max: 100, step: 1 } }}
                  />
                  <NormalRangeIndicator value={formData.eosinophils} min={1} max={4} unit="%" />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="basophils"
                    name="basophils"
                    label="Basophils (%)"
                    type="number"
                    value={formData.basophils}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, max: 100, step: 1 } }}
                  />
                  <NormalRangeIndicator value={formData.basophils} min={0} max={1} unit="%" />
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'medium', mb: 1, mt: 2 }}>Iron Studies</Typography>
                </Grid>
                <Grid item xs={12} sm={6} md={6}>
                  <TextField
                    fullWidth
                    id="ferritin"
                    name="ferritin"
                    label="Ferritin (ng/mL)"
                    type="number"
                    value={formData.ferritin}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 1 } }}
                  />
                  <NormalRangeIndicator value={formData.ferritin} min={20} max={250} unit="ng/mL" />
                </Grid>
                <Grid item xs={12} sm={6} md={6}>
                  <TextField
                    fullWidth
                    id="iron"
                    name="iron"
                    label="Iron (μg/dL)"
                    type="number"
                    value={formData.iron}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 1 } }}
                  />
                  <NormalRangeIndicator value={formData.iron} min={60} max={170} unit="μg/dL" />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <ScienceIcon /> Vitamin Status
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Vitamin Status"
                contextKey="lab_results_vitamins"
              />
              <Grid container spacing={3}>
                <Grid item xs={12} sm={4}>
                  <TextField
                    fullWidth
                    id="vitamin_b12"
                    name="vitamin_b12"
                    label="Vitamin B12 (pg/mL)"
                    type="number"
                    value={formData.vitamin_b12}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 1 } }}
                  />
                  <NormalRangeIndicator value={formData.vitamin_b12} min={200} max={900} unit="pg/mL" />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    fullWidth
                    id="vitamin_d"
                    name="vitamin_d"
                    label="Vitamin D (ng/mL)"
                    type="number"
                    value={formData.vitamin_d}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.vitamin_d} min={30} max={50} unit="ng/mL" />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    fullWidth
                    id="folate"
                    name="folate"
                    label="Folate (ng/mL)"
                    type="number"
                    value={formData.folate}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.folate} min={3} max={20} unit="ng/mL" />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <ScienceIcon /> Thyroid Function Tests
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Thyroid Function"
                contextKey="lab_results_thyroid"
              />
              <Grid container spacing={3}>
                <Grid item xs={12} sm={4}>
                  <TextField
                    fullWidth
                    id="tsh"
                    name="tsh"
                    label="TSH (mIU/L)"
                    type="number"
                    value={formData.tsh}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                    helperText="Thyroid-stimulating hormone"
                  />
                  <NormalRangeIndicator value={formData.tsh} min={0.4} max={4.0} unit="mIU/L" />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    fullWidth
                    id="t4"
                    name="t4"
                    label="T4 (ng/dL)"
                    type="number"
                    value={formData.t4}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                    helperText="Thyroxine"
                  />
                  <NormalRangeIndicator value={formData.t4} min={0.8} max={1.8} unit="ng/dL" />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    fullWidth
                    id="t3"
                    name="t3"
                    label="T3 (pg/mL)"
                    type="number"
                    value={formData.t3}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                    helperText="Triiodothyronine"
                  />
                  <NormalRangeIndicator value={formData.t3} min={2.3} max={4.2} unit="pg/mL" />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <ScienceIcon /> Inflammatory Markers
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Inflammatory Markers"
                contextKey="lab_results_inflammation"
              />
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="crp"
                    name="crp"
                    label="CRP (mg/L)"
                    type="number"
                    value={formData.crp}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    helperText="C-reactive protein"
                  />
                  <NormalRangeIndicator value={formData.crp} min={0} max={3.0} unit="mg/L" />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="esr"
                    name="esr"
                    label="ESR (mm/hr)"
                    type="number"
                    value={formData.esr}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 1 } }}
                    helperText="Erythrocyte sedimentation rate"
                  />
                  <NormalRangeIndicator value={formData.esr} min={0} max={20} unit="mm/hr" />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <ScienceIcon /> Urinalysis
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Urinalysis"
                contextKey="lab_results_urinalysis"
              />
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'medium', mb: 1 }}>Physical Properties</Typography>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="urine_color"
                    name="urine_color"
                    label="Color"
                    value={formData.urine_color}
                    onChange={onChange}
                    variant="outlined"
                    select
                  >
                    <MenuItem value="pale yellow">Pale Yellow</MenuItem>
                    <MenuItem value="yellow">Yellow</MenuItem>
                    <MenuItem value="dark yellow">Dark Yellow</MenuItem>
                    <MenuItem value="amber">Amber</MenuItem>
                    <MenuItem value="red">Red</MenuItem>
                    <MenuItem value="orange">Orange</MenuItem>
                    <MenuItem value="blue/green">Blue/Green</MenuItem>
                    <MenuItem value="brown">Brown</MenuItem>
                    <MenuItem value="black">Black</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="urine_transparency"
                    name="urine_transparency"
                    label="Transparency"
                    value={formData.urine_transparency}
                    onChange={onChange}
                    variant="outlined"
                    select
                  >
                    <MenuItem value="clear">Clear</MenuItem>
                    <MenuItem value="slightly cloudy">Slightly Cloudy</MenuItem>
                    <MenuItem value="cloudy">Cloudy</MenuItem>
                    <MenuItem value="turbid">Turbid</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="urine_ph"
                    name="urine_ph"
                    label="pH"
                    type="number"
                    value={formData.urine_ph}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 4.5, max: 9.0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.urine_ph} min={4.5} max={8.0} unit="" />
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'medium', mb: 1, mt: 2 }}>Chemical Tests</Typography>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="urine_protein"
                    name="urine_protein"
                    label="Protein"
                    value={formData.urine_protein}
                    onChange={onChange}
                    variant="outlined"
                    select
                  >
                    <MenuItem value="negative">Negative</MenuItem>
                    <MenuItem value="trace">Trace</MenuItem>
                    <MenuItem value="1+">1+</MenuItem>
                    <MenuItem value="2+">2+</MenuItem>
                    <MenuItem value="3+">3+</MenuItem>
                    <MenuItem value="4+">4+</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="urine_sugar"
                    name="urine_sugar"
                    label="Glucose"
                    value={formData.urine_sugar}
                    onChange={onChange}
                    variant="outlined"
                    select
                  >
                    <MenuItem value="negative">Negative</MenuItem>
                    <MenuItem value="trace">Trace</MenuItem>
                    <MenuItem value="1+">1+</MenuItem>
                    <MenuItem value="2+">2+</MenuItem>
                    <MenuItem value="3+">3+</MenuItem>
                    <MenuItem value="4+">4+</MenuItem>
                  </TextField>
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'medium', mb: 1, mt: 2 }}>Microscopic Examination</Typography>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="urine_rbcs"
                    name="urine_rbcs"
                    label="RBCs"
                    value={formData.urine_rbcs}
                    onChange={onChange}
                    variant="outlined"
                    select
                  >
                    <MenuItem value="0-2/HPF">0-2/HPF</MenuItem>
                    <MenuItem value="3-5/HPF">3-5/HPF</MenuItem>
                    <MenuItem value="6-10/HPF">6-10/HPF</MenuItem>
                    <MenuItem value="11-20/HPF">11-20/HPF</MenuItem>
                    <MenuItem value=">20/HPF">&gt;20/HPF</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="urine_pus_cells"
                    name="urine_pus_cells"
                    label="Pus Cells (WBCs)"
                    value={formData.urine_pus_cells}
                    onChange={onChange}
                    variant="outlined"
                    select
                  >
                    <MenuItem value="0-5/HPF">0-5/HPF</MenuItem>
                    <MenuItem value="6-10/HPF">6-10/HPF</MenuItem>
                    <MenuItem value="11-20/HPF">11-20/HPF</MenuItem>
                    <MenuItem value="21-50/HPF">21-50/HPF</MenuItem>
                    <MenuItem value=">50/HPF">&gt;50/HPF</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="urine_epithelial_cells"
                    name="urine_epithelial_cells"
                    label="Epithelial Cells"
                    value={formData.urine_epithelial_cells}
                    onChange={onChange}
                    variant="outlined"
                    select
                  >
                    <MenuItem value="few">Few</MenuItem>
                    <MenuItem value="moderate">Moderate</MenuItem>
                    <MenuItem value="many">Many</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={6} md={6}>
                  <TextField
                    fullWidth
                    id="urine_crystals"
                    name="urine_crystals"
                    label="Crystals"
                    value={formData.urine_crystals}
                    onChange={onChange}
                    variant="outlined"
                    select
                  >
                    <MenuItem value="none">None</MenuItem>
                    <MenuItem value="calcium oxalate">Calcium Oxalate</MenuItem>
                    <MenuItem value="uric acid">Uric Acid</MenuItem>
                    <MenuItem value="triple phosphate">Triple Phosphate</MenuItem>
                    <MenuItem value="amorphous phosphates">Amorphous Phosphates</MenuItem>
                    <MenuItem value="amorphous urates">Amorphous Urates</MenuItem>
                    <MenuItem value="other">Other</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={6} md={6}>
                  <TextField
                    fullWidth
                    id="urine_casts"
                    name="urine_casts"
                    label="Casts"
                    value={formData.urine_casts}
                    onChange={onChange}
                    variant="outlined"
                    select
                  >
                    <MenuItem value="none">None</MenuItem>
                    <MenuItem value="hyaline">Hyaline</MenuItem>
                    <MenuItem value="granular">Granular</MenuItem>
                    <MenuItem value="waxy">Waxy</MenuItem>
                    <MenuItem value="cellular">Cellular</MenuItem>
                    <MenuItem value="rbc">RBC</MenuItem>
                    <MenuItem value="wbc">WBC</MenuItem>
                    <MenuItem value="other">Other</MenuItem>
                  </TextField>
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <ScienceIcon /> Miscellaneous Screening
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Cancer Screening"
                contextKey="lab_results_cancer"
              />
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="psa"
                    name="psa"
                    label="PSA (ng/mL)"
                    type="number"
                    value={formData.psa}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                    helperText="Prostate-specific antigen (for male patients)"
                  />
                  <NormalRangeIndicator value={formData.psa} min={0} max={4.0} unit="ng/mL" />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="ca125"
                    name="ca125"
                    label="CA-125 (U/mL)"
                    type="number"
                    value={formData.ca125}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    helperText="Cancer antigen 125 (for ovarian cancer)"
                  />
                  <NormalRangeIndicator value={formData.ca125} min={0} max={35} unit="U/mL" />
                </Grid>
                <Grid item xs={12} sm={12} md={4}>
                  <TextField
                    fullWidth
                    id="cancer_screening_results"
                    name="cancer_screening_results"
                    label="Other Cancer Screening Results"
                    value={formData.cancer_screening_results}
                    onChange={onChange}
                    variant="outlined"
                    multiline
                    rows={2}
                    helperText="Results from mammogram, colonoscopy, etc."
                  />
                </Grid>
              </Grid>
            </FormSection>
          </TabPanel>

          {/* Cognitive Health Tab */}
          <TabPanel value={activeStep} index={5}>
            <FormSection>
              <SectionTitle variant="h6">
                <PsychologyIcon /> Cognitive Health Assessment (Mini-Cog)
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Cognitive Assessment"
                contextKey="mental_health_cognitive"
              />
              <CognitiveHealthAssessment
                value=""
                miniCogWordRecallScore={formData.mini_cog_word_recall_score}
                miniCogClockDrawingScore={formData.mini_cog_clock_drawing_score}
                miniCogWordsUsed={formData.mini_cog_words_used}
                miniCogWordsRecalled={formData.mini_cog_words_recalled}
                miniCogNotes={formData.mini_cog_notes}
                onChange={(value) => {
                  try {
                    // Parse the value to extract the score (for backward compatibility)
                    const cognitiveData = JSON.parse(value);
                    const miniCogScore =
                      (cognitiveData.miniCog.wordRecallScore || 0) +
                      (cognitiveData.miniCog.clockDrawing || 0);

                    setFormData({
                      ...formData,
                      cognitive_impairment_score: miniCogScore.toString()
                    });
                  } catch (e) {
                    // If parsing fails, just ignore
                    console.error("Error parsing cognitive assessment data:", e);
                  }
                }}
                onMiniCogChange={(fields) => {
                  setFormData({
                    ...formData,
                    mini_cog_word_recall_score: fields.mini_cog_word_recall_score,
                    mini_cog_clock_drawing_score: fields.mini_cog_clock_drawing_score,
                    mini_cog_words_used: fields.mini_cog_words_used,
                    mini_cog_words_recalled: fields.mini_cog_words_recalled,
                    mini_cog_notes: fields.mini_cog_notes,
                    cognitive_impairment_score: fields.cognitive_impairment_score
                  });
                }}
              />
            </FormSection>
          </TabPanel>

          {/* Depression Tab */}
          <TabPanel value={activeStep} index={6}>
            <FormSection>
              <SectionTitle variant="h6">
                <MoodIcon /> Depression Assessment (PHQ-9)
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Depression Assessment (PHQ-9)"
                contextKey="mental_health_depression"
              />
              <DepressionAssessment
                value={formData.depression_screening}
                onChange={(value) => {
                  setFormData({
                    ...formData,
                    depression_screening: value
                  });
                }}
                phq9InterestPleasure={formData.phq9_interest_pleasure}
                phq9FeelingDown={formData.phq9_feeling_down}
                phq9SleepIssues={formData.phq9_sleep_issues}
                phq9Tired={formData.phq9_tired}
                phq9Appetite={formData.phq9_appetite}
                phq9FeelingBad={formData.phq9_feeling_bad}
                phq9Concentration={formData.phq9_concentration}
                phq9MovingSpeaking={formData.phq9_moving_speaking}
                phq9ThoughtsHurting={formData.phq9_thoughts_hurting}
                phq9DifficultyLevel={formData.phq9_difficulty_level}
                phq9Notes={formData.phq9_notes}
                depressionScore={formData.depression_score}
                onPhq9Change={(fields) => {
                  setFormData({
                    ...formData,
                    phq9_interest_pleasure: fields.phq9_interest_pleasure,
                    phq9_feeling_down: fields.phq9_feeling_down,
                    phq9_sleep_issues: fields.phq9_sleep_issues,
                    phq9_tired: fields.phq9_tired,
                    phq9_appetite: fields.phq9_appetite,
                    phq9_feeling_bad: fields.phq9_feeling_bad,
                    phq9_concentration: fields.phq9_concentration,
                    phq9_moving_speaking: fields.phq9_moving_speaking,
                    phq9_thoughts_hurting: fields.phq9_thoughts_hurting,
                    phq9_difficulty_level: fields.phq9_difficulty_level,
                    phq9_notes: fields.phq9_notes,
                    depression_score: fields.depression_score,
                    depression_screening: fields.depression_screening
                  });
                }}
              />
            </FormSection>
          </TabPanel>

          {/* Anxiety Tab */}
          <TabPanel value={activeStep} index={7}>
            <FormSection>
              <SectionTitle variant="h6">
                <MoodIcon /> Anxiety Assessment (GAD-7)
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Anxiety Assessment (GAD-7)"
                contextKey="mental_health_anxiety"
              />
              <AnxietyAssessment
                value={formData.anxiety_screening}
                onChange={(value) => {
                  setFormData({
                    ...formData,
                    anxiety_screening: value
                  });
                }}
                gad7FeelingNervous={formData.gad7_feeling_nervous}
                gad7StopWorrying={formData.gad7_stop_worrying}
                gad7WorryingMuch={formData.gad7_worrying_much}
                gad7TroubleRelaxing={formData.gad7_trouble_relaxing}
                gad7Restless={formData.gad7_restless}
                gad7Annoyed={formData.gad7_annoyed}
                gad7FeelingAfraid={formData.gad7_feeling_afraid}
                gad7DifficultyLevel={formData.gad7_difficulty_level}
                gad7Notes={formData.gad7_notes}
                anxietyScore={formData.anxiety_score}
                onGad7Change={(fields) => {
                  setFormData({
                    ...formData,
                    gad7_feeling_nervous: fields.gad7_feeling_nervous,
                    gad7_stop_worrying: fields.gad7_stop_worrying,
                    gad7_worrying_much: fields.gad7_worrying_much,
                    gad7_trouble_relaxing: fields.gad7_trouble_relaxing,
                    gad7_restless: fields.gad7_restless,
                    gad7_annoyed: fields.gad7_annoyed,
                    gad7_feeling_afraid: fields.gad7_feeling_afraid,
                    gad7_difficulty_level: fields.gad7_difficulty_level,
                    gad7_notes: fields.gad7_notes,
                    anxiety_score: fields.anxiety_score,
                    anxiety_screening: fields.anxiety_screening
                  });
                }}
              />
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <PsychologyIcon /> General Mental Health Notes
              </SectionTitle>
              <TextField
                fullWidth
                id="mental_health_assessment"
                name="mental_health_assessment"
                label="General Mental Health Notes"
                multiline
                rows={4}
                value={formData.mental_health_assessment || ""}
                onChange={onChange}
                variant="outlined"
                placeholder="Enter general mental health observations or notes that are not covered by the PHQ-9 or GAD-7 assessments"
              />
            </FormSection>
          </TabPanel>

          {/* Vaccinations Tab */}
          <TabPanel value={activeStep} index={10}>
            <FormSection>
              <SectionTitle variant="h6">
                <VaccinesIcon /> Vaccination Records
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Vaccinations for Older Adults"
                contextKey="vaccinations_older_adults"
              />
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="influenza_vaccination_date"
                    name="influenza_vaccination_date"
                    label="Influenza Vaccine Date"
                    type="date"
                    value={formData.influenza_vaccination_date}
                    onChange={onChange}
                    variant="outlined"
                    InputLabelProps={{ shrink: true }}
                    helperText="Annual flu vaccination"
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="pneumococcal_vaccination_date"
                    name="pneumococcal_vaccination_date"
                    label="Pneumococcal Vaccine Date"
                    type="date"
                    value={formData.pneumococcal_vaccination_date}
                    onChange={onChange}
                    variant="outlined"
                    InputLabelProps={{ shrink: true }}
                    helperText="Pneumonia vaccination"
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="zoster_vaccination_date"
                    name="zoster_vaccination_date"
                    label="Zoster Vaccine Date"
                    type="date"
                    value={formData.zoster_vaccination_date}
                    onChange={onChange}
                    variant="outlined"
                    InputLabelProps={{ shrink: true }}
                    helperText="Shingles vaccination"
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="tdap_vaccination_date"
                    name="tdap_vaccination_date"
                    label="Tdap Vaccine Date"
                    type="date"
                    value={formData.tdap_vaccination_date}
                    onChange={onChange}
                    variant="outlined"
                    InputLabelProps={{ shrink: true }}
                    helperText="Tetanus, diphtheria, pertussis"
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="covid19_vaccination_date"
                    name="covid19_vaccination_date"
                    label="COVID-19 Initial Vaccine Date"
                    type="date"
                    value={formData.covid19_vaccination_date}
                    onChange={onChange}
                    variant="outlined"
                    InputLabelProps={{ shrink: true }}
                    helperText="Initial COVID-19 vaccination"
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="covid19_booster_date"
                    name="covid19_booster_date"
                    label="COVID-19 Booster Date"
                    type="date"
                    value={formData.covid19_booster_date}
                    onChange={onChange}
                    variant="outlined"
                    InputLabelProps={{ shrink: true }}
                    helperText="Most recent COVID-19 booster"
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="hepatitis_a_vaccination_date"
                    name="hepatitis_a_vaccination_date"
                    label="Hepatitis A Vaccine Date"
                    type="date"
                    value={formData.hepatitis_a_vaccination_date}
                    onChange={onChange}
                    variant="outlined"
                    InputLabelProps={{ shrink: true }}
                    helperText="Hepatitis A vaccination"
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="hepatitis_b_vaccination_date"
                    name="hepatitis_b_vaccination_date"
                    label="Hepatitis B Vaccine Date"
                    type="date"
                    value={formData.hepatitis_b_vaccination_date}
                    onChange={onChange}
                    variant="outlined"
                    InputLabelProps={{ shrink: true }}
                    helperText="Hepatitis B vaccination"
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="mmr_vaccination_date"
                    name="mmr_vaccination_date"
                    label="MMR Vaccine Date"
                    type="date"
                    value={formData.mmr_vaccination_date}
                    onChange={onChange}
                    variant="outlined"
                    InputLabelProps={{ shrink: true }}
                    helperText="Measles, mumps, rubella"
                  />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="varicella_vaccination_date"
                    name="varicella_vaccination_date"
                    label="Varicella Vaccine Date"
                    type="date"
                    value={formData.varicella_vaccination_date}
                    onChange={onChange}
                    variant="outlined"
                    InputLabelProps={{ shrink: true }}
                    helperText="Chickenpox vaccination"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="other_vaccinations"
                    name="other_vaccinations"
                    label="Other Vaccinations"
                    value={formData.other_vaccinations}
                    onChange={onChange}
                    variant="outlined"
                    multiline
                    rows={3}
                    helperText="Record any other vaccinations and their dates"
                  />
                </Grid>
              </Grid>
            </FormSection>
          </TabPanel>

          {/* Sleep & Pain Tab */}
          <TabPanel value={activeStep} index={8}>
            <FormSection>
              <BasicSleepInfo
                formData={formData}
                onChange={onChange}
                handleSelectChange={handleSelectChange}
              />
            </FormSection>

            <FormSection>
              <PSQIAssessment
                formData={formData}
                onChange={onChange}
                handleSelectChange={handleSelectChange}
              />
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <PainIcon /> Pain Assessment
              </SectionTitle>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="pain_level"
                    name="pain_level"
                    label="Pain Level (0-10)"
                    type="number"
                    value={formData.pain_level}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, max: 10, step: 1 } }}
                    helperText="0 = No pain, 10 = Worst possible pain"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="pain-character-label">Pain Character</InputLabel>
                    <Select
                      labelId="pain-character-label"
                      id="pain_character"
                      name="pain_character"
                      value={formData.pain_character}
                      onChange={handleSelectChange}
                      label="Pain Character"
                    >
                      <MenuItem value="">Select Pain Character</MenuItem>
                      <MenuItem value="Sharp">Sharp</MenuItem>
                      <MenuItem value="Dull">Dull</MenuItem>
                      <MenuItem value="Burning">Burning</MenuItem>
                      <MenuItem value="Aching">Aching</MenuItem>
                      <MenuItem value="Throbbing">Throbbing</MenuItem>
                      <MenuItem value="Stabbing">Stabbing</MenuItem>
                      <MenuItem value="Cramping">Cramping</MenuItem>
                      <MenuItem value="Shooting">Shooting</MenuItem>
                      <MenuItem value="Tingling">Tingling</MenuItem>
                      <MenuItem value="Pressure">Pressure</MenuItem>
                    </Select>
                    <FormHelperText>Character or quality of the pain</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="pain_location"
                    name="pain_location"
                    label="Pain Location"
                    value={formData.pain_location}
                    onChange={onChange}
                    variant="outlined"
                    multiline
                    rows={2}
                    helperText="Where the pain is located with intensity details (e.g., lower back 7/10, left knee 5/10)"
                  />
                </Grid>



                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="safe_pain_medications"
                    name="safe_pain_medications"
                    label="Safe Pain Medications"
                    value={formData.safe_pain_medications}
                    onChange={onChange}
                    variant="outlined"
                    multiline
                    rows={2}
                    helperText="Pain medications that are safe for this patient"
                  />
                </Grid>
              </Grid>
            </FormSection>
          </TabPanel>

          {/* Frailty Assessment Tab */}
          <TabPanel value={activeStep} index={9}>
            <FormSection>
              <FrailtyAssessment
                formData={formData}
                onChange={onChange}
                handleSelectChange={handleSelectChange}
                handleRadioChange={handleRadioChange}
              />
            </FormSection>

            <FormSection>
              <FallsRiskAssessment
                formData={formData}
                onChange={onChange}
                handleSelectChange={handleSelectChange}
                handleRadioChange={handleRadioChange}
              />
            </FormSection>
          </TabPanel>

          {/* Health Status Tab */}
          <TabPanel value={activeStep} index={11}>
            <FormSection>
              <SectionTitle variant="h6">
                <HealthIcon /> General Health Status
              </SectionTitle>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="activity-level-label">Activity Level</InputLabel>
                    <Select
                      labelId="activity-level-label"
                      id="activity_level"
                      name="activity_level"
                      value={formData.activity_level}
                      onChange={handleSelectChange}
                      label="Activity Level"
                    >
                      <MenuItem value="">Select Activity Level</MenuItem>
                      <MenuItem value="Sedentary">Sedentary</MenuItem>
                      <MenuItem value="Light">Light</MenuItem>
                      <MenuItem value="Moderate">Moderate</MenuItem>
                      <MenuItem value="Active">Active</MenuItem>
                      <MenuItem value="Very Active">Very Active</MenuItem>
                    </Select>
                    <FormHelperText>Patient's typical physical activity level</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="exercise-frequency-label">Exercise Frequency</InputLabel>
                    <Select
                      labelId="exercise-frequency-label"
                      id="exercise_frequency"
                      name="exercise_frequency"
                      value={formData.exercise_frequency}
                      onChange={handleSelectChange}
                      label="Exercise Frequency"
                    >
                      <MenuItem value="">Select Exercise Frequency</MenuItem>
                      <MenuItem value="Never">Never</MenuItem>
                      <MenuItem value="Rarely (1-2 times/month)">Rarely (1-2 times/month)</MenuItem>
                      <MenuItem value="Occasionally (1-2 times/week)">Occasionally (1-2 times/week)</MenuItem>
                      <MenuItem value="Regularly (3-4 times/week)">Regularly (3-4 times/week)</MenuItem>
                      <MenuItem value="Daily">Daily</MenuItem>
                      <MenuItem value="Multiple times daily">Multiple times daily</MenuItem>
                    </Select>
                    <FormHelperText>How often the patient exercises</FormHelperText>
                  </FormControl>
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <RestaurantIcon /> Nutrition & Hydration
              </SectionTitle>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="nutritional-status-label">Nutritional Status</InputLabel>
                    <Select
                      labelId="nutritional-status-label"
                      id="nutritional_status"
                      name="nutritional_status"
                      value={formData.nutritional_status}
                      onChange={handleSelectChange}
                      label="Nutritional Status"
                    >
                      <MenuItem value="">Select Nutritional Status</MenuItem>
                      <MenuItem value="Good">Good</MenuItem>
                      <MenuItem value="Fair">Fair</MenuItem>
                      <MenuItem value="Poor">Poor</MenuItem>
                      <MenuItem value="Malnourished">Malnourished</MenuItem>
                      <MenuItem value="Severely malnourished">Severely malnourished</MenuItem>
                    </Select>
                    <FormHelperText>Patient's current nutritional status</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="dietary-intake-label">Dietary Intake Quality</InputLabel>
                    <Select
                      labelId="dietary-intake-label"
                      id="dietary_intake_quality"
                      name="dietary_intake_quality"
                      value={formData.dietary_intake_quality}
                      onChange={handleSelectChange}
                      label="Dietary Intake Quality"
                    >
                      <MenuItem value="">Select Dietary Intake Quality</MenuItem>
                      <MenuItem value="Excellent">Excellent</MenuItem>
                      <MenuItem value="Good">Good</MenuItem>
                      <MenuItem value="Fair">Fair</MenuItem>
                      <MenuItem value="Poor">Poor</MenuItem>
                      <MenuItem value="Very poor">Very poor</MenuItem>
                    </Select>
                    <FormHelperText>Quality of patient's daily food intake</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="hydration-status-label">Hydration Status</InputLabel>
                    <Select
                      labelId="hydration-status-label"
                      id="hydration_status"
                      name="hydration_status"
                      value={formData.hydration_status}
                      onChange={handleSelectChange}
                      label="Hydration Status"
                    >
                      <MenuItem value="">Select Hydration Status</MenuItem>
                      <MenuItem value="Well Hydrated">Well Hydrated</MenuItem>
                      <MenuItem value="Adequately Hydrated">Adequately Hydrated</MenuItem>
                      <MenuItem value="Mildly Dehydrated">Mildly Dehydrated</MenuItem>
                      <MenuItem value="Moderately Dehydrated">Moderately Dehydrated</MenuItem>
                      <MenuItem value="Severely Dehydrated">Severely Dehydrated</MenuItem>
                      <MenuItem value="Dehydrated">Dehydrated</MenuItem>
                    </Select>
                    <FormHelperText>Patient's current hydration status</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="supplements"
                    name="supplements"
                    label="Supplements"
                    value={formData.supplements}
                    onChange={onChange}
                    variant="outlined"
                    multiline
                    rows={2}
                    helperText="Vitamins, minerals, or other supplements taken regularly"
                  />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <VisibilityIcon /> Sensory Assessment
              </SectionTitle>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="vision-status-label">Vision Status</InputLabel>
                    <Select
                      labelId="vision-status-label"
                      id="vision_status"
                      name="vision_status"
                      value={formData.vision_status}
                      onChange={handleSelectChange}
                      label="Vision Status"
                    >
                      <MenuItem value="">Select Vision Status</MenuItem>
                      <MenuItem value="Normal">Normal</MenuItem>
                      <MenuItem value="Mild impairment">Mild impairment</MenuItem>
                      <MenuItem value="Moderate impairment">Moderate impairment</MenuItem>
                      <MenuItem value="Severe impairment">Severe impairment</MenuItem>
                      <MenuItem value="Legally blind">Legally blind</MenuItem>
                    </Select>
                    <FormHelperText>Patient's current vision status</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="vision-aid-label">Vision Aid</InputLabel>
                    <Select
                      labelId="vision-aid-label"
                      id="use_of_aid_vision"
                      name="use_of_aid_vision"
                      value={formData.use_of_aid_vision}
                      onChange={handleSelectChange}
                      label="Vision Aid"
                    >
                      <MenuItem value="">Select Vision Aid</MenuItem>
                      <MenuItem value="None">None</MenuItem>
                      <MenuItem value="Glasses">Glasses</MenuItem>
                      <MenuItem value="Contact lenses">Contact lenses</MenuItem>
                      <MenuItem value="Magnifier">Magnifier</MenuItem>
                      <MenuItem value="Multiple aids">Multiple aids</MenuItem>
                    </Select>
                    <FormHelperText>Vision aids used by patient</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="hearing-status-label">Hearing Status</InputLabel>
                    <Select
                      labelId="hearing-status-label"
                      id="hearing_status"
                      name="hearing_status"
                      value={formData.hearing_status}
                      onChange={handleSelectChange}
                      label="Hearing Status"
                    >
                      <MenuItem value="">Select Hearing Status</MenuItem>
                      <MenuItem value="Normal">Normal</MenuItem>
                      <MenuItem value="Mild Impairment">Mild Impairment</MenuItem>
                      <MenuItem value="Moderate Impairment">Moderate Impairment</MenuItem>
                      <MenuItem value="Severe Impairment">Severe Impairment</MenuItem>
                      <MenuItem value="Profound Loss">Profound Loss</MenuItem>
                      <MenuItem value="Uses hearing aid">Uses hearing aid</MenuItem>
                    </Select>
                    <FormHelperText>Patient's current hearing status</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="hearing-aid-label">Hearing Aid</InputLabel>
                    <Select
                      labelId="hearing-aid-label"
                      id="use_of_aid_hearing"
                      name="use_of_aid_hearing"
                      value={formData.use_of_aid_hearing}
                      onChange={handleSelectChange}
                      label="Hearing Aid"
                    >
                      <MenuItem value="">Select Hearing Aid</MenuItem>
                      <MenuItem value="None">None</MenuItem>
                      <MenuItem value="Hearing aid">Hearing aid</MenuItem>
                      <MenuItem value="Cochlear implant">Cochlear implant</MenuItem>
                      <MenuItem value="Assistive listening device">Assistive listening device</MenuItem>
                      <MenuItem value="Multiple aids">Multiple aids</MenuItem>
                    </Select>
                    <FormHelperText>Hearing aids used by patient</FormHelperText>
                  </FormControl>
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <PersonIcon /> Social & Environmental Factors
              </SectionTitle>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="social-interaction-label">Social Interaction Levels</InputLabel>
                    <Select
                      labelId="social-interaction-label"
                      id="social_interaction_levels"
                      name="social_interaction_levels"
                      value={formData.social_interaction_levels}
                      onChange={handleSelectChange}
                      label="Social Interaction Levels"
                    >
                      <MenuItem value="">Select Social Interaction Level</MenuItem>
                      <MenuItem value="Very active">Very active</MenuItem>
                      <MenuItem value="Active">Active</MenuItem>
                      <MenuItem value="Regular">Regular</MenuItem>
                      <MenuItem value="Moderate">Moderate</MenuItem>
                      <MenuItem value="Limited">Limited</MenuItem>
                      <MenuItem value="Isolated">Isolated</MenuItem>
                    </Select>
                    <FormHelperText>Patient's level of social engagement</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="social-support-label">Social Support</InputLabel>
                    <Select
                      labelId="social-support-label"
                      id="social_support"
                      name="social_support"
                      value={formData.social_support}
                      onChange={handleSelectChange}
                      label="Social Support"
                    >
                      <MenuItem value="">Select Social Support</MenuItem>
                      <MenuItem value="Strong">Strong (extensive support network)</MenuItem>
                      <MenuItem value="Adequate">Adequate (sufficient support available)</MenuItem>
                      <MenuItem value="Limited">Limited (minimal support available)</MenuItem>
                      <MenuItem value="None">None (no support system)</MenuItem>
                    </Select>
                    <FormHelperText>Level of social support available to patient</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="social_support_network"
                    name="social_support_network"
                    label="Social Support Network"
                    value={formData.social_support_network}
                    onChange={onChange}
                    variant="outlined"
                    multiline
                    rows={2}
                    helperText="Details about family, friends, community support"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="living-situation-label">Living Situation</InputLabel>
                    <Select
                      labelId="living-situation-label"
                      id="living_situation"
                      name="living_situation"
                      value={formData.living_situation}
                      onChange={handleSelectChange}
                      label="Living Situation"
                    >
                      <MenuItem value="">Select Living Situation</MenuItem>
                      <MenuItem value="Lives alone">Lives alone</MenuItem>
                      <MenuItem value="Lives with spouse/partner">Lives with spouse/partner</MenuItem>
                      <MenuItem value="Lives with family">Lives with family</MenuItem>
                      <MenuItem value="Lives with caregiver">Lives with caregiver</MenuItem>
                      <MenuItem value="Assisted living">Assisted living facility</MenuItem>
                      <MenuItem value="Nursing home">Nursing home</MenuItem>
                      <MenuItem value="Other">Other</MenuItem>
                    </Select>
                    <FormHelperText>Patient's current living arrangement</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="living_conditions"
                    name="living_conditions"
                    label="Living Conditions"
                    value={formData.living_conditions}
                    onChange={onChange}
                    variant="outlined"
                    multiline
                    rows={2}
                    helperText="Description of home environment and conditions"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="environmental_risks"
                    name="environmental_risks"
                    label="Environmental Risks"
                    value={formData.environmental_risks}
                    onChange={onChange}
                    variant="outlined"
                    multiline
                    rows={2}
                    helperText="Potential hazards in the patient's environment"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="age-friendly-environment-label">Age-Friendly Environment</InputLabel>
                    <Select
                      labelId="age-friendly-environment-label"
                      id="age_friendly_environment"
                      name="age_friendly_environment"
                      value={formData.age_friendly_environment}
                      onChange={handleSelectChange}
                      label="Age-Friendly Environment"
                    >
                      <MenuItem value="">Select</MenuItem>
                      <MenuItem value="true">Yes</MenuItem>
                      <MenuItem value="false">No</MenuItem>
                    </Select>
                    <FormHelperText>Is the patient's environment adapted for older adults?</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="transportation-access-label">Transportation Access</InputLabel>
                    <Select
                      labelId="transportation-access-label"
                      id="transportation_access"
                      name="transportation_access"
                      value={formData.transportation_access}
                      onChange={handleSelectChange}
                      label="Transportation Access"
                    >
                      <MenuItem value="">Select Transportation Access</MenuItem>
                      <MenuItem value="Independent driver">Independent driver</MenuItem>
                      <MenuItem value="Relies on others">Relies on others</MenuItem>
                      <MenuItem value="Relies on family">Relies on family</MenuItem>
                      <MenuItem value="Public transportation">Public transportation</MenuItem>
                      <MenuItem value="Medical transportation">Medical transportation</MenuItem>
                      <MenuItem value="Limited access">Limited access</MenuItem>
                      <MenuItem value="No access">No access</MenuItem>
                    </Select>
                    <FormHelperText>Patient's access to transportation</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="financial-concern-label">Financial Concerns</InputLabel>
                    <Select
                      labelId="financial-concern-label"
                      id="financial_concern"
                      name="financial_concern"
                      value={formData.financial_concern}
                      onChange={handleSelectChange}
                      label="Financial Concerns"
                    >
                      <MenuItem value="">Select Financial Status</MenuItem>
                      <MenuItem value="None">None</MenuItem>
                      <MenuItem value="Minor">Minor</MenuItem>
                      <MenuItem value="Mild">Mild</MenuItem>
                      <MenuItem value="Moderate">Moderate</MenuItem>
                      <MenuItem value="Significant">Significant</MenuItem>
                      <MenuItem value="Severe">Severe</MenuItem>
                    </Select>
                    <FormHelperText>Level of financial concerns affecting care</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="home_safety_evaluation"
                    name="home_safety_evaluation"
                    label="Home Safety Evaluation"
                    value={formData.home_safety_evaluation}
                    onChange={onChange}
                    variant="outlined"
                    multiline
                    rows={2}
                    helperText="Results of home safety assessment"
                  />
                </Grid>
              </Grid>
            </FormSection>
          </TabPanel>

          {/* Diagnosis & Prescriptions Tab */}
          <TabPanel value={activeStep} index={12}>
            <FormSection>
              <SectionTitle variant="h6">
                <MedicalIcon /> Diagnosis
              </SectionTitle>
              <TextField
                fullWidth
                id="diagnosis"
                name="diagnosis"
                label="Diagnosis"
                multiline
                rows={4}
                value={formData.diagnosis || ""}
                onChange={onChange}
                variant="outlined"
                placeholder="Enter patient diagnosis information"
                helperText="Enter the patient's diagnosis, including primary and secondary conditions"
              />
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <MedicalIcon /> Treatment Plan
              </SectionTitle>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="treatment_plan"
                    name="treatment_plan"
                    label="Treatment Plan"
                    multiline
                    rows={4}
                    value={formData.treatment_plan || ""}
                    onChange={onChange}
                    variant="outlined"
                    placeholder="Enter treatment plan details"
                    helperText="Describe the recommended treatment approach for this patient"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="follow_up_instructions"
                    name="follow_up_instructions"
                    label="Follow-up Instructions"
                    multiline
                    rows={3}
                    value={formData.follow_up_instructions || ""}
                    onChange={onChange}
                    variant="outlined"
                    placeholder="Enter follow-up instructions"
                    helperText="Specify any follow-up appointments, tests, or monitoring needed"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="referrals"
                    name="referrals"
                    label="Referrals"
                    multiline
                    rows={2}
                    value={formData.referrals || ""}
                    onChange={onChange}
                    variant="outlined"
                    placeholder="Enter any referrals to specialists or other healthcare providers"
                    helperText="List any specialists or services the patient should be referred to"
                  />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <MedicationIcon /> Prescriptions
              </SectionTitle>
              <PrescriptionSection
                prescriptions={formData.prescriptions || []}
                onPrescriptionsChange={(prescriptions) => {
                  setFormData(prev => ({
                    ...prev,
                    prescriptions
                  }));
                }}
                patientId={parseInt(id || '0')}
                patientAge={formData.date_of_birth ? calculateAge(formData.date_of_birth) : undefined}
              />

              {/* BEERS Criteria Checker */}
              {formData.prescriptions && formData.prescriptions.length > 0 && (
                <BeersCriteriaChecker
                  medications={formData.prescriptions.map(p => p.medication)}
                  patientId={parseInt(id || '0')}
                  patientAge={formData.date_of_birth ? calculateAge(formData.date_of_birth) : undefined}
                  prescriptions={formData.prescriptions}
                  onOverride={(alert, reason) => {
                    // Find the prescription that matches the alert
                    const updatedPrescriptions = [...(formData.prescriptions || [])];
                    const prescriptionIndex = updatedPrescriptions.findIndex(
                      p => p.medication.toLowerCase() === alert.medication.toLowerCase()
                    );

                    if (prescriptionIndex !== -1) {
                      // Get the current user ID from the auth context
                      const currentUserId = user?.user_id;

                      // Update the prescription with the override information
                      updatedPrescriptions[prescriptionIndex] = {
                        ...updatedPrescriptions[prescriptionIndex],
                        beers_criteria_id: alert.criterion.criteria_id,
                        beers_criteria_name: alert.criterion.medication_name,
                        beers_criteria_category: alert.criterion.category,
                        beers_criteria_recommendation: alert.criterion.recommendation,
                        beers_criteria_rationale: alert.criterion.rationale,
                        beers_override_reason: reason,
                        beers_overridden_by: currentUserId, // Set the user ID who overrode the alert
                        beers_overridden_at: new Date().toISOString()
                      };

                      // Log the user ID for debugging
                      console.log(`Setting beers_overridden_by to user ID: ${currentUserId} for medication: ${alert.medication}`);

                      // Update the form data with the updated prescriptions
                      setFormData(prev => ({
                        ...prev,
                        prescriptions: updatedPrescriptions
                      }));
                    }
                  }}
                />
              )}
            </FormSection>
          </TabPanel>

          {/* Review & Submit Tab */}
          <TabPanel value={activeStep} index={13}>
            <FormSection>
              <SectionTitle variant="h6">
                <AssessmentIcon /> Review Patient Information
              </SectionTitle>
              <Typography variant="body1" paragraph>
                Please review all the information you've entered before submitting. Make sure all required fields are filled correctly.
              </Typography>
              <Alert severity="info" sx={{ mb: 3 }}>
                Once submitted, you'll be redirected to the patient's profile page where you can make additional changes if needed.
              </Alert>

              {/* Form Completion Progress */}
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" gutterBottom>
                  <CheckCircleIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Form Completion Status
                </Typography>

                {/* Define logical sections for completion tracking */}
                {(() => {
                  // Create section data for visualization
                  const sectionData = [
                    // Personal Information
                    'Basic Information',
                    'Medical Assignment',
                    'Contact Details',
                    'Emergency Contact',

                    // Medical Data
                    'Medical Information',
                    'Vital Signs',
                    'Lab Results',

                    // Mental Health
                    'Cognitive Health',
                    'Depression',
                    'Anxiety',

                    // Assessments
                    'Sleep Assessment',
                    'Pain Assessment',
                    'Frailty Assessment',

                    // Other
                    'Vaccinations',
                    'Health Status',
                    'Diagnosis & Prescriptions'
                  ].map((section) => {
                    // Calculate completion for this section
                    const { isComplete, percentage, missingFields, tabIndex } = calculateSectionCompletion(section);

                    // Get validation errors for this section
                    const validationErrors = validateSectionData(section);

                    // Determine status
                    let status: 'complete' | 'partial' | 'incomplete' | 'error' = 'incomplete';
                    if (isComplete) {
                      status = 'complete';
                    } else if (percentage > 0) {
                      status = 'partial';
                    } else if (validationErrors.length > 0) {
                      status = 'error';
                    }

                    // Format missing fields to be more readable
                    const formattedMissingFields = missingFields.map(field => getFieldLabel(field));

                    return {
                      id: section.toLowerCase().replace(/\s+/g, '_'),
                      label: section,
                      status,
                      percentage,
                      missingFields: formattedMissingFields,
                      tabIndex,
                      errorCount: validationErrors.length,
                      validationErrors
                    };
                  });

                  // Function to navigate to a section
                  const handleSectionClick = (sectionId: string) => {
                    const section = sectionData.find(s => s.id === sectionId);
                    if (section) {
                      setActiveStep(section.tabIndex);
                    }
                  };

                  // Import the components
                  const TwoPanelReview = require('../common/TwoPanelReview').default;

                  // Function to navigate to a section
                  const handleNavigate = (tabIndex: number) => {
                    setActiveStep(tabIndex);
                  };

                  return (
                    <Box>
                      {/* Two-Panel Review Layout */}
                      <TwoPanelReview
                        sections={sectionData}
                        onNavigate={handleNavigate}
                      />
                    </Box>
                  );
                })()}
              </Box>

              {/* Validation Summary */}
              {validatePatientData().length > 0 && (
                <Alert severity="warning" sx={{ mb: 3 }}>
                  <AlertTitle>Please address the following issues:</AlertTitle>
                  <List dense sx={{ mt: 1, mb: 0 }}>
                    {validatePatientData().map((error, index) => {
                      // Extract field name from error message
                      const fieldMatch = error.match(/^([A-Za-z\s]+) (is required|must be|format is invalid|value seems)/);
                      const fieldName = fieldMatch ? fieldMatch[1].toLowerCase().replace(/\s+/g, '_') : '';

                      // Determine which tab to navigate to
                      let tabIndex = 0;
                      if (fieldName.includes('name') || fieldName.includes('gender') || fieldName.includes('birth')) {
                        tabIndex = 0; // Basic Information
                      } else if (fieldName.includes('phone') || fieldName.includes('email') || fieldName.includes('emergency')) {
                        tabIndex = 1; // Contact Details
                      } else if (fieldName.includes('height') || fieldName.includes('weight') || fieldName.includes('pressure') || fieldName.includes('temperature')) {
                        tabIndex = 3; // Vital Signs
                      } else if (fieldName.includes('glucose') || fieldName.includes('cholesterol')) {
                        tabIndex = 4; // Lab Results
                      }

                      // Get smart validation suggestion
                      const suggestion = getSmartValidationSuggestion(error, fieldName);

                      return (
                        <ListItem key={index} sx={{
                          py: 0.5,
                          borderLeft: '3px solid',
                          borderColor: 'warning.main',
                          bgcolor: 'warning.lightest',
                          pl: 2,
                          mb: 1,
                          borderRadius: '0 4px 4px 0'
                        }}>
                          <ListItemText
                            primary={error}
                            secondary={suggestion ? (
                              <Typography variant="body2" sx={{ mt: 0.5, color: 'text.secondary', fontStyle: 'italic' }}>
                                <InfoIcon sx={{ fontSize: '0.9rem', mr: 0.5, verticalAlign: 'text-top' }} />
                                Suggestion: {suggestion}
                              </Typography>
                            ) : null}
                          />
                          {fieldName && (
                            <Button
                              size="small"
                              variant="outlined"
                              color="warning"
                              startIcon={<JumpIcon />}
                              onClick={() => navigateToField(tabIndex, fieldName)}
                              sx={{ ml: 2, whiteSpace: 'nowrap' }}
                            >
                              Jump to Field
                            </Button>
                          )}
                        </ListItem>
                      );
                    })}
                  </List>
                </Alert>
              )}

              {/* Changes Summary - Only show in edit mode */}
              {isEditMode && (
                <EnhancedCard sx={{ mb: 3 }}>
                  <EnhancedCardContent>
                    <CardTitle>
                      <EditIcon /> Changes Summary
                    </CardTitle>
                    <Typography variant="body2" paragraph>
                      The following fields have been modified:
                    </Typography>

                    {getChangedFields().length === 0 ? (
                      <Alert severity="info">No changes have been made yet.</Alert>
                    ) : (
                      <TableContainer component={Paper} variant="outlined">
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell><strong>Field</strong></TableCell>
                              <TableCell><strong>Original Value</strong></TableCell>
                              <TableCell><strong>New Value</strong></TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {getChangedFields().map((change, index) => (
                              <TableRow key={index}>
                                <TableCell>{getFieldLabel(change.field)}</TableCell>
                                <TableCell>{formatFieldValue(change.field, change.oldValue)}</TableCell>
                                <TableCell>
                                  <Typography color="primary">
                                    <strong>{formatFieldValue(change.field, change.newValue)}</strong>
                                  </Typography>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    )}
                  </EnhancedCardContent>
                </EnhancedCard>
              )}

              {/* Patient Information Summary */}
              <Box sx={{ mt: 3 }}>
                {/* Basic Information */}
                <EnhancedCard sx={{ mb: 3 }}>
                  <EnhancedCardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                           onClick={() => toggleSectionExpansion('basicInfo')}>
                        {expandedSections.basicInfo ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                        <CardTitle sx={{ mb: 0, ml: 1 }}>
                          <PersonIcon /> Basic Information
                        </CardTitle>
                      </Box>
                      <Button
                        size="small"
                        variant="outlined"
                        color="primary"
                        startIcon={<EditIcon />}
                        onClick={() => navigateToField(0)}
                      >
                        Edit
                      </Button>
                    </Box>

                    {expandedSections.basicInfo && (
                      <Grid container spacing={2} sx={{ mt: 1 }}>
                        <Grid item xs={12} sm={6} md={4}>
                          <Box>
                            <Typography variant="subtitle2" color="text.secondary">Full Name</Typography>
                            <Typography variant="body1">
                              {formData.first_name && formData.last_name
                                ? `${formData.first_name} ${formData.last_name}`
                                : <Typography component="span" color="error">Missing</Typography>}
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                          <Box>
                            <Typography variant="subtitle2" color="text.secondary">Date of Birth</Typography>
                            <Typography variant="body1">
                              {formData.date_of_birth
                                ? formData.date_of_birth
                                : <Typography component="span" color="error">Missing</Typography>}
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                          <Box>
                            <Typography variant="subtitle2" color="text.secondary">Gender</Typography>
                            <Typography variant="body1">
                              {formData.gender
                                ? formData.gender
                                : <Typography component="span" color="error">Missing</Typography>}
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                          <Box>
                            <Typography variant="subtitle2" color="text.secondary">Blood Type</Typography>
                            <Typography variant="body1">{formData.blood_type || 'Not specified'}</Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                          <Box>
                            <Typography variant="subtitle2" color="text.secondary">Assigned Doctor</Typography>
                            <Typography variant="body1">
                              {formData.doctor_id
                                ? doctors.find(d => d.doctor_id.toString() === formData.doctor_id)
                                  ? `Dr. ${doctors.find(d => d.doctor_id.toString() === formData.doctor_id)?.first_name} ${doctors.find(d => d.doctor_id.toString() === formData.doctor_id)?.last_name}`
                                  : formData.doctor_id
                                : 'Not assigned'}
                            </Typography>
                          </Box>
                        </Grid>
                      </Grid>
                    )}
                  </EnhancedCardContent>
                </EnhancedCard>

                {/* Contact Information */}
                <EnhancedCard sx={{ mb: 3 }}>
                  <EnhancedCardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                           onClick={() => toggleSectionExpansion('contactInfo')}>
                        {expandedSections.contactInfo ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                        <CardTitle sx={{ mb: 0, ml: 1 }}>
                          <PhoneIcon /> Contact Information
                        </CardTitle>
                      </Box>
                      <Button
                        size="small"
                        variant="outlined"
                        color="primary"
                        startIcon={<EditIcon />}
                        onClick={() => navigateToField(1)}
                      >
                        Edit
                      </Button>
                    </Box>

                    {expandedSections.contactInfo && (
                      <Grid container spacing={2} sx={{ mt: 1 }}>
                        <Grid item xs={12} sm={6} md={4}>
                          <Box>
                            <Typography variant="subtitle2" color="text.secondary">Phone</Typography>
                            <Typography variant="body1">
                              {formData.phone
                                ? formData.phone
                                : <Typography component="span" color="error">Missing</Typography>}
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                          <Box>
                            <Typography variant="subtitle2" color="text.secondary">Email</Typography>
                            <Typography variant="body1">{formData.email || 'Not provided'}</Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                          <Box>
                            <Typography variant="subtitle2" color="text.secondary">Address</Typography>
                            <Typography variant="body1">{formData.address || 'Not provided'}</Typography>
                          </Box>
                        </Grid>
                      </Grid>
                    )}
                  </EnhancedCardContent>
                </EnhancedCard>

                {/* Emergency Contact */}
                <EnhancedCard sx={{ mb: 3 }}>
                  <EnhancedCardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                           onClick={() => toggleSectionExpansion('emergencyContact')}>
                        {expandedSections.emergencyContact ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                        <CardTitle sx={{ mb: 0, ml: 1 }}>
                          <PhoneIcon /> Emergency Contact
                        </CardTitle>
                      </Box>
                      <Button
                        size="small"
                        variant="outlined"
                        color="primary"
                        startIcon={<EditIcon />}
                        onClick={() => navigateToField(1, 'emergency_contact_name')}
                      >
                        Edit
                      </Button>
                    </Box>

                    {expandedSections.emergencyContact && (
                      <Grid container spacing={2} sx={{ mt: 1 }}>
                        <Grid item xs={12} sm={6} md={3}>
                          <Box>
                            <Typography variant="subtitle2" color="text.secondary">Name</Typography>
                            <Typography variant="body1">{formData.emergency_contact_name || 'Not provided'}</Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                          <Box>
                            <Typography variant="subtitle2" color="text.secondary">Phone</Typography>
                            <Typography variant="body1">{formData.emergency_contact_phone || 'Not provided'}</Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                          <Box>
                            <Typography variant="subtitle2" color="text.secondary">Relationship</Typography>
                            <Typography variant="body1">{formData.emergency_contact_relationship || 'Not provided'}</Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                          <Box>
                            <Typography variant="subtitle2" color="text.secondary">Updated</Typography>
                            <Typography variant="body1">{formData.emergency_contact_updated === 'yes' ? 'Yes' : formData.emergency_contact_updated === 'no' ? 'No' : 'Not specified'}</Typography>
                          </Box>
                        </Grid>
                      </Grid>
                    )}
                  </EnhancedCardContent>
                </EnhancedCard>

                {/* Vital Signs */}
                <EnhancedCard sx={{ mb: 3 }}>
                  <EnhancedCardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
                           onClick={() => toggleSectionExpansion('vitalSigns')}>
                        {expandedSections.vitalSigns ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                        <CardTitle sx={{ mb: 0, ml: 1 }}>
                          <FavoriteIcon /> Vital Signs
                        </CardTitle>
                      </Box>
                      <Button
                        size="small"
                        variant="outlined"
                        color="primary"
                        startIcon={<EditIcon />}
                        onClick={() => navigateToField(3)}
                      >
                        Edit
                      </Button>
                    </Box>

                    {expandedSections.vitalSigns && (
                      <Grid container spacing={2} sx={{ mt: 1 }}>
                        <Grid item xs={12} sm={6} md={3}>
                          <Box>
                            <Typography variant="subtitle2" color="text.secondary">Height</Typography>
                            <Typography variant="body1">{formData.height ? `${formData.height} cm` : 'Not recorded'}</Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                          <Box>
                            <Typography variant="subtitle2" color="text.secondary">Weight</Typography>
                            <Typography variant="body1">{formData.weight ? `${formData.weight} kg` : 'Not recorded'}</Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                          <Box>
                            <Typography variant="subtitle2" color="text.secondary">BMI</Typography>
                            <Typography variant="body1">{formData.bmi || 'Not calculated'}</Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                          <Box>
                            <Typography variant="subtitle2" color="text.secondary">Temperature</Typography>
                            <Typography variant="body1">{formData.temperature ? `${formData.temperature} °C` : 'Not recorded'}</Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                          <Box>
                            <Typography variant="subtitle2" color="text.secondary">Blood Pressure (Sitting)</Typography>
                            <Typography variant="body1">
                              {formData.sitting_bp_systolic && formData.sitting_bp_diastolic
                                ? `${formData.sitting_bp_systolic}/${formData.sitting_bp_diastolic} mmHg`
                                : 'Not recorded'}
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                          <Box>
                            <Typography variant="subtitle2" color="text.secondary">Heart Rate (Sitting)</Typography>
                            <Typography variant="body1">
                              {formData.sitting_heart_rate
                                ? `${formData.sitting_heart_rate} bpm`
                                : <Typography component="span" color="warning.main">Not recorded</Typography>}
                            </Typography>
                          </Box>
                        </Grid>
                        <Grid item xs={12} sm={6} md={3}>
                          <Box>
                            <Typography variant="subtitle2" color="text.secondary">Blood Glucose</Typography>
                            <Typography variant="body1">{formData.blood_glucose ? `${formData.blood_glucose} mg/dL` : 'Not recorded'}</Typography>
                          </Box>
                        </Grid>
                      </Grid>
                    )}
                  </EnhancedCardContent>
                </EnhancedCard>

                {/* Diagnosis & Prescriptions */}
                <EnhancedCard sx={{ mb: 3 }}>
                  <EnhancedCardContent>
                    <CardTitle>
                      <MedicationIcon /> Diagnosis & Prescriptions
                    </CardTitle>

                    {/* Diagnosis */}
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="subtitle2" color="text.secondary">Diagnosis</Typography>
                      <Typography variant="body1">
                        {formData.diagnosis || 'No diagnosis provided'}
                      </Typography>
                    </Box>

                    {/* Treatment Plan */}
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="subtitle2" color="text.secondary">Treatment Plan</Typography>
                      <Typography variant="body1">
                        {formData.treatment_plan || 'No treatment plan provided'}
                      </Typography>
                    </Box>

                    {/* Follow-up Instructions */}
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="subtitle2" color="text.secondary">Follow-up Instructions</Typography>
                      <Typography variant="body1">
                        {formData.follow_up_instructions || 'No follow-up instructions provided'}
                      </Typography>
                    </Box>

                    {/* Referrals */}
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="subtitle2" color="text.secondary">Referrals</Typography>
                      <Typography variant="body1">
                        {formData.referrals || 'No referrals provided'}
                      </Typography>
                    </Box>

                    {/* Prescriptions */}
                    <Typography variant="subtitle2" color="text.secondary" sx={{ mt: 2, mb: 1 }}>Prescriptions</Typography>
                    <Grid container spacing={2}>
                      {formData.prescriptions && formData.prescriptions.length > 0 ? (
                        formData.prescriptions.map((prescription, index) => (
                          <Grid item xs={12} key={index}>
                            <Box sx={{ mb: 1, p: 1, border: '1px solid #eee', borderRadius: 1 }}>
                              <Typography variant="subtitle2" color="primary">
                                {prescription.medication} - {prescription.dosage}
                              </Typography>
                              <Typography variant="body2">
                                Frequency: {prescription.frequency} | Duration: {prescription.duration}
                              </Typography>
                              {/* Notes */}
                              <Box sx={{ mt: 1 }}>
                                <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 'bold' }}>
                                  Notes:
                                </Typography>
                                <Typography variant="body2" color="text.secondary">
                                  {prescription.notes || 'None'}
                                </Typography>
                              </Box>

                              {/* BEERS Override */}
                              <Box sx={{ mt: 1 }}>
                                <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 'bold' }}>
                                  BEERS Override:
                                </Typography>
                                {prescription.beers_override_reason ? (
                                  <Box sx={{ display: 'flex', flexDirection: 'column', mt: 0.5 }}>
                                    <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                                      <Chip
                                        icon={<WarningIcon fontSize="small" />}
                                        label="Override"
                                        size="small"
                                        color="warning"
                                        sx={{ mr: 1, mt: 0.3 }}
                                      />
                                      <Typography variant="body2" color="text.secondary">
                                        <strong>Reason:</strong> {prescription.beers_override_reason}
                                      </Typography>
                                    </Box>
                                    {prescription.overridden_by_username && (
                                      <Typography variant="body2" color="text.secondary" sx={{ mt: 1, fontSize: '0.8rem' }}>
                                        Overridden by {prescription.overridden_by_username} on {new Date(prescription.beers_overridden_at || '').toLocaleDateString()}
                                      </Typography>
                                    )}
                                  </Box>
                                ) : (
                                  <Typography variant="body2" color="text.secondary">
                                    None
                                  </Typography>
                                )}
                              </Box>
                            </Box>
                          </Grid>
                        ))
                      ) : (
                        <Grid item xs={12}>
                          <Typography variant="body1" color="text.secondary">
                            No prescriptions added
                          </Typography>
                        </Grid>
                      )}
                    </Grid>
                  </EnhancedCardContent>
                </EnhancedCard>

                {/* Medical Information */}
                <EnhancedCard sx={{ mb: 3 }}>
                  <EnhancedCardContent>
                    <CardTitle>
                      <MedicalIcon /> Medical Information
                    </CardTitle>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <Box>
                          <Typography variant="subtitle2" color="text.secondary">Medical History</Typography>
                          <Typography variant="body1">{formData.medical_history || 'Not provided'}</Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={12}>
                        <Box>
                          <Typography variant="subtitle2" color="text.secondary">Allergies</Typography>
                          <Typography variant="body1">{formData.allergies || 'None reported'}</Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </EnhancedCardContent>
                </EnhancedCard>

                {/* Mental Health Assessments */}
                <EnhancedCard sx={{ mb: 3 }}>
                  <EnhancedCardContent>
                    <CardTitle>
                      <PsychologyIcon /> Mental Health Assessments
                    </CardTitle>

                    {/* Cognitive Health */}
                    <Box sx={{ mb: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid rgba(0, 0, 0, 0.08)' }}>
                      <Typography variant="subtitle1" color="primary" gutterBottom>
                        <PsychologyIcon sx={{ fontSize: '1rem', mr: 1, verticalAlign: 'text-bottom' }} />
                        Cognitive Health (Mini-Cog)
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6} md={4}>
                          <Typography variant="subtitle2" color="text.secondary">Word Recall Score</Typography>
                          <Typography variant="body1">{formData.mini_cog_word_recall_score ? `${formData.mini_cog_word_recall_score}/3` : 'Not assessed'}</Typography>
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                          <Typography variant="subtitle2" color="text.secondary">Clock Drawing Score</Typography>
                          <Typography variant="body1">{formData.mini_cog_clock_drawing_score ? `${formData.mini_cog_clock_drawing_score}/2` : 'Not assessed'}</Typography>
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                          <Typography variant="subtitle2" color="text.secondary">Total Score</Typography>
                          <Typography variant="body1">
                            {formData.cognitive_impairment_score ? (
                              <>
                                {formData.cognitive_impairment_score}/5
                                <Chip
                                  size="small"
                                  label={parseInt(formData.cognitive_impairment_score) <= 2 ? "Positive screen" : "Negative screen"}
                                  color={parseInt(formData.cognitive_impairment_score) <= 2 ? "warning" : "success"}
                                  sx={{ ml: 1 }}
                                />
                              </>
                            ) : 'Not assessed'}
                          </Typography>
                        </Grid>
                        {formData.mini_cog_notes && (
                          <Grid item xs={12}>
                            <Typography variant="subtitle2" color="text.secondary">Notes</Typography>
                            <Typography variant="body2">{formData.mini_cog_notes}</Typography>
                          </Grid>
                        )}
                      </Grid>
                    </Box>

                    {/* Depression */}
                    <Box sx={{ mb: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid rgba(0, 0, 0, 0.08)' }}>
                      <Typography variant="subtitle1" color="primary" gutterBottom>
                        <MoodIcon sx={{ fontSize: '1rem', mr: 1, verticalAlign: 'text-bottom' }} />
                        Depression Assessment (PHQ-9)
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">PHQ-9 Score</Typography>
                          <Typography variant="body1">
                            {formData.depression_score ? (
                              <>
                                {formData.depression_score}/27
                                <Chip
                                  size="small"
                                  label={
                                    parseInt(formData.depression_score) >= 20 ? "Severe depression" :
                                    parseInt(formData.depression_score) >= 15 ? "Moderately severe depression" :
                                    parseInt(formData.depression_score) >= 10 ? "Moderate depression" :
                                    parseInt(formData.depression_score) >= 5 ? "Mild depression" :
                                    "Minimal depression"
                                  }
                                  color={
                                    parseInt(formData.depression_score) >= 10 ? "error" :
                                    parseInt(formData.depression_score) >= 5 ? "warning" :
                                    "success"
                                  }
                                  sx={{ ml: 1 }}
                                />
                              </>
                            ) : 'Not assessed'}
                          </Typography>
                        </Grid>
                        {formData.phq9_notes && (
                          <Grid item xs={12}>
                            <Typography variant="subtitle2" color="text.secondary">Notes</Typography>
                            <Typography variant="body2">{formData.phq9_notes}</Typography>
                          </Grid>
                        )}
                      </Grid>
                    </Box>

                    {/* Anxiety */}
                    <Box sx={{ p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid rgba(0, 0, 0, 0.08)' }}>
                      <Typography variant="subtitle1" color="primary" gutterBottom>
                        <MoodIcon sx={{ fontSize: '1rem', mr: 1, verticalAlign: 'text-bottom' }} />
                        Anxiety Assessment (GAD-7)
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">GAD-7 Score</Typography>
                          <Typography variant="body1">
                            {formData.anxiety_score ? (
                              <>
                                {formData.anxiety_score}/21
                                <Chip
                                  size="small"
                                  label={
                                    parseInt(formData.anxiety_score) >= 15 ? "Severe anxiety" :
                                    parseInt(formData.anxiety_score) >= 10 ? "Moderate anxiety" :
                                    parseInt(formData.anxiety_score) >= 5 ? "Mild anxiety" :
                                    "Minimal anxiety"
                                  }
                                  color={
                                    parseInt(formData.anxiety_score) >= 10 ? "error" :
                                    parseInt(formData.anxiety_score) >= 5 ? "warning" :
                                    "success"
                                  }
                                  sx={{ ml: 1 }}
                                />
                              </>
                            ) : 'Not assessed'}
                          </Typography>
                        </Grid>
                        {formData.gad7_notes && (
                          <Grid item xs={12}>
                            <Typography variant="subtitle2" color="text.secondary">Notes</Typography>
                            <Typography variant="body2">{formData.gad7_notes}</Typography>
                          </Grid>
                        )}
                      </Grid>
                    </Box>

                    {/* General Mental Health Notes */}
                    {formData.mental_health_assessment && (
                      <Box sx={{ mt: 3 }}>
                        <Typography variant="subtitle2" color="text.secondary">General Mental Health Notes</Typography>
                        <Typography variant="body2">{formData.mental_health_assessment}</Typography>
                      </Box>
                    )}
                  </EnhancedCardContent>
                </EnhancedCard>

                {/* Other Assessments Summary */}
                <EnhancedCard sx={{ mb: 3 }}>
                  <EnhancedCardContent>
                    <CardTitle>
                      <AssessmentIcon /> Assessments Summary
                    </CardTitle>

                    {/* Sleep Assessment */}
                    <Box sx={{ mb: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: '1px solid rgba(0, 0, 0, 0.08)' }}>
                      <Typography variant="subtitle1" color="primary" gutterBottom>
                        <BedIcon sx={{ fontSize: '1rem', mr: 1, verticalAlign: 'text-bottom' }} />
                        Sleep Assessment (PSQI)
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6} md={4}>
                          <Typography variant="subtitle2" color="text.secondary">PSQI Total Score</Typography>
                          <Typography variant="body1">
                            {formData.psqi_total_score ? (
                              <>
                                {formData.psqi_total_score}/21
                                <Chip
                                  size="small"
                                  label={parseInt(formData.psqi_total_score) > 5 ? "Poor sleep quality" : "Good sleep quality"}
                                  color={parseInt(formData.psqi_total_score) > 5 ? "warning" : "success"}
                                  sx={{ ml: 1 }}
                                />
                              </>
                            ) : 'Not assessed'}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                          <Typography variant="subtitle2" color="text.secondary">Sleep Quality</Typography>
                          <Typography variant="body1">{formData.sleep_quality || 'Not assessed'}</Typography>
                        </Grid>
                        <Grid item xs={12} sm={6} md={4}>
                          <Typography variant="subtitle2" color="text.secondary">Sleep Duration</Typography>
                          <Typography variant="body1">{formData.sleep_duration || 'Not assessed'}</Typography>
                        </Grid>
                        {formData.sleep_disturbances && (
                          <Grid item xs={12}>
                            <Typography variant="subtitle2" color="text.secondary">Sleep Disturbances</Typography>
                            <Typography variant="body2">{formData.sleep_disturbances}</Typography>
                          </Grid>
                        )}
                      </Grid>
                    </Box>

                    {/* Other Assessments */}
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6} md={4}>
                        <Box>
                          <Typography variant="subtitle2" color="text.secondary">Frailty Risk Level</Typography>
                          <Typography variant="body1">{formData.frat_risk_level || 'Not assessed'}</Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={12} sm={6} md={4}>
                        <Box>
                          <Typography variant="subtitle2" color="text.secondary">Pain Level</Typography>
                          <Typography variant="body1">{formData.pain_level ? `${formData.pain_level}/10` : 'Not assessed'}</Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </EnhancedCardContent>
                </EnhancedCard>
              </Box>
            </FormSection>
          </TabPanel>

          {/* Form Actions */}
          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
            {/* Left side - Back button or Cancel */}
            <Box>
              {activeStep > 0 ? (
                <Button
                  variant="outlined"
                  onClick={handleBack}
                  startIcon={<BackIcon />}
                  sx={{ minWidth: 120 }}
                >
                  Back
                </Button>
              ) : (
                <Button
                  variant="outlined"
                  color="secondary"
                  component={Link}
                  to="/patients"
                  sx={{ minWidth: 120 }}
                >
                  Cancel
                </Button>
              )}
            </Box>

            {/* Right side - Next or Submit */}
            <Box>
              {activeStep < steps.length - 1 ? (
                <Button
                  variant="contained"
                  color="primary"
                  onClick={handleNext}
                  endIcon={<NextIcon />}
                  sx={{ minWidth: 120 }}
                >
                  Next
                </Button>
              ) : (
                <Button
                  variant="contained"
                  color="primary"
                  type="submit"
                  disabled={submitting}
                  startIcon={submitting ? <CircularProgress size={20} color="inherit" /> : <SaveIcon />}
                  sx={{
                    minWidth: 180,
                    fontSize: '1rem',
                    py: 1.2,
                    boxShadow: 3
                  }}
                >
                  {submitting ? 'Saving...' : isEditMode ? 'Update Patient' : 'Save Patient'}
                </Button>
              )}
            </Box>
          </Box>
        </form>
      </StyledPaper>
    </Container>
  );
};

export default PatientForm;
