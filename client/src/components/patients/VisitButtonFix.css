/* Fix for the "View Visit Details" button in dark mode */
.dark-mode .MuiButton-containedSecondary {
  background-color: #D97B3A !important; /* Orange from logo */
  color: #FFFFFF !important;
}

.dark-mode .MuiButton-containedSecondary:hover {
  background-color: #F2A65A !important; /* Light Orange from logo */
}

/* Fix for the "Edit Patient" button in dark mode */
.dark-mode .patient-detail-container .MuiButton-outlined {
  border-color: rgba(255, 255, 255, 0.2) !important;
  color: #FFFFFF !important;
}

/* Fix for the "Edit Patient" link */
.dark-mode a[href*="/patients/safe-edit/"] {
  color: #FFFFFF !important;
}
