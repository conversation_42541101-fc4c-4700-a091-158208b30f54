import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  Button,
  useTheme,
  alpha,
  Chip,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import { Link } from 'react-router-dom';
import PsychologyIcon from '@mui/icons-material/Psychology';
import SentimentVeryDissatisfiedIcon from '@mui/icons-material/SentimentVeryDissatisfied';
import SentimentDissatisfiedIcon from '@mui/icons-material/SentimentDissatisfied';
import WarningIcon from '@mui/icons-material/Warning';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import InfoIcon from '@mui/icons-material/Info';
import AddCircleIcon from '@mui/icons-material/AddCircle';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import NoteIcon from '@mui/icons-material/Note';
import MedicationIcon from '@mui/icons-material/Medication';
import { getMiniCogRecommendation, getPhq9TreatmentRecommendation, getGad7TreatmentRecommendation } from '../../utils/mentalHealthUtils';

interface MentalHealthTabProps {
  patient: any;
  recentVisit: any;
}

// Status indicator component
const StatusIndicator = ({ status }: { status: 'normal' | 'warning' | 'abnormal' | null | undefined }) => {
  const theme = useTheme();
  if (!status) return null;

  const getStatusColor = () => {
    switch (status) {
      case 'abnormal':
        return theme.palette.error.main;
      case 'warning':
        return theme.palette.warning.main;
      default:
        return theme.palette.success.main;
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'abnormal':
        return <WarningIcon fontSize="small" />;
      case 'warning':
        return <InfoIcon fontSize="small" />;
      default:
        return <CheckCircleIcon fontSize="small" />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'abnormal':
        return 'Needs attention';
      case 'warning':
        return 'Monitor';
      default:
        return 'Normal';
    }
  };

  return (
    <Box sx={{
      display: 'flex',
      alignItems: 'center',
      gap: 0.5,
      color: getStatusColor(),
      fontSize: '0.875rem',
      mt: 0.5
    }}>
      {getStatusIcon()}
      <Typography variant="body2" sx={{ color: getStatusColor() }}>
        {getStatusText()}
      </Typography>
    </Box>
  );
};

// Enhanced assessment card component
const EnhancedAssessmentCard = ({
  title,
  score,
  maxScore,
  interpretation,
  clinicalNotes,
  treatmentRecommendation,
  icon,
  status,
  dataSource = 'patient'
}: {
  title: string;
  score: number | null | undefined;
  maxScore: number;
  interpretation?: string;
  clinicalNotes?: string;
  treatmentRecommendation?: string;
  icon: React.ReactNode;
  status?: 'normal' | 'warning' | 'abnormal' | null;
  dataSource?: 'visit' | 'patient';
}) => {
  const theme = useTheme();

  // Get status color for styling
  const getStatusColor = () => {
    if (!status) return theme.palette.grey[500];
    switch (status) {
      case 'abnormal':
        return theme.palette.error.main;
      case 'warning':
        return theme.palette.warning.main;
      default:
        return theme.palette.success.main;
    }
  };

  // Format score display
  const scoreDisplay = score !== null && score !== undefined 
    ? `${score}/${maxScore}` 
    : (dataSource === 'visit' ? 'Not assessed' : 'Not available');

  return (
    <Paper
      elevation={0}
      sx={{
        height: '100%',
        borderRadius: 2,
        bgcolor: theme.palette.background.paper,
        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        overflow: 'hidden',
        '&:hover': {
          boxShadow: `0 4px 8px ${alpha(theme.palette.primary.main, 0.1)}`,
          borderColor: alpha(theme.palette.primary.main, 0.2),
        }
      }}
    >
      {/* Header section with score and status */}
      <Box sx={{ 
        p: 2, 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        bgcolor: alpha(theme.palette.primary.main, 0.03)
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
          <Box
            sx={{
              width: 40,
              height: 40,
              borderRadius: 1,
              bgcolor: alpha(theme.palette.primary.main, 0.1),
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: theme.palette.primary.main
            }}
          >
            {icon}
          </Box>
          <Box>
            <Typography variant="h6" sx={{ fontWeight: 600, fontSize: '1rem' }}>
              {title}
            </Typography>
            {score !== null && score !== undefined && (
              <Chip 
                label={scoreDisplay} 
                size="small" 
                sx={{ 
                  mt: 0.5, 
                  fontWeight: 600,
                  bgcolor: alpha(getStatusColor(), 0.1),
                  color: getStatusColor(),
                  border: `1px solid ${alpha(getStatusColor(), 0.3)}`
                }} 
              />
            )}
          </Box>
        </Box>
        
        {status && <StatusIndicator status={status} />}
      </Box>

      {/* Content section with interpretation and notes */}
      {score !== null && score !== undefined ? (
        <Box sx={{ p: 0 }}>
          {/* Interpretation section */}
          <Box sx={{ p: 2, borderBottom: interpretation && (clinicalNotes || treatmentRecommendation) ? `1px solid ${alpha(theme.palette.divider, 0.1)}` : 'none' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
              <InfoIcon fontSize="small" color="primary" />
              <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 600 }}>
                Interpretation
              </Typography>
            </Box>
            <Typography variant="body1">
              {interpretation || "No interpretation available"}
            </Typography>
          </Box>

          {/* Clinical Notes (collapsible) */}
          {clinicalNotes && (
            <Accordion disableGutters elevation={0} sx={{ 
              '&:before': { display: 'none' },
              borderBottom: treatmentRecommendation ? `1px solid ${alpha(theme.palette.divider, 0.1)}` : 'none'
            }}>
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                sx={{ px: 2, py: 0.5 }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <NoteIcon fontSize="small" color="primary" />
                  <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 600 }}>
                    Clinical Notes
                  </Typography>
                </Box>
              </AccordionSummary>
              <AccordionDetails sx={{ px: 2, pt: 0, pb: 2 }}>
                <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                  {clinicalNotes}
                </Typography>
              </AccordionDetails>
            </Accordion>
          )}

          {/* Treatment Recommendations (collapsible) */}
          {treatmentRecommendation && (
            <Accordion disableGutters elevation={0} sx={{ '&:before': { display: 'none' } }}>
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                sx={{ px: 2, py: 0.5 }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <MedicationIcon fontSize="small" color="primary" />
                  <Typography variant="body2" color="text.secondary" sx={{ fontWeight: 600 }}>
                    Treatment Recommendation
                  </Typography>
                </Box>
              </AccordionSummary>
              <AccordionDetails sx={{ px: 2, pt: 0, pb: 2 }}>
                <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
                  {treatmentRecommendation}
                </Typography>
              </AccordionDetails>
            </Accordion>
          )}
        </Box>
      ) : (
        <Box sx={{ p: 2, display: 'flex', alignItems: 'center', gap: 1, color: 'text.secondary' }}>
          <InfoIcon sx={{ fontSize: '1rem' }} />
          <Typography variant="body1">
            {dataSource === 'visit' ? 'Not measured in this visit' : 'No assessment data available'}
          </Typography>
        </Box>
      )}
    </Paper>
  );
};

// Simple metric card component
const MetricCard = ({
  title,
  value,
  icon,
  status,
  additionalInfo,
  dataSource = 'patient'
}: {
  title: string;
  value: any;
  icon: React.ReactNode;
  status?: 'normal' | 'warning' | 'abnormal' | null;
  additionalInfo?: string;
  dataSource?: 'visit' | 'patient';
}) => {
  const theme = useTheme();

  return (
    <Paper
      elevation={0}
      sx={{
        p: 2,
        height: '100%',
        display: 'flex',
        alignItems: 'flex-start',
        gap: 2,
        borderRadius: 2,
        bgcolor: theme.palette.background.paper,
        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        '&:hover': {
          boxShadow: `0 4px 8px ${alpha(theme.palette.primary.main, 0.1)}`,
          borderColor: alpha(theme.palette.primary.main, 0.2),
        }
      }}
    >
      <Box
        sx={{
          width: 40,
          height: 40,
          borderRadius: 1,
          bgcolor: alpha(theme.palette.primary.main, 0.1),
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: theme.palette.primary.main
        }}
      >
        {icon}
      </Box>

      <Box sx={{ flex: 1 }}>
        <Typography variant="h6" sx={{ fontWeight: 600, mb: 0.5, fontSize: '1rem' }}>
          {title}
        </Typography>

        <Typography variant="body1" sx={{ mb: 0.5, fontSize: '1.125rem', fontWeight: 500 }}>
          {value !== null && value !== undefined ? value : (dataSource === 'visit' ? 'Not assessed' : 'Not available')}
        </Typography>

        {status && <StatusIndicator status={status} />}

        {additionalInfo && (
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1, fontStyle: 'italic' }}>
            {additionalInfo}
          </Typography>
        )}
      </Box>
    </Paper>
  );
};

const MentalHealthTab: React.FC<MentalHealthTabProps> = ({ patient, recentVisit }) => {
  const theme = useTheme();
  
  // Determine data source - use recent visit data if available, otherwise use patient data
  const dataSource = recentVisit ? 'visit' : 'patient';
  const data = recentVisit || patient;
  
  // Calculate scores
  const miniCogWordRecallScore = data.mini_cog_word_recall_score ? parseInt(data.mini_cog_word_recall_score) : null;
  const miniCogClockDrawingScore = data.mini_cog_clock_drawing_score ? parseInt(data.mini_cog_clock_drawing_score) : null;
  const miniCogTotalScore = (miniCogWordRecallScore !== null && miniCogClockDrawingScore !== null) 
    ? miniCogWordRecallScore + miniCogClockDrawingScore 
    : null;
  
  const depressionScore = data.depression_score ? parseInt(data.depression_score) : null;
  const anxietyScore = data.anxiety_score ? parseInt(data.anxiety_score) : null;

  // Determine status for each assessment
  const getMiniCogStatus = (score: number | null): 'normal' | 'warning' | 'abnormal' | null => {
    if (score === null) return null;
    return score <= 2 ? 'abnormal' : 'normal';
  };

  const getPhq9Status = (score: number | null): 'normal' | 'warning' | 'abnormal' | null => {
    if (score === null) return null;
    if (score <= 4) return 'normal';
    if (score <= 9) return 'warning';
    return 'abnormal';
  };

  const getGad7Status = (score: number | null): 'normal' | 'warning' | 'abnormal' | null => {
    if (score === null) return null;
    if (score <= 4) return 'normal';
    if (score <= 9) return 'warning';
    return 'abnormal';
  };

  // Get interpretations
  const getMiniCogInterpretation = (score: number | null): string | undefined => {
    if (score === null) return undefined;
    return score <= 2 ? "Positive screen for cognitive impairment" : "Negative screen for cognitive impairment";
  };

  const getPhq9Interpretation = (score: number | null): string | undefined => {
    if (score === null) return undefined;
    if (score >= 0 && score <= 4) return "Minimal or no depression";
    if (score >= 5 && score <= 9) return "Mild depression";
    if (score >= 10 && score <= 14) return "Moderate depression";
    if (score >= 15 && score <= 19) return "Moderately severe depression";
    if (score >= 20) return "Severe depression";
    return "Invalid score";
  };

  const getGad7Interpretation = (score: number | null): string | undefined => {
    if (score === null) return undefined;
    if (score >= 0 && score <= 4) return "Minimal anxiety";
    if (score >= 5 && score <= 9) return "Mild anxiety";
    if (score >= 10 && score <= 14) return "Moderate anxiety";
    if (score >= 15) return "Severe anxiety";
    return "Invalid score";
  };

  // Check if we have any mental health data
  const hasMentalHealthData = miniCogTotalScore !== null || depressionScore !== null || anxietyScore !== null;

  // Get assessment date if available
  const assessmentDate = recentVisit ? new Date(recentVisit.visit_date).toLocaleDateString() : '';

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="body1" sx={{ mb: 3 }}>
        This tab displays mental health assessment results for the patient, including cognitive function, depression, and anxiety screenings.
        The most recent values are shown, prioritizing data from the latest visit when available.
        {assessmentDate && ` Last assessment date: ${assessmentDate}`}
      </Typography>

      {/* Cognitive Assessment Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Cognitive Assessment
      </Typography>
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={8}>
          <EnhancedAssessmentCard
            title="Mini-Cog Assessment"
            score={miniCogTotalScore}
            maxScore={5}
            icon={<PsychologyIcon />}
            status={getMiniCogStatus(miniCogTotalScore)}
            interpretation={getMiniCogInterpretation(miniCogTotalScore)}
            clinicalNotes={data.mini_cog_notes}
            treatmentRecommendation={miniCogTotalScore !== null ? getMiniCogRecommendation(miniCogTotalScore) : undefined}
            dataSource={dataSource}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <MetricCard
                title="Word Recall Score"
                value={miniCogWordRecallScore !== null ? `${miniCogWordRecallScore}/3` : 'Not available'}
                icon={<PsychologyIcon />}
                additionalInfo="Ability to recall 3 words after a brief delay"
                dataSource={dataSource}
              />
            </Grid>
            <Grid item xs={12}>
              <MetricCard
                title="Clock Drawing Score"
                value={miniCogClockDrawingScore !== null ? `${miniCogClockDrawingScore}/2` : 'Not available'}
                icon={<PsychologyIcon />}
                additionalInfo="Ability to draw a clock showing a specific time"
                dataSource={dataSource}
              />
            </Grid>
          </Grid>
        </Grid>
      </Grid>

      {/* Depression Assessment Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Depression Assessment
      </Typography>
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={8}>
          <EnhancedAssessmentCard
            title="PHQ-9 Depression Screening"
            score={depressionScore}
            maxScore={27}
            icon={<SentimentVeryDissatisfiedIcon />}
            status={getPhq9Status(depressionScore)}
            interpretation={getPhq9Interpretation(depressionScore)}
            clinicalNotes={data.phq9_notes}
            treatmentRecommendation={depressionScore !== null ? getPhq9TreatmentRecommendation(depressionScore) : undefined}
            dataSource={dataSource}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <MetricCard
                title="Depression Severity"
                value={getPhq9Interpretation(depressionScore) || 'Not assessed'}
                icon={<SentimentVeryDissatisfiedIcon />}
                status={getPhq9Status(depressionScore)}
                dataSource={dataSource}
              />
            </Grid>
            <Grid item xs={12}>
              <MetricCard
                title="Suicidal Ideation"
                value={data.phq9_suicidal_ideation || 'Not assessed'}
                icon={<WarningIcon />}
                status={data.phq9_suicidal_ideation === 'Yes' ? 'abnormal' : null}
                additionalInfo="Based on PHQ-9 question about thoughts of self-harm"
                dataSource={dataSource}
              />
            </Grid>
          </Grid>
        </Grid>
      </Grid>

      {/* Anxiety Assessment Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Anxiety Assessment
      </Typography>
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={8}>
          <EnhancedAssessmentCard
            title="GAD-7 Anxiety Screening"
            score={anxietyScore}
            maxScore={21}
            icon={<SentimentDissatisfiedIcon />}
            status={getGad7Status(anxietyScore)}
            interpretation={getGad7Interpretation(anxietyScore)}
            clinicalNotes={data.gad7_notes}
            treatmentRecommendation={anxietyScore !== null ? getGad7TreatmentRecommendation(anxietyScore) : undefined}
            dataSource={dataSource}
          />
        </Grid>
        <Grid item xs={12} md={4}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <MetricCard
                title="Anxiety Severity"
                value={getGad7Interpretation(anxietyScore) || 'Not assessed'}
                icon={<SentimentDissatisfiedIcon />}
                status={getGad7Status(anxietyScore)}
                dataSource={dataSource}
              />
            </Grid>
            <Grid item xs={12}>
              <MetricCard
                title="Difficulty in Daily Life"
                value={data.gad7_difficulty || 'Not assessed'}
                icon={<InfoIcon />}
                additionalInfo="How difficult anxiety symptoms make it to function"
                dataSource={dataSource}
              />
            </Grid>
          </Grid>
        </Grid>
      </Grid>

      {/* Add New Assessment Button */}
      {!hasMentalHealthData && patient && (
        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
          <Button
            component={Link}
            to={`/patients/${patient.patient_id}/visits/new`}
            variant="contained"
            color="primary"
            startIcon={<AddCircleIcon />}
            sx={{ borderRadius: 2, px: 3, py: 1 }}
          >
            Perform New Assessment
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default MentalHealthTab;
