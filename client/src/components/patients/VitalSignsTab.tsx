import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  useTheme,
  alpha
} from '@mui/material';
import { Patient, Visit } from '../../types';
import FavoriteIcon from '@mui/icons-material/Favorite';
import MonitorHeartIcon from '@mui/icons-material/MonitorHeart';
import DeviceThermostatIcon from '@mui/icons-material/DeviceThermostat';
import HeightIcon from '@mui/icons-material/Height';
import ScaleIcon from '@mui/icons-material/Scale';
import AirIcon from '@mui/icons-material/Air';
import SpeedIcon from '@mui/icons-material/Speed';
import WarningIcon from '@mui/icons-material/Warning';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import InfoIcon from '@mui/icons-material/Info';
import AccessibilityNewIcon from '@mui/icons-material/AccessibilityNew';

interface VitalSignsTabProps {
  patient: Patient;
  recentVisit: Visit | null;
}

// Helper function to determine if a value is within normal range
const getValueStatus = (value: number | null | undefined, min: number, max: number): 'normal' | 'warning' | 'abnormal' => {
  if (value === null || value === undefined) return 'normal';

  // Define warning thresholds (slightly outside normal range)
  const warningLowerThreshold = min * 0.95;
  const warningUpperThreshold = max * 1.05;

  if (value < min || value > max) {
    if (value >= warningLowerThreshold && value <= warningUpperThreshold) {
      return 'warning';
    }
    return 'abnormal';
  }
  return 'normal';
};

// Helper function to get status indicator component
const StatusIndicator = ({ status }: { status: 'normal' | 'warning' | 'abnormal' }) => {
  const theme = useTheme();

  const getStatusColor = () => {
    switch (status) {
      case 'abnormal':
        return theme.palette.error.main;
      case 'warning':
        return theme.palette.warning.main;
      default:
        return theme.palette.success.main;
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'abnormal':
        return <WarningIcon fontSize="small" />;
      case 'warning':
        return <InfoIcon fontSize="small" />;
      default:
        return <CheckCircleIcon fontSize="small" />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'abnormal':
        return 'Outside normal range';
      case 'warning':
        return 'Borderline';
      default:
        return 'Normal';
    }
  };

  return (
    <Box sx={{
      display: 'flex',
      alignItems: 'center',
      gap: 0.5,
      color: getStatusColor(),
      fontSize: '0.875rem',
      mt: 0.5
    }}>
      {getStatusIcon()}
      <Typography variant="body2" sx={{ color: getStatusColor() }}>
        {getStatusText()}
      </Typography>
    </Box>
  );
};

// Metric card component for consistent styling
const MetricCard = ({
  title,
  value,
  unit,
  icon,
  status,
  normalRange,
  additionalInfo,
  dataSource = 'patient'
}: {
  title: string;
  value: number | string | null | undefined;
  unit: string;
  icon: React.ReactNode;
  status?: 'normal' | 'warning' | 'abnormal';
  normalRange?: string;
  additionalInfo?: string;
  dataSource?: 'visit' | 'patient';
}) => {
  const theme = useTheme();

  return (
    <Paper
      elevation={0}
      sx={{
        p: 2,
        height: '100%',
        borderRadius: 2,
        border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5, gap: 1 }}>
        <Box sx={{
          width: 40,
          height: 40,
          borderRadius: 1,
          bgcolor: alpha(theme.palette.primary.main, 0.1),
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: theme.palette.primary.main
        }}>
          {icon}
        </Box>
        <Typography variant="h6" sx={{ fontWeight: 500 }}>
          {title}
        </Typography>
      </Box>

      <Box sx={{ flex: 1 }}>
        <Typography variant="h4" sx={{ fontWeight: 600, mb: 0.5 }}>
          {value !== null && value !== undefined ? value : (dataSource === 'visit' ? 'Not measured' : 'Not available')}
          {value !== null && value !== undefined && <Typography component="span" variant="body1" sx={{ ml: 0.5 }}>{unit}</Typography>}
        </Typography>

        {normalRange && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            Normal range: {normalRange}
          </Typography>
        )}

        {status && value !== null && value !== undefined && <StatusIndicator status={status} />}

        {additionalInfo && (
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1, fontStyle: 'italic' }}>
            {additionalInfo}
          </Typography>
        )}
      </Box>
    </Paper>
  );
};

const VitalSignsTab: React.FC<VitalSignsTabProps> = ({ patient, recentVisit }) => {
  const theme = useTheme();

  // Determine data source - use recent visit data if available, otherwise use patient data
  const dataSource = recentVisit ? 'visit' : 'patient';
  console.log(`Using data from ${dataSource} table`);

  // Create vital signs object based on data source
  // If viewing visit data, only show values measured during that visit (no fallbacks)
  // If viewing patient profile, show values from patient table
  const vitalSigns = dataSource === 'visit'
    ? {
        // Basic measurements - only from this visit
        height: recentVisit?.height,
        weight: recentVisit?.weight,
        bmi: recentVisit?.bmi,

        // Blood pressure - only from this visit
        lying_bp_systolic: recentVisit?.lying_bp_systolic,
        lying_bp_diastolic: recentVisit?.lying_bp_diastolic,
        sitting_bp_systolic: recentVisit?.sitting_bp_systolic,
        sitting_bp_diastolic: recentVisit?.sitting_bp_diastolic,
        standing_bp_systolic: recentVisit?.standing_bp_systolic,
        standing_bp_diastolic: recentVisit?.standing_bp_diastolic,

        // Heart rate - only from this visit
        lying_heart_rate: recentVisit?.lying_heart_rate,
        // No sitting heart rate in Visit interface, so it will be undefined
        sitting_heart_rate: undefined,
        standing_heart_rate: recentVisit?.standing_heart_rate,
        heart_rhythm: recentVisit?.heart_rhythm,

        // Other vital signs - only from this visit
        temperature: recentVisit?.temperature,
        respiratory_rate: recentVisit?.respiratory_rate,
        pulse_oximetry: recentVisit?.pulse_oximetry
      }
    : {
        // Basic measurements from patient profile
        height: patient.height,
        weight: patient.weight,
        bmi: patient.bmi,

        // Blood pressure from patient profile
        lying_bp_systolic: patient.lying_bp_systolic,
        lying_bp_diastolic: patient.lying_bp_diastolic,
        sitting_bp_systolic: patient.sitting_bp_systolic,
        sitting_bp_diastolic: patient.sitting_bp_diastolic,
        standing_bp_systolic: patient.standing_bp_systolic,
        standing_bp_diastolic: patient.standing_bp_diastolic,

        // Heart rate from patient profile
        lying_heart_rate: patient.lying_heart_rate,
        // Use heart_rate as fallback for sitting position in patient profile only
        sitting_heart_rate: patient.heart_rate ?? patient.lying_heart_rate,
        standing_heart_rate: patient.standing_heart_rate,
        heart_rhythm: patient.heart_rhythm,

        // Other vital signs from patient profile
        temperature: patient.temperature ?? patient.body_temperature,
        respiratory_rate: patient.respiratory_rate,
        pulse_oximetry: patient.pulse_oximetry
      };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
        <FavoriteIcon sx={{ mr: 1, verticalAlign: 'text-bottom' }} />
        Vital Signs
      </Typography>

      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        mb: 3,
        p: 2,
        bgcolor: alpha(theme.palette.info.main, 0.1),
        borderRadius: 1
      }}>
        <InfoIcon sx={{ mr: 1, color: theme.palette.info.main }} />
        <Typography variant="body1">
          {recentVisit ? (
            <>Displaying vital signs data from the patient's most recent visit on <strong>{new Date(recentVisit.visit_date).toLocaleDateString()}</strong>.</>
          ) : (
            <>Displaying vital signs data from the patient's profile. No visit data available.</>
          )}
        </Typography>
      </Box>

      <Typography variant="body1" sx={{ mb: 3 }}>
        This tab displays all vital signs measurements for the patient, including basic measurements, blood pressure, heart rate, and other vital signs.
        The most recent values are shown, prioritizing data from the latest visit when available.
      </Typography>

      {/* Basic Measurements Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Basic Measurements
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Height"
            value={vitalSigns.height}
            unit="cm"
            icon={<HeightIcon />}
            normalRange="150-190 cm"
            status={getValueStatus(Number(vitalSigns.height), 150, 190)}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Weight"
            value={vitalSigns.weight}
            unit="kg"
            icon={<ScaleIcon />}
            normalRange="45-100 kg"
            status={getValueStatus(Number(vitalSigns.weight), 45, 100)}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="BMI"
            value={vitalSigns.bmi}
            unit="kg/m²"
            icon={<SpeedIcon />}
            normalRange="18.5-24.9 kg/m²"
            status={getValueStatus(Number(vitalSigns.bmi), 18.5, 24.9)}
            additionalInfo="For older adults, a slightly higher BMI (23-30) may be acceptable."
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* Blood Pressure Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Blood Pressure
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Lying Position"
            value={vitalSigns.lying_bp_systolic && vitalSigns.lying_bp_diastolic
              ? `${vitalSigns.lying_bp_systolic}/${vitalSigns.lying_bp_diastolic}`
              : null}
            unit="mmHg"
            icon={<MonitorHeartIcon />}
            normalRange="90-140/60-90 mmHg"
            status={
              vitalSigns.lying_bp_systolic && vitalSigns.lying_bp_diastolic
                ? (getValueStatus(vitalSigns.lying_bp_systolic, 90, 140) === 'abnormal' ||
                   getValueStatus(vitalSigns.lying_bp_diastolic, 60, 90) === 'abnormal')
                  ? 'abnormal'
                  : (getValueStatus(vitalSigns.lying_bp_systolic, 90, 140) === 'warning' ||
                     getValueStatus(vitalSigns.lying_bp_diastolic, 60, 90) === 'warning')
                    ? 'warning'
                    : 'normal'
                : undefined
            }
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Sitting Position"
            value={vitalSigns.sitting_bp_systolic && vitalSigns.sitting_bp_diastolic
              ? `${vitalSigns.sitting_bp_systolic}/${vitalSigns.sitting_bp_diastolic}`
              : null}
            unit="mmHg"
            icon={<MonitorHeartIcon />}
            normalRange="90-140/60-90 mmHg"
            status={
              vitalSigns.sitting_bp_systolic && vitalSigns.sitting_bp_diastolic
                ? (getValueStatus(vitalSigns.sitting_bp_systolic, 90, 140) === 'abnormal' ||
                   getValueStatus(vitalSigns.sitting_bp_diastolic, 60, 90) === 'abnormal')
                  ? 'abnormal'
                  : (getValueStatus(vitalSigns.sitting_bp_systolic, 90, 140) === 'warning' ||
                     getValueStatus(vitalSigns.sitting_bp_diastolic, 60, 90) === 'warning')
                    ? 'warning'
                    : 'normal'
                : undefined
            }
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Standing Position"
            value={vitalSigns.standing_bp_systolic && vitalSigns.standing_bp_diastolic
              ? `${vitalSigns.standing_bp_systolic}/${vitalSigns.standing_bp_diastolic}`
              : null}
            unit="mmHg"
            icon={<MonitorHeartIcon />}
            normalRange="90-140/60-90 mmHg"
            status={
              vitalSigns.standing_bp_systolic && vitalSigns.standing_bp_diastolic
                ? (getValueStatus(vitalSigns.standing_bp_systolic, 90, 140) === 'abnormal' ||
                   getValueStatus(vitalSigns.standing_bp_diastolic, 60, 90) === 'abnormal')
                  ? 'abnormal'
                  : (getValueStatus(vitalSigns.standing_bp_systolic, 90, 140) === 'warning' ||
                     getValueStatus(vitalSigns.standing_bp_diastolic, 60, 90) === 'warning')
                    ? 'warning'
                    : 'normal'
                : undefined
            }
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* Heart & Respiratory Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Heart & Respiratory
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Lying Heart Rate"
            value={vitalSigns.lying_heart_rate}
            unit="bpm"
            icon={<FavoriteIcon />}
            normalRange="60-100 bpm"
            status={getValueStatus(Number(vitalSigns.lying_heart_rate), 60, 100)}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Sitting Heart Rate"
            value={vitalSigns.sitting_heart_rate}
            unit="bpm"
            icon={<FavoriteIcon />}
            normalRange="60-100 bpm"
            status={getValueStatus(Number(vitalSigns.sitting_heart_rate), 60, 100)}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Standing Heart Rate"
            value={vitalSigns.standing_heart_rate}
            unit="bpm"
            icon={<FavoriteIcon />}
            normalRange="60-100 bpm"
            status={getValueStatus(Number(vitalSigns.standing_heart_rate), 60, 100)}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Heart Rhythm"
            value={vitalSigns.heart_rhythm}
            unit=""
            icon={<FavoriteIcon />}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Respiratory Rate"
            value={vitalSigns.respiratory_rate}
            unit="breaths/min"
            icon={<AirIcon />}
            normalRange="12-20 breaths/min"
            status={getValueStatus(Number(vitalSigns.respiratory_rate), 12, 20)}
            dataSource={dataSource}
          />
        </Grid>

        {/* Orthostatic Assessment */}
        {vitalSigns.lying_bp_systolic && vitalSigns.standing_bp_systolic &&
         vitalSigns.lying_bp_diastolic && vitalSigns.standing_bp_diastolic &&
         vitalSigns.lying_heart_rate && vitalSigns.standing_heart_rate && (
          <Grid item xs={12} md={8}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                height: '100%',
                borderRadius: 2,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                display: 'flex',
                flexDirection: 'column'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1.5, gap: 1 }}>
                <Box sx={{
                  width: 40,
                  height: 40,
                  borderRadius: 1,
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: theme.palette.primary.main
                }}>
                  <AccessibilityNewIcon />
                </Box>
                <Typography variant="h6" sx={{ fontWeight: 500 }}>
                  Orthostatic Assessment
                </Typography>
              </Box>

              <Box sx={{ flex: 1 }}>
                {(() => {
                  const systolicDiff = Number(vitalSigns.lying_bp_systolic) - Number(vitalSigns.standing_bp_systolic);
                  const diastolicDiff = Number(vitalSigns.lying_bp_diastolic) - Number(vitalSigns.standing_bp_diastolic);
                  const heartRateDiff = Number(vitalSigns.standing_heart_rate) - Number(vitalSigns.lying_heart_rate);

                  const hasOrthostaticHypotension = systolicDiff >= 20 || diastolicDiff >= 10;
                  const hasCompensatoryTachycardia = heartRateDiff >= 20;

                  let assessmentText = '';
                  let status: 'normal' | 'warning' | 'abnormal' = 'normal';
                  let explanation = '';

                  if (hasOrthostaticHypotension) {
                    assessmentText = 'Orthostatic Hypotension Detected';
                    status = 'abnormal';
                    explanation = `Blood pressure drop: Systolic ${systolicDiff} mmHg (≥20 indicates OH), Diastolic ${diastolicDiff} mmHg (≥10 indicates OH)`;
                  } else if (hasCompensatoryTachycardia) {
                    assessmentText = 'Compensatory Tachycardia Present';
                    status = 'warning';
                    explanation = `Heart rate increase: ${heartRateDiff} bpm (≥20 indicates compensatory tachycardia)`;
                  } else {
                    assessmentText = 'No Orthostatic Hypotension';
                    status = 'normal';
                    explanation = 'Blood pressure remains stable when changing positions from lying to standing.';
                  }

                  return (
                    <>
                      <Typography variant="h5" sx={{ fontWeight: 600, mb: 1, color:
                        status === 'abnormal' ? theme.palette.error.main :
                        status === 'warning' ? theme.palette.warning.main :
                        theme.palette.success.main
                      }}>
                        {assessmentText}
                      </Typography>

                      <Typography variant="body1" sx={{ mb: 1 }}>
                        {explanation}
                      </Typography>

                      <Box sx={{ mt: 2 }}>
                        <Typography variant="body2" color="text.secondary">
                          <strong>Systolic BP Change:</strong> {systolicDiff} mmHg
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          <strong>Diastolic BP Change:</strong> {diastolicDiff} mmHg
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          <strong>Heart Rate Change:</strong> +{heartRateDiff} bpm
                        </Typography>
                      </Box>
                    </>
                  );
                })()}
              </Box>
            </Paper>
          </Grid>
        )}
      </Grid>

      {/* Other Vital Signs Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Other Vital Signs
      </Typography>

      <Grid container spacing={3}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Temperature"
            value={vitalSigns.temperature}
            unit="°C"
            icon={<DeviceThermostatIcon />}
            normalRange="36.1-37.2 °C"
            status={getValueStatus(Number(vitalSigns.temperature), 36.1, 37.2)}
            additionalInfo="Fever is defined as ≥38.0°C. Hypothermia is <35.0°C."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Oxygen Saturation"
            value={vitalSigns.pulse_oximetry}
            unit="%"
            icon={<AirIcon />}
            normalRange="95-100 %"
            status={getValueStatus(Number(vitalSigns.pulse_oximetry), 95, 100)}
            additionalInfo="Values below 90% indicate severe hypoxemia."
            dataSource={dataSource}
          />
        </Grid>
      </Grid>
    </Box>
  );
};

export default VitalSignsTab;
