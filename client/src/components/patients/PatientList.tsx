import React, { useState, useEffect, useContext } from 'react';
import { Link } from 'react-router-dom';
import { getPatients } from '../../services/patientService';
import { CircularProgress, Switch, FormControlLabel } from '@mui/material';
import AuthContext from '../../context/AuthContext';
import axios from 'axios';
import LoadingSpinner from '../common/LoadingSpinner';

interface Patient {
  patient_id: number;
  first_name: string;
  last_name: string;
  unique_id: string;
  date_of_birth: string;
  gender: string;
  phone: string;
  email: string | null;
  doctor_id: number | null;
  doctor_name?: string | null;
}

// Helper function to calculate age
const calculateAge = (dateOfBirth: string): number => {
  const today = new Date();
  const birthDate = new Date(dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDifference = today.getMonth() - birthDate.getMonth();

  // Adjust age if birth month hasn't occurred yet this year or
  // if birth month is current month but birth day hasn't occurred yet
  if (monthDifference < 0 || (monthDifference === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }

  return age;
};

// Safe toLowerCase helper function to handle null/undefined values
const safeToLowerCase = (str: string | null | undefined): string => {
  return str ? str.toLowerCase() : '';
};

const PatientList: React.FC = () => {
  const { user } = useContext(AuthContext);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [filteredPatients, setFilteredPatients] = useState<Patient[]>([]);
  const [allPatients, setAllPatients] = useState<Patient[]>([]);
  const [doctorPatients, setDoctorPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAllPatients, setShowAllPatients] = useState(false);
  const isDoctor = user?.role === 'doctor' && user?.doctor_id;
  const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001';

  // Fetch patients
  useEffect(() => {
    const fetchPatients = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get all patients
        const allPatientsData = await getPatients();
        setAllPatients(allPatientsData);

        // If user is a doctor, get patients assigned to the doctor
        if (isDoctor && user?.doctor_id) {
          const token = localStorage.getItem('token');
          const doctorPatientsResponse = await axios.get(
            `${API_URL}/api/doctors/${user.doctor_id}/patients`,
            {
              headers: {
                'Content-Type': 'application/json',
                'x-auth-token': token
              }
            }
          );
          setDoctorPatients(doctorPatientsResponse.data);
          setPatients(doctorPatientsResponse.data);
          setFilteredPatients(doctorPatientsResponse.data);
        } else {
          // For non-doctors, show all patients
          setPatients(allPatientsData);
          setFilteredPatients(allPatientsData);
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch patients');
        console.error('Error fetching patients:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchPatients();
  }, [isDoctor, user?.doctor_id, API_URL]);

  // Handle toggle between all patients and doctor's patients
  const handleTogglePatients = () => {
    setShowAllPatients(!showAllPatients);
    if (isDoctor) {
      if (!showAllPatients) {
        // Switching to all patients
        setPatients(allPatients);
        setFilteredPatients(
          searchTerm ? filterPatientsBySearchTerm(allPatients, searchTerm) : allPatients
        );
      } else {
        // Switching back to doctor's patients
        setPatients(doctorPatients);
        setFilteredPatients(
          searchTerm ? filterPatientsBySearchTerm(doctorPatients, searchTerm) : doctorPatients
        );
      }
    }
  };

  // Filter patients by search term
  const filterPatientsBySearchTerm = (patientsToFilter: Patient[], term: string): Patient[] => {
    if (term.trim() === '') {
      return patientsToFilter;
    }

    const searchTermLower = term.toLowerCase();
    return patientsToFilter.filter((patient) => {
      return (
        safeToLowerCase(patient.first_name).includes(searchTermLower) ||
        safeToLowerCase(patient.last_name).includes(searchTermLower) ||
        safeToLowerCase(patient.unique_id).includes(searchTermLower) ||
        safeToLowerCase(patient.email).includes(searchTermLower) ||
        safeToLowerCase(patient.doctor_name).includes(searchTermLower)
      );
    });
  };

  // Handle search
  useEffect(() => {
    setFilteredPatients(filterPatientsBySearchTerm(patients, searchTerm));
  }, [searchTerm, patients]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  if (loading) {
    return <LoadingSpinner size="large" message="Loading patients..." />;
  }

  if (error) {
    return (
      <div className="alert alert-danger">
        <p>{error}</p>
        <button
          className="btn btn-primary"
          onClick={() => window.location.reload()}
        >
          Try Again
        </button>
      </div>
    );
  }

  return (
    <div className="patient-list">
      <div className="patient-list-header">
        <div className="patient-header-top">
          <div className="patient-list-title">
            <h1>Patients</h1>
            <div className="patient-count-badge">
              <span>{filteredPatients.length}</span>
              <span className="count-label">{searchTerm ? 'results' : (isDoctor && !showAllPatients ? 'assigned' : 'total')}</span>
            </div>
          </div>

          <div className="patient-actions-right">
            {isDoctor && (
              <div className="filter-container">
                <FormControlLabel
                  control={
                    <Switch
                      checked={showAllPatients}
                      onChange={handleTogglePatients}
                      color="primary"
                    />
                  }
                  label={
                    <span className="filter-label">
                      <i className={`fas ${showAllPatients ? 'fa-users' : 'fa-user-md'}`}></i>
                      {showAllPatients ? "All patients" : "My patients"}
                    </span>
                  }
                />
              </div>
            )}

            <Link to="/patients/new" className="btn btn-primary add-patient-btn">
              <i className="fas fa-plus"></i> Add Patient
            </Link>
          </div>
        </div>

        <div className="search-container">
          <i className="fas fa-search search-icon-left"></i>
          <input
            type="text"
            placeholder="Search by name, email, or ID..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="search-input"
          />
          {searchTerm && (
            <button
              className="search-clear-btn"
              onClick={() => setSearchTerm('')}
              title="Clear search"
            >
              <i className="fas fa-times"></i>
            </button>
          )}
        </div>
      </div>

      {filteredPatients.length > 0 ? (
        <div className="patient-cards-container">
          {filteredPatients.map((patient) => (
            <div className={`patient-card ${patient.gender === 'Female' ? 'female' : ''}`} key={patient.patient_id}>
              <div className="patient-card-header">
                <div className="patient-name-container">
                  <Link to={`/patients/${patient.patient_id}`} className="patient-name">
                    {patient.first_name} {patient.last_name}
                  </Link>
                  <span className="patient-id">{patient.unique_id || 'N/A'}</span>
                </div>
                <div className="patient-badges">
                  <span className="patient-badge patient-age">
                    <i className="fas fa-birthday-cake"></i> {calculateAge(patient.date_of_birth)}
                  </span>
                  <span className="patient-badge patient-gender">
                    <i className={patient.gender === 'Male' ? 'fas fa-mars' : 'fas fa-venus'}></i> {patient.gender}
                  </span>
                </div>
              </div>

              <div className="patient-card-body">
                <div className="patient-info-item">
                  <div className="info-label">
                    <i className="fas fa-phone"></i> Phone
                  </div>
                  <div className="info-value">{patient.phone || 'N/A'}</div>
                </div>

                <div className="patient-info-item">
                  <div className="info-label">
                    <i className="fas fa-envelope"></i> Email
                  </div>
                  <div className="info-value">{patient.email || 'N/A'}</div>
                </div>

                <div className="patient-info-item">
                  <div className="info-label">
                    <i className="fas fa-user-md"></i> Doctor
                  </div>
                  <div className="info-value">{patient.doctor_name || 'Not assigned'}</div>
                </div>
              </div>

              <div className="patient-card-footer">
                <Link to={`/patients/${patient.patient_id}`} className="card-action-button view-button">
                  <i className="fas fa-eye"></i> View
                </Link>
                <Link to={`/patients/safe-edit/${patient.patient_id}`} className="card-action-button edit-button">
                  <i className="fas fa-shield-alt"></i> Edit
                </Link>
                <Link to={`/patients/${patient.patient_id}/visits/new`} className="card-action-button visit-button">
                  <i className="fas fa-calendar-plus"></i> New Visit
                </Link>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="alert alert-info">
          {searchTerm ? (
            <p>No patients found matching "{searchTerm}"</p>
          ) : isDoctor && !showAllPatients ? (
            <p>No patients assigned to you. Toggle switch to view all patients.</p>
          ) : (
            <p>No patients found. Add your first patient!</p>
          )}
        </div>
      )}
    </div>
  );
};

export default PatientList;