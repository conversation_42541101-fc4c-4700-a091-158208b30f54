import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  useTheme,
  Divider,
  Chip,
  alpha
} from '@mui/material';
import { 
  Bed as BedIcon,
  LocalHospital as PainIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import { Patient } from '../../types';

interface SleepPainTabProps {
  patient: Patient | null;
  recentVisit: any | null;
}

// Helper function to determine sleep quality status
const getSleepQualityStatus = (quality: string | null | undefined) => {
  if (!quality) return null;
  if (quality === 'Very good' || quality === 'Fairly good') return 'normal';
  return 'warning';
};

// Helper function to determine PSQI score status
const getPsqiScoreStatus = (score: string | number | null | undefined) => {
  if (!score) return null;
  const numScore = typeof score === 'string' ? parseInt(score) : score;
  if (numScore <= 5) return 'normal';
  if (numScore <= 10) return 'warning';
  return 'abnormal';
};

// Helper function to determine pain level status
const getPainLevelStatus = (level: string | number | null | undefined) => {
  if (!level) return null;
  const numLevel = typeof level === 'string' ? parseInt(level) : level;
  if (numLevel <= 3) return 'normal';
  if (numLevel <= 6) return 'warning';
  return 'abnormal';
};

// Helper function to get PSQI interpretation
const getPsqiInterpretation = (score: string | number | null | undefined) => {
  if (!score) return 'Not assessed';
  const numScore = typeof score === 'string' ? parseInt(score) : score;
  if (numScore <= 5) return 'Good sleep quality';
  if (numScore <= 10) return 'Poor sleep quality';
  return 'Severe sleep disturbance';
};

const SleepPainTab: React.FC<SleepPainTabProps> = ({ patient, recentVisit }) => {
  const theme = useTheme();
  const data = recentVisit || patient || {};
  const dataSource = recentVisit ? 'visit' : 'patient';

  return (
    <Box sx={{ p: 3 }}>
      {/* Sleep Assessment Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
          Sleep Assessment
        </Typography>
        
        <Grid container spacing={3}>
          {/* Basic Sleep Information */}
          <Grid item xs={12} md={6}>
            <Paper 
              elevation={0} 
              sx={{ 
                p: 2, 
                borderRadius: 2, 
                border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                height: '100%'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <BedIcon sx={{ color: theme.palette.primary.main, mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Basic Sleep Information
                </Typography>
              </Box>
              
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">Sleep Quality</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                    {getSleepQualityStatus(data.sleep_quality) === 'normal' ? (
                      <CheckCircleIcon sx={{ fontSize: 18, color: 'success.main', mr: 0.5 }} />
                    ) : getSleepQualityStatus(data.sleep_quality) === 'warning' ? (
                      <WarningIcon sx={{ fontSize: 18, color: 'warning.main', mr: 0.5 }} />
                    ) : null}
                    <Typography variant="body1">{data.sleep_quality || 'Not assessed'}</Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="text.secondary">Sleep Duration</Typography>
                  <Typography variant="body1">{data.sleep_duration || 'Not assessed'}</Typography>
                </Grid>
                
                <Grid item xs={12}>
                  <Divider sx={{ my: 1 }} />
                </Grid>
                
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">Sleep Patterns</Typography>
                  <Typography variant="body1">{data.sleep_patterns || 'Not documented'}</Typography>
                </Grid>
                
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">Sleep Initiation Difficulties</Typography>
                  <Typography variant="body1">{data.sleep_initiation_difficulties || 'Not documented'}</Typography>
                </Grid>
                
                <Grid item xs={12}>
                  <Typography variant="subtitle2" color="text.secondary">Sleep Disturbances</Typography>
                  <Typography variant="body1">{data.sleep_disturbances || 'Not documented'}</Typography>
                </Grid>
              </Grid>
            </Paper>
          </Grid>
          
          {/* PSQI Assessment */}
          <Grid item xs={12} md={6}>
            <Paper 
              elevation={0} 
              sx={{ 
                p: 2, 
                borderRadius: 2, 
                border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                height: '100%'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <BedIcon sx={{ color: theme.palette.primary.main, mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Pittsburgh Sleep Quality Index (PSQI)
                </Typography>
              </Box>
              
              {data.psqi_total_score ? (
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Box sx={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      justifyContent: 'space-between',
                      mb: 1
                    }}>
                      <Typography variant="subtitle1">Global PSQI Score</Typography>
                      <Chip 
                        label={`${data.psqi_total_score}/21`} 
                        color={getPsqiScoreStatus(data.psqi_total_score) === 'normal' ? 'success' : 
                              getPsqiScoreStatus(data.psqi_total_score) === 'warning' ? 'warning' : 'error'}
                        size="small"
                      />
                    </Box>
                    <Box sx={{ 
                      p: 1, 
                      borderRadius: 1, 
                      bgcolor: alpha(
                        getPsqiScoreStatus(data.psqi_total_score) === 'normal' ? theme.palette.success.main : 
                        getPsqiScoreStatus(data.psqi_total_score) === 'warning' ? theme.palette.warning.main : theme.palette.error.main, 
                        0.1
                      ),
                      mb: 2
                    }}>
                      <Typography variant="body2" fontWeight={500}>
                        {getPsqiInterpretation(data.psqi_total_score)}
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12}>
                    <Divider sx={{ my: 1 }} />
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">Bedtime</Typography>
                    <Typography variant="body1">{data.psqi_bedtime || 'Not recorded'}</Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">Wake-up Time</Typography>
                    <Typography variant="body1">{data.psqi_wake_up_time || 'Not recorded'}</Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">Minutes to Fall Asleep</Typography>
                    <Typography variant="body1">{data.psqi_minutes_to_fall_asleep || 'Not recorded'}</Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">Hours of Actual Sleep</Typography>
                    <Typography variant="body1">{data.psqi_hours_of_sleep || 'Not recorded'}</Typography>
                  </Grid>
                  
                  {data.psqi_notes && (
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="text.secondary">Additional Notes</Typography>
                      <Typography variant="body1">{data.psqi_notes}</Typography>
                    </Grid>
                  )}
                </Grid>
              ) : (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, color: 'text.secondary' }}>
                  <InfoIcon sx={{ fontSize: '1rem' }} />
                  <Typography variant="body1">
                    {dataSource === 'visit' ? 'PSQI not assessed in this visit' : 'No PSQI assessment data available'}
                  </Typography>
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      </Box>
      
      {/* Pain Assessment Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
          Pain Assessment
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Paper 
              elevation={0} 
              sx={{ 
                p: 2, 
                borderRadius: 2, 
                border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <PainIcon sx={{ color: theme.palette.primary.main, mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Pain Assessment
                </Typography>
              </Box>
              
              {data.pain_level !== undefined && data.pain_level !== null ? (
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={4}>
                    <Typography variant="subtitle2" color="text.secondary">Pain Level (0-10)</Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                      {getPainLevelStatus(data.pain_level) === 'normal' ? (
                        <CheckCircleIcon sx={{ fontSize: 18, color: 'success.main', mr: 0.5 }} />
                      ) : getPainLevelStatus(data.pain_level) === 'warning' ? (
                        <WarningIcon sx={{ fontSize: 18, color: 'warning.main', mr: 0.5 }} />
                      ) : (
                        <WarningIcon sx={{ fontSize: 18, color: 'error.main', mr: 0.5 }} />
                      )}
                      <Typography 
                        variant="body1" 
                        fontWeight={500}
                        color={
                          getPainLevelStatus(data.pain_level) === 'normal' ? 'success.main' : 
                          getPainLevelStatus(data.pain_level) === 'warning' ? 'warning.main' : 'error.main'
                        }
                      >
                        {data.pain_level}/10
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} sm={4}>
                    <Typography variant="subtitle2" color="text.secondary">Pain Location</Typography>
                    <Typography variant="body1">{data.pain_location || 'Not specified'}</Typography>
                  </Grid>
                  
                  <Grid item xs={12} sm={4}>
                    <Typography variant="subtitle2" color="text.secondary">Pain Character</Typography>
                    <Typography variant="body1">{data.pain_character || 'Not specified'}</Typography>
                  </Grid>
                  
                  {data.safe_pain_medications && (
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="text.secondary">Safe Pain Medications</Typography>
                      <Typography variant="body1">{data.safe_pain_medications}</Typography>
                    </Grid>
                  )}
                </Grid>
              ) : (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, color: 'text.secondary' }}>
                  <InfoIcon sx={{ fontSize: '1rem' }} />
                  <Typography variant="body1">
                    {dataSource === 'visit' ? 'Pain not assessed in this visit' : 'No pain assessment data available'}
                  </Typography>
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
};

export default SleepPainTab;
