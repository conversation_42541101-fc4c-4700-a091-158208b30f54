import React from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  useTheme,
  Divider,
  Chip,
  alpha
} from '@mui/material';
import {
  Straighten as MeasurementIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Info as InfoIcon,
  History as HistoryIcon,
  Assessment as AssessmentIcon,
  Medication as MedicationIcon,
  Psychology as PsychologyIcon,
  Memory as CognitiveIcon
} from '@mui/icons-material';
import { Patient } from '../../types';

interface FrailtyAssessmentTabProps {
  patient: Patient | null;
  recentVisit: any | null;
}

// Helper function to determine calf circumference status
const getCalfCircumferenceStatus = (value: string | number | null | undefined) => {
  if (!value) return null;
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  if (numValue >= 31) return 'normal';
  return 'warning';
};

// Helper function to determine grip strength status
// Note: This is a simplified version. In reality, grip strength norms vary by age, sex, and body size
const getGripStrengthStatus = (value: string | number | null | undefined) => {
  if (!value) return null;
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  if (numValue >= 20) return 'normal';
  if (numValue >= 15) return 'warning';
  return 'abnormal';
};

// Helper function to determine fall risk status based on number of incidents
const getFallRiskStatus = (incidents: string | number | null | undefined) => {
  if (incidents === undefined || incidents === null) return null;
  const numIncidents = typeof incidents === 'string' ? parseInt(incidents) : incidents;
  if (numIncidents === 0) return 'normal';
  if (numIncidents === 1) return 'warning';
  return 'abnormal';
};

const FrailtyAssessmentTab: React.FC<FrailtyAssessmentTabProps> = ({ patient, recentVisit }) => {
  const theme = useTheme();
  const data = recentVisit || patient || {};
  const dataSource = recentVisit ? 'visit' : 'patient';

  return (
    <Box sx={{ p: 3 }}>
      {/* Physical Measurements Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
          Physical Measurements
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                borderRadius: 2,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <MeasurementIcon sx={{ color: theme.palette.primary.main, mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Anthropometric Measurements
                </Typography>
              </Box>

              <Grid container spacing={2}>
                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="subtitle2" color="text.secondary">Calf Circumference</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                    {getCalfCircumferenceStatus(data.calf_circumference) === 'normal' ? (
                      <CheckCircleIcon sx={{ fontSize: 18, color: 'success.main', mr: 0.5 }} />
                    ) : getCalfCircumferenceStatus(data.calf_circumference) === 'warning' ? (
                      <WarningIcon sx={{ fontSize: 18, color: 'warning.main', mr: 0.5 }} />
                    ) : null}
                    <Typography variant="body1">
                      {data.calf_circumference ? `${data.calf_circumference} cm` : 'Not assessed'}
                    </Typography>
                  </Box>
                  {data.calf_circumference && parseFloat(data.calf_circumference) < 31 && (
                    <Typography variant="caption" color="warning.main">
                      Below 31 cm indicates sarcopenia risk
                    </Typography>
                  )}
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="subtitle2" color="text.secondary">Grip Strength</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                    {getGripStrengthStatus(data.grip_strength) === 'normal' ? (
                      <CheckCircleIcon sx={{ fontSize: 18, color: 'success.main', mr: 0.5 }} />
                    ) : getGripStrengthStatus(data.grip_strength) === 'warning' ? (
                      <WarningIcon sx={{ fontSize: 18, color: 'warning.main', mr: 0.5 }} />
                    ) : getGripStrengthStatus(data.grip_strength) === 'abnormal' ? (
                      <WarningIcon sx={{ fontSize: 18, color: 'error.main', mr: 0.5 }} />
                    ) : null}
                    <Typography variant="body1">
                      {data.grip_strength ? `${data.grip_strength} kg` : 'Not assessed'}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="subtitle2" color="text.secondary">Weight</Typography>
                  <Typography variant="body1">
                    {data.weight ? `${data.weight} kg` : 'Not recorded'}
                  </Typography>
                </Grid>

                <Grid item xs={12} sm={6} md={3}>
                  <Typography variant="subtitle2" color="text.secondary">Height</Typography>
                  <Typography variant="body1">
                    {data.height ? `${data.height} cm` : 'Not recorded'}
                  </Typography>
                </Grid>

                {data.frailty_score && (
                  <>
                    <Grid item xs={12}>
                      <Divider sx={{ my: 1 }} />
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Box sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'space-between',
                        mb: 1
                      }}>
                        <Typography variant="subtitle2" color="text.secondary">Frailty Score</Typography>
                        <Chip
                          label={`${data.frailty_score}/20`}
                          color={
                            parseInt(data.frailty_score) >= 15 ? 'success' :
                            parseInt(data.frailty_score) >= 10 ? 'warning' : 'error'
                          }
                          size="small"
                        />
                      </Box>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle2" color="text.secondary">Frailty Level</Typography>
                      <Typography variant="body1">{data.frailty_level || 'Not specified'}</Typography>
                    </Grid>
                  </>
                )}

                {data.frailty_assessment_date && (
                  <Grid item xs={12}>
                    <Typography variant="subtitle2" color="text.secondary">Assessment Date</Typography>
                    <Typography variant="body1">
                      {new Date(data.frailty_assessment_date).toLocaleDateString()}
                    </Typography>
                  </Grid>
                )}
              </Grid>
            </Paper>
          </Grid>
        </Grid>
      </Box>

      {/* Mobility and Falls History Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
          Mobility and Falls History
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                borderRadius: 2,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <HistoryIcon sx={{ color: theme.palette.primary.main, mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Mobility Status & Falls History
                </Typography>
              </Box>

              {data.mobility_status || data.mobility_aids_used || data.fall_detection_incidents !== undefined ? (
                <Grid container spacing={2}>
                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">Mobility Status</Typography>
                    <Typography variant="body1">{data.mobility_status || 'Not specified'}</Typography>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">Mobility Aids Used</Typography>
                    <Typography variant="body1">{data.mobility_aids_used || 'None'}</Typography>
                  </Grid>

                  <Grid item xs={12}>
                    <Divider sx={{ my: 1 }} />
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">Fall Incidents (past 12 months)</Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                      {getFallRiskStatus(data.fall_detection_incidents) === 'normal' ? (
                        <CheckCircleIcon sx={{ fontSize: 18, color: 'success.main', mr: 0.5 }} />
                      ) : getFallRiskStatus(data.fall_detection_incidents) === 'warning' ? (
                        <WarningIcon sx={{ fontSize: 18, color: 'warning.main', mr: 0.5 }} />
                      ) : getFallRiskStatus(data.fall_detection_incidents) === 'abnormal' ? (
                        <WarningIcon sx={{ fontSize: 18, color: 'error.main', mr: 0.5 }} />
                      ) : null}
                      <Typography
                        variant="body1"
                        fontWeight={500}
                        color={
                          getFallRiskStatus(data.fall_detection_incidents) === 'normal' ? 'success.main' :
                          getFallRiskStatus(data.fall_detection_incidents) === 'warning' ? 'warning.main' : 'error.main'
                        }
                      >
                        {data.fall_detection_incidents !== undefined && data.fall_detection_incidents !== null ?
                          data.fall_detection_incidents : 'Not recorded'}
                      </Typography>
                    </Box>
                  </Grid>

                  <Grid item xs={12} sm={6}>
                    <Typography variant="subtitle2" color="text.secondary">Last Fall Date</Typography>
                    <Typography variant="body1">
                      {data.last_fall_date ? new Date(data.last_fall_date).toLocaleDateString() : 'Not recorded'}
                    </Typography>
                  </Grid>

                  {data.fall_circumstances && (
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="text.secondary">Fall Circumstances</Typography>
                      <Typography variant="body1">{data.fall_circumstances}</Typography>
                    </Grid>
                  )}

                  {data.fall_injuries && (
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="text.secondary">Fall Injuries</Typography>
                      <Typography variant="body1">{data.fall_injuries}</Typography>
                    </Grid>
                  )}

                  {data.home_safety_evaluation && (
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="text.secondary">Home Safety Evaluation</Typography>
                      <Typography variant="body1">{data.home_safety_evaluation}</Typography>
                    </Grid>
                  )}
                </Grid>
              ) : (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, color: 'text.secondary' }}>
                  <InfoIcon sx={{ fontSize: '1rem' }} />
                  <Typography variant="body1">
                    {dataSource === 'visit' ? 'Mobility and falls history not assessed in this visit' : 'No mobility and falls history data available'}
                  </Typography>
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      </Box>

      {/* Falls Risk Assessment Tool (FRAT) Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
          Falls Risk Assessment Tool (FRAT)
        </Typography>

        <Grid container spacing={3}>
          {/* Recent Falls */}
          <Grid item xs={12} md={6}>
            <Paper
              elevation={0}
              sx={{
                p: 0,
                borderRadius: 2,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                overflow: 'hidden',
                height: '100%'
              }}
            >
              <Box sx={{
                p: 2,
                display: 'flex',
                alignItems: 'center',
                bgcolor: alpha(theme.palette.primary.main, 0.1),
                borderBottom: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
              }}>
                <HistoryIcon sx={{ color: theme.palette.primary.main, mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Recent Falls
                </Typography>
              </Box>

              <Box sx={{ p: 2 }}>
                {data.frat_fall_history ? (
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="text.secondary">Fall History</Typography>
                      <Typography variant="body1" sx={{ mt: 0.5 }}>
                        {data.frat_fall_history === '1' ? 'None in last 12 months' :
                         data.frat_fall_history === '2' ? 'One or more between 3 and 12 months ago' :
                         data.frat_fall_history === '3' ? 'One or more in last 3 months' :
                         data.frat_fall_history === '4' ? 'One or more in last 3 months whilst inpatient/resident' :
                         'Not recorded'}
                      </Typography>
                    </Grid>

                    <Grid item xs={12}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="subtitle2" color="text.secondary">Score</Typography>
                        <Chip
                          label={data.frat_fall_history_score || '0'}
                          size="small"
                          color={
                            data.frat_fall_history_score === '2' ? 'success' :
                            data.frat_fall_history_score === '4' ? 'info' :
                            data.frat_fall_history_score === '6' ? 'warning' : 'error'
                          }
                        />
                      </Box>
                    </Grid>

                    {data.fall_detection_incidents !== undefined && (
                      <Grid item xs={12}>
                        <Divider sx={{ my: 1 }} />
                        <Typography variant="subtitle2" color="text.secondary">Fall Incidents (past 12 months)</Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                          {getFallRiskStatus(data.fall_detection_incidents) === 'normal' ? (
                            <CheckCircleIcon sx={{ fontSize: 18, color: 'success.main', mr: 0.5 }} />
                          ) : getFallRiskStatus(data.fall_detection_incidents) === 'warning' ? (
                            <WarningIcon sx={{ fontSize: 18, color: 'warning.main', mr: 0.5 }} />
                          ) : getFallRiskStatus(data.fall_detection_incidents) === 'abnormal' ? (
                            <WarningIcon sx={{ fontSize: 18, color: 'error.main', mr: 0.5 }} />
                          ) : null}
                          <Typography variant="body1">
                            {data.fall_detection_incidents !== null ? data.fall_detection_incidents : 'Not recorded'}
                          </Typography>
                        </Box>
                      </Grid>
                    )}
                  </Grid>
                ) : (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, color: 'text.secondary' }}>
                    <InfoIcon sx={{ fontSize: '1rem' }} />
                    <Typography variant="body1">
                      {dataSource === 'visit' ? 'Fall history not assessed in this visit' : 'No fall history data available'}
                    </Typography>
                  </Box>
                )}
              </Box>
            </Paper>
          </Grid>

          {/* Medications */}
          <Grid item xs={12} md={6}>
            <Paper
              elevation={0}
              sx={{
                p: 0,
                borderRadius: 2,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                overflow: 'hidden',
                height: '100%'
              }}
            >
              <Box sx={{
                p: 2,
                display: 'flex',
                alignItems: 'center',
                bgcolor: alpha(theme.palette.primary.main, 0.1),
                borderBottom: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
              }}>
                <MedicationIcon sx={{ color: theme.palette.primary.main, mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Medications
                </Typography>
              </Box>

              <Box sx={{ p: 2 }}>
                {data.frat_medications ? (
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="text.secondary">Medication Risk</Typography>
                      <Typography variant="body1" sx={{ mt: 0.5 }}>
                        {data.frat_medications === '1' ? 'Not taking any high-risk medications' :
                         data.frat_medications === '2' ? 'Taking one high-risk medication' :
                         data.frat_medications === '3' ? 'Taking two high-risk medications' :
                         data.frat_medications === '4' ? 'Taking three or more high-risk medications' :
                         'Not recorded'}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        High-risk medications include: sedatives, anti-depressants, anti-Parkinson's, diuretics, anti-hypertensives, hypnotics
                      </Typography>
                    </Grid>

                    <Grid item xs={12}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="subtitle2" color="text.secondary">Score</Typography>
                        <Chip
                          label={data.frat_medications_score || '0'}
                          size="small"
                          color={
                            data.frat_medications_score === '1' ? 'success' :
                            data.frat_medications_score === '2' ? 'info' :
                            data.frat_medications_score === '3' ? 'warning' : 'error'
                          }
                        />
                      </Box>
                    </Grid>
                  </Grid>
                ) : (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, color: 'text.secondary' }}>
                    <InfoIcon sx={{ fontSize: '1rem' }} />
                    <Typography variant="body1">
                      {dataSource === 'visit' ? 'Medication risk not assessed in this visit' : 'No medication risk data available'}
                    </Typography>
                  </Box>
                )}
              </Box>
            </Paper>
          </Grid>

          {/* Psychological */}
          <Grid item xs={12} md={6}>
            <Paper
              elevation={0}
              sx={{
                p: 0,
                borderRadius: 2,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                overflow: 'hidden',
                height: '100%'
              }}
            >
              <Box sx={{
                p: 2,
                display: 'flex',
                alignItems: 'center',
                bgcolor: alpha(theme.palette.primary.main, 0.1),
                borderBottom: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
              }}>
                <PsychologyIcon sx={{ color: theme.palette.primary.main, mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Psychological
                </Typography>
              </Box>

              <Box sx={{ p: 2 }}>
                {data.frat_psychological ? (
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="text.secondary">Psychological Status</Typography>
                      <Typography variant="body1" sx={{ mt: 0.5 }}>
                        {data.frat_psychological === '1' ? 'Does not appear to have any psychological risk factors' :
                         data.frat_psychological === '2' ? 'Appears mildly affected by one or more factors' :
                         data.frat_psychological === '3' ? 'Appears moderately affected by one or more factors' :
                         data.frat_psychological === '4' ? 'Appears severely affected by one or more factors' :
                         'Not recorded'}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Psychological factors include: anxiety, depression, decreased cooperation, decreased insight or judgment regarding mobility
                      </Typography>
                    </Grid>

                    <Grid item xs={12}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="subtitle2" color="text.secondary">Score</Typography>
                        <Chip
                          label={data.frat_psychological_score || '0'}
                          size="small"
                          color={
                            data.frat_psychological_score === '1' ? 'success' :
                            data.frat_psychological_score === '2' ? 'info' :
                            data.frat_psychological_score === '3' ? 'warning' : 'error'
                          }
                        />
                      </Box>
                    </Grid>
                  </Grid>
                ) : (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, color: 'text.secondary' }}>
                    <InfoIcon sx={{ fontSize: '1rem' }} />
                    <Typography variant="body1">
                      {dataSource === 'visit' ? 'Psychological status not assessed in this visit' : 'No psychological status data available'}
                    </Typography>
                  </Box>
                )}
              </Box>
            </Paper>
          </Grid>

          {/* Cognitive Status */}
          <Grid item xs={12} md={6}>
            <Paper
              elevation={0}
              sx={{
                p: 0,
                borderRadius: 2,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                overflow: 'hidden',
                height: '100%'
              }}
            >
              <Box sx={{
                p: 2,
                display: 'flex',
                alignItems: 'center',
                bgcolor: alpha(theme.palette.primary.main, 0.1),
                borderBottom: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
              }}>
                <CognitiveIcon sx={{ color: theme.palette.primary.main, mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Cognitive Status
                </Typography>
              </Box>

              <Box sx={{ p: 2 }}>
                {data.frat_cognitive ? (
                  <Grid container spacing={2}>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="text.secondary">Cognitive Assessment</Typography>
                      <Typography variant="body1" sx={{ mt: 0.5 }}>
                        {data.frat_cognitive === '1' ? 'AMTS 9 or 10/10 OR intact' :
                         data.frat_cognitive === '2' ? 'AMTS 7-8 OR mildly impaired' :
                         data.frat_cognitive === '3' ? 'AMTS 5-6 OR moderately impaired' :
                         data.frat_cognitive === '4' ? 'AMTS 4 or less OR severely impaired' :
                         'Not recorded'}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        AMTS: Abbreviated Mental Test Score
                      </Typography>
                    </Grid>

                    <Grid item xs={12}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="subtitle2" color="text.secondary">Score</Typography>
                        <Chip
                          label={data.frat_cognitive_score || '0'}
                          size="small"
                          color={
                            data.frat_cognitive_score === '1' ? 'success' :
                            data.frat_cognitive_score === '2' ? 'info' :
                            data.frat_cognitive_score === '3' ? 'warning' : 'error'
                          }
                        />
                      </Box>
                    </Grid>

                    {data.mini_cog_total && (
                      <Grid item xs={12}>
                        <Divider sx={{ my: 1 }} />
                        <Typography variant="subtitle2" color="text.secondary">Mini-Cog Score</Typography>
                        <Typography variant="body1">
                          {data.mini_cog_total}/5
                        </Typography>
                      </Grid>
                    )}
                  </Grid>
                ) : (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, color: 'text.secondary' }}>
                    <InfoIcon sx={{ fontSize: '1rem' }} />
                    <Typography variant="body1">
                      {dataSource === 'visit' ? 'Cognitive status not assessed in this visit' : 'No cognitive status data available'}
                    </Typography>
                  </Box>
                )}
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Box>

      {/* Falls Risk Assessment Results Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
          Falls Risk Assessment Results
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Paper
              elevation={0}
              sx={{
                p: 0,
                borderRadius: 2,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                overflow: 'hidden'
              }}
            >
              <Box sx={{
                p: 2,
                display: 'flex',
                alignItems: 'center',
                bgcolor: alpha(theme.palette.primary.main, 0.1),
                borderBottom: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
              }}>
                <AssessmentIcon sx={{ color: theme.palette.primary.main, mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Risk Assessment Results
                </Typography>
              </Box>

              <Box sx={{ p: 2 }}>
                {data.frat_risk_level || data.frat_total_score ? (
                  <Grid container spacing={3}>
                    <Grid item xs={12} md={6}>
                      <Box sx={{
                        p: 2,
                        borderRadius: 2,
                        bgcolor: alpha(
                          data.frat_risk_level === 'Low Risk' ? theme.palette.success.main :
                          data.frat_risk_level === 'Medium Risk' ? theme.palette.warning.main : theme.palette.error.main,
                          0.1
                        ),
                        border: `1px solid ${alpha(
                          data.frat_risk_level === 'Low Risk' ? theme.palette.success.main :
                          data.frat_risk_level === 'Medium Risk' ? theme.palette.warning.main : theme.palette.error.main,
                          0.3
                        )}`,
                        mb: { xs: 2, md: 0 }
                      }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                          <Typography variant="subtitle1" fontWeight={600}>
                            Overall Falls Risk Level
                          </Typography>
                          {data.frat_total_score && (
                            <Chip
                              label={`Score: ${data.frat_total_score}/20`}
                              color={
                                data.frat_risk_level === 'Low Risk' ? 'success' :
                                data.frat_risk_level === 'Medium Risk' ? 'warning' : 'error'
                              }
                              size="small"
                            />
                          )}
                        </Box>
                        <Typography variant="body1" fontWeight={500} sx={{
                          color: data.frat_risk_level === 'Low Risk' ? 'success.main' :
                                 data.frat_risk_level === 'Medium Risk' ? 'warning.main' : 'error.main'
                        }}>
                          {data.frat_risk_level || 'Not assessed'}
                        </Typography>
                        <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 1 }}>
                          {data.frat_risk_level === 'Low Risk' ? 'Score range: 5-11' :
                           data.frat_risk_level === 'Medium Risk' ? 'Score range: 12-15' :
                           data.frat_risk_level === 'High Risk' ? 'Score range: 16-20' : ''}
                        </Typography>
                      </Box>
                    </Grid>

                    <Grid item xs={12} md={6}>
                      <Box sx={{ height: '100%' }}>
                        <Typography variant="subtitle2" color="text.secondary">Component Scores</Typography>
                        <Grid container spacing={1} sx={{ mt: 0.5 }}>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="text.secondary">Recent Falls:</Typography>
                            <Typography variant="body2">
                              {data.frat_fall_history_score || 'Not assessed'}
                            </Typography>
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="text.secondary">Medications:</Typography>
                            <Typography variant="body2">
                              {data.frat_medications_score || 'Not assessed'}
                            </Typography>
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="text.secondary">Psychological:</Typography>
                            <Typography variant="body2">
                              {data.frat_psychological_score || 'Not assessed'}
                            </Typography>
                          </Grid>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="text.secondary">Cognitive:</Typography>
                            <Typography variant="body2">
                              {data.frat_cognitive_score || 'Not assessed'}
                            </Typography>
                          </Grid>
                        </Grid>
                      </Box>
                    </Grid>

                    <Grid item xs={12}>
                      <Divider sx={{ my: 1 }} />
                    </Grid>

                    <Grid item xs={12} md={data.frat_reassessment_date ? 6 : 12}>
                      <Typography variant="subtitle2" color="text.secondary">Recommended Interventions</Typography>
                      <Typography variant="body1">
                        {data.frat_recommended_interventions ? data.frat_recommended_interventions :
                         data.frat_risk_level === 'Low Risk' ?
                          'Routine care, maintain mobility, review medications periodically' :
                         data.frat_risk_level === 'Medium Risk' ?
                          'Medication review, balance training, environmental modifications, assistive devices if needed' :
                         data.frat_risk_level === 'High Risk' ?
                          'Comprehensive falls prevention program, physical therapy, medication review, environmental assessment, assistive devices, close monitoring' :
                          'No specific interventions recommended'}
                      </Typography>
                      <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
                        Interventions are automatically suggested based on risk level
                      </Typography>
                    </Grid>

                    {data.frat_reassessment_date && (
                      <Grid item xs={12} md={6}>
                        <Typography variant="subtitle2" color="text.secondary">Reassessment Date</Typography>
                        <Typography variant="body1">
                          {new Date(data.frat_reassessment_date).toLocaleDateString()}
                        </Typography>
                      </Grid>
                    )}

                    {data.frat_notes && (
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" color="text.secondary">Clinical Notes</Typography>
                        <Typography variant="body1">{data.frat_notes}</Typography>
                      </Grid>
                    )}

                    {data.fall_risk_notes && (
                      <Grid item xs={12}>
                        <Typography variant="subtitle2" color="text.secondary">Additional Risk Notes</Typography>
                        <Typography variant="body1">{data.fall_risk_notes}</Typography>
                      </Grid>
                    )}
                  </Grid>
                ) : (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, color: 'text.secondary', p: 2 }}>
                    <InfoIcon sx={{ fontSize: '1rem' }} />
                    <Typography variant="body1">
                      {dataSource === 'visit' ? 'Falls risk assessment results not available for this visit' : 'No falls risk assessment results available'}
                    </Typography>
                  </Box>
                )}
              </Box>
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
};

export default FrailtyAssessmentTab;
