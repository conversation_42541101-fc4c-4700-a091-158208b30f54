/* Senior-Friendly Dashboard Styles */

/* Base container */
.senior-dashboard {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  background-color: var(--background-color, #f8fafc);
  min-height: calc(100vh - 64px);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Text size inheritance for all elements */
.senior-dashboard * {
  font-size: inherit;
}

/* Dark mode variables */
.dark-mode {
  --background-color: #121212;
  --text-primary: #ffffff;
  --text-secondary: #aaaaaa;
  --border-color: rgba(255, 255, 255, 0.1);
  --card-background: #1e1e1e;
  --hover-background: rgba(255, 255, 255, 0.05);

  /* Fix for MUI components in dark mode */
  --MuiTypography-root-color: #ffffff;
  --MuiTypography-body1-color: #e0e0e0;
  --MuiTypography-body2-color: #cccccc;
  --MuiTypography-caption-color: #aaaaaa;
  --MuiAlert-standardInfoColor: #90caf9;
  --MuiAlert-standardInfoBackgroundColor: rgba(144, 202, 249, 0.1);
  --MuiPaper-root-backgroundColor: #1e1e1e;
  --MuiPaper-root-color: #ffffff;
  --MuiChip-outlinedPrimaryColor: #90caf9;
  --MuiChip-outlinedPrimaryBorderColor: rgba(144, 202, 249, 0.5);
}

/* Modern Dashboard header */
.dashboard-header {
  margin-bottom: 2.5rem;
  position: relative;
}

/* Modern header with cleaner styling */
.modern-header {
  padding-bottom: 0;
  border-bottom: none;
}

.dashboard-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary, #1e293b);
  margin-bottom: 0.75rem;
  letter-spacing: -0.02em;
}

/* Modern title with lighter weight for a cleaner look */
.modern-title {
  font-size: 2.75rem;
  font-weight: 300;
  letter-spacing: -0.5px;
  margin-bottom: 0.5rem;
}

.dashboard-welcome {
  font-size: 1.25rem;
  color: var(--text-secondary, #64748b);
  font-weight: 400;
  margin-top: 0.5rem;
}

.dashboard-subtitle {
  font-size: 1.375rem;
  color: var(--text-secondary, #64748b);
  margin-bottom: 1.25rem;
  line-height: 1.4;
}

.last-visit-chip {
  font-size: 1.125rem !important;
  height: auto !important;
  padding: 0.5rem 0.75rem !important;
  border-radius: 24px !important;
  font-weight: 500 !important;
}

/* Tab navigation - larger, more readable */
.senior-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 2.5rem;
  border-bottom: 2px solid #e2e8f0;
  padding-bottom: 0.5rem;
  overflow-x: auto;
  width: 100%;
}

.senior-tab {
  padding: 1.25rem 1.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-secondary, #64748b);
  background-color: transparent;
  border: none;
  border-radius: 12px 12px 0 0;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  white-space: nowrap;
  flex: 0 1 auto;
}

.senior-tab:hover {
  background-color: var(--hover-background, rgba(0, 0, 0, 0.05));
  color: var(--text-primary, #0f172a);
}

.senior-tab.active {
  color: #0284c7;
  border-bottom: 4px solid #0284c7;
  background-color: rgba(2, 132, 199, 0.08);
}

.tab-icon {
  font-size: 1.75rem !important;
}

/* Health metrics cards */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.metric-card {
  background-color: var(--card-background, white);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
  border-left: 6px solid #0284c7;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.dark-mode .metric-card {
  background-color: var(--card-background, #1e1e1e);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.25);
  border-left: 6px solid #90caf9;
}

.metric-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.dark-mode .metric-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.3);
}

.metric-header {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
}

.metric-icon {
  width: 56px;
  height: 56px;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1.25rem;
  font-size: 1.75rem;
  background-color: rgba(2, 132, 199, 0.15);
  color: #0284c7;
  box-shadow: 0 2px 8px rgba(2, 132, 199, 0.2);
}

.dark-mode .metric-icon {
  background-color: rgba(144, 202, 249, 0.15);
  color: #90caf9;
  box-shadow: 0 2px 8px rgba(144, 202, 249, 0.2);
}

.metric-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary, #334155);
  margin: 0;
}

.dark-mode .metric-title {
  color: var(--text-primary, #ffffff);
}

.metric-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary, #0f172a);
  margin: 0.75rem 0;
  line-height: 1.2;
}

.dark-mode .metric-value {
  color: var(--text-primary, #ffffff);
}

.metric-unit {
  font-size: 1.25rem;
  color: var(--text-secondary, #64748b);
  font-weight: 500;
}

.dark-mode .metric-unit {
  color: var(--text-secondary, #aaaaaa);
}

.metric-context {
  font-size: 1.125rem;
  color: var(--text-secondary, #64748b);
  margin-top: 1rem;
  line-height: 1.5;
}

.dark-mode .metric-context {
  color: var(--text-secondary, #aaaaaa);
}

/* Status indicators */
.normal-value {
  color: #10b981;
  font-weight: 600;
}

.warning-value {
  color: #f59e0b;
  font-weight: 600;
}

.abnormal-value {
  color: #ef4444;
  font-weight: 600;
}

.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border-radius: 10px;
  font-size: 1.125rem;
  font-weight: 600;
  margin-top: 1rem;
  gap: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.status-normal {
  background-color: rgba(16, 185, 129, 0.15);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.status-warning {
  background-color: rgba(245, 158, 11, 0.15);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.3);
}

.status-abnormal {
  background-color: rgba(239, 68, 68, 0.15);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.3);
}

/* Section containers */
.dashboard-section {
  background-color: var(--card-background, white);
  border-radius: 20px;
  padding: 2.5rem;
  margin-bottom: 3rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12);
  border: 1px solid var(--border-color, rgba(0, 0, 0, 0.05));
}

.section-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: var(--text-primary, #1e293b);
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.section-title i {
  font-size: 1.75rem;
  color: #0284c7;
}

/* Medication list */
.medication-list {
  list-style: none;
  padding: 0;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
}

.medication-item {
  padding: 1.5rem;
  border-radius: 16px;
  background-color: #f8fafc;
  border: 1px solid rgba(0, 0, 0, 0.08);
  border-left: 6px solid #0284c7;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.medication-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.medication-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
}

.medication-details {
  font-size: 1.25rem;
  color: #475569;
  line-height: 1.5;
}

/* Visit summary */
.visit-summary {
  padding: 2rem;
  border-radius: 16px;
  background-color: #f8fafc;
  margin-bottom: 2rem;
  border: 1px solid rgba(0, 0, 0, 0.08);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
}

.visit-date {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 1rem;
}

.visit-doctor {
  font-size: 1.25rem;
  color: #475569;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.visit-reason {
  font-size: 1.25rem;
  color: #1e293b;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.visit-diagnosis {
  font-size: 1.25rem;
  font-weight: 500;
  color: #1e293b;
  line-height: 1.5;
  padding: 1rem;
  background-color: rgba(2, 132, 199, 0.08);
  border-radius: 8px;
  border-left: 4px solid #0284c7;
}

/* Dark mode overrides for MUI components */
.dark-mode .MuiTypography-root {
  color: var(--MuiTypography-root-color, #ffffff) !important;
}

.dark-mode .MuiTypography-body1 {
  color: var(--MuiTypography-body1-color, #e0e0e0) !important;
}

.dark-mode .MuiTypography-body2 {
  color: var(--MuiTypography-body2-color, #cccccc) !important;
}

.dark-mode .MuiTypography-caption {
  color: var(--MuiTypography-caption-color, #aaaaaa) !important;
}

.dark-mode .MuiAlert-standardInfo {
  color: var(--MuiAlert-standardInfoColor, #90caf9) !important;
  background-color: var(--MuiAlert-standardInfoBackgroundColor, rgba(144, 202, 249, 0.1)) !important;
}

.dark-mode .MuiPaper-root {
  background-color: var(--MuiPaper-root-backgroundColor, #1e1e1e) !important;
  color: var(--MuiPaper-root-color, #ffffff) !important;
}

.dark-mode .MuiChip-outlinedPrimary {
  color: var(--MuiChip-outlinedPrimaryColor, #90caf9) !important;
  border-color: var(--MuiChip-outlinedPrimaryBorderColor, rgba(144, 202, 249, 0.5)) !important;
}

.dark-mode .MuiInputBase-input {
  color: var(--text-primary, #ffffff) !important;
}

.dark-mode .MuiInputLabel-root {
  color: var(--text-secondary, #aaaaaa) !important;
}

.dark-mode .MuiOutlinedInput-notchedOutline {
  border-color: var(--border-color, rgba(255, 255, 255, 0.2)) !important;
}

.dark-mode .MuiSelect-select {
  color: var(--text-primary, #ffffff) !important;
}

.dark-mode .MuiFormHelperText-root {
  color: var(--text-secondary, #aaaaaa) !important;
}

.dark-mode .MuiCircularProgress-root {
  color: var(--MuiChip-outlinedPrimaryColor, #90caf9) !important;
}

.dark-mode .MuiSvgIcon-root {
  color: inherit;
}

.dark-mode .MuiIconButton-root {
  color: var(--text-primary, #ffffff) !important;
}

.dark-mode .MuiMenuItem-root {
  color: var(--text-primary, #ffffff) !important;
}

.dark-mode .MuiMenu-paper {
  background-color: var(--card-background, #1e1e1e) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .senior-dashboard {
    padding: 1.5rem;
  }

  .dashboard-title {
    font-size: 2rem;
  }

  .dashboard-subtitle {
    font-size: 1.25rem;
  }

  .senior-tabs {
    flex-wrap: wrap;
    gap: 0.5rem;
    overflow-x: visible;
  }

  .senior-tab {
    padding: 0.75rem 1rem;
    font-size: 1.125rem;
    flex: 0 1 auto;
    min-width: auto;
    text-align: center;
    justify-content: center;
  }

  .tab-icon {
    font-size: 1.5rem !important;
  }

  .dashboard-section {
    padding: 1.75rem;
    margin-bottom: 2rem;
  }

  .section-title {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .metric-card {
    padding: 1.5rem;
  }

  .metric-value {
    font-size: 2rem;
  }

  .metric-unit {
    font-size: 1.125rem;
  }

  .metric-explanation {
    font-size: 1.125rem;
  }

  .status-indicator {
    font-size: 1.125rem;
    padding: 0.625rem 0.875rem;
  }

  .medication-list {
    grid-template-columns: 1fr;
  }

  .medication-name {
    font-size: 1.375rem;
  }

  .medication-details {
    font-size: 1.125rem;
  }

  .visit-date {
    font-size: 1.375rem;
  }

  .visit-doctor {
    font-size: 1.125rem;
  }

  .visit-reason, .visit-diagnosis {
    font-size: 1.125rem;
  }
}

/* Print styles */
@media print {
  /* General print styles */
  body {
    background-color: white !important;
    color: black !important;
    font-size: 12pt !important;
    line-height: 1.3 !important;
  }

  .senior-dashboard {
    padding: 0 !important;
    max-width: 100% !important;
    background-color: white !important;
    min-height: auto !important;
    box-shadow: none !important;
  }

  /* Hide elements not needed for printing */
  .senior-tabs,
  button:not(.print-include),
  .MuiButton-root:not(.print-include),
  .MuiIconButton-root:not(.print-include),
  .MuiTabs-root,
  .MuiTab-root,
  .MuiChip-root,
  .MuiDialog-root,
  .MuiSnackbar-root,
  .MuiTooltip-popper,
  .MuiMenu-root,
  .MuiPopover-root,
  .MuiDrawer-root,
  .MuiAppBar-root,
  .MuiToolbar-root,
  .MuiBottomNavigation-root,
  .MuiSpeedDial-root,
  .MuiFab-root {
    display: none !important;
  }

  /* Show all tabs content */
  .MuiBox-root[style*="display: none"] {
    display: block !important;
  }

  /* Header styling */
  .dashboard-header {
    margin-bottom: 1.5rem !important;
    padding-bottom: 1rem !important;
    border-bottom: 1px solid #000 !important;
  }

  .dashboard-title {
    font-size: 24pt !important;
    margin-bottom: 0.5rem !important;
    color: black !important;
  }

  .dashboard-subtitle {
    font-size: 14pt !important;
    margin-bottom: 0.5rem !important;
    color: black !important;
  }

  /* Section styling */
  .dashboard-section {
    page-break-inside: avoid !important;
    margin-bottom: 1.5rem !important;
    padding: 1rem !important;
    border: 1px solid #ccc !important;
    border-radius: 0 !important;
    background-color: white !important;
    box-shadow: none !important;
  }

  .section-title {
    font-size: 18pt !important;
    margin-bottom: 1rem !important;
    color: black !important;
  }

  /* Card styling */
  .MuiPaper-root,
  .MuiCard-root,
  .metric-card {
    page-break-inside: avoid !important;
    background-color: white !important;
    border: 1px solid #ccc !important;
    box-shadow: none !important;
    margin-bottom: 1rem !important;
  }

  /* Typography */
  .MuiTypography-root {
    color: black !important;
  }

  /* Ensure proper page breaks */
  h1, h2, h3, h4, h5, h6 {
    page-break-after: avoid !important;
  }

  img, svg {
    page-break-inside: avoid !important;
  }

  /* Add page numbers */
  @page {
    margin: 0.5in !important;
    size: portrait !important;
  }

  /* Print footer with date and patient info */
  .senior-dashboard::after {
    content: "Printed on " attr(data-print-date) " - Patient Dashboard" !important;
    display: block !important;
    text-align: center !important;
    font-size: 10pt !important;
    margin-top: 2rem !important;
    border-top: 1px solid #ccc !important;
    padding-top: 0.5rem !important;
  }

  /* Ensure grid layouts print properly */
  .MuiGrid-container {
    display: block !important;
  }

  .MuiGrid-item {
    width: 100% !important;
    max-width: 100% !important;
    flex-basis: 100% !important;
    margin-bottom: 1rem !important;
  }
}

/* Watermark class that will be added dynamically */
.print-watermark {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  pointer-events: none;
  /* Text styling is set dynamically in JavaScript */
}

/* High contrast mode */
.high-contrast {
  --text-primary: #000000;
  --text-secondary: #333333;
  --background-primary: #ffffff;
  --background-secondary: #f0f0f0;
  --accent-color: #0000ff;
  --border-color: #000000;
  --normal-color: #006600;
  --warning-color: #996600;
  --abnormal-color: #cc0000;
}

/* Base container */
.high-contrast.senior-dashboard {
  background-color: var(--background-primary);
}

/* Dashboard header */
.high-contrast .dashboard-title {
  color: var(--text-primary);
}

.high-contrast .dashboard-subtitle {
  color: var(--text-secondary);
}

/* Tab navigation */
.high-contrast .senior-tab {
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.high-contrast .senior-tab.active {
  color: var(--accent-color);
  border-bottom: 4px solid var(--accent-color);
  background-color: var(--background-secondary);
}

/* Section containers */
.high-contrast .dashboard-section {
  background-color: var(--background-primary);
  border: 2px solid var(--border-color);
}

.high-contrast .section-title {
  color: var(--text-primary);
}

/* Metric cards */
.high-contrast .metric-card {
  background-color: var(--background-primary);
  border: 2px solid var(--border-color);
  border-left: 6px solid var(--accent-color);
}

.high-contrast .metric-icon {
  background-color: var(--background-secondary);
  color: var(--accent-color);
  border: 1px solid var(--border-color);
}

.high-contrast .metric-title {
  color: var(--text-primary);
}

.high-contrast .metric-value {
  color: var(--text-primary);
}

.high-contrast .metric-unit {
  color: var(--text-secondary);
}

.high-contrast .metric-context {
  color: var(--text-secondary);
}

/* Status indicators */
.high-contrast .status-normal {
  background-color: #e6ffe6;
  color: var(--normal-color);
  border: 2px solid var(--normal-color);
}

.high-contrast .status-warning {
  background-color: #fff9e6;
  color: var(--warning-color);
  border: 2px solid var(--warning-color);
}

.high-contrast .status-abnormal {
  background-color: #ffe6e6;
  color: var(--abnormal-color);
  border: 2px solid var(--abnormal-color);
}

/* Medication items */
.high-contrast .medication-item {
  background-color: var(--background-primary);
  border: 2px solid var(--border-color);
  border-left: 6px solid var(--accent-color);
}

.high-contrast .medication-name {
  color: var(--text-primary);
}

.high-contrast .medication-details {
  color: var(--text-secondary);
}

/* Visit summary */
.high-contrast .visit-summary {
  background-color: var(--background-primary);
  border: 2px solid var(--border-color);
}

.high-contrast .visit-date {
  color: var(--text-primary);
}

.high-contrast .visit-doctor {
  color: var(--text-secondary);
}

.high-contrast .visit-reason {
  color: var(--text-primary);
}

.high-contrast .visit-diagnosis {
  color: var(--text-primary);
  background-color: var(--background-secondary);
  border-left: 4px solid var(--accent-color);
}
