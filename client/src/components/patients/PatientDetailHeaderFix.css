/* Fix for black box in patient view header in dark mode */

/* Target the Card component in the patient header */
.dark-mode .MuiCard-root {
  background-color: transparent !important;
}

/* Target the CardContent component in the patient header */
.dark-mode .MuiCard-root .MuiCardContent-root {
  background-color: transparent !important;
}

/* Target the Paper component in the health data source indicator */
.dark-mode .MuiPaper-root {
  background-color: transparent !important;
}

/* Ensure all Box components inside the patient header have transparent backgrounds */
.dark-mode .MuiCard-root .MuiBox-root {
  background-color: transparent !important;
}

/* Ensure all Grid components inside the patient header have transparent backgrounds */
.dark-mode .MuiCard-root .MuiGrid-root {
  background-color: transparent !important;
}

/* Ensure all Typography components inside the patient header have transparent backgrounds */
.dark-mode .MuiCard-root .MuiTypography-root {
  background-color: transparent !important;
}

/* Ensure all Chip components inside the patient header have the correct background */
.dark-mode .MuiCard-root .MuiChip-root {
  background-color: rgba(255, 255, 255, 0.15) !important;
}

/* Specific fix for the patient header card */
.dark-mode .MuiCard-root[style*="elevation"] {
  background-color: #1e1e1e !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Fix for the health data source indicator */
.dark-mode .MuiPaper-root[style*="elevation=0"] {
  background-color: rgba(14, 165, 233, 0.1) !important;
  border: 1px solid rgba(14, 165, 233, 0.2) !important;
}

/* Fix for the Key Health Indicators section */
.dark-mode .MuiTypography-root[style*="Key Health Indicators"] {
  background-color: transparent !important;
}

/* Fix for the health metric cards */
.dark-mode .MuiCard-root[style*="borderRadius: 2"] {
  background-color: #1e1e1e !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Fix for the Box components inside the health metric cards */
.dark-mode .MuiCard-root .MuiBox-root[style*="display: flex"] {
  background-color: transparent !important;
}

/* Fix for the Grid components inside the health metric cards */
.dark-mode .MuiCard-root .MuiGrid-container {
  background-color: transparent !important;
}

/* Fix for the Grid items inside the health metric cards */
.dark-mode .MuiCard-root .MuiGrid-item {
  background-color: transparent !important;
}

/* More targeted fix for any black boxes in the patient view */
.dark-mode .patient-detail-container * {
  background-color: transparent !important;
}

/* Exceptions for elements that need specific background colors */
.dark-mode .patient-detail-container .MuiCard-root {
  background-color: #1e1e1e !important;
}

.dark-mode .patient-detail-container .MuiChip-root {
  background-color: rgba(255, 255, 255, 0.15) !important;
}

.dark-mode .patient-detail-container .MuiPaper-root[style*="elevation=0"] {
  background-color: rgba(14, 165, 233, 0.1) !important;
}

/* Fix for the colored boxes behind health indicator icons */
.dark-mode .patient-detail-container .MuiBox-root[style*="borderRadius: 1"] {
  background-color: inherit !important;
}

/* Fix for the icon containers in health metric cards */
.dark-mode .patient-detail-container .MuiBox-root[style*="width: 32"] {
  background-color: inherit !important;
}

/* Fix for the icon containers in health metric cards */
.dark-mode .patient-detail-container [class*="bgcolor"] {
  background-color: inherit !important;
}

/* Fix for the black boxes behind health indicator titles */
.dark-mode .patient-detail-container .MuiTypography-subtitle2 {
  background-color: transparent !important;
}

/* Fix for the black boxes behind BLOOD GLUCOSE, BLOOD PRESSURE, etc. */
.dark-mode .patient-detail-container .MuiTypography-subtitle2[style*="textTransform: uppercase"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="textTransform: uppercase"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="BLOOD GLUCOSE"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="BLOOD PRESSURE"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="HEART RATE"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="BMI"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="TEMPERATURE"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="OXYGEN SATURATION"] {
  background-color: transparent !important;
}

/* Fix for the black boxes behind health indicator values */
.dark-mode .patient-detail-container .MuiTypography-h5,
.dark-mode .patient-detail-container .MuiTypography-h5[style*="fontWeight: 700"],
.dark-mode .patient-detail-container .MuiTypography-h5[style*="fontSize: 1.5rem"],
.dark-mode .patient-detail-container .MuiTypography-h5[style*="fontSize: 1.3rem"] {
  background-color: transparent !important;
}

/* Fix for the black boxes behind the values */
.dark-mode .patient-detail-container .MuiTypography-root[style*="72"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="148/85"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="152/84"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="56"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="64"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="20.4"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="36.2"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="96"] {
  background-color: transparent !important;
}

/* Fix for the black boxes behind normal range text */
.dark-mode .patient-detail-container .MuiTypography-caption,
.dark-mode .patient-detail-container .MuiTypography-caption[style*="Normal range"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="Normal range"] {
  background-color: transparent !important;
}

/* Fix for the New Visit button to make it more visible in dark mode */
.dark-mode .patient-detail-container .MuiButton-containedPrimary {
  background-color: #f44336 !important; /* Use error.main color */
  color: white !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
}

/* Fix for the Edit Patient button to make it more visible in dark mode */
.dark-mode .patient-detail-container .MuiButton-outlined {
  border: 1px solid rgba(255, 255, 255, 0.5) !important;
  color: white !important;
}

/* Fix for the Delete Patient button to make it more visible in dark mode */
.dark-mode .patient-detail-container .MuiButton-outlinedError {
  border: 1px solid rgba(244, 67, 54, 0.5) !important;
  color: #f44336 !important;
}

/* Fix for the black boxes behind lying/standing labels */
.dark-mode .patient-detail-container .MuiTypography-caption[style*="display: flex"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="Lying"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="Standing"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="Height"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="Weight"] {
  background-color: transparent !important;
}

/* Fix for the black boxes behind BMI classification */
.dark-mode .patient-detail-container .MuiTypography-body2,
.dark-mode .patient-detail-container .MuiTypography-body2[style*="Normal weight"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="Normal weight"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="mg/dL"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="mmHg"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="bpm"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="°C"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="%"] {
  background-color: transparent !important;
}

/* Final catch-all fix for any remaining black boxes in the health metric cards */
.dark-mode .patient-detail-container .MuiCard-root .MuiBox-root,
.dark-mode .patient-detail-container .MuiCard-root .MuiGrid-root,
.dark-mode .patient-detail-container .MuiCard-root .MuiTypography-root {
  background-color: transparent !important;
}

/* Fix for the black box behind the patient name in the header */
.dark-mode .patient-detail-container .MuiCard-root h1,
.dark-mode .patient-detail-container .MuiCard-root h5,
.dark-mode .patient-detail-container .MuiTypography-h5,
.dark-mode .patient-detail-container .MuiTypography-h5[style*="fontWeight: 600"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="New User"],
.dark-mode .patient-detail-container .MuiTypography-root[style*="first_name"],
.dark-mode .patient-detail-container .MuiTypography-root[component="h1"] {
  background-color: transparent !important;
}

/* Fix for the black box behind the patient header */
.dark-mode .patient-detail-container .MuiCard-root[style*="elevation=2"] {
  background-color: #1e1e1e !important;
}

/* Fix for the black background in the patient header */
.dark-mode .patient-detail-container .MuiCard-root[style*="bgcolor: transparent"] {
  background-color: #1e1e1e !important;
}

/* Fix for the black box behind the patient name */
.dark-mode .patient-detail-container .MuiCard-root[style*="elevation=2"] .MuiTypography-root {
  background-color: transparent !important;
}

/* Final catch-all fix for the black box behind the patient name */
.dark-mode .patient-detail-container .MuiCard-root .MuiCardContent-root .MuiBox-root .MuiTypography-root {
  background-color: transparent !important;
}

/* Fix for the black box behind the patient name in the header */
.dark-mode .patient-detail-container .MuiCard-root .MuiCardContent-root .MuiBox-root .MuiBox-root .MuiTypography-h5 {
  background-color: transparent !important;
}

/* Direct fix for the "New User" text */
.dark-mode .patient-detail-container h1,
.dark-mode .patient-detail-container h5,
.dark-mode .patient-detail-container .MuiTypography-h5 {
  background-color: transparent !important;
}

/* Nuclear option - fix any remaining black boxes in the patient header */
.dark-mode .patient-detail-container .MuiCard-root:first-of-type * {
  background-color: transparent !important;
}

.dark-mode .patient-detail-container .MuiCard-root:first-of-type {
  background-color: #1e1e1e !important;
}

/* Super specific fix for the black box behind the patient name */
.dark-mode .patient-detail-container .MuiCard-root:first-of-type .MuiCardContent-root .MuiBox-root .MuiBox-root:first-of-type .MuiBox-root:first-of-type .MuiTypography-h5 {
  background-color: transparent !important;
}

/* Extremely specific fix for the "New User" text */
.dark-mode .patient-detail-container .MuiCard-root:first-of-type .MuiCardContent-root .MuiBox-root .MuiBox-root:first-of-type .MuiBox-root:first-of-type .MuiTypography-h5[component="h1"] {
  background-color: transparent !important;
}

/* Specific fix for the "Back to Patients" link */
.dark-mode .patient-detail-container .MuiCard-root:first-of-type .MuiCardContent-root .MuiBox-root .MuiBox-root:first-of-type a {
  background-color: transparent !important;
}

/* Fix for the black box behind the patient info section */
.dark-mode .patient-detail-container .MuiCard-root:first-of-type .MuiCardContent-root .MuiBox-root .MuiBox-root:first-of-type {
  background-color: transparent !important;
}
