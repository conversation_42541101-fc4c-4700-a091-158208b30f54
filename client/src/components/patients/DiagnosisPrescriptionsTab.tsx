import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Grid,
  Paper,
  useTheme,
  Divider,
  Chip,
  alpha,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  LocalHospital as DiagnosisIcon,
  Medication as MedicationIcon,
  Assignment as TreatmentIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  EventNote as FollowUpIcon,
  Share as ReferralIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material';
import { Patient } from '../../types';
import { getAllPrescriptionsByPatientId } from '../../services/prescriptionService';

interface DiagnosisPrescriptionsTabProps {
  patient: Patient | null;
  recentVisit: any | null;
}

interface Prescription {
  prescription_id: number;
  medication: string;
  dosage: string;
  frequency: string;
  duration: string;
  notes?: string;
  beers_criteria_id?: number;
  beers_criteria_name?: string;
  beers_criteria_category?: string;
  beers_criteria_recommendation?: string;
  beers_criteria_rationale?: string;
  beers_override_reason?: string;
  beers_overridden_at?: string;
  beers_overridden_by?: number;
  overridden_by_username?: string;
  created_at: string;
  visit_id?: number;
  prescription_date?: string;
}

const DiagnosisPrescriptionsTab: React.FC<DiagnosisPrescriptionsTabProps> = ({ patient, recentVisit }) => {
  const theme = useTheme();
  const data = recentVisit || patient || {};
  const dataSource = recentVisit ? 'visit' : 'patient';

  const [prescriptions, setPrescriptions] = useState<Prescription[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPrescriptions = async () => {
      if (!patient?.patient_id) return;

      try {
        setLoading(true);
        const allPrescriptions = await getAllPrescriptionsByPatientId(patient.patient_id);
        setPrescriptions(allPrescriptions);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching prescriptions:', err);
        setError('Failed to load prescriptions');
        setLoading(false);
      }
    };

    fetchPrescriptions();
  }, [patient?.patient_id]);

  return (
    <Box sx={{ p: 3 }}>
      {/* Diagnosis Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
          Diagnosis Information
        </Typography>

        <Grid container spacing={3}>
          {/* Diagnosis */}
          <Grid item xs={12} md={6}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                borderRadius: 2,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                height: '100%'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <DiagnosisIcon sx={{ color: theme.palette.primary.main, mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Diagnosis
                </Typography>
              </Box>

              {data.diagnosis ? (
                <Typography variant="body1" sx={{ p: 1 }}>
                  {data.diagnosis}
                </Typography>
              ) : (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, color: 'text.secondary', p: 1 }}>
                  <InfoIcon sx={{ fontSize: '1rem' }} />
                  <Typography variant="body1">
                    {dataSource === 'visit' ? 'No diagnosis recorded in this visit' : 'No diagnosis information available'}
                  </Typography>
                </Box>
              )}

              {data.medication_changes && (
                <>
                  <Divider sx={{ my: 1.5 }} />
                  <Typography variant="subtitle2" color="text.secondary">Medication Changes</Typography>
                  <Typography variant="body1" sx={{ mt: 0.5, p: 1 }}>
                    {data.medication_changes}
                  </Typography>
                </>
              )}
            </Paper>
          </Grid>

          {/* Treatment Plan */}
          <Grid item xs={12} md={6}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                borderRadius: 2,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                height: '100%'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TreatmentIcon sx={{ color: theme.palette.primary.main, mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Treatment Plan
                </Typography>
              </Box>

              {data.treatment_plan ? (
                <Typography variant="body1" sx={{ p: 1 }}>
                  {data.treatment_plan}
                </Typography>
              ) : (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, color: 'text.secondary', p: 1 }}>
                  <InfoIcon sx={{ fontSize: '1rem' }} />
                  <Typography variant="body1">
                    {dataSource === 'visit' ? 'No treatment plan recorded in this visit' : 'No treatment plan available'}
                  </Typography>
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      </Box>

      {/* Follow-up Instructions Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
          Follow-up & Referrals
        </Typography>

        <Grid container spacing={3}>
          {/* Follow-up Instructions */}
          <Grid item xs={12} md={6}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                borderRadius: 2,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                height: '100%'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <FollowUpIcon sx={{ color: theme.palette.primary.main, mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Follow-up Instructions
                </Typography>
              </Box>

              {data.follow_up_instructions ? (
                <Typography variant="body1" sx={{ p: 1 }}>
                  {data.follow_up_instructions}
                </Typography>
              ) : (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, color: 'text.secondary', p: 1 }}>
                  <InfoIcon sx={{ fontSize: '1rem' }} />
                  <Typography variant="body1">
                    {dataSource === 'visit' ? 'No follow-up instructions recorded in this visit' : 'No follow-up instructions available'}
                  </Typography>
                </Box>
              )}

              {data.assessment_plan && (
                <>
                  <Divider sx={{ my: 1.5 }} />
                  <Typography variant="subtitle2" color="text.secondary">Assessment Plan</Typography>
                  <Typography variant="body1" sx={{ mt: 0.5, p: 1 }}>
                    {data.assessment_plan}
                  </Typography>
                </>
              )}
            </Paper>
          </Grid>

          {/* Referrals */}
          <Grid item xs={12} md={6}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                borderRadius: 2,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                height: '100%'
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <ReferralIcon sx={{ color: theme.palette.primary.main, mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Referrals
                </Typography>
              </Box>

              {data.referrals ? (
                <Typography variant="body1" sx={{ p: 1 }}>
                  {data.referrals}
                </Typography>
              ) : (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, color: 'text.secondary', p: 1 }}>
                  <InfoIcon sx={{ fontSize: '1rem' }} />
                  <Typography variant="body1">
                    {dataSource === 'visit' ? 'No referrals recorded in this visit' : 'No referrals available'}
                  </Typography>
                </Box>
              )}
            </Paper>
          </Grid>
        </Grid>
      </Box>

      {/* Prescriptions Section */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
          Current Prescriptions
        </Typography>

        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Paper
              elevation={0}
              sx={{
                p: 2,
                borderRadius: 2,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <MedicationIcon sx={{ color: theme.palette.primary.main, mr: 1 }} />
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Medications
                </Typography>
              </Box>

              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress size={30} />
                </Box>
              ) : error ? (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
              ) : prescriptions.length === 0 ? (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, p: 2, color: 'text.secondary' }}>
                  <InfoIcon sx={{ fontSize: '1rem' }} />
                  <Typography variant="body1">
                    {dataSource === 'visit' ? 'No prescriptions recorded in this visit' : 'No prescriptions found for this patient'}
                  </Typography>
                </Box>
              ) : (
                <Grid container spacing={2}>
                  {prescriptions.map((prescription) => (
                    <Grid item xs={12} key={prescription.prescription_id}>
                      <Paper
                        elevation={0}
                        sx={{
                          p: 2,
                          borderRadius: 1,
                          bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.03)' : 'rgba(0, 0, 0, 0.02)',
                          border: theme.palette.mode === 'dark' ? '1px solid rgba(255, 255, 255, 0.05)' : '1px solid rgba(0, 0, 0, 0.03)',
                          mb: 1
                        }}
                      >
                        <Grid container spacing={2}>
                          <Grid item xs={12} sm={3}>
                            <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                              {prescription.beers_criteria_id ? (
                                prescription.beers_override_reason ? (
                                  <WarningIcon sx={{ fontSize: 18, color: 'warning.main', mt: 0.3 }} />
                                ) : (
                                  <WarningIcon sx={{ fontSize: 18, color: 'error.main', mt: 0.3 }} />
                                )
                              ) : (
                                <CheckCircleIcon sx={{ fontSize: 18, color: 'success.main', mt: 0.3 }} />
                              )}
                              <Box>
                                <Typography variant="subtitle2" color="text.secondary">Medication</Typography>
                                <Typography variant="body1" fontWeight={500}>
                                  {prescription.medication}
                                </Typography>
                              </Box>
                            </Box>
                          </Grid>

                          <Grid item xs={6} sm={3}>
                            <Typography variant="subtitle2" color="text.secondary">Dosage</Typography>
                            <Typography variant="body1">{prescription.dosage || 'Not specified'}</Typography>
                          </Grid>

                          <Grid item xs={6} sm={3}>
                            <Typography variant="subtitle2" color="text.secondary">Frequency</Typography>
                            <Typography variant="body1">{prescription.frequency || 'Not specified'}</Typography>
                          </Grid>

                          <Grid item xs={6} sm={3}>
                            <Typography variant="subtitle2" color="text.secondary">Duration</Typography>
                            <Typography variant="body1">{prescription.duration || 'Not specified'}</Typography>
                          </Grid>

                          {prescription.notes && (
                            <Grid item xs={12}>
                              <Divider sx={{ my: 1 }} />
                              <Typography variant="subtitle2" color="text.secondary">Notes</Typography>
                              <Typography variant="body1">{prescription.notes}</Typography>
                            </Grid>
                          )}

                          {prescription.beers_criteria_id && (
                            <Grid item xs={12}>
                              <Divider sx={{ my: 1 }} />
                              <Box sx={{
                                p: 1,
                                borderRadius: 1,
                                bgcolor: alpha(
                                  prescription.beers_override_reason ? theme.palette.warning.main : theme.palette.error.main,
                                  0.1
                                ),
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center'
                              }}>
                                <Box>
                                  <Typography variant="body2" fontWeight={500} color={prescription.beers_override_reason ? 'warning.main' : 'error.main'}>
                                    {prescription.beers_criteria_name || 'BEERS Criteria Alert'}
                                  </Typography>
                                  {prescription.beers_criteria_recommendation && (
                                    <Typography variant="caption" display="block">
                                      {prescription.beers_criteria_recommendation}
                                    </Typography>
                                  )}
                                </Box>
                                <Chip
                                  label={prescription.beers_override_reason ? "Override Applied" : "BEERS Alert"}
                                  color={prescription.beers_override_reason ? "warning" : "error"}
                                  size="small"
                                  sx={{ fontSize: '0.7rem' }}
                                />
                              </Box>
                              {prescription.beers_override_reason && (
                                <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
                                  Override reason: {prescription.beers_override_reason}
                                </Typography>
                              )}
                            </Grid>
                          )}
                        </Grid>
                      </Paper>
                    </Grid>
                  ))}
                </Grid>
              )}
            </Paper>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
};

export default DiagnosisPrescriptionsTab;
