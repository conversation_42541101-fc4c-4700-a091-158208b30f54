import React from 'react';
import { Typography, Box } from '@mui/material';

interface TestMentalHealthTabProps {
  patient: any;
  recentVisit: any;
}

const TestMentalHealthTab: React.FC<TestMentalHealthTabProps> = ({ patient, recentVisit }) => {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" color="error" sx={{ mb: 3, fontWeight: 'bold' }}>
        TEST MENTAL HEALTH TAB IS RENDERING!
      </Typography>
      
      <Typography variant="body1">
        This is a test component to see if the Mental Health tab is rendering correctly.
      </Typography>
      
      <Typography variant="body1" sx={{ mt: 2 }}>
        Patient ID: {patient?.patient_id}
      </Typography>
      
      <Typography variant="body1" sx={{ mt: 2 }}>
        Recent Visit ID: {recentVisit?.visit_id}
      </Typography>
    </Box>
  );
};

export default TestMentalHealthTab;
