import React from 'react';
import { Grid, Box, Typography, Paper, Chip, Divider } from '@mui/material';
import { useTheme, alpha } from '@mui/material/styles';
import MonitorHeartIcon from '@mui/icons-material/MonitorHeart';
import PsychologyIcon from '@mui/icons-material/Psychology';
import MedicationIcon from '@mui/icons-material/Medication';
import ScienceIcon from '@mui/icons-material/Science';
import FitnessCenterIcon from '@mui/icons-material/FitnessCenter';
import BedIcon from '@mui/icons-material/Bed';
import WarningIcon from '@mui/icons-material/Warning';
import TrendIndicator from '../common/TrendIndicator';
import SparklineChart from '../common/SparklineChart';
import AlertBanner from '../common/AlertBanner';

interface PatientSummaryTabProps {
  patient: any;
  recentVisit: any | null;
  previousVisit?: any | null;
  visits?: any[];
}

// Helper component for section headers
const SectionHeader: React.FC<{
  title: string;
  icon: React.ReactNode;
  color?: string;
}> = ({ title, icon, color }) => {
  const theme = useTheme();
  return (
    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, gap: 1 }}>
      <Box
        sx={{
          width: 32,
          height: 32,
          borderRadius: 1,
          bgcolor: alpha(color || theme.palette.primary.main, 0.1),
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: color || theme.palette.primary.main
        }}
      >
        {icon}
      </Box>
      <Typography variant="h6" sx={{ fontWeight: 600 }}>
        {title}
      </Typography>
    </Box>
  );
};

// Helper component for metric display
const MetricItem: React.FC<{
  label: string;
  value: string | number | null | undefined;
  unit?: string;
  isAbnormal?: boolean;
  previousValue?: number | null;
  isLowerBetter?: boolean;
  sparklineData?: number[];
}> = ({
  label,
  value,
  unit = '',
  isAbnormal = false,
  previousValue = null,
  isLowerBetter = false,
  sparklineData = []
}) => {
  const theme = useTheme();
  const hasValue = value !== null && value !== undefined;
  const numericValue = hasValue && !isNaN(Number(value)) ? Number(value) : null;
  const hasPreviousValue = previousValue !== null && previousValue !== undefined;
  const hasSparklineData = sparklineData && sparklineData.length > 1;

  return (
    <Box sx={{ mb: 2 }}>
      <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 0.5 }}>
        {label}
      </Typography>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Typography
          variant="body1"
          fontWeight={500}
          color={isAbnormal ? 'error.main' : 'text.primary'}
        >
          {hasValue ? `${value}${unit}` : 'Not assessed'}
        </Typography>
        {isAbnormal && (
          <Chip
            icon={<WarningIcon sx={{ fontSize: '0.8rem !important' }} />}
            label="Abnormal"
            size="small"
            color="error"
            sx={{ height: 20, '& .MuiChip-label': { px: 1, py: 0.1 } }}
          />
        )}
        {numericValue !== null && hasPreviousValue && (
          <TrendIndicator
            current={numericValue}
            previous={previousValue}
            isLowerBetter={isLowerBetter}
          />
        )}
      </Box>
      {hasSparklineData && (
        <Box sx={{ mt: 0.5, height: 20 }}>
          <SparklineChart
            data={sparklineData}
            height={20}
            width={100}
            isLowerBetter={isLowerBetter}
          />
        </Box>
      )}
    </Box>
  );
};

const PatientSummaryTab: React.FC<PatientSummaryTabProps> = ({
  patient,
  recentVisit,
  previousVisit,
  visits = []
}) => {
  const theme = useTheme();
  const data = recentVisit || patient || {};
  const dataSource = recentVisit ? 'visit' : 'patient';

  // Helper function to check if a value is abnormal
  const isAbnormal = (value: number | null | undefined, min: number, max: number): boolean => {
    if (value === null || value === undefined) return false;
    return value < min || value > max;
  };

  // Helper function to safely access properties that might not exist in the type
  const getField = (field: string, source: any = null): any => {
    const dataSource = source || (recentVisit ? recentVisit : patient);
    if (!dataSource) return null;

    // Log the data source for debugging
    if (!source) {
      console.log('Data source:', dataSource);
    }

    // Check if the field exists directly
    if ((dataSource as any)[field] !== undefined) {
      return (dataSource as any)[field];
    }

    // Check if the field exists in vital_signs
    if (dataSource.vital_signs && (dataSource.vital_signs as any)[field] !== undefined) {
      return (dataSource.vital_signs as any)[field];
    }

    // Check if the field exists in mental_health
    if (dataSource.mental_health && (dataSource.mental_health as any)[field] !== undefined) {
      return (dataSource.mental_health as any)[field];
    }

    // Check if the field exists in pain_assessment
    if (dataSource.pain_assessment && (dataSource.pain_assessment as any)[field] !== undefined) {
      return (dataSource.pain_assessment as any)[field];
    }

    return null;
  };

  // Helper function to get previous value for trend calculation
  const getPreviousValue = (field: string): any => {
    if (!previousVisit) return null;
    return getField(field, previousVisit);
  };

  // Extract vital signs data
  // Try to get from vital_signs object first, then direct fields
  const vitalSigns = (recentVisit && recentVisit.vital_signs) || {};

  // Blood pressure
  const systolicBP = getField('lying_bp_systolic') ||
                     (vitalSigns.lying_bp_systolic) ||
                     getField('systolic_bp');

  const diastolicBP = getField('lying_bp_diastolic') ||
                      (vitalSigns.lying_bp_diastolic) ||
                      getField('diastolic_bp');

  const bloodPressure = systolicBP && diastolicBP ? `${systolicBP}/${diastolicBP}` :
                        vitalSigns.blood_pressure || null;

  // Heart rate
  const heartRate = getField('lying_heart_rate') ||
                    (vitalSigns.lying_heart_rate) ||
                    (vitalSigns.heart_rate) ||
                    getField('heart_rate');

  // Temperature
  const temperature = getField('temperature') ||
                      (vitalSigns.temperature) ||
                      getField('body_temperature');

  // Other vitals
  const respiratoryRate = getField('respiratory_rate') || (vitalSigns.respiratory_rate);
  const bloodGlucose = getField('blood_glucose');
  const weight = getField('weight') || (vitalSigns.weight);
  const height = getField('height') || (vitalSigns.height);
  const bmi = getField('bmi') || (vitalSigns.bmi);

  // Log the values for debugging
  console.log('Vital signs data:', {
    systolicBP,
    diastolicBP,
    heartRate,
    temperature,
    bloodGlucose,
    bmi
  });

  // Extract previous vital signs data
  const prevVitalSigns = (previousVisit && previousVisit.vital_signs) || {};

  const prevSystolicBP = getPreviousValue('lying_bp_systolic') ||
                         (prevVitalSigns.lying_bp_systolic) ||
                         getPreviousValue('systolic_bp');

  const prevDiastolicBP = getPreviousValue('lying_bp_diastolic') ||
                          (prevVitalSigns.lying_bp_diastolic) ||
                          getPreviousValue('diastolic_bp');

  const prevHeartRate = getPreviousValue('lying_heart_rate') ||
                        (prevVitalSigns.lying_heart_rate) ||
                        (prevVitalSigns.heart_rate) ||
                        getPreviousValue('heart_rate');

  const prevTemperature = getPreviousValue('temperature') ||
                          (prevVitalSigns.temperature) ||
                          getPreviousValue('body_temperature');

  const prevBloodGlucose = getPreviousValue('blood_glucose');
  const prevBMI = getPreviousValue('bmi') || (prevVitalSigns.bmi);

  // Extract mental health data
  const mentalHealth = (recentVisit && recentVisit.mental_health) || {};

  const miniCogScore = getField('mini_cog_total_score') ||
    mentalHealth.mini_cog_total_score ||
    ((getField('mini_cog_word_recall_score') || mentalHealth.mini_cog_word_recall_score) &&
     (getField('mini_cog_clock_drawing_score') || mentalHealth.mini_cog_clock_drawing_score) ?
      parseInt(String(getField('mini_cog_word_recall_score') || mentalHealth.mini_cog_word_recall_score)) +
      parseInt(String(getField('mini_cog_clock_drawing_score') || mentalHealth.mini_cog_clock_drawing_score)) :
      null);

  const phq9Score = getField('phq9_total') ||
                    mentalHealth.phq9_total ||
                    getField('depression_score') ||
                    mentalHealth.depression_score;

  const gad7Score = getField('gad7_total') ||
                    mentalHealth.gad7_total ||
                    getField('anxiety_score') ||
                    mentalHealth.anxiety_score;

  // Log mental health data for debugging
  console.log('Mental health data:', {
    mentalHealth,
    miniCogScore,
    phq9Score,
    gad7Score
  });

  // Extract previous mental health data
  const prevMentalHealth = (previousVisit && previousVisit.mental_health) || {};

  const prevMiniCogScore = getPreviousValue('mini_cog_total_score') ||
    prevMentalHealth.mini_cog_total_score ||
    ((getPreviousValue('mini_cog_word_recall_score') || prevMentalHealth.mini_cog_word_recall_score) &&
     (getPreviousValue('mini_cog_clock_drawing_score') || prevMentalHealth.mini_cog_clock_drawing_score) ?
      parseInt(String(getPreviousValue('mini_cog_word_recall_score') || prevMentalHealth.mini_cog_word_recall_score)) +
      parseInt(String(getPreviousValue('mini_cog_clock_drawing_score') || prevMentalHealth.mini_cog_clock_drawing_score)) :
      null);

  const prevPhq9Score = getPreviousValue('phq9_total') ||
                        prevMentalHealth.phq9_total ||
                        getPreviousValue('depression_score') ||
                        prevMentalHealth.depression_score;

  const prevGad7Score = getPreviousValue('gad7_total') ||
                        prevMentalHealth.gad7_total ||
                        getPreviousValue('anxiety_score') ||
                        prevMentalHealth.anxiety_score;

  // Extract sleep & pain data
  const painAssessment = (recentVisit && recentVisit.pain_assessment) || {};
  const sleepAssessment = (recentVisit && recentVisit.sleep_assessment) || {};

  const psqiScore = getField('psqi_total_score') || sleepAssessment.psqi_total_score;
  const painLevel = getField('pain_level') || painAssessment.pain_level;
  const painLocation = getField('pain_location') || painAssessment.pain_location;

  // Log sleep & pain data for debugging
  console.log('Sleep & pain data:', {
    psqiScore,
    painLevel,
    painLocation
  });

  // Extract previous sleep & pain data
  const prevPainAssessment = (previousVisit && previousVisit.pain_assessment) || {};
  const prevSleepAssessment = (previousVisit && previousVisit.sleep_assessment) || {};

  const prevPsqiScore = getPreviousValue('psqi_total_score') || prevSleepAssessment.psqi_total_score;
  const prevPainLevel = getPreviousValue('pain_level') || prevPainAssessment.pain_level;

  // Generate alerts for abnormal values
  const generateAlerts = () => {
    const alerts = [];

    // Blood pressure alerts
    if (systolicBP > 140 || diastolicBP > 90) {
      alerts.push({
        name: 'High Blood Pressure',
        value: `${systolicBP}/${diastolicBP} mmHg`,
        severity: 'warning' as const,
        details: 'Above normal range (120/80 mmHg)'
      });
    } else if (systolicBP < 90 || diastolicBP < 60) {
      alerts.push({
        name: 'Low Blood Pressure',
        value: `${systolicBP}/${diastolicBP} mmHg`,
        severity: 'warning' as const,
        details: 'Below normal range (90/60 mmHg)'
      });
    }

    // Heart rate alerts
    if (heartRate > 100) {
      alerts.push({
        name: 'Elevated Heart Rate',
        value: `${heartRate} bpm`,
        severity: 'warning' as const,
        details: 'Above normal range (60-100 bpm)'
      });
    } else if (heartRate < 60) {
      alerts.push({
        name: 'Low Heart Rate',
        value: `${heartRate} bpm`,
        severity: 'warning' as const,
        details: 'Below normal range (60-100 bpm)'
      });
    }

    // Blood glucose alerts
    if (bloodGlucose > 140) {
      alerts.push({
        name: 'High Blood Glucose',
        value: `${bloodGlucose} mg/dL`,
        severity: 'warning' as const,
        details: 'Above normal range (70-140 mg/dL)'
      });
    } else if (bloodGlucose < 70) {
      alerts.push({
        name: 'Low Blood Glucose',
        value: `${bloodGlucose} mg/dL`,
        severity: 'warning' as const,
        details: 'Below normal range (70-140 mg/dL)'
      });
    }

    // Temperature alerts
    if (temperature > 37.5) {
      alerts.push({
        name: 'Elevated Temperature',
        value: `${temperature}°C`,
        severity: 'warning' as const,
        details: 'Above normal range (36.1-37.2°C)'
      });
    } else if (temperature < 36) {
      alerts.push({
        name: 'Low Temperature',
        value: `${temperature}°C`,
        severity: 'warning' as const,
        details: 'Below normal range (36.1-37.2°C)'
      });
    }

    // Mental health alerts
    if (phq9Score && phq9Score > 9) {
      alerts.push({
        name: 'Depression Risk',
        value: `PHQ-9 Score: ${phq9Score}/27`,
        severity: 'warning' as const,
        details: 'Score indicates moderate depression'
      });
    }

    if (gad7Score && gad7Score > 9) {
      alerts.push({
        name: 'Anxiety Risk',
        value: `GAD-7 Score: ${gad7Score}/21`,
        severity: 'warning' as const,
        details: 'Score indicates moderate anxiety'
      });
    }

    if (miniCogScore !== null && miniCogScore < 3) {
      alerts.push({
        name: 'Cognitive Impairment Risk',
        value: `Mini-Cog Score: ${miniCogScore}/5`,
        severity: 'warning' as const,
        details: 'Score indicates possible cognitive impairment'
      });
    }

    // Pain alerts
    if (painLevel && painLevel > 4) {
      alerts.push({
        name: 'Significant Pain',
        value: `Pain Level: ${painLevel}/10`,
        severity: 'warning' as const,
        details: painLocation ? `Location: ${painLocation}` : 'Requires attention'
      });
    }

    return alerts;
  };

  const alerts = generateAlerts();

  // Extract lab results
  const hemoglobin = getField('hemoglobin');
  const whiteBloodCellCount = getField('white_blood_cell_count');
  const plateletCount = getField('platelet_count');
  const sodium = getField('sodium');
  const potassium = getField('potassium');
  const creatinine = getField('creatinine');

  // Extract diagnoses & medications
  const diagnoses = getField('diagnoses') || [];
  const medications = getField('medications') || [];

  // Helper function to extract historical data for sparklines
  const getHistoricalData = (field: string, count: number = 5): number[] => {
    if (!visits || visits.length < 2) return [];

    // Get the most recent visits (up to count)
    const recentVisits = visits.slice(0, Math.min(count, visits.length));

    // Extract the field values from each visit
    return recentVisits.map(visit => {
      // Try to get the value from different possible locations
      let value = null;

      // Direct field access
      if ((visit as any)[field] !== undefined) {
        value = (visit as any)[field];
      }
      // Check in vital_signs
      else if (visit.vital_signs && (visit.vital_signs as any)[field] !== undefined) {
        value = (visit.vital_signs as any)[field];
      }
      // Check in mental_health
      else if (visit.mental_health && (visit.mental_health as any)[field] !== undefined) {
        value = (visit.mental_health as any)[field];
      }
      // Check in pain_assessment
      else if (visit.pain_assessment && (visit.pain_assessment as any)[field] !== undefined) {
        value = (visit.pain_assessment as any)[field];
      }
      // Check in sleep_assessment
      else if (visit.sleep_assessment && (visit.sleep_assessment as any)[field] !== undefined) {
        value = (visit.sleep_assessment as any)[field];
      }

      return value !== null && value !== undefined && !isNaN(Number(value)) ? Number(value) : null;
    })
    .filter((value): value is number => value !== null);
  };

  // Get historical data for key metrics
  const bpHistoricalData = getHistoricalData('lying_bp_systolic').map((systolic, index) => {
    const diastolic = getHistoricalData('lying_bp_diastolic')[index];
    return systolic && diastolic ? (systolic + diastolic) / 2 : null;
  }).filter((value): value is number => value !== null);

  const heartRateHistoricalData = getHistoricalData('lying_heart_rate');
  const temperatureHistoricalData = getHistoricalData('temperature');
  const bloodGlucoseHistoricalData = getHistoricalData('blood_glucose');
  const bmiHistoricalData = getHistoricalData('bmi');
  const phq9HistoricalData = getHistoricalData('phq9_total');
  const gad7HistoricalData = getHistoricalData('gad7_total');

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="body1" sx={{ mb: 3 }}>
        This summary provides an overview of the patient's current health status, highlighting key metrics from their most recent assessment.
        {dataSource === 'visit' && recentVisit && (
          <> Data shown is from the visit on {new Date(recentVisit.visit_date).toLocaleDateString('en-GB')}.</>
        )}
      </Typography>

      {/* Alerts Section */}
      {alerts.length > 0 && (
        <AlertBanner
          title="Areas Needing Attention"
          alerts={alerts}
          sx={{ mb: 3 }}
        />
      )}

      <Grid container spacing={3}>
        {/* Vital Signs Section */}
        <Grid item xs={12} md={6}>
          <Paper
            elevation={0}
            sx={{
              p: 2,
              borderRadius: 2,
              border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
              height: '100%'
            }}
          >
            <SectionHeader
              title="Vital Signs"
              icon={<MonitorHeartIcon />}
              color={theme.palette.primary.main}
            />

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <MetricItem
                  label="Blood Pressure"
                  value={bloodPressure}
                  unit=" mmHg"
                  isAbnormal={systolicBP > 140 || diastolicBP > 90 || systolicBP < 90 || diastolicBP < 60}
                  previousValue={prevSystolicBP && prevDiastolicBP ? (prevSystolicBP + prevDiastolicBP) : null}
                  isLowerBetter={true}
                  sparklineData={bpHistoricalData}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <MetricItem
                  label="Heart Rate"
                  value={heartRate}
                  unit=" bpm"
                  isAbnormal={isAbnormal(heartRate, 60, 100)}
                  previousValue={prevHeartRate}
                  isLowerBetter={true}
                  sparklineData={heartRateHistoricalData}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <MetricItem
                  label="Temperature"
                  value={temperature}
                  unit="°C"
                  isAbnormal={isAbnormal(temperature, 36, 37.5)}
                  previousValue={prevTemperature}
                  isLowerBetter={temperature > 37.5}
                  sparklineData={temperatureHistoricalData}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <MetricItem
                  label="Blood Glucose"
                  value={bloodGlucose}
                  unit=" mg/dL"
                  isAbnormal={isAbnormal(bloodGlucose, 70, 140)}
                  previousValue={prevBloodGlucose}
                  isLowerBetter={true}
                  sparklineData={bloodGlucoseHistoricalData}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <MetricItem
                  label="BMI"
                  value={bmi}
                  unit=" kg/m²"
                  isAbnormal={isAbnormal(bmi, 18.5, 25)}
                  previousValue={prevBMI}
                  isLowerBetter={bmi > 25}
                  sparklineData={bmiHistoricalData}
                />
              </Grid>
            </Grid>
          </Paper>
        </Grid>

        {/* Mental Health Section */}
        <Grid item xs={12} md={6}>
          <Paper
            elevation={0}
            sx={{
              p: 2,
              borderRadius: 2,
              border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`,
              height: '100%'
            }}
          >
            <SectionHeader
              title="Mental Health"
              icon={<PsychologyIcon />}
              color={theme.palette.info.main}
            />

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <MetricItem
                  label="Cognitive Assessment (Mini-Cog)"
                  value={miniCogScore}
                  unit="/5"
                  isAbnormal={miniCogScore !== null && miniCogScore < 3}
                  previousValue={prevMiniCogScore}
                  isLowerBetter={false}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <MetricItem
                  label="Depression (PHQ-9)"
                  value={phq9Score}
                  unit="/27"
                  isAbnormal={phq9Score !== null && phq9Score > 9}
                  previousValue={prevPhq9Score}
                  isLowerBetter={true}
                  sparklineData={phq9HistoricalData}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <MetricItem
                  label="Anxiety (GAD-7)"
                  value={gad7Score}
                  unit="/21"
                  isAbnormal={gad7Score !== null && gad7Score > 9}
                  previousValue={prevGad7Score}
                  isLowerBetter={true}
                  sparklineData={gad7HistoricalData}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <MetricItem
                  label="Sleep Quality (PSQI)"
                  value={psqiScore}
                  unit="/21"
                  isAbnormal={psqiScore !== null && psqiScore > 5}
                  previousValue={prevPsqiScore}
                  isLowerBetter={true}
                />
              </Grid>
              <Grid item xs={12} sm={6}>
                <MetricItem
                  label="Pain Level"
                  value={painLevel}
                  unit="/10"
                  isAbnormal={painLevel !== null && painLevel > 4}
                  previousValue={prevPainLevel}
                  isLowerBetter={true}
                />
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default PatientSummaryTab;
