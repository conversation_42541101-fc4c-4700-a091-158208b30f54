import React from 'react';
import { Box, Grid, Paper, Typography, useTheme } from '@mui/material';
import { Patient, Visit } from '../../types';

// Icons
import BloodtypeIcon from '@mui/icons-material/Bloodtype';
import MonitorHeartIcon from '@mui/icons-material/MonitorHeart';
import WaterDropIcon from '@mui/icons-material/WaterDrop';
import ScienceIcon from '@mui/icons-material/Science';
import BiotechIcon from '@mui/icons-material/Biotech';
import ColorizeIcon from '@mui/icons-material/Colorize';
import LocalPharmacyIcon from '@mui/icons-material/LocalPharmacy';
import HealthAndSafetyIcon from '@mui/icons-material/HealthAndSafety';
import MedicationIcon from '@mui/icons-material/Medication';
import OpacityIcon from '@mui/icons-material/Opacity';
import WarningIcon from '@mui/icons-material/Warning';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';

interface LabResultsTabProps {
  patient: Patient;
  recentVisit: Visit | null;
}

// Helper function to determine if a value is within normal range
const getValueStatus = (value: number | null | undefined, min: number, max: number): 'normal' | 'warning' | 'abnormal' => {
  if (value === null || value === undefined) return 'normal';

  // Define warning thresholds (slightly outside normal range)
  const warningLowerThreshold = min * 0.95;
  const warningUpperThreshold = max * 1.05;

  if (value < min || value > max) {
    if (value >= warningLowerThreshold && value <= warningUpperThreshold) {
      return 'warning';
    }
    return 'abnormal';
  }
  return 'normal';
};

// Status indicator component
const StatusIndicator = ({ status }: { status: 'normal' | 'warning' | 'abnormal' }) => {
  const theme = useTheme();

  const getStatusColor = () => {
    switch (status) {
      case 'normal':
        return theme.palette.success.main;
      case 'warning':
        return theme.palette.warning.main;
      case 'abnormal':
        return theme.palette.error.main;
      default:
        return theme.palette.text.secondary;
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'normal':
        return <CheckCircleIcon fontSize="small" sx={{ color: getStatusColor() }} />;
      case 'warning':
        return <WarningIcon fontSize="small" sx={{ color: getStatusColor() }} />;
      case 'abnormal':
        return <ErrorIcon fontSize="small" sx={{ color: getStatusColor() }} />;
      default:
        return null;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'normal':
        return 'Normal';
      case 'warning':
        return 'Borderline';
      case 'abnormal':
        return 'Abnormal';
      default:
        return '';
    }
  };

  return (
    <Box sx={{
      display: 'flex',
      alignItems: 'center',
      gap: 0.5,
      color: getStatusColor(),
      fontSize: '0.875rem',
      mt: 0.5
    }}>
      {getStatusIcon()}
      <Typography variant="body2" sx={{ color: getStatusColor() }}>
        {getStatusText()}
      </Typography>
    </Box>
  );
};

// Metric card component for consistent styling
const MetricCard = ({
  title,
  value,
  unit,
  icon,
  status,
  normalRange,
  additionalInfo,
  dataSource = 'patient'
}: {
  title: string;
  value: number | string | null | undefined;
  unit: string;
  icon: React.ReactNode;
  status?: 'normal' | 'warning' | 'abnormal';
  normalRange?: string;
  additionalInfo?: string;
  dataSource?: 'visit' | 'patient';
}) => {
  const theme = useTheme();

  return (
    <Paper
      elevation={0}
      sx={{
        p: 2,
        height: '100%',
        border: `1px solid ${theme.palette.grey[200]}`,
        borderRadius: 2,
        display: 'flex',
        flexDirection: 'column',
        boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
      }}
    >
      <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
        <Box sx={{
          width: 28,
          height: 28,
          borderRadius: 1,
          bgcolor: 'rgba(59, 130, 246, 0.1)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          {icon}
        </Box>
        {title}
      </Typography>

      <Box sx={{ flex: 1 }}>
        <Typography variant="h4" sx={{ fontWeight: 600, mb: 0.5 }}>
          {value !== null && value !== undefined ? value : (dataSource === 'visit' ? 'Not measured' : 'Not available')}
          {value !== null && value !== undefined && <Typography component="span" variant="body1" sx={{ ml: 0.5 }}>{unit}</Typography>}
        </Typography>

        {normalRange && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
            Normal range: {normalRange}
          </Typography>
        )}

        {status && value !== null && value !== undefined && <StatusIndicator status={status} />}

        {additionalInfo && (
          <Typography variant="body2" color="text.secondary" sx={{ mt: 1, fontStyle: 'italic' }}>
            {additionalInfo}
          </Typography>
        )}
      </Box>
    </Paper>
  );
};

const LabResultsTab: React.FC<LabResultsTabProps> = ({ patient, recentVisit }) => {
  const theme = useTheme();

  // Determine data source - use recent visit data if available, otherwise use patient data
  const dataSource = recentVisit ? 'visit' : 'patient';
  console.log(`Using data from ${dataSource} table`);

  // Create lab results object based on data source
  const labResults = dataSource === 'visit'
    ? {
        // Blood Glucose & Diabetes Markers
        blood_glucose: recentVisit?.blood_glucose,
        hba1c: recentVisit?.hba1c,

        // Lipid Profile
        cholesterol_total: recentVisit?.cholesterol_total,
        hdl_cholesterol: recentVisit?.hdl_cholesterol || recentVisit?.hdl,
        ldl_cholesterol: recentVisit?.ldl_cholesterol || recentVisit?.ldl,
        triglycerides: recentVisit?.triglycerides,
        vldl: null, // Not available in Visit type

        // Kidney Function Tests
        creatinine: recentVisit?.creatinine,
        egfr: recentVisit?.egfr,
        blood_urea_nitrogen: recentVisit?.blood_urea_nitrogen || recentVisit?.bun,
        uric_acid: null, // Not available in Visit type

        // Liver Function Tests
        alt: recentVisit?.alt,
        ast: recentVisit?.ast,
        alp: recentVisit?.alp,
        bilirubin_t: recentVisit?.bilirubin_t,
        albumin: recentVisit?.albumin,

        // Complete Blood Count
        hemoglobin: recentVisit?.hemoglobin,
        hematocrit: recentVisit?.hematocrit,
        rbc: null, // Not available in Visit type
        wbc: null, // Not available in Visit type
        platelets: null, // Not available in Visit type

        // Thyroid Function Tests
        tsh: recentVisit?.tsh,
        t4: recentVisit?.free_t4,
        t3: recentVisit?.free_t3,

        // Inflammatory Markers
        crp: recentVisit?.crp,
        esr: recentVisit?.esr,

        // Vitamin Status & Iron Studies
        vitamin_d: recentVisit?.vitamin_d,
        vitamin_b12: recentVisit?.vitamin_b12,
        folate: recentVisit?.folate,
        ferritin: recentVisit?.ferritin,
        iron: recentVisit?.iron,

        // Urinalysis
        urine_color: null, // Not available in Visit type
        urine_ph: null, // Not available in Visit type
        urine_protein: null, // Not available in Visit type
        urine_sugar: null, // Not available in Visit type

        // Cancer Screening
        psa: null, // Not available in Visit type
        ca125: null, // Not available in Visit type
        cancer_screening_results: recentVisit?.cancer_screening_results
      }
    : {
        // Blood Glucose & Diabetes Markers
        blood_glucose: patient.blood_glucose,
        hba1c: patient.hba1c,

        // Lipid Profile
        cholesterol_total: patient.cholesterol_total,
        hdl_cholesterol: patient.hdl_cholesterol,
        ldl_cholesterol: patient.ldl_cholesterol,
        triglycerides: patient.triglycerides,
        vldl: patient.vldl,

        // Kidney Function Tests
        creatinine: patient.creatinine,
        egfr: patient.egfr,
        blood_urea_nitrogen: patient.blood_urea_nitrogen,
        uric_acid: patient.uric_acid,

        // Liver Function Tests
        alt: patient.alt,
        ast: patient.ast,
        alp: patient.alp,
        bilirubin_t: patient.bilirubin_t,
        albumin: patient.albumin,

        // Complete Blood Count
        hemoglobin: patient.hemoglobin,
        hematocrit: patient.hematocrit,
        rbc: patient.rbc,
        wbc: patient.wbc,
        platelets: patient.platelets,

        // Thyroid Function Tests
        tsh: patient.tsh,
        t4: patient.t4,
        t3: patient.t3,

        // Inflammatory Markers
        crp: patient.crp,
        esr: patient.esr,

        // Vitamin Status & Iron Studies
        vitamin_d: patient.vitamin_d,
        vitamin_b12: patient.vitamin_b12,
        folate: patient.folate,
        ferritin: patient.ferritin,
        iron: patient.iron,

        // Urinalysis
        urine_color: patient.urine_color,
        urine_ph: patient.urine_ph,
        urine_protein: patient.urine_protein,
        urine_sugar: patient.urine_sugar,

        // Cancer Screening
        psa: patient.psa,
        ca125: patient.ca125,
        cancer_screening_results: patient.cancer_screening_results
      };

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="body1" sx={{ mb: 3 }}>
        This tab displays all laboratory test results for the patient, organized by category.
        The most recent values are shown, prioritizing data from the latest visit when available.
      </Typography>

      {/* Blood Glucose & Diabetes Markers Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Blood Glucose & Diabetes Markers
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Blood Glucose"
            value={labResults.blood_glucose}
            unit="mg/dL"
            icon={<BloodtypeIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="70-99 mg/dL"
            status={getValueStatus(Number(labResults.blood_glucose), 70, 99)}
            additionalInfo="Fasting blood glucose. 100-125 mg/dL indicates prediabetes."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="HbA1c"
            value={labResults.hba1c}
            unit="%"
            icon={<MonitorHeartIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="4.0-5.6 %"
            status={getValueStatus(Number(labResults.hba1c), 4.0, 5.6)}
            additionalInfo="5.7-6.4% indicates prediabetes. ≥6.5% indicates diabetes."
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* Lipid Profile Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Lipid Profile
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Total Cholesterol"
            value={labResults.cholesterol_total}
            unit="mg/dL"
            icon={<OpacityIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="<200 mg/dL"
            status={getValueStatus(Number(labResults.cholesterol_total), 0, 200)}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="HDL Cholesterol"
            value={labResults.hdl_cholesterol}
            unit="mg/dL"
            icon={<OpacityIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange=">40 mg/dL"
            status={getValueStatus(Number(labResults.hdl_cholesterol), 40, 100)}
            additionalInfo="Higher values are better. HDL is 'good' cholesterol."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="LDL Cholesterol"
            value={labResults.ldl_cholesterol}
            unit="mg/dL"
            icon={<OpacityIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="<100 mg/dL"
            status={getValueStatus(Number(labResults.ldl_cholesterol), 0, 100)}
            additionalInfo="Lower values are better. LDL is 'bad' cholesterol."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Triglycerides"
            value={labResults.triglycerides}
            unit="mg/dL"
            icon={<OpacityIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="<150 mg/dL"
            status={getValueStatus(Number(labResults.triglycerides), 0, 150)}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="VLDL Cholesterol"
            value={labResults.vldl}
            unit="mg/dL"
            icon={<OpacityIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="<30 mg/dL"
            status={getValueStatus(Number(labResults.vldl), 0, 30)}
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* Kidney Function Tests Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Kidney Function Tests
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Creatinine"
            value={labResults.creatinine}
            unit="mg/dL"
            icon={<ScienceIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="0.7-1.3 mg/dL"
            status={getValueStatus(Number(labResults.creatinine), 0.7, 1.3)}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="eGFR"
            value={labResults.egfr}
            unit="mL/min/1.73m²"
            icon={<ScienceIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange=">60 mL/min/1.73m²"
            status={getValueStatus(Number(labResults.egfr), 60, 200)}
            additionalInfo="Estimated glomerular filtration rate. Measures kidney function."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Blood Urea Nitrogen"
            value={labResults.blood_urea_nitrogen}
            unit="mg/dL"
            icon={<ScienceIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="7-20 mg/dL"
            status={getValueStatus(Number(labResults.blood_urea_nitrogen), 7, 20)}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Uric Acid"
            value={labResults.uric_acid}
            unit="mg/dL"
            icon={<ScienceIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="3.5-7.2 mg/dL"
            status={getValueStatus(Number(labResults.uric_acid), 3.5, 7.2)}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Urine Albumin-Creatinine Ratio"
            value={dataSource === 'visit' ? recentVisit?.urine_albumin_creatinine_ratio : (patient as any).urine_albumin_creatinine_ratio || 'Not available'}
            unit="mg/g"
            icon={<ScienceIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="<30 mg/g"
            status={dataSource === 'visit' ? getValueStatus(Number(recentVisit?.urine_albumin_creatinine_ratio), 0, 30) : 'normal'}
            additionalInfo="30-300 mg/g: Microalbuminuria, >300 mg/g: Macroalbuminuria"
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* Liver Enzymes Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Liver Enzymes
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="ALT (SGPT)"
            value={dataSource === 'visit' ? recentVisit?.alt : patient.alt}
            unit="U/L"
            icon={<LocalPharmacyIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="7-56 U/L"
            status={getValueStatus(Number(dataSource === 'visit' ? recentVisit?.alt : patient.alt), 7, 56)}
            additionalInfo="Alanine Aminotransferase. Elevated in liver damage."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="AST (SGOT)"
            value={dataSource === 'visit' ? recentVisit?.ast : patient.ast}
            unit="U/L"
            icon={<LocalPharmacyIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="5-40 U/L"
            status={getValueStatus(Number(dataSource === 'visit' ? recentVisit?.ast : patient.ast), 5, 40)}
            additionalInfo="Aspartate Aminotransferase. Elevated in liver damage."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="ALP"
            value={dataSource === 'visit' ? recentVisit?.alp : patient.alp}
            unit="U/L"
            icon={<LocalPharmacyIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="44-147 U/L"
            status={getValueStatus(Number(dataSource === 'visit' ? recentVisit?.alp : patient.alp), 44, 147)}
            additionalInfo="Alkaline Phosphatase. Elevated in liver and bone disorders."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="GGT"
            value={dataSource === 'visit' ? recentVisit?.ggt : patient.ggt}
            unit="U/L"
            icon={<LocalPharmacyIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="8-61 U/L"
            status={getValueStatus(Number(dataSource === 'visit' ? recentVisit?.ggt : patient.ggt), 8, 61)}
            additionalInfo="Gamma-Glutamyl Transferase. Sensitive to liver and biliary disease."
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* Liver Function Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Liver Function
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Total Bilirubin"
            value={dataSource === 'visit' ? recentVisit?.bilirubin_t : patient.bilirubin_t}
            unit="mg/dL"
            icon={<LocalPharmacyIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="0.1-1.2 mg/dL"
            status={getValueStatus(Number(dataSource === 'visit' ? recentVisit?.bilirubin_t : patient.bilirubin_t), 0.1, 1.2)}
            additionalInfo="Breakdown product of hemoglobin. Elevated in liver disease."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Direct Bilirubin"
            value={dataSource === 'visit' ? null : (patient as any).direct_bilirubin || 'Not available'}
            unit="mg/dL"
            icon={<LocalPharmacyIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="0.0-0.3 mg/dL"
            status="normal"
            additionalInfo="Conjugated bilirubin. Elevated in liver and biliary disease."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Albumin"
            value={dataSource === 'visit' ? recentVisit?.albumin : patient.albumin}
            unit="g/dL"
            icon={<LocalPharmacyIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="3.4-5.4 g/dL"
            status={getValueStatus(Number(dataSource === 'visit' ? recentVisit?.albumin : patient.albumin), 3.4, 5.4)}
            additionalInfo="Major protein produced by the liver. Low in liver disease."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Total Protein"
            value={dataSource === 'visit' ? null : (patient as any).total_protein || 'Not available'}
            unit="g/dL"
            icon={<LocalPharmacyIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="6.0-8.3 g/dL"
            status="normal"
            additionalInfo="Sum of albumin and globulins. Abnormal in liver and kidney disease."
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* Complete Blood Count Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Complete Blood Count
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Hemoglobin"
            value={dataSource === 'visit' ? recentVisit?.hemoglobin : patient.hemoglobin}
            unit="g/dL"
            icon={<BiotechIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="13.5-17.5 g/dL (male), 12.0-15.5 g/dL (female)"
            status={getValueStatus(
              Number(dataSource === 'visit' ? recentVisit?.hemoglobin : patient.hemoglobin),
              patient.gender === 'Male' ? 13.5 : 12.0,
              patient.gender === 'Male' ? 17.5 : 15.5
            )}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Hematocrit"
            value={dataSource === 'visit' ? recentVisit?.hematocrit : patient.hematocrit}
            unit="%"
            icon={<BiotechIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="38.3-48.6% (male), 35.5-44.9% (female)"
            status={getValueStatus(
              Number(dataSource === 'visit' ? recentVisit?.hematocrit : patient.hematocrit),
              patient.gender === 'Male' ? 38.3 : 35.5,
              patient.gender === 'Male' ? 48.6 : 44.9
            )}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Red Blood Cells"
            value={dataSource === 'visit' ? null : patient.rbc}
            unit="million/μL"
            icon={<BiotechIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="4.5-5.9 million/μL (male), 4.1-5.1 million/μL (female)"
            status={dataSource === 'visit' ? 'normal' : getValueStatus(
              Number(patient.rbc),
              patient.gender === 'Male' ? 4.5 : 4.1,
              patient.gender === 'Male' ? 5.9 : 5.1
            )}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="White Blood Cells"
            value={dataSource === 'visit' ? null : patient.wbc}
            unit="thousand/μL"
            icon={<BiotechIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="4.5-11.0 thousand/μL"
            status={dataSource === 'visit' ? 'normal' : getValueStatus(Number(patient.wbc), 4.5, 11.0)}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Platelets"
            value={dataSource === 'visit' ? null : patient.platelets}
            unit="thousand/μL"
            icon={<BiotechIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="150-450 thousand/μL"
            status={dataSource === 'visit' ? 'normal' : getValueStatus(Number(patient.platelets), 150, 450)}
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* Red Blood Cell Indices Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Red Blood Cell Indices
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="MCV"
            value={dataSource === 'visit' ? null : (patient as any).mcv || 'Not available'}
            unit="fL"
            icon={<BiotechIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="80-100 fL"
            status="normal"
            additionalInfo="Mean Corpuscular Volume. Measures the average size of red blood cells."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="MCH"
            value={dataSource === 'visit' ? null : (patient as any).mch || 'Not available'}
            unit="pg"
            icon={<BiotechIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="27-33 pg"
            status="normal"
            additionalInfo="Mean Corpuscular Hemoglobin. Measures the average amount of hemoglobin in each red blood cell."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="MCHC"
            value={dataSource === 'visit' ? null : (patient as any).mchc || 'Not available'}
            unit="g/dL"
            icon={<BiotechIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="33-36 g/dL"
            status="normal"
            additionalInfo="Mean Corpuscular Hemoglobin Concentration. Measures the average concentration of hemoglobin in a given volume of red blood cells."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="RDW"
            value={dataSource === 'visit' ? null : (patient as any).rdw || 'Not available'}
            unit="%"
            icon={<BiotechIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="11.5-14.5%"
            status="normal"
            additionalInfo="Red Cell Distribution Width. Measures the variation in size of red blood cells."
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* White Blood Cell Differential Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        White Blood Cell Differential
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Neutrophils"
            value={dataSource === 'visit' ? null : (patient as any).neutrophils || 'Not available'}
            unit="%"
            icon={<BiotechIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="40-60%"
            status="normal"
            additionalInfo="Fight bacterial infections."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Lymphocytes"
            value={dataSource === 'visit' ? null : (patient as any).lymphocytes || 'Not available'}
            unit="%"
            icon={<BiotechIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="20-40%"
            status="normal"
            additionalInfo="Fight viral infections and produce antibodies."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Monocytes"
            value={dataSource === 'visit' ? null : (patient as any).monocytes || 'Not available'}
            unit="%"
            icon={<BiotechIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="2-8%"
            status="normal"
            additionalInfo="Fight infections and help other white blood cells remove dead or damaged tissues."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Eosinophils"
            value={dataSource === 'visit' ? null : (patient as any).eosinophils || 'Not available'}
            unit="%"
            icon={<BiotechIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="1-4%"
            status="normal"
            additionalInfo="Fight parasitic infections and are involved in allergic reactions."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Basophils"
            value={dataSource === 'visit' ? null : (patient as any).basophils || 'Not available'}
            unit="%"
            icon={<BiotechIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="0.5-1%"
            status="normal"
            additionalInfo="Involved in inflammatory reactions and allergic responses."
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* Thyroid Function Tests Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Thyroid Function Tests
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="TSH"
            value={dataSource === 'visit' ? recentVisit?.tsh : patient.tsh}
            unit="μIU/mL"
            icon={<HealthAndSafetyIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="0.4-4.0 μIU/mL"
            status={getValueStatus(Number(dataSource === 'visit' ? recentVisit?.tsh : patient.tsh), 0.4, 4.0)}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="T4 (Free)"
            value={dataSource === 'visit' ? recentVisit?.free_t4 : patient.t4}
            unit="ng/dL"
            icon={<HealthAndSafetyIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="0.8-1.8 ng/dL"
            status={getValueStatus(Number(dataSource === 'visit' ? recentVisit?.free_t4 : patient.t4), 0.8, 1.8)}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="T3 (Free)"
            value={dataSource === 'visit' ? recentVisit?.free_t3 : patient.t3}
            unit="pg/mL"
            icon={<HealthAndSafetyIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="2.3-4.2 pg/mL"
            status={getValueStatus(Number(dataSource === 'visit' ? recentVisit?.free_t3 : patient.t3), 2.3, 4.2)}
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* Inflammatory Markers Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Inflammatory Markers
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="C-Reactive Protein"
            value={dataSource === 'visit' ? recentVisit?.crp : patient.crp}
            unit="mg/L"
            icon={<ErrorIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="<10 mg/L"
            status={getValueStatus(Number(dataSource === 'visit' ? recentVisit?.crp : patient.crp), 0, 10)}
            additionalInfo="Elevated levels indicate inflammation."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Erythrocyte Sedimentation Rate"
            value={dataSource === 'visit' ? recentVisit?.esr : patient.esr}
            unit="mm/hr"
            icon={<ErrorIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="0-22 mm/hr (male), 0-29 mm/hr (female)"
            status={getValueStatus(
              Number(dataSource === 'visit' ? recentVisit?.esr : patient.esr),
              0,
              patient.gender === 'Male' ? 22 : 29
            )}
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* Electrolytes and Minerals Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Electrolytes and Minerals
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Sodium"
            value={dataSource === 'visit' ? recentVisit?.sodium : patient.sodium}
            unit="mEq/L"
            icon={<WaterDropIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="135-145 mEq/L"
            status={getValueStatus(Number(dataSource === 'visit' ? recentVisit?.sodium : patient.sodium), 135, 145)}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Potassium"
            value={dataSource === 'visit' ? recentVisit?.potassium : patient.potassium}
            unit="mEq/L"
            icon={<WaterDropIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="3.5-5.0 mEq/L"
            status={getValueStatus(Number(dataSource === 'visit' ? recentVisit?.potassium : patient.potassium), 3.5, 5.0)}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Calcium"
            value={dataSource === 'visit' ? recentVisit?.calcium : patient.calcium}
            unit="mg/dL"
            icon={<WaterDropIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="8.5-10.5 mg/dL"
            status={getValueStatus(Number(dataSource === 'visit' ? recentVisit?.calcium : patient.calcium), 8.5, 10.5)}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Magnesium"
            value={dataSource === 'visit' ? recentVisit?.magnesium : patient.magnesium}
            unit="mg/dL"
            icon={<WaterDropIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="1.7-2.2 mg/dL"
            status={getValueStatus(Number(dataSource === 'visit' ? recentVisit?.magnesium : patient.magnesium), 1.7, 2.2)}
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* Vitamin Status & Iron Studies Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Vitamin Status & Iron Studies
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Vitamin D"
            value={dataSource === 'visit' ? recentVisit?.vitamin_d : patient.vitamin_d}
            unit="ng/mL"
            icon={<MedicationIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="30-100 ng/mL"
            status={getValueStatus(Number(dataSource === 'visit' ? recentVisit?.vitamin_d : patient.vitamin_d), 30, 100)}
            additionalInfo="<20 ng/mL: Deficiency, 20-30 ng/mL: Insufficiency"
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Vitamin B12"
            value={dataSource === 'visit' ? recentVisit?.vitamin_b12 : patient.vitamin_b12}
            unit="pg/mL"
            icon={<MedicationIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="200-900 pg/mL"
            status={getValueStatus(Number(dataSource === 'visit' ? recentVisit?.vitamin_b12 : patient.vitamin_b12), 200, 900)}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Folate"
            value={dataSource === 'visit' ? recentVisit?.folate : patient.folate}
            unit="ng/mL"
            icon={<MedicationIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="2.7-17.0 ng/mL"
            status={getValueStatus(Number(dataSource === 'visit' ? recentVisit?.folate : patient.folate), 2.7, 17.0)}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Ferritin"
            value={dataSource === 'visit' ? recentVisit?.ferritin : patient.ferritin}
            unit="ng/mL"
            icon={<MedicationIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="20-250 ng/mL (male), 10-120 ng/mL (female)"
            status={getValueStatus(
              Number(dataSource === 'visit' ? recentVisit?.ferritin : patient.ferritin),
              patient.gender === 'Male' ? 20 : 10,
              patient.gender === 'Male' ? 250 : 120
            )}
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Iron"
            value={dataSource === 'visit' ? recentVisit?.iron : patient.iron}
            unit="μg/dL"
            icon={<MedicationIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="65-175 μg/dL (male), 50-170 μg/dL (female)"
            status={getValueStatus(
              Number(dataSource === 'visit' ? recentVisit?.iron : patient.iron),
              patient.gender === 'Male' ? 65 : 50,
              patient.gender === 'Male' ? 175 : 170
            )}
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* Urinalysis Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Urinalysis
      </Typography>

      {/* Physical Properties */}
      <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 500, color: theme.palette.text.secondary }}>
        Physical Properties
      </Typography>

      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Urine Color"
            value={dataSource === 'visit' ? null : patient.urine_color}
            unit=""
            icon={<ColorizeIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="Pale yellow to amber"
            status="normal"
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Urine Transparency"
            value={dataSource === 'visit' ? null : (patient as any).urine_transparency || 'Not available'}
            unit=""
            icon={<ColorizeIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="Clear"
            status="normal"
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Urine pH"
            value={dataSource === 'visit' ? null : patient.urine_ph}
            unit=""
            icon={<ColorizeIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="4.5-8.0"
            status="normal"
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* Chemical Tests */}
      <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 500, color: theme.palette.text.secondary }}>
        Chemical Tests
      </Typography>

      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Urine Protein"
            value={dataSource === 'visit' ? null : patient.urine_protein}
            unit=""
            icon={<ColorizeIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="Negative"
            status="normal"
            additionalInfo="Presence may indicate kidney disease"
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Urine Sugar"
            value={dataSource === 'visit' ? null : patient.urine_sugar}
            unit=""
            icon={<ColorizeIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="Negative"
            status="normal"
            additionalInfo="Presence may indicate diabetes"
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* Microscopic Examination */}
      <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 500, color: theme.palette.text.secondary }}>
        Microscopic Examination
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="RBCs"
            value={dataSource === 'visit' ? null : (patient as any).urine_rbcs || 'Not available'}
            unit="/HPF"
            icon={<ColorizeIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="0-3 /HPF"
            status="normal"
            additionalInfo="Red blood cells in urine. Elevated in kidney disease."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Pus Cells"
            value={dataSource === 'visit' ? null : (patient as any).urine_pus_cells || 'Not available'}
            unit="/HPF"
            icon={<ColorizeIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="0-5 /HPF"
            status="normal"
            additionalInfo="White blood cells in urine. Elevated in UTI."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Epithelial Cells"
            value={dataSource === 'visit' ? null : (patient as any).urine_epithelial_cells || 'Not available'}
            unit="/HPF"
            icon={<ColorizeIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="Few"
            status="normal"
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Crystals"
            value={dataSource === 'visit' ? null : (patient as any).urine_crystals || 'Not available'}
            unit=""
            icon={<ColorizeIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="None to few"
            status="normal"
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="Casts"
            value={dataSource === 'visit' ? null : (patient as any).urine_casts || 'Not available'}
            unit=""
            icon={<ColorizeIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="None"
            status="normal"
            additionalInfo="Presence may indicate kidney disease"
            dataSource={dataSource}
          />
        </Grid>
      </Grid>

      {/* Cancer Screening Section */}
      <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 600, color: theme.palette.primary.main }}>
        Cancer Screening
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="PSA"
            value={dataSource === 'visit' ? null : (patient as any).psa || 'Not available'}
            unit="ng/mL"
            icon={<HealthAndSafetyIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="<4.0 ng/mL"
            status="normal"
            additionalInfo="Prostate-specific antigen. Used for prostate cancer screening."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <MetricCard
            title="CA-125"
            value={dataSource === 'visit' ? null : (patient as any).ca125 || 'Not available'}
            unit="U/mL"
            icon={<HealthAndSafetyIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange="<35 U/mL"
            status="normal"
            additionalInfo="Cancer antigen 125. Used for ovarian cancer monitoring."
            dataSource={dataSource}
          />
        </Grid>

        <Grid item xs={12}>
          <MetricCard
            title="Other Cancer Screening Results"
            value={dataSource === 'visit' ? recentVisit?.cancer_screening_results : (patient as any).cancer_screening_results || 'No additional screening results available.'}
            unit=""
            icon={<HealthAndSafetyIcon sx={{ color: '#3b82f6', fontSize: '1rem' }} />}
            normalRange=""
            dataSource={dataSource}
          />
        </Grid>
      </Grid>
    </Box>
  );
};

export default LabResultsTab;
