import React, { useState, useEffect } from 'react';
import { Visit, Patient } from '../../types';
import { getHealthMetricsData, getPatientVisits } from '../../services/visitService';
import { getPatientById } from '../../services/patientService';
import { Box, CircularProgress, Typography, Paper, useTheme, alpha, Card, CardContent } from '@mui/material';
import HealthMetricsChart from '../visits/HealthMetricsChart';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import TimelineIcon from '@mui/icons-material/Timeline';

interface PatientHealthChartsProps {
  patientId: number;
}

const PatientHealthCharts: React.FC<PatientHealthChartsProps> = ({ patientId }) => {
  const theme = useTheme();
  const [healthMetricsData, setHealthMetricsData] = useState<Visit[]>([]);
  const [baselineIncluded, setBaselineIncluded] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch combined health metrics data
  useEffect(() => {
    const fetchHealthMetrics = async () => {
      if (!patientId) return;

      setLoading(true);
      try {
        // Fetch both patient data and visits data
        const [patientData, visitsData] = await Promise.all([
          getPatientById(patientId),
          getPatientVisits(patientId)
        ]);

        // Create a synthetic "baseline" visit from patient data
        const baselineVisit: Visit = {
          visit_id: -1, // Use negative ID to indicate this is a synthetic visit
          patient_id: patientData.patient_id,
          visit_date: patientData.created_at,
          visit_reason: 'Initial Assessment',
          visit_type: 'baseline',

          // Copy health metrics from patient record
          lying_bp_systolic: patientData.lying_bp_systolic,
          lying_bp_diastolic: patientData.lying_bp_diastolic,
          sitting_bp_systolic: patientData.sitting_bp_systolic,
          sitting_bp_diastolic: patientData.sitting_bp_diastolic,
          standing_bp_systolic: patientData.standing_bp_systolic,
          standing_bp_diastolic: patientData.standing_bp_diastolic,
          lying_heart_rate: patientData.lying_heart_rate,
          standing_heart_rate: patientData.standing_heart_rate,
          heart_rhythm: patientData.heart_rhythm,
          temperature: patientData.temperature,
          respiratory_rate: patientData.respiratory_rate,
          pulse_oximetry: patientData.pulse_oximetry,
          weight: patientData.weight,
          height: patientData.height,
          bmi: patientData.bmi,

          // Add other health metrics from patient record
          blood_glucose: patientData.blood_glucose,
          cholesterol_total: patientData.cholesterol_total,
          hdl_cholesterol: patientData.hdl_cholesterol,
          ldl_cholesterol: patientData.ldl_cholesterol,

          // Add created_at field required by Visit interface
          created_at: patientData.created_at
        };

        // Check if the baseline visit has any health metrics data
        const hasHealthData = Object.entries(baselineVisit).some(([key, value]) => {
          // Only check health metric fields, not metadata fields
          const metricFields = [
            'lying_bp_systolic', 'lying_bp_diastolic',
            'sitting_bp_systolic', 'sitting_bp_diastolic',
            'standing_bp_systolic', 'standing_bp_diastolic',
            'lying_heart_rate', 'standing_heart_rate',
            'temperature', 'respiratory_rate', 'pulse_oximetry',
            'weight', 'height', 'bmi',
            'blood_glucose', 'cholesterol_total', 'hdl_cholesterol', 'ldl_cholesterol'
          ];

          return metricFields.includes(key) && value !== null && value !== undefined;
        });

        // Only include the baseline visit if it has health data
        let combinedData: Visit[];
        if (hasHealthData) {
          combinedData = [baselineVisit, ...visitsData];
          setBaselineIncluded(true);
        } else {
          combinedData = [...visitsData];
          setBaselineIncluded(false);
        }

        // Sort by date
        combinedData.sort((a, b) => new Date(a.visit_date).getTime() - new Date(b.visit_date).getTime());

        // Set the combined data
        setHealthMetricsData(combinedData);
      } catch (err) {
        console.error('Error fetching health metrics data:', err);
        setError('Failed to fetch health metrics data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchHealthMetrics();
  }, [patientId]);

  if (loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="40vh"
        flexDirection="column"
        gap={2}
      >
        <CircularProgress size={50} thickness={4} />
        <Typography variant="body2" color="text.secondary">
          Loading health data...
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mt: 2,
          borderRadius: 2,
          border: `1px solid ${alpha(theme.palette.error.main, 0.2)}`,
          bgcolor: alpha(theme.palette.error.main, 0.05)
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <InfoOutlinedIcon color="error" />
          <Typography color="error.main" variant="h6" fontWeight="500">
            {error}
          </Typography>
        </Box>
      </Paper>
    );
  }

  // Check if we have enough data points for visualization
  // We need at least 2 data points with health metrics
  if (healthMetricsData.length < 2) {
    // If we have baseline data but not enough visits, show a more specific message
    if (baselineIncluded && healthMetricsData.length === 1) {
      return (
        <Paper
          elevation={0}
          sx={{
            p: 3,
            mt: 2,
            borderRadius: 2,
            border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`,
            bgcolor: alpha(theme.palette.info.main, 0.05)
          }}
        >
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2, py: 2 }}>
            <InfoOutlinedIcon color="info" sx={{ fontSize: 40, opacity: 0.8 }} />
            <Typography variant="h6" align="center" color="info.main" fontWeight="500" gutterBottom>
              Not Enough Health Data
            </Typography>
            <Typography variant="body1" align="center" sx={{ maxWidth: 600 }}>
              This patient has initial health data but needs at least one follow-up visit to visualize health metrics over time.
              Please add a visit for this patient to see health trends.
            </Typography>
          </Box>
        </Paper>
      );
    }

    // Default message for not enough data
    return (
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mt: 2,
          borderRadius: 2,
          border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`,
          bgcolor: alpha(theme.palette.info.main, 0.05)
        }}
      >
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 2, py: 2 }}>
          <InfoOutlinedIcon color="info" sx={{ fontSize: 40, opacity: 0.8 }} />
          <Typography variant="h6" align="center" color="info.main" fontWeight="500" gutterBottom>
            Not Enough Health Data
          </Typography>
          <Typography variant="body1" align="center" sx={{ maxWidth: 600 }}>
            At least two data points are required to visualize health metrics over time.
            Please add more visits with health metrics for this patient to see health trends.
          </Typography>
        </Box>
      </Paper>
    );
  }

  return (
    <Card
      elevation={0}
      sx={{
        borderRadius: 2,
        border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
        overflow: 'visible'
      }}
    >
      <CardContent sx={{ p: 0 }}>
        <Box sx={{
          p: 3,
          borderBottom: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
          display: 'flex',
          alignItems: 'center',
          gap: 1.5
        }}>
          <TimelineIcon color="primary" />
          <Typography variant="h5" color="primary.main" fontWeight="500">
            Health Metrics Charts
          </Typography>
        </Box>

        <Box sx={{ p: 3, pb: 0 }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3, lineHeight: 1.6 }}>
            These charts display the progression of health metrics over time, including initial data from patient registration
            and all subsequent visits. Use the dropdown to switch between different health parameters.
          </Typography>
        </Box>

        <HealthMetricsChart visits={healthMetricsData} />
      </CardContent>
    </Card>
  );
};

export default PatientHealthCharts;