import React, { useEffect, useState, useContext } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Paper,
  List,
  ListItem,
  ListItemText,
  Divider,
  CircularProgress,
  Alert,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab,
  TextField,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  IconButton,
  Collapse,
  Tooltip,
  Avatar,
  Chip
} from '@mui/material';
import { styled, useTheme } from '@mui/material/styles';
import { useNavigate } from 'react-router-dom';
import AuthContext from '../../context/AuthContext';
import { API_URL } from '../../config';
import { getPatientVisits } from '../../services/visitService';
import { getPatientById } from '../../services/patientService';
import HealthMetricsChart from '../visits/HealthMetricsChart';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';
import SendIcon from '@mui/icons-material/Send';
import FavoriteIcon from '@mui/icons-material/Favorite';
import MonitorHeartIcon from '@mui/icons-material/MonitorHeart';
import ThermostatIcon from '@mui/icons-material/Thermostat';
import ScaleIcon from '@mui/icons-material/Scale';
import HeightIcon from '@mui/icons-material/Height';
import MedicationIcon from '@mui/icons-material/Medication';
import EventNoteIcon from '@mui/icons-material/EventNote';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import BloodtypeIcon from '@mui/icons-material/Bloodtype';
import AccessibilityNewIcon from '@mui/icons-material/AccessibilityNew';
import InfoIcon from '@mui/icons-material/Info';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import PersonIcon from '@mui/icons-material/Person';
import AirIcon from '@mui/icons-material/Air';
import WaterDropIcon from '@mui/icons-material/WaterDrop';
import RestaurantIcon from '@mui/icons-material/Restaurant';
import NoteAltIcon from '@mui/icons-material/NoteAlt';
import HelpIcon from '@mui/icons-material/Help';
import WarningIcon from '@mui/icons-material/Warning';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ContactPhoneIcon from '@mui/icons-material/ContactPhone';
import ContactEmergencyIcon from '@mui/icons-material/ContactEmergency';
import PrintIcon from '@mui/icons-material/Print';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import LocationOnIcon from '@mui/icons-material/LocationOn';
import EventAvailableIcon from '@mui/icons-material/EventAvailable';
import EditIcon from '@mui/icons-material/Edit';
import AddIcon from '@mui/icons-material/Add';

import './SeniorFriendlyDashboard.css';

interface PatientDashboardData {
  patientInfo: {
    patient_id: number;
    first_name: string;
    last_name: string;
    unique_id: string;
    date_of_birth: string;
    gender: string;
    email: string | null;
    phone: string;
    blood_type: string | null;
    allergies: string | null;
    doctor_id: number | null;
    doctor_name?: string | null;
    emergency_contact_name?: string | null;
    emergency_contact_phone?: string | null;
    emergency_contact_relationship?: string | null;
    emergency_contact_updated?: boolean | null;
  };
  visits: Array<{
    visit_id: number;
    visit_date: string;
    visit_reason: string;
    vital_signs: {
      blood_pressure: string;
      heart_rate: number | null;
      temperature: number | null;
      oxygen_level: number | null;
      weight: number | null;
      height: number | null;
      lying_bp_systolic?: number | null;
      lying_bp_diastolic?: number | null;
      standing_bp_systolic?: number | null;
      standing_bp_diastolic?: number | null;
      lying_heart_rate?: number | null;
      standing_heart_rate?: number | null;
    } | null;
    notes: string;
    diagnosis?: string;
    doctor_id: number;
    doctor_first_name: string;
    doctor_last_name: string;
  }>;
  medications: Array<{
    medication_name: string;
    dosage: string;
    instructions: string;
    prescribed_date: string;
  }>;
  latestVitals: {
    blood_pressure: string;
    heart_rate: number | null;
    temperature: number | null;
    oxygen_level: number | null;
    weight: number | null;
    height: number | null;
    lying_bp_systolic?: number | null;
    lying_bp_diastolic?: number | null;
    sitting_bp_systolic?: number | null;
    sitting_bp_diastolic?: number | null;
    standing_bp_systolic?: number | null;
    standing_bp_diastolic?: number | null;
    lying_heart_rate?: number | null;
    sitting_heart_rate?: number | null;
    standing_heart_rate?: number | null;
    bmi?: number | null;
  } | null;
  dataSource?: 'patient' | 'visit'; // Indicates whether data is from patient table or visit
}

// Add a new interface for messages
interface Message {
  id: number;
  sender: string;
  recipient: string;
  content: string;
  timestamp: string;
  read: boolean;
}

// Update the Visit interface to include orthostatic measurements
interface Visit {
  visit_id: number;
  visit_date: string;
  visit_reason: string;
  vital_signs: {
    blood_pressure: string;
    heart_rate: number;
    temperature: number;
    oxygen_level: number;
    weight: number;
    height: number;
  } | null;
  lying_bp_systolic?: number;
  lying_bp_diastolic?: number;
  sitting_bp_systolic?: number;
  sitting_bp_diastolic?: number;
  standing_bp_systolic?: number;
  standing_bp_diastolic?: number;
  lying_heart_rate?: number;
  sitting_heart_rate?: number;
  standing_heart_rate?: number;
  notes: string;
  diagnosis?: string;
  doctor_id: number;
  doctor_first_name: string;
  doctor_last_name: string;
}

// Enhanced Card styling for better readability and senior-friendly design
const StyledCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  marginBottom: theme.spacing(4),
  borderRadius: '16px',
  boxShadow: `0 4px 12px ${theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.08)'}`,
  overflow: 'hidden',
  border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.08)'}`,
  transition: 'transform 0.2s ease, box-shadow 0.2s ease',
  '&:hover': {
    boxShadow: `0 8px 24px ${theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.12)'}`,
    transform: 'translateY(-4px)'
  },
  '& .MuiCardContent-root': {
    flexGrow: 1,
    padding: theme.spacing(4),
  },
}));

// Enhanced vital sign display with larger text and better contrast
const VitalSign = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  padding: theme.spacing(3),
  borderRadius: '16px',
  backgroundColor: theme.palette.background.paper,
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
  marginBottom: theme.spacing(3),
  border: '1px solid rgba(0, 0, 0, 0.08)',
  transition: 'transform 0.2s ease',
  '&:hover': {
    transform: 'translateY(-3px)',
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
  },
  '& .MuiTypography-h6': {
    fontSize: '2rem',
    fontWeight: 600,
    marginTop: theme.spacing(1.5),
    marginBottom: theme.spacing(1),
  },
  '& .MuiTypography-body2': {
    fontSize: '1.125rem',
  }
}));

// Section title with icon
const SectionTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.75rem',
  fontWeight: 600,
  color: theme.palette.primary.main,
  marginBottom: theme.spacing(3),
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1.5),
  '& svg': {
    fontSize: '2rem'
  }
}));

// Status indicator for health metrics
const StatusIndicator = styled(Box)(({ theme, status }: { theme: any, status: 'normal' | 'warning' | 'abnormal' }) => {
  const isDarkMode = theme.palette.mode === 'dark';

  const colors = {
    normal: {
      bg: isDarkMode ? 'rgba(16, 185, 129, 0.2)' : 'rgba(16, 185, 129, 0.15)',
      color: isDarkMode ? '#34d399' : '#10b981',
      border: `1px solid ${isDarkMode ? 'rgba(16, 185, 129, 0.4)' : 'rgba(16, 185, 129, 0.3)'}`
    },
    warning: {
      bg: isDarkMode ? 'rgba(245, 158, 11, 0.2)' : 'rgba(245, 158, 11, 0.15)',
      color: isDarkMode ? '#fbbf24' : '#f59e0b',
      border: `1px solid ${isDarkMode ? 'rgba(245, 158, 11, 0.4)' : 'rgba(245, 158, 11, 0.3)'}`
    },
    abnormal: {
      bg: isDarkMode ? 'rgba(239, 68, 68, 0.2)' : 'rgba(239, 68, 68, 0.15)',
      color: isDarkMode ? '#f87171' : '#ef4444',
      border: `1px solid ${isDarkMode ? 'rgba(239, 68, 68, 0.4)' : 'rgba(239, 68, 68, 0.3)'}`
    }
  };

  return {
    display: 'inline-flex',
    alignItems: 'center',
    padding: '10px 16px',
    borderRadius: '8px',
    fontSize: '1.125rem',
    fontWeight: 600,
    backgroundColor: colors[status].bg,
    color: colors[status].color,
    border: colors[status].border,
    marginTop: theme.spacing(1.5),
    gap: theme.spacing(1),
    '& svg': {
      fontSize: '1.5rem'
    }
  };
});

// Metric Card for health data
const MetricCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  boxShadow: `0 2px 8px ${theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.2)' : 'rgba(0, 0, 0, 0.08)'}`,
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.08)'}`,
  transition: 'transform 0.2s ease, box-shadow 0.2s ease',
  '&:hover': {
    transform: 'translateY(-3px)',
    boxShadow: `0 6px 16px ${theme.palette.mode === 'dark' ? 'rgba(0, 0, 0, 0.3)' : 'rgba(0, 0, 0, 0.12)'}`,
  }
}));

// Metric header with icon
const MetricHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
  gap: theme.spacing(1.5),
}));

// Define the props interface for MetricIconContainer
interface MetricIconContainerProps {
  bgcolor?: string;
}

// Create a custom component that wraps Avatar and handles the bgcolor prop
const MetricIconContainer: React.FC<MetricIconContainerProps & React.PropsWithChildren> = ({
  bgcolor = 'primary.light',
  children,
  ...props
}) => {
  const theme = useTheme();
  const [colorName, shade] = bgcolor.split('.');

  // Handle the color in a type-safe way
  let backgroundColor = theme.palette.primary.light; // Default fallback

  // Check if the color exists in the palette
  if (colorName === 'primary' || colorName === 'secondary' ||
      colorName === 'error' || colorName === 'warning' ||
      colorName === 'info' || colorName === 'success') {
    // These are the standard palette colors
    backgroundColor = theme.palette[colorName][shade as 'light' | 'main' | 'dark'] || theme.palette.primary.light;
  }

  return (
    <Avatar
      {...props}
      sx={{
        bgcolor: backgroundColor,
        width: 56,
        height: 56,
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
      }}
    >
      {children}
    </Avatar>
  );
};

// Metric value display
const MetricValue = styled(Typography)(({ theme }) => ({
  fontSize: '2.25rem',
  fontWeight: 700,
  color: theme.palette.text.primary,
  marginTop: theme.spacing(1),
  marginBottom: theme.spacing(0.5),
}));

// Metric label
const MetricLabel = styled(Typography)(({ theme }) => ({
  fontSize: '1.25rem',
  fontWeight: 600,
  color: theme.palette.text.secondary,
}));

// Metric unit
const MetricUnit = styled('span')(({ theme }) => ({
  fontSize: '1.25rem',
  color: theme.palette.text.secondary,
  fontWeight: 500,
}));

// Metric explanation
const MetricExplanation = styled(Typography)(({ theme }) => ({
  fontSize: '1.125rem',
  color: theme.palette.text.secondary,
  marginTop: theme.spacing(1.5),
}));

// Help tooltip
const HelpTooltip = styled(Tooltip)(({ theme }) => ({
  '& .MuiTooltip-tooltip': {
    fontSize: '1rem',
    padding: theme.spacing(1.5),
    maxWidth: 300,
  }
}));

const PatientDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<PatientDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState(0);
  const [visits, setVisits] = useState<any[]>([]);
  const [patientData, setPatientData] = useState<any>(null);
  const [combinedData, setCombinedData] = useState<any[]>([]);
  const [visitsLoading, setVisitsLoading] = useState(false);
  const [expandedVisitId, setExpandedVisitId] = useState<number | null>(null);
  const [messageText, setMessageText] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const [messageSending, setMessageSending] = useState(false);
  const [messageSuccess, setMessageSuccess] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const { user } = useContext(AuthContext);
  const navigate = useNavigate();
  const theme = useTheme();


  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);
        console.log('Fetching patient dashboard data for user:', user);

        if (!user?.patient_id) {
          throw new Error('Patient ID not found');
        }

        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('No authentication token found');
        }

        // Fetch patient dashboard data
        const response = await fetch(`${API_URL}/api/patients/dashboard/${user.patient_id}`, {
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          }
        });

        if (!response.ok) {
          const errorData = await response.text();
          console.error('Dashboard response error:', errorData);
          throw new Error(`Failed to fetch dashboard data: ${errorData}`);
        }

        const data = await response.json();
        console.log('Received patient dashboard data:', data);

        // Check if visits data exists and log it
        if (data.visits) {
          console.log('Visits from dashboard data:', data.visits);
          console.log('Number of visits:', data.visits.length);

          // Add a flag to indicate if data is from visits or patient table
          if (data.visits.length > 0) {
            data.dataSource = 'visit';
            console.log('Data source: Most recent visit');
          } else {
            data.dataSource = 'patient';
            console.log('Data source: Patient record (no visits)');
          }
        } else {
          console.warn('No visits data found in dashboard response');
          data.dataSource = 'patient';
          console.log('Data source: Patient record (no visits data)');
        }

        setDashboardData(data);
      } catch (err) {
        console.error('Error fetching patient dashboard data:', err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [user]);

  // Fetch visits and patient data for health charts
  useEffect(() => {
    const fetchVisitsForCharts = async () => {
      if (!user?.patient_id) return;

      try {
        setVisitsLoading(true);

        // Fetch both patient data and visits in parallel
        const [patientInfo, visitsData] = await Promise.all([
          getPatientById(user.patient_id),
          getPatientVisits(user.patient_id)
        ]);

        console.log('Fetched patient data for charts:', patientInfo);
        console.log('Fetched visits data for charts:', visitsData);

        // Store the raw data
        setPatientData(patientInfo);
        setVisits(visitsData);

        // Create a synthetic "initial visit" from patient data
        if (patientInfo) {
          const initialVisit = {
            visit_id: -1, // Use a negative ID to indicate this is a synthetic visit
            patient_id: patientInfo.patient_id,
            visit_date: patientInfo.created_at,
            visit_reason: 'Initial Registration',

            // Copy health metrics from patient record
            lying_bp_systolic: patientInfo.lying_bp_systolic,
            lying_bp_diastolic: patientInfo.lying_bp_diastolic,
            sitting_bp_systolic: patientInfo.sitting_bp_systolic,
            sitting_bp_diastolic: patientInfo.sitting_bp_diastolic,
            standing_bp_systolic: patientInfo.standing_bp_systolic,
            standing_bp_diastolic: patientInfo.standing_bp_diastolic,
            lying_heart_rate: patientInfo.lying_heart_rate,
            standing_heart_rate: patientInfo.standing_heart_rate,
            heart_rhythm: patientInfo.heart_rhythm,
            temperature: patientInfo.temperature,
            respiratory_rate: patientInfo.respiratory_rate,
            pulse_oximetry: patientInfo.pulse_oximetry,
            weight: patientInfo.weight,
            height: patientInfo.height,
            bmi: patientInfo.bmi,

            // Add other fields required by the Visit interface
            created_at: patientInfo.created_at
          };

          // Check if the initial visit has any health metrics data
          const hasHealthData = Object.entries(initialVisit).some(([key, value]) => {
            // Only check health metric fields, not metadata fields
            const metricFields = [
              'lying_bp_systolic', 'lying_bp_diastolic',
              'sitting_bp_systolic', 'sitting_bp_diastolic',
              'standing_bp_systolic', 'standing_bp_diastolic',
              'lying_heart_rate', 'standing_heart_rate',
              'temperature', 'respiratory_rate', 'pulse_oximetry',
              'weight', 'height', 'bmi'
            ];

            return metricFields.includes(key) && value !== null && value !== undefined;
          });

          // Only include the initial visit if it has health data
          const combined = hasHealthData
            ? [initialVisit, ...visitsData]
            : [...visitsData];

          setCombinedData(combined);

          console.log('Combined data for visualization:', {
            initialVisitIncluded: hasHealthData,
            totalDataPoints: combined.length,
            firstPoint: combined[0]
          });
        }
      } catch (err) {
        console.error('Error fetching data for health charts:', err);
        // We don't set the main error state here to avoid interrupting the dashboard display
      } finally {
        setVisitsLoading(false);
      }
    };

    fetchVisitsForCharts();
  }, [user?.patient_id]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  const calculateAge = (dateOfBirth: string): number => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();

    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  // Mock function to simulate sending a message to the doctor
  const handleSendMessage = async () => {
    if (!messageText.trim() || !dashboardData?.patientInfo.doctor_id) return;

    setMessageSending(true);

    try {
      // In a real app, this would be an API call to save the message
      // For now, we'll simulate a successful message send
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Create a new message object
      const newMessage: Message = {
        id: Date.now(),
        sender: `${dashboardData.patientInfo.first_name} ${dashboardData.patientInfo.last_name}`,
        recipient: dashboardData.patientInfo.doctor_name || 'Your Doctor',
        content: messageText,
        timestamp: new Date().toISOString(),
        read: false
      };

      // Add the new message to the messages array
      setMessages(prevMessages => [...prevMessages, newMessage]);
      setMessageText('');
      setMessageSuccess(true);
      setOpenDialog(true);

      // Reset success message after 3 seconds
      setTimeout(() => {
        setMessageSuccess(false);
      }, 3000);
    } catch (err) {
      console.error('Error sending message:', err);
    } finally {
      setMessageSending(false);
    }
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const toggleExpandVisit = (visitId: number) => {
    setExpandedVisitId(expandedVisitId === visitId ? null : visitId);
  };





  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="80vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={3}>
        <Alert severity="error">{error}</Alert>
        <Box mt={2} display="flex" justifyContent="center">
          <Button variant="contained" color="primary" onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </Box>
      </Box>
    );
  }

  if (!dashboardData) {
    return (
      <Box p={3}>
        <Alert severity="warning">No data available. Please check back later.</Alert>
      </Box>
    );
  }

  const { patientInfo, visits: recentVisits, medications, latestVitals } = dashboardData;

  // Helper function to determine if a value is abnormal
  const getValueStatus = (value: number | null | undefined, normalMin: number, normalMax: number): 'normal' | 'warning' | 'abnormal' => {
    if (value === undefined || value === null) return 'normal';

    const numValue = Number(value);
    if (isNaN(numValue)) return 'normal';

    if (numValue < normalMin * 0.9 || numValue > normalMax * 1.1) {
      return 'abnormal';
    } else if (numValue < normalMin || numValue > normalMax) {
      return 'warning';
    }
    return 'normal';
  };

  // Helper function to get normal range text
  const getNormalRangeText = (min: number, max: number, unit: string): string => {
    return `Normal range: ${min}-${max} ${unit}`;
  };

  // Set the current date for printing
  const currentDate = new Date().toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });

  // Mock upcoming appointments (in a real app, this would come from the API)
  const upcomingAppointments = [
    {
      id: 1,
      date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
      time: '10:30 AM',
      doctor: patientInfo?.doctor_name || 'Your Doctor',
      type: 'Regular Checkup',
      location: 'Main Clinic, Room 204'
    },
    {
      id: 2,
      date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      time: '2:15 PM',
      doctor: patientInfo?.doctor_name || 'Your Doctor',
      type: 'Follow-up Visit',
      location: 'Main Clinic, Room 108'
    }
  ];

  // Add a hidden image for the watermark
  const handlePrintWithWatermark = () => {
    // Create a watermark element
    const watermarkImg = document.createElement('div');
    watermarkImg.className = 'print-watermark';
    watermarkImg.style.display = 'none';

    // Create a text watermark instead of an image
    watermarkImg.textContent = 'WiseAge Wellness';
    watermarkImg.style.color = 'rgba(0, 0, 0, 0.1)';
    watermarkImg.style.fontSize = '120px';
    watermarkImg.style.fontWeight = 'bold';
    watermarkImg.style.textAlign = 'center';
    watermarkImg.style.display = 'flex';
    watermarkImg.style.alignItems = 'center';
    watermarkImg.style.justifyContent = 'center';
    watermarkImg.style.transform = 'rotate(-45deg)';

    // Add the watermark to the document
    document.body.appendChild(watermarkImg);

    // Print the document
    window.print();

    // Remove the watermark after printing
    setTimeout(() => {
      document.body.removeChild(watermarkImg);
    }, 1000);
  };

  return (
    <div className="senior-dashboard" data-print-date={currentDate}>
      {/* Modern, Clean, Minimalist Dashboard Header */}
      <div className="dashboard-header modern-header">
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 3
        }}>
          <Box sx={{ display: 'flex', flexDirection: 'column' }}>
            <Typography
              variant="h3"
              className="dashboard-title modern-title"
              sx={{
                fontWeight: 300,
                letterSpacing: '-0.5px',
                color: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.9)' : 'rgba(0, 0, 0, 0.87)'
              }}
            >
              Your Health Dashboard
            </Typography>

            <Typography
              variant="h6"
              className="dashboard-welcome"
              sx={{
                fontWeight: 400,
                mt: 1,
                color: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
                fontSize: '1.25rem'
              }}
            >
              Welcome back, {patientInfo.first_name}
            </Typography>
          </Box>

          {/* Print Button */}
          <Tooltip title="Print dashboard">
            <IconButton
              onClick={handlePrintWithWatermark}
              aria-label="Print dashboard"
              sx={{
                color: 'primary.main',
                width: '48px',
                height: '48px',
                '&:hover': {
                  bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.08)' : 'rgba(0, 0, 0, 0.04)',
                }
              }}
            >
              <PrintIcon fontSize="medium" />
            </IconButton>
          </Tooltip>
        </Box>

        {/* Data Source Information */}
        <Box
          sx={{
            py: 2.5,
            px: 3,
            borderRadius: '12px',
            bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.04)' : 'rgba(0, 0, 0, 0.02)',
            mb: 4,
            display: 'flex',
            flexDirection: { xs: 'column', sm: 'row' },
            alignItems: { xs: 'flex-start', sm: 'center' },
            justifyContent: 'space-between',
            gap: 2
          }}
        >
          <Typography
            variant="body1"
            sx={{
              fontSize: '1.125rem',
              color: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.7)' : 'rgba(0, 0, 0, 0.6)',
              display: 'flex',
              alignItems: 'center',
              gap: 1
            }}
          >
            <InfoIcon fontSize="small" color="primary" />
            {dashboardData.dataSource === 'visit'
              ? "Here's your health information from your most recent visit."
              : "Here's your health information from your patient record."}
          </Typography>

          <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
            {recentVisits.length > 0 ? (
              <Chip
                icon={<CalendarTodayIcon />}
                label={`Last visit: ${new Date(recentVisits[0].visit_date).toLocaleDateString()}`}
                color="primary"
                variant="outlined"
                sx={{
                  height: '36px',
                  borderRadius: '18px',
                  '& .MuiChip-label': { px: 1.5, fontSize: '0.9375rem' }
                }}
              />
            ) : (
              <Chip
                icon={<InfoIcon />}
                label="No visits recorded yet"
                color="secondary"
                variant="outlined"
                sx={{
                  height: '36px',
                  borderRadius: '18px',
                  '& .MuiChip-label': { px: 1.5, fontSize: '0.9375rem' }
                }}
              />
            )}

            {/* Data source indicator */}
            <Chip
              icon={dashboardData.dataSource === 'visit' ? <EventNoteIcon /> : <PersonIcon />}
              label={dashboardData.dataSource === 'visit'
                ? "Data from most recent visit"
                : "Data from patient record"}
              color={dashboardData.dataSource === 'visit' ? "success" : "info"}
              variant="outlined"
              sx={{
                height: '36px',
                borderRadius: '18px',
                '& .MuiChip-label': { px: 1.5, fontSize: '0.9375rem' }
              }}
            />
          </Box>
        </Box>
      </div>

      {/* Senior-friendly Tab Navigation */}
      <div className="senior-tabs">
        <button
          className={`senior-tab ${activeTab === 0 ? 'active' : ''}`}
          onClick={() => setActiveTab(0)}
          aria-label="View health overview"
        >
          <PersonIcon className="tab-icon" />
          My Health Summary
        </button>
        <button
          className={`senior-tab ${activeTab === 1 ? 'active' : ''}`}
          onClick={() => setActiveTab(1)}
          aria-label="View health charts"
        >
          <MonitorHeartIcon className="tab-icon" />
          Health Trends
        </button>
        <button
          className={`senior-tab ${activeTab === 2 ? 'active' : ''}`}
          onClick={() => setActiveTab(2)}
          aria-label="View medical history"
        >
          <EventNoteIcon className="tab-icon" />
          Visit History
        </button>
        <button
          className={`senior-tab ${activeTab === 3 ? 'active' : ''}`}
          onClick={() => setActiveTab(3)}
          aria-label="View prescriptions"
        >
          <MedicationIcon className="tab-icon" />
          My Prescriptions
        </button>
        <button
          className={`senior-tab ${activeTab === 4 ? 'active' : ''}`}
          onClick={() => setActiveTab(4)}
          aria-label="Contact your doctor"
        >
          <LocalHospitalIcon className="tab-icon" />
          Contact Doctor
        </button>
      </div>

      {/* Overview Tab Content */}
      <Box sx={{ display: activeTab !== 0 ? 'none' : 'block' }}>
        {/* Latest Visit Summary */}
        {recentVisits.length > 0 ? (
          <div className="dashboard-section">
            <SectionTitle>
              <EventNoteIcon /> Your Most Recent Visit
            </SectionTitle>

            <Paper elevation={0} sx={{
              p: 3,
              bgcolor: 'background.default',
              borderRadius: '16px',
              border: '1px solid var(--border-color)',
              mb: 3
            }}>
              <Grid container spacing={3}>
                {/* Visit Header with Date and Doctor */}
                <Grid item xs={12}>
                  <Box sx={{
                    display: 'flex',
                    flexDirection: { xs: 'column', md: 'row' },
                    justifyContent: 'space-between',
                    alignItems: { xs: 'flex-start', md: 'center' },
                    mb: 2,
                    pb: 2,
                    borderBottom: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)'}`
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: { xs: 2, md: 0 } }}>
                      <MetricIconContainer>
                        <CalendarTodayIcon fontSize="large" />
                      </MetricIconContainer>
                      <Box sx={{ ml: 2 }}>
                        <Typography variant="h6" sx={{ fontSize: '1.375rem', fontWeight: 600, color: 'primary.main' }}>
                          Visit Date
                        </Typography>
                        <Typography variant="h5" sx={{ fontSize: '1.5rem', fontWeight: 600 }}>
                          {new Date(recentVisits[0].visit_date).toLocaleDateString(undefined, {
                            weekday: 'long',
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          })}
                        </Typography>
                      </Box>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <MetricIconContainer bgcolor="secondary.light">
                        <LocalHospitalIcon fontSize="large" />
                      </MetricIconContainer>
                      <Box sx={{ ml: 2 }}>
                        <Typography variant="h6" sx={{ fontSize: '1.375rem', fontWeight: 600, color: 'secondary.main' }}>
                          Your Doctor
                        </Typography>
                        <Typography variant="h5" sx={{ fontSize: '1.5rem', fontWeight: 600 }}>
                          Dr. {recentVisits[0].doctor_first_name} {recentVisits[0].doctor_last_name}
                        </Typography>
                      </Box>
                    </Box>
                  </Box>
                </Grid>

                {/* Reason for Visit */}
                <Grid item xs={12} md={6}>
                  <Box sx={{
                    p: 2.5,
                    borderRadius: '12px',
                    bgcolor: theme.palette.background.paper,
                    border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.08)'}`,
                    height: '100%'
                  }}>
                    <Typography variant="h6" sx={{
                      fontSize: '1.375rem',
                      fontWeight: 600,
                      mb: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}>
                      <NoteAltIcon color="primary" /> Reason for Visit
                    </Typography>
                    <Typography variant="body1" sx={{ fontSize: '1.25rem', lineHeight: 1.5 }}>
                      {recentVisits[0].visit_reason || 'Routine checkup'}
                    </Typography>
                  </Box>
                </Grid>

                {/* Diagnosis */}
                <Grid item xs={12} md={6}>
                  <Box sx={{
                    p: 2.5,
                    borderRadius: '12px',
                    bgcolor: theme.palette.background.paper,
                    border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.08)'}`,
                    height: '100%'
                  }}>
                    <Typography variant="h6" sx={{
                      fontSize: '1.375rem',
                      fontWeight: 600,
                      mb: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}>
                      <LocalHospitalIcon color="primary" /> Diagnosis
                    </Typography>
                    <Typography variant="body1" sx={{ fontSize: '1.25rem', lineHeight: 1.5 }}>
                      {recentVisits[0].diagnosis || 'No specific diagnosis was recorded for this visit.'}
                    </Typography>
                  </Box>
                </Grid>

                {/* Doctor's Notes */}
                {recentVisits[0].notes && (
                  <Grid item xs={12}>
                    <Box sx={{
                      p: 2.5,
                      borderRadius: '12px',
                      bgcolor: theme.palette.background.paper,
                      border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.08)'}`
                    }}>
                      <Typography variant="h6" sx={{
                        fontSize: '1.375rem',
                        fontWeight: 600,
                        mb: 2,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 1
                      }}>
                        <EventNoteIcon color="primary" /> Doctor's Notes
                      </Typography>
                      <Typography variant="body1" sx={{ fontSize: '1.25rem', lineHeight: 1.6 }}>
                        {recentVisits[0].notes}
                      </Typography>
                    </Box>
                  </Grid>
                )}

                {/* Quick Summary of Vital Signs */}
                <Grid item xs={12}>
                  <Box sx={{
                    mt: 1,
                    p: 2.5,
                    borderRadius: '12px',
                    bgcolor: theme.palette.mode === 'dark' ? 'rgba(2, 132, 199, 0.15)' : 'rgba(2, 132, 199, 0.08)',
                    border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(2, 132, 199, 0.3)' : 'rgba(2, 132, 199, 0.2)'}`
                  }}>
                    <Typography variant="h6" sx={{
                      fontSize: '1.375rem',
                      fontWeight: 600,
                      mb: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                      color: 'primary.main'
                    }}>
                      <MonitorHeartIcon /> Health Measurements from this Visit
                    </Typography>

                    <Grid container spacing={3}>
                      <Grid item xs={12} sm={6} md={3}>
                        <Box sx={{ textAlign: 'center' }}>
                          <FavoriteIcon sx={{ fontSize: '2rem', color: 'error.main', mb: 1 }} />
                          <Typography variant="body1" sx={{ fontSize: '1.125rem', color: 'text.secondary', mb: 0.5 }}>
                            Heart Rate
                          </Typography>
                          <Typography variant="h6" sx={{ fontSize: '1.5rem', fontWeight: 600 }}>
                            {recentVisits[0].vital_signs?.heart_rate || 'N/A'}
                            {recentVisits[0].vital_signs?.heart_rate && <span style={{ fontSize: '1.125rem' }}> bpm</span>}
                          </Typography>
                        </Box>
                      </Grid>

                      <Grid item xs={12} sm={6} md={3}>
                        <Box sx={{ textAlign: 'center' }}>
                          <MonitorHeartIcon sx={{ fontSize: '2rem', color: 'primary.main', mb: 1 }} />
                          <Typography variant="body1" sx={{ fontSize: '1.125rem', color: 'text.secondary', mb: 0.5 }}>
                            Blood Pressure
                          </Typography>
                          <Typography variant="h6" sx={{ fontSize: '1.5rem', fontWeight: 600 }}>
                            {recentVisits[0].vital_signs?.blood_pressure || 'N/A'}
                            {recentVisits[0].vital_signs?.blood_pressure && <span style={{ fontSize: '1.125rem' }}> mmHg</span>}
                          </Typography>
                        </Box>
                      </Grid>

                      <Grid item xs={12} sm={6} md={3}>
                        <Box sx={{ textAlign: 'center' }}>
                          <ThermostatIcon sx={{ fontSize: '2rem', color: 'warning.main', mb: 1 }} />
                          <Typography variant="body1" sx={{ fontSize: '1.125rem', color: 'text.secondary', mb: 0.5 }}>
                            Temperature
                          </Typography>
                          <Typography variant="h6" sx={{ fontSize: '1.5rem', fontWeight: 600 }}>
                            {recentVisits[0].vital_signs?.temperature || 'N/A'}
                            {recentVisits[0].vital_signs?.temperature && <span style={{ fontSize: '1.125rem' }}> °C</span>}
                          </Typography>
                        </Box>
                      </Grid>

                      <Grid item xs={12} sm={6} md={3}>
                        <Box sx={{ textAlign: 'center' }}>
                          <AirIcon sx={{ fontSize: '2rem', color: 'info.main', mb: 1 }} />
                          <Typography variant="body1" sx={{ fontSize: '1.125rem', color: 'text.secondary', mb: 0.5 }}>
                            Oxygen Level
                          </Typography>
                          <Typography variant="h6" sx={{ fontSize: '1.5rem', fontWeight: 600 }}>
                            {recentVisits[0].vital_signs?.oxygen_level || 'N/A'}
                            {recentVisits[0].vital_signs?.oxygen_level && <span style={{ fontSize: '1.125rem' }}> %</span>}
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>

                    <Box sx={{ mt: 2, textAlign: 'center' }}>
                      <Button
                        variant="outlined"
                        color="primary"
                        size="large"
                        onClick={() => document.getElementById('health-measurements-section')?.scrollIntoView({ behavior: 'smooth' })}
                        sx={{
                          fontSize: '1.125rem',
                          mt: 1,
                          borderRadius: '8px',
                          padding: '8px 24px'
                        }}
                      >
                        View Detailed Health Information
                      </Button>
                    </Box>
                  </Box>
                </Grid>

                {/* Next Steps or Recommendations */}
                <Grid item xs={12}>
                  <Box sx={{
                    mt: 1,
                    p: 2.5,
                    borderRadius: '12px',
                    bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 193, 7, 0.15)' : '#fff9e6',
                    border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 193, 7, 0.3)' : '#ffeeba'}`
                  }}>
                    <Typography variant="h6" sx={{
                      fontSize: '1.375rem',
                      fontWeight: 600,
                      mb: 2,
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1,
                      color: theme.palette.mode === 'dark' ? '#ffc107' : '#b45309'
                    }}>
                      <InfoIcon /> Important Reminders
                    </Typography>

                    <Typography variant="body1" sx={{ fontSize: '1.25rem', mb: 1.5, lineHeight: 1.5 }}>
                      • Remember to take all medications as prescribed by your doctor
                    </Typography>
                    <Typography variant="body1" sx={{ fontSize: '1.25rem', mb: 1.5, lineHeight: 1.5 }}>
                      • Follow any specific instructions provided during your visit
                    </Typography>
                    <Typography variant="body1" sx={{ fontSize: '1.25rem', lineHeight: 1.5 }}>
                      • Contact your doctor if you experience any new or worsening symptoms
                    </Typography>

                    <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
                      <Button
                        variant="contained"
                        color="primary"
                        size="large"
                        onClick={() => setActiveTab(4)}
                        sx={{
                          fontSize: '1.125rem',
                          mt: 1,
                          borderRadius: '8px',
                          padding: '10px 24px'
                        }}
                      >
                        Contact Your Doctor
                      </Button>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </Paper>
          </div>
        ) : (
          <Alert severity="info" sx={{ fontSize: '1.25rem', padding: '24px', mb: 3 }}>
            <Typography variant="h6" sx={{ mb: 1, fontSize: '1.375rem', fontWeight: 600 }}>
              No Visit History
            </Typography>
            <Typography variant="body1" sx={{ fontSize: '1.25rem' }}>
              You don't have any recorded medical visits yet. After your first appointment, your visit information will appear here.
            </Typography>
          </Alert>
        )}

        {/* Health Metrics Cards */}
        <div className="dashboard-section" id="health-measurements-section">
          <SectionTitle>
            <MonitorHeartIcon /> Your Health Measurements
          </SectionTitle>

          <Box sx={{ mb: 4 }}>
            <Typography variant="body1" sx={{ fontSize: '1.25rem', mb: 2 }}>
              {dashboardData.dataSource === 'visit'
                ? `These are your detailed health measurements from your most recent visit on ${recentVisits.length > 0 ? new Date(recentVisits[0].visit_date).toLocaleDateString() : 'N/A'}.`
                : `These are your detailed health measurements from your patient record.`
              }

              {/* Data source indicator */}
              <Chip
                icon={dashboardData.dataSource === 'visit' ? <EventNoteIcon /> : <PersonIcon />}
                label={dashboardData.dataSource === 'visit'
                  ? "Data from most recent visit"
                  : "Data from patient record"}
                color={dashboardData.dataSource === 'visit' ? "success" : "info"}
                variant="outlined"
                size="small"
                sx={{ ml: 2, verticalAlign: 'middle' }}
              />
            </Typography>
            <Typography variant="body1" sx={{ fontSize: '1.25rem', mb: 2 }}>
              Understanding your health measurements helps you and your doctor monitor your health over time. The colored indicators show whether each measurement is within the normal range.
            </Typography>
            <Box sx={{
              display: 'flex',
              flexWrap: 'wrap',
              gap: 2,
              mt: 2,
              p: 2,
              bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.05)' : 'rgba(0, 0, 0, 0.02)',
              borderRadius: '8px',
              border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.08)'}`
            }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Box className="status-indicator status-normal" sx={{ py: 0.5, px: 1.5 }}>
                  <CheckCircleIcon />
                  Normal
                </Box>
                <Typography variant="body2" sx={{ fontSize: '1.125rem' }}>
                  Within healthy range
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Box className="status-indicator status-warning" sx={{ py: 0.5, px: 1.5 }}>
                  <InfoIcon />
                  Borderline
                </Box>
                <Typography variant="body2" sx={{ fontSize: '1.125rem' }}>
                  Slightly outside normal range
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Box className="status-indicator status-abnormal" sx={{ py: 0.5, px: 1.5 }}>
                  <WarningIcon />
                  Outside Range
                </Box>
                <Typography variant="body2" sx={{ fontSize: '1.125rem' }}>
                  Needs attention
                </Typography>
              </Box>
            </Box>
          </Box>

          {latestVitals ? (
            <Grid container spacing={3}>
              {/* Blood Pressure Card */}
              <Grid item xs={12} md={6} lg={4}>
                <MetricCard>
                  <MetricHeader>
                    <MetricIconContainer bgcolor="primary.light">
                      <MonitorHeartIcon fontSize="large" />
                    </MetricIconContainer>
                    <Box>
                      <MetricLabel variant="h6">Blood Pressure</MetricLabel>
                    </Box>
                  </MetricHeader>

                  <MetricValue variant="h4">
                    {latestVitals.sitting_bp_systolic && latestVitals.sitting_bp_diastolic
                      ? `${latestVitals.sitting_bp_systolic}/${latestVitals.sitting_bp_diastolic}`
                      : latestVitals.blood_pressure}
                    <MetricUnit> mmHg</MetricUnit>
                    <Typography variant="caption" sx={{ display: 'block', mt: 0.5, color: 'text.secondary', fontSize: '0.875rem' }}>
                      (Sitting position)
                    </Typography>
                  </MetricValue>

                  {/* Detailed BP Measurements */}
                  <Box sx={{ mt: 2, mb: 2 }}>
                    <Typography variant="body2" sx={{ fontSize: '1.125rem', color: 'text.secondary', mb: 1 }}>
                      Detailed measurements:
                    </Typography>

                    <Box sx={{
                      display: 'flex',
                      flexDirection: 'column',
                      gap: 1,
                      p: 1.5,
                      bgcolor: theme.palette.mode === 'dark' ? 'rgba(2, 132, 199, 0.15)' : 'rgba(2, 132, 199, 0.08)',
                      borderRadius: '8px',
                      border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(2, 132, 199, 0.3)' : 'rgba(2, 132, 199, 0.2)'}`
                    }}>
                      {latestVitals.lying_bp_systolic && latestVitals.lying_bp_diastolic && (
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" sx={{ fontSize: '1rem' }}>
                            <strong>Lying:</strong>
                          </Typography>
                          <Typography variant="body2" sx={{ fontSize: '1rem', fontWeight: 500 }}>
                            {latestVitals.lying_bp_systolic}/{latestVitals.lying_bp_diastolic} mmHg
                          </Typography>
                        </Box>
                      )}

                      {latestVitals.sitting_bp_systolic && latestVitals.sitting_bp_diastolic && (
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" sx={{ fontSize: '1rem' }}>
                            <strong>Sitting:</strong>
                          </Typography>
                          <Typography variant="body2" sx={{ fontSize: '1rem', fontWeight: 500 }}>
                            {latestVitals.sitting_bp_systolic}/{latestVitals.sitting_bp_diastolic} mmHg
                          </Typography>
                        </Box>
                      )}

                      {latestVitals.standing_bp_systolic && latestVitals.standing_bp_diastolic && (
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" sx={{ fontSize: '1rem' }}>
                            <strong>Standing:</strong>
                          </Typography>
                          <Typography variant="body2" sx={{ fontSize: '1rem', fontWeight: 500 }}>
                            {latestVitals.standing_bp_systolic}/{latestVitals.standing_bp_diastolic} mmHg
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </Box>

                  <Box sx={{ mt: 1, mb: 2 }}>
                    <Typography variant="body2" sx={{ fontSize: '1.125rem', color: 'text.secondary' }}>
                      Your reading has two numbers:
                    </Typography>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mt: 1 }}>
                      <Box sx={{
                        flex: '1 1 auto',
                        minWidth: '120px',
                        p: 1.5,
                        bgcolor: theme.palette.mode === 'dark' ? 'rgba(2, 132, 199, 0.15)' : 'rgba(2, 132, 199, 0.08)',
                        borderRadius: '8px',
                        border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(2, 132, 199, 0.3)' : 'rgba(2, 132, 199, 0.2)'}`
                      }}>
                        <Typography variant="body2" sx={{ fontSize: '1rem', fontWeight: 600, color: 'primary.main' }}>
                          Top Number (Systolic)
                        </Typography>
                        <Typography variant="body2" sx={{ fontSize: '1rem', mt: 0.5 }}>
                          Pressure when heart beats
                        </Typography>
                        <Typography variant="body2" sx={{ fontSize: '0.875rem', mt: 0.5, color: 'text.secondary' }}>
                          Normal range: 90-140 mmHg
                        </Typography>
                      </Box>
                      <Box sx={{
                        flex: '1 1 auto',
                        minWidth: '120px',
                        p: 1.5,
                        bgcolor: theme.palette.mode === 'dark' ? 'rgba(2, 132, 199, 0.15)' : 'rgba(2, 132, 199, 0.08)',
                        borderRadius: '8px',
                        border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(2, 132, 199, 0.3)' : 'rgba(2, 132, 199, 0.2)'}`
                      }}>
                        <Typography variant="body2" sx={{ fontSize: '1rem', fontWeight: 600, color: 'primary.main' }}>
                          Bottom Number (Diastolic)
                        </Typography>
                        <Typography variant="body2" sx={{ fontSize: '1rem', mt: 0.5 }}>
                          Pressure when heart rests
                        </Typography>
                        <Typography variant="body2" sx={{ fontSize: '0.875rem', mt: 0.5, color: 'text.secondary' }}>
                          Normal range: 60-90 mmHg
                        </Typography>
                      </Box>
                    </Box>
                  </Box>

                  <MetricExplanation>
                    Blood pressure measures the force of blood against your artery walls. High blood pressure can lead to heart disease and stroke.
                  </MetricExplanation>

                  {latestVitals.lying_bp_systolic && latestVitals.lying_bp_diastolic && (
                    <Box sx={{ mt: 2 }}>
                      {(() => {
                        const systolicStatus = getValueStatus(latestVitals.lying_bp_systolic, 90, 140);
                        const diastolicStatus = getValueStatus(latestVitals.lying_bp_diastolic, 60, 90);
                        const worstStatus = systolicStatus === 'abnormal' || diastolicStatus === 'abnormal'
                          ? 'abnormal'
                          : systolicStatus === 'warning' || diastolicStatus === 'warning'
                            ? 'warning'
                            : 'normal';

                        const statusIcon = worstStatus === 'abnormal'
                          ? <WarningIcon />
                          : worstStatus === 'warning'
                            ? <InfoIcon />
                            : <CheckCircleIcon />;

                        const statusText = worstStatus === 'abnormal'
                          ? 'Outside normal range'
                          : worstStatus === 'warning'
                            ? 'Borderline'
                            : 'Normal';

                        // Add specific advice based on status
                        let advice = '';
                        if (worstStatus === 'abnormal') {
                          advice = 'Talk to your doctor about ways to manage your blood pressure.';
                        } else if (worstStatus === 'warning') {
                          advice = 'Monitor your blood pressure regularly and discuss with your doctor.';
                        } else {
                          advice = 'Continue maintaining your healthy blood pressure with good habits.';
                        }

                        return (
                          <>
                            <HelpTooltip
                              title={
                                <React.Fragment>
                                  <Typography variant="subtitle2">Normal Blood Pressure Range:</Typography>
                                  <Typography variant="body2">Systolic: 90-140 mmHg</Typography>
                                  <Typography variant="body2">Diastolic: 60-90 mmHg</Typography>
                                </React.Fragment>
                              }
                              placement="top"
                              arrow
                            >
                              <Box className={`status-indicator status-${worstStatus}`}>
                                {statusIcon}
                                {statusText}
                              </Box>
                            </HelpTooltip>

                            <Typography variant="body2" sx={{ fontSize: '1rem', mt: 1.5, fontStyle: 'italic' }}>
                              {advice}
                            </Typography>
                          </>
                        );
                      })()}
                    </Box>
                  )}
                </MetricCard>
              </Grid>

              {/* Heart Rate Card */}
              <Grid item xs={12} md={6} lg={4}>
                <MetricCard>
                  <MetricHeader>
                    <MetricIconContainer bgcolor="error.light">
                      <FavoriteIcon fontSize="large" />
                    </MetricIconContainer>
                    <Box>
                      <MetricLabel variant="h6">Heart Rate</MetricLabel>
                    </Box>
                  </MetricHeader>

                  <MetricValue variant="h4">
                    {latestVitals.sitting_heart_rate
                      ? latestVitals.sitting_heart_rate
                      : latestVitals.heart_rate}
                    <MetricUnit> bpm</MetricUnit>
                    <Typography variant="caption" sx={{ display: 'block', mt: 0.5, color: 'text.secondary', fontSize: '0.875rem' }}>
                      (Sitting position)
                    </Typography>
                  </MetricValue>

                  <Box sx={{ mt: 1, mb: 2 }}>
                    <Typography variant="body2" sx={{ fontSize: '1.125rem', color: 'text.secondary' }}>
                      Detailed measurements:
                    </Typography>
                    <Box sx={{
                      mt: 1,
                      p: 1.5,
                      bgcolor: 'rgba(239, 68, 68, 0.08)',
                      borderRadius: '8px',
                      border: '1px solid rgba(239, 68, 68, 0.2)'
                    }}>
                      {latestVitals.lying_heart_rate && (
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                          <Typography variant="body2" sx={{ fontSize: '1rem' }}>
                            <strong>Lying:</strong>
                          </Typography>
                          <Typography variant="body2" sx={{ fontSize: '1rem', fontWeight: 500 }}>
                            {latestVitals.lying_heart_rate} bpm
                          </Typography>
                        </Box>
                      )}

                      {latestVitals.sitting_heart_rate && (
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                          <Typography variant="body2" sx={{ fontSize: '1rem' }}>
                            <strong>Sitting:</strong>
                          </Typography>
                          <Typography variant="body2" sx={{ fontSize: '1rem', fontWeight: 500 }}>
                            {latestVitals.sitting_heart_rate} bpm
                          </Typography>
                        </Box>
                      )}

                      {latestVitals.standing_heart_rate && (
                        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                          <Typography variant="body2" sx={{ fontSize: '1rem' }}>
                            <strong>Standing:</strong>
                          </Typography>
                          <Typography variant="body2" sx={{ fontSize: '1rem', fontWeight: 500 }}>
                            {latestVitals.standing_heart_rate} bpm
                          </Typography>
                        </Box>
                      )}

                      <Divider sx={{ my: 1 }} />

                      <Typography variant="body2" sx={{ fontSize: '1rem' }}>
                        <strong>bpm</strong> = beats per minute
                      </Typography>
                      <Box sx={{
                        mt: 1,
                        p: 1,
                        bgcolor: 'rgba(239, 68, 68, 0.05)',
                        borderRadius: '4px',
                        border: '1px solid rgba(239, 68, 68, 0.1)'
                      }}>
                        <Typography variant="body2" sx={{ fontSize: '1rem', fontWeight: 500 }}>
                          Normal adult range: 60-100 bpm
                        </Typography>
                        <Typography variant="body2" sx={{ fontSize: '0.875rem', mt: 0.5, color: 'text.secondary' }}>
                          Values outside this range may require attention
                        </Typography>
                      </Box>
                    </Box>
                  </Box>

                  <MetricExplanation>
                    Heart rate is the number of times your heart beats per minute. It can be affected by activity, emotions, medications, and health conditions.
                  </MetricExplanation>

                  <Box sx={{ mt: 2 }}>
                    {(() => {
                      const status = getValueStatus(latestVitals.heart_rate, 60, 100);
                      const statusIcon = status === 'abnormal'
                        ? <WarningIcon />
                        : status === 'warning'
                          ? <InfoIcon />
                          : <CheckCircleIcon />;

                      const statusText = status === 'abnormal'
                        ? 'Outside normal range'
                        : status === 'warning'
                          ? 'Borderline'
                          : 'Normal';

                      // Add specific advice based on status
                      let advice = '';
                      const heartRate = latestVitals.heart_rate ? Number(latestVitals.heart_rate) : null;
                      if (status === 'abnormal' && heartRate && heartRate > 100) {
                        advice = 'A high heart rate may indicate stress, anxiety, or other conditions. Discuss with your doctor.';
                      } else if (status === 'abnormal' && heartRate && heartRate < 60) {
                        advice = 'A low heart rate may be normal for some people, especially those who exercise regularly. Discuss with your doctor if you feel unwell.';
                      } else if (status === 'warning') {
                        advice = 'Your heart rate is slightly outside the normal range. Monitor how you feel and discuss with your doctor.';
                      } else {
                        advice = 'Your heart rate is within the normal range.';
                      }

                      return (
                        <>
                          <HelpTooltip
                            title={
                              <React.Fragment>
                                <Typography variant="subtitle2">Normal Heart Rate Range:</Typography>
                                <Typography variant="body2">60-100 beats per minute</Typography>
                                <Typography variant="body2" sx={{ mt: 1 }}>Athletes and very active people may have lower heart rates.</Typography>
                              </React.Fragment>
                            }
                            placement="top"
                            arrow
                          >
                            <Box className={`status-indicator status-${status}`}>
                              {statusIcon}
                              {statusText}
                            </Box>
                          </HelpTooltip>

                          <Typography variant="body2" sx={{ fontSize: '1rem', mt: 1.5, fontStyle: 'italic' }}>
                            {advice}
                          </Typography>
                        </>
                      );
                    })()}
                  </Box>
                </MetricCard>
              </Grid>

              {/* Temperature Card */}
              <Grid item xs={12} md={6} lg={4}>
                <MetricCard>
                  <MetricHeader>
                    <MetricIconContainer bgcolor="warning.light">
                      <ThermostatIcon fontSize="large" />
                    </MetricIconContainer>
                    <Box>
                      <MetricLabel variant="h6">Temperature</MetricLabel>
                    </Box>
                  </MetricHeader>

                  <MetricValue variant="h4">
                    {latestVitals.temperature}
                    <MetricUnit> °C</MetricUnit>
                  </MetricValue>

                  <Box sx={{ mt: 1, mb: 2 }}>
                    <Typography variant="body2" sx={{ fontSize: '1.125rem', color: 'text.secondary' }}>
                      Temperature ranges:
                    </Typography>
                    <Box sx={{
                      mt: 1,
                      p: 1.5,
                      bgcolor: 'rgba(245, 158, 11, 0.08)',
                      borderRadius: '8px',
                      border: '1px solid rgba(245, 158, 11, 0.2)'
                    }}>
                      <Typography variant="body2" sx={{ fontSize: '1rem', display: 'flex', justifyContent: 'space-between' }}>
                        <span>Normal:</span> <strong>36.1-37.2°C</strong>
                      </Typography>
                      <Typography variant="body2" sx={{ fontSize: '1rem', mt: 0.5, display: 'flex', justifyContent: 'space-between' }}>
                        <span>Fever:</span> <strong>Above 38°C</strong>
                      </Typography>
                      <Typography variant="body2" sx={{ fontSize: '1rem', mt: 0.5, display: 'flex', justifyContent: 'space-between' }}>
                        <span>Low:</span> <strong>Below 35°C</strong>
                      </Typography>
                    </Box>
                  </Box>

                  <MetricExplanation>
                    Body temperature is a measure of your body's ability to generate and get rid of heat. Fever can indicate infection or illness.
                  </MetricExplanation>

                  <Box sx={{ mt: 2 }}>
                    {(() => {
                      const status = getValueStatus(latestVitals.temperature, 36.1, 37.2);
                      const statusIcon = status === 'abnormal'
                        ? <WarningIcon />
                        : status === 'warning'
                          ? <InfoIcon />
                          : <CheckCircleIcon />;

                      const statusText = status === 'abnormal'
                        ? 'Outside normal range'
                        : status === 'warning'
                          ? 'Borderline'
                          : 'Normal';

                      // Add specific advice based on status
                      let advice = '';
                      const temperature = latestVitals.temperature ? Number(latestVitals.temperature) : null;
                      if (status === 'abnormal' && temperature && temperature > 37.2) {
                        advice = 'An elevated temperature may indicate infection or illness. Contact your doctor if it persists or is accompanied by other symptoms.';
                      } else if (status === 'abnormal' && temperature && temperature < 36.1) {
                        advice = 'A low body temperature may indicate exposure to cold or certain medical conditions. Discuss with your doctor.';
                      } else if (status === 'warning') {
                        advice = 'Your temperature is slightly outside the normal range. Monitor for changes and other symptoms.';
                      } else {
                        advice = 'Your temperature is within the normal range.';
                      }

                      return (
                        <>
                          <HelpTooltip
                            title={
                              <React.Fragment>
                                <Typography variant="subtitle2">About Body Temperature:</Typography>
                                <Typography variant="body2">Normal adult range: 36.1-37.2°C</Typography>
                                <Typography variant="body2" sx={{ mt: 1 }}>Temperature can vary throughout the day and may be affected by activity and time of day.</Typography>
                              </React.Fragment>
                            }
                            placement="top"
                            arrow
                          >
                            <Box className={`status-indicator status-${status}`}>
                              {statusIcon}
                              {statusText}
                            </Box>
                          </HelpTooltip>

                          <Typography variant="body2" sx={{ fontSize: '1rem', mt: 1.5, fontStyle: 'italic' }}>
                            {advice}
                          </Typography>
                        </>
                      );
                    })()}
                  </Box>
                </MetricCard>
              </Grid>

              {/* Body Measurements Card */}
              <Grid item xs={12} md={6} lg={4}>
                <MetricCard>
                  <MetricHeader>
                    <MetricIconContainer bgcolor="success.light">
                      <ScaleIcon fontSize="large" />
                    </MetricIconContainer>
                    <Box>
                      <MetricLabel variant="h6">Body Measurements</MetricLabel>
                    </Box>
                  </MetricHeader>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="h6" sx={{ fontSize: '1.25rem', fontWeight: 600 }}>
                      Weight:
                    </Typography>
                    <Typography variant="h6" sx={{ fontSize: '1.5rem', fontWeight: 700 }}>
                      {latestVitals.weight}
                      <span style={{ fontSize: '1.125rem', fontWeight: 500, color: theme.palette.text.secondary }}> lbs</span>
                    </Typography>
                  </Box>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                    <Typography variant="h6" sx={{ fontSize: '1.25rem', fontWeight: 600 }}>
                      Height:
                    </Typography>
                    <Typography variant="h6" sx={{ fontSize: '1.5rem', fontWeight: 700 }}>
                      {latestVitals.height}
                      <span style={{ fontSize: '1.125rem', fontWeight: 500, color: theme.palette.text.secondary }}> in</span>
                    </Typography>
                  </Box>

                  {latestVitals.bmi && (
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                      <Typography variant="h6" sx={{ fontSize: '1.25rem', fontWeight: 600 }}>
                        BMI:
                      </Typography>
                      <Typography variant="h6" sx={{ fontSize: '1.5rem', fontWeight: 700 }}>
                        {latestVitals.bmi}
                        <HelpTooltip
                          title={
                            <React.Fragment>
                              <Typography variant="subtitle2">BMI Categories:</Typography>
                              <Typography variant="body2">Underweight: Below 18.5</Typography>
                              <Typography variant="body2">Normal weight: 18.5-24.9</Typography>
                              <Typography variant="body2">Overweight: 25-29.9</Typography>
                              <Typography variant="body2">Obesity: 30 or higher</Typography>
                            </React.Fragment>
                          }
                          placement="top"
                          arrow
                        >
                          <InfoIcon sx={{ fontSize: '1rem', ml: 0.5, color: 'info.main', cursor: 'pointer', verticalAlign: 'middle' }} />
                        </HelpTooltip>
                      </Typography>
                    </Box>
                  )}

                  <Divider sx={{ my: 1.5 }} />

                  <Box sx={{ mt: 1, mb: 2 }}>
                    <Typography variant="body2" sx={{ fontSize: '1.125rem', color: 'text.secondary' }}>
                      Weight tracking:
                    </Typography>
                    <Box sx={{
                      mt: 1,
                      p: 1.5,
                      bgcolor: 'rgba(16, 185, 129, 0.08)',
                      borderRadius: '8px',
                      border: '1px solid rgba(16, 185, 129, 0.2)'
                    }}>
                      <Typography variant="body2" sx={{ fontSize: '1rem' }}>
                        <strong>Last measured:</strong> {recentVisits.length > 0 ? new Date(recentVisits[0].visit_date).toLocaleDateString() : 'N/A'}
                      </Typography>
                      {recentVisits.length > 1 && recentVisits[1].vital_signs?.weight && (
                        <Typography variant="body2" sx={{ fontSize: '1rem', mt: 0.5 }}>
                          <strong>Previous weight:</strong> {recentVisits[1].vital_signs.weight} lbs
                          {recentVisits[0].vital_signs?.weight && (
                            <span style={{
                              marginLeft: '8px',
                              color: ((recentVisits[0].vital_signs.weight as number) > (recentVisits[1].vital_signs.weight as number)) ? '#ef4444' :
                                    ((recentVisits[0].vital_signs.weight as number) < (recentVisits[1].vital_signs.weight as number)) ? '#10b981' : '#64748b'
                            }}>
                              ({((recentVisits[0].vital_signs.weight as number) > (recentVisits[1].vital_signs.weight as number)) ? '+' : ''}
                              {((recentVisits[0].vital_signs.weight as number) - (recentVisits[1].vital_signs.weight as number)).toFixed(1)} lbs)
                            </span>
                          )}
                        </Typography>
                      )}
                    </Box>
                  </Box>

                  <MetricExplanation>
                    Monitoring your body measurements helps track your overall health. BMI is one indicator but should be considered alongside other health factors.
                  </MetricExplanation>
                </MetricCard>
              </Grid>

              {/* Oxygen Level Card */}
              {latestVitals.oxygen_level && (
                <Grid item xs={12} md={6} lg={4}>
                  <MetricCard>
                    <MetricHeader>
                      <MetricIconContainer bgcolor="info.light">
                        <AirIcon fontSize="large" />
                      </MetricIconContainer>
                      <Box>
                        <MetricLabel variant="h6">Oxygen Level</MetricLabel>
                      </Box>
                    </MetricHeader>

                    <MetricValue variant="h4">
                      {latestVitals.oxygen_level}
                      <MetricUnit> %</MetricUnit>
                    </MetricValue>

                    <Box sx={{ mt: 1, mb: 2 }}>
                      <Typography variant="body2" sx={{ fontSize: '1.125rem', color: 'text.secondary' }}>
                        Oxygen saturation ranges:
                      </Typography>
                      <Box sx={{
                        mt: 1,
                        p: 1.5,
                        bgcolor: 'rgba(14, 165, 233, 0.08)',
                        borderRadius: '8px',
                        border: '1px solid rgba(14, 165, 233, 0.2)'
                      }}>
                        <Typography variant="body2" sx={{ fontSize: '1rem', display: 'flex', justifyContent: 'space-between' }}>
                          <span>Normal:</span> <strong>95-100%</strong>
                        </Typography>
                        <Typography variant="body2" sx={{ fontSize: '1rem', mt: 0.5, display: 'flex', justifyContent: 'space-between' }}>
                          <span>Concerning:</span> <strong>90-94%</strong>
                        </Typography>
                        <Typography variant="body2" sx={{ fontSize: '1rem', mt: 0.5, display: 'flex', justifyContent: 'space-between' }}>
                          <span>Low (seek care):</span> <strong>Below 90%</strong>
                        </Typography>
                      </Box>
                    </Box>

                    <MetricExplanation>
                      Oxygen saturation measures how much oxygen your blood is carrying. It's measured using a device called a pulse oximeter.
                    </MetricExplanation>

                    <Box sx={{ mt: 2 }}>
                      {(() => {
                        const status = getValueStatus(latestVitals.oxygen_level, 95, 100);
                        const statusIcon = status === 'abnormal'
                          ? <WarningIcon />
                          : status === 'warning'
                            ? <InfoIcon />
                            : <CheckCircleIcon />;

                        const statusText = status === 'abnormal'
                          ? 'Outside normal range'
                          : status === 'warning'
                            ? 'Borderline'
                            : 'Normal';

                        // Add specific advice based on status
                        let advice = '';
                        const oxygenLevel = latestVitals.oxygen_level ? Number(latestVitals.oxygen_level) : null;
                        if (status === 'abnormal' && oxygenLevel && oxygenLevel < 90) {
                          advice = 'Low oxygen levels may indicate a respiratory problem. Contact your doctor immediately.';
                        } else if (status === 'warning' || (status === 'abnormal' && oxygenLevel && oxygenLevel >= 90)) {
                          advice = 'Slightly low oxygen levels should be monitored. Discuss with your doctor, especially if you have breathing difficulties.';
                        } else {
                          advice = 'Your oxygen level is within the normal range.';
                        }

                        return (
                          <>
                            <HelpTooltip
                              title={
                                <React.Fragment>
                                  <Typography variant="subtitle2">About Oxygen Levels:</Typography>
                                  <Typography variant="body2">Normal range: 95-100%</Typography>
                                  <Typography variant="body2" sx={{ mt: 1 }}>Oxygen levels may be lower in people with certain lung conditions or at high altitudes.</Typography>
                                </React.Fragment>
                              }
                              placement="top"
                              arrow
                            >
                              <Box className={`status-indicator status-${status}`}>
                                {statusIcon}
                                {statusText}
                              </Box>
                            </HelpTooltip>

                            <Typography variant="body2" sx={{ fontSize: '1rem', mt: 1.5, fontStyle: 'italic' }}>
                              {advice}
                            </Typography>
                          </>
                        );
                      })()}
                    </Box>
                  </MetricCard>
                </Grid>
              )}

              {/* Orthostatic Assessment Card */}
              {latestVitals.lying_bp_systolic && latestVitals.standing_bp_systolic &&
               latestVitals.lying_bp_diastolic && latestVitals.standing_bp_diastolic &&
               latestVitals.lying_heart_rate && latestVitals.standing_heart_rate && (
                <Grid item xs={12}>
                  <MetricCard>
                    <MetricHeader>
                      <MetricIconContainer bgcolor="secondary.light">
                        <AccessibilityNewIcon fontSize="large" />
                      </MetricIconContainer>
                      <Box>
                        <MetricLabel variant="h6">Orthostatic Assessment</MetricLabel>
                      </Box>
                    </MetricHeader>

                    <Box sx={{ mt: 1, mb: 3 }}>
                      <Typography variant="body1" sx={{ fontSize: '1.25rem', mb: 2 }}>
                        This test measures how your blood pressure and heart rate change when you move from lying down to standing up.
                      </Typography>

                      <Box sx={{
                        p: 2,
                        bgcolor: 'rgba(0, 0, 0, 0.02)',
                        borderRadius: '8px',
                        border: '1px solid rgba(0, 0, 0, 0.08)'
                      }}>
                        <Typography variant="body2" sx={{ fontSize: '1.125rem', fontWeight: 600, mb: 1 }}>
                          Why this matters:
                        </Typography>
                        <Typography variant="body2" sx={{ fontSize: '1.125rem', mb: 1 }}>
                          • Helps identify if you might be at risk for falls or dizziness when standing up
                        </Typography>
                        <Typography variant="body2" sx={{ fontSize: '1.125rem', mb: 1 }}>
                          • Important for monitoring certain medications that can affect blood pressure
                        </Typography>
                        <Typography variant="body2" sx={{ fontSize: '1.125rem' }}>
                          • Can detect orthostatic hypotension, a condition common in older adults
                        </Typography>
                      </Box>
                    </Box>

                    {(() => {
                      const systolicDiff = latestVitals.lying_bp_systolic - latestVitals.standing_bp_systolic;
                      const diastolicDiff = latestVitals.lying_bp_diastolic - latestVitals.standing_bp_diastolic;
                      const heartRateDiff = latestVitals.standing_heart_rate - latestVitals.lying_heart_rate;

                      const hasOrthostaticHypotension = systolicDiff >= 20 || diastolicDiff >= 10;
                      const hasCompensatoryTachycardia = heartRateDiff >= 20;

                      let assessmentText = '';
                      let status: 'normal' | 'warning' | 'abnormal' = 'normal';
                      let explanation = '';
                      let recommendations = '';

                      if (hasOrthostaticHypotension) {
                        assessmentText = 'Orthostatic Hypotension Detected';
                        status = 'abnormal';
                        explanation = 'Your blood pressure drops significantly when you stand up. This can cause dizziness, lightheadedness, or fainting.';
                        recommendations = 'Stand up slowly from sitting or lying down. Stay hydrated and discuss with your doctor about medication adjustments if needed.';
                      } else if (hasCompensatoryTachycardia) {
                        assessmentText = 'Compensatory Tachycardia Present';
                        status = 'warning';
                        explanation = 'Your heart rate increases significantly when you stand up, which may be compensating for blood pressure changes.';
                        recommendations = 'Monitor for symptoms like dizziness when standing. Ensure you stay well-hydrated and discuss with your doctor.';
                      } else {
                        assessmentText = 'No Orthostatic Hypotension';
                        status = 'normal';
                        explanation = 'Your blood pressure remains stable when changing positions from lying to standing.';
                        recommendations = 'Continue your current health practices. No specific action needed for orthostatic blood pressure.';
                      }

                      const statusIcon = status === 'abnormal'
                        ? <WarningIcon />
                        : status === 'warning'
                          ? <InfoIcon />
                          : <CheckCircleIcon />;

                      return (
                        <>
                          <Box sx={{ mt: 1, mb: 2 }}>
                            <Box className={`status-indicator status-${status}`} sx={{ fontSize: '1.25rem', py: 1.5, px: 2 }}>
                              {statusIcon}
                              {assessmentText}
                            </Box>
                          </Box>

                          <MetricExplanation sx={{ mb: 2, fontSize: '1.25rem' }}>
                            {explanation}
                          </MetricExplanation>

                          <Box sx={{
                            p: 2.5,
                            bgcolor: status === 'abnormal' ? 'rgba(239, 68, 68, 0.08)' :
                                    status === 'warning' ? 'rgba(245, 158, 11, 0.08)' :
                                    'rgba(16, 185, 129, 0.08)',
                            borderRadius: '12px',
                            border: `1px solid ${status === 'abnormal' ? 'rgba(239, 68, 68, 0.2)' :
                                              status === 'warning' ? 'rgba(245, 158, 11, 0.2)' :
                                              'rgba(16, 185, 129, 0.2)'}`,
                            mb: 3
                          }}>
                            <Typography variant="h6" sx={{ fontSize: '1.25rem', fontWeight: 600, mb: 1.5, display: 'flex', alignItems: 'center', gap: 1 }}>
                              <InfoIcon /> Recommendations
                            </Typography>
                            <Typography variant="body1" sx={{ fontSize: '1.25rem' }}>
                              {recommendations}
                            </Typography>
                          </Box>

                          <Typography variant="h6" sx={{ fontSize: '1.375rem', fontWeight: 600, mb: 2 }}>
                            Your Measurements
                          </Typography>

                          <Grid container spacing={3} sx={{ mt: 0 }}>
                            <Grid item xs={12} md={6}>
                              <Box sx={{
                                p: 2,
                                bgcolor: 'background.paper',
                                borderRadius: '8px',
                                border: '1px solid var(--border-color)'
                              }}>
                                <Typography variant="body1" sx={{ fontSize: '1.25rem', fontWeight: 600, mb: 1.5, color: 'primary.main' }}>
                                  Blood Pressure Changes
                                </Typography>

                                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                  <Typography variant="body2" sx={{ fontSize: '1.125rem' }}>
                                    Lying down:
                                  </Typography>
                                  <Typography variant="body2" sx={{ fontSize: '1.125rem', fontWeight: 600 }}>
                                    {latestVitals.lying_bp_systolic}/{latestVitals.lying_bp_diastolic} mmHg
                                  </Typography>
                                </Box>

                                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                  <Typography variant="body2" sx={{ fontSize: '1.125rem' }}>
                                    Standing up:
                                  </Typography>
                                  <Typography variant="body2" sx={{ fontSize: '1.125rem', fontWeight: 600 }}>
                                    {latestVitals.standing_bp_systolic}/{latestVitals.standing_bp_diastolic} mmHg
                                  </Typography>
                                </Box>

                                <Divider sx={{ my: 1.5 }} />

                                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                  <Typography variant="body2" sx={{ fontSize: '1.125rem', fontWeight: 500 }}>
                                    Systolic change:
                                  </Typography>
                                  <Typography variant="body2" sx={{
                                    fontSize: '1.125rem',
                                    fontWeight: 600,
                                    color: systolicDiff >= 20 ? '#ef4444' :
                                           systolicDiff >= 10 ? '#f59e0b' :
                                           '#10b981'
                                  }}>
                                    {systolicDiff > 0 ? `-${systolicDiff}` : `+${Math.abs(systolicDiff)}`} mmHg
                                  </Typography>
                                </Box>

                                <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 1 }}>
                                  <Typography variant="body2" sx={{ fontSize: '1.125rem', fontWeight: 500 }}>
                                    Diastolic change:
                                  </Typography>
                                  <Typography variant="body2" sx={{
                                    fontSize: '1.125rem',
                                    fontWeight: 600,
                                    color: diastolicDiff >= 10 ? '#ef4444' :
                                           diastolicDiff >= 5 ? '#f59e0b' :
                                           '#10b981'
                                  }}>
                                    {diastolicDiff > 0 ? `-${diastolicDiff}` : `+${Math.abs(diastolicDiff)}`} mmHg
                                  </Typography>
                                </Box>
                              </Box>
                            </Grid>

                            <Grid item xs={12} md={6}>
                              <Box sx={{
                                p: 2,
                                bgcolor: 'background.paper',
                                borderRadius: '8px',
                                border: '1px solid var(--border-color)',
                                height: '100%'
                              }}>
                                <Typography variant="body1" sx={{ fontSize: '1.25rem', fontWeight: 600, mb: 1.5, color: 'error.main' }}>
                                  Heart Rate Changes
                                </Typography>

                                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                  <Typography variant="body2" sx={{ fontSize: '1.125rem' }}>
                                    Lying down:
                                  </Typography>
                                  <Typography variant="body2" sx={{ fontSize: '1.125rem', fontWeight: 600 }}>
                                    {latestVitals.lying_heart_rate} bpm
                                  </Typography>
                                </Box>

                                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                                  <Typography variant="body2" sx={{ fontSize: '1.125rem' }}>
                                    Standing up:
                                  </Typography>
                                  <Typography variant="body2" sx={{ fontSize: '1.125rem', fontWeight: 600 }}>
                                    {latestVitals.standing_heart_rate} bpm
                                  </Typography>
                                </Box>

                                <Divider sx={{ my: 1.5 }} />

                                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                                  <Typography variant="body2" sx={{ fontSize: '1.125rem', fontWeight: 500 }}>
                                    Heart rate change:
                                  </Typography>
                                  <Typography variant="body2" sx={{
                                    fontSize: '1.125rem',
                                    fontWeight: 600,
                                    color: heartRateDiff >= 30 ? '#ef4444' :
                                           heartRateDiff >= 20 ? '#f59e0b' :
                                           '#10b981'
                                  }}>
                                    +{heartRateDiff} bpm
                                  </Typography>
                                </Box>

                                <Typography variant="body2" sx={{ fontSize: '1rem', mt: 2, fontStyle: 'italic' }}>
                                  Some increase in heart rate when standing is normal. A large increase may be your body's way of compensating for blood pressure changes.
                                </Typography>
                              </Box>
                            </Grid>
                          </Grid>
                        </>
                      );
                    })()}
                  </MetricCard>
                </Grid>
              )}
            </Grid>
          ) : (
            <Alert severity="info" sx={{ fontSize: '1.25rem', padding: '24px' }}>
              <Typography variant="h6" sx={{ mb: 1, fontSize: '1.375rem', fontWeight: 600 }}>
                No Health Measurements Available
              </Typography>
              <Typography variant="body1" sx={{ fontSize: '1.25rem' }}>
                No vital signs have been recorded yet. These will appear after your next medical visit.
              </Typography>
            </Alert>
          )}
        </div>

        {/* Personal Information Card */}
        <div className="dashboard-section">
          <SectionTitle>
            <PersonIcon /> Your Personal Information
          </SectionTitle>

          <Paper elevation={0} sx={{
            p: 3,
            bgcolor: 'background.default',
            borderRadius: '16px',
            border: '1px solid var(--border-color)',
            mb: 3
          }}>
            <Grid container spacing={4}>
              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2.5 }}>
                  <Typography variant="h6" sx={{ fontSize: '1.375rem', fontWeight: 600, color: 'primary.main' }}>
                    Personal Details
                  </Typography>

                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                    <Typography sx={{ fontSize: '1.25rem' }}>
                      <Box component="span" sx={{ fontWeight: 600, minWidth: '140px', display: 'inline-block' }}>Name:</Box>
                      {patientInfo.first_name} {patientInfo.last_name}
                    </Typography>

                    <Typography sx={{ fontSize: '1.25rem' }}>
                      <Box component="span" sx={{ fontWeight: 600, minWidth: '140px', display: 'inline-block' }}>Age:</Box>
                      {calculateAge(patientInfo.date_of_birth)} years
                    </Typography>

                    <Typography sx={{ fontSize: '1.25rem' }}>
                      <Box component="span" sx={{ fontWeight: 600, minWidth: '140px', display: 'inline-block' }}>Date of Birth:</Box>
                      {new Date(patientInfo.date_of_birth).toLocaleDateString(undefined, {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric'
                      })}
                    </Typography>

                    <Typography sx={{ fontSize: '1.25rem' }}>
                      <Box component="span" sx={{ fontWeight: 600, minWidth: '140px', display: 'inline-block' }}>Gender:</Box>
                      {patientInfo.gender}
                    </Typography>
                  </Box>

                  {/* Emergency Contact Information */}
                  <Box sx={{ mt: 2 }}>
                    <Typography variant="h6" sx={{
                      fontSize: '1.375rem',
                      fontWeight: 600,
                      color: 'primary.main',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1.5,
                      mb: 1.5
                    }}>
                      <ContactEmergencyIcon fontSize="large" />
                      Emergency Contact
                    </Typography>

                    {patientInfo.emergency_contact_name ? (
                      <Paper
                        elevation={0}
                        sx={{
                          p: 2.5,
                          borderRadius: '12px',
                          bgcolor: theme.palette.mode === 'dark' ? 'rgba(239, 68, 68, 0.15)' : 'rgba(239, 68, 68, 0.08)',
                          border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(239, 68, 68, 0.3)' : 'rgba(239, 68, 68, 0.2)'}`,
                        }}
                      >
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                          <Typography variant="body1" sx={{ fontSize: '1.25rem', fontWeight: 600 }}>
                            {patientInfo.emergency_contact_name}
                            {patientInfo.emergency_contact_relationship && (
                              <Typography component="span" sx={{ fontSize: '1.125rem', fontWeight: 400, ml: 1, color: 'text.secondary' }}>
                                ({patientInfo.emergency_contact_relationship})
                              </Typography>
                            )}
                          </Typography>

                          {patientInfo.emergency_contact_phone && (
                            <Typography
                              variant="body1"
                              sx={{
                                fontSize: '1.25rem',
                                display: 'flex',
                                alignItems: 'center',
                                gap: 1
                              }}
                            >
                              <ContactPhoneIcon color="error" />
                              {patientInfo.emergency_contact_phone}
                            </Typography>
                          )}

                          <Typography variant="body2" sx={{ fontSize: '1rem', color: 'text.secondary', mt: 1 }}>
                            In case of emergency, this contact will be notified about your condition.
                          </Typography>
                        </Box>
                      </Paper>
                    ) : (
                      <Alert severity="warning" sx={{ fontSize: '1.125rem' }}>
                        <Typography variant="body1" sx={{ fontSize: '1.125rem' }}>
                          No emergency contact information provided. Please update your profile with emergency contact details.
                        </Typography>
                      </Alert>
                    )}
                  </Box>
                </Box>
              </Grid>

              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2.5 }}>
                  <Typography variant="h6" sx={{ fontSize: '1.375rem', fontWeight: 600, color: 'primary.main' }}>
                    Medical Information
                  </Typography>

                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                    <Typography sx={{ fontSize: '1.25rem' }}>
                      <Box component="span" sx={{ fontWeight: 600, minWidth: '140px', display: 'inline-block' }}>Patient ID:</Box>
                      {patientInfo.unique_id}
                    </Typography>

                    <Typography sx={{ fontSize: '1.25rem' }}>
                      <Box component="span" sx={{ fontWeight: 600, minWidth: '140px', display: 'inline-block' }}>Blood Type:</Box>
                      {patientInfo.blood_type || 'Not recorded'}
                    </Typography>

                    <Typography sx={{ fontSize: '1.25rem' }}>
                      <Box component="span" sx={{ fontWeight: 600, minWidth: '140px', display: 'inline-block' }}>Allergies:</Box>
                      {patientInfo.allergies || 'None recorded'}
                    </Typography>

                    <Typography sx={{ fontSize: '1.25rem' }}>
                      <Box component="span" sx={{ fontWeight: 600, minWidth: '140px', display: 'inline-block' }}>Primary Doctor:</Box>
                      {patientInfo.doctor_name ? `Dr. ${patientInfo.doctor_name}` : 'Not assigned'}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Paper>

          <Typography variant="body1" sx={{ fontSize: '1.25rem', mt: 2, color: 'text.secondary' }}>
            If any of your personal information needs to be updated, please contact your healthcare provider.
          </Typography>
        </div>

        {/* Upcoming Appointments Section */}
        <div className="dashboard-section">
          <SectionTitle>
            <EventNoteIcon /> Upcoming Appointments
          </SectionTitle>

          {upcomingAppointments.length > 0 ? (
            <Box sx={{ mb: 4 }}>
              {upcomingAppointments.map((appointment, index) => (
                <Paper
                  key={appointment.id}
                  elevation={1}
                  sx={{
                    p: 3,
                    mb: 2,
                    borderRadius: '16px',
                    border: '1px solid',
                    borderColor: 'divider',
                    bgcolor: theme.palette.mode === 'dark' ? 'rgba(2, 132, 199, 0.08)' : 'rgba(2, 132, 199, 0.04)',
                    borderLeft: '6px solid',
                    borderLeftColor: 'primary.main',
                    position: 'relative',
                    overflow: 'hidden'
                  }}
                >
                  <Box sx={{
                    position: 'absolute',
                    top: 0,
                    right: 0,
                    width: { xs: '80px', md: '120px' },
                    height: '100%',
                    bgcolor: 'primary.main',
                    opacity: 0.05,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}>
                    <EventNoteIcon sx={{ fontSize: '4rem', opacity: 0.2, color: 'primary.main' }} />
                  </Box>

                  <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, justifyContent: 'space-between', alignItems: { xs: 'flex-start', md: 'center' } }}>
                    <Box>
                      <Typography variant="h6" sx={{ fontSize: '1.5rem', fontWeight: 600, mb: 1, color: 'primary.main' }}>
                        {appointment.type}
                      </Typography>

                      <Typography variant="body1" sx={{ fontSize: '1.25rem', mb: 0.5, display: 'flex', alignItems: 'center', gap: 1 }}>
                        <CalendarTodayIcon color="primary" />
                        <strong>Date:</strong> {appointment.date.toLocaleDateString(undefined, { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
                      </Typography>

                      <Typography variant="body1" sx={{ fontSize: '1.25rem', mb: 0.5, display: 'flex', alignItems: 'center', gap: 1 }}>
                        <AccessTimeIcon color="primary" />
                        <strong>Time:</strong> {appointment.time}
                      </Typography>

                      <Typography variant="body1" sx={{ fontSize: '1.25rem', mb: 0.5, display: 'flex', alignItems: 'center', gap: 1 }}>
                        <PersonIcon color="primary" />
                        <strong>Doctor:</strong> {appointment.doctor}
                      </Typography>

                      <Typography variant="body1" sx={{ fontSize: '1.25rem', mb: 0.5, display: 'flex', alignItems: 'center', gap: 1 }}>
                        <LocationOnIcon color="primary" />
                        <strong>Location:</strong> {appointment.location}
                      </Typography>
                    </Box>

                    <Box sx={{ mt: { xs: 2, md: 0 }, display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Button
                        variant="contained"
                        color="primary"
                        size="large"
                        startIcon={<EventAvailableIcon />}
                        sx={{
                          fontSize: '1.125rem',
                          py: 1.5,
                          px: 3,
                          borderRadius: '8px',
                          fontWeight: 600
                        }}
                      >
                        Add to Calendar
                      </Button>

                      <Button
                        variant="outlined"
                        color="primary"
                        size="large"
                        startIcon={<EditIcon />}
                        sx={{
                          fontSize: '1.125rem',
                          py: 1.5,
                          px: 3,
                          borderRadius: '8px',
                          fontWeight: 600
                        }}
                      >
                        Reschedule
                      </Button>
                    </Box>
                  </Box>

                  <Box sx={{ mt: 2, pt: 2, borderTop: '1px dashed', borderColor: 'divider' }}>
                    <Typography variant="body2" sx={{ fontSize: '1.125rem', color: 'text.secondary' }}>
                      <InfoIcon fontSize="small" sx={{ mr: 1, verticalAlign: 'middle', color: 'info.main' }} />
                      Please arrive 15 minutes before your appointment time. Bring your insurance card and a list of current medications.
                    </Typography>
                  </Box>
                </Paper>
              ))}

              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                <Button
                  variant="outlined"
                  color="primary"
                  size="large"
                  startIcon={<AddIcon />}
                  sx={{
                    fontSize: '1.25rem',
                    py: 1.5,
                    px: 4,
                    borderRadius: '8px',
                    fontWeight: 600
                  }}
                >
                  Schedule New Appointment
                </Button>
              </Box>
            </Box>
          ) : (
            <Alert severity="info" sx={{ fontSize: '1.25rem', padding: '24px', mb: 4 }}>
              <Typography variant="h6" sx={{ mb: 1, fontSize: '1.375rem', fontWeight: 600 }}>
                No Upcoming Appointments
              </Typography>
              <Typography variant="body1" sx={{ fontSize: '1.25rem' }}>
                You don't have any scheduled appointments. Would you like to schedule a visit with your doctor?
              </Typography>
              <Button
                variant="contained"
                color="primary"
                size="large"
                startIcon={<AddIcon />}
                sx={{
                  fontSize: '1.125rem',
                  mt: 2,
                  py: 1.5,
                  px: 3,
                  borderRadius: '8px',
                  fontWeight: 600
                }}
              >
                Schedule Appointment
              </Button>
            </Alert>
          )}
        </div>

        {/* View Medications Button */}
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4, mb: 2 }}>
          <Button
            variant="contained"
            color="primary"
            size="large"
            onClick={() => setActiveTab(3)} // Switch to Prescriptions tab
            startIcon={<MedicationIcon />}
            sx={{
              fontSize: '1.25rem',
              padding: '12px 32px',
              borderRadius: '8px',
              boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)'
            }}
          >
            View Your Medications
          </Button>
        </Box>
      </Box>

      {/* Health Charts Tab Content */}
      <Box sx={{ display: activeTab !== 1 ? 'none' : 'block' }}>
        <div className="dashboard-section">
          <SectionTitle>
            <MonitorHeartIcon /> Your Health Trends
          </SectionTitle>

          {combinedData.length >= 2 ? (
            <Box>
              <Typography variant="body1" sx={{ fontSize: '1.125rem', mb: 2 }}>
                These charts show how your health measurements have changed over time, including data from your initial registration
                and all subsequent visits. This helps you and your doctor track your progress and identify any areas that need attention.
              </Typography>

              {/* Data source indicator */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3, gap: 2 }}>
                <Chip
                  icon={<InfoIcon />}
                  label="Data includes both patient record and visit data"
                  color="info"
                  variant="outlined"
                  size="medium"
                />
                <Typography variant="body2" sx={{ fontSize: '1rem', fontStyle: 'italic' }}>
                  {visits.length} visit{visits.length !== 1 ? 's' : ''} + initial patient data
                </Typography>
              </Box>

              <Typography variant="body1" sx={{ fontSize: '1.125rem', mb: 3 }}>
                <strong>How to use:</strong> Select different health measurements from the dropdown menu below to see how they've changed.
              </Typography>
              <Paper elevation={0} sx={{ p: 3, bgcolor: 'background.default', borderRadius: '16px', mb: 3 }}>
                <HealthMetricsChart visits={combinedData} />
              </Paper>
            </Box>
          ) : visits.length >= 1 && patientData ? (
            <Box>
              <Typography variant="body1" sx={{ fontSize: '1.125rem', mb: 2 }}>
                These charts show how your health measurements have changed over time, including data from your initial registration
                and all subsequent visits. This helps you and your doctor track your progress and identify any areas that need attention.
              </Typography>

              {/* Data source indicator */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 3, gap: 2 }}>
                <Chip
                  icon={<InfoIcon />}
                  label={visits.length > 0 ? "Data from visits" : "Data from patient record"}
                  color="info"
                  variant="outlined"
                  size="medium"
                />
                <Typography variant="body2" sx={{ fontSize: '1rem', fontStyle: 'italic' }}>
                  {visits.length} visit{visits.length !== 1 ? 's' : ''}
                </Typography>
              </Box>

              <Typography variant="body1" sx={{ fontSize: '1.125rem', mb: 3 }}>
                <strong>How to use:</strong> Select different health measurements from the dropdown menu below to see how they've changed.
              </Typography>
              <Paper elevation={0} sx={{ p: 3, bgcolor: 'background.default', borderRadius: '16px', mb: 3 }}>
                <HealthMetricsChart visits={combinedData.length > 0 ? combinedData : visits} />
              </Paper>
            </Box>
          ) : visitsLoading ? (
            <Box display="flex" justifyContent="center" alignItems="center" height="400px">
              <CircularProgress size={60} thickness={4} />
            </Box>
          ) : (
            <Alert severity="info" sx={{ fontSize: '1.125rem', padding: '24px' }}>
              <Typography variant="h6" sx={{ mb: 1, fontSize: '1.25rem' }}>
                Not Enough Health Data Yet
              </Typography>
              <Typography variant="body1" sx={{ fontSize: '1.125rem' }}>
                At least two data points with recorded health metrics are needed to display health charts.
                This includes your initial registration data and subsequent visits.
              </Typography>
            </Alert>
          )}
        </div>
      </Box>

      {/* Medical History Tab Content */}
      <Box sx={{ display: activeTab !== 2 ? 'none' : 'block' }}>
        <div className="dashboard-section">
          <SectionTitle>
            <EventNoteIcon /> Your Medical Visits
          </SectionTitle>

          {recentVisits && recentVisits.length > 0 ? (
            <div>
              <Typography variant="body1" sx={{ fontSize: '1.125rem', mb: 3 }}>
                Below is a record of your recent medical visits. Click on any visit to see more details.
              </Typography>

              {recentVisits.map((visit) => (
                <Paper
                  key={visit.visit_id}
                  elevation={2}
                  sx={{
                    mb: 3,
                    overflow: 'hidden',
                    transition: 'all 0.3s ease',
                    borderRadius: '12px',
                    border: '1px solid var(--border-color)'
                  }}
                >
                  <Box
                    sx={{
                      p: 3,
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center',
                      backgroundColor: 'primary.light',
                      color: 'primary.contrastText',
                      cursor: 'pointer',
                      fontSize: '1.25rem'
                    }}
                    onClick={() => toggleExpandVisit(visit.visit_id)}
                    role="button"
                    aria-expanded={expandedVisitId === visit.visit_id}
                    aria-controls={`visit-content-${visit.visit_id}`}
                  >
                    <Typography variant="h6" sx={{ fontSize: '1.25rem', fontWeight: 600 }}>
                      {formatDate(visit.visit_date)} - {visit.visit_reason || 'Routine checkup'}
                    </Typography>
                    <IconButton
                      size="large"
                      sx={{ color: 'inherit' }}
                      aria-label={expandedVisitId === visit.visit_id ? "Collapse visit details" : "Expand visit details"}
                    >
                      {expandedVisitId === visit.visit_id ? <ExpandLessIcon fontSize="large" /> : <ExpandMoreIcon fontSize="large" />}
                    </IconButton>
                  </Box>

                  <Collapse in={expandedVisitId === visit.visit_id} id={`visit-content-${visit.visit_id}`}>
                    <Box sx={{ p: 3 }}>
                      <Grid container spacing={3}>
                        <Grid item xs={12} md={6}>
                          <Typography variant="h6" sx={{ fontSize: '1.25rem', fontWeight: 600, mb: 2 }}>
                            Visit Information
                          </Typography>
                          <Typography sx={{ fontSize: '1.125rem', mb: 1.5 }}>
                            <strong>Doctor:</strong> Dr. {visit.doctor_first_name} {visit.doctor_last_name}
                          </Typography>
                          <Typography sx={{ fontSize: '1.125rem', mb: 1.5 }}>
                            <strong>Date:</strong> {formatDate(visit.visit_date)}
                          </Typography>
                          <Typography sx={{ fontSize: '1.125rem', mb: 1.5 }}>
                            <strong>Reason for Visit:</strong> {visit.visit_reason || 'Routine checkup'}
                          </Typography>
                        </Grid>

                        <Grid item xs={12} md={6}>
                          <Typography variant="h6" sx={{ fontSize: '1.25rem', fontWeight: 600, mb: 2 }}>
                            Vital Signs
                          </Typography>

                          {visit.vital_signs && (
                            <Box>
                              <Typography sx={{ fontSize: '1.125rem', mb: 1.5 }}>
                                <strong>Blood Pressure:</strong> {visit.vital_signs.blood_pressure} mmHg
                              </Typography>
                              <Typography sx={{ fontSize: '1.125rem', mb: 1.5 }}>
                                <strong>Heart Rate:</strong> {visit.vital_signs.heart_rate} bpm
                              </Typography>
                              <Typography sx={{ fontSize: '1.125rem', mb: 1.5 }}>
                                <strong>Temperature:</strong> {visit.vital_signs.temperature}°C
                              </Typography>
                            </Box>
                          )}

                          {/* Orthostatic Assessment for Visit */}
                          {visit.vital_signs?.lying_bp_systolic && visit.vital_signs?.standing_bp_systolic &&
                           visit.vital_signs?.lying_bp_diastolic && visit.vital_signs?.standing_bp_diastolic &&
                           visit.vital_signs?.lying_heart_rate && visit.vital_signs?.standing_heart_rate && (
                            <Box mt={2}>
                              {(() => {
                                const systolicDiff = (visit.vital_signs.lying_bp_systolic as number) - (visit.vital_signs.standing_bp_systolic as number);
                                const diastolicDiff = (visit.vital_signs.lying_bp_diastolic as number) - (visit.vital_signs.standing_bp_diastolic as number);
                                const heartRateDiff = (visit.vital_signs.standing_heart_rate as number) - (visit.vital_signs.lying_heart_rate as number);

                                const hasOrthostaticHypotension = systolicDiff >= 20 || diastolicDiff >= 10;
                                const hasCompensatoryTachycardia = heartRateDiff >= 20;

                                let assessmentText = '';
                                let status: 'normal' | 'warning' | 'abnormal' = 'normal';

                                if (hasOrthostaticHypotension) {
                                  assessmentText = 'Orthostatic Hypotension Detected';
                                  status = 'abnormal';
                                } else if (hasCompensatoryTachycardia) {
                                  assessmentText = 'Compensatory Tachycardia Present';
                                  status = 'warning';
                                } else {
                                  assessmentText = 'No Orthostatic Hypotension';
                                  status = 'normal';
                                }

                                return (
                                  <div className={`status-indicator status-${status}`}>
                                    <InfoIcon />
                                    {assessmentText}
                                  </div>
                                );
                              })()}
                            </Box>
                          )}
                        </Grid>

                        <Grid item xs={12}>
                          <Divider sx={{ my: 2 }} />
                          <Typography variant="h6" sx={{ fontSize: '1.25rem', fontWeight: 600, mb: 1.5 }}>
                            Diagnosis
                          </Typography>
                          <Typography variant="body1" sx={{ fontSize: '1.125rem', mb: 3 }}>
                            {visit.diagnosis || 'No diagnosis was recorded for this visit.'}
                          </Typography>
                        </Grid>

                        <Grid item xs={12}>
                          <Typography variant="h6" sx={{ fontSize: '1.25rem', fontWeight: 600, mb: 1.5 }}>
                            Visit Summary
                          </Typography>
                          <Typography variant="body1" sx={{ fontSize: '1.125rem', mb: 3 }}>
                            {visit.notes || 'No additional notes were recorded for this visit.'}
                          </Typography>
                        </Grid>

                        <Grid item xs={12}>
                          <Button
                            variant="contained"
                            size="large"
                            onClick={() => setActiveTab(4)} // Switch to Contact Doctor tab (now tab 4)
                            sx={{
                              mt: 2,
                              fontSize: '1.125rem',
                              padding: '12px 24px',
                              borderRadius: '8px'
                            }}
                          >
                            Follow up with doctor
                          </Button>
                        </Grid>
                      </Grid>
                    </Box>
                  </Collapse>
                </Paper>
              ))}
            </div>
          ) : (
            <Alert severity="info" sx={{ fontSize: '1.125rem', padding: '24px' }}>
              <Typography variant="h6" sx={{ mb: 1, fontSize: '1.25rem' }}>
                No Visit History
              </Typography>
              <Typography variant="body1" sx={{ fontSize: '1.125rem' }}>
                You don't have any recorded medical visits yet. After your first appointment, your visit history will appear here.
              </Typography>
              <Box sx={{ mt: 3, textAlign: 'center' }}>
                <Typography variant="body1" sx={{ fontSize: '1.125rem', mb: 2, fontStyle: 'italic' }}>
                  Please contact your healthcare provider to schedule your first appointment.
                </Typography>
              </Box>
            </Alert>
          )}
        </div>
      </Box>

      {/* Prescriptions Tab Content */}
      <Box sx={{ display: activeTab !== 3 ? 'none' : 'block' }}>
        <div className="dashboard-section">
          <SectionTitle>
            <MedicationIcon /> Your Prescriptions
          </SectionTitle>

          <Box sx={{ mb: 4 }}>
            <Typography variant="body1" sx={{ fontSize: '1.25rem', mb: 3 }}>
              Below are your current medications prescribed by your healthcare provider. Always take medications as directed and report any side effects to your doctor.
            </Typography>

            {medications.length > 0 ? (
              <Grid container spacing={3}>
                {medications.map((med, index) => (
                  <Grid item xs={12} md={6} key={index}>
                    <Paper elevation={2} sx={{
                      p: 3,
                      borderRadius: '16px',
                      height: '100%',
                      border: '1px solid var(--border-color)',
                      borderLeft: '5px solid #0284c7',
                      transition: 'transform 0.2s ease, box-shadow 0.2s ease',
                      '&:hover': {
                        transform: 'translateY(-4px)',
                        boxShadow: '0 6px 16px rgba(0, 0, 0, 0.1)'
                      }
                    }}>
                      <Typography variant="h6" sx={{ fontSize: '1.375rem', fontWeight: 600, mb: 2, color: 'text.primary' }}>
                        {med.medication_name}
                      </Typography>

                      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                        <Typography sx={{ fontSize: '1.25rem' }}>
                          <Box component="span" sx={{ fontWeight: 600, color: 'text.secondary' }}>Dosage: </Box>
                          {med.dosage}
                        </Typography>

                        <Typography sx={{ fontSize: '1.25rem' }}>
                          <Box component="span" sx={{ fontWeight: 600, color: 'text.secondary' }}>Instructions: </Box>
                          {med.instructions}
                        </Typography>

                        {med.prescribed_date && (
                          <Typography sx={{ fontSize: '1.125rem', mt: 1, color: 'text.secondary' }}>
                            Prescribed on {new Date(med.prescribed_date).toLocaleDateString()}
                          </Typography>
                        )}
                      </Box>
                    </Paper>
                  </Grid>
                ))}
              </Grid>
            ) : (
              <Alert severity="info" sx={{ fontSize: '1.25rem', padding: '24px' }}>
                <Typography variant="h6" sx={{ mb: 1, fontSize: '1.375rem', fontWeight: 600 }}>
                  No Active Medications
                </Typography>
                <Typography variant="body1" sx={{ fontSize: '1.25rem' }}>
                  You don't have any active medications prescribed at this time. Your doctor will add medications here if they are prescribed.
                </Typography>
              </Alert>
            )}
          </Box>

          <Box sx={{
            p: 3,
            bgcolor: theme.palette.mode === 'dark' ? 'rgba(255, 193, 7, 0.15)' : '#fff9e6',
            borderRadius: '12px',
            border: `1px solid ${theme.palette.mode === 'dark' ? 'rgba(255, 193, 7, 0.3)' : '#ffeeba'}`
          }}>
            <Typography variant="h6" sx={{ fontSize: '1.25rem', fontWeight: 600, display: 'flex', alignItems: 'center', gap: 1, mb: 1.5 }}>
              <WarningIcon color="warning" /> Important Medication Information
            </Typography>
            <Box component="ul" sx={{ pl: 3, mb: 0 }}>
              <Typography component="li" variant="body1" sx={{ fontSize: '1.25rem', mb: 1 }}>
                Always take medications exactly as prescribed by your doctor.
              </Typography>
              <Typography component="li" variant="body1" sx={{ fontSize: '1.25rem', mb: 1 }}>
                Do not stop taking medications without consulting your doctor first.
              </Typography>
              <Typography component="li" variant="body1" sx={{ fontSize: '1.25rem', mb: 1 }}>
                Keep all medications out of reach of children and pets.
              </Typography>
              <Typography component="li" variant="body1" sx={{ fontSize: '1.25rem' }}>
                If you experience any side effects, contact your healthcare provider immediately.
              </Typography>
            </Box>
          </Box>

          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'center' }}>
            <Button
              variant="contained"
              color="primary"
              size="large"
              onClick={() => setActiveTab(4)} // Switch to Contact Doctor tab
              startIcon={<ContactPhoneIcon />}
              sx={{
                fontSize: '1.125rem',
                padding: '12px 24px',
                borderRadius: '8px'
              }}
            >
              Contact Your Doctor About Medications
            </Button>
          </Box>
        </div>
      </Box>

      {/* Contact Doctor Tab Content */}
      <Box sx={{ display: activeTab !== 4 ? 'none' : 'block' }}>
        <div className="dashboard-section">
          <SectionTitle>
            <LocalHospitalIcon /> Contact Your Doctor
          </SectionTitle>

          {patientInfo.doctor_id ? (
            <Grid container spacing={4}>
              <Grid item xs={12} md={6}>
                <Typography variant="h6" sx={{ fontSize: '1.25rem', fontWeight: 600, mb: 2 }}>
                  Send a Message
                </Typography>

                <Typography variant="body1" sx={{ fontSize: '1.125rem', mb: 3 }}>
                  Use this form to send a message to Dr. {patientInfo.doctor_name || 'your doctor'} about non-urgent health concerns or questions.
                </Typography>

                <TextField
                  label="Your message"
                  multiline
                  rows={8}
                  fullWidth
                  value={messageText}
                  onChange={(e) => setMessageText(e.target.value)}
                  placeholder="Type your message here..."
                  variant="outlined"
                  sx={{
                    mb: 3,
                    '& .MuiInputBase-root': {
                      fontSize: '1.125rem',
                    },
                    '& .MuiInputLabel-root': {
                      fontSize: '1.125rem',
                    }
                  }}
                />

                <Alert severity="warning" sx={{ mb: 3, fontSize: '1.125rem' }}>
                  <Typography variant="body1" sx={{ fontWeight: 600, fontSize: '1.125rem' }}>
                    Important:
                  </Typography>
                  <Typography variant="body1" sx={{ fontSize: '1.125rem' }}>
                    For urgent medical concerns, please call your doctor's office directly or go to the nearest emergency room.
                  </Typography>
                </Alert>

                <Button
                  variant="contained"
                  color="primary"
                  size="large"
                  endIcon={<SendIcon />}
                  onClick={handleSendMessage}
                  disabled={!messageText.trim() || messageSending}
                  sx={{
                    fontSize: '1.125rem',
                    padding: '12px 24px',
                    borderRadius: '8px'
                  }}
                >
                  {messageSending ? 'Sending...' : 'Send Message'}
                </Button>

                {messageSuccess && (
                  <Alert severity="success" sx={{ mt: 3, fontSize: '1.125rem', padding: '16px 24px' }}>
                    <Typography variant="body1" sx={{ fontWeight: 600, fontSize: '1.125rem' }}>
                      Message Sent Successfully!
                    </Typography>
                    <Typography variant="body1" sx={{ fontSize: '1.125rem' }}>
                      Your doctor will respond as soon as possible.
                    </Typography>
                  </Alert>
                )}
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="h6" sx={{ fontSize: '1.25rem', fontWeight: 600, mb: 2 }}>
                  Message History
                </Typography>

                {messages.length > 0 ? (
                  <Paper elevation={0} sx={{ bgcolor: 'background.default', borderRadius: '12px', p: 0 }}>
                    <List sx={{ p: 0 }}>
                      {messages.map((message) => (
                        <React.Fragment key={message.id}>
                          <ListItem alignItems="flex-start" sx={{ p: 3 }}>
                            <ListItemText
                              primary={
                                <Typography variant="subtitle1" sx={{ fontSize: '1.125rem', fontWeight: 600, mb: 1 }}>
                                  To: {message.recipient}
                                </Typography>
                              }
                              secondary={
                                <>
                                  <Typography component="div" variant="body1" color="text.primary" sx={{ fontSize: '1.125rem', mb: 2 }}>
                                    {message.content}
                                  </Typography>
                                  <Typography variant="body2" sx={{ color: '#64748b', fontSize: '1rem' }}>
                                    Sent on {new Date(message.timestamp).toLocaleString()}
                                  </Typography>
                                </>
                              }
                            />
                          </ListItem>
                          <Divider component="li" />
                        </React.Fragment>
                      ))}
                    </List>
                  </Paper>
                ) : (
                  <Alert severity="info" sx={{ fontSize: '1.125rem', padding: '24px' }}>
                    <Typography variant="body1" sx={{ fontSize: '1.125rem' }}>
                      No message history yet. Start a conversation with your doctor using the form.
                    </Typography>
                  </Alert>
                )}
              </Grid>
            </Grid>
          ) : (
            <Alert severity="info" sx={{ fontSize: '1.125rem', padding: '24px' }}>
              <Typography variant="h6" sx={{ mb: 1, fontSize: '1.25rem' }}>
                No Doctor Assigned
              </Typography>
              <Typography variant="body1" sx={{ fontSize: '1.125rem' }}>
                You don't have a primary doctor assigned yet. Please contact the clinic to have a doctor assigned to you.
              </Typography>
            </Alert>
          )}
        </div>
      </Box>

      {/* Success Dialog - Larger and more readable for seniors */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        PaperProps={{
          sx: {
            borderRadius: '16px',
            padding: '16px',
            maxWidth: '500px'
          }
        }}
      >
        <DialogTitle id="alert-dialog-title" sx={{ fontSize: '1.5rem', fontWeight: 600 }}>
          Message Sent Successfully
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="alert-dialog-description" sx={{ fontSize: '1.125rem' }}>
            Your message has been sent to your doctor. They will review it and respond as soon as possible.
            <Box component="p" sx={{ mt: 2, fontWeight: 600 }}>
              For urgent matters, please call your doctor's office directly.
            </Box>
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={handleCloseDialog}
            color="primary"
            variant="contained"
            size="large"
            autoFocus
            sx={{
              fontSize: '1.125rem',
              padding: '10px 20px',
              borderRadius: '8px'
            }}
          >
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
};

export default PatientDashboard;