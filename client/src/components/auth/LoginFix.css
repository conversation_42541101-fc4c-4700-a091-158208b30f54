/* Additional fixes for the login page */

/* This is a more specific selector to override any other styles */
body.dark-mode .login-form-section .login-logo-container .login-heading {
  background-color: transparent !important;
  color: #ffffff !important;
}

/* Target any potential parent elements that might have a background */
body.dark-mode .login-form-section .login-logo-container,
body.dark-mode .login-form-section .login-logo-container > *,
body.dark-mode .login-form-section .login-logo-container > * > * {
  background-color: transparent !important;
}

/* Force all typography elements in the login form to have transparent backgrounds */
body.dark-mode .login-form-section .MuiTypography-root {
  background-color: transparent !important;
}

/* Target any potential black bars or backgrounds */
body.dark-mode .login-form-section *[style*="background-color"] {
  background-color: transparent !important;
}
