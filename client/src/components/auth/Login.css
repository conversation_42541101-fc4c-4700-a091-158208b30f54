/* Login page specific styles */

/* Override global dark mode styles for the login page */
body.dark-mode .login-container.MuiBox-root {
  background-color: #121212 !important;
}

body.dark-mode .login-card.MuiCard-root {
  background-color: #1e1e1e !important;
}

body.dark-mode .login-card .MuiCardContent-root {
  background-color: #1e1e1e !important;
  color: #ffffff !important;
}

body.dark-mode .login-image-section.MuiBox-root {
  background-color: transparent !important;
  background-image: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%),
    url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%232C4B2B' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") !important;
}

/* Typography overrides */
body.dark-mode .login-card .MuiTypography-root {
  color: #ffffff !important;
  background-color: transparent !important;
}

body.dark-mode .login-card .MuiTypography-colorTextSecondary {
  color: #aaaaaa !important;
  background-color: transparent !important;
}

/* Form controls */
body.dark-mode .login-card .MuiInputBase-input {
  color: #ffffff !important;
}

body.dark-mode .login-card .MuiInputLabel-root {
  color: #aaaaaa !important;
}

body.dark-mode .login-card .MuiOutlinedInput-notchedOutline {
  border-color: rgba(255, 255, 255, 0.2) !important;
}

/* Ensure the wave pattern at the bottom of the image section is visible */
body.dark-mode .login-image-section .wave-pattern {
  opacity: 1 !important;
}

/* Ensure the avatar and icon colors are preserved */
body.dark-mode .login-image-section .MuiAvatar-root {
  background-color: #2C4B2B !important;
}

body.dark-mode .login-form-section .MuiAvatar-root {
  background-color: #D97B3A !important;
}

/* Fix the black bar behind the Sign In text */
body.dark-mode .login-logo-container {
  background-color: transparent !important;
}

/* Ensure the logo container has no background */
body.dark-mode .login-logo-container.MuiBox-root {
  background-color: transparent !important;
}

/* Ensure the Sign In heading has no background */
body.dark-mode .login-heading {
  background-color: transparent !important;
}

body.dark-mode .login-heading.MuiTypography-root {
  background-color: transparent !important;
  color: #ffffff !important;
}
