import React, { useState, useEffect, useContext } from 'react';
import {
  Box,
  Button,
  TextField,
  Typography,
  Paper,
  CircularProgress,
  Alert,
  Container,
  InputAdornment,
  IconButton,
  LinearProgress,
  Grid,
  Card,
  CardContent,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  useTheme
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  Check as CheckIcon,
  Close as CloseIcon,
  LockReset as LockResetIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import axios from 'axios';
import { API_URL } from '../../config';
import AuthContext from '../../context/AuthContext';
import { useNavigate } from 'react-router-dom';

interface PasswordPolicy {
  min_length: number;
  max_length: number;
  require_uppercase: boolean;
  require_lowercase: boolean;
  require_numbers: boolean;
  require_special_chars: boolean;
  password_expiry_days: number;
  password_history_count: number;
  max_failed_attempts: number;
}

interface PasswordValidation {
  isValid: boolean;
  errors: string[];
}

const ChangePassword: React.FC = () => {
  const { user, logout } = useContext(AuthContext);
  const navigate = useNavigate();
  const theme = useTheme();

  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  
  const [passwordPolicy, setPasswordPolicy] = useState<PasswordPolicy | null>(null);
  const [policyLoading, setPolicyLoading] = useState(true);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [validation, setValidation] = useState<PasswordValidation | null>(null);

  // Determine if this is a first-time login or password reset
  const isFirstLogin = user?.passwordChange?.reason === 'first_login';
  const isDefaultPassword = user?.passwordChange?.reason === 'default_password';
  const isPasswordExpired = user?.passwordChange?.reason === 'password_expired';
  const isForced = isFirstLogin || isDefaultPassword || isPasswordExpired;

  // Fetch password policy on component mount
  useEffect(() => {
    const fetchPasswordPolicy = async () => {
      try {
        setPolicyLoading(true);
        const response = await axios.get(`${API_URL}/api/auth/password-policy`);
        setPasswordPolicy(response.data);
      } catch (err) {
        console.error('Error fetching password policy:', err);
        setError('Failed to load password requirements. Please try again.');
      } finally {
        setPolicyLoading(false);
      }
    };

    fetchPasswordPolicy();
  }, []);

  // Validate password against policy when it changes
  useEffect(() => {
    const validatePassword = async () => {
      if (!newPassword) {
        setValidation(null);
        setPasswordStrength(0);
        return;
      }

      try {
        const token = localStorage.getItem('token');
        if (!token) {
          setError('Authentication token not found. Please log in again.');
          return;
        }

        const response = await axios.post(
          `${API_URL}/api/auth/validate-password`,
          { password: newPassword },
          {
            headers: {
              'Content-Type': 'application/json',
              'x-auth-token': token
            }
          }
        );

        setValidation(response.data);
        
        // Calculate password strength
        if (response.data.isValid) {
          setPasswordStrength(100);
        } else {
          // Calculate strength based on number of errors
          const errorCount = response.data.errors.length;
          const maxErrors = 5; // Assuming 5 possible error types
          const strength = Math.max(10, Math.round(100 * (1 - errorCount / maxErrors)));
          setPasswordStrength(strength);
        }
      } catch (err) {
        console.error('Error validating password:', err);
      }
    };

    if (newPassword) {
      validatePassword();
    }
  }, [newPassword]);

  const handleToggleCurrentPasswordVisibility = () => {
    setShowCurrentPassword(!showCurrentPassword);
  };

  const handleToggleNewPasswordVisibility = () => {
    setShowNewPassword(!showNewPassword);
  };

  const handleToggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setValidationErrors([]);
    
    // Validate inputs
    const errors = [];
    
    if (!isForced && !currentPassword) {
      errors.push('Current password is required');
    }
    
    if (!newPassword) {
      errors.push('New password is required');
    }
    
    if (newPassword !== confirmPassword) {
      errors.push('Passwords do not match');
    }
    
    if (errors.length > 0) {
      setValidationErrors(errors);
      return;
    }
    
    setLoading(true);
    
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        setError('Authentication token not found. Please log in again.');
        setLoading(false);
        return;
      }
      
      // Make API request to change password
      const response = await axios.post(
        `${API_URL}/api/auth/change-password`,
        {
          currentPassword: isForced ? undefined : currentPassword,
          newPassword
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          }
        }
      );
      
      setSuccess(true);
      
      // Clear form
      setCurrentPassword('');
      setNewPassword('');
      setConfirmPassword('');
      
      // Logout after 3 seconds to force re-login with new password
      setTimeout(() => {
        logout();
        navigate('/login');
      }, 3000);
    } catch (err: any) {
      console.error('Error changing password:', err);
      
      if (err.response?.data?.errors) {
        setValidationErrors(err.response.data.errors);
      } else if (err.response?.data?.msg) {
        setError(err.response.data.msg);
      } else {
        setError('Failed to change password. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const getPasswordStrengthColor = () => {
    if (passwordStrength < 30) return theme.palette.error.main;
    if (passwordStrength < 70) return theme.palette.warning.main;
    return theme.palette.success.main;
  };

  const getPasswordStrengthLabel = () => {
    if (passwordStrength < 30) return 'Weak';
    if (passwordStrength < 70) return 'Moderate';
    return 'Strong';
  };

  return (
    <Container maxWidth="md" sx={{ mt: 4, mb: 4 }}>
      <Paper 
        elevation={3} 
        sx={{ 
          p: 4, 
          borderRadius: 2,
          backgroundColor: theme.palette.background.paper
        }}
      >
        <Box sx={{ mb: 3, display: 'flex', alignItems: 'center' }}>
          <LockResetIcon sx={{ fontSize: 32, mr: 2, color: theme.palette.primary.main }} />
          <Typography variant="h4" component="h1" fontWeight="500">
            {isFirstLogin ? 'Set Your Password' : 
             isDefaultPassword ? 'Change Default Password' : 
             isPasswordExpired ? 'Password Expired' : 
             'Change Password'}
          </Typography>
        </Box>

        {isForced && (
          <Alert severity="info" sx={{ mb: 3 }}>
            {isFirstLogin && 'This appears to be your first login. Please set a new password to continue.'}
            {isDefaultPassword && 'You are using a default password. Please change it to continue.'}
            {isPasswordExpired && 'Your password has expired. Please set a new password to continue.'}
          </Alert>
        )}

        {error && <Alert severity="error" sx={{ mb: 3 }}>{error}</Alert>}
        
        {validationErrors.length > 0 && (
          <Alert severity="error" sx={{ mb: 3 }}>
            <Typography variant="subtitle2">Please correct the following errors:</Typography>
            <ul style={{ marginTop: 8, paddingLeft: 20 }}>
              {validationErrors.map((err, index) => (
                <li key={index}>{err}</li>
              ))}
            </ul>
          </Alert>
        )}
        
        {success && (
          <Alert severity="success" sx={{ mb: 3 }}>
            Password changed successfully! You will be redirected to login again.
          </Alert>
        )}

        <Grid container spacing={3}>
          <Grid item xs={12} md={7}>
            <form onSubmit={handleSubmit}>
              {!isForced && (
                <TextField
                  fullWidth
                  label="Current Password"
                  type={showCurrentPassword ? 'text' : 'password'}
                  value={currentPassword}
                  onChange={(e) => setCurrentPassword(e.target.value)}
                  margin="normal"
                  required={!isForced}
                  disabled={loading || success}
                  InputProps={{
                    endAdornment: (
                      <InputAdornment position="end">
                        <IconButton
                          onClick={handleToggleCurrentPasswordVisibility}
                          edge="end"
                          disabled={loading || success}
                        >
                          {showCurrentPassword ? <VisibilityOff /> : <Visibility />}
                        </IconButton>
                      </InputAdornment>
                    )
                  }}
                />
              )}
              
              <TextField
                fullWidth
                label="New Password"
                type={showNewPassword ? 'text' : 'password'}
                value={newPassword}
                onChange={(e) => setNewPassword(e.target.value)}
                margin="normal"
                required
                disabled={loading || success}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={handleToggleNewPasswordVisibility}
                        edge="end"
                        disabled={loading || success}
                      >
                        {showNewPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  )
                }}
              />
              
              {newPassword && (
                <Box sx={{ mt: 1, mb: 2 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                    <Typography variant="body2" sx={{ mr: 1 }}>
                      Password Strength: {getPasswordStrengthLabel()}
                    </Typography>
                    <Typography variant="body2" sx={{ ml: 'auto', color: getPasswordStrengthColor() }}>
                      {passwordStrength}%
                    </Typography>
                  </Box>
                  <LinearProgress 
                    variant="determinate" 
                    value={passwordStrength} 
                    sx={{ 
                      height: 8, 
                      borderRadius: 4,
                      backgroundColor: theme.palette.grey[200],
                      '& .MuiLinearProgress-bar': {
                        backgroundColor: getPasswordStrengthColor()
                      }
                    }}
                  />
                </Box>
              )}
              
              <TextField
                fullWidth
                label="Confirm New Password"
                type={showConfirmPassword ? 'text' : 'password'}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                margin="normal"
                required
                disabled={loading || success}
                error={newPassword !== confirmPassword && confirmPassword !== ''}
                helperText={newPassword !== confirmPassword && confirmPassword !== '' ? 'Passwords do not match' : ''}
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={handleToggleConfirmPasswordVisibility}
                        edge="end"
                        disabled={loading || success}
                      >
                        {showConfirmPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  )
                }}
              />
              
              <Button
                type="submit"
                variant="contained"
                color="primary"
                fullWidth
                size="large"
                disabled={loading || success}
                sx={{ mt: 3, mb: 2, py: 1.5 }}
              >
                {loading ? <CircularProgress size={24} /> : 'Change Password'}
              </Button>
            </form>
          </Grid>
          
          <Grid item xs={12} md={5}>
            <Card variant="outlined" sx={{ height: '100%' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
                  <InfoIcon sx={{ mr: 1, fontSize: 20 }} />
                  Password Requirements
                </Typography>
                
                {policyLoading ? (
                  <CircularProgress size={24} sx={{ mt: 2 }} />
                ) : passwordPolicy ? (
                  <List dense>
                    <ListItem>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        {validation?.errors.includes(`Password must be at least ${passwordPolicy.min_length} characters long`) ? 
                          <CloseIcon color="error" fontSize="small" /> : 
                          <CheckIcon color="success" fontSize="small" />
                        }
                      </ListItemIcon>
                      <ListItemText primary={`At least ${passwordPolicy.min_length} characters`} />
                    </ListItem>
                    
                    {passwordPolicy.require_uppercase && (
                      <ListItem>
                        <ListItemIcon sx={{ minWidth: 32 }}>
                          {validation?.errors.includes('Password must contain at least one uppercase letter') ? 
                            <CloseIcon color="error" fontSize="small" /> : 
                            <CheckIcon color="success" fontSize="small" />
                          }
                        </ListItemIcon>
                        <ListItemText primary="At least one uppercase letter" />
                      </ListItem>
                    )}
                    
                    {passwordPolicy.require_lowercase && (
                      <ListItem>
                        <ListItemIcon sx={{ minWidth: 32 }}>
                          {validation?.errors.includes('Password must contain at least one lowercase letter') ? 
                            <CloseIcon color="error" fontSize="small" /> : 
                            <CheckIcon color="success" fontSize="small" />
                          }
                        </ListItemIcon>
                        <ListItemText primary="At least one lowercase letter" />
                      </ListItem>
                    )}
                    
                    {passwordPolicy.require_numbers && (
                      <ListItem>
                        <ListItemIcon sx={{ minWidth: 32 }}>
                          {validation?.errors.includes('Password must contain at least one number') ? 
                            <CloseIcon color="error" fontSize="small" /> : 
                            <CheckIcon color="success" fontSize="small" />
                          }
                        </ListItemIcon>
                        <ListItemText primary="At least one number" />
                      </ListItem>
                    )}
                    
                    {passwordPolicy.require_special_chars && (
                      <ListItem>
                        <ListItemIcon sx={{ minWidth: 32 }}>
                          {validation?.errors.includes('Password must contain at least one special character') ? 
                            <CloseIcon color="error" fontSize="small" /> : 
                            <CheckIcon color="success" fontSize="small" />
                          }
                        </ListItemIcon>
                        <ListItemText primary="At least one special character" />
                      </ListItem>
                    )}
                    
                    <ListItem>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        {validation?.errors.some(err => err.includes('Cannot reuse')) ? 
                          <CloseIcon color="error" fontSize="small" /> : 
                          <CheckIcon color="success" fontSize="small" />
                        }
                      </ListItemIcon>
                      <ListItemText primary={`Different from previous ${passwordPolicy.password_history_count} passwords`} />
                    </ListItem>
                    
                    <ListItem>
                      <ListItemIcon sx={{ minWidth: 32 }}>
                        {validation?.errors.includes('Password cannot contain your username') ? 
                          <CloseIcon color="error" fontSize="small" /> : 
                          <CheckIcon color="success" fontSize="small" />
                        }
                      </ListItemIcon>
                      <ListItemText primary="Cannot contain your username" />
                    </ListItem>
                  </List>
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    Failed to load password requirements.
                  </Typography>
                )}
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Paper>
    </Container>
  );
};

export default ChangePassword;
