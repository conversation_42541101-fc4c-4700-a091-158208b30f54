import React, { useState, useContext, useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import AuthContext from '../../context/AuthContext';
import {
  Avatar,
  Button,
  TextField,
  Typography,
  Alert,
  Paper,
  CircularProgress,
  InputAdornment,
  IconButton,
  Grid,
  Container,
  Box,
  Card,
  CardContent,
  Divider,
  useTheme,
  useMediaQuery,
  alpha
} from '@mui/material';
import LoadingSpinner from '../common/LoadingSpinner';
import {
  LockOutlined as LockOutlinedIcon,
  Visibility,
  VisibilityOff,
  MedicalServices as MedicalIcon,
  HealthAndSafety as HealthIcon
} from '@mui/icons-material';
import { API_URL } from '../../config';
import { styled } from '@mui/material/styles';
import './Login.css';
import './LoginFix.css';
import './LoginDarkModeFix.css';
import './LoginHeadingFix.css';

// Default max attempts, will be updated from server response
const DEFAULT_MAX_ATTEMPTS = 5;

// Styled components
const LoginContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  minHeight: '100vh',
  backgroundColor: theme.palette.mode === 'dark' ? theme.palette.background.default : '#f8fafc',
  alignItems: 'center',
  justifyContent: 'center',
  padding: theme.spacing(2),
  // Override any global dark mode styles
  '&.MuiBox-root': {
    backgroundColor: theme.palette.mode === 'dark' ? '#121212 !important' : '#f8fafc !important'
  }
}));

const LoginCard = styled(Card)(({ theme }) => ({
  display: 'flex',
  maxWidth: 1000,
  width: '100%',
  borderRadius: 16,
  overflow: 'hidden',
  boxShadow: theme.palette.mode === 'dark'
    ? '0 10px 40px rgba(0, 0, 0, 0.2)'
    : '0 10px 40px rgba(0, 0, 0, 0.04)',
  backgroundColor: theme.palette.background.paper,
  [theme.breakpoints.down('md')]: {
    flexDirection: 'column',
    maxWidth: 450
  },
  // Override any global dark mode styles
  '&.MuiCard-root': {
    backgroundColor: theme.palette.mode === 'dark' ? '#1e1e1e !important' : '#ffffff !important'
  }
}));

const ImageSection = styled(Box)(({ theme }) => ({
  flex: 1,
  background: theme.palette.mode === 'dark'
    ? 'linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%)'
    : 'linear-gradient(135deg, #f0f0f0 0%, #e5e5e5 100%)',
  backgroundImage: theme.palette.mode === 'dark'
    ? `
      linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%),
      url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%232C4B2B' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
    `
    : `
      linear-gradient(135deg, #f0f0f0 0%, #e5e5e5 100%),
      url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%232C4B2B' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
    `,
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  color: theme.palette.mode === 'dark' ? '#4CAF50' : '#2C4B2B',
  padding: theme.spacing(6),
  position: 'relative',
  [theme.breakpoints.down('md')]: {
    display: 'none'
  },
  // Override any global dark mode styles
  '&.MuiBox-root': {
    backgroundColor: 'transparent !important'
  }
}));

const FormSection = styled(CardContent)(({ theme }) => ({
  flex: 1,
  padding: theme.spacing(6),
  display: 'flex',
  flexDirection: 'column',
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(4, 2)
  },
  // Override any global dark mode styles
  '&.MuiCardContent-root': {
    backgroundColor: theme.palette.mode === 'dark' ? '#1e1e1e !important' : '#ffffff !important',
    color: theme.palette.mode === 'dark' ? '#ffffff !important' : 'inherit !important'
  },
  '& form': {
    backgroundColor: 'transparent !important'
  },
  '& form > *': {
    backgroundColor: 'transparent !important'
  }
}));

const LogoContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(4),
  backgroundColor: 'transparent',
  // Override any global dark mode styles
  '&.MuiBox-root': {
    backgroundColor: 'transparent !important'
  }
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  '& .MuiOutlinedInput-root': {
    borderRadius: 8,
    transition: 'all 0.2s ease-in-out',
    '&.Mui-focused': {
      boxShadow: `0 0 0 2px ${alpha(theme.palette.primary.main, 0.2)}`
    }
  }
}));

const Login: React.FC = () => {
  const navigate = useNavigate();
  const { login, isAuthenticated, error, clearError, user } = useContext(AuthContext);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [loading, setLoading] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [loginAttempts, setLoginAttempts] = useState(0);
  const [maxAttempts, setMaxAttempts] = useState(DEFAULT_MAX_ATTEMPTS);
  const [isLocked, setIsLocked] = useState(false);
  const [lockoutMessage, setLockoutMessage] = useState<string>('');

  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });

  const [showPassword, setShowPassword] = useState(false);
  const { email, password } = formData;

  // Reset state when component mounts
  useEffect(() => {
    console.log('Login component mounted');
    setLoginAttempts(0);
    setIsLocked(false);
    setLockoutMessage('');

    // Fetch the current password policy to get max_failed_attempts
    const fetchPasswordPolicy = async () => {
      try {
        const response = await fetch(`${API_URL}/api/auth/password-policy`);
        if (response.ok) {
          const policy = await response.json();
          if (policy.max_failed_attempts) {
            console.log('Setting max attempts from policy:', policy.max_failed_attempts);
            setMaxAttempts(policy.max_failed_attempts);
          }
        }
      } catch (err) {
        console.error('Error fetching password policy:', err);
        // Keep using the default max attempts
      }
    };

    fetchPasswordPolicy();
  }, []);

  // Add useEffect for conditional redirects
  useEffect(() => {
    if (isAuthenticated && user) {
      console.log('User authenticated, redirecting based on role:', user.role);

      // Check if password change is required
      if (user.passwordChange && user.passwordChange.required) {
        console.log('Password change required, redirecting to change password page');
        navigate('/change-password');
        return;
      }

      // Redirect based on user role
      if (user.role === 'admin') {
        navigate('/admin');
      } else if (user.role === 'patient') {
        navigate('/patient/dashboard');
      } else if (user.role === 'assistant') {
        // Rita should go directly to appointments
        navigate('/appointments');
      } else if (user.role === 'doctor') {
        navigate('/dashboard');
      } else {
        // Default redirect
        navigate('/dashboard');
      }
    }
  }, [isAuthenticated, user, navigate]);

  // Handle error state changes
  useEffect(() => {
    if (error) {
      console.log('Auth context error detected:', error);

      // Check if error indicates account is locked
      if (error.includes('locked') || error.includes('Account is locked')) {
        setIsLocked(true);
        setLockoutMessage(error);
        setFormError(error);
        setLoading(false);
        return;
      }

      // Check if the error message contains attempt information
      const attemptMatch = error.match(/\(Attempt (\d+) of (\d+)\)/);
      if (attemptMatch && attemptMatch[1] && attemptMatch[2]) {
        // Use the server-reported attempt number and max attempts
        const serverReportedAttempt = parseInt(attemptMatch[1], 10);
        const serverMaxAttempts = parseInt(attemptMatch[2], 10);
        console.log(`Server reported attempt: ${serverReportedAttempt} of ${serverMaxAttempts}`);
        setLoginAttempts(serverReportedAttempt);
        setMaxAttempts(serverMaxAttempts);

        // Set a simplified error message without the attempt information
        setFormError('Invalid credentials');
      } else {
        // For other errors, show the full error message
        setFormError(error);
      }

      setLoading(false);
    }

    // Cleanup error when component unmounts
    return () => {
      clearError();
    };
  }, [error, clearError]);

  const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
    setFormError(null);
    clearError();
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (isLocked) {
      setFormError(lockoutMessage || 'Account is locked. Please contact an administrator.');
      return;
    }

    setLoading(true);
    setFormError(null);

    if (!email || !password) {
      setFormError('Please enter both email and password');
      setLoading(false);
      return;
    }

    console.log('Submitting login form with email/username:', email);
    try {
      const loginResult = await login(email, password);
      console.log('Login successful, redirecting via useEffect...');

      // Reset login attempts on successful login
      setLoginAttempts(0);

      // No need to manually redirect here; useEffect will handle redirects based on role
    } catch (err: any) {
      console.error('Login error caught in Login component:', err);

      // Make sure loading state is reset
      setLoading(false);
    }
  };

  return (
    <LoginContainer className="login-container">
      <LoginCard className="login-card">
        {/* Left side - Image and branding */}
        <ImageSection className="login-image-section">
          <Avatar
            sx={{
              width: 80,
              height: 80,
              bgcolor: '#2C4B2B', // Dark Green from logo
              mb: 3,
              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)'
            }}
          >
            <HealthIcon sx={{ fontSize: 40, color: 'white' }} />
          </Avatar>
          <Typography
            variant="h4"
            component="h1"
            fontWeight="700"
            gutterBottom
            sx={{
              color: theme.palette.mode === 'dark' ? '#4CAF50' : '#2C4B2B',
              letterSpacing: '0.5px',
              textShadow: theme.palette.mode === 'dark'
                ? '0 2px 4px rgba(0,0,0,0.2)'
                : '0 2px 4px rgba(0,0,0,0.05)'
            }}
          >
            MedApp
          </Typography>
          <Typography
            variant="body1"
            sx={{
              mb: 4,
              textAlign: 'center',
              color: theme.palette.mode === 'dark' ? '#aaa' : '#555',
              maxWidth: '80%',
              lineHeight: 1.6
            }}
          >
            Secure, modern healthcare management platform for medical professionals
          </Typography>

          {/* Decorative elements - removed wave pattern that was causing the black bar */}
        </ImageSection>

        {/* Right side - Login form */}
        <FormSection className="login-form-section">
          <Box
            className="login-logo-container"
            sx={{
              display: 'flex',
              alignItems: 'center',
              marginBottom: 4,
              backgroundColor: 'transparent !important'
            }}
          >
            <Avatar
              sx={{
                bgcolor: '#D97B3A', // Orange from logo
                width: 40,
                height: 40,
                mr: 2,
                boxShadow: '0 2px 8px rgba(217, 123, 58, 0.3)'
              }}
            >
              <MedicalIcon />
            </Avatar>
            <Typography
              variant="h5"
              component="h2"
              fontWeight="600"
              className="login-heading"
              sx={{
                backgroundColor: 'transparent !important',
                padding: '0',
                color: theme.palette.mode === 'dark' ? '#ffffff !important' : 'inherit'
              }}
            >
              Sign In
            </Typography>
          </Box>

          <Typography
            variant="body2"
            color="text.secondary"
            className="login-credentials-text"
            sx={{
              mb: 4,
              color: theme.palette.mode === 'dark' ? '#aaaaaa !important' : 'inherit'
            }}
          >
            Enter your credentials to access your account
          </Typography>

          {formError && (
            <Alert
              severity="error"
              sx={{
                mb: 3,
                borderRadius: 2,
                '& .MuiAlert-icon': { alignItems: 'center' }
              }}
            >
              {formError}
            </Alert>
          )}

          {loginAttempts > 0 && loginAttempts < maxAttempts && (
            <Alert
              severity="warning"
              sx={{
                mb: 3,
                borderRadius: 2,
                '& .MuiAlert-icon': { alignItems: 'center' }
              }}
              key={`attempts-${loginAttempts}-${Date.now()}`}
            >
              <div>
                <Typography variant="body2" fontWeight="500">
                  Failed login attempts: {loginAttempts} of {maxAttempts}
                </Typography>
                <Typography variant="body2" sx={{ mt: 0.5 }}>
                  You have {maxAttempts - loginAttempts} attempts remaining before your account is locked.
                </Typography>
              </div>
            </Alert>
          )}

          {isLocked && (
            <Alert
              severity="error"
              sx={{
                mb: 3,
                borderRadius: 2,
                '& .MuiAlert-icon': { alignItems: 'center' }
              }}
            >
              <div>
                <Typography variant="body2" fontWeight="500">
                  Account Locked
                </Typography>
                <Typography variant="body2" sx={{ mt: 0.5 }}>
                  {lockoutMessage || 'Your account has been locked due to too many failed login attempts. Please contact an administrator to unlock your account.'}
                </Typography>
              </div>
            </Alert>
          )}

          <form onSubmit={onSubmit}>
            <StyledTextField
              fullWidth
              label="Email or Username"
              name="email"
              value={email}
              onChange={onChange}
              required
              disabled={loading || isLocked}
              error={!!formError}
              placeholder="Enter your email or username"
              variant="outlined"
              InputProps={{
                sx: { height: 56 }
              }}
            />

            <StyledTextField
              fullWidth
              label="Password"
              name="password"
              type={showPassword ? "text" : "password"}
              value={password}
              onChange={onChange}
              required
              disabled={loading || isLocked}
              error={!!formError}
              variant="outlined"
              InputProps={{
                sx: { height: 56 },
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={togglePasswordVisibility}
                      edge="end"
                      disabled={loading || isLocked}
                      sx={{ color: theme.palette.text.secondary }}
                    >
                      {showPassword ? <VisibilityOff /> : <Visibility />}
                    </IconButton>
                  </InputAdornment>
                )
              }}
            />

            {/* Custom button implementation to ensure text visibility */}
            <div
              style={{
                marginTop: '8px',
                marginBottom: '24px',
                width: '100%'
              }}
            >
              <button
                type="submit"
                disabled={loading || isLocked}
                style={{
                  width: '100%',
                  height: '56px',
                  borderRadius: '8px',
                  backgroundColor: '#D97B3A',
                  color: '#FFFFFF',
                  border: 'none',
                  fontSize: '1rem',
                  fontWeight: 600,
                  cursor: loading || isLocked ? 'not-allowed' : 'pointer',
                  boxShadow: '0 4px 12px rgba(217, 123, 58, 0.3)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  opacity: loading || isLocked ? 0.7 : 1
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.backgroundColor = '#F2A65A';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.backgroundColor = '#D97B3A';
                }}
              >
                {loading ?
                  <span style={{display: 'flex', alignItems: 'center', gap: '8px'}}>
                    <i className="fas fa-circle-notch fa-spin" style={{color: 'white'}}></i>
                    <span style={{color: 'white', fontWeight: 600}}>Signing In...</span>
                  </span> :
                  <span style={{color: 'white', fontWeight: 600}}>Sign In</span>
                }
              </button>
            </div>

            <Box sx={{
              textAlign: 'center',
              backgroundColor: theme.palette.mode === 'dark' ? '#1e1e1e !important' : 'transparent !important',
              position: 'relative',
              zIndex: 1,
              padding: '8px',
              marginTop: '8px'
            }}>
              <Typography
                variant="body2"
                color="text.secondary"
                className="login-help-text"
                sx={{
                  backgroundColor: theme.palette.mode === 'dark' ? '#1e1e1e !important' : 'transparent !important',
                  position: 'relative',
                  zIndex: 2,
                  padding: '8px 16px',
                  borderRadius: '4px'
                }}
              >
                Having trouble signing in? Contact your system administrator.
              </Typography>
            </Box>
          </form>
        </FormSection>
      </LoginCard>
    </LoginContainer>
  );
};

export default Login;