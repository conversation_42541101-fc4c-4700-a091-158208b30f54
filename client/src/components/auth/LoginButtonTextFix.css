/* Fix for the Sign In button text color in both light and dark modes */

/* Ensure text is white in both light and dark modes - with extremely high specificity */
.login-submit-button,
.login-form-section form button[type="submit"],
.login-form-section form .login-submit-button,
.login-form-section form .MuiButton-root,
button.login-submit-button,
button[type="submit"].MuiButton-root,
.MuiButton-root.login-submit-button,
.MuiButton-contained.login-submit-button,
.MuiButton-containedPrimary.login-submit-button {
  color: #ffffff !important;
}

/* Target the button text directly */
.login-submit-button span,
.login-form-section form button[type="submit"] span,
.login-form-section form .login-submit-button span,
.login-form-section form .MuiButton-root span,
.login-submit-button .MuiButton-label,
.login-form-section form button[type="submit"] .MuiButton-label,
.login-form-section form .login-submit-button .MuiButton-label,
.login-form-section form .MuiButton-root .MuiButton-label {
  color: #ffffff !important;
}

/* Dark mode specific styles (already implemented, but included for completeness) */
body.dark-mode .login-form-section form button[type="submit"],
body.dark-mode .login-form-section form .login-submit-button,
body.dark-mode .login-form-section form .MuiButton-root,
html.dark-mode .login-form-section form button[type="submit"] {
  color: #ffffff !important;
}

/* Light mode specific styles with higher specificity */
body:not(.dark-mode) .login-form-section form button[type="submit"],
body:not(.dark-mode) .login-form-section form .login-submit-button,
body:not(.dark-mode) .login-form-section form .MuiButton-root,
html:not(.dark-mode) .login-form-section form button[type="submit"],
body:not(.dark-mode) button.login-submit-button,
html:not(.dark-mode) button.login-submit-button,
body:not(.dark-mode) button[type="submit"].MuiButton-root,
html:not(.dark-mode) button[type="submit"].MuiButton-root {
  color: #ffffff !important;
}

/* Direct style override for the button text */
.login-form-section form button[type="submit"]::before,
.login-form-section form .login-submit-button::before,
.login-form-section form .MuiButton-root::before {
  content: "";
  display: none !important;
}

/* Inline style override */
[style*="color"] .login-submit-button,
[style*="color"] .login-form-section form button[type="submit"],
[style*="color"] .login-form-section form .login-submit-button,
[style*="color"] .login-form-section form .MuiButton-root {
  color: #ffffff !important;
}
