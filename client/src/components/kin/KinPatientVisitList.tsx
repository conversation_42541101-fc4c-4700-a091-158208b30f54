import React, { useEffect, useState, useContext } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { getPatientVisits } from '../../services/visitService';
import { getPatientById } from '../../services/patientService';
import { Visit, Patient } from '../../types';
import AuthContext from '../../context/AuthContext';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Container,
  Divider,
  Grid,
  IconButton,
  Paper,
  Tooltip,
  Typography,
  useTheme
} from '@mui/material';
import {
  Add as AddIcon,
  ArrowBack as ArrowBackIcon,
  CalendarMonth as CalendarMonthIcon,
  MedicalInformation as MedicalInformationIcon,
  MonitorHeart as MonitorHeartIcon,
  Science as ScienceIcon,
  Medication as MedicationIcon,
  Psychology as PsychologyIcon,
  HealthAndSafety as HealthAndSafetyIcon,
  ArrowForward as ArrowForwardIcon
} from '@mui/icons-material';

const KinPatientVisitList: React.FC = () => {
  const { patientId } = useParams<{ patientId: string }>();
  const navigate = useNavigate();
  const theme = useTheme();
  const { user } = useContext(AuthContext);

  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [visits, setVisits] = useState<Visit[]>([]);
  const [patient, setPatient] = useState<Patient | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        if (!patientId) {
          throw new Error('Patient ID is required');
        }

        // Fetch patient data
        const patientData = await getPatientById(parseInt(patientId));
        setPatient(patientData);

        // Fetch visits for this patient
        const visitsData = await getPatientVisits(parseInt(patientId));

        // Sort visits by date (most recent first)
        const sortedVisits = visitsData.sort((a, b) =>
          new Date(b.visit_date).getTime() - new Date(a.visit_date).getTime()
        );

        setVisits(sortedVisits);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [patientId]);

  // Format date to dd/mm/yyyy
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  // Get a summary of vital signs
  const getVitalSignsSummary = (visit: Visit) => {
    const hasBP = visit.lying_bp_systolic && visit.lying_bp_diastolic;
    const hasHeartRate = visit.lying_heart_rate;
    const hasOxygen = visit.pulse_oximetry;

    if (!hasBP && !hasHeartRate && !hasOxygen) {
      return null;
    }

    return (
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
        {hasBP && (
          <Chip
            size="small"
            icon={<MonitorHeartIcon fontSize="small" />}
            label={`BP: ${visit.lying_bp_systolic}/${visit.lying_bp_diastolic}`}
            sx={{ bgcolor: 'rgba(25, 118, 210, 0.08)' }}
          />
        )}
        {hasHeartRate && (
          <Chip
            size="small"
            icon={<MonitorHeartIcon fontSize="small" />}
            label={`HR: ${visit.lying_heart_rate}`}
            sx={{ bgcolor: 'rgba(25, 118, 210, 0.08)' }}
          />
        )}
        {hasOxygen && (
          <Chip
            size="small"
            icon={<MonitorHeartIcon fontSize="small" />}
            label={`O₂: ${visit.pulse_oximetry}%`}
            sx={{ bgcolor: 'rgba(25, 118, 210, 0.08)' }}
          />
        )}
      </Box>
    );
  };

  // Get a summary of lab results
  const getLabResultsSummary = (visit: Visit) => {
    const hasGlucose = visit.blood_glucose;
    const hasHbA1c = visit.hba1c;
    const hasLipids = visit.cholesterol_total || visit.ldl_cholesterol || visit.hdl_cholesterol;

    if (!hasGlucose && !hasHbA1c && !hasLipids) {
      return null;
    }

    return (
      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
        {hasGlucose && (
          <Chip
            size="small"
            icon={<ScienceIcon fontSize="small" />}
            label={`Glucose: ${visit.blood_glucose}`}
            sx={{ bgcolor: 'rgba(76, 175, 80, 0.08)' }}
          />
        )}
        {hasHbA1c && (
          <Chip
            size="small"
            icon={<ScienceIcon fontSize="small" />}
            label={`HbA1c: ${visit.hba1c}%`}
            sx={{ bgcolor: 'rgba(76, 175, 80, 0.08)' }}
          />
        )}
        {hasLipids && (
          <Chip
            size="small"
            icon={<ScienceIcon fontSize="small" />}
            label="Lipids"
            sx={{ bgcolor: 'rgba(76, 175, 80, 0.08)' }}
          />
        )}
      </Box>
    );
  };

  // Get orthostatic hypotension assessment
  const getOrthostaticAssessment = (visit: Visit) => {
    if (!(visit.lying_bp_systolic && visit.standing_bp_systolic &&
         visit.lying_bp_diastolic && visit.standing_bp_diastolic &&
         visit.lying_heart_rate && visit.standing_heart_rate)) {
      return null;
    }

    const systolicDiff = visit.lying_bp_systolic - visit.standing_bp_systolic;
    const diastolicDiff = visit.lying_bp_diastolic - visit.standing_bp_diastolic;
    const heartRateDiff = visit.standing_heart_rate - visit.lying_heart_rate;

    const hasOrthostaticHypotension = systolicDiff >= 20 || diastolicDiff >= 10;
    const hasCompensatoryTachycardia = heartRateDiff >= 20;

    let chipColor = 'success';
    let label = 'No Orthostatic Hypotension';

    if (hasOrthostaticHypotension) {
      chipColor = 'error';
      label = 'Orthostatic Hypotension';
    } else if (hasCompensatoryTachycardia) {
      chipColor = 'warning';
      label = 'Compensatory Tachycardia';
    }

    return (
      <Chip
        size="small"
        color={chipColor as any}
        icon={<MonitorHeartIcon fontSize="small" />}
        label={label}
        variant="outlined"
      />
    );
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="80vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ mt: 4, mb: 4 }}>
          <Paper elevation={2} sx={{ p: 3, bgcolor: 'error.light', color: 'error.contrastText' }}>
            <Typography variant="h6">Error</Typography>
            <Typography variant="body1">{error}</Typography>
            <Button
              variant="contained"
              color="primary"
              sx={{ mt: 2 }}
              onClick={() => navigate('/kin/dashboard')}
            >
              Back to Dashboard
            </Button>
          </Paper>
        </Box>
      </Container>
    );
  }

  if (!patient) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ mt: 4, mb: 4 }}>
          <Paper elevation={2} sx={{ p: 3 }}>
            <Typography variant="h6">Patient Not Found</Typography>
            <Button
              variant="contained"
              color="primary"
              sx={{ mt: 2 }}
              onClick={() => navigate('/kin/dashboard')}
            >
              Back to Dashboard
            </Button>
          </Paper>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        {/* Header with patient info and actions */}
        <Paper
          elevation={2}
          sx={{
            p: 3,
            mb: 3,
            borderRadius: 2,
            background: 'linear-gradient(to right, rgba(44, 75, 43, 0.05), rgba(44, 75, 43, 0.01))' // Primary color from theme
          }}
        >
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={7}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Button
                  variant="outlined"
                  startIcon={<ArrowBackIcon />}
                  onClick={() => navigate('/kin/dashboard')}
                  sx={{
                    mr: 2,
                    borderRadius: 2,
                    borderColor: '#2C4B2B',
                    color: '#2C4B2B',
                    '&:hover': {
                      borderColor: '#3E6A3D',
                      backgroundColor: 'rgba(44, 75, 43, 0.04)'
                    }
                  }}
                >
                  Back
                </Button>
                <Box>
                  <Typography variant="h4" component="h1" sx={{ fontWeight: 500 }}>
                    Patient Visits
                  </Typography>
                  <Typography variant="subtitle1" color="text.secondary">
                    {patient.first_name} {patient.last_name}
                    {patient.unique_id && ` • ID: ${patient.unique_id}`}
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Paper>

        {/* Visit list */}
        {visits.length === 0 ? (
          <Paper
            elevation={2}
            sx={{
              p: 4,
              textAlign: 'center',
              borderRadius: 2,
              bgcolor: 'background.paper'
            }}
          >
            <MedicalInformationIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="textSecondary">
              No visits recorded yet
            </Typography>
            <Typography variant="body1" color="textSecondary" sx={{ mb: 3 }}>
              This patient doesn't have any recorded visits yet.
            </Typography>
            <Button
              variant="contained"
              color="primary"
              onClick={() => navigate('/kin/dashboard')}
              sx={{
                borderRadius: 2,
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                px: 3
              }}
            >
              Back to Dashboard
            </Button>
          </Paper>
        ) : (
          <Grid container spacing={2}>
            {visits.map((visit) => (
              <Grid item xs={12} key={visit.visit_id}>
                <Card
                  sx={{
                    borderRadius: 2,
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
                    transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-3px)',
                      boxShadow: '0 6px 16px rgba(0, 0, 0, 0.12)',
                      cursor: 'pointer'
                    }
                  }}
                  onClick={() => navigate(`/kin/patients/${patientId}/visits/${visit.visit_id}`)}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Grid container spacing={2}>
                      {/* Visit date and doctor */}
                      <Grid item xs={12} sm={3}>
                        <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <CalendarMonthIcon sx={{ mr: 1, fontSize: 20, color: '#2C4B2B' }} />
                            <Typography variant="h6" sx={{ fontWeight: 500 }}>
                              {formatDate(visit.visit_date)}
                            </Typography>
                          </Box>
                          {visit.doctor_first_name && visit.doctor_last_name && (
                            <Typography variant="body2" color="text.secondary">
                              Dr. {visit.doctor_first_name} {visit.doctor_last_name}
                            </Typography>
                          )}
                        </Box>
                      </Grid>

                      {/* Visit reason */}
                      <Grid item xs={12} sm={5}>
                        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                          Reason for Visit
                        </Typography>
                        <Typography variant="body1" sx={{
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                        }}>
                          {visit.visit_reason || 'No reason specified'}
                        </Typography>
                      </Grid>

                      {/* Key metrics */}
                      <Grid item xs={12} sm={3}>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                          {getVitalSignsSummary(visit)}
                          {getLabResultsSummary(visit)}
                          {getOrthostaticAssessment(visit)}
                        </Box>
                      </Grid>

                      {/* View button */}
                      <Grid item xs={12} sm={1} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                        <Tooltip title="View details">
                          <IconButton
                            color="primary"
                            size="small"
                            sx={{
                              bgcolor: 'rgba(25, 118, 210, 0.08)',
                              '&:hover': {
                                bgcolor: 'rgba(25, 118, 210, 0.16)'
                              }
                            }}
                          >
                            <ArrowForwardIcon />
                          </IconButton>
                        </Tooltip>
                      </Grid>
                    </Grid>

                    {/* Diagnosis section if available */}
                    {visit.diagnosis && (
                      <>
                        <Divider sx={{ my: 2 }} />
                        <Box>
                          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                            Diagnosis
                          </Typography>
                          <Typography variant="body2" sx={{
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitLineClamp: 1,
                            WebkitBoxOrient: 'vertical',
                          }}>
                            {visit.diagnosis}
                          </Typography>
                        </Box>
                      </>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
      </Box>
    </Container>
  );
};

export default KinPatientVisitList;
