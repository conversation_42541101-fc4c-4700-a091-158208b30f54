import React, { useEffect, useState } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { getPatientVisits } from '../../services/visitService';
import { getPatientById } from '../../services/patientService';
import { Visit, Patient } from '../../types/index';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Divider,
  Grid,
  Paper,
  Typography,
  useTheme,
  alpha,
  List,
  ListItem,
  ListItemIcon,
  ListItemText
} from '@mui/material';
import LoadingSpinner from '../common/LoadingSpinner';
import {
  ArrowBack as ArrowBackIcon,
  Medication as MedicationIcon,
  LocalHospital as LocalHospitalIcon,
  CalendarToday as CalendarTodayIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon
} from '@mui/icons-material';

const KinMedicationList: React.FC = () => {
  const { patientId } = useParams<{ patientId: string }>();
  const navigate = useNavigate();
  const theme = useTheme();
  const location = useLocation();

  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [patient, setPatient] = useState<Patient | null>(null);
  const [medications, setMedications] = useState<{
    visit: Visit,
    medications: string[],
    date: string,
    doctor: string | null
  }[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Get patient ID from URL params or query string
        let id = patientId;
        if (!id) {
          const params = new URLSearchParams(location.search);
          const patientParam = params.get('patient');
          id = patientParam || undefined;
        }

        if (!id) {
          throw new Error('Patient ID is required');
        }

        // Fetch patient data
        const patientData = await getPatientById(parseInt(id));
        setPatient(patientData);

        // Fetch visits for this patient
        const visitsData = await getPatientVisits(parseInt(id));

        // Sort visits by date (most recent first)
        const sortedVisits = visitsData.sort((a, b) =>
          new Date(b.visit_date).getTime() - new Date(a.visit_date).getTime()
        );

        // Extract medications from visits
        const medicationsList = sortedVisits
          .filter(visit => visit.current_medications || visit.medication_changes)
          .map(visit => {
            // Extract medications from current_medications field
            const currentMeds = visit.current_medications
              ? visit.current_medications.split(/[,;\\n]/).map(med => med.trim()).filter(med => med)
              : [];

            // Extract medications from medication_changes field
            const medChanges = visit.medication_changes
              ? visit.medication_changes.split(/[,;\\n]/).map(med => med.trim()).filter(med => med)
              : [];

            // Combine both lists
            const allMeds = [...currentMeds, ...medChanges];

            return {
              visit,
              medications: allMeds,
              date: formatDate(visit.visit_date),
              doctor: visit.doctor_first_name && visit.doctor_last_name
                ? `Dr. ${visit.doctor_first_name} ${visit.doctor_last_name}`
                : null
            };
          });

        setMedications(medicationsList);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [patientId, location.search]);

  // Format date to dd/mm/yyyy
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  if (loading) {
    return <LoadingSpinner size="large" message="Loading medication list..." />;
  }

  if (error) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ mt: 4, mb: 4 }}>
          <Paper elevation={2} sx={{ p: 3, bgcolor: 'error.light', color: 'error.contrastText' }}>
            <Typography variant="h6">Error</Typography>
            <Typography variant="body1">{error}</Typography>
            <Button
              variant="contained"
              color="primary"
              sx={{ mt: 2 }}
              onClick={() => navigate('/kin/dashboard')}
            >
              Back to Dashboard
            </Button>
          </Paper>
        </Box>
      </Container>
    );
  }

  if (!patient) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ mt: 4, mb: 4 }}>
          <Paper elevation={2} sx={{ p: 3 }}>
            <Typography variant="h6">Patient Not Found</Typography>
            <Button
              variant="contained"
              color="primary"
              sx={{ mt: 2 }}
              onClick={() => navigate('/kin/dashboard')}
            >
              Back to Dashboard
            </Button>
          </Paper>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        {/* Header with patient info and actions */}
        <Paper
          elevation={2}
          sx={{
            p: 3,
            mb: 3,
            borderRadius: 2,
            background: 'linear-gradient(to right, rgba(25, 118, 210, 0.05), rgba(25, 118, 210, 0.01))'
          }}
        >
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={7}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Button
                  variant="outlined"
                  startIcon={<ArrowBackIcon />}
                  onClick={() => navigate('/kin/dashboard')}
                  sx={{ mr: 2, borderRadius: 2 }}
                >
                  Back
                </Button>
                <Box>
                  <Typography variant="h4" component="h1" sx={{ fontWeight: 500 }}>
                    Medication List
                  </Typography>
                  <Typography variant="subtitle1" color="text.secondary">
                    {patient.first_name} {patient.last_name}
                    {patient.unique_id && ` • ID: ${patient.unique_id}`}
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Paper>

        {/* Medication list */}
        {medications.length === 0 ? (
          <Paper
            elevation={2}
            sx={{
              p: 4,
              textAlign: 'center',
              borderRadius: 2,
              bgcolor: 'background.paper'
            }}
          >
            <MedicationIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="textSecondary">
              No medications recorded
            </Typography>
            <Typography variant="body1" color="textSecondary" sx={{ mb: 3 }}>
              There are no medications recorded for this patient yet.
            </Typography>
            <Button
              variant="contained"
              color="primary"
              onClick={() => navigate('/kin/dashboard')}
              sx={{
                borderRadius: 2,
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                px: 3
              }}
            >
              Back to Dashboard
            </Button>
          </Paper>
        ) : (
          <Grid container spacing={3}>
            {medications.map((item, index) => (
              <Grid item xs={12} key={index}>
                <Card
                  sx={{
                    borderRadius: 2,
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.08)',
                    overflow: 'visible'
                  }}
                >
                  <CardContent sx={{ p: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                      <Box>
                        <Typography variant="h6" fontWeight="500">
                          Medications from {item.date}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 1 }}>
                          {item.doctor && (
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>
                              <LocalHospitalIcon fontSize="small" sx={{ mr: 0.5, color: 'text.secondary' }} />
                              <Typography variant="body2" color="text.secondary">
                                {item.doctor}
                              </Typography>
                            </Box>
                          )}
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <CalendarTodayIcon fontSize="small" sx={{ mr: 0.5, color: 'text.secondary' }} />
                            <Typography variant="body2" color="text.secondary">
                              {item.date}
                            </Typography>
                          </Box>
                        </Box>
                      </Box>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => navigate(`/kin/patients/${patientId}/visits/${item.visit.visit_id}`)}
                        sx={{ borderRadius: 2 }}
                      >
                        View Visit Details
                      </Button>
                    </Box>

                    <Divider sx={{ my: 2 }} />

                    <List sx={{ py: 0 }}>
                      {item.medications.map((med, medIndex) => (
                        <ListItem
                          key={medIndex}
                          sx={{
                            py: 1,
                            px: 2,
                            borderRadius: 1,
                            mb: 1,
                            bgcolor: alpha(theme.palette.primary.main, 0.04),
                            '&:hover': {
                              bgcolor: alpha(theme.palette.primary.main, 0.08),
                            }
                          }}
                        >
                          <ListItemIcon sx={{ minWidth: 40 }}>
                            <MedicationIcon color="primary" />
                          </ListItemIcon>
                          <ListItemText
                            primary={med}
                          />
                        </ListItem>
                      ))}
                    </List>

                    {item.visit.medication_changes && (
                      <Box sx={{ mt: 2 }}>
                        <Typography variant="subtitle2" color="text.secondary" fontWeight="500" gutterBottom>
                          Medication Changes
                        </Typography>
                        <Paper
                          elevation={0}
                          sx={{
                            p: 2,
                            bgcolor: alpha(theme.palette.warning.main, 0.05),
                            borderRadius: 2,
                            border: `1px solid ${alpha(theme.palette.warning.main, 0.2)}`
                          }}
                        >
                          <Typography variant="body2">
                            {item.visit.medication_changes}
                          </Typography>
                        </Paper>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}

        {/* Medication safety information */}
        <Paper
          elevation={2}
          sx={{
            p: 3,
            mt: 4,
            borderRadius: 2,
            bgcolor: alpha(theme.palette.info.main, 0.05),
            border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
          }}
        >
          <Typography variant="h6" color="primary" gutterBottom>
            Medication Safety Information
          </Typography>
          <Typography variant="body1" paragraph>
            This medication list is compiled from your family member's medical visits. Always consult with their doctor before making any changes to their medication regimen.
          </Typography>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                <WarningIcon color="warning" sx={{ mt: 0.5 }} />
                <Typography variant="body2">
                  Never change dosages or stop medications without consulting a healthcare provider.
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                <CheckCircleIcon color="success" sx={{ mt: 0.5 }} />
                <Typography variant="body2">
                  Keep all medications in their original containers with labels intact.
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                <ErrorIcon color="error" sx={{ mt: 0.5 }} />
                <Typography variant="body2">
                  Store medications away from heat, moisture, and direct sunlight.
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Paper>

        {/* Back button */}
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <Button
            variant="contained"
            color="primary"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/kin/dashboard')}
            sx={{ borderRadius: 2, px: 4 }}
          >
            Back to Dashboard
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

export default KinMedicationList;
