import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { getVisitById } from '../../services/visitService';
import { getPatientById } from '../../services/patientService';
import { Visit, Patient } from '../../types';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Divider,
  Grid,
  Paper,
  Typography,
  useTheme,
  alpha
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  CalendarMonth as CalendarMonthIcon,
  LocalHospital as LocalHospitalIcon,
  MonitorHeart as MonitorHeartIcon,
  Science as ScienceIcon,
  Bloodtype as BloodtypeIcon,
  Thermostat as ThermostatIcon,
  Scale as ScaleIcon,
  Height as HeightIcon,
  Favorite as FavoriteIcon,
  MedicalInformation as MedicalInformationIcon
} from '@mui/icons-material';

const KinVisitDetail: React.FC = () => {
  const { patientId, visitId } = useParams<{ patientId: string; visitId: string }>();
  const navigate = useNavigate();
  const theme = useTheme();

  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [visit, setVisit] = useState<Visit | null>(null);
  const [patient, setPatient] = useState<Patient | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        if (!visitId || !patientId) {
          throw new Error('Visit ID and Patient ID are required');
        }

        // Fetch visit data
        const visitData = await getVisitById(parseInt(visitId));
        setVisit(visitData);

        // Fetch patient data
        const patientData = await getPatientById(parseInt(patientId));
        setPatient(patientData);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err.message : 'An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [visitId, patientId]);

  // Format date to a readable format
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="80vh">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ mt: 4, mb: 4 }}>
          <Paper elevation={2} sx={{ p: 3, bgcolor: 'error.light', color: 'error.contrastText' }}>
            <Typography variant="h6">Error</Typography>
            <Typography variant="body1">{error}</Typography>
            <Button
              variant="contained"
              sx={{
                mt: 2,
                backgroundColor: '#2C4B2B',
                '&:hover': {
                  backgroundColor: '#3E6A3D'
                }
              }}
              onClick={() => navigate('/kin/dashboard')}
            >
              Back to Dashboard
            </Button>
          </Paper>
        </Box>
      </Container>
    );
  }

  if (!visit || !patient) {
    return (
      <Container maxWidth="lg">
        <Box sx={{ mt: 4, mb: 4 }}>
          <Paper elevation={2} sx={{ p: 3 }}>
            <Typography variant="h6">Visit or Patient Not Found</Typography>
            <Button
              variant="contained"
              sx={{
                mt: 2,
                backgroundColor: '#2C4B2B',
                '&:hover': {
                  backgroundColor: '#3E6A3D'
                }
              }}
              onClick={() => navigate('/kin/dashboard')}
            >
              Back to Dashboard
            </Button>
          </Paper>
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        {/* Header with patient info and actions */}
        <Paper
          elevation={2}
          sx={{
            p: 3,
            mb: 3,
            borderRadius: 2,
            background: 'linear-gradient(to right, rgba(44, 75, 43, 0.05), rgba(44, 75, 43, 0.01))' // Primary color from theme
          }}
        >
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={8}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Button
                  variant="outlined"
                  startIcon={<ArrowBackIcon />}
                  onClick={() => navigate(`/kin/patients/${patientId}/visits`)}
                  sx={{
                    mr: 2,
                    borderRadius: 2,
                    borderColor: '#2C4B2B',
                    color: '#2C4B2B',
                    '&:hover': {
                      borderColor: '#3E6A3D',
                      backgroundColor: 'rgba(44, 75, 43, 0.04)'
                    }
                  }}
                >
                  Back to Visits
                </Button>
                <Box>
                  <Typography variant="h4" component="h1" sx={{ fontWeight: 500 }}>
                    Visit Details
                  </Typography>
                  <Typography variant="subtitle1" color="text.secondary">
                    {patient.first_name} {patient.last_name} • {formatDate(visit.visit_date)}
                  </Typography>
                </Box>
              </Box>
            </Grid>
            <Grid item xs={12} sm={4} sx={{ display: 'flex', justifyContent: { xs: 'flex-start', sm: 'flex-end' } }}>
              <Button
                variant="outlined"
                startIcon={<ArrowBackIcon />}
                onClick={() => navigate('/kin/dashboard')}
                sx={{
                  borderRadius: 2,
                  px: 3,
                  borderColor: '#2C4B2B',
                  color: '#2C4B2B',
                  '&:hover': {
                    borderColor: '#3E6A3D',
                    backgroundColor: 'rgba(44, 75, 43, 0.04)'
                  }
                }}
              >
                Back to Dashboard
              </Button>
            </Grid>
          </Grid>
        </Paper>

        {/* Visit Summary Card */}
        <Card sx={{ mb: 4, borderRadius: 2, boxShadow: '0 2px 12px rgba(0, 0, 0, 0.08)' }}>
          <CardContent sx={{ p: 3 }}>
            <Grid container spacing={3}>
              {/* Visit date and doctor */}
              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                  <CalendarMonthIcon sx={{ mr: 2, mt: 0.5, color: '#2C4B2B' }} />
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary">
                      Visit Date
                    </Typography>
                    <Typography variant="h6" sx={{ fontWeight: 500 }}>
                      {formatDate(visit.visit_date)}
                    </Typography>
                  </Box>
                </Box>

                {(visit.doctor_first_name || visit.doctor_last_name) && (
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                    <LocalHospitalIcon sx={{ mr: 2, mt: 0.5, color: '#2C4B2B' }} />
                    <Box>
                      <Typography variant="subtitle2" color="text.secondary">
                        Doctor
                      </Typography>
                      <Typography variant="h6" sx={{ fontWeight: 500 }}>
                        Dr. {visit.doctor_first_name} {visit.doctor_last_name}
                      </Typography>
                    </Box>
                  </Box>
                )}
              </Grid>

              {/* Visit reason and type */}
              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                  <MedicalInformationIcon sx={{ mr: 2, mt: 0.5, color: '#2C4B2B' }} />
                  <Box>
                    <Typography variant="subtitle2" color="text.secondary">
                      Reason for Visit
                    </Typography>
                    <Typography variant="h6" sx={{ fontWeight: 500 }}>
                      {visit.visit_reason || 'Not specified'}
                    </Typography>
                  </Box>
                </Box>

                {/* Visit Type section removed as it's not in the Visit type */}
              </Grid>
            </Grid>

            {/* Diagnosis section */}
            {visit.diagnosis && (
              <>
                <Divider sx={{ my: 3 }} />
                <Box sx={{ mb: 3 }}>
                  <Typography variant="h6" sx={{ color: '#2C4B2B' }} gutterBottom>
                    Diagnosis
                  </Typography>
                  <Typography variant="body1" sx={{ whiteSpace: 'pre-line' }}>
                    {visit.diagnosis}
                  </Typography>
                </Box>
              </>
            )}

            {/* Treatment plan section */}
            {visit.treatment_plan && (
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ color: '#2C4B2B' }} gutterBottom>
                  Treatment Plan
                </Typography>
                <Typography variant="body1" sx={{ whiteSpace: 'pre-line' }}>
                  {visit.treatment_plan}
                </Typography>
              </Box>
            )}
          </CardContent>
        </Card>

        {/* Vital Signs Card */}
        <Card sx={{ mb: 4, borderRadius: 2, boxShadow: '0 2px 12px rgba(0, 0, 0, 0.08)' }}>
          <CardContent sx={{ p: 3 }}>
            <Typography variant="h5" gutterBottom sx={{ fontWeight: 500, color: '#2C4B2B' }}>
              Vital Signs
            </Typography>
            <Divider sx={{ mb: 3 }} />

            <Grid container spacing={3}>
              {/* Blood Pressure */}
              {(visit.lying_bp_systolic && visit.lying_bp_diastolic) && (
                <Grid item xs={12} sm={6} md={4}>
                  <Box sx={{
                    p: 2,
                    borderRadius: 2,
                    bgcolor: 'rgba(44, 75, 43, 0.05)', // Primary color from theme with opacity
                    height: '100%'
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <MonitorHeartIcon sx={{ mr: 1, color: '#2C4B2B' }} />
                      <Typography variant="subtitle1" fontWeight="500">
                        Blood Pressure (Lying)
                      </Typography>
                    </Box>
                    <Typography variant="h5" sx={{ fontWeight: 500 }}>
                      {visit.lying_bp_systolic}/{visit.lying_bp_diastolic} mmHg
                    </Typography>
                  </Box>
                </Grid>
              )}

              {/* Standing Blood Pressure */}
              {(visit.standing_bp_systolic && visit.standing_bp_diastolic) && (
                <Grid item xs={12} sm={6} md={4}>
                  <Box sx={{
                    p: 2,
                    borderRadius: 2,
                    bgcolor: alpha(theme.palette.primary.main, 0.05),
                    height: '100%'
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <MonitorHeartIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="subtitle1" fontWeight="500">
                        Blood Pressure (Standing)
                      </Typography>
                    </Box>
                    <Typography variant="h5" sx={{ fontWeight: 500 }}>
                      {visit.standing_bp_systolic}/{visit.standing_bp_diastolic} mmHg
                    </Typography>
                  </Box>
                </Grid>
              )}

              {/* Heart Rate */}
              {visit.lying_heart_rate && (
                <Grid item xs={12} sm={6} md={4}>
                  <Box sx={{
                    p: 2,
                    borderRadius: 2,
                    bgcolor: alpha(theme.palette.primary.main, 0.05),
                    height: '100%'
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <FavoriteIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="subtitle1" fontWeight="500">
                        Heart Rate (Lying)
                      </Typography>
                    </Box>
                    <Typography variant="h5" sx={{ fontWeight: 500 }}>
                      {visit.lying_heart_rate} bpm
                    </Typography>
                  </Box>
                </Grid>
              )}

              {/* Standing Heart Rate */}
              {visit.standing_heart_rate && (
                <Grid item xs={12} sm={6} md={4}>
                  <Box sx={{
                    p: 2,
                    borderRadius: 2,
                    bgcolor: alpha(theme.palette.primary.main, 0.05),
                    height: '100%'
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <FavoriteIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="subtitle1" fontWeight="500">
                        Heart Rate (Standing)
                      </Typography>
                    </Box>
                    <Typography variant="h5" sx={{ fontWeight: 500 }}>
                      {visit.standing_heart_rate} bpm
                    </Typography>
                  </Box>
                </Grid>
              )}

              {/* Temperature */}
              {visit.temperature && (
                <Grid item xs={12} sm={6} md={4}>
                  <Box sx={{
                    p: 2,
                    borderRadius: 2,
                    bgcolor: alpha(theme.palette.primary.main, 0.05),
                    height: '100%'
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <ThermostatIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="subtitle1" fontWeight="500">
                        Temperature
                      </Typography>
                    </Box>
                    <Typography variant="h5" sx={{ fontWeight: 500 }}>
                      {visit.temperature} °C
                    </Typography>
                  </Box>
                </Grid>
              )}

              {/* Weight */}
              {visit.weight && (
                <Grid item xs={12} sm={6} md={4}>
                  <Box sx={{
                    p: 2,
                    borderRadius: 2,
                    bgcolor: alpha(theme.palette.primary.main, 0.05),
                    height: '100%'
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <ScaleIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="subtitle1" fontWeight="500">
                        Weight
                      </Typography>
                    </Box>
                    <Typography variant="h5" sx={{ fontWeight: 500 }}>
                      {visit.weight} kg
                    </Typography>
                  </Box>
                </Grid>
              )}

              {/* BMI */}
              {visit.bmi && (
                <Grid item xs={12} sm={6} md={4}>
                  <Box sx={{
                    p: 2,
                    borderRadius: 2,
                    bgcolor: alpha(theme.palette.primary.main, 0.05),
                    height: '100%'
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <HeightIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="subtitle1" fontWeight="500">
                        BMI
                      </Typography>
                    </Box>
                    <Typography variant="h5" sx={{ fontWeight: 500 }}>
                      {visit.bmi} kg/m²
                    </Typography>
                  </Box>
                </Grid>
              )}

              {/* Blood Glucose */}
              {visit.blood_glucose && (
                <Grid item xs={12} sm={6} md={4}>
                  <Box sx={{
                    p: 2,
                    borderRadius: 2,
                    bgcolor: alpha(theme.palette.primary.main, 0.05),
                    height: '100%'
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <BloodtypeIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="subtitle1" fontWeight="500">
                        Blood Glucose
                      </Typography>
                    </Box>
                    <Typography variant="h5" sx={{ fontWeight: 500 }}>
                      {visit.blood_glucose} mg/dL
                    </Typography>
                  </Box>
                </Grid>
              )}

              {/* Pulse Oximetry */}
              {visit.pulse_oximetry && (
                <Grid item xs={12} sm={6} md={4}>
                  <Box sx={{
                    p: 2,
                    borderRadius: 2,
                    bgcolor: alpha(theme.palette.primary.main, 0.05),
                    height: '100%'
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <MonitorHeartIcon color="primary" sx={{ mr: 1 }} />
                      <Typography variant="subtitle1" fontWeight="500">
                        Oxygen Saturation
                      </Typography>
                    </Box>
                    <Typography variant="h5" sx={{ fontWeight: 500 }}>
                      {visit.pulse_oximetry}%
                    </Typography>
                  </Box>
                </Grid>
              )}
            </Grid>
          </CardContent>
        </Card>

        {/* Lab Results Card */}
        {(visit.cholesterol_total || visit.ldl_cholesterol || visit.hdl_cholesterol || visit.hba1c) && (
          <Card sx={{ mb: 4, borderRadius: 2, boxShadow: '0 2px 12px rgba(0, 0, 0, 0.08)' }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h5" gutterBottom sx={{ fontWeight: 500, color: '#2C4B2B' }}>
                Lab Results
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <Grid container spacing={3}>
                {/* HbA1c */}
                {visit.hba1c && (
                  <Grid item xs={12} sm={6} md={4}>
                    <Box sx={{
                      p: 2,
                      borderRadius: 2,
                      bgcolor: alpha(theme.palette.success.main, 0.05),
                      height: '100%'
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <ScienceIcon color="success" sx={{ mr: 1 }} />
                        <Typography variant="subtitle1" fontWeight="500">
                          HbA1c
                        </Typography>
                      </Box>
                      <Typography variant="h5" sx={{ fontWeight: 500 }}>
                        {visit.hba1c}%
                      </Typography>
                    </Box>
                  </Grid>
                )}

                {/* Total Cholesterol */}
                {visit.cholesterol_total && (
                  <Grid item xs={12} sm={6} md={4}>
                    <Box sx={{
                      p: 2,
                      borderRadius: 2,
                      bgcolor: alpha(theme.palette.success.main, 0.05),
                      height: '100%'
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <ScienceIcon color="success" sx={{ mr: 1 }} />
                        <Typography variant="subtitle1" fontWeight="500">
                          Total Cholesterol
                        </Typography>
                      </Box>
                      <Typography variant="h5" sx={{ fontWeight: 500 }}>
                        {visit.cholesterol_total} mg/dL
                      </Typography>
                    </Box>
                  </Grid>
                )}

                {/* LDL Cholesterol */}
                {visit.ldl_cholesterol && (
                  <Grid item xs={12} sm={6} md={4}>
                    <Box sx={{
                      p: 2,
                      borderRadius: 2,
                      bgcolor: alpha(theme.palette.success.main, 0.05),
                      height: '100%'
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <ScienceIcon color="success" sx={{ mr: 1 }} />
                        <Typography variant="subtitle1" fontWeight="500">
                          LDL Cholesterol
                        </Typography>
                      </Box>
                      <Typography variant="h5" sx={{ fontWeight: 500 }}>
                        {visit.ldl_cholesterol} mg/dL
                      </Typography>
                    </Box>
                  </Grid>
                )}

                {/* HDL Cholesterol */}
                {visit.hdl_cholesterol && (
                  <Grid item xs={12} sm={6} md={4}>
                    <Box sx={{
                      p: 2,
                      borderRadius: 2,
                      bgcolor: alpha(theme.palette.success.main, 0.05),
                      height: '100%'
                    }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <ScienceIcon color="success" sx={{ mr: 1 }} />
                        <Typography variant="subtitle1" fontWeight="500">
                          HDL Cholesterol
                        </Typography>
                      </Box>
                      <Typography variant="h5" sx={{ fontWeight: 500 }}>
                        {visit.hdl_cholesterol} mg/dL
                      </Typography>
                    </Box>
                  </Grid>
                )}
              </Grid>
            </CardContent>
          </Card>
        )}

        {/* Medications Section - removed as it's not in the Visit type */}

        {/* Medication Changes Section */}
        {visit.medication_changes && (
          <Card sx={{ mb: 4, borderRadius: 2, boxShadow: '0 2px 12px rgba(0, 0, 0, 0.08)' }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h5" gutterBottom sx={{ fontWeight: 500, color: '#2C4B2B' }}>
                Medication Changes
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <Typography variant="body1" sx={{ whiteSpace: 'pre-line' }}>
                {visit.medication_changes}
              </Typography>
            </CardContent>
          </Card>
        )}

        {/* Notes Section */}
        {visit.notes && (
          <Card sx={{ mb: 4, borderRadius: 2, boxShadow: '0 2px 12px rgba(0, 0, 0, 0.08)' }}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h5" gutterBottom sx={{ fontWeight: 500, color: '#2C4B2B' }}>
                Notes
              </Typography>
              <Divider sx={{ mb: 3 }} />

              <Typography variant="body1" sx={{ whiteSpace: 'pre-line' }}>
                {visit.notes}
              </Typography>
            </CardContent>
          </Card>
        )}

        {/* Back Button */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate(`/kin/patients/${patientId}/visits`)}
            sx={{
              borderRadius: 2,
              borderColor: '#2C4B2B',
              color: '#2C4B2B',
              '&:hover': {
                borderColor: '#3E6A3D',
                backgroundColor: 'rgba(44, 75, 43, 0.04)'
              }
            }}
          >
            Back to Visits
          </Button>
          <Button
            variant="contained"
            onClick={() => navigate('/kin/dashboard')}
            sx={{
              borderRadius: 2,
              backgroundColor: '#2C4B2B',
              '&:hover': {
                backgroundColor: '#3E6A3D'
              }
            }}
          >
            Back to Dashboard
          </Button>
        </Box>
      </Box>
    </Container>
  );
};

export default KinVisitDetail;
