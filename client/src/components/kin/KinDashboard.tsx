import React, { useState, useEffect, useContext } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  CircularProgress,
  Alert,
  AlertTitle,
  Button,
  Card,
  CardContent,
  Divider,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  useTheme,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  SelectChangeEvent,
  IconButton,
  Tabs,
  Tab,
  Badge,
  Tooltip,
  LinearProgress,
  Stack,
  ButtonProps
} from '@mui/material';
import LoadingSpinner from '../common/LoadingSpinner';
import MessageDoctor from '../messages/MessageDoctor';
import { styled, alpha } from '@mui/material/styles';
import { Link } from 'react-router-dom';
import AuthContext from '../../context/AuthContext';
import { API_URL } from '../../config';
import HealthMetricsChart from '../visits/HealthMetricsChart';
import AlertBanner from '../common/AlertBanner';

// Icons
import MonitorHeartIcon from '@mui/icons-material/MonitorHeart';
import BloodtypeIcon from '@mui/icons-material/Bloodtype';
import ThermostatIcon from '@mui/icons-material/Thermostat';
import ScaleIcon from '@mui/icons-material/Scale';
import HeightIcon from '@mui/icons-material/Height';
import EventNoteIcon from '@mui/icons-material/EventNote';
import MedicationIcon from '@mui/icons-material/Medication';
import WarningIcon from '@mui/icons-material/Warning';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import MessageIcon from '@mui/icons-material/Message';
import NotificationsIcon from '@mui/icons-material/Notifications';
import TipsAndUpdatesIcon from '@mui/icons-material/TipsAndUpdates';
import FavoriteIcon from '@mui/icons-material/Favorite';
import RefreshIcon from '@mui/icons-material/Refresh';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import InfoIcon from '@mui/icons-material/Info';
import PersonIcon from '@mui/icons-material/Person';
import PhoneIcon from '@mui/icons-material/Phone';
import EmailIcon from '@mui/icons-material/Email';
import HomeIcon from '@mui/icons-material/Home';
import ContactPhoneIcon from '@mui/icons-material/ContactPhone';
import ContactEmergencyIcon from '@mui/icons-material/ContactEmergency';
import MedicalInformationIcon from '@mui/icons-material/MedicalInformation';
import PrintIcon from '@mui/icons-material/Print';
import TimelineIcon from '@mui/icons-material/Timeline';
import HistoryIcon from '@mui/icons-material/History';
import CompareArrowsIcon from '@mui/icons-material/CompareArrows';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import TrendingFlatIcon from '@mui/icons-material/TrendingFlat';
import AssignmentIcon from '@mui/icons-material/Assignment';



// Styled components
const DashboardContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  [theme.breakpoints.up('md')]: {
    padding: theme.spacing(4),
  },
  backgroundColor: alpha(theme.palette.background.default, 0.5),
}));

const DashboardHeader = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  marginBottom: theme.spacing(4),
  background: theme.palette.mode === 'dark'
    ? `linear-gradient(135deg, ${alpha(theme.palette.primary.dark, 0.8)} 0%, ${alpha(theme.palette.primary.dark, 0.6)} 100%)`
    : `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
  color: theme.palette.primary.contrastText,
  boxShadow: theme.palette.mode === 'dark'
    ? '0 8px 32px rgba(0, 0, 0, 0.3)'
    : '0 8px 32px rgba(0, 0, 0, 0.1)',
  position: 'relative',
  overflow: 'hidden',
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    right: 0,
    width: '30%',
    height: '100%',
    background: `linear-gradient(90deg, transparent 0%, ${alpha(theme.palette.primary.dark, theme.palette.mode === 'dark' ? 0.5 : 0.3)} 100%)`,
    zIndex: 1,
  }
}));

const PatientSelectorCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  marginBottom: theme.spacing(4),
  boxShadow: theme.palette.mode === 'dark'
    ? '0 4px 20px rgba(0, 0, 0, 0.2)'
    : '0 4px 20px rgba(0, 0, 0, 0.05)',
  border: `1px solid ${alpha(theme.palette.primary.main, theme.palette.mode === 'dark' ? 0.2 : 0.1)}`,
  background: theme.palette.background.paper,
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  '&:hover': {
    boxShadow: theme.palette.mode === 'dark'
      ? '0 6px 24px rgba(0, 0, 0, 0.3)'
      : '0 6px 24px rgba(0, 0, 0, 0.08)',
  },
}));

const QuickActionsContainer = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  marginBottom: theme.spacing(4),
  boxShadow: theme.palette.mode === 'dark'
    ? '0 4px 20px rgba(0, 0, 0, 0.2)'
    : '0 4px 20px rgba(0, 0, 0, 0.05)',
  background: theme.palette.background.paper,
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '4px',
    background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.primary.light})`,
  },
}));

// Create a custom button component that accepts Link as component prop
interface LinkButtonProps extends ButtonProps {
  component?: React.ElementType;
  to?: string;
}

const ActionButton = styled(Button)<LinkButtonProps>(({ theme }) => ({
  borderRadius: '12px',
  padding: theme.spacing(1.5, 2),
  textTransform: 'none',
  fontWeight: 500,
  boxShadow: 'none',
  transition: 'all 0.2s ease-in-out',
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    background: 'rgba(255, 255, 255, 0.1)',
    transform: 'translateX(-100%)',
    transition: 'transform 0.3s ease-out',
  },
  '&:hover': {
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
    transform: 'translateY(-2px)',
    '&::before': {
      transform: 'translateX(0)',
    }
  },
  '&:active': {
    transform: 'translateY(1px)',
  }
}));

const MetricCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  height: '100%',
  boxShadow: theme.palette.mode === 'dark'
    ? '0 4px 20px rgba(0, 0, 0, 0.2)'
    : '0 4px 20px rgba(0, 0, 0, 0.05)',
  transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
  position: 'relative',
  overflow: 'hidden',
  border: `1px solid ${alpha(theme.palette.primary.main, theme.palette.mode === 'dark' ? 0.15 : 0.08)}`,
  backgroundColor: theme.palette.background.paper,
  '&:hover': {
    transform: 'translateY(-5px)',
    boxShadow: theme.palette.mode === 'dark'
      ? '0 8px 25px rgba(0, 0, 0, 0.3)'
      : '0 8px 25px rgba(0, 0, 0, 0.1)',
  },
  '&::after': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '4px',
    background: theme.palette.primary.main,
    opacity: 0.7,
  },
  '&.warning': {
    borderColor: alpha(theme.palette.warning.main, theme.palette.mode === 'dark' ? 0.4 : 0.3),
    '&::after': {
      background: theme.palette.warning.main,
    }
  },
  '&.error': {
    borderColor: alpha(theme.palette.error.main, theme.palette.mode === 'dark' ? 0.4 : 0.3),
    '&::after': {
      background: theme.palette.error.main,
    }
  },
  '&.success': {
    borderColor: alpha(theme.palette.success.main, theme.palette.mode === 'dark' ? 0.4 : 0.3),
    '&::after': {
      background: theme.palette.success.main,
    }
  },
}));

const MetricHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
  gap: theme.spacing(2),
}));

const MetricIconContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  width: 52,
  height: 52,
  borderRadius: '14px',
  color: theme.palette.primary.main,
  background: alpha(theme.palette.primary.main, 0.1),
  boxShadow: `0 4px 8px ${alpha(theme.palette.primary.main, 0.15)}`,
  transition: 'transform 0.2s ease-in-out',
  '&:hover': {
    transform: 'scale(1.05)',
  }
}));

const MetricLabel = styled(Typography)(({ theme }) => ({
  fontWeight: 500,
  color: theme.palette.text.secondary,
  fontSize: '1.1rem',
  letterSpacing: '0.01em',
}));

const MetricValue = styled(Typography)(({ theme }) => ({
  fontWeight: 600,
  color: theme.palette.text.primary,
  display: 'flex',
  alignItems: 'baseline',
  letterSpacing: '-0.02em',
}));

const MetricUnit = styled('span')(({ theme }) => ({
  fontSize: '1rem',
  color: theme.palette.text.secondary,
  marginLeft: theme.spacing(0.5),
  fontWeight: 400,
}));

const VisitCard = styled(Card)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  borderRadius: '16px',
  boxShadow: theme.palette.mode === 'dark'
    ? '0 2px 12px rgba(0, 0, 0, 0.2)'
    : '0 2px 12px rgba(0, 0, 0, 0.05)',
  overflow: 'visible',
  transition: 'all 0.25s ease-in-out',
  border: `1px solid ${alpha(theme.palette.primary.main, theme.palette.mode === 'dark' ? 0.2 : 0.1)}`,
  backgroundColor: theme.palette.background.paper,
  '&:hover': {
    transform: 'translateY(-3px)',
    boxShadow: theme.palette.mode === 'dark'
      ? '0 8px 24px rgba(0, 0, 0, 0.3)'
      : '0 8px 24px rgba(0, 0, 0, 0.08)',
    borderColor: alpha(theme.palette.primary.main, theme.palette.mode === 'dark' ? 0.3 : 0.2),
  },
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  fontWeight: 600,
  marginBottom: theme.spacing(3),
  position: 'relative',
  display: 'inline-block',
  fontSize: '1.5rem',
  color: theme.palette.text.primary,
  '&:after': {
    content: '""',
    position: 'absolute',
    bottom: -8,
    left: 0,
    width: '40%',
    height: 3,
    background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${alpha(theme.palette.primary.main, 0.5)})`,
    borderRadius: 3,
  },
}));

const HealthTipCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  marginBottom: theme.spacing(2),
  boxShadow: theme.palette.mode === 'dark'
    ? '0 4px 20px rgba(0, 0, 0, 0.2)'
    : '0 4px 20px rgba(0, 0, 0, 0.05)',
  background: theme.palette.mode === 'dark'
    ? alpha(theme.palette.info.dark, 0.15)
    : alpha(theme.palette.info.light, 0.1),
  border: `1px solid ${alpha(theme.palette.info.main, theme.palette.mode === 'dark' ? 0.3 : 0.2)}`,
  transition: 'all 0.25s ease-in-out',
  '&:hover': {
    transform: 'translateY(-3px)',
    boxShadow: theme.palette.mode === 'dark'
      ? '0 8px 24px rgba(0, 0, 0, 0.3)'
      : '0 8px 24px rgba(0, 0, 0, 0.08)',
    borderColor: alpha(theme.palette.info.main, theme.palette.mode === 'dark' ? 0.4 : 0.3),
  },
}));

const EmergencyInfoCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  marginBottom: theme.spacing(4),
  boxShadow: theme.palette.mode === 'dark'
    ? '0 4px 20px rgba(0, 0, 0, 0.2)'
    : '0 4px 20px rgba(0, 0, 0, 0.05)',
  background: theme.palette.mode === 'dark'
    ? alpha(theme.palette.error.dark, 0.15)
    : alpha(theme.palette.error.light, 0.1),
  border: `1px solid ${alpha(theme.palette.error.main, theme.palette.mode === 'dark' ? 0.3 : 0.2)}`,
  transition: 'all 0.25s ease-in-out',
  position: 'relative',
  overflow: 'hidden',
  '&:hover': {
    transform: 'translateY(-3px)',
    boxShadow: theme.palette.mode === 'dark'
      ? '0 8px 24px rgba(0, 0, 0, 0.3)'
      : '0 8px 24px rgba(0, 0, 0, 0.08)',
    borderColor: alpha(theme.palette.error.main, theme.palette.mode === 'dark' ? 0.4 : 0.3),
  },
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '4px',
    height: '100%',
    background: theme.palette.error.main,
    opacity: theme.palette.mode === 'dark' ? 0.8 : 0.7,
  },
}));

const HealthAlertsCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  marginBottom: theme.spacing(4),
  boxShadow: theme.palette.mode === 'dark'
    ? '0 4px 20px rgba(0, 0, 0, 0.2)'
    : '0 4px 20px rgba(0, 0, 0, 0.05)',
  background: theme.palette.mode === 'dark'
    ? alpha(theme.palette.warning.dark, 0.15)
    : alpha(theme.palette.warning.light, 0.05),
  border: `1px solid ${alpha(theme.palette.warning.main, theme.palette.mode === 'dark' ? 0.3 : 0.2)}`,
  transition: 'all 0.25s ease-in-out',
  position: 'relative',
  overflow: 'hidden',
  '&:hover': {
    transform: 'translateY(-3px)',
    boxShadow: theme.palette.mode === 'dark'
      ? '0 8px 24px rgba(0, 0, 0, 0.3)'
      : '0 8px 24px rgba(0, 0, 0, 0.08)',
    borderColor: alpha(theme.palette.warning.main, theme.palette.mode === 'dark' ? 0.4 : 0.3),
  },
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '4px',
    height: '100%',
    background: theme.palette.warning.main,
    opacity: theme.palette.mode === 'dark' ? 0.8 : 0.7,
  },
}));

const TimelineCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  marginBottom: theme.spacing(4),
  boxShadow: theme.palette.mode === 'dark'
    ? '0 4px 20px rgba(0, 0, 0, 0.2)'
    : '0 4px 20px rgba(0, 0, 0, 0.05)',
  background: theme.palette.mode === 'dark'
    ? alpha(theme.palette.info.dark, 0.15)
    : alpha(theme.palette.info.light, 0.05),
  border: `1px solid ${alpha(theme.palette.info.main, theme.palette.mode === 'dark' ? 0.3 : 0.2)}`,
  transition: 'all 0.25s ease-in-out',
  position: 'relative',
  overflow: 'hidden',
  '&:hover': {
    transform: 'translateY(-3px)',
    boxShadow: theme.palette.mode === 'dark'
      ? '0 8px 24px rgba(0, 0, 0, 0.3)'
      : '0 8px 24px rgba(0, 0, 0, 0.08)',
    borderColor: alpha(theme.palette.info.main, theme.palette.mode === 'dark' ? 0.4 : 0.3),
  },
}));

const TimelineConnector = styled(Box)(({ theme }) => ({
  position: 'absolute',
  left: '24px',
  top: 0,
  bottom: 0,
  width: '3px', // Increase width for better visibility
  background: alpha(theme.palette.info.main, theme.palette.mode === 'dark' ? 0.6 : 0.5), // Increase opacity for better visibility
  zIndex: 0,
}));

const TimelineItem = styled(Box)(({ theme }) => ({
  position: 'relative',
  paddingLeft: theme.spacing(8), // Increase left padding to make room for the dot
  paddingBottom: theme.spacing(4),
  marginBottom: theme.spacing(2), // Add margin for better spacing between items
  '&:last-child': {
    paddingBottom: 0
  }
}));

const TimelineDot = styled(Box)(({ theme }) => ({
  position: 'absolute',
  left: 0,
  top: 0,
  width: '50px',
  height: '50px',
  borderRadius: '50%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  zIndex: 1,
  boxShadow: theme.palette.mode === 'dark'
    ? '0 4px 12px rgba(0, 0, 0, 0.3)'
    : '0 4px 12px rgba(0, 0, 0, 0.1)',
  backgroundColor: theme.palette.background.paper, // Add background color to ensure visibility
}));

const AppointmentCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(3),
  borderRadius: '16px',
  marginBottom: theme.spacing(2),
  boxShadow: theme.palette.mode === 'dark'
    ? '0 4px 20px rgba(0, 0, 0, 0.2)'
    : '0 4px 20px rgba(0, 0, 0, 0.05)',
  background: theme.palette.background.paper,
  border: `1px solid ${alpha(theme.palette.primary.main, theme.palette.mode === 'dark' ? 0.2 : 0.1)}`,
  transition: 'all 0.25s ease-in-out',
  position: 'relative',
  overflow: 'hidden',
  '&:hover': {
    transform: 'translateY(-3px)',
    boxShadow: theme.palette.mode === 'dark'
      ? '0 8px 24px rgba(0, 0, 0, 0.3)'
      : '0 8px 24px rgba(0, 0, 0, 0.08)',
    borderColor: alpha(theme.palette.primary.main, theme.palette.mode === 'dark' ? 0.3 : 0.2),
  },
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    width: '4px',
    height: '100%',
    background: theme.palette.primary.main,
    opacity: theme.palette.mode === 'dark' ? 0.8 : 0.7,
  },
}));

const StatusBadge = styled(Chip)(({ theme }) => ({
  fontWeight: 500,
  borderRadius: '8px',
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
  textTransform: 'capitalize',
}));

const TabPanel = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3, 0),
  animation: 'fadeIn 0.3s ease-in-out',
  '@keyframes fadeIn': {
    '0%': {
      opacity: 0,
      transform: 'translateY(10px)',
    },
    '100%': {
      opacity: 1,
      transform: 'translateY(0)',
    },
  },
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  textTransform: 'none',
  fontWeight: 500,
  fontSize: '1rem',
  minWidth: 'auto',
  padding: theme.spacing(1.5, 3),
  transition: 'all 0.2s ease-in-out',
  borderRadius: '8px 8px 0 0',
  color: theme.palette.text.secondary,
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, theme.palette.mode === 'dark' ? 0.15 : 0.05),
    color: theme.palette.primary.main,
  },
  '&.Mui-selected': {
    fontWeight: 600,
    color: theme.palette.mode === 'dark' ? theme.palette.primary.light : theme.palette.primary.main,
  }
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
  marginBottom: theme.spacing(3),
  '& .MuiTabs-indicator': {
    height: 3,
    borderRadius: 3,
    background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.primary.light})`,
  },
  '& .MuiTabs-flexContainer': {
    gap: theme.spacing(1),
  }
}));

// Define interfaces for type safety
interface RelatedPatient {
  patient_id: number;
  first_name: string;
  last_name: string;
  date_of_birth: string;
  gender: string;
  relationship_type: string;
  is_primary: boolean;
  relationship_id: number;
}

interface HealthTip {
  id: number;
  title: string;
  content: string;
  category: string;
  icon: React.ReactNode;
}

interface Appointment {
  id: number;
  date: string;
  time: string;
  doctor: string;
  reason: string;
  status: 'scheduled' | 'completed' | 'cancelled';
}

interface Medication {
  id: number;
  name: string;
  dosage: string;
  frequency: string;
  instructions: string;
  nextDose?: string;
  refillDate?: string;
  isLow?: boolean;
  schedule?: {
    morning?: boolean;
    afternoon?: boolean;
    evening?: boolean;
    bedtime?: boolean;
  };
  lastRefill?: string;
  daysRemaining?: number;
}

interface Message {
  id: number;
  sender: string;
  content: string;
  date: string;
  isRead: boolean;
}

interface HealthMetric {
  name: string;
  value: string | number | null;
  unit: string;
  icon: React.ReactNode;
  status?: 'normal' | 'warning' | 'critical';
  change?: {
    value: number;
    direction: 'up' | 'down';
    isGood: boolean;
  };
}

interface HealthAlert {
  name: string;
  value: string | number;
  severity: 'warning' | 'error' | 'info';
  details?: string;
  date?: string;
  actionRequired?: string;
  category: 'vital' | 'medication' | 'appointment' | 'preventive';
}

interface VisitTimelineItem {
  id: string | number;
  date: string;
  doctor: string;
  reason?: string;
  keyFindings: {
    category: string;
    name: string;
    value: string | number;
    unit?: string;
    change?: {
      direction: 'up' | 'down' | 'stable';
      value: number;
      isPositive: boolean;
    };
  }[];
  medications?: {
    name: string;
    action: 'added' | 'changed' | 'discontinued';
  }[];
  notes?: string;
}

const KinDashboard: React.FC = () => {
  const { user } = useContext(AuthContext);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [dashboardData, setDashboardData] = useState<any>(null);
  const [visits, setVisits] = useState<any[]>([]);
  const [visitsLoading, setVisitsLoading] = useState<boolean>(true);
  const [patientData, setPatientData] = useState<any>(null);
  const [combinedData, setCombinedData] = useState<any[]>([]);
  const [relatedPatients, setRelatedPatients] = useState<RelatedPatient[]>([]);
  const [selectedPatientId, setSelectedPatientId] = useState<number | null>(null);
  const [patientsLoading, setPatientsLoading] = useState<boolean>(true);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<number>(0);
  const [healthMetrics, setHealthMetrics] = useState<HealthMetric[]>([]);
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [messages, setMessages] = useState<Message[]>([]);
  const [healthTips, setHealthTips] = useState<HealthTip[]>([]);
  const [healthAlerts, setHealthAlerts] = useState<HealthAlert[]>([]);
  const [visitTimelineItems, setVisitTimelineItems] = useState<VisitTimelineItem[]>([]);
  const [messageDialogOpen, setMessageDialogOpen] = useState<boolean>(false);
  const theme = useTheme();

  // Fetch all patients related to this kin user
  useEffect(() => {
    const fetchRelatedPatients = async () => {
      try {
        setPatientsLoading(true);
        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('No authentication token found');
        }

        const response = await fetch(`${API_URL}/api/kin/patients`, {
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          }
        });

        if (!response.ok) {
          // If the new endpoint fails (might not be deployed yet), fall back to using the patient_id from user object
          if (user?.patient_id) {
            console.log('Falling back to user.patient_id:', user.patient_id);
            // Fetch the patient details to create a related patient object
            const patientResponse = await fetch(`${API_URL}/api/patients/${user.patient_id}`, {
              headers: {
                'Content-Type': 'application/json',
                'x-auth-token': token
              }
            });

            if (patientResponse.ok) {
              const patientData = await patientResponse.json();
              const fallbackPatient: RelatedPatient = {
                patient_id: user.patient_id,
                first_name: patientData.first_name,
                last_name: patientData.last_name,
                date_of_birth: patientData.date_of_birth,
                gender: patientData.gender || 'Unknown',
                relationship_type: 'Family Member',
                is_primary: true,
                relationship_id: 0
              };
              setRelatedPatients([fallbackPatient]);
              setSelectedPatientId(user.patient_id);
            } else {
              throw new Error(`Failed to fetch patient data: ${patientResponse.statusText}`);
            }
          } else {
            throw new Error(`Failed to fetch related patients: ${response.statusText}`);
          }
        } else {
          const data = await response.json();
          console.log('Related patients:', data);
          setRelatedPatients(data);

          // Set the selected patient to the primary one, or the first one if no primary
          const primaryPatient = data.find((p: RelatedPatient) => p.is_primary);
          setSelectedPatientId(primaryPatient ? primaryPatient.patient_id : data[0]?.patient_id);
        }
      } catch (err) {
        console.error('Error fetching related patients:', err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setPatientsLoading(false);
      }
    };

    fetchRelatedPatients();
  }, [user]);

  // Process health metrics from vital signs
  const processHealthMetrics = (latestVitals: any, previousVitals?: any) => {
    if (!latestVitals) return [];

    const metrics: HealthMetric[] = [];

    // Blood Glucose
    if (latestVitals.blood_glucose) {
      const value = parseFloat(latestVitals.blood_glucose);
      let status: 'normal' | 'warning' | 'critical' = 'normal';

      // Determine status based on blood glucose levels
      if (value > 180) status = 'critical';
      else if (value > 140 || value < 70) status = 'warning';

      // Calculate change if previous data exists
      let change = undefined;
      if (previousVitals?.blood_glucose) {
        const prevValue = parseFloat(previousVitals.blood_glucose);
        const diff = value - prevValue;
        change = {
          value: Math.abs(diff),
          direction: diff >= 0 ? 'up' as const : 'down' as const,
          isGood: (diff < 0 && value > 70) || (diff > 0 && value < 70)
        };
      }

      metrics.push({
        name: 'Blood Glucose',
        value,
        unit: 'mg/dL',
        icon: <BloodtypeIcon />,
        status,
        change
      });
    }

    // Blood Pressure
    if (latestVitals.lying_bp_systolic && latestVitals.lying_bp_diastolic) {
      const systolic = parseFloat(latestVitals.lying_bp_systolic);
      const diastolic = parseFloat(latestVitals.lying_bp_diastolic);
      let status: 'normal' | 'warning' | 'critical' = 'normal';

      // Determine status based on blood pressure levels
      if (systolic >= 140 || diastolic >= 90) status = 'warning';
      if (systolic >= 180 || diastolic >= 120) status = 'critical';
      if (systolic < 90 || diastolic < 60) status = 'warning';

      metrics.push({
        name: 'Blood Pressure',
        value: `${systolic}/${diastolic}`,
        unit: 'mmHg',
        icon: <MonitorHeartIcon />,
        status
      });
    }

    // Heart Rate (Lying)
    if (latestVitals.lying_heart_rate) {
      const value = parseFloat(latestVitals.lying_heart_rate);
      let status: 'normal' | 'warning' | 'critical' = 'normal';

      // Determine status based on heart rate
      if (value > 100 || value < 60) status = 'warning';
      if (value > 120 || value < 50) status = 'critical';

      metrics.push({
        name: 'Lying Heart Rate',
        value,
        unit: 'bpm',
        icon: <FavoriteIcon />,
        status
      });
    }

    // Heart Rate (Standing)
    if (latestVitals.standing_heart_rate) {
      const value = parseFloat(latestVitals.standing_heart_rate);
      let status: 'normal' | 'warning' | 'critical' = 'normal';

      // Determine status based on heart rate
      if (value > 100 || value < 60) status = 'warning';
      if (value > 120 || value < 50) status = 'critical';

      metrics.push({
        name: 'Standing Heart Rate',
        value,
        unit: 'bpm',
        icon: <FavoriteIcon />,
        status
      });
    }

    // Temperature
    if (latestVitals.temperature) {
      const value = parseFloat(latestVitals.temperature);
      let status: 'normal' | 'warning' | 'critical' = 'normal';

      // Determine status based on temperature (Celsius)
      if (value > 37.5 || value < 36) status = 'warning';
      if (value > 38.5 || value < 35) status = 'critical';

      metrics.push({
        name: 'Temperature',
        value,
        unit: '°C',
        icon: <ThermostatIcon />,
        status
      });
    }

    // Weight
    if (latestVitals.weight) {
      metrics.push({
        name: 'Weight',
        value: latestVitals.weight,
        unit: 'kg',
        icon: <ScaleIcon />
      });
    }

    // BMI
    if (latestVitals.bmi) {
      const value = parseFloat(latestVitals.bmi);
      let status: 'normal' | 'warning' | 'critical' = 'normal';

      // Determine status based on BMI
      if (value >= 25 || value < 18.5) status = 'warning';
      if (value >= 30 || value < 16) status = 'critical';

      metrics.push({
        name: 'BMI',
        value,
        unit: 'kg/m²',
        icon: <HeightIcon />,
        status
      });
    }

    return metrics;
  };

  // Generate sample health tips
  const generateHealthTips = () => {
    return [
      {
        id: 1,
        title: 'Stay Hydrated',
        content: 'Ensure your family member drinks at least 8 glasses of water daily, especially during hot weather.',
        category: 'general',
        icon: <InfoIcon color="info" />
      },
      {
        id: 2,
        title: 'Medication Reminder',
        content: 'Always check that medications are taken with food if required, and at the correct times of day.',
        category: 'medication',
        icon: <MedicationIcon color="primary" />
      },
      {
        id: 3,
        title: 'Blood Pressure Management',
        content: 'Regular physical activity and reducing salt intake can help maintain healthy blood pressure levels.',
        category: 'heart',
        icon: <FavoriteIcon color="error" />
      }
    ];
  };

  // Generate sample appointments
  const generateAppointments = () => {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const nextWeek = new Date(today);
    nextWeek.setDate(nextWeek.getDate() + 7);

    return [
      {
        id: 1,
        date: tomorrow.toISOString().split('T')[0],
        time: '10:30 AM',
        doctor: 'Dr. Sarah Johnson',
        reason: 'Blood Pressure Check',
        status: 'scheduled' as const
      },
      {
        id: 2,
        date: nextWeek.toISOString().split('T')[0],
        time: '2:15 PM',
        doctor: 'Dr. Michael Chen',
        reason: 'Diabetes Follow-up',
        status: 'scheduled' as const
      }
    ];
  };

  // Generate sample messages
  const generateMessages = () => {
    return [
      {
        id: 1,
        sender: 'Dr. Sarah Johnson',
        content: 'The latest blood test results look good. Please continue with the current medication regimen.',
        date: new Date(Date.now() - 86400000).toISOString(), // yesterday
        isRead: false
      },
      {
        id: 2,
        sender: 'Nurse Williams',
        content: 'Just a reminder about the upcoming appointment on Friday. Please bring the medication list.',
        date: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
        isRead: true
      }
    ];
  };

  // Process visit data for timeline
  const processVisitsForTimeline = (visits: any[], patientData: any) => {
    console.log('processVisitsForTimeline called with:', {
      visits,
      patientData,
      hasPatientInfo: patientData && patientData.patientInfo ? true : false
    });

    if (!visits || visits.length === 0) {
      console.log('No visits provided, returning empty array');
      return [];
    }

    // Create an array to hold all visits
    let allVisits = [...visits];
    console.log('Initial allVisits:', allVisits);

    // Add baseline visit with hardcoded date
    const baselineVisit = {
      visit_id: 'baseline',
      visit_date: '2024-01-19T00:00:00.000Z', // Hardcoded date for Ram's baseline
      visit_reason: 'Initial Assessment',
      doctor_first_name: 'John',
      doctor_last_name: 'Smith',
      notes: 'Baseline health assessment at registration',
      vital_signs: {
        lying_bp_systolic: '118',
        lying_bp_diastolic: '84',
        lying_heart_rate: '83',
        weight: '94.00',
        height: '175',
        bmi: '30.7',
        blood_glucose: '110',
        temperature: '36.8'
      },
      medications: []
    };

    // Add baseline visit to the array
    allVisits.push(baselineVisit);
    console.log('Added baseline visit to timeline:', baselineVisit);

    // If we still have no visits, return empty array
    if (allVisits.length === 0) {
      console.log('No visits after adding baseline, returning empty array');
      return [];
    }

    console.log('All visits before sorting:', allVisits);

    // Sort visits by date (newest first)
    const sortedVisits = [...allVisits].sort((a, b) => {
      return new Date(b.visit_date).getTime() - new Date(a.visit_date).getTime();
    });

    console.log('Sorted visits:', sortedVisits);

    // Process each visit to create timeline items
    const timelineItems: VisitTimelineItem[] = sortedVisits.map((visit, index) => {
      const previousVisit = index < sortedVisits.length - 1 ? sortedVisits[index + 1] : null;
      const keyFindings: VisitTimelineItem['keyFindings'] = [];

      // Process vital signs
      if (visit.vital_signs) {
        // Blood pressure
        if (visit.vital_signs.lying_bp_systolic && visit.vital_signs.lying_bp_diastolic) {
          const currentBP = `${visit.vital_signs.lying_bp_systolic}/${visit.vital_signs.lying_bp_diastolic}`;
          let change;

          if (previousVisit?.vital_signs?.lying_bp_systolic && previousVisit?.vital_signs?.lying_bp_diastolic) {
            const prevSystolic = parseInt(previousVisit.vital_signs.lying_bp_systolic);
            const prevDiastolic = parseInt(previousVisit.vital_signs.lying_bp_diastolic);
            const currSystolic = parseInt(visit.vital_signs.lying_bp_systolic);
            const currDiastolic = parseInt(visit.vital_signs.lying_bp_diastolic);

            // Calculate average change
            const systolicChange = currSystolic - prevSystolic;
            const diastolicChange = currDiastolic - prevDiastolic;
            const avgChange = (Math.abs(systolicChange) + Math.abs(diastolicChange)) / 2;

            // Determine direction
            let direction: 'up' | 'down' | 'stable' = 'stable';
            if (systolicChange > 5 || diastolicChange > 5) {
              direction = 'up';
            } else if (systolicChange < -5 || diastolicChange < -5) {
              direction = 'down';
            }

            // Determine if change is positive (normal BP is around 120/80)
            const isHighBP = currSystolic > 140 || currDiastolic > 90;
            const isLowBP = currSystolic < 90 || currDiastolic < 60;

            // If BP was high and now lower, that's positive
            // If BP was low and now higher, that's positive
            // If BP was normal and still normal, that's positive
            const isPositive = (
              (prevSystolic > 140 && currSystolic < prevSystolic) ||
              (prevDiastolic > 90 && currDiastolic < prevDiastolic) ||
              (prevSystolic < 90 && currSystolic > prevSystolic) ||
              (prevDiastolic < 60 && currDiastolic > prevDiastolic) ||
              (!isHighBP && !isLowBP)
            );

            change = {
              direction,
              value: Math.round(avgChange),
              isPositive
            };
          }

          keyFindings.push({
            category: 'Vital Signs',
            name: 'Blood Pressure',
            value: currentBP,
            unit: 'mmHg',
            change
          });
        }

        // Heart rate
        if (visit.vital_signs.lying_heart_rate) {
          const currentHR = visit.vital_signs.lying_heart_rate;
          let change;

          if (previousVisit?.vital_signs?.lying_heart_rate) {
            const prevHR = parseInt(previousVisit.vital_signs.lying_heart_rate);
            const currHR = parseInt(currentHR);

            // Calculate change
            const hrChange = currHR - prevHR;

            // Determine direction
            let direction: 'up' | 'down' | 'stable' = 'stable';
            if (hrChange > 5) {
              direction = 'up';
            } else if (hrChange < -5) {
              direction = 'down';
            }

            // Determine if change is positive (normal HR is 60-100)
            const isHighHR = currHR > 100;
            const isLowHR = currHR < 60;

            // If HR was high and now lower, that's positive
            // If HR was low and now higher, that's positive
            // If HR was normal and still normal, that's positive
            const isPositive = (
              (prevHR > 100 && currHR < prevHR) ||
              (prevHR < 60 && currHR > prevHR) ||
              (!isHighHR && !isLowHR)
            );

            change = {
              direction,
              value: Math.abs(hrChange),
              isPositive
            };
          }

          keyFindings.push({
            category: 'Vital Signs',
            name: 'Heart Rate',
            value: currentHR,
            unit: 'bpm',
            change
          });
        }

        // Weight
        if (visit.vital_signs.weight) {
          const currentWeight = visit.vital_signs.weight;
          let change;

          if (previousVisit?.vital_signs?.weight) {
            const prevWeight = parseFloat(previousVisit.vital_signs.weight);
            const currWeight = parseFloat(currentWeight);

            // Calculate change
            const weightChange = currWeight - prevWeight;

            // Determine direction
            let direction: 'up' | 'down' | 'stable' = 'stable';
            if (weightChange > 1) {
              direction = 'up';
            } else if (weightChange < -1) {
              direction = 'down';
            }

            // For weight, stability is generally positive unless there's a medical reason for weight change
            // This is a simplification - in reality, the "positive" nature would depend on the patient's goals
            const isPositive = Math.abs(weightChange) < 2; // Less than 2kg change is considered stable

            change = {
              direction,
              value: Math.abs(weightChange),
              isPositive
            };
          }

          keyFindings.push({
            category: 'Vital Signs',
            name: 'Weight',
            value: currentWeight,
            unit: 'kg',
            change
          });
        }

        // Blood glucose
        if (visit.vital_signs.blood_glucose) {
          const currentBG = visit.vital_signs.blood_glucose;
          let change;

          if (previousVisit?.vital_signs?.blood_glucose) {
            const prevBG = parseFloat(previousVisit.vital_signs.blood_glucose);
            const currBG = parseFloat(currentBG);

            // Calculate change
            const bgChange = currBG - prevBG;

            // Determine direction
            let direction: 'up' | 'down' | 'stable' = 'stable';
            if (bgChange > 10) {
              direction = 'up';
            } else if (bgChange < -10) {
              direction = 'down';
            }

            // Determine if change is positive (normal BG is 70-140 mg/dL)
            const isHighBG = currBG > 140;
            const isLowBG = currBG < 70;

            // If BG was high and now lower, that's positive
            // If BG was low and now higher, that's positive
            // If BG was normal and still normal, that's positive
            const isPositive = (
              (prevBG > 140 && currBG < prevBG) ||
              (prevBG < 70 && currBG > prevBG) ||
              (!isHighBG && !isLowBG)
            );

            change = {
              direction,
              value: Math.abs(bgChange),
              isPositive
            };
          }

          keyFindings.push({
            category: 'Vital Signs',
            name: 'Blood Glucose',
            value: currentBG,
            unit: 'mg/dL',
            change
          });
        }
      }

      // Process medications if available
      const medications: VisitTimelineItem['medications'] = [];
      if (visit.medications && visit.medications.length > 0) {
        for (const med of visit.medications) {
          medications.push({
            name: med.medication || med.name || '',
            action: 'added' as const // Use 'as const' to ensure it's the literal type "added"
          });
        }
      }

      // Determine if this is the baseline visit
      const isBaseline = visit.visit_id === 'baseline';

      return {
        id: visit.visit_id,
        date: visit.visit_date,
        doctor: visit.doctor_first_name && visit.doctor_last_name
          ? `Dr. ${visit.doctor_first_name} ${visit.doctor_last_name}`
          : 'Unknown Doctor',
        reason: isBaseline ? 'Initial Assessment' : (visit.visit_reason || 'Routine checkup'),
        keyFindings,
        medications: medications.length > 0 ? medications : undefined,
        notes: isBaseline ? 'Baseline health assessment at registration' : visit.notes
      };
    });

    console.log('Final timeline items:', timelineItems);
    return timelineItems;
  };

  // Generate health alerts based on patient data
  const generateHealthAlerts = (patientData: any, currentAppointments: Appointment[] = []) => {
    if (!patientData) return [];

    const alerts: HealthAlert[] = [];
    const { patientInfo, latestVitals, medications, visits: recentVisits } = patientData;

    // 1. Vital signs alerts
    if (latestVitals) {
      // Blood pressure alerts
      if (latestVitals.lying_bp_systolic && latestVitals.lying_bp_diastolic) {
        const systolic = parseFloat(latestVitals.lying_bp_systolic);
        const diastolic = parseFloat(latestVitals.lying_bp_diastolic);

        if (systolic >= 180 || diastolic >= 120) {
          alerts.push({
            name: 'Critical Blood Pressure',
            value: `${systolic}/${diastolic} mmHg`,
            severity: 'error',
            details: 'Blood pressure is at a crisis level. Immediate medical attention may be needed.',
            actionRequired: 'Contact healthcare provider immediately',
            category: 'vital'
          });
        } else if (systolic >= 140 || diastolic >= 90) {
          alerts.push({
            name: 'High Blood Pressure',
            value: `${systolic}/${diastolic} mmHg`,
            severity: 'warning',
            details: 'Blood pressure is above the normal range (120/80 mmHg).',
            actionRequired: 'Monitor closely and consult with doctor',
            category: 'vital'
          });
        } else if (systolic < 90 || diastolic < 60) {
          alerts.push({
            name: 'Low Blood Pressure',
            value: `${systolic}/${diastolic} mmHg`,
            severity: 'warning',
            details: 'Blood pressure is below the normal range (90/60 mmHg).',
            actionRequired: 'Monitor for dizziness or fainting',
            category: 'vital'
          });
        }
      }

      // Heart rate alerts
      if (latestVitals.lying_heart_rate) {
        const heartRate = parseFloat(latestVitals.lying_heart_rate);

        if (heartRate > 120) {
          alerts.push({
            name: 'Elevated Heart Rate',
            value: `${heartRate} bpm`,
            severity: 'warning',
            details: 'Heart rate is significantly above normal range (60-100 bpm).',
            actionRequired: 'Monitor and consult with doctor if persistent',
            category: 'vital'
          });
        } else if (heartRate < 50) {
          alerts.push({
            name: 'Low Heart Rate',
            value: `${heartRate} bpm`,
            severity: 'warning',
            details: 'Heart rate is below normal range (60-100 bpm).',
            actionRequired: 'Monitor for dizziness or fatigue',
            category: 'vital'
          });
        }
      }

      // Temperature alerts
      if (latestVitals.temperature) {
        const temperature = parseFloat(latestVitals.temperature);

        if (temperature > 38.5) {
          alerts.push({
            name: 'High Fever',
            value: `${temperature}°C`,
            severity: 'error',
            details: 'Temperature is significantly elevated.',
            actionRequired: 'Monitor closely and consider seeking medical attention',
            category: 'vital'
          });
        } else if (temperature > 37.5) {
          alerts.push({
            name: 'Mild Fever',
            value: `${temperature}°C`,
            severity: 'warning',
            details: 'Temperature is slightly elevated above normal (36.1-37.2°C).',
            actionRequired: 'Monitor and ensure adequate hydration',
            category: 'vital'
          });
        } else if (temperature < 35.5) {
          alerts.push({
            name: 'Low Body Temperature',
            value: `${temperature}°C`,
            severity: 'warning',
            details: 'Temperature is below normal range (36.1-37.2°C).',
            actionRequired: 'Ensure patient is warm and monitor',
            category: 'vital'
          });
        }
      }

      // Blood glucose alerts
      if (latestVitals.blood_glucose) {
        const glucose = parseFloat(latestVitals.blood_glucose);

        if (glucose > 180) {
          alerts.push({
            name: 'High Blood Glucose',
            value: `${glucose} mg/dL`,
            severity: 'warning',
            details: 'Blood glucose is above target range.',
            actionRequired: 'Monitor food intake and medication adherence',
            category: 'vital'
          });
        } else if (glucose < 70) {
          alerts.push({
            name: 'Low Blood Glucose',
            value: `${glucose} mg/dL`,
            severity: 'warning',
            details: 'Blood glucose is below target range.',
            actionRequired: 'Ensure patient consumes fast-acting carbohydrates',
            category: 'vital'
          });
        }
      }
    }

    // 2. Medication alerts
    if (medications && medications.length > 0) {
      // Find medications that need refills soon
      const lowMedications = medications.filter((med: any) => med.isLow || (med.daysRemaining !== undefined && med.daysRemaining <= 7));

      if (lowMedications.length > 0) {
        lowMedications.forEach((med: any) => {
          alerts.push({
            name: 'Medication Refill Needed',
            value: med.name,
            severity: med.daysRemaining <= 3 ? 'error' : 'warning',
            details: `Only ${med.daysRemaining} days of medication remaining.`,
            actionRequired: 'Contact pharmacy for refill',
            category: 'medication'
          });
        });
      }
    }

    // 3. Appointment alerts
    if (currentAppointments && currentAppointments.length > 0) {
      const today = new Date();
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      // Find appointments happening tomorrow
      const upcomingAppointments = currentAppointments.filter(app => {
        const appDate = new Date(app.date);
        return appDate.toDateString() === tomorrow.toDateString();
      });

      if (upcomingAppointments.length > 0) {
        upcomingAppointments.forEach(app => {
          alerts.push({
            name: 'Upcoming Appointment',
            value: `Tomorrow at ${app.time}`,
            severity: 'info',
            details: `Appointment with ${app.doctor} for ${app.reason}`,
            actionRequired: 'Prepare necessary documents and transportation',
            category: 'appointment'
          });
        });
      }
    }

    // 4. Preventive care alerts
    // Age-based preventive care recommendations
    if (patientInfo && patientInfo.date_of_birth) {
      // Calculate age directly here to avoid dependency on calculateAge function
      const birthDate = new Date(patientInfo.date_of_birth);
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }

      // For seniors (65+)
      if (age >= 65) {
        // Check if there's a recent visit within the last year
        const oneYearAgo = new Date();
        oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);

        const hasRecentVisit = recentVisits && recentVisits.some((visit: any) => {
          const visitDate = new Date(visit.visit_date);
          return visitDate > oneYearAgo;
        });

        if (!hasRecentVisit) {
          alerts.push({
            name: 'Annual Wellness Visit Due',
            value: 'Overdue',
            severity: 'info',
            details: 'It has been over a year since the last comprehensive health check.',
            actionRequired: 'Schedule an annual wellness visit',
            category: 'preventive'
          });
        }

        // Flu shot reminder during flu season (October-March in Northern Hemisphere)
        const currentMonth = new Date().getMonth(); // 0-11
        if (currentMonth >= 9 || currentMonth <= 2) { // October (9) through March (2)
          alerts.push({
            name: 'Flu Shot Reminder',
            value: 'Recommended',
            severity: 'info',
            details: 'Annual flu vaccination is recommended for older adults.',
            actionRequired: 'Discuss with healthcare provider',
            category: 'preventive'
          });
        }
      }
    }

    return alerts;
  };

  // Generate enhanced medication data with schedules, refill information, and interactions
  const generateEnhancedMedications = (medications: any[]) => {
    if (!medications || medications.length === 0) return [];

    // Define common medication interactions (simplified for demo)
    const knownInteractions: Record<string, string[]> = {
      'Warfarin': ['Aspirin', 'Ibuprofen', 'Naproxen', 'Clopidogrel'],
      'Aspirin': ['Warfarin', 'Heparin', 'Clopidogrel'],
      'Lisinopril': ['Potassium supplements', 'Spironolactone'],
      'Simvastatin': ['Erythromycin', 'Clarithromycin', 'Ketoconazole'],
      'Levothyroxine': ['Calcium supplements', 'Iron supplements', 'Antacids'],
      'Metformin': ['Cimetidine', 'Furosemide'],
      'Digoxin': ['Amiodarone', 'Verapamil', 'Quinidine'],
      'Phenytoin': ['Valproic acid', 'Carbamazepine'],
      'Fluoxetine': ['Tramadol', 'Codeine', 'Triptans'],
      'Amiodarone': ['Digoxin', 'Warfarin', 'Simvastatin']
    };

    // Check for interactions between medications
    const findInteractions = (medName: string, allMeds: any[]) => {
      const interactions: string[] = [];
      const normalizedName = medName.toLowerCase();

      // Check if this medication has known interactions
      Object.keys(knownInteractions).forEach(key => {
        // If this is the medication we're checking
        if (key.toLowerCase().includes(normalizedName) || normalizedName.includes(key.toLowerCase())) {
          // Check if any of its known interactions are in the medication list
          knownInteractions[key].forEach(interactingMed => {
            allMeds.forEach(otherMed => {
              const otherMedName = otherMed.name || otherMed.medication_name || '';
              if (otherMedName.toLowerCase().includes(interactingMed.toLowerCase()) ||
                  interactingMed.toLowerCase().includes(otherMedName.toLowerCase())) {
                interactions.push(otherMedName);
              }
            });
          });
        }
      });

      return interactions;
    };

    // Calculate adherence score (simplified for demo)
    const calculateAdherenceScore = () => {
      // In a real app, this would be based on actual adherence data
      // For demo, generate a random score between 70 and 100
      return Math.floor(Math.random() * 31) + 70;
    };

    return medications.map((med, index) => {
      // Parse frequency to determine schedule
      const schedule = {
        morning: false,
        afternoon: false,
        evening: false,
        bedtime: false
      };

      const frequencyLower = (med.frequency || '').toLowerCase();

      if (frequencyLower.includes('daily') || frequencyLower.includes('once a day')) {
        // For once daily, default to morning
        schedule.morning = true;
      } else if (frequencyLower.includes('twice') || frequencyLower.includes('two times') || frequencyLower.includes('2 times')) {
        // For twice daily, default to morning and evening
        schedule.morning = true;
        schedule.evening = true;
      } else if (frequencyLower.includes('three times') || frequencyLower.includes('3 times')) {
        // For three times daily, default to morning, afternoon, and evening
        schedule.morning = true;
        schedule.afternoon = true;
        schedule.evening = true;
      } else if (frequencyLower.includes('four times') || frequencyLower.includes('4 times')) {
        // For four times daily, all times
        schedule.morning = true;
        schedule.afternoon = true;
        schedule.evening = true;
        schedule.bedtime = true;
      } else if (frequencyLower.includes('bedtime') || frequencyLower.includes('night')) {
        // For bedtime only
        schedule.bedtime = true;
      } else if (frequencyLower.includes('morning')) {
        schedule.morning = true;
      } else if (frequencyLower.includes('evening')) {
        schedule.evening = true;
      } else {
        // Default to morning if we can't determine
        schedule.morning = true;
      }

      // Generate random next dose time based on current time
      const now = new Date();
      const hour = now.getHours();
      let nextDose = '';

      if (hour < 9 && schedule.morning) {
        nextDose = 'Today, 8:00 AM';
      } else if (hour < 13 && schedule.afternoon) {
        nextDose = 'Today, 12:00 PM';
      } else if (hour < 18 && schedule.evening) {
        nextDose = 'Today, 6:00 PM';
      } else if (hour < 22 && schedule.bedtime) {
        nextDose = 'Today, 10:00 PM';
      } else if (schedule.morning) {
        nextDose = 'Tomorrow, 8:00 AM';
      }

      // Generate random refill date and days remaining
      const today = new Date();
      const randomDays = Math.floor(Math.random() * 30) + 1; // 1-30 days
      const refillDate = new Date(today);
      refillDate.setDate(today.getDate() + randomDays);

      const formattedRefillDate = refillDate.toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric'
      });

      // Determine if medication is running low (less than 7 days)
      const isLow = randomDays <= 7;

      // Generate random last refill date
      const lastRefillDate = new Date(today);
      lastRefillDate.setDate(today.getDate() - (Math.floor(Math.random() * 20) + 10)); // 10-30 days ago

      const formattedLastRefillDate = lastRefillDate.toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric'
      });

      // Find potential interactions with other medications
      const interactions = findInteractions(med.medication || med.name || '', medications);

      // Calculate adherence score
      const adherenceScore = calculateAdherenceScore();

      // Generate random start date (for demo purposes)
      const startDate = new Date(today);
      startDate.setDate(today.getDate() - (Math.floor(Math.random() * 90) + 30)); // 30-120 days ago

      const formattedStartDate = startDate.toLocaleDateString('en-US', {
        month: 'long',
        day: 'numeric',
        year: 'numeric'
      });

      return {
        ...med,
        id: index + 1,
        name: med.medication || med.name,
        dosage: med.dosage || '1 tablet',
        frequency: med.frequency || 'Once daily',
        instructions: med.instructions || 'Take with food',
        schedule,
        nextDose,
        refillDate: formattedRefillDate,
        lastRefill: formattedLastRefillDate,
        daysRemaining: randomDays,
        isLow,
        interactions,
        adherenceScore,
        startDate: formattedStartDate
      };
    });
  };

  // Fetch dashboard data for the selected patient
  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!selectedPatientId) {
        return;
      }

      setLoading(true);
      setError(null);
      setRefreshing(true);

      try {
        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('No authentication token found');
        }

        // Fetch dashboard data
        const response = await fetch(`${API_URL}/api/patients/dashboard/${selectedPatientId}`, {
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch dashboard data: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Received patient dashboard data:', data);

        // Log specific fields for debugging
        console.log('Patient Info Fields:', Object.keys(data.patientInfo));
        console.log('Patient created_at:', data.patientInfo.created_at);
        console.log('Patient date_of_birth:', data.patientInfo.date_of_birth);

        // Check if visits data exists and log it
        if (data.visits) {
          console.log('Visits from dashboard data:', data.visits);
          console.log('Number of visits:', data.visits.length);
        } else {
          console.warn('No visits data found in dashboard response');
        }

        setDashboardData(data);

        // Process health metrics
        if (data.latestVitals) {
          const previousVisit = data.visits && data.visits.length > 1 ? data.visits[1].vital_signs : null;
          const metrics = processHealthMetrics(data.latestVitals, previousVisit);
          setHealthMetrics(metrics);
        }

        // Set sample data for new features
        setHealthTips(generateHealthTips());
        setAppointments(generateAppointments());
        setMessages(generateMessages());

        // Enhance medication data if available
        if (data.medications && data.medications.length > 0) {
          data.medications = generateEnhancedMedications(data.medications);
        }

        // Generate health alerts
        const alerts = generateHealthAlerts(data, appointments);
        setHealthAlerts(alerts);

        // Process visits for timeline
        console.log('Processing timeline data with:', {
          visits: data.visits,
          patientInfo: data.patientInfo,
          hasVisits: data.visits && Array.isArray(data.visits) && data.visits.length > 0
        });

        try {
          // Fetch additional visits
          console.log('Fetching additional visits for timeline');
          const visitsToken = localStorage.getItem('token');
          if (!visitsToken) {
            throw new Error('No authentication token found for visits');
          }

          const visitsResponse = await fetch(`${API_URL}/api/visits/patient/${selectedPatientId}`, {
            headers: {
              'Content-Type': 'application/json',
              'x-auth-token': visitsToken
            }
          });

          if (!visitsResponse.ok) {
            throw new Error(`Failed to fetch visits: ${visitsResponse.statusText}`);
          }

          const additionalVisits = await visitsResponse.json();
          console.log('Additional visits fetched:', additionalVisits);

          // Create a visit-like object for each visit
          const formattedVisits = additionalVisits.map((visit: any) => {
            return {
              visit_id: visit.visit_id,
              visit_date: visit.visit_date,
              visit_reason: visit.visit_reason || 'Follow-up Visit',
              doctor_first_name: visit.doctor_first_name || '',
              doctor_last_name: visit.doctor_last_name || '',
              notes: visit.notes || '',
              vital_signs: {
                lying_bp_systolic: visit.lying_bp_systolic || null,
                lying_bp_diastolic: visit.lying_bp_diastolic || null,
                lying_heart_rate: visit.lying_heart_rate || null,
                standing_heart_rate: visit.standing_heart_rate || null,
                weight: visit.weight || null,
                height: visit.height || null,
                bmi: visit.bmi || null,
                blood_glucose: visit.blood_glucose || null,
                temperature: visit.temperature || null
              },
              medications: visit.medications || []
            };
          });

          console.log('Formatted visits:', formattedVisits);

          // Process timeline items with visits
          const timelineItems = processVisitsForTimeline(formattedVisits, data);
          console.log('Timeline items generated:', timelineItems);

          if (timelineItems.length > 0) {
            setVisitTimelineItems(timelineItems);
          } else {
            setVisitTimelineItems([]);
          }
        } catch (timelineErr) {
          console.error('Error processing timeline:', timelineErr);
          setVisitTimelineItems([]);
        }
      } catch (err) {
        console.error('Error fetching patient dashboard data:', err);
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setLoading(false);
        setTimeout(() => setRefreshing(false), 500);
      }
    };

    fetchDashboardData();
  }, [selectedPatientId]);

  // Fetch visits and patient data for health charts
  useEffect(() => {
    const fetchVisitsForCharts = async () => {
      if (!selectedPatientId) return;

      try {
        setVisitsLoading(true);
        const token = localStorage.getItem('token');

        // Fetch patient data
        const patientResponse = await fetch(`${API_URL}/api/patients/${selectedPatientId}`, {
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token as string
          }
        });

        if (!patientResponse.ok) {
          throw new Error(`Failed to fetch patient data: ${patientResponse.statusText}`);
        }

        const patientData = await patientResponse.json();
        setPatientData(patientData);

        // Fetch visits
        const visitsResponse = await fetch(`${API_URL}/api/visits/patient/${selectedPatientId}`, {
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token as string
          }
        });

        if (!visitsResponse.ok) {
          throw new Error(`Failed to fetch visits: ${visitsResponse.statusText}`);
        }

        const visitsData = await visitsResponse.json();
        setVisits(visitsData);

        // Combine patient data with visits for charts
        if (patientData) {
          // Create a visit-like object from patient data for initial values
          const initialDataPoint = {
            visit_id: 'initial',
            visit_date: patientData.created_at || new Date().toISOString(),
            blood_pressure: patientData.blood_pressure || null,
            heart_rate: patientData.heart_rate || null,
            temperature: patientData.temperature || null,
            weight: patientData.weight || null,
            height: patientData.height || null,
            bmi: patientData.bmi || null,
            blood_glucose: patientData.blood_glucose || null,
            oxygen_saturation: patientData.oxygen_saturation || null,
            respiratory_rate: patientData.respiratory_rate || null,
            lying_bp_systolic: patientData.lying_bp_systolic || null,
            lying_bp_diastolic: patientData.lying_bp_diastolic || null,
            standing_bp_systolic: patientData.standing_bp_systolic || null,
            standing_bp_diastolic: patientData.standing_bp_diastolic || null,
            lying_heart_rate: patientData.lying_heart_rate || null,
            standing_heart_rate: patientData.standing_heart_rate || null,
            isInitialData: true
          };

          // Combine with visits
          const combined = [initialDataPoint, ...visitsData];
          setCombinedData(combined);
        } else {
          setCombinedData(visitsData);
        }
      } catch (err) {
        console.error('Error fetching visits for charts:', err);
      } finally {
        setVisitsLoading(false);
      }
    };

    fetchVisitsForCharts();
  }, [selectedPatientId]);

  // Handle patient selection change
  const handlePatientChange = (event: SelectChangeEvent<number>) => {
    setSelectedPatientId(event.target.value as number);
  };

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // Handle refresh dashboard
  const handleRefresh = () => {
    if (refreshing) return;

    // Re-fetch dashboard data
    const fetchDashboardData = async () => {
      if (!selectedPatientId) return;

      setRefreshing(true);

      try {
        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('No authentication token found');
        }

        const response = await fetch(`${API_URL}/api/patients/dashboard/${selectedPatientId}`, {
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch dashboard data: ${response.statusText}`);
        }

        const data = await response.json();
        setDashboardData(data);

        // Process health metrics
        if (data.latestVitals) {
          const previousVisit = data.visits && data.visits.length > 1 ? data.visits[1].vital_signs : null;
          const metrics = processHealthMetrics(data.latestVitals, previousVisit);
          setHealthMetrics(metrics);
        }
      } catch (err) {
        console.error('Error refreshing dashboard data:', err);
      } finally {
        setTimeout(() => setRefreshing(false), 500);
      }
    };

    fetchDashboardData();
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    };
    return new Date(dateString).toLocaleDateString('en-US', options);
  };

  // Get status color for health metrics
  const getStatusColor = (status?: 'normal' | 'warning' | 'critical') => {
    switch (status) {
      case 'warning':
        return theme.palette.warning.main;
      case 'critical':
        return theme.palette.error.main;
      default:
        return theme.palette.success.main;
    }
  };

  // Get status class for metric cards
  const getStatusClass = (status?: 'normal' | 'warning' | 'critical') => {
    switch (status) {
      case 'warning':
        return 'warning';
      case 'critical':
        return 'error';
      default:
        return 'success';
    }
  };

  // Handle opening the message dialog
  const handleOpenMessageDialog = () => {
    setMessageDialogOpen(true);
  };

  // Handle closing the message dialog
  const handleCloseMessageDialog = () => {
    setMessageDialogOpen(false);
  };

  // Handle printing emergency information
  const handlePrintEmergencyInfo = (patientInfo: any) => {
    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      alert('Please allow pop-ups to print emergency information');
      return;
    }

    // Calculate age from date of birth
    const birthDate = new Date(patientInfo.date_of_birth);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    // Get current date in a readable format
    const currentDate = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Create the print content with the WiseAge Wellness logo as a watermark
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Emergency Information - ${patientInfo.first_name} ${patientInfo.last_name}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 0;
              padding: 20px;
              position: relative;
              color: #333;
            }
            .watermark {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%) rotate(-30deg);
              opacity: 0.1;
              z-index: -1;
              width: 60%;
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
              border-bottom: 2px solid #f44336;
              padding-bottom: 10px;
            }
            .header h1 {
              color: #f44336;
              margin-bottom: 5px;
            }
            .header p {
              margin: 5px 0;
              color: #666;
            }
            .section {
              margin-bottom: 25px;
            }
            .section h2 {
              color: #f44336;
              border-bottom: 1px solid #ddd;
              padding-bottom: 5px;
            }
            .info-row {
              display: flex;
              margin-bottom: 15px;
            }
            .info-label {
              font-weight: bold;
              width: 200px;
            }
            .info-value {
              flex: 1;
            }
            .emergency-box {
              border: 2px solid #f44336;
              padding: 15px;
              border-radius: 5px;
              margin-bottom: 25px;
              background-color: #fff9f9;
            }
            .emergency-box h2 {
              color: #f44336;
              margin-top: 0;
            }
            .footer {
              margin-top: 50px;
              text-align: center;
              font-size: 12px;
              color: #999;
              border-top: 1px solid #ddd;
              padding-top: 10px;
            }
            @media print {
              body {
                padding: 0;
                margin: 15mm;
              }
              .no-print {
                display: none;
              }
            }
          </style>
        </head>
        <body>
          <img src="/assets/wise-age-logo.svg" class="watermark" alt="WiseAge Wellness Watermark" />

          <div class="header">
            <h1>EMERGENCY INFORMATION</h1>
            <p>Printed on ${currentDate}</p>
          </div>

          <div class="emergency-box">
            <h2>EMERGENCY CONTACT</h2>
            <div class="info-row">
              <div class="info-label">Name:</div>
              <div class="info-value">${patientInfo.emergency_contact_name || 'Not provided'}</div>
            </div>
            ${patientInfo.emergency_contact_relationship ? `
            <div class="info-row">
              <div class="info-label">Relationship:</div>
              <div class="info-value">${patientInfo.emergency_contact_relationship}</div>
            </div>
            ` : ''}
            <div class="info-row">
              <div class="info-label">Phone:</div>
              <div class="info-value">${patientInfo.emergency_contact_phone || 'Not provided'}</div>
            </div>
          </div>

          <div class="section">
            <h2>PATIENT INFORMATION</h2>
            <div class="info-row">
              <div class="info-label">Name:</div>
              <div class="info-value">${patientInfo.first_name} ${patientInfo.last_name}</div>
            </div>
            <div class="info-row">
              <div class="info-label">Date of Birth:</div>
              <div class="info-value">${new Date(patientInfo.date_of_birth).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })} (Age: ${age})</div>
            </div>
            <div class="info-row">
              <div class="info-label">Gender:</div>
              <div class="info-value">${patientInfo.gender || 'Not specified'}</div>
            </div>
            ${patientInfo.blood_type ? `
            <div class="info-row">
              <div class="info-label">Blood Type:</div>
              <div class="info-value">${patientInfo.blood_type}</div>
            </div>
            ` : ''}
            <div class="info-row">
              <div class="info-label">Allergies:</div>
              <div class="info-value">${patientInfo.allergies || 'None reported'}</div>
            </div>
          </div>

          <div class="section">
            <h2>MEDICAL PROVIDER</h2>
            <div class="info-row">
              <div class="info-label">Primary Doctor:</div>
              <div class="info-value">${patientInfo.doctor_name || 'Not assigned'}</div>
            </div>
          </div>

          <div class="footer">
            <p>This document was generated by WiseAge Wellness. Keep this information updated and readily available for emergencies.</p>
          </div>

          <div class="no-print" style="text-align: center; margin-top: 30px;">
            <button onclick="window.print()" style="padding: 10px 20px; background-color: #f44336; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
              Print Document
            </button>
          </div>
        </body>
      </html>
    `);

    printWindow.document.close();
  };

  // Handle printing medication list
  const handlePrintMedicationList = (medications: any[]) => {
    if (!medications || medications.length === 0) {
      alert('No medications to print');
      return;
    }

    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      alert('Please allow pop-ups to print medication list');
      return;
    }

    // Get current date in a readable format
    const currentDate = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // Get patient info
    const patientName = dashboardData?.patientInfo?.first_name + ' ' + dashboardData?.patientInfo?.last_name;

    // Create medication schedule table rows
    const medicationRows = medications.map((med) => {
      // Determine schedule checkmarks
      const morningCheck = med.schedule?.morning ? '✓' : '—';
      const afternoonCheck = med.schedule?.afternoon ? '✓' : '—';
      const eveningCheck = med.schedule?.evening ? '✓' : '—';
      const bedtimeCheck = med.schedule?.bedtime ? '✓' : '—';

      return `
        <tr>
          <td>${med.name}</td>
          <td>${med.dosage}</td>
          <td>${med.frequency}</td>
          <td>${morningCheck}</td>
          <td>${afternoonCheck}</td>
          <td>${eveningCheck}</td>
          <td>${bedtimeCheck}</td>
          <td>${med.instructions || 'N/A'}</td>
          <td>${med.refillDate || 'N/A'}</td>
        </tr>
      `;
    }).join('');

    // Create the print content with the WiseAge Wellness logo as a watermark
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Medication List - ${patientName}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 0;
              padding: 20px;
              position: relative;
              color: #333;
            }
            .watermark {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%) rotate(-30deg);
              opacity: 0.1;
              z-index: -1;
              width: 60%;
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
              border-bottom: 2px solid #3f51b5;
              padding-bottom: 10px;
            }
            .header h1 {
              color: #3f51b5;
              margin-bottom: 5px;
            }
            .header p {
              margin: 5px 0;
              color: #666;
            }
            .patient-info {
              margin-bottom: 20px;
            }
            .patient-info p {
              margin: 5px 0;
              font-size: 16px;
            }
            .patient-info strong {
              font-weight: bold;
            }
            table {
              width: 100%;
              border-collapse: collapse;
              margin-bottom: 30px;
            }
            th, td {
              border: 1px solid #ddd;
              padding: 8px;
              text-align: left;
            }
            th {
              background-color: #f2f2f2;
              font-weight: bold;
            }
            tr:nth-child(even) {
              background-color: #f9f9f9;
            }
            .schedule-header {
              text-align: center;
              background-color: #e8eaf6;
              font-weight: bold;
            }
            .schedule-cell {
              text-align: center;
            }
            .footer {
              margin-top: 50px;
              text-align: center;
              font-size: 12px;
              color: #999;
              border-top: 1px solid #ddd;
              padding-top: 10px;
            }
            .instructions {
              margin-top: 30px;
              padding: 15px;
              background-color: #f5f5f5;
              border-radius: 5px;
            }
            .instructions h2 {
              margin-top: 0;
              color: #3f51b5;
            }
            .instructions ul {
              margin-left: 20px;
              padding-left: 0;
            }
            .instructions li {
              margin-bottom: 8px;
            }
            @media print {
              body {
                padding: 0;
                margin: 15mm;
              }
              .no-print {
                display: none;
              }
              table {
                page-break-inside: avoid;
              }
            }
          </style>
        </head>
        <body>
          <img src="/assets/wise-age-logo.svg" class="watermark" alt="WiseAge Wellness Watermark" />

          <div class="header">
            <h1>MEDICATION LIST</h1>
            <p>Printed on ${currentDate}</p>
          </div>

          <div class="patient-info">
            <p><strong>Patient:</strong> ${patientName}</p>
            <p><strong>Primary Doctor:</strong> ${dashboardData?.patientInfo?.doctor_name || 'Not assigned'}</p>
          </div>

          <table>
            <thead>
              <tr>
                <th>Medication</th>
                <th>Dosage</th>
                <th>Frequency</th>
                <th colspan="4" class="schedule-header">Daily Schedule</th>
                <th>Instructions</th>
                <th>Refill Due</th>
              </tr>
              <tr>
                <th></th>
                <th></th>
                <th></th>
                <th class="schedule-cell">Morning</th>
                <th class="schedule-cell">Afternoon</th>
                <th class="schedule-cell">Evening</th>
                <th class="schedule-cell">Bedtime</th>
                <th></th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              ${medicationRows}
            </tbody>
          </table>

          <div class="instructions">
            <h2>Medication Instructions</h2>
            <ul>
              <li>Take all medications as prescribed by your doctor.</li>
              <li>Do not stop taking any medication without consulting your doctor first.</li>
              <li>Store medications in a cool, dry place away from direct sunlight.</li>
              <li>Keep all medications out of reach of children.</li>
              <li>Check refill dates and request refills at least one week before running out.</li>
              <li>Bring this medication list to all doctor appointments and hospital visits.</li>
            </ul>
          </div>

          <div class="footer">
            <p>This document was generated by WiseAge Wellness. Keep this information updated and bring it to all medical appointments.</p>
          </div>

          <div class="no-print" style="text-align: center; margin-top: 30px;">
            <button onclick="window.print()" style="padding: 10px 20px; background-color: #3f51b5; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 16px;">
              Print Medication List
            </button>
          </div>
        </body>
      </html>
    `);

    printWindow.document.close();
  };

  // Loading state for patients list
  if (patientsLoading) {
    return <LoadingSpinner size="large" message="Loading patients..." />;
  }

  // Error state for patients list
  if (error && relatedPatients.length === 0) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        {error}
      </Alert>
    );
  }

  // No related patients found
  if (relatedPatients.length === 0) {
    return (
      <Alert severity="info" sx={{ mt: 2 }}>
        No related patients found for this kin user. Please contact an administrator to set up patient relationships.
      </Alert>
    );
  }

  // Loading state for dashboard data
  if (loading) {
    return <LoadingSpinner size="large" message="Loading dashboard data..." />;
  }

  // Error state for dashboard data
  if (error && selectedPatientId) {
    return (
      <>
        {/* Patient Selector */}
        <Box sx={{ mb: 4 }}>
          <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
            <InputLabel id="patient-select-label">Select Patient</InputLabel>
            <Select
              labelId="patient-select-label"
              id="patient-select"
              value={selectedPatientId || ''}
              onChange={handlePatientChange}
              label="Select Patient"
            >
              {relatedPatients.map((patient) => (
                <MenuItem key={patient.patient_id} value={patient.patient_id}>
                  {patient.first_name} {patient.last_name}
                  {patient.is_primary && ' (Primary)'}
                  {patient.relationship_type && ` - ${patient.relationship_type}`}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>

        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      </>
    );
  }

  // No dashboard data available
  if (!dashboardData) {
    return (
      <>
        {/* Patient Selector */}
        <Box sx={{ mb: 4 }}>
          <FormControl fullWidth variant="outlined" sx={{ mb: 2 }}>
            <InputLabel id="patient-select-label">Select Patient</InputLabel>
            <Select
              labelId="patient-select-label"
              id="patient-select"
              value={selectedPatientId || ''}
              onChange={handlePatientChange}
              label="Select Patient"
            >
              {relatedPatients.map((patient) => (
                <MenuItem key={patient.patient_id} value={patient.patient_id}>
                  {patient.first_name} {patient.last_name}
                  {patient.is_primary && ' (Primary)'}
                  {patient.relationship_type && ` - ${patient.relationship_type}`}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>

        <Alert severity="info" sx={{ mt: 2 }}>
          No dashboard data available for the selected patient.
        </Alert>
      </>
    );
  }

  const { patientInfo, visits: recentVisits, medications, latestVitals } = dashboardData;

  // Calculate age from date of birth
  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  return (
    <DashboardContainer>
      {/* Patient Selector */}
      {relatedPatients.length > 1 && (
        <PatientSelectorCard>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
            <Box>
              <Typography variant="h6" fontWeight="600" sx={{ mb: 0.5 }}>
                Family Members
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Select a family member to view their health dashboard
              </Typography>
            </Box>
            <Tooltip title="Refresh dashboard">
              <IconButton
                color="primary"
                onClick={handleRefresh}
                disabled={refreshing}
                sx={{
                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                  '&:hover': {
                    bgcolor: alpha(theme.palette.primary.main, 0.2),
                  }
                }}
              >
                {refreshing ? <CircularProgress size={24} /> : <RefreshIcon />}
              </IconButton>
            </Tooltip>
          </Box>

          <Grid container spacing={2}>
            {relatedPatients.map((patient) => (
              <Grid item xs={12} sm={6} md={4} key={patient.patient_id}>
                <Paper
                  elevation={0}
                  onClick={() => setSelectedPatientId(patient.patient_id)}
                  sx={{
                    p: 2,
                    borderRadius: '12px',
                    cursor: 'pointer',
                    border: `2px solid ${selectedPatientId === patient.patient_id
                      ? theme.palette.primary.main
                      : alpha(theme.palette.primary.main, 0.1)}`,
                    bgcolor: selectedPatientId === patient.patient_id
                      ? alpha(theme.palette.primary.main, 0.05)
                      : 'background.paper',
                    transition: 'all 0.2s ease-in-out',
                    display: 'flex',
                    alignItems: 'center',
                    '&:hover': {
                      borderColor: alpha(theme.palette.primary.main, 0.5),
                      bgcolor: alpha(theme.palette.primary.main, 0.05),
                      transform: 'translateY(-2px)',
                      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
                    }
                  }}
                >
                  <Avatar
                    sx={{
                      mr: 2,
                      bgcolor: selectedPatientId === patient.patient_id
                        ? theme.palette.primary.main
                        : alpha(theme.palette.primary.main, 0.1),
                      color: selectedPatientId === patient.patient_id
                        ? 'white'
                        : theme.palette.primary.main,
                      width: 48,
                      height: 48,
                    }}
                  >
                    <PersonIcon />
                  </Avatar>
                  <Box>
                    <Typography variant="body1" fontWeight="500">
                      {patient.first_name} {patient.last_name}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                      {patient.is_primary && (
                        <Chip
                          size="small"
                          label="Primary"
                          color="primary"
                          sx={{ mr: 1, height: 20, fontSize: '0.7rem' }}
                        />
                      )}
                      {patient.relationship_type && (
                        <Typography variant="body2" color="text.secondary">
                          {patient.relationship_type}
                        </Typography>
                      )}
                    </Box>
                  </Box>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </PatientSelectorCard>
      )}

      {/* Header Section */}
      <DashboardHeader>
        <Grid container spacing={2} alignItems="center">
          <Grid item>
            <Avatar
              sx={{
                width: 64,
                height: 64,
                bgcolor: 'white',
                color: theme.palette.primary.main,
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
              }}
            >
              <PersonIcon fontSize="large" />
            </Avatar>
          </Grid>
          <Grid item xs>
            <Typography variant="h4" fontWeight="600" color="inherit" gutterBottom>
              {patientInfo.first_name} {patientInfo.last_name}
            </Typography>
            <Typography variant="subtitle1" sx={{ opacity: 0.9 }} color="inherit">
              Family Health Dashboard
            </Typography>
          </Grid>
          <Grid item>
            <Box sx={{ textAlign: 'right' }}>
              <Typography variant="body2" sx={{ opacity: 0.9 }} color="inherit">
                Last updated: {new Date().toLocaleString()}
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, mt: 1, justifyContent: 'flex-end' }}>
                <Chip
                  label={`Age: ${calculateAge(patientInfo.date_of_birth)}`}
                  size="small"
                  sx={{
                    bgcolor: 'rgba(255, 255, 255, 0.2)',
                    color: 'white',
                    fontWeight: 500
                  }}
                />
                <Chip
                  label={patientInfo.gender}
                  size="small"
                  sx={{
                    bgcolor: 'rgba(255, 255, 255, 0.2)',
                    color: 'white',
                    fontWeight: 500
                  }}
                />
              </Box>
            </Box>
          </Grid>
        </Grid>

        <Box sx={{ mt: 3, display: 'flex', flexWrap: 'wrap', gap: 2 }}>
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            bgcolor: 'rgba(255, 255, 255, 0.2)',
            borderRadius: 2,
            px: 2,
            py: 1
          }}>
            <LocalHospitalIcon sx={{ mr: 1 }} />
            <Typography variant="body2" fontWeight="500">
              Doctor: {patientInfo.doctor_name || 'Not assigned'}
            </Typography>
          </Box>

          {patientInfo.phone && (
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              bgcolor: 'rgba(255, 255, 255, 0.2)',
              borderRadius: 2,
              px: 2,
              py: 1
            }}>
              <PhoneIcon sx={{ mr: 1 }} />
              <Typography variant="body2" fontWeight="500">
                {patientInfo.phone}
              </Typography>
            </Box>
          )}

          {patientInfo.email && (
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              bgcolor: 'rgba(255, 255, 255, 0.2)',
              borderRadius: 2,
              px: 2,
              py: 1
            }}>
              <EmailIcon sx={{ mr: 1 }} />
              <Typography variant="body2" fontWeight="500">
                {patientInfo.email}
              </Typography>
            </Box>
          )}
        </Box>
      </DashboardHeader>

      {/* Quick Actions Section */}
      <QuickActionsContainer>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
          <Box>
            <Typography variant="h6" fontWeight="600" sx={{ mb: 0.5 }}>
              Quick Actions
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Common tasks for managing {patientInfo?.first_name}'s healthcare
            </Typography>
          </Box>
        </Box>

        <Grid container spacing={3}>
          {/* Primary Actions */}
          <Grid item xs={12} md={8}>
            <Typography variant="subtitle1" fontWeight="500" sx={{ mb: 2, color: theme.palette.text.secondary }}>
              Primary Actions
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <ActionButton
                  fullWidth
                  variant="contained"
                  color="primary"
                  startIcon={<CalendarTodayIcon />}
                  component={Link}
                  to={`/kin/patients/${selectedPatientId}/schedule-appointment`}
                  sx={{
                    py: 1.5,
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                  }}
                >
                  Schedule Appointment
                </ActionButton>
              </Grid>
              <Grid item xs={12} sm={6}>
                <ActionButton
                  fullWidth
                  variant="contained"
                  color="primary"
                  startIcon={<MessageIcon />}
                  onClick={handleOpenMessageDialog}
                  sx={{
                    py: 1.5,
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                  }}
                >
                  Message Doctor
                </ActionButton>
              </Grid>
            </Grid>
          </Grid>

          {/* Secondary Actions */}
          <Grid item xs={12} md={4}>
            <Typography variant="subtitle1" fontWeight="500" sx={{ mb: 2, color: theme.palette.text.secondary }}>
              View Information
            </Typography>
            <Box sx={{
              p: 2,
              bgcolor: alpha(theme.palette.background.default, 0.5),
              borderRadius: '12px',
              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
            }}>
              <Stack spacing={1.5}>
                <ActionButton
                  fullWidth
                  variant="outlined"
                  color="primary"
                  startIcon={<EventNoteIcon />}
                  component={Link}
                  to={`/kin/patients/${selectedPatientId}/visits`}
                  sx={{ justifyContent: 'flex-start' }}
                >
                  View All Visits
                </ActionButton>
                <ActionButton
                  fullWidth
                  variant="outlined"
                  color="primary"
                  startIcon={<MedicationIcon />}
                  component={Link}
                  to={`/kin/patients/${selectedPatientId}/medications`}
                  sx={{ justifyContent: 'flex-start' }}
                >
                  Medication List
                </ActionButton>
              </Stack>
            </Box>
          </Grid>
        </Grid>
      </QuickActionsContainer>

      {/* Main Dashboard Tabs */}
      <Paper
        elevation={0}
        sx={{
          mb: 4,
          borderRadius: '16px',
          overflow: 'hidden',
          border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
        }}
      >
        <Box sx={{ px: 2, pt: 2 }}>
          <StyledTabs
            value={activeTab}
            onChange={handleTabChange}
            variant="scrollable"
            scrollButtons="auto"
          >
            <StyledTab
              icon={<MonitorHeartIcon />}
              iconPosition="start"
              label="Health Overview"
            />
            <StyledTab
              icon={<MessageIcon />}
              iconPosition="start"
              label={
                <Badge
                  color="error"
                  badgeContent={messages.filter(m => !m.isRead).length}
                  invisible={messages.filter(m => !m.isRead).length === 0}
                  sx={{ '& .MuiBadge-badge': { top: -5, right: -10 } }}
                >
                  Messages
                </Badge>
              }
            />
            <StyledTab
              icon={<CalendarTodayIcon />}
              iconPosition="start"
              label="Appointments"
            />
            <StyledTab
              icon={<TipsAndUpdatesIcon />}
              iconPosition="start"
              label="Health Tips"
            />
          </StyledTabs>
        </Box>
      </Paper>

      {/* Tab Panels */}
      {activeTab === 0 && (
        <Box sx={{ py: 3 }}>
          {/* Health Overview Tab Content */}
          {/* Key Health Indicators Section */}
      <Box sx={{ mb: 5 }}>
        <SectionTitle variant="h5">Key Health Indicators</SectionTitle>

        <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'space-between', flexWrap: 'wrap' }}>
          <Box>
            <Typography variant="body1" sx={{ fontSize: '1.125rem', mb: 1 }}>
              These are the most recent health measurements for your family member.
            </Typography>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexWrap: 'wrap' }}>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 0.5,
              bgcolor: alpha(theme.palette.success.main, 0.1),
              color: theme.palette.success.main,
              borderRadius: 1,
              py: 0.5,
              px: 1.5,
              fontWeight: 500
            }}>
              <CheckCircleIcon fontSize="small" />
              <Typography variant="body2" fontWeight="500">Normal</Typography>
            </Box>

            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 0.5,
              bgcolor: alpha(theme.palette.warning.main, 0.1),
              color: theme.palette.warning.main,
              borderRadius: 1,
              py: 0.5,
              px: 1.5,
              fontWeight: 500
            }}>
              <WarningIcon fontSize="small" />
              <Typography variant="body2" fontWeight="500">Attention</Typography>
            </Box>

            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 0.5,
              bgcolor: alpha(theme.palette.error.main, 0.1),
              color: theme.palette.error.main,
              borderRadius: 1,
              py: 0.5,
              px: 1.5,
              fontWeight: 500
            }}>
              <ErrorIcon fontSize="small" />
              <Typography variant="body2" fontWeight="500">Critical</Typography>
            </Box>
          </Box>
        </Box>

        {healthMetrics.length > 0 ? (
          <Grid container spacing={3}>
            {healthMetrics.map((metric, index) => (
              <Grid item xs={12} md={6} lg={4} key={index}>
                <MetricCard className={getStatusClass(metric.status)}>
                  <MetricHeader>
                    <MetricIconContainer>
                      {metric.icon}
                    </MetricIconContainer>
                    <Box>
                      <MetricLabel variant="h6">{metric.name}</MetricLabel>
                    </Box>
                    {metric.status && metric.status !== 'normal' && (
                      <Tooltip title={metric.status === 'warning' ? 'Needs attention' : 'Critical value'}>
                        <Box sx={{ ml: 'auto' }}>
                          {metric.status === 'warning' ? (
                            <WarningIcon color="warning" />
                          ) : (
                            <ErrorIcon color="error" />
                          )}
                        </Box>
                      </Tooltip>
                    )}
                  </MetricHeader>

                  <Box sx={{ display: 'flex', alignItems: 'baseline', justifyContent: 'space-between' }}>
                    <MetricValue variant="h4">
                      {metric.value !== null ? metric.value : 'N/A'}
                      {metric.value !== null && <MetricUnit> {metric.unit}</MetricUnit>}
                    </MetricValue>

                    {metric.change && (
                      <Box sx={{
                        display: 'flex',
                        alignItems: 'center',
                        color: metric.change.isGood ? theme.palette.success.main : theme.palette.error.main,
                        bgcolor: metric.change.isGood
                          ? alpha(theme.palette.success.main, 0.1)
                          : alpha(theme.palette.error.main, 0.1),
                        borderRadius: 1,
                        py: 0.5,
                        px: 1,
                        ml: 2
                      }}>
                        {metric.change.direction === 'up' ? (
                          <ArrowUpwardIcon fontSize="small" sx={{ mr: 0.5 }} />
                        ) : (
                          <ArrowDownwardIcon fontSize="small" sx={{ mr: 0.5 }} />
                        )}
                        <Typography variant="body2" fontWeight="500">
                          {metric.change.value.toFixed(1)}
                        </Typography>
                      </Box>
                    )}
                  </Box>

                  {/* Add a progress bar for metrics that have normal ranges */}
                  {(metric.name === 'Blood Glucose' || metric.name === 'BMI') && (
                    <Box sx={{ mt: 2 }}>
                      <LinearProgress
                        variant="determinate"
                        value={
                          metric.name === 'Blood Glucose'
                            ? Math.min(100, (parseFloat(metric.value as string) / 200) * 100)
                            : Math.min(100, (parseFloat(metric.value as string) / 35) * 100)
                        }
                        color={
                          metric.status === 'critical'
                            ? 'error'
                            : metric.status === 'warning'
                              ? 'warning'
                              : 'success'
                        }
                        sx={{ height: 8, borderRadius: 4 }}
                      />
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 0.5 }}>
                        <Typography variant="caption" color="text.secondary">
                          {metric.name === 'Blood Glucose' ? 'Low' : 'Underweight'}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {metric.name === 'Blood Glucose' ? 'High' : 'Overweight'}
                        </Typography>
                      </Box>
                    </Box>
                  )}
                </MetricCard>
              </Grid>
            ))}
          </Grid>
        ) : latestVitals ? (
          <Grid container spacing={3}>
            {/* Blood Glucose Card */}
            <Grid item xs={12} md={6} lg={4}>
              <MetricCard>
                <MetricHeader>
                  <MetricIconContainer>
                    <BloodtypeIcon fontSize="large" />
                  </MetricIconContainer>
                  <Box>
                    <MetricLabel variant="h6">Blood Glucose</MetricLabel>
                  </Box>
                </MetricHeader>

                <MetricValue variant="h4">
                  {latestVitals.blood_glucose || 'N/A'}
                  {latestVitals.blood_glucose && <MetricUnit> mg/dL</MetricUnit>}
                </MetricValue>
              </MetricCard>
            </Grid>

            {/* Blood Pressure Card */}
            <Grid item xs={12} md={6} lg={4}>
              <MetricCard>
                <MetricHeader>
                  <MetricIconContainer>
                    <MonitorHeartIcon fontSize="large" />
                  </MetricIconContainer>
                  <Box>
                    <MetricLabel variant="h6">Blood Pressure</MetricLabel>
                  </Box>
                </MetricHeader>

                <MetricValue variant="h4">
                  {latestVitals.blood_pressure || 'N/A'}
                  {latestVitals.blood_pressure && <MetricUnit> mmHg</MetricUnit>}
                </MetricValue>
              </MetricCard>
            </Grid>

            {/* Heart Rate Card */}
            <Grid item xs={12} md={6} lg={4}>
              <MetricCard>
                <MetricHeader>
                  <MetricIconContainer>
                    <FavoriteIcon fontSize="large" />
                  </MetricIconContainer>
                  <Box>
                    <MetricLabel variant="h6">Heart Rate</MetricLabel>
                  </Box>
                </MetricHeader>

                <Grid container spacing={1}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Lying</Typography>
                    <MetricValue variant="h5">
                      {latestVitals.lying_heart_rate || 'N/A'}
                      {latestVitals.lying_heart_rate && <MetricUnit> bpm</MetricUnit>}
                    </MetricValue>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">Standing</Typography>
                    <MetricValue variant="h5">
                      {latestVitals.standing_heart_rate || 'N/A'}
                      {latestVitals.standing_heart_rate && <MetricUnit> bpm</MetricUnit>}
                    </MetricValue>
                  </Grid>
                </Grid>
              </MetricCard>
            </Grid>

            {/* Temperature Card */}
            <Grid item xs={12} md={6} lg={4}>
              <MetricCard>
                <MetricHeader>
                  <MetricIconContainer>
                    <ThermostatIcon fontSize="large" />
                  </MetricIconContainer>
                  <Box>
                    <MetricLabel variant="h6">Temperature</MetricLabel>
                  </Box>
                </MetricHeader>

                <MetricValue variant="h4">
                  {latestVitals.temperature || 'N/A'}
                  {latestVitals.temperature && <MetricUnit> °C</MetricUnit>}
                </MetricValue>
              </MetricCard>
            </Grid>

            {/* Weight Card */}
            <Grid item xs={12} md={6} lg={4}>
              <MetricCard>
                <MetricHeader>
                  <MetricIconContainer>
                    <ScaleIcon fontSize="large" />
                  </MetricIconContainer>
                  <Box>
                    <MetricLabel variant="h6">Weight</MetricLabel>
                  </Box>
                </MetricHeader>

                <MetricValue variant="h4">
                  {latestVitals.weight || 'N/A'}
                  {latestVitals.weight && <MetricUnit> kg</MetricUnit>}
                </MetricValue>
              </MetricCard>
            </Grid>

            {/* BMI Card */}
            <Grid item xs={12} md={6} lg={4}>
              <MetricCard>
                <MetricHeader>
                  <MetricIconContainer>
                    <HeightIcon fontSize="large" />
                  </MetricIconContainer>
                  <Box>
                    <MetricLabel variant="h6">BMI</MetricLabel>
                  </Box>
                </MetricHeader>

                <MetricValue variant="h4">
                  {latestVitals.bmi || 'N/A'}
                  {latestVitals.bmi && <MetricUnit> kg/m²</MetricUnit>}
                </MetricValue>
              </MetricCard>
            </Grid>
          </Grid>
        ) : (
          <Alert severity="info">No vital signs data available yet.</Alert>
        )}
      </Box>

      {/* Emergency Information Section */}
      <Box sx={{ mb: 5 }}>
        <SectionTitle variant="h5">Emergency Information</SectionTitle>

        <Typography variant="body1" sx={{ fontSize: '1.125rem', mb: 3 }}>
          Critical information for emergency situations. Keep this information up to date.
        </Typography>

        {patientInfo.emergency_contact_name ? (
          <EmergencyInfoCard>
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                  <Avatar
                    sx={{
                      bgcolor: alpha(theme.palette.error.main, 0.1),
                      color: theme.palette.error.main,
                      width: 56,
                      height: 56,
                      boxShadow: `0 4px 8px ${alpha(theme.palette.error.main, 0.15)}`,
                    }}
                  >
                    <ContactEmergencyIcon fontSize="large" />
                  </Avatar>
                  <Box>
                    <Typography variant="h6" fontWeight="600" gutterBottom>
                      Emergency Contact
                    </Typography>
                    <Typography variant="h5" fontWeight="500">
                      {patientInfo.emergency_contact_name}
                      {patientInfo.emergency_contact_relationship && (
                        <Typography component="span" sx={{ fontSize: '1.125rem', fontWeight: 400, ml: 1, color: 'text.secondary' }}>
                          ({patientInfo.emergency_contact_relationship})
                        </Typography>
                      )}
                    </Typography>

                    {patientInfo.emergency_contact_phone && (
                      <Box sx={{ display: 'flex', alignItems: 'center', mt: 2, gap: 1 }}>
                        <ContactPhoneIcon color="error" />
                        <Typography variant="h6">
                          {patientInfo.emergency_contact_phone}
                        </Typography>
                      </Box>
                    )}
                  </Box>
                </Box>
              </Grid>
              <Grid item xs={12} md={4} sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <Box sx={{
                  p: 2,
                  bgcolor: alpha(theme.palette.error.main, 0.1),
                  borderRadius: '12px',
                  border: `1px solid ${alpha(theme.palette.error.main, 0.2)}`,
                  width: '100%',
                  textAlign: 'center'
                }}>
                  <Typography variant="body1" fontWeight="500" color="error.main" gutterBottom>
                    In Case of Emergency
                  </Typography>
                  <Typography variant="body2">
                    Call emergency services first (911), then contact the person above.
                  </Typography>
                </Box>
              </Grid>
            </Grid>

            {patientInfo.blood_type && (
              <Box sx={{ mt: 3, pt: 3, borderTop: `1px dashed ${alpha(theme.palette.text.primary, 0.1)}` }}>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <BloodtypeIcon color="error" />
                      <Box>
                        <Typography variant="body2" color="text.secondary">Blood Type</Typography>
                        <Typography variant="h6" fontWeight="600">{patientInfo.blood_type}</Typography>
                      </Box>
                    </Box>
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <MedicalInformationIcon color="primary" />
                      <Box>
                        <Typography variant="body2" color="text.secondary">Allergies</Typography>
                        <Typography variant="h6" fontWeight="600">{patientInfo.allergies || 'None reported'}</Typography>
                      </Box>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            )}

            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                color="error"
                startIcon={<PrintIcon />}
                onClick={() => handlePrintEmergencyInfo(patientInfo)}
                sx={{
                  borderRadius: '8px',
                  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
                }}
              >
                Print Emergency Information
              </Button>
            </Box>
          </EmergencyInfoCard>
        ) : (
          <Alert
            severity="warning"
            sx={{
              borderRadius: '12px',
              '& .MuiAlert-icon': { fontSize: '1.5rem' }
            }}
          >
            <AlertTitle>No Emergency Contact Information</AlertTitle>
            <Typography variant="body1">
              No emergency contact information has been provided for this patient. This is critical information that should be updated as soon as possible.
            </Typography>
          </Alert>
        )}
      </Box>

      {/* Health Alerts Section */}
      <Box sx={{ mb: 5 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <SectionTitle variant="h5">Health Alerts</SectionTitle>
          <Badge
            badgeContent={healthAlerts.length}
            color="warning"
            sx={{
              '& .MuiBadge-badge': {
                fontSize: '0.9rem',
                height: '22px',
                minWidth: '22px',
                fontWeight: 'bold'
              }
            }}
          >
            <Chip
              icon={<NotificationsIcon />}
              label="Alerts"
              color="warning"
              variant="outlined"
              sx={{
                borderRadius: '8px',
                fontWeight: 500,
                px: 1
              }}
            />
          </Badge>
        </Box>

        <Typography variant="body1" sx={{ fontSize: '1.125rem', mb: 3 }}>
          Important notifications about {patientInfo.first_name}'s health that may require attention.
        </Typography>

        {healthAlerts.length > 0 ? (
          <HealthAlertsCard>
            <Box sx={{ mb: 3 }}>
              <Grid container spacing={2}>
                {/* Filter alerts by category */}
                <Grid item xs={6} sm={3}>
                  <Box
                    sx={{
                      p: 1.5,
                      bgcolor: alpha(theme.palette.error.main, 0.1),
                      borderRadius: '8px',
                      textAlign: 'center',
                      border: `1px solid ${alpha(theme.palette.error.main, 0.2)}`
                    }}
                  >
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Vital Signs
                    </Typography>
                    <Typography variant="h5" fontWeight="600" color="error.main">
                      {healthAlerts.filter(alert => alert.category === 'vital').length}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={6} sm={3}>
                  <Box
                    sx={{
                      p: 1.5,
                      bgcolor: alpha(theme.palette.warning.main, 0.1),
                      borderRadius: '8px',
                      textAlign: 'center',
                      border: `1px solid ${alpha(theme.palette.warning.main, 0.2)}`
                    }}
                  >
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Medications
                    </Typography>
                    <Typography variant="h5" fontWeight="600" color="warning.main">
                      {healthAlerts.filter(alert => alert.category === 'medication').length}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={6} sm={3}>
                  <Box
                    sx={{
                      p: 1.5,
                      bgcolor: alpha(theme.palette.info.main, 0.1),
                      borderRadius: '8px',
                      textAlign: 'center',
                      border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`
                    }}
                  >
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Appointments
                    </Typography>
                    <Typography variant="h5" fontWeight="600" color="info.main">
                      {healthAlerts.filter(alert => alert.category === 'appointment').length}
                    </Typography>
                  </Box>
                </Grid>

                <Grid item xs={6} sm={3}>
                  <Box
                    sx={{
                      p: 1.5,
                      bgcolor: alpha(theme.palette.success.main, 0.1),
                      borderRadius: '8px',
                      textAlign: 'center',
                      border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`
                    }}
                  >
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Preventive Care
                    </Typography>
                    <Typography variant="h5" fontWeight="600" color="success.main">
                      {healthAlerts.filter(alert => alert.category === 'preventive').length}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </Box>

            <AlertBanner
              title="Health Issues Requiring Attention"
              alerts={healthAlerts}
              sx={{
                mb: 0,
                bgcolor: 'transparent',
                border: 'none',
                p: 0
              }}
            />

            <Box sx={{ mt: 3, pt: 3, borderTop: `1px dashed ${alpha(theme.palette.text.primary, 0.1)}` }}>
              <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                These alerts are generated based on the most recent health data. Always consult with a healthcare professional for medical advice.
              </Typography>
            </Box>
          </HealthAlertsCard>
        ) : (
          <Alert
            severity="success"
            sx={{
              borderRadius: '12px',
              '& .MuiAlert-icon': { fontSize: '1.5rem' }
            }}
          >
            <AlertTitle>No Health Alerts</AlertTitle>
            <Typography variant="body1">
              There are currently no health alerts for {patientInfo.first_name}. All monitored health parameters appear to be within normal ranges.
            </Typography>
          </Alert>
        )}
      </Box>

      {/* Visit History Timeline Section */}
      <Box sx={{ mb: 5 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <SectionTitle variant="h5">Visit History Timeline</SectionTitle>
          <Tooltip title="View all visits">
            <Button
              variant="outlined"
              size="small"
              component={Link}
              to={`/kin/patients/${selectedPatientId}/visits`}
              startIcon={<HistoryIcon />}
              sx={{
                borderRadius: '10px',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
              }}
            >
              View All Visits
            </Button>
          </Tooltip>
        </Box>

        <Typography variant="body1" sx={{ fontSize: '1.125rem', mb: 3 }}>
          A chronological history of {patientInfo.first_name}'s medical visits, showing key health changes over time.
        </Typography>

        {visitTimelineItems.length > 0 ? (
          <TimelineCard>
            <Box sx={{ position: 'relative' }}>
              <TimelineConnector />

              {visitTimelineItems.slice(0, 3).map((item, index) => (
                <TimelineItem key={item.id}>
                  <TimelineDot sx={{
                    bgcolor: item.id === 'baseline'
                      ? alpha(theme.palette.success.main, 0.1)
                      : alpha(theme.palette.info.main, 0.1),
                    border: `2px solid ${item.id === 'baseline'
                      ? theme.palette.success.main
                      : theme.palette.info.main}`
                  }}>
                    {item.id === 'baseline'
                      ? <AssignmentIcon color="success" fontSize="large" />
                      : <CalendarTodayIcon color="info" fontSize="large" />}
                  </TimelineDot>

                  <Box sx={{
                    bgcolor: alpha(theme.palette.background.paper, 0.5),
                    borderRadius: '12px',
                    p: 2,
                    border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`,
                    ml: 3, // Increase margin to align better with the timeline dot
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)' // Add subtle shadow for better visibility
                  }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                      <Box>
                        <Typography variant="h6" fontWeight="600">
                          {formatDate(item.date)}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {item.doctor} • {item.reason}
                        </Typography>
                      </Box>
                      <Chip
                        label={item.id === 'baseline' ? 'Baseline' : `Visit #${visitTimelineItems.length - index}`}
                        size="small"
                        sx={{
                          bgcolor: item.id === 'baseline'
                            ? alpha(theme.palette.success.main, 0.1)
                            : alpha(theme.palette.info.main, 0.1),
                          color: item.id === 'baseline'
                            ? theme.palette.success.main
                            : theme.palette.info.main,
                          fontWeight: 500
                        }}
                      />
                    </Box>

                    {item.keyFindings.length > 0 && (
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle1" fontWeight="600" gutterBottom>
                          Key Health Metrics
                        </Typography>
                        <Grid container spacing={2}>
                          {item.keyFindings.map((finding, idx) => (
                            <Grid item xs={12} sm={6} md={3} key={idx}>
                              <Box sx={{
                                p: 1.5,
                                bgcolor: alpha(theme.palette.background.default, 0.7),
                                borderRadius: '8px',
                                border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                              }}>
                                <Typography variant="body2" color="text.secondary" gutterBottom>
                                  {finding.name}
                                </Typography>
                                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                  <Typography variant="h6" fontWeight="600">
                                    {finding.value} {finding.unit}
                                  </Typography>

                                  {finding.change && (
                                    <Box sx={{
                                      display: 'flex',
                                      alignItems: 'center',
                                      color: finding.change.isPositive ? theme.palette.success.main : theme.palette.error.main
                                    }}>
                                      {finding.change.direction === 'up' && <TrendingUpIcon fontSize="small" />}
                                      {finding.change.direction === 'down' && <TrendingDownIcon fontSize="small" />}
                                      {finding.change.direction === 'stable' && <TrendingFlatIcon fontSize="small" />}
                                      <Typography
                                        variant="body2"
                                        fontWeight="600"
                                        sx={{ ml: 0.5 }}
                                      >
                                        {finding.change.value}
                                      </Typography>
                                    </Box>
                                  )}
                                </Box>
                              </Box>
                            </Grid>
                          ))}
                        </Grid>
                      </Box>
                    )}

                    {item.medications && item.medications.length > 0 && (
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="subtitle1" fontWeight="600" gutterBottom>
                          Medication Changes
                        </Typography>
                        <Box sx={{
                          p: 1.5,
                          bgcolor: alpha(theme.palette.background.default, 0.7),
                          borderRadius: '8px',
                          border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                        }}>
                          {item.medications.map((med, idx) => (
                            <Chip
                              key={idx}
                              icon={<MedicationIcon />}
                              label={med.name}
                              variant="outlined"
                              color={med.action === 'added' ? 'success' : med.action === 'discontinued' ? 'error' : 'warning'}
                              sx={{ m: 0.5 }}
                            />
                          ))}
                        </Box>
                      </Box>
                    )}

                    {item.notes && (
                      <Box>
                        <Typography variant="subtitle1" fontWeight="600" gutterBottom>
                          Visit Notes
                        </Typography>
                        <Typography variant="body2" sx={{
                          p: 1.5,
                          bgcolor: alpha(theme.palette.background.default, 0.7),
                          borderRadius: '8px',
                          border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                        }}>
                          {item.notes}
                        </Typography>
                      </Box>
                    )}

                    <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
                      <Button
                        variant="text"
                        size="small"
                        component={Link}
                        to={item.id === 'baseline'
                          ? `/kin/dashboard`
                          : `/kin/patients/${selectedPatientId}/visits/${item.id}`}
                        endIcon={<ArrowForwardIcon />}
                        sx={{ fontWeight: 500 }}
                      >
                        {item.id === 'baseline' ? 'Back to Dashboard' : 'View Visit Details'}
                      </Button>
                    </Box>
                  </Box>
                </TimelineItem>
              ))}

              {visitTimelineItems.length > 3 && (
                <Box sx={{ textAlign: 'center', mt: 2, position: 'relative', zIndex: 2 }}>
                  <Button
                    variant="outlined"
                    component={Link}
                    to={`/kin/patients/${selectedPatientId}/visits`}
                    startIcon={<HistoryIcon />}
                    sx={{ borderRadius: '20px' }}
                  >
                    View All {visitTimelineItems.length} Visits
                  </Button>
                </Box>
              )}
            </Box>
          </TimelineCard>
        ) : (
          <Alert
            severity="info"
            sx={{
              borderRadius: '12px',
              '& .MuiAlert-icon': { fontSize: '1.5rem' }
            }}
          >
            <AlertTitle>No Visit History</AlertTitle>
            <Typography variant="body1">
              There are no recorded medical visits for {patientInfo.first_name} yet. After the first visit, a timeline will be displayed here showing health changes over time.
            </Typography>
          </Alert>
        )}
      </Box>

      {/* Health Trends Section */}
      <Box sx={{ mb: 5 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <SectionTitle variant="h5">Health Trends</SectionTitle>
          <Tooltip title="Refresh health data">
            <IconButton
              color="primary"
              onClick={handleRefresh}
              disabled={refreshing}
              size="small"
            >
              {refreshing ? <CircularProgress size={20} /> : <RefreshIcon />}
            </IconButton>
          </Tooltip>
        </Box>

        {combinedData.length >= 2 ? (
          <Box>
            <Typography variant="body1" sx={{ fontSize: '1.125rem', mb: 3 }}>
              These charts show how your family member's health measurements have changed over time, including data from their initial registration
              and all subsequent visits. This helps you and their doctor track progress and identify any areas that need attention.
            </Typography>
            <Paper
              elevation={0}
              sx={{
                p: 3,
                bgcolor: alpha(theme.palette.primary.light, 0.05),
                borderRadius: '16px',
                mb: 3,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
              }}
            >
              <HealthMetricsChart visits={combinedData} />
            </Paper>
          </Box>
        ) : visits.length >= 1 && patientData ? (
          <Box>
            <Typography variant="body1" sx={{ fontSize: '1.125rem', mb: 3 }}>
              These charts show how your family member's health measurements have changed over time, including data from their initial registration
              and all subsequent visits. This helps you and their doctor track progress and identify any areas that need attention.
            </Typography>
            <Paper
              elevation={0}
              sx={{
                p: 3,
                bgcolor: alpha(theme.palette.primary.light, 0.05),
                borderRadius: '16px',
                mb: 3,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`
              }}
            >
              <HealthMetricsChart visits={combinedData.length > 0 ? combinedData : visits} />
            </Paper>
          </Box>
        ) : visitsLoading ? (
          <Box sx={{ p: 4, height: '200px' }}>
            <LoadingSpinner size="medium" message="Loading health trends..." />
          </Box>
        ) : (
          <Alert
            severity="info"
            sx={{
              mb: 3,
              borderRadius: '12px',
              '& .MuiAlert-icon': {
                fontSize: '1.5rem'
              }
            }}
          >
            <AlertTitle>No Trend Data Available</AlertTitle>
            Not enough visit data available to show health trends. At least two visits are needed to display trends.
          </Alert>
        )}
      </Box>

      {/* Recent Visits Section */}
      <Paper
        elevation={0}
        sx={{
          mb: 5,
          p: 3,
          borderRadius: '16px',
          border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box>
            <SectionTitle variant="h5">Recent Medical Visits</SectionTitle>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Latest healthcare appointments and check-ups
            </Typography>
          </Box>
          <ActionButton
            variant="outlined"
            size="small"
            component={Link}
            to={`/kin/patients/${selectedPatientId}/visits`}
            startIcon={<EventNoteIcon />}
            sx={{
              borderRadius: '10px',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
            }}
          >
            View All Visits
          </ActionButton>
        </Box>

        {recentVisits && recentVisits.length > 0 ? (
          <Grid container spacing={3}>
            {recentVisits.map((visit: any) => (
              <Grid item xs={12} key={visit.visit_id}>
                <VisitCard>
                  <CardContent sx={{ p: 3 }}>
                    <Grid container spacing={2}>
                      <Grid item xs={12} md={8}>
                        <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                          <Avatar
                            sx={{
                              bgcolor: alpha(theme.palette.primary.main, 0.1),
                              color: theme.palette.primary.main,
                            }}
                          >
                            <EventNoteIcon />
                          </Avatar>
                          <Box>
                            <Typography variant="h6" fontWeight="600">
                              {visit.visit_reason || 'Medical Visit'}
                            </Typography>
                            <Box sx={{ display: 'flex', alignItems: 'center', flexWrap: 'wrap', gap: 2, mt: 1 }}>
                              <Chip
                                icon={<CalendarTodayIcon />}
                                label={formatDate(visit.visit_date)}
                                size="small"
                                sx={{
                                  bgcolor: alpha(theme.palette.primary.main, 0.1),
                                  color: theme.palette.primary.main,
                                  fontWeight: 500,
                                  borderRadius: '8px',
                                }}
                              />
                              {visit.doctor_name && (
                                <Chip
                                  icon={<LocalHospitalIcon />}
                                  label={`Dr. ${visit.doctor_name}`}
                                  size="small"
                                  sx={{
                                    bgcolor: alpha(theme.palette.info.main, 0.1),
                                    color: theme.palette.info.main,
                                    fontWeight: 500,
                                    borderRadius: '8px',
                                  }}
                                />
                              )}
                            </Box>
                          </Box>
                        </Box>

                        {visit.diagnosis && (
                          <Box sx={{ mt: 3, ml: 7 }}>
                            <Typography variant="subtitle2" color="text.secondary" fontWeight="500">Diagnosis</Typography>
                            <Typography variant="body1" sx={{ mt: 0.5 }}>{visit.diagnosis}</Typography>
                          </Box>
                        )}
                      </Grid>

                      <Grid item xs={12} md={4} sx={{ display: 'flex', flexDirection: 'column', justifyContent: 'space-between' }}>
                        <Box sx={{ display: 'flex', justifyContent: { xs: 'flex-start', md: 'flex-end' } }}>
                          <ActionButton
                            variant="contained"
                            size="small"
                            component={Link}
                            to={`/kin/patients/${selectedPatientId}/visits/${visit.visit_id}`}
                            endIcon={<ArrowForwardIcon />}
                            sx={{
                              borderRadius: '10px',
                              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                              mt: { xs: 2, md: 0 },
                            }}
                          >
                            View Details
                          </ActionButton>
                        </Box>
                      </Grid>
                    </Grid>

                    <Divider sx={{ my: 3 }} />

                    <Typography variant="subtitle2" color="text.secondary" fontWeight="500" sx={{ mb: 2 }}>Key Measurements</Typography>
                    <Grid container spacing={2}>
                      {visit.vital_signs?.blood_pressure && (
                        <Grid item xs={6} sm={4} md={3}>
                          <Box sx={{
                            p: 2,
                            bgcolor: alpha(theme.palette.primary.main, 0.05),
                            borderRadius: '12px',
                            height: '100%',
                            transition: 'transform 0.2s ease-in-out',
                            '&:hover': {
                              transform: 'translateY(-3px)',
                              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
                            }
                          }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <MonitorHeartIcon color="primary" sx={{ mr: 1, fontSize: 20 }} />
                              <Typography variant="body2" color="text.secondary">Blood Pressure</Typography>
                            </Box>
                            <Typography variant="body1" fontWeight="600">{visit.vital_signs.blood_pressure} mmHg</Typography>
                          </Box>
                        </Grid>
                      )}
                      {visit.vital_signs?.heart_rate && (
                        <Grid item xs={6} sm={4} md={3}>
                          <Box sx={{
                            p: 2,
                            bgcolor: alpha(theme.palette.primary.main, 0.05),
                            borderRadius: '12px',
                            height: '100%',
                            transition: 'transform 0.2s ease-in-out',
                            '&:hover': {
                              transform: 'translateY(-3px)',
                              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
                            }
                          }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <FavoriteIcon color="error" sx={{ mr: 1, fontSize: 20 }} />
                              <Typography variant="body2" color="text.secondary">Heart Rate</Typography>
                            </Box>
                            <Typography variant="body1" fontWeight="600">{visit.vital_signs.heart_rate} bpm</Typography>
                          </Box>
                        </Grid>
                      )}
                      {visit.vital_signs?.temperature && (
                        <Grid item xs={6} sm={4} md={3}>
                          <Box sx={{
                            p: 2,
                            bgcolor: alpha(theme.palette.primary.main, 0.05),
                            borderRadius: '12px',
                            height: '100%',
                            transition: 'transform 0.2s ease-in-out',
                            '&:hover': {
                              transform: 'translateY(-3px)',
                              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
                            }
                          }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <ThermostatIcon color="warning" sx={{ mr: 1, fontSize: 20 }} />
                              <Typography variant="body2" color="text.secondary">Temperature</Typography>
                            </Box>
                            <Typography variant="body1" fontWeight="600">{visit.vital_signs.temperature} °C</Typography>
                          </Box>
                        </Grid>
                      )}
                      {visit.vital_signs?.weight && (
                        <Grid item xs={6} sm={4} md={3}>
                          <Box sx={{
                            p: 2,
                            bgcolor: alpha(theme.palette.primary.main, 0.05),
                            borderRadius: '12px',
                            height: '100%',
                            transition: 'transform 0.2s ease-in-out',
                            '&:hover': {
                              transform: 'translateY(-3px)',
                              boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)',
                            }
                          }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                              <ScaleIcon color="info" sx={{ mr: 1, fontSize: 20 }} />
                              <Typography variant="body2" color="text.secondary">Weight</Typography>
                            </Box>
                            <Typography variant="body1" fontWeight="600">{visit.vital_signs.weight} kg</Typography>
                          </Box>
                        </Grid>
                      )}
                    </Grid>
                  </CardContent>
                </VisitCard>
              </Grid>
            ))}
          </Grid>
        ) : (
          <Alert
            severity="info"
            sx={{
              borderRadius: '12px',
              '& .MuiAlert-icon': {
                fontSize: '1.5rem'
              }
            }}
          >
            <AlertTitle>No Recent Visits</AlertTitle>
            <Typography variant="body2">
              No recent medical visits have been recorded for this patient. When visits are added, they will appear here.
            </Typography>
            <Box sx={{ mt: 2 }}>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<CalendarTodayIcon />}
                component={Link}
                to={`/kin/patients/${selectedPatientId}/schedule-appointment`}
                size="small"
              >
                Schedule First Appointment
              </Button>
            </Box>
          </Alert>
        )}
      </Paper>

      {/* Medications Section */}
      <Paper
        elevation={0}
        sx={{
          mb: 5,
          p: 3,
          borderRadius: '16px',
          border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.05)',
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Box>
            <SectionTitle variant="h5">Medication Management</SectionTitle>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Comprehensive medication tracking, schedules, and alerts
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <ActionButton
              variant="outlined"
              size="small"
              onClick={() => handlePrintMedicationList(medications)}
              startIcon={<PrintIcon />}
              sx={{
                borderRadius: '10px',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
              }}
            >
              Print Medication List
            </ActionButton>
            <ActionButton
              variant="outlined"
              size="small"
              component={Link}
              to={`/kin/patients/${selectedPatientId}/medications`}
              startIcon={<MedicationIcon />}
              sx={{
                borderRadius: '10px',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
              }}
            >
              View All Medications
            </ActionButton>
          </Box>
        </Box>

        {/* Medication Interactions Summary */}
        {medications && medications.length > 0 && (
          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" fontWeight="600" sx={{ mb: 2 }}>
              Medication Interactions Summary
            </Typography>
            <Grid container spacing={3}>
              {/* Interaction Warnings */}
              <Grid item xs={12} md={6}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 3,
                    borderRadius: '12px',
                    border: `1px solid ${alpha(theme.palette.warning.main, 0.2)}`,
                    bgcolor: alpha(theme.palette.warning.light, 0.05),
                    height: '100%'
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar
                      sx={{
                        bgcolor: alpha(theme.palette.warning.main, 0.1),
                        color: theme.palette.warning.main,
                        mr: 2
                      }}
                    >
                      <WarningIcon />
                    </Avatar>
                    <Box>
                      <Typography variant="h6" fontWeight="600">Potential Interactions</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Medications that may interact with each other
                      </Typography>
                    </Box>
                  </Box>

                  {(() => {
                    // Find all medications with interactions
                    const medsWithInteractions = medications.filter(
                      (med: any) => med.interactions && med.interactions.length > 0
                    );

                    if (medsWithInteractions.length === 0) {
                      return (
                        <Alert severity="success" sx={{ mt: 2 }}>
                          No potential medication interactions detected.
                        </Alert>
                      );
                    }

                    return (
                      <List sx={{ mt: 1 }}>
                        {medsWithInteractions.map((med: any, idx: number) => (
                          <ListItem
                            key={idx}
                            sx={{
                              px: 2,
                              py: 1,
                              mb: 1,
                              bgcolor: alpha(theme.palette.background.paper, 0.5),
                              borderRadius: '8px',
                              border: `1px solid ${alpha(theme.palette.warning.main, 0.1)}`
                            }}
                          >
                            <ListItemText
                              primary={
                                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                  <Typography fontWeight="600">{med.name}</Typography>
                                  <Chip
                                    size="small"
                                    label={`${med.interactions.length} interaction${med.interactions.length > 1 ? 's' : ''}`}
                                    color="warning"
                                    sx={{ ml: 1, height: 20, fontSize: '0.7rem' }}
                                  />
                                </Box>
                              }
                              secondary={
                                <Box sx={{ mt: 1 }}>
                                  <Typography variant="body2" color="text.secondary" gutterBottom>
                                    May interact with:
                                  </Typography>
                                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                    {med.interactions.map((interaction: string, i: number) => (
                                      <Chip
                                        key={i}
                                        label={interaction}
                                        size="small"
                                        variant="outlined"
                                        color="warning"
                                        sx={{ borderRadius: '4px' }}
                                      />
                                    ))}
                                  </Box>
                                </Box>
                              }
                            />
                          </ListItem>
                        ))}
                      </List>
                    );
                  })()}
                </Paper>
              </Grid>

              {/* Adherence Overview */}
              <Grid item xs={12} md={6}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 3,
                    borderRadius: '12px',
                    border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`,
                    bgcolor: alpha(theme.palette.info.light, 0.05),
                    height: '100%'
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar
                      sx={{
                        bgcolor: alpha(theme.palette.info.main, 0.1),
                        color: theme.palette.info.main,
                        mr: 2
                      }}
                    >
                      <CheckCircleIcon />
                    </Avatar>
                    <Box>
                      <Typography variant="h6" fontWeight="600">Medication Adherence</Typography>
                      <Typography variant="body2" color="text.secondary">
                        Overall adherence to medication schedule
                      </Typography>
                    </Box>
                  </Box>

                  {(() => {
                    // Calculate average adherence score
                    const totalScore = medications.reduce((sum: number, med: any) =>
                      sum + (med.adherenceScore || 0), 0);
                    const avgScore = Math.round(totalScore / medications.length);

                    // Determine status based on average score
                    let status = 'success';
                    let statusText = 'Excellent';

                    if (avgScore < 75) {
                      status = 'warning';
                      statusText = 'Needs Improvement';
                    } else if (avgScore < 90) {
                      status = 'info';
                      statusText = 'Good';
                    }

                    return (
                      <Box>
                        <Box sx={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'center',
                          mb: 1
                        }}>
                          <Typography variant="body1" fontWeight="500">
                            Overall Adherence
                          </Typography>
                          <Chip
                            label={statusText}
                            color={status as "success" | "info" | "warning"}
                            size="small"
                          />
                        </Box>

                        <Box sx={{ position: 'relative', mb: 3 }}>
                          <LinearProgress
                            variant="determinate"
                            value={avgScore}
                            sx={{
                              height: 12,
                              borderRadius: 6,
                              bgcolor: alpha(theme.palette.grey[300], 0.5),
                              '& .MuiLinearProgress-bar': {
                                bgcolor: status === 'success'
                                  ? theme.palette.success.main
                                  : status === 'info'
                                    ? theme.palette.info.main
                                    : theme.palette.warning.main
                              }
                            }}
                          />
                          <Typography
                            variant="body2"
                            sx={{
                              position: 'absolute',
                              right: 0,
                              top: -20,
                              fontWeight: 'bold',
                              color: status === 'success'
                                ? theme.palette.success.main
                                : status === 'info'
                                  ? theme.palette.info.main
                                  : theme.palette.warning.main
                            }}
                          >
                            {avgScore}%
                          </Typography>
                        </Box>

                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          Medications with lowest adherence:
                        </Typography>

                        {/* Show medications with lowest adherence */}
                        {[...medications]
                          .sort((a, b) => (a.adherenceScore || 100) - (b.adherenceScore || 100))
                          .slice(0, 2)
                          .map((med, idx) => (
                            <Box
                              key={idx}
                              sx={{
                                p: 1.5,
                                mb: 1,
                                bgcolor: alpha(theme.palette.background.paper, 0.5),
                                borderRadius: '8px',
                                border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                              }}
                            >
                              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                                <Typography variant="body2" fontWeight="500">
                                  {med.name}
                                </Typography>
                                <Typography
                                  variant="body2"
                                  fontWeight="bold"
                                  color={med.adherenceScore > 90
                                    ? theme.palette.success.main
                                    : med.adherenceScore > 75
                                      ? theme.palette.info.main
                                      : theme.palette.warning.main}
                                >
                                  {med.adherenceScore}%
                                </Typography>
                              </Box>
                              <LinearProgress
                                variant="determinate"
                                value={med.adherenceScore}
                                sx={{
                                  height: 6,
                                  borderRadius: 3,
                                  bgcolor: alpha(theme.palette.grey[300], 0.5),
                                  '& .MuiLinearProgress-bar': {
                                    bgcolor: med.adherenceScore > 90
                                      ? theme.palette.success.main
                                      : med.adherenceScore > 75
                                        ? theme.palette.info.main
                                        : theme.palette.warning.main
                                  }
                                }}
                              />
                            </Box>
                          ))
                        }
                      </Box>
                    );
                  })()}
                </Paper>
              </Grid>
            </Grid>
          </Box>
        )}

        {/* Medication Timeline */}
        {medications && medications.length > 0 && (
          <Box sx={{ mb: 4 }}>
            <Typography variant="h6" fontWeight="600" sx={{ mb: 2 }}>
              Daily Medication Schedule
            </Typography>
            <Paper
              elevation={0}
              sx={{
                p: 3,
                borderRadius: '12px',
                border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                bgcolor: alpha(theme.palette.background.paper, 0.7)
              }}
            >
              <Box sx={{ position: 'relative', mb: 2 }}>
                <Box sx={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  mb: 1,
                  px: 2
                }}>
                  <Typography variant="body2" color="text.secondary">Morning</Typography>
                  <Typography variant="body2" color="text.secondary">Afternoon</Typography>
                  <Typography variant="body2" color="text.secondary">Evening</Typography>
                  <Typography variant="body2" color="text.secondary">Bedtime</Typography>
                </Box>
                <Box sx={{
                  height: '8px',
                  bgcolor: alpha(theme.palette.grey[300], 0.5),
                  borderRadius: '4px',
                  position: 'relative',
                  mb: 3
                }}>
                  {/* Time markers */}
                  <Box sx={{
                    position: 'absolute',
                    left: '0%',
                    top: 0,
                    height: '100%',
                    width: '2px',
                    bgcolor: theme.palette.primary.main
                  }} />
                  <Box sx={{
                    position: 'absolute',
                    left: '33%',
                    top: 0,
                    height: '100%',
                    width: '2px',
                    bgcolor: theme.palette.primary.main
                  }} />
                  <Box sx={{
                    position: 'absolute',
                    left: '66%',
                    top: 0,
                    height: '100%',
                    width: '2px',
                    bgcolor: theme.palette.primary.main
                  }} />
                  <Box sx={{
                    position: 'absolute',
                    left: '100%',
                    top: 0,
                    height: '100%',
                    width: '2px',
                    bgcolor: theme.palette.primary.main
                  }} />

                  {/* Current time indicator */}
                  {(() => {
                    const now = new Date();
                    const hour = now.getHours();
                    let position = 0;

                    if (hour >= 6 && hour < 12) {
                      // Morning (6am-12pm)
                      position = ((hour - 6) / 6) * 33;
                    } else if (hour >= 12 && hour < 18) {
                      // Afternoon (12pm-6pm)
                      position = 33 + ((hour - 12) / 6) * 33;
                    } else if (hour >= 18 && hour < 22) {
                      // Evening (6pm-10pm)
                      position = 66 + ((hour - 18) / 4) * 33;
                    } else if (hour >= 22 || hour < 6) {
                      // Bedtime (10pm-6am)
                      position = hour >= 22 ? 100 : 0;
                    }

                    return (
                      <Box sx={{
                        position: 'absolute',
                        left: `${position}%`,
                        top: '-8px',
                        width: '24px',
                        height: '24px',
                        borderRadius: '50%',
                        bgcolor: theme.palette.error.main,
                        border: `2px solid white`,
                        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.2)',
                        transform: 'translateX(-50%)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        zIndex: 2
                      }}>
                        <AccessTimeIcon sx={{ color: 'white', fontSize: '14px' }} />
                      </Box>
                    );
                  })()}
                </Box>

                {/* Medication schedule items */}
                {medications.map((med: any, index: number) => (
                  <Box key={index} sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <Avatar
                        sx={{
                          width: 32,
                          height: 32,
                          bgcolor: alpha(theme.palette.primary.main, 0.1),
                          color: theme.palette.primary.main,
                          mr: 1,
                          fontSize: '0.8rem'
                        }}
                      >
                        <MedicationIcon fontSize="small" />
                      </Avatar>
                      <Typography variant="body1" fontWeight="500">
                        {med.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                        {med.dosage}
                      </Typography>
                    </Box>

                    <Box sx={{
                      display: 'flex',
                      position: 'relative',
                      height: '40px',
                      bgcolor: alpha(theme.palette.grey[100], 0.5),
                      borderRadius: '20px',
                      overflow: 'hidden'
                    }}>
                      {/* Morning dose */}
                      {med.schedule?.morning && (
                        <Box sx={{
                          position: 'absolute',
                          left: '0%',
                          width: '33%',
                          height: '100%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}>
                          <Chip
                            icon={<CheckCircleIcon />}
                            label="Morning"
                            size="small"
                            color="primary"
                            sx={{
                              bgcolor: alpha(theme.palette.primary.main, 0.1),
                              borderRadius: '12px'
                            }}
                          />
                        </Box>
                      )}

                      {/* Afternoon dose */}
                      {med.schedule?.afternoon && (
                        <Box sx={{
                          position: 'absolute',
                          left: '33%',
                          width: '33%',
                          height: '100%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}>
                          <Chip
                            icon={<CheckCircleIcon />}
                            label="Afternoon"
                            size="small"
                            color="info"
                            sx={{
                              bgcolor: alpha(theme.palette.info.main, 0.1),
                              borderRadius: '12px'
                            }}
                          />
                        </Box>
                      )}

                      {/* Evening dose */}
                      {med.schedule?.evening && (
                        <Box sx={{
                          position: 'absolute',
                          left: '66%',
                          width: '33%',
                          height: '100%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}>
                          <Chip
                            icon={<CheckCircleIcon />}
                            label="Evening"
                            size="small"
                            color="secondary"
                            sx={{
                              bgcolor: alpha(theme.palette.secondary.main, 0.1),
                              borderRadius: '12px'
                            }}
                          />
                        </Box>
                      )}

                      {/* Bedtime dose */}
                      {med.schedule?.bedtime && (
                        <Box sx={{
                          position: 'absolute',
                          left: '90%',
                          transform: 'translateX(-50%)',
                          height: '100%',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}>
                          <Chip
                            icon={<CheckCircleIcon />}
                            label="Bedtime"
                            size="small"
                            color="warning"
                            sx={{
                              bgcolor: alpha(theme.palette.warning.main, 0.1),
                              borderRadius: '12px'
                            }}
                          />
                        </Box>
                      )}
                    </Box>
                  </Box>
                ))}
              </Box>

              <Box sx={{
                mt: 3,
                pt: 2,
                borderTop: `1px dashed ${alpha(theme.palette.divider, 0.5)}`,
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <Typography variant="body2" color="text.secondary" sx={{ fontStyle: 'italic' }}>
                  Next dose: {(() => {
                    // Find the next medication to take
                    const now = new Date();
                    const hour = now.getHours();

                    // Find medications with upcoming doses
                    const upcomingMeds = medications.filter((med: any) => {
                      if (hour < 9 && med.schedule?.morning) return true;
                      if (hour < 13 && med.schedule?.afternoon) return true;
                      if (hour < 18 && med.schedule?.evening) return true;
                      if (hour < 22 && med.schedule?.bedtime) return true;
                      return false;
                    });

                    if (upcomingMeds.length > 0) {
                      return upcomingMeds[0].nextDose;
                    } else {
                      return "Tomorrow morning";
                    }
                  })()}
                </Typography>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<NotificationsIcon />}
                  sx={{ borderRadius: '8px' }}
                >
                  Set Reminders
                </Button>
              </Box>
            </Paper>
          </Box>
        )}

        {medications && medications.length > 0 ? (
          <Grid container spacing={3}>
            {medications.map((medication: any, index: number) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Paper
                  elevation={0}
                  sx={{
                    p: 3,
                    borderRadius: '16px',
                    bgcolor: '#ffffff',
                    border: '1px solid',
                    borderColor: alpha(theme.palette.primary.main, 0.1),
                    transition: 'all 0.25s ease-in-out',
                    position: 'relative',
                    overflow: 'hidden',
                    '&:hover': {
                      transform: 'translateY(-5px)',
                      boxShadow: '0 8px 25px rgba(0, 0, 0, 0.1)',
                      borderColor: alpha(theme.palette.primary.main, 0.2),
                    },
                    '&::before': {
                      content: '""',
                      position: 'absolute',
                      top: 0,
                      left: 0,
                      width: '100%',
                      height: '4px',
                      background: medication.isLow
                        ? `linear-gradient(90deg, ${theme.palette.error.main}, ${theme.palette.error.light})`
                        : `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.primary.light})`,
                    },
                  }}
                >
                  {medication.isLow && (
                    <Box sx={{
                      position: 'absolute',
                      top: 12,
                      right: 12,
                      bgcolor: alpha(theme.palette.error.main, 0.1),
                      color: theme.palette.error.main,
                      borderRadius: '8px',
                      px: 1.5,
                      py: 0.5,
                      fontSize: '0.75rem',
                      fontWeight: 'bold',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 0.5
                    }}>
                      <WarningIcon fontSize="small" />
                      Refill Soon
                    </Box>
                  )}

                  <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 3 }}>
                    <Avatar
                      sx={{
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                        color: theme.palette.primary.main,
                        mr: 2,
                        width: 48,
                        height: 48,
                        boxShadow: `0 4px 8px ${alpha(theme.palette.primary.main, 0.15)}`,
                      }}
                    >
                      <MedicationIcon />
                    </Avatar>
                    <Box>
                      <Typography variant="h6" fontWeight="600">{medication.name}</Typography>
                      <Chip
                        size="small"
                        label={medication.dosage}
                        sx={{
                          mt: 0.5,
                          bgcolor: alpha(theme.palette.primary.main, 0.1),
                          color: theme.palette.primary.main,
                          fontWeight: 500,
                          borderRadius: '8px',
                        }}
                      />
                    </Box>
                  </Box>

                  {/* Medication Schedule */}
                  {medication.schedule && (
                    <Box sx={{ mb: 3 }}>
                      <Typography variant="body2" color="text.secondary" fontWeight="500" sx={{ mb: 1 }}>
                        Daily Schedule
                      </Typography>
                      <Box sx={{
                        display: 'flex',
                        justifyContent: 'space-between',
                        bgcolor: alpha(theme.palette.background.default, 0.5),
                        borderRadius: '12px',
                        p: 1.5,
                        border: '1px solid',
                        borderColor: alpha(theme.palette.primary.main, 0.1)
                      }}>
                        <Box sx={{
                          textAlign: 'center',
                          p: 1,
                          borderRadius: '8px',
                          bgcolor: medication.schedule.morning
                            ? alpha(theme.palette.primary.main, 0.1)
                            : 'transparent',
                          color: medication.schedule.morning
                            ? theme.palette.primary.main
                            : alpha(theme.palette.text.primary, 0.4),
                          fontWeight: medication.schedule.morning ? 'bold' : 'normal',
                          flex: 1
                        }}>
                          <Typography variant="body2">Morning</Typography>
                          <Box sx={{ mt: 1 }}>
                            {medication.schedule.morning ? <CheckCircleIcon color="primary" /> : '—'}
                          </Box>
                        </Box>

                        <Box sx={{
                          textAlign: 'center',
                          p: 1,
                          borderRadius: '8px',
                          bgcolor: medication.schedule.afternoon
                            ? alpha(theme.palette.primary.main, 0.1)
                            : 'transparent',
                          color: medication.schedule.afternoon
                            ? theme.palette.primary.main
                            : alpha(theme.palette.text.primary, 0.4),
                          fontWeight: medication.schedule.afternoon ? 'bold' : 'normal',
                          flex: 1
                        }}>
                          <Typography variant="body2">Afternoon</Typography>
                          <Box sx={{ mt: 1 }}>
                            {medication.schedule.afternoon ? <CheckCircleIcon color="primary" /> : '—'}
                          </Box>
                        </Box>

                        <Box sx={{
                          textAlign: 'center',
                          p: 1,
                          borderRadius: '8px',
                          bgcolor: medication.schedule.evening
                            ? alpha(theme.palette.primary.main, 0.1)
                            : 'transparent',
                          color: medication.schedule.evening
                            ? theme.palette.primary.main
                            : alpha(theme.palette.text.primary, 0.4),
                          fontWeight: medication.schedule.evening ? 'bold' : 'normal',
                          flex: 1
                        }}>
                          <Typography variant="body2">Evening</Typography>
                          <Box sx={{ mt: 1 }}>
                            {medication.schedule.evening ? <CheckCircleIcon color="primary" /> : '—'}
                          </Box>
                        </Box>

                        <Box sx={{
                          textAlign: 'center',
                          p: 1,
                          borderRadius: '8px',
                          bgcolor: medication.schedule.bedtime
                            ? alpha(theme.palette.primary.main, 0.1)
                            : 'transparent',
                          color: medication.schedule.bedtime
                            ? theme.palette.primary.main
                            : alpha(theme.palette.text.primary, 0.4),
                          fontWeight: medication.schedule.bedtime ? 'bold' : 'normal',
                          flex: 1
                        }}>
                          <Typography variant="body2">Bedtime</Typography>
                          <Box sx={{ mt: 1 }}>
                            {medication.schedule.bedtime ? <CheckCircleIcon color="primary" /> : '—'}
                          </Box>
                        </Box>
                      </Box>
                    </Box>
                  )}

                  <Box sx={{
                    p: 2,
                    bgcolor: alpha(theme.palette.background.default, 0.5),
                    borderRadius: '12px',
                    mb: 2,
                    border: '1px solid',
                    borderColor: alpha(theme.palette.primary.main, 0.1)
                  }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <AccessTimeIcon color="primary" sx={{ mr: 1, fontSize: 18 }} />
                      <Typography variant="body2" color="text.secondary" fontWeight="500">
                        Frequency
                      </Typography>
                    </Box>
                    <Typography variant="body1" fontWeight="600">
                      {medication.frequency}
                    </Typography>
                  </Box>

                  {medication.instructions && (
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary" fontWeight="500" sx={{ mb: 0.5 }}>
                        Instructions
                      </Typography>
                      <Typography variant="body2" sx={{
                        p: 1.5,
                        bgcolor: alpha(theme.palette.background.default, 0.3),
                        borderRadius: '8px',
                        border: `1px dashed ${alpha(theme.palette.text.secondary, 0.2)}`,
                      }}>
                        {medication.instructions}
                      </Typography>
                    </Box>
                  )}

                  {/* Medication Interactions */}
                  {medication.interactions && medication.interactions.length > 0 && (
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary" fontWeight="500" sx={{ mb: 0.5 }}>
                        Potential Interactions
                      </Typography>
                      <Box sx={{
                        p: 1.5,
                        bgcolor: alpha(theme.palette.warning.light, 0.1),
                        borderRadius: '8px',
                        border: `1px solid ${alpha(theme.palette.warning.main, 0.2)}`,
                      }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <WarningIcon color="warning" sx={{ mr: 1, fontSize: 18 }} />
                          <Typography variant="body2" color="warning.main" fontWeight="500">
                            May interact with other medications
                          </Typography>
                        </Box>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {medication.interactions.map((interaction: string, idx: number) => (
                            <Chip
                              key={idx}
                              label={interaction}
                              size="small"
                              color="warning"
                              variant="outlined"
                              sx={{ borderRadius: '4px' }}
                            />
                          ))}
                        </Box>
                      </Box>
                    </Box>
                  )}

                  {/* Medication Adherence */}
                  {medication.adherenceScore !== undefined && (
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary" fontWeight="500" sx={{ mb: 0.5 }}>
                        Adherence Score
                      </Typography>
                      <Box sx={{
                        p: 1.5,
                        bgcolor: alpha(
                          medication.adherenceScore > 90
                            ? theme.palette.success.light
                            : medication.adherenceScore > 75
                              ? theme.palette.info.light
                              : theme.palette.warning.light,
                          0.1
                        ),
                        borderRadius: '8px',
                        border: `1px solid ${alpha(
                          medication.adherenceScore > 90
                            ? theme.palette.success.main
                            : medication.adherenceScore > 75
                              ? theme.palette.info.main
                              : theme.palette.warning.main,
                          0.2
                        )}`,
                      }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <Typography variant="body2" fontWeight="500">
                            {medication.adherenceScore > 90
                              ? 'Excellent'
                              : medication.adherenceScore > 75
                                ? 'Good'
                                : 'Needs Improvement'}
                          </Typography>
                          <Typography
                            variant="body1"
                            fontWeight="600"
                            color={
                              medication.adherenceScore > 90
                                ? theme.palette.success.main
                                : medication.adherenceScore > 75
                                  ? theme.palette.info.main
                                  : theme.palette.warning.main
                            }
                          >
                            {medication.adherenceScore}%
                          </Typography>
                        </Box>
                        <LinearProgress
                          variant="determinate"
                          value={medication.adherenceScore}
                          sx={{
                            mt: 1,
                            height: 8,
                            borderRadius: 4,
                            bgcolor: alpha(theme.palette.background.paper, 0.5),
                            '& .MuiLinearProgress-bar': {
                              bgcolor: medication.adherenceScore > 90
                                ? theme.palette.success.main
                                : medication.adherenceScore > 75
                                  ? theme.palette.info.main
                                  : theme.palette.warning.main,
                            }
                          }}
                        />
                      </Box>
                    </Box>
                  )}

                  {/* Next Dose and Refill Information */}
                  <Grid container spacing={2} sx={{ mt: 1 }}>
                    {medication.nextDose && (
                      <Grid item xs={12} sm={6}>
                        <Box sx={{
                          p: 2,
                          bgcolor: alpha(theme.palette.info.light, 0.1),
                          borderRadius: '12px',
                          display: 'flex',
                          alignItems: 'center',
                          border: `1px solid ${alpha(theme.palette.info.main, 0.2)}`,
                          height: '100%'
                        }}>
                          <AccessTimeIcon color="info" sx={{ mr: 1.5, fontSize: 22 }} />
                          <Box>
                            <Typography variant="body2" color="text.secondary" fontWeight="500">
                              Next Dose
                            </Typography>
                            <Typography variant="body1" fontWeight="600" color={theme.palette.info.dark}>
                              {medication.nextDose}
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                    )}

                    {medication.refillDate && (
                      <Grid item xs={12} sm={6}>
                        <Box sx={{
                          p: 2,
                          bgcolor: medication.isLow
                            ? alpha(theme.palette.error.light, 0.1)
                            : alpha(theme.palette.success.light, 0.1),
                          borderRadius: '12px',
                          display: 'flex',
                          alignItems: 'center',
                          border: `1px solid ${medication.isLow
                            ? alpha(theme.palette.error.main, 0.2)
                            : alpha(theme.palette.success.main, 0.2)}`,
                          height: '100%'
                        }}>
                          <CalendarTodayIcon
                            color={medication.isLow ? "error" : "success"}
                            sx={{ mr: 1.5, fontSize: 22 }}
                          />
                          <Box>
                            <Typography variant="body2" color="text.secondary" fontWeight="500">
                              Refill Due
                            </Typography>
                            <Typography
                              variant="body1"
                              fontWeight="600"
                              color={medication.isLow
                                ? theme.palette.error.dark
                                : theme.palette.success.dark
                              }
                            >
                              {medication.refillDate}
                              {medication.daysRemaining !== undefined && (
                                <Typography
                                  component="span"
                                  variant="caption"
                                  sx={{
                                    ml: 1,
                                    color: medication.isLow
                                      ? theme.palette.error.main
                                      : theme.palette.success.main,
                                    fontWeight: 'bold'
                                  }}
                                >
                                  ({medication.daysRemaining} days)
                                </Typography>
                              )}
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                    )}
                  </Grid>
                </Paper>
              </Grid>
            ))}
          </Grid>
        ) : (
          <Alert
            severity="info"
            sx={{
              borderRadius: '12px',
              '& .MuiAlert-icon': {
                fontSize: '1.5rem'
              }
            }}
          >
            <AlertTitle>No Medications</AlertTitle>
            <Typography variant="body2">
              No current medications are listed for this patient. When medications are prescribed, they will appear here.
            </Typography>
            <Box sx={{ mt: 2 }}>
              <Button
                variant="outlined"
                color="primary"
                startIcon={<MedicationIcon />}
                component={Link}
                to={`/kin/patients/${selectedPatientId}/visits`}
                size="small"
              >
                View Medical History
              </Button>
            </Box>
          </Alert>
        )}
      </Paper>
      </Box>
      )}

      {/* Additional Tab Panels */}
      {activeTab === 1 && (
        <Box sx={{ py: 3 }}>
          {/* Messages Tab Content */}
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h5" fontWeight="600">Messages</Typography>
              <Button
                variant="contained"
                startIcon={<MessageIcon />}
                sx={{ borderRadius: '8px' }}
                onClick={handleOpenMessageDialog}
              >
                New Message
              </Button>
            </Box>

            {messages.length > 0 ? (
              <List sx={{ bgcolor: 'background.paper', borderRadius: '16px', overflow: 'hidden' }}>
                {messages.map((message) => (
                  <React.Fragment key={message.id}>
                    <ListItem
                      alignItems="flex-start"
                      sx={{
                        bgcolor: message.isRead ? 'transparent' : alpha(theme.palette.primary.light, 0.1),
                        py: 2
                      }}
                    >
                      <ListItemAvatar>
                        <Avatar sx={{ bgcolor: theme.palette.primary.main }}>
                          {message.sender.charAt(0)}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                            <Typography variant="subtitle1" fontWeight="500">
                              {message.sender}
                              {!message.isRead && (
                                <Chip
                                  size="small"
                                  label="New"
                                  color="primary"
                                  sx={{ ml: 1, height: 20, fontSize: '0.7rem' }}
                                />
                              )}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {new Date(message.date).toLocaleDateString()}
                            </Typography>
                          </Box>
                        }
                        secondary={
                          <Typography
                            variant="body2"
                            color="text.primary"
                            sx={{ mt: 1 }}
                          >
                            {message.content}
                          </Typography>
                        }
                      />
                    </ListItem>
                    <Divider component="li" />
                  </React.Fragment>
                ))}
              </List>
            ) : (
              <Alert severity="info">No messages available.</Alert>
            )}
          </Box>
        </Box>
      )}

      {activeTab === 2 && (
        <Box sx={{ py: 3 }}>
          {/* Appointments Tab Content */}
          <Box>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h5" fontWeight="600">Upcoming Appointments</Typography>
              <ActionButton
                variant="contained"
                startIcon={<CalendarTodayIcon />}
                component={Link}
                to={`/kin/patients/${selectedPatientId}/schedule-appointment`}
                sx={{ borderRadius: '8px' }}
              >
                Schedule Appointment
              </ActionButton>
            </Box>

            {appointments.length > 0 ? (
              <Grid container spacing={3}>
                {appointments.map((appointment) => (
                  <Grid item xs={12} md={6} key={appointment.id}>
                    <AppointmentCard>
                      <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between' }}>
                        <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                          <Avatar
                            sx={{
                              bgcolor: alpha(theme.palette.primary.main, 0.1),
                              color: theme.palette.primary.main,
                              mr: 2
                            }}
                          >
                            <CalendarTodayIcon />
                          </Avatar>
                          <Box>
                            <Typography variant="h6" fontWeight="500">{appointment.reason}</Typography>
                            <Typography variant="body2" color="text.secondary">
                              {formatDate(appointment.date)} at {appointment.time}
                            </Typography>
                            <Typography variant="body2" sx={{ mt: 1 }}>
                              <strong>Doctor:</strong> {appointment.doctor}
                            </Typography>
                          </Box>
                        </Box>
                        <StatusBadge
                          label={appointment.status === 'scheduled' ? 'Upcoming' : appointment.status}
                          color={appointment.status === 'scheduled' ? 'primary' : 'default'}
                        />
                      </Box>

                      <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end', gap: 1 }}>
                        <Button
                          size="small"
                          variant="outlined"
                          sx={{ borderRadius: '8px' }}
                        >
                          Reschedule
                        </Button>
                        <Button
                          size="small"
                          variant="contained"
                          sx={{ borderRadius: '8px' }}
                        >
                          Details
                        </Button>
                      </Box>
                    </AppointmentCard>
                  </Grid>
                ))}
              </Grid>
            ) : (
              <Alert severity="info">No upcoming appointments scheduled.</Alert>
            )}
          </Box>
        </Box>
      )}

      {activeTab === 3 && (
        <Box sx={{ py: 3 }}>
          {/* Health Tips Tab Content */}
          <Box>
            <Typography variant="h5" fontWeight="600" gutterBottom>Health Tips & Recommendations</Typography>
            <Typography variant="body1" paragraph>
              These tips are personalized based on your family member's health profile and recent measurements.
            </Typography>

            <Grid container spacing={3}>
              {healthTips.map((tip) => (
                <Grid item xs={12} md={6} key={tip.id}>
                  <HealthTipCard>
                    <Box sx={{ display: 'flex', alignItems: 'flex-start' }}>
                      <Avatar
                        sx={{
                          bgcolor: 'transparent',
                          mr: 2
                        }}
                      >
                        {tip.icon}
                      </Avatar>
                      <Box>
                        <Typography variant="h6" fontWeight="500" gutterBottom>
                          {tip.title}
                        </Typography>
                        <Typography variant="body1">
                          {tip.content}
                        </Typography>
                      </Box>
                    </Box>
                  </HealthTipCard>
                </Grid>
              ))}
            </Grid>
          </Box>
        </Box>
      )}

      {/* Message Doctor Dialog */}
      <MessageDoctor
        open={messageDialogOpen}
        onClose={handleCloseMessageDialog}
        patientId={selectedPatientId || undefined}
        relatedPatients={relatedPatients}
      />
    </DashboardContainer>
  );
};

export default KinDashboard;
