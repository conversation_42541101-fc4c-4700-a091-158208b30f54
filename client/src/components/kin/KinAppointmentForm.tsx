import React, { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import axios from 'axios';
import { Doctor } from '../../types';
import AuthContext from '../../context/AuthContext';
import { API_URL } from '../../config';
import { SelectChangeEvent } from '@mui/material/Select';
import {
  Box,
  Button,
  Container,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  TextField,
  Typography,
  Alert,
  CircularProgress,
  Divider,
  useTheme,
  alpha
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  CalendarToday as CalendarTodayIcon,
  LocalHospital as LocalHospitalIcon,
  Event as EventIcon,
  AccessTime as AccessTimeIcon,
  Note as NoteIcon
} from '@mui/icons-material';

interface AppointmentFormData {
  patient_id: string;
  doctor_id: string;
  title: string;
  date: string;
  start_time: string;
  end_time: string;
  status: string;
  type: string;
  notes: string;
}

const KinAppointmentForm: React.FC = () => {
  const { patientId } = useParams<{ patientId: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const { } = useContext(AuthContext); // Keep the context for future use
  const theme = useTheme();

  // States
  const [formData, setFormData] = useState<AppointmentFormData>({
    patient_id: patientId || '',
    doctor_id: '',
    title: '',
    date: '',
    start_time: '',
    end_time: '',
    status: 'scheduled', // Default to scheduled for new appointments
    type: 'checkup',
    notes: ''
  });

  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [patientName, setPatientName] = useState<string>('');

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get patient ID from URL params or query string
        let id = patientId;
        if (!id) {
          const params = new URLSearchParams(location.search);
          const patientParam = params.get('patient');
          id = patientParam || undefined;
        }

        if (!id) {
          throw new Error('Patient ID is required');
        }

        // Fetch doctors list
        const doctorsRes = await axios.get(`${API_URL}/api/doctors`);
        setDoctors(doctorsRes.data);

        // Fetch patient details to get the name
        const patientRes = await axios.get(`${API_URL}/api/patients/${id}`);
        const patient = patientRes.data;
        setPatientName(`${patient.first_name} ${patient.last_name}`);

        // Set patient ID in form data
        setFormData(prevData => ({
          ...prevData,
          patient_id: id || ''
        }));

        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again later.');
        setLoading(false);
      }
    };

    fetchData();
  }, [patientId, location.search]);

  const onChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement> | SelectChangeEvent
  ) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);

    try {
      // Validate form data
      const validationErrors = [];

      if (!formData.patient_id) {
        validationErrors.push('Patient ID is missing');
      }

      if (!formData.doctor_id) {
        validationErrors.push('Please select a doctor');
      }

      if (!formData.date) {
        validationErrors.push('Please select a date');
      }

      if (!formData.start_time) {
        validationErrors.push('Please select a start time');
      }

      if (!formData.end_time) {
        validationErrors.push('Please select an end time');
      }

      if (formData.start_time && formData.end_time && formData.start_time >= formData.end_time) {
        validationErrors.push('End time must be after start time');
      }

      if (validationErrors.length > 0) {
        setError(validationErrors.join('. '));
        setSubmitting(false);
        return;
      }

      // Generate a default reason if title is empty
      const defaultReason = formData.title || `${formData.type.charAt(0).toUpperCase() + formData.type.slice(1)} appointment`;

      // Map client form fields to server expected fields
      const serverAppointmentData = {
        patient_id: parseInt(formData.patient_id),
        doctor_id: parseInt(formData.doctor_id),
        reason: defaultReason, // Use title as reason, or generate from type if empty
        title: formData.title, // Also include title directly
        date: formData.date,
        time: formData.start_time, // Map start_time to time
        start_time: formData.start_time, // Also include start_time directly
        end_time: formData.end_time, // Make sure end_time is included
        status: formData.status,
        notes: formData.notes,
        type: formData.type
      };

      // Get auth token
      const token = localStorage.getItem('token');
      if (!token) {
        setError('Authentication error. Please log in again.');
        setSubmitting(false);
        return;
      }

      const headers = {
        'Content-Type': 'application/json',
        'x-auth-token': token
      };

      // Create a new appointment
      await axios.post(
        `${API_URL}/api/appointments`,
        serverAppointmentData,
        { headers }
      );

      // Show success message
      alert('Appointment scheduled successfully!');
      navigate(`/kin/dashboard`);
    } catch (err: any) {
      console.error('Error scheduling appointment:', err);

      // More detailed error reporting
      if (err.response) {
        console.error('Error response data:', err.response.data);
        console.error('Error response status:', err.response.status);

        if (err.response.data.msg) {
          setError(`Failed to schedule appointment: ${err.response.data.msg}`);
        } else if (err.response.data.errors && err.response.data.errors.length > 0) {
          setError(`Failed to schedule appointment: ${err.response.data.errors[0].msg}`);
        } else {
          setError('Failed to schedule appointment. Please check your inputs and try again.');
        }
      } else if (err.request) {
        // The request was made but no response was received
        console.error('Error request:', err.request);
        setError('Network error - no response from server. Please check your internet connection and try again.');
      } else {
        // Something happened in setting up the request
        setError(`Error: ${err.message}`);
      }

      setSubmitting(false);
    }
  };

  // Generate time slot options (30-minute intervals)
  const getTimeOptions = () => {
    const options = [];
    for (let hour = 8; hour < 18; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const formattedHour = hour.toString().padStart(2, '0');
        const formattedMinute = minute.toString().padStart(2, '0');
        const time = `${formattedHour}:${formattedMinute}`;
        options.push(
          <MenuItem key={time} value={time}>
            {hour > 12 ? (hour - 12) : hour}:{formattedMinute} {hour >= 12 ? 'PM' : 'AM'}
          </MenuItem>
        );
      }
    }
    return options;
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="80vh">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        {/* Header with patient info and actions */}
        <Paper
          elevation={2}
          sx={{
            p: 3,
            mb: 3,
            borderRadius: 2,
            background: 'linear-gradient(to right, rgba(44, 75, 43, 0.05), rgba(44, 75, 43, 0.01))' // Primary color from theme
          }}
        >
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={7}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Button
                  variant="outlined"
                  startIcon={<ArrowBackIcon />}
                  onClick={() => navigate('/kin/dashboard')}
                  sx={{
                    mr: 2,
                    borderRadius: 2,
                    borderColor: '#2C4B2B',
                    color: '#2C4B2B',
                    '&:hover': {
                      borderColor: '#3E6A3D',
                      backgroundColor: 'rgba(44, 75, 43, 0.04)'
                    }
                  }}
                >
                  Back
                </Button>
                <Box>
                  <Typography variant="h4" component="h1" sx={{ fontWeight: 500 }}>
                    Schedule Appointment
                  </Typography>
                  <Typography variant="subtitle1" color="text.secondary">
                    {patientName}
                  </Typography>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Paper>

        {error && (
          <Alert severity="error" sx={{ mb: 3, borderRadius: 2 }}>
            {error}
          </Alert>
        )}

        <Paper elevation={2} sx={{ p: 3, borderRadius: 2 }}>
          <form onSubmit={onSubmit}>
            <Grid container spacing={3}>
              {/* Doctor Selection */}
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <LocalHospitalIcon sx={{ mr: 1, color: '#2C4B2B' }} />
                  <Typography variant="h6">Select Doctor</Typography>
                </Box>
                <FormControl fullWidth required>
                  <InputLabel id="doctor-select-label">Doctor</InputLabel>
                  <Select
                    labelId="doctor-select-label"
                    id="doctor_id"
                    name="doctor_id"
                    value={formData.doctor_id}
                    onChange={onChange}
                    label="Doctor"
                  >
                    <MenuItem value="">Select a doctor</MenuItem>
                    {doctors.map((doctor) => (
                      <MenuItem key={doctor.doctor_id} value={doctor.doctor_id.toString()}>
                        Dr. {doctor.first_name} {doctor.last_name} {doctor.specialty ? `(${doctor.specialty})` : ''}
                      </MenuItem>
                    ))}
                  </Select>
                  <FormHelperText>Select the doctor for this appointment</FormHelperText>
                </FormControl>
              </Grid>

              <Grid item xs={12}>
                <Divider sx={{ my: 1 }} />
              </Grid>

              {/* Appointment Details */}
              <Grid item xs={12}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <EventIcon sx={{ mr: 1, color: '#2C4B2B' }} />
                  <Typography variant="h6">Appointment Details</Typography>
                </Box>
              </Grid>

              {/* Appointment Type */}
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel id="type-select-label">Appointment Type</InputLabel>
                  <Select
                    labelId="type-select-label"
                    id="type"
                    name="type"
                    value={formData.type}
                    onChange={onChange}
                    label="Appointment Type"
                  >
                    <MenuItem value="checkup">Regular Checkup</MenuItem>
                    <MenuItem value="follow-up">Follow-up</MenuItem>
                    <MenuItem value="urgent">Urgent Care</MenuItem>
                    <MenuItem value="consultation">Consultation</MenuItem>
                    <MenuItem value="other">Other</MenuItem>
                  </Select>
                </FormControl>
              </Grid>

              {/* Appointment Title/Reason */}
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  id="title"
                  name="title"
                  label="Appointment Reason"
                  value={formData.title}
                  onChange={onChange}
                  placeholder="e.g., Annual checkup, Blood pressure follow-up"
                  helperText="Brief reason for the appointment"
                />
              </Grid>

              {/* Date */}
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  id="date"
                  name="date"
                  label="Date"
                  type="date"
                  value={formData.date}
                  onChange={onChange}
                  InputLabelProps={{ shrink: true }}
                  required
                  inputProps={{
                    min: new Date().toISOString().split('T')[0] // Set min date to today
                  }}
                />
              </Grid>

              {/* Start Time */}
              <Grid item xs={12} md={4}>
                <FormControl fullWidth required>
                  <InputLabel id="start-time-label">Start Time</InputLabel>
                  <Select
                    labelId="start-time-label"
                    id="start_time"
                    name="start_time"
                    value={formData.start_time}
                    onChange={onChange}
                    label="Start Time"
                  >
                    <MenuItem value="">Select a time</MenuItem>
                    {getTimeOptions()}
                  </Select>
                </FormControl>
              </Grid>

              {/* End Time */}
              <Grid item xs={12} md={4}>
                <FormControl fullWidth required>
                  <InputLabel id="end-time-label">End Time</InputLabel>
                  <Select
                    labelId="end-time-label"
                    id="end_time"
                    name="end_time"
                    value={formData.end_time}
                    onChange={onChange}
                    label="End Time"
                  >
                    <MenuItem value="">Select a time</MenuItem>
                    {getTimeOptions()}
                  </Select>
                </FormControl>
              </Grid>

              {/* Notes */}
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  id="notes"
                  name="notes"
                  label="Additional Notes"
                  multiline
                  rows={4}
                  value={formData.notes}
                  onChange={onChange}
                  placeholder="Any additional information the doctor should know before the appointment"
                />
              </Grid>

              {/* Submit Button */}
              <Grid item xs={12} sx={{ mt: 2, display: 'flex', justifyContent: 'space-between' }}>
                <Button
                  variant="outlined"
                  onClick={() => navigate('/kin/dashboard')}
                  startIcon={<ArrowBackIcon />}
                  sx={{
                    borderRadius: 2,
                    borderColor: '#2C4B2B',
                    color: '#2C4B2B',
                    '&:hover': {
                      borderColor: '#3E6A3D',
                      backgroundColor: 'rgba(44, 75, 43, 0.04)'
                    }
                  }}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="contained"
                  disabled={submitting}
                  startIcon={<CalendarTodayIcon />}
                  sx={{
                    borderRadius: 2,
                    px: 4,
                    backgroundColor: '#2C4B2B',
                    '&:hover': {
                      backgroundColor: '#3E6A3D'
                    }
                  }}
                >
                  {submitting ? <CircularProgress size={24} /> : 'Schedule Appointment'}
                </Button>
              </Grid>
            </Grid>
          </form>
        </Paper>

        {/* Information Card */}
        <Paper
          elevation={1}
          sx={{
            p: 3,
            mt: 3,
            borderRadius: 2,
            bgcolor: 'rgba(44, 75, 43, 0.05)', // Primary color from theme with opacity
            border: '1px solid rgba(44, 75, 43, 0.2)' // Primary color from theme with opacity
          }}
        >
          <Typography variant="h6" sx={{ color: '#2C4B2B' }} gutterBottom>
            Appointment Information
          </Typography>
          <Typography variant="body1" paragraph>
            You are scheduling an appointment for {patientName}. The doctor's office may contact you to confirm the appointment or make adjustments if the requested time is not available.
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1, mb: 1 }}>
            <AccessTimeIcon color="action" sx={{ mt: 0.5 }} />
            <Typography variant="body2">
              Appointments are typically 30 minutes long. Please arrive 15 minutes early to complete any necessary paperwork.
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
            <NoteIcon color="action" sx={{ mt: 0.5 }} />
            <Typography variant="body2">
              If you need to cancel or reschedule, please do so at least 24 hours in advance.
            </Typography>
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default KinAppointmentForm;
