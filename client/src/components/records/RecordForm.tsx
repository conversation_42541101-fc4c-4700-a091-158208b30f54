import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import axios from 'axios';

// Add the API_URL constant
const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5001';

interface RecordFormData {
  patient_id: number;
  record_date: string;
  diagnosis: string;
  treatment: string;
  notes: string;
  attending_doctor_id: number | null;
}

interface Patient {
  patient_id: number;
  first_name: string;
  last_name: string;
}

interface Doctor {
  doctor_id: number;
  first_name: string;
  last_name: string;
  specialty: string;
}

const RecordForm: React.FC = () => {
  const { id, patientId } = useParams<{ id?: string; patientId?: string }>();
  const navigate = useNavigate();
  const isEditMode = !!id;

  const [formData, setFormData] = useState<RecordFormData>({
    patient_id: patientId ? parseInt(patientId) : 0,
    record_date: new Date().toISOString().split('T')[0],
    diagnosis: '',
    treatment: '',
    notes: '',
    attending_doctor_id: null,
  });

  const [patient, setPatient] = useState<Patient | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Add these new states
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [loadingDoctors, setLoadingDoctors] = useState(false);

  const { record_date, diagnosis, treatment, notes } = formData;

  useEffect(() => {
    const fetchData = async () => {
      try {
        // If editing an existing record
        if (isEditMode) {
          const recordRes = await axios.get(`${API_URL}/api/records/${id}`);
          const recordData = recordRes.data;
          
          // Format date to YYYY-MM-DD for the date input
          const formattedDate = new Date(recordData.record_date)
            .toISOString()
            .split('T')[0];
          
          setFormData({
            patient_id: recordData.patient_id,
            record_date: formattedDate,
            diagnosis: recordData.diagnosis || '',
            treatment: recordData.treatment || '',
            notes: recordData.notes || '',
            attending_doctor_id: recordData.attending_doctor_id || null,
          });
          
          // Get patient info
          const patientRes = await axios.get(`${API_URL}/api/patients/${recordData.patient_id}`);
          setPatient(patientRes.data);
        } 
        // If adding a new record for a specific patient
        else if (patientId) {
          const patientRes = await axios.get(`${API_URL}/api/patients/${patientId}`);
          setPatient(patientRes.data);
        }
        
        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load required data');
        setLoading(false);
      }
    };
    
    fetchData();
  }, [id, patientId, isEditMode]);

  // Add a new useEffect to fetch doctors
  useEffect(() => {
    const fetchDoctors = async () => {
      setLoadingDoctors(true);
      try {
        const res = await axios.get(`${API_URL}/api/doctors`);
        setDoctors(res.data);
      } catch (err) {
        console.error('Error fetching doctors:', err);
      } finally {
        setLoadingDoctors(false);
      }
    };
    
    fetchDoctors();
  }, []);

  const onChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    try {
      if (isEditMode) {
        // Update existing record
        await axios.put(`${API_URL}/api/records/${id}`, formData);
      } else {
        // Create new record
        await axios.post(`${API_URL}/api/records`, formData);
      }
      
      // Redirect to patient details page
      navigate(`/patients/${formData.patient_id}`);
    } catch (err) {
      console.error('Error saving record:', err);
      setError('Failed to save medical record');
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!patient) {
    return <div className="alert alert-danger">Patient not found</div>;
  }

  return (
    <div className="record-form">
      <Link to={`/patients/${formData.patient_id}`} className="btn btn-light">
        <i className="fas fa-arrow-left"></i> Back to Patient
      </Link>
      
      <h1 className="large text-primary">
        {isEditMode ? 'Edit Medical Record' : 'Add Medical Record'}
      </h1>
      <p className="lead">
        <i className="fas fa-notes-medical"></i>{' '}
        {`${isEditMode ? 'Edit' : 'Create'} medical record for ${patient.first_name} ${patient.last_name}`}
      </p>
      
      {error && <div className="alert alert-danger">{error}</div>}
      
      <form className="form" onSubmit={onSubmit}>
        <div className="form-group">
          <label htmlFor="record_date">Date</label>
          <input
            type="date"
            id="record_date"
            name="record_date"
            value={record_date}
            onChange={onChange}
            required
          />
        </div>
        
        <div className="form-group">
          <label htmlFor="diagnosis">Diagnosis</label>
          <textarea
            id="diagnosis"
            name="diagnosis"
            value={diagnosis}
            onChange={onChange}
            rows={3}
            required
          ></textarea>
        </div>
        
        <div className="form-group">
          <label htmlFor="treatment">Treatment</label>
          <textarea
            id="treatment"
            name="treatment"
            value={treatment}
            onChange={onChange}
            rows={3}
            required
          ></textarea>
        </div>
        
        <div className="form-group">
          <label htmlFor="notes">Notes</label>
          <textarea
            id="notes"
            name="notes"
            value={notes}
            onChange={onChange}
            rows={3}
          ></textarea>
        </div>
        
        <div className="form-group">
          <label htmlFor="attending_doctor_id">Attending Doctor</label>
          <select
            id="attending_doctor_id"
            name="attending_doctor_id"
            value={formData.attending_doctor_id || ''}
            onChange={onChange}
            required
          >
            <option value="">-- Select Attending Doctor --</option>
            {doctors.map(doctor => (
              <option key={doctor.doctor_id} value={doctor.doctor_id}>
                Dr. {doctor.first_name} {doctor.last_name} ({doctor.specialty})
              </option>
            ))}
          </select>
          {loadingDoctors && <span><i className="fas fa-spinner fa-spin"></i> Loading doctors...</span>}
        </div>
        
        <input
          type="submit"
          className="btn btn-primary"
          value={isEditMode ? 'Update Record' : 'Add Record'}
        />
      </form>
    </div>
  );
};

export default RecordForm; 