/* Navbar CSS Optimizations */

/* Apply CSS containment to isolate rendering */
.navbar {
  contain: layout style paint;
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimize animations */
.navbar a,
.navbar i,
.navbar-brand a,
.nav-item a::after {
  will-change: transform, opacity;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Optimize hover states */
.nav-item a:hover {
  transform: translateY(-1px);
}

/* Optimize active states */
.nav-item.active a::after {
  transform: scaleX(1);
  transform-origin: center;
}

/* Optimize loading spinner */
.loading-placeholder i {
  will-change: transform;
}

/* Optimize user info section */
.user-info {
  contain: content;
}

/* Theme toggle button styling */
.nav-item.theme-toggle a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  margin: 0 0.5rem;
  transition: all 0.3s ease;
}

.nav-item.theme-toggle a:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.nav-item.theme-toggle i {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.95);
}

/* Optimize mobile styles */
@media (max-width: 768px) {
  .navbar {
    contain: layout style;
  }

  .nav-item.theme-toggle a {
    width: 32px;
    height: 32px;
    margin: 0 0.3rem;
  }
}
