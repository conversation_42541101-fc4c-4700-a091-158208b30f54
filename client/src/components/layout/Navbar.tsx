import React, { useContext, useState, useEffect, useMemo, useCallback } from 'react';
import { Link, useLocation } from 'react-router-dom';
import AuthContext from '../../context/AuthContext';
import ThemeContext from '../../context/ThemeContext';
import { API_URL } from '../../config';
import LoadingSpinner from '../common/LoadingSpinner';
import './Navbar.css';

interface UserDetails {
  first_name?: string;
  last_name?: string;
}

const Navbar: React.FC = () => {
  const { isAuthenticated, user, logout } = useContext(AuthContext);
  const { mode, toggleTheme } = useContext(ThemeContext);
  const location = useLocation();
  const [userDetails, setUserDetails] = useState<UserDetails>({});
  const [loading, setLoading] = useState<boolean>(false);

  // Function to check if a link is active - memoized to prevent recalculation
  const isActive = useCallback((path: string) => {
    return location.pathname.startsWith(path);
  }, [location.pathname]);

  // Fetch user details based on role - optimized with useCallback
  const fetchUserDetails = useCallback(async () => {
    if (!user) return;

    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      if (!token) {
        setLoading(false);
        return;
      }

      let endpoint = '';
      if (user.role === 'doctor' && user.doctor_id) {
        endpoint = `${API_URL}/api/doctors/${user.doctor_id}`;
      } else if ((user.role === 'patient' || user.role === 'kin') && user.patient_id) {
        endpoint = `${API_URL}/api/patients/${user.patient_id}`;
      } else {
        // For other roles, extract first and last name from username if possible
        const nameParts = user.username.split('_');
        if (nameParts.length === 2) {
          setUserDetails({
            first_name: nameParts[1].charAt(0).toUpperCase() + nameParts[1].slice(1),
            last_name: nameParts[0].charAt(0).toUpperCase() + nameParts[0].slice(1)
          });
        }
        setLoading(false);
        return;
      }

      // Use AbortController for cancellable fetch
      const controller = new AbortController();
      const signal = controller.signal;

      const response = await fetch(endpoint, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': token
        },
        signal
      });

      if (response.ok) {
        const data = await response.json();
        setUserDetails({
          first_name: data.first_name,
          last_name: data.last_name
        });
      }
      setLoading(false);

      return () => controller.abort(); // Cleanup function
    } catch (error) {
      // Only log errors that aren't from aborting the request
      if (!(error instanceof DOMException && error.name === 'AbortError')) {
        console.error('Error fetching user details:', error);
      }
      setLoading(false);
    }
  }, [user, API_URL]);

  // Effect to fetch user details
  useEffect(() => {
    let isMounted = true;

    if (isAuthenticated && user) {
      fetchUserDetails();
    }

    return () => { isMounted = false; };
  }, [isAuthenticated, user, fetchUserDetails]);

  // Get display name - memoized to prevent recalculation
  const displayName = useMemo(() => {
    if (userDetails.first_name && userDetails.last_name) {
      // For kin users, show that they are viewing as a family member
      if (user?.role === 'kin') {
        return `${user.username} (Family of ${userDetails.first_name} ${userDetails.last_name})`;
      }
      return `${userDetails.first_name} ${userDetails.last_name}`;
    }

    return user?.username || '';
  }, [userDetails.first_name, userDetails.last_name, user?.role, user?.username]);

  // Get role display text - memoized to prevent recalculation
  const roleDisplay = useMemo(() => {
    if (!user) return '';

    switch (user.role) {
      case 'admin':
        return 'Admin';
      case 'doctor':
        return 'Doctor';
      case 'patient':
        return 'Patient';
      case 'kin':
        return 'Family Member';
      case 'assistant':
        return 'Assistant';
      default:
        return user.role.charAt(0).toUpperCase() + user.role.slice(1);
    }
  }, [user]);

  // Special links for admin users only - memoized to prevent unnecessary re-renders
  const adminLinks = useMemo(() => (
    <ul className="navbar-nav">
      <li className={`nav-item ${isActive('/admin') && !isActive('/admin_exp') ? 'active' : ''}`}>
        <Link to="/admin">
          <i className="fas fa-user-shield"></i>
          <span className="hide-sm">Admin</span>
        </Link>
      </li>
      <li className={`nav-item ${isActive('/admin_exp') ? 'active' : ''}`}>
        <Link to="/admin_exp">
          <i className="fas fa-flask"></i>
          <span className="hide-sm">Enhanced Admin</span>
        </Link>
      </li>
      <li className="nav-item theme-toggle">
        <a href="#!" onClick={toggleTheme} title={mode === 'dark' ? 'Switch to Light Mode' : 'Switch to Dark Mode'}>
          <i className={`fas ${mode === 'dark' ? 'fa-sun' : 'fa-moon'}`}></i>
        </a>
      </li>
      <li className={`nav-item ${isActive('/change-password') ? 'active' : ''}`}>
        <Link to="/change-password">
          <i className="fas fa-key"></i>
          <span className="hide-sm">Change Password</span>
        </Link>
      </li>
      <li className="nav-item logout">
        <a href="#!" onClick={() => logout()}>
          <i className="fas fa-sign-out-alt"></i>
          <span className="hide-sm">Logout</span>
        </a>
      </li>
      {user && (
        <li className="user-info">
          <div className="user-avatar">
            <i className="fas fa-user-circle"></i>
          </div>
          <div className="user-details">
            <span className="user-name">
              {loading ? (
                <span className="loading-placeholder">
                  <i className="fas fa-circle-notch fa-spin" style={{ fontSize: '0.8rem', marginRight: '0.5rem' }}></i>
                  {user?.username || ''}
                </span>
              ) : displayName}
            </span>
            <span className="user-role">{roleDisplay}</span>
          </div>
        </li>
      )}
    </ul>
  ), [isActive, mode, toggleTheme, user, loading, displayName, roleDisplay, logout]);

  // Links for patient users - memoized to prevent unnecessary re-renders
  const patientLinks = useMemo(() => (
    <ul className="navbar-nav">
      <li className={`nav-item ${isActive('/patient/dashboard') ? 'active' : ''}`}>
        <Link to="/patient/dashboard">
          <i className="fas fa-hospital-user"></i>
          <span className="hide-sm">Dashboard</span>
        </Link>
      </li>
      <li className="nav-item theme-toggle">
        <a href="#!" onClick={toggleTheme} title={mode === 'dark' ? 'Switch to Light Mode' : 'Switch to Dark Mode'}>
          <i className={`fas ${mode === 'dark' ? 'fa-sun' : 'fa-moon'}`}></i>
        </a>
      </li>
      <li className={`nav-item ${isActive('/change-password') ? 'active' : ''}`}>
        <Link to="/change-password">
          <i className="fas fa-key"></i>
          <span className="hide-sm">Change Password</span>
        </Link>
      </li>
      <li className="nav-item logout">
        <a href="#!" onClick={() => logout()}>
          <i className="fas fa-sign-out-alt"></i>
          <span className="hide-sm">Logout</span>
        </a>
      </li>
      {user && (
        <li className="user-info">
          <div className="user-avatar">
            <i className="fas fa-user-circle"></i>
          </div>
          <div className="user-details">
            <span className="user-name">
              {loading ? (
                <span className="loading-placeholder">
                  <i className="fas fa-circle-notch fa-spin" style={{ fontSize: '0.8rem', marginRight: '0.5rem' }}></i>
                  {user?.username || ''}
                </span>
              ) : displayName}
            </span>
            <span className="user-role">{roleDisplay}</span>
          </div>
        </li>
      )}
    </ul>
  ), [isActive, mode, toggleTheme, user, loading, displayName, roleDisplay, logout]);

  // Links for kin users - memoized to prevent unnecessary re-renders
  const kinLinks = useMemo(() => (
    <ul className="navbar-nav">
      <li className={`nav-item ${isActive('/kin/dashboard') ? 'active' : ''}`}>
        <Link to="/kin/dashboard">
          <i className="fas fa-user-friends"></i>
          <span className="hide-sm">Dashboard</span>
        </Link>
      </li>
      <li className="nav-item theme-toggle">
        <a href="#!" onClick={toggleTheme} title={mode === 'dark' ? 'Switch to Light Mode' : 'Switch to Dark Mode'}>
          <i className={`fas ${mode === 'dark' ? 'fa-sun' : 'fa-moon'}`}></i>
        </a>
      </li>
      <li className={`nav-item ${isActive('/change-password') ? 'active' : ''}`}>
        <Link to="/change-password">
          <i className="fas fa-key"></i>
          <span className="hide-sm">Change Password</span>
        </Link>
      </li>
      <li className="nav-item logout">
        <a href="#!" onClick={() => logout()}>
          <i className="fas fa-sign-out-alt"></i>
          <span className="hide-sm">Logout</span>
        </a>
      </li>
      {user && (
        <li className="user-info">
          <div className="user-avatar">
            <i className="fas fa-user-circle"></i>
          </div>
          <div className="user-details">
            <span className="user-name">
              {loading ? (
                <span className="loading-placeholder">
                  <i className="fas fa-circle-notch fa-spin" style={{ fontSize: '0.8rem', marginRight: '0.5rem' }}></i>
                  {user?.username || ''}
                </span>
              ) : displayName}
            </span>
            <span className="user-role">{roleDisplay}</span>
          </div>
        </li>
      )}
    </ul>
  ), [isActive, mode, toggleTheme, user, loading, displayName, roleDisplay, logout]);

  // Links for regular authenticated users (doctors, nurses, etc.) - memoized to prevent unnecessary re-renders
  const regularUserLinks = useMemo(() => (
    <ul className="navbar-nav">
      {user?.role !== 'assistant' && (
        <li className={`nav-item ${isActive('/dashboard') ? 'active' : ''}`}>
          <Link to="/dashboard">
            <i className="fas fa-tachometer-alt"></i>
            <span className="hide-sm">Dashboard</span>
          </Link>
        </li>
      )}

      <li className={`nav-item ${isActive('/patients') ? 'active' : ''}`}>
        <Link to="/patients">
          <i className="fas fa-users"></i>
          <span className="hide-sm">Patients</span>
        </Link>
      </li>

      <li className={`nav-item ${isActive('/appointments') ? 'active' : ''}`}>
        <Link to="/appointments">
          <i className="fas fa-calendar-alt"></i>
          <span className="hide-sm">Appointments</span>
        </Link>
      </li>

      <li className={`nav-item ${isActive('/beers-criteria') ? 'active' : ''}`}>
        <Link to="/beers-criteria">
          <i className="fas fa-pills"></i>
          <span className="hide-sm">BEERS Criteria</span>
        </Link>
      </li>

      <li className="nav-item theme-toggle">
        <a href="#!" onClick={toggleTheme} title={mode === 'dark' ? 'Switch to Light Mode' : 'Switch to Dark Mode'}>
          <i className={`fas ${mode === 'dark' ? 'fa-sun' : 'fa-moon'}`}></i>
        </a>
      </li>

      <li className={`nav-item ${isActive('/change-password') ? 'active' : ''}`}>
        <Link to="/change-password">
          <i className="fas fa-key"></i>
          <span className="hide-sm">Change Password</span>
        </Link>
      </li>

      <li className="nav-item logout">
        <a href="#!" onClick={() => logout()}>
          <i className="fas fa-sign-out-alt"></i>
          <span className="hide-sm">Logout</span>
        </a>
      </li>

      {user && (
        <li className="user-info">
          <div className="user-avatar">
            <i className="fas fa-user-circle"></i>
          </div>
          <div className="user-details">
            <span className="user-name">
              {loading ? (
                <span className="loading-placeholder">
                  <i className="fas fa-circle-notch fa-spin" style={{ fontSize: '0.8rem', marginRight: '0.5rem' }}></i>
                  {user?.username || ''}
                </span>
              ) : displayName}
            </span>
            <span className="user-role">{roleDisplay}</span>
          </div>
        </li>
      )}
    </ul>
  ), [isActive, mode, toggleTheme, user, loading, displayName, roleDisplay, logout]);

  // Guest links - memoized to prevent unnecessary re-renders
  const guestLinks = useMemo(() => (
    <ul className="navbar-nav">
      <li className="nav-item">
        <Link to="/login">
          <i className="fas fa-sign-in-alt"></i>
          <span className="hide-sm">Login</span>
        </Link>
      </li>
      <li className="nav-item theme-toggle">
        <a href="#!" onClick={toggleTheme} title={mode === 'dark' ? 'Switch to Light Mode' : 'Switch to Dark Mode'}>
          <i className={`fas ${mode === 'dark' ? 'fa-sun' : 'fa-moon'}`}></i>
        </a>
      </li>
    </ul>
  ), [mode, toggleTheme]);

  // Use a stable key based on user role and authentication status for better reconciliation
  const navbarKey = useMemo(() => `navbar-${isAuthenticated ? user?.role || 'user' : 'guest'}`, [isAuthenticated, user?.role]);

  return (
    <nav className="navbar" key={navbarKey}>
      <div className="navbar-brand">
        <Link to={
          user?.role === 'admin'
            ? "/admin"
            : user?.role === 'patient'
              ? "/patient/dashboard"
              : user?.role === 'kin'
                ? "/kin/dashboard"
                : user?.role === 'assistant'
                  ? "/appointments"
                  : "/"
        }>
          <i className="fas fa-heartbeat"></i> MedApp
        </Link>
      </div>
      <div className="navbar-menu">
        {!isAuthenticated ? (
          guestLinks
        ) : user?.role === 'admin' ? (
          adminLinks
        ) : user?.role === 'patient' ? (
          patientLinks
        ) : user?.role === 'kin' ? (
          kinLinks
        ) : (
          regularUserLinks
        )}
      </div>
    </nav>
  );
};

// Export memoized component to prevent unnecessary re-renders
export default React.memo(Navbar);