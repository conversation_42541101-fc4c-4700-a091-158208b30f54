import React, { useState, use<PERSON><PERSON>back, ReactNode } from 'react';
import { Link } from 'react-router-dom';
import {
  Box,
  Button,
  Container,
  Typography,
  Paper,
  Tabs,
  Tab,
  LinearProgress,
  Alert,
  AlertTitle,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  styled
} from '@mui/material';
import {
  ArrowBack as BackIcon,
  ArrowForward as NextIcon,
  Save as SaveIcon,
  Check as CheckIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';

// Styled components
const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: 0,
  marginBottom: theme.spacing(3),
  borderRadius: theme.shape.borderRadius * 2,
  boxShadow: '0 6px 20px rgba(0, 0, 0, 0.05)',
  overflow: 'hidden',
  border: '1px solid rgba(0, 0, 0, 0.04)'
}));

const FormSection = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(4)
}));

// Tab Panel component
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`medical-form-tabpanel-${index}`}
      aria-labelledby={`medical-form-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

export interface TabDefinition {
  value: number;
  label: string;
  icon: ReactNode;
}

interface MedicalFormBaseProps {
  title: string;
  backButtonText: string;
  backButtonLink: string;
  tabs: TabDefinition[];
  renderStepContent: (step: number) => ReactNode;
  onSubmit: () => Promise<void>;
  validateStep: (step: number) => boolean;
  isSubmitting: boolean;
  error: string | null;
  success: string | null;
  confirmDialogTitle: string;
  confirmDialogContent: ReactNode;
  hasChanges: boolean;
  isAddMode?: boolean;
  extraActions?: ReactNode;
}

const MedicalFormBase: React.FC<MedicalFormBaseProps> = ({
  title,
  backButtonText,
  backButtonLink,
  tabs,
  renderStepContent,
  onSubmit,
  validateStep,
  isSubmitting,
  error,
  success,
  confirmDialogTitle,
  confirmDialogContent,
  hasChanges,
  isAddMode = false,
  extraActions
}) => {
  // State for the current step
  const [activeStep, setActiveStep] = useState(0);

  // State for confirmation dialog
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);

  // Function to get props for each tab
  const a11yProps = (index: number) => {
    return {
      id: `medical-form-tab-${index}`,
      'aria-controls': `medical-form-tabpanel-${index}`,
    };
  };

  // Step navigation
  const handleNext = () => {
    if (validateStep(activeStep)) {
      setActiveStep((prevActiveStep) => {
        // Don't go beyond the last tab
        if (prevActiveStep < tabs.length - 1) {
          return prevActiveStep + 1;
        }
        return prevActiveStep;
      });
    }
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => Math.max(0, prevActiveStep - 1));
  };

  // Simple tab change handler
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setActiveStep(newValue);
  };

  // Open confirmation dialog
  const handleOpenConfirmDialog = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate all tabs before showing confirmation
    let isValid = true;
    for (let i = 0; i < tabs.length; i++) {
      if (!validateStep(i)) {
        setActiveStep(i);
        isValid = false;
        break;
      }
    }

    if (!isValid) {
      return;
    }

    // Open confirmation dialog
    setConfirmDialogOpen(true);
  };

  // Close confirmation dialog
  const handleCloseConfirmDialog = () => {
    setConfirmDialogOpen(false);
  };

  // Handle form submission
  const handleSubmit = async () => {
    setConfirmDialogOpen(false);
    await onSubmit();
  };

  return (
    <Container maxWidth="lg">
      <StyledPaper>
        {/* Header */}
        <Box
          sx={{
            p: 3,
            background: 'linear-gradient(to right, rgba(25, 118, 210, 0.05), rgba(25, 118, 210, 0.01))',
            borderRadius: '16px 16px 0 0',
            display: 'flex',
            flexDirection: 'column',
            gap: 1
          }}
        >
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <Box>
              <Button
                component={Link}
                to={backButtonLink}
                variant="outlined"
                sx={{
                  mb: 2,
                  borderRadius: '8px',
                  textTransform: 'none',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
                }}
                startIcon={<BackIcon />}
              >
                {backButtonText}
              </Button>

              <Typography
                variant="h4"
                component="h1"
                gutterBottom
                sx={{
                  fontWeight: 600,
                  color: 'primary.main',
                  fontSize: { xs: '1.75rem', md: '2.25rem' }
                }}
              >
                {title}
              </Typography>
            </Box>
          </Box>

          {/* Progress indicator */}
          <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', gap: 2 }}>
            <Box sx={{ flexGrow: 1 }}>
              <LinearProgress
                variant="determinate"
                value={(activeStep / (tabs.length - 1)) * 100}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  backgroundColor: 'rgba(0,0,0,0.05)'
                }}
              />
            </Box>
            <Typography variant="body2" color="text.secondary">
              Step {activeStep + 1} of {tabs.length}
            </Typography>
          </Box>
        </Box>

        {/* Alert Messages */}
        {error && (
          <Alert
            severity="error"
            sx={{
              mb: 3,
              mx: 3,
              borderRadius: 2,
              boxShadow: '0 2px 8px rgba(211, 47, 47, 0.1)'
            }}
          >
            <AlertTitle>Error</AlertTitle>
            {error}
          </Alert>
        )}

        {success && (
          <Alert
            severity="success"
            sx={{
              mb: 3,
              mx: 3,
              borderRadius: 2,
              boxShadow: '0 2px 8px rgba(46, 125, 50, 0.1)'
            }}
          >
            <AlertTitle>Success</AlertTitle>
            {success}
          </Alert>
        )}

        {/* Tabbed Navigation */}
        <Box sx={{ px: 3 }}>
          <Box
            sx={{
              borderRadius: 2,
              backgroundColor: 'rgba(0,0,0,0.02)',
              p: 0.5,
              mb: 3,
              overflowX: 'auto'
            }}
          >
            <Tabs
              value={activeStep}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons="auto"
              aria-label="medical form tabs"
              sx={{
                minHeight: '48px',
                '& .MuiTabs-indicator': {
                  display: 'none'
                }
              }}
            >
              {tabs.map((tab) => (
                <Tab
                  key={tab.value}
                  label={
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      {tab.icon && <Box sx={{ mr: 1, display: 'flex', alignItems: 'center' }}>{tab.icon}</Box>}
                      {tab.label}
                    </Box>
                  }
                  {...a11yProps(tab.value)}
                  sx={{
                    minHeight: 48,
                    textTransform: 'none',
                    fontWeight: activeStep === tab.value ? 600 : 500,
                    fontSize: '0.9rem',
                    padding: '8px 16px',
                    borderRadius: '8px',
                    marginRight: '4px',
                    color: activeStep === tab.value ? 'primary.main' : 'text.secondary',
                    backgroundColor: activeStep === tab.value ? 'rgba(25, 118, 210, 0.08)' : 'transparent',
                    '&:hover': {
                      backgroundColor: activeStep === tab.value ? 'rgba(25, 118, 210, 0.12)' : 'rgba(0, 0, 0, 0.04)'
                    },
                    '& .MuiSvgIcon-root': {
                      fontSize: '1.2rem',
                      marginRight: '8px'
                    }
                  }}
                />
              ))}
            </Tabs>
          </Box>
        </Box>

        {/* Form Content */}
        <Box sx={{ px: 3, pb: 3 }}>
          <form onSubmit={handleOpenConfirmDialog}>
            {/* Tab Panels */}
            <Box
              sx={{
                backgroundColor: 'background.paper',
                borderRadius: 2,
                boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
                overflow: 'hidden',
                border: '1px solid var(--border-color, rgba(0,0,0,0.04))',
                minHeight: '300px'
              }}
            >
              {tabs.map((tab) => (
                <TabPanel key={tab.value} value={activeStep} index={tab.value}>
                  {renderStepContent(tab.value)}
                </TabPanel>
              ))}
            </Box>

            {/* Navigation Buttons */}
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                mt: 4,
                gap: 2
              }}
            >
              <Button
                variant="outlined"
                disabled={activeStep === 0 || isSubmitting}
                onClick={handleBack}
                startIcon={<BackIcon />}
                sx={{
                  borderRadius: '8px',
                  textTransform: 'none',
                  px: 3,
                  py: 1
                }}
              >
                Previous
              </Button>

              <Box sx={{ display: 'flex', gap: 2 }}>
                {extraActions && activeStep === tabs.length - 1 && (
                  <Box sx={{ mr: 2 }}>{extraActions}</Box>
                )}

                {/* Skip to Review button */}
                {activeStep !== 0 && activeStep !== tabs.length - 1 && (
                  <Button
                    variant="text"
                    color="primary"
                    onClick={() => setActiveStep(0)}
                    sx={{
                      borderRadius: '8px',
                      textTransform: 'none'
                    }}
                  >
                    Go to Start
                  </Button>
                )}

                {/* Skip to Review button */}
                {activeStep !== tabs.length - 1 && (
                  <Button
                    variant="text"
                    color="primary"
                    onClick={() => setActiveStep(tabs.length - 1)}
                    sx={{
                      borderRadius: '8px',
                      textTransform: 'none'
                    }}
                  >
                    Skip to Review
                  </Button>
                )}

                {activeStep < tabs.length - 1 ? (
                  <Button
                    variant="contained"
                    onClick={handleNext}
                    endIcon={<NextIcon />}
                    disabled={isSubmitting}
                    sx={{
                      borderRadius: '8px',
                      textTransform: 'none',
                      px: 3,
                      py: 1
                    }}
                  >
                    Next
                  </Button>
                ) : (
                  <Button
                    variant="contained"
                    color="primary"
                    type="submit"
                    disabled={isSubmitting || (!isAddMode && !hasChanges)}
                    startIcon={<SaveIcon />}
                    sx={{
                      borderRadius: '8px',
                      textTransform: 'none',
                      px: 3,
                      py: 1
                    }}
                  >
                    {isAddMode ? 'Create' : 'Save Changes'}
                  </Button>
                )}
              </Box>
            </Box>
          </form>
        </Box>
      </StyledPaper>

      {/* Confirmation Dialog */}
      <Dialog
        open={confirmDialogOpen}
        onClose={handleCloseConfirmDialog}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)'
          }
        }}
      >
        <DialogTitle sx={{ pb: 1 }}>
          {confirmDialogTitle}
        </DialogTitle>
        <DialogContent sx={{ pt: 2 }}>
          {confirmDialogContent}
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={handleCloseConfirmDialog}
            variant="outlined"
            startIcon={<CancelIcon />}
            sx={{
              borderRadius: '8px',
              textTransform: 'none'
            }}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            color="primary"
            startIcon={<CheckIcon />}
            disabled={isSubmitting}
            sx={{
              borderRadius: '8px',
              textTransform: 'none'
            }}
          >
            {isAddMode ? 'Create' : 'Save Changes'}
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export { MedicalFormBase, FormSection };
