import React from 'react';
import { Box, Paper, Typography, Chip, Grid, SxProps, Theme } from '@mui/material';
import { useTheme, alpha } from '@mui/material/styles';
import WarningAmberIcon from '@mui/icons-material/WarningAmber';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';

interface AlertItem {
  name: string;
  value: string | number;
  severity: 'warning' | 'error' | 'info';
  details?: string;
}

interface AlertBannerProps {
  title?: string;
  alerts: AlertItem[];
  sx?: SxProps<Theme>;
}

const AlertBanner: React.FC<AlertBannerProps> = ({
  title = 'Areas Needing Attention',
  alerts,
  sx = {}
}) => {
  const theme = useTheme();
  
  if (!alerts || alerts.length === 0) {
    return null;
  }

  // Get icon based on severity
  const getIcon = (severity: string) => {
    switch (severity) {
      case 'error':
        return <ErrorOutlineIcon sx={{ fontSize: '1rem' }} />;
      case 'info':
        return <InfoOutlinedIcon sx={{ fontSize: '1rem' }} />;
      case 'warning':
      default:
        return <WarningAmberIcon sx={{ fontSize: '1rem' }} />;
    }
  };

  // Get color based on severity
  const getColor = (severity: string) => {
    switch (severity) {
      case 'error':
        return theme.palette.error.main;
      case 'info':
        return theme.palette.info.main;
      case 'warning':
      default:
        return theme.palette.warning.main;
    }
  };

  return (
    <Paper
      elevation={0}
      sx={{
        p: 2,
        borderRadius: 2,
        border: `1px solid ${alpha(theme.palette.warning.main, 0.3)}`,
        bgcolor: alpha(theme.palette.warning.main, 0.05),
        ...sx
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, gap: 1 }}>
        <WarningAmberIcon color="warning" />
        <Typography variant="h6" sx={{ fontWeight: 600, color: theme.palette.warning.dark }}>
          {title}
        </Typography>
      </Box>
      
      <Grid container spacing={2}>
        {alerts.map((alert, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <Chip
                  icon={getIcon(alert.severity)}
                  label={alert.name}
                  size="small"
                  sx={{
                    bgcolor: alpha(getColor(alert.severity), 0.1),
                    color: getColor(alert.severity),
                    fontWeight: 500,
                    '& .MuiChip-icon': {
                      color: getColor(alert.severity)
                    }
                  }}
                />
              </Box>
              <Typography variant="body1" fontWeight={500}>
                {alert.value}
              </Typography>
              {alert.details && (
                <Typography variant="caption" color="text.secondary">
                  {alert.details}
                </Typography>
              )}
            </Box>
          </Grid>
        ))}
      </Grid>
    </Paper>
  );
};

export default AlertBanner;
