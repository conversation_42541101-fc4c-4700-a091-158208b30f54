import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  useTheme,
  alpha,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Tabs,
  Tab,
  Divider,
  useMediaQuery,
  IconButton
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import WarningIcon from '@mui/icons-material/Warning';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import EditIcon from '@mui/icons-material/Edit';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import MenuIcon from '@mui/icons-material/Menu';
import CloseIcon from '@mui/icons-material/Close';
import PersonIcon from '@mui/icons-material/Person';
import FavoriteIcon from '@mui/icons-material/Favorite';
import MedicalInformationIcon from '@mui/icons-material/MedicalInformation';
import PsychologyIcon from '@mui/icons-material/Psychology';
import AssessmentIcon from '@mui/icons-material/Assessment';
import HealthAndSafetyIcon from '@mui/icons-material/HealthAndSafety';
import VaccinesIcon from '@mui/icons-material/Vaccines';
import MedicationIcon from '@mui/icons-material/Medication';

// Define section group interface
interface SectionGroup {
  id: string;
  label: string;
  icon: React.ReactNode;
  sections: string[];
}

// Define section status interface
interface SectionStatus {
  id: string;
  label: string;
  status: 'complete' | 'partial' | 'incomplete' | 'error';
  percentage: number;
  errorCount?: number;
  missingFields?: string[];
  tabIndex: number;
  category?: string;
}

interface TwoPanelReviewProps {
  sections: SectionStatus[];
  onNavigate: (tabIndex: number) => void;
}

// Define section groups
const sectionGroups: SectionGroup[] = [
  {
    id: 'personal_info',
    label: 'Personal Information',
    icon: <PersonIcon />,
    sections: ['Basic Information', 'Medical Assignment', 'Contact Details', 'Emergency Contact', 'Visit Information']
  },
  {
    id: 'medical_data',
    label: 'Medical Data',
    icon: <FavoriteIcon />,
    sections: ['Medical Information', 'Vital Signs', 'Lab Results']
  },
  {
    id: 'mental_health',
    label: 'Mental Health',
    icon: <PsychologyIcon />,
    sections: ['Cognitive Health', 'Depression', 'Anxiety']
  },
  {
    id: 'assessments',
    label: 'Assessments',
    icon: <AssessmentIcon />,
    sections: ['Sleep Assessment', 'Pain Assessment', 'Frailty Assessment', 'Sleep & Pain']
  },
  {
    id: 'health_status',
    label: 'Health Status',
    icon: <HealthAndSafetyIcon />,
    sections: ['Health Status']
  },
  {
    id: 'preventive_care',
    label: 'Preventive Care',
    icon: <VaccinesIcon />,
    sections: ['Vaccinations']
  },
  {
    id: 'medications',
    label: 'Medications',
    icon: <MedicationIcon />,
    sections: ['Diagnosis & Prescriptions']
  }
];

const TwoPanelReview: React.FC<TwoPanelReviewProps> = ({ sections, onNavigate }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [activeGroup, setActiveGroup] = useState<string>(sectionGroups[0].id);
  const [mobileNavOpen, setMobileNavOpen] = useState<boolean>(false);

  // Get color for status
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'complete':
        return theme.palette.success.main;
      case 'partial':
        return theme.palette.warning.main;
      case 'error':
        return theme.palette.error.main;
      case 'incomplete':
      default:
        return theme.palette.grey[300];
    }
  };

  // Get background color (lighter version of status color)
  const getBackgroundColor = (status: string) => {
    switch (status) {
      case 'complete':
        return alpha(theme.palette.success.main, 0.1);
      case 'partial':
        return alpha(theme.palette.warning.main, 0.1);
      case 'error':
        return alpha(theme.palette.error.main, 0.1);
      case 'incomplete':
      default:
        return alpha(theme.palette.grey[300], 0.1);
    }
  };

  // Get icon for status
  const getStatusIcon = (status: string, errorCount?: number) => {
    switch (status) {
      case 'complete':
        return <CheckCircleIcon color="success" />;
      case 'partial':
        return <WarningIcon color="warning" />;
      case 'error':
        return (
          <>
            <ErrorIcon color="error" />
            {errorCount !== undefined && errorCount > 0 && (
              <Typography variant="caption" color="error" sx={{ ml: 0.5 }}>
                ({errorCount})
              </Typography>
            )}
          </>
        );
      case 'incomplete':
      default:
        return <HelpOutlineIcon color="disabled" />;
    }
  };

  // Calculate group completion percentage
  const calculateGroupCompletion = (groupId: string) => {
    const groupSections = sectionGroups.find(g => g.id === groupId)?.sections || [];
    const relevantSections = sections.filter(s => groupSections.includes(s.label));

    if (relevantSections.length === 0) return 0;

    const totalPercentage = relevantSections.reduce((sum, section) => sum + section.percentage, 0);
    return Math.round(totalPercentage / relevantSections.length);
  };

  // Check if group has any errors
  const groupHasErrors = (groupId: string) => {
    const groupSections = sectionGroups.find(g => g.id === groupId)?.sections || [];
    return sections.some(s => groupSections.includes(s.label) && s.status === 'error');
  };

  // Get sections for active group
  const getActiveSections = () => {
    const groupSections = sectionGroups.find(g => g.id === activeGroup)?.sections || [];

    // First try to filter by category if available
    const sectionsByCategory = sections.filter(s => s.category === sectionGroups.find(g => g.id === activeGroup)?.label);

    // If we have sections with matching category, use those
    if (sectionsByCategory.length > 0) {
      return sectionsByCategory;
    }

    // Otherwise fall back to the original logic
    return sections.filter(s => groupSections.includes(s.label));
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' } }}>
      {/* Mobile Navigation Toggle */}
      {isMobile && (
        <Box sx={{ mb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">
            {sectionGroups.find(g => g.id === activeGroup)?.label || 'Review'}
          </Typography>
          <IconButton
            onClick={() => setMobileNavOpen(!mobileNavOpen)}
            color="primary"
          >
            {mobileNavOpen ? <CloseIcon /> : <MenuIcon />}
          </IconButton>
        </Box>
      )}

      {/* Left Panel - Navigation */}
      <Box
        sx={{
          width: { xs: '100%', md: '280px' },
          flexShrink: 0,
          display: { xs: mobileNavOpen ? 'block' : 'none', md: 'block' },
          mb: { xs: 2, md: 0 },
          mr: { md: 3 }
        }}
      >
        <Paper
          elevation={3}
          sx={{
            p: 0,
            borderRadius: 2,
            height: '100%',
            overflow: 'hidden',
            border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
          }}
        >
          <Tabs
            orientation="vertical"
            variant="scrollable"
            value={activeGroup}
            onChange={(_, newValue) => {
              setActiveGroup(newValue);
              // Only change the active group, don't navigate to the section
            }}
            sx={{
              borderRight: 1,
              borderColor: 'divider',
              '& .MuiTab-root': {
                alignItems: 'flex-start',
                textAlign: 'left',
                pl: 2,
                py: 2,
                minHeight: '80px',
                transition: 'all 0.2s ease'
              },
              '& .MuiTabs-indicator': {
                width: '4px',
                borderTopRightRadius: 4,
                borderBottomRightRadius: 4
              }
            }}
          >
            {sectionGroups.map((group) => {
              const completion = calculateGroupCompletion(group.id);
              const hasErrors = groupHasErrors(group.id);

              return (
                <Tab
                  key={group.id}
                  value={group.id}
                  label={
                    <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                        <Box sx={{
                          mr: 1.5,
                          color: hasErrors ? 'error.main' :
                                 completion === 100 ? 'success.main' :
                                 completion > 0 ? 'warning.main' : 'text.secondary',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center'
                        }}>
                          {group.icon}
                        </Box>
                        <Typography
                          variant="body1"
                          component="span"
                          fontWeight={activeGroup === group.id ? "bold" : "medium"}
                          color={activeGroup === group.id ? "primary" : "text.primary"}
                        >
                          {group.label}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', pl: 0.5 }}>
                        <Box sx={{
                          width: '100%',
                          height: '6px',
                          backgroundColor: theme.palette.grey[200],
                          borderRadius: 3,
                          mr: 1,
                          overflow: 'hidden'
                        }}>
                          <Box sx={{
                            width: `${completion}%`,
                            height: '100%',
                            backgroundColor: hasErrors ? theme.palette.error.main :
                                            completion === 100 ? theme.palette.success.main :
                                            completion > 0 ? theme.palette.warning.main :
                                            theme.palette.grey[300],
                            borderRadius: 3
                          }} />
                        </Box>
                        <Typography
                          variant="caption"
                          sx={{
                            fontWeight: 'medium',
                            color: hasErrors ? 'error.main' :
                                   completion === 100 ? 'success.main' :
                                   completion > 0 ? 'warning.main' : 'text.secondary',
                            minWidth: '36px'
                          }}
                        >
                          {completion}%
                        </Typography>
                      </Box>
                    </Box>
                  }
                  sx={{
                    borderLeft: 4,
                    borderColor: hasErrors ? 'error.main' :
                                completion === 100 ? 'success.main' :
                                completion > 0 ? 'warning.main' :
                                'transparent',
                    backgroundColor: activeGroup === group.id ? alpha(theme.palette.primary.main, 0.08) : 'transparent',
                    '&:hover': {
                      backgroundColor: activeGroup === group.id ?
                                      alpha(theme.palette.primary.main, 0.12) :
                                      alpha(theme.palette.action.hover, 0.1)
                    }
                  }}
                />
              );
            })}
          </Tabs>
        </Paper>
      </Box>

      {/* Right Panel - Content */}
      <Box sx={{ flexGrow: 1 }}>
        <Typography variant="h6" sx={{ mb: 2, display: { xs: 'none', md: 'block' } }}>
          {sectionGroups.find(g => g.id === activeGroup)?.label || 'Review'}
        </Typography>

        <Grid container spacing={2}>
          {getActiveSections().map((section) => (
            <Grid item xs={12} sm={6} key={section.id}>
              <Paper
                elevation={2}
                sx={{
                  borderRadius: 2,
                  backgroundColor: getBackgroundColor(section.status),
                  borderLeft: `6px solid ${getStatusColor(section.status)}`,
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    boxShadow: 4,
                    cursor: 'pointer',
                    transform: 'translateY(-2px)'
                  },
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  overflow: 'hidden',
                  position: 'relative',
                  border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                }}
                onClick={() => onNavigate(section.tabIndex)}
              >
                {/* Section Header */}
                <Box
                  sx={{
                    p: 2,
                    pb: 2,
                    display: 'flex',
                    flexDirection: 'column',
                    borderBottom: `1px solid ${alpha(theme.palette.divider, 0.5)}`,
                    backgroundColor: alpha(getBackgroundColor(section.status), 0.7)
                  }}
                >
                  {/* Section Title and Icon */}
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    mb: 1
                  }}>
                    <Box sx={{ color: getStatusColor(section.status), mr: 1 }}>
                      {getStatusIcon(section.status, section.errorCount)}
                    </Box>
                    <Typography variant="subtitle1" fontWeight="medium">
                      {section.label}
                    </Typography>
                  </Box>

                  {/* Progress Bar */}
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    mb: 1.5
                  }}>
                    <Box sx={{
                      flexGrow: 1,
                      height: '8px',
                      backgroundColor: theme.palette.grey[200],
                      borderRadius: 4,
                      mr: 1,
                      overflow: 'hidden',
                    }}>
                      <Box sx={{
                        width: `${section.percentage}%`,
                        height: '100%',
                        backgroundColor: getStatusColor(section.status),
                        borderRadius: 4
                      }} />
                    </Box>
                    <Typography
                      variant="caption"
                      fontWeight="medium"
                      sx={{
                        minWidth: '50px',
                        color: section.status === 'complete' ? "success.main" :
                               section.status === 'partial' ? "warning.main" :
                               section.status === 'error' ? "error.main" : "text.secondary"
                      }}
                    >
                      {section.percentage}% Complete
                    </Typography>
                  </Box>

                  {/* Action Button */}
                  <Button
                    fullWidth
                    size="small"
                    variant="contained"
                    disableElevation
                    color={section.status === 'complete' ? "success" : section.status === 'partial' ? "warning" : "error"}
                    onClick={(e) => {
                      e.stopPropagation(); // Prevent event from bubbling up to parent
                      onNavigate(section.tabIndex);
                    }}
                    startIcon={section.status === 'complete' ? <EditIcon /> : <ArrowForwardIcon />}
                    sx={{
                      fontWeight: 'medium',
                      '&.MuiButton-containedWarning': {
                        color: 'white'
                      },
                      '&.MuiButton-containedError': {
                        color: 'white'
                      }
                    }}
                  >
                    {section.status === 'complete' ? 'Review' : 'Complete Now'}
                  </Button>
                </Box>

                {/* Section Content */}
                <Box sx={{ p: 2, pt: 1.5, flexGrow: 1 }}>
                  {/* Missing fields */}
                  {section.missingFields && section.missingFields.length > 0 && (
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="subtitle2" fontWeight="medium" color="warning.main">
                        Missing Fields:
                      </Typography>
                      <List dense disablePadding sx={{ mt: 0.5 }}>
                        {section.missingFields.map((field, idx) => (
                          <ListItem key={idx} disablePadding sx={{ py: 0.5 }}>
                            <ListItemIcon sx={{ minWidth: 28 }}>
                              <WarningIcon fontSize="small" color="warning" />
                            </ListItemIcon>
                            <ListItemText
                              primary={field}
                              primaryTypographyProps={{
                                variant: 'body2',
                                fontWeight: 'medium',
                                color: 'text.primary'
                              }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </Box>
                  )}



                  {/* No issues message */}
                  {(!section.missingFields || section.missingFields.length === 0) && (
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <CheckCircleIcon color="success" fontSize="small" sx={{ mr: 1 }} />
                      <Typography variant="body2" fontWeight="medium" color="success.main">
                        This section is complete with no missing fields.
                      </Typography>
                    </Box>
                  )}
                </Box>
              </Paper>
            </Grid>
          ))}
        </Grid>
      </Box>
    </Box>
  );
};

export default TwoPanelReview;
