/* Global Dark Mode Overrides for MUI Components */

/* Landing Page Overrides */
.dark-mode .modern-landing {
  background-color: #121212 !important;
  color: #fff !important;
}

.dark-mode .landing-content,
.dark-mode .landing-left {
  background-color: transparent !important;
}

.dark-mode .landing-title {
  color: #fff !important;
}

.dark-mode .landing-description {
  color: #aaa !important;
}

/* Exclude login page from global dark mode overrides */
.dark-mode .login-container,
.dark-mode .login-card,
.dark-mode .login-image-section,
.dark-mode .login-form-section {
  /* These elements will be styled by Login.css */
  background-color: initial;
  color: initial;
}

/* Force the body and html to have dark background in dark mode */
.dark-mode body,
.dark-mode html,
.dark-mode #root {
  background-color: #121212 !important;
}

/* Typography */
.dark-mode .MuiTypography-root {
  color: var(--text-color, #fff) !important;
}

.dark-mode .MuiTypography-colorTextPrimary {
  color: var(--text-color, #fff) !important;
}

.dark-mode .MuiTypography-colorTextSecondary {
  color: var(--text-secondary-color, #aaa) !important;
}

/* Card titles and headings */
.dark-mode .MuiTypography-subtitle1 {
  color: var(--text-color, #fff) !important;
}

.dark-mode .MuiTypography-h6 {
  color: var(--text-color, #fff) !important;
}

.dark-mode .MuiTypography-h5 {
  color: var(--text-color, #fff) !important;
}

.dark-mode .MuiTypography-h4 {
  color: var(--text-color, #fff) !important;
}

/* Form Controls */
.dark-mode .MuiInputBase-input {
  color: var(--text-color, #fff) !important;
}

.dark-mode .MuiInputLabel-root {
  color: var(--text-secondary-color, #aaa) !important;
}

.dark-mode .MuiOutlinedInput-notchedOutline {
  border-color: var(--border-color, rgba(255, 255, 255, 0.2)) !important;
}

.dark-mode .MuiSelect-select {
  color: var(--text-color, #fff) !important;
}

.dark-mode .MuiFormHelperText-root {
  color: var(--text-secondary-color, #aaa) !important;
}

/* When the helper text is a warning, keep it visible */
.dark-mode .MuiFormHelperText-root.Mui-error,
.dark-mode .MuiFormHelperText-root[data-warning="true"] {
  color: #f59e0b !important;
}

/* Input adornments (units like mg/dL) */
.dark-mode .MuiInputAdornment-root,
.dark-mode .MuiInputBase-input + span {
  color: var(--text-secondary-color, #aaa) !important;
}

/* Checkboxes and radio buttons */
.dark-mode .MuiCheckbox-root,
.dark-mode .MuiRadio-root {
  color: var(--text-secondary-color, #aaa) !important;
}

.dark-mode .MuiCheckbox-root.Mui-checked,
.dark-mode .MuiRadio-root.Mui-checked {
  color: var(--primary-color, #90caf9) !important;
}

/* Form labels */
.dark-mode .MuiFormControlLabel-label {
  color: var(--text-color, #fff) !important;
}

/* Dialog content */
.dark-mode .MuiDialog-paper {
  background-color: var(--card-bg-color, #1e1e1e) !important;
  color: var(--text-color, #fff) !important;
}

.dark-mode .MuiDialogTitle-root {
  color: var(--text-color, #fff) !important;
}

.dark-mode .MuiDialogContentText-root {
  color: var(--text-secondary-color, #aaa) !important;
}

/* Card content */
.dark-mode .MuiCard-root {
  background-color: var(--card-bg-color, #1e1e1e) !important;
}

.dark-mode .MuiCardContent-root {
  color: var(--text-color, #fff) !important;
  background-color: transparent !important;
}

/* Card headers and backgrounds */
.dark-mode .MuiBox-root[style*="background-color"] {
  background-color: transparent !important;
}

.dark-mode .MuiBox-root[style*="border-bottom"] {
  border-bottom-color: var(--border-color, rgba(255, 255, 255, 0.1)) !important;
}

/* Specific fix for PatientDetail card headers */
.dark-mode .MuiCard-root .MuiBox-root {
  background-color: transparent !important;
  border-bottom-color: var(--border-color, rgba(255, 255, 255, 0.1)) !important;
}

.dark-mode .MuiBox-root[style*="bgcolor"] {
  background-color: transparent !important;
}

/* Fix for dashboard card headers and backgrounds */
.dark-mode .MuiCardContent-root .MuiBox-root {
  background-color: transparent !important;
}

/* Fix for dashboard stat cards */
.dark-mode .MuiCard-root {
  background-color: var(--card-bg-color, #1e1e1e) !important;
}

.dark-mode .MuiCardContent-root {
  background-color: transparent !important;
}

/* Tabs */
.dark-mode .MuiTab-root {
  color: var(--text-secondary-color, #aaa) !important;
}

.dark-mode .MuiTab-root.Mui-selected {
  color: var(--primary-color, #90caf9) !important;
}

/* Paper */
.dark-mode .MuiPaper-root {
  background-color: var(--card-bg-color, #1e1e1e) !important;
  color: var(--text-color, #fff) !important;
}

/* Fix for any Box components with background color */
.dark-mode .MuiBox-root {
  background-color: transparent !important;
}

/* Fix for Box components in cards */
.dark-mode .MuiCard-root .MuiBox-root {
  background-color: transparent !important;
}

/* Fix for Box components in CardContent */
.dark-mode .MuiCardContent-root .MuiBox-root {
  background-color: transparent !important;
}

/* Exception for dashboard header gradient backgrounds */
.dark-mode .MuiBox-root[style*="linear-gradient"] {
  background: linear-gradient(135deg, #2C4B2B 0%, #1E3A1D 100%) !important;
}

/* Dividers */
.dark-mode .MuiDivider-root {
  border-color: var(--border-color, rgba(255, 255, 255, 0.1)) !important;
}

/* Buttons */
.dark-mode .MuiButton-outlined {
  border-color: var(--border-color, rgba(255, 255, 255, 0.2)) !important;
  color: var(--text-color, #fff) !important;
}

.dark-mode .MuiButton-contained {
  background-color: var(--primary-color, #90caf9) !important;
  color: #000 !important;
}

/* Fix for date pickers */
.dark-mode .MuiPickersDay-root {
  color: var(--text-color, #fff) !important;
}

.dark-mode .MuiPickersDay-root.Mui-selected {
  background-color: var(--primary-color, #90caf9) !important;
  color: #000 !important;
}

/* Fix for select dropdowns */
.dark-mode .MuiMenu-paper {
  background-color: var(--card-bg-color, #1e1e1e) !important;
}

.dark-mode .MuiMenuItem-root {
  color: var(--text-color, #fff) !important;
}

.dark-mode .MuiMenuItem-root:hover {
  background-color: rgba(255, 255, 255, 0.08) !important;
}

/* Progress indicators */
.dark-mode .MuiCircularProgress-root {
  color: var(--primary-color, #90caf9) !important;
}

.dark-mode .MuiLinearProgress-root {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

/* Icons */
.dark-mode .MuiSvgIcon-root {
  color: inherit;
}

.dark-mode .MuiIconButton-root {
  color: var(--text-color, #fff) !important;
}

/* Alerts */
.dark-mode .MuiAlert-standardInfo {
  background-color: rgba(144, 202, 249, 0.1) !important;
  color: #90caf9 !important;
}

.dark-mode .MuiAlert-standardWarning {
  background-color: rgba(255, 167, 38, 0.1) !important;
  color: #ffa726 !important;
}

.dark-mode .MuiAlert-standardError {
  background-color: rgba(244, 67, 54, 0.1) !important;
  color: #f44336 !important;
}

.dark-mode .MuiAlert-standardSuccess {
  background-color: rgba(102, 187, 106, 0.1) !important;
  color: #66bb6a !important;
}

/* Chips */
.dark-mode .MuiChip-root {
  background-color: rgba(255, 255, 255, 0.08) !important;
  color: var(--text-color, #fff) !important;
}

.dark-mode .MuiChip-outlinedPrimary {
  color: var(--primary-color, #90caf9) !important;
  border-color: rgba(144, 202, 249, 0.5) !important;
}

/* Tables */
.dark-mode .MuiTableCell-root {
  color: var(--text-color, #fff) !important;
  border-bottom-color: var(--border-color, rgba(255, 255, 255, 0.1)) !important;
}

.dark-mode .MuiTableHead-root .MuiTableCell-root {
  color: var(--text-secondary-color, #aaa) !important;
}

.dark-mode .MuiTableRow-root:hover {
  background-color: rgba(255, 255, 255, 0.04) !important;
}

/* Tooltips */
.dark-mode .MuiTooltip-tooltip {
  background-color: #424242 !important;
  color: #fff !important;
}

/* Badges */
.dark-mode .MuiBadge-badge {
  background-color: var(--primary-color, #90caf9) !important;
  color: #000 !important;
}

/* Avatars */
.dark-mode .MuiAvatar-root {
  background-color: rgba(255, 255, 255, 0.08) !important;
  color: var(--text-color, #fff) !important;
}

/* Sliders */
.dark-mode .MuiSlider-root {
  color: var(--primary-color, #90caf9) !important;
}

.dark-mode .MuiSlider-rail {
  background-color: rgba(255, 255, 255, 0.2) !important;
}

.dark-mode .MuiSlider-track {
  background-color: var(--primary-color, #90caf9) !important;
}

.dark-mode .MuiSlider-thumb {
  background-color: var(--primary-color, #90caf9) !important;
}

/* Switches */
.dark-mode .MuiSwitch-root .MuiSwitch-track {
  background-color: rgba(255, 255, 255, 0.2) !important;
}

.dark-mode .MuiSwitch-root .Mui-checked + .MuiSwitch-track {
  background-color: rgba(144, 202, 249, 0.5) !important;
}

.dark-mode .MuiSwitch-root .MuiSwitch-thumb {
  background-color: #f5f5f5 !important;
}

.dark-mode .MuiSwitch-root .Mui-checked .MuiSwitch-thumb {
  background-color: var(--primary-color, #90caf9) !important;
}
