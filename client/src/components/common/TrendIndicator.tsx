import React from 'react';
import { Box, Tooltip, Typography } from '@mui/material';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';
import RemoveIcon from '@mui/icons-material/Remove';

interface TrendIndicatorProps {
  current: number | null | undefined;
  previous: number | null | undefined;
  isLowerBetter?: boolean;
  threshold?: number; // Minimum percentage change to show trend
  tooltipPrefix?: string;
}

const TrendIndicator: React.FC<TrendIndicatorProps> = ({
  current,
  previous,
  isLowerBetter = false,
  threshold = 1, // Default 1% threshold
  tooltipPrefix = ''
}) => {
  // If either value is missing, we can't calculate a trend
  if (current === null || current === undefined || previous === null || previous === undefined || previous === 0) {
    return null;
  }

  // Calculate percentage change
  const percentChange = ((current - previous) / previous) * 100;
  const absoluteChange = Math.abs(percentChange);

  // If change is below threshold, show no significant change
  if (absoluteChange < threshold) {
    return (
      <Tooltip title={`${tooltipPrefix}No significant change (${percentChange.toFixed(1)}%)`} arrow>
        <Box sx={{ display: 'flex', alignItems: 'center', color: 'text.secondary' }}>
          <RemoveIcon sx={{ fontSize: '1rem' }} />
        </Box>
      </Tooltip>
    );
  }

  // Determine if the change is good or bad
  const isImproving = isLowerBetter ? percentChange < 0 : percentChange > 0;
  
  // Set color and icon based on whether the change is good or bad
  const color = isImproving ? 'success.main' : 'error.main';
  const Icon = percentChange > 0 ? ArrowUpwardIcon : ArrowDownwardIcon;
  
  // Create tooltip text
  const changeText = percentChange > 0 ? 'increased' : 'decreased';
  const tooltipText = `${tooltipPrefix}${changeText} by ${absoluteChange.toFixed(1)}% since last visit`;

  return (
    <Tooltip title={tooltipText} arrow>
      <Box sx={{ display: 'flex', alignItems: 'center', color }}>
        <Icon sx={{ fontSize: '1rem' }} />
        <Typography variant="caption" sx={{ fontWeight: 500 }}>
          {absoluteChange.toFixed(1)}%
        </Typography>
      </Box>
    </Tooltip>
  );
};

export default TrendIndicator;
