import React from 'react';
import { Box, useTheme } from '@mui/material';

interface SparklineChartProps {
  data: number[];
  height?: number;
  width?: number;
  color?: string;
  lineColor?: string;
  fillColor?: string;
  lineWidth?: number;
  isLowerBetter?: boolean;
}

export const SparklineChart: React.FC<SparklineChartProps> = ({
  data,
  height = 20,
  width = 60,
  lineColor,
  fillColor,
  lineWidth = 1.5,
  isLowerBetter = false
}) => {
  const theme = useTheme();

  // If no data or only one point, we can't draw a line
  if (!data || data.length < 2) {
    return <Box sx={{ height, width }} />;
  }

  // Determine if the trend is improving
  const firstValue = data[0];
  const lastValue = data[data.length - 1];
  const isImproving = isLowerBetter ? lastValue < firstValue : lastValue > firstValue;

  // Set default colors based on theme and trend
  const defaultLineColor = isImproving ? theme.palette.success.main : theme.palette.error.main;
  const defaultFillColor = isImproving ?
    theme.palette.mode === 'dark' ? 'rgba(74, 222, 128, 0.1)' : 'rgba(74, 222, 128, 0.05)' :
    theme.palette.mode === 'dark' ? 'rgba(239, 68, 68, 0.1)' : 'rgba(239, 68, 68, 0.05)';

  // Use provided colors or defaults
  const actualLineColor = lineColor || defaultLineColor;
  const actualFillColor = fillColor || defaultFillColor;

  // Find min and max values for scaling
  const minValue = Math.min(...data);
  const maxValue = Math.max(...data);
  const range = maxValue - minValue || 1; // Avoid division by zero

  // Calculate points for the polyline
  const points = data.map((value, index) => {
    const x = (index / (data.length - 1)) * width;
    const y = height - ((value - minValue) / range) * height;
    return `${x},${y}`;
  }).join(' ');

  // Calculate points for the polygon (filled area)
  const fillPoints = `0,${height} ${points} ${width},${height}`;

  return (
    <Box sx={{ height, width, position: 'relative' }}>
      <svg width={width} height={height} style={{ display: 'block' }}>
        {/* Filled area under the line */}
        <polygon
          points={fillPoints}
          fill={actualFillColor}
          stroke="none"
        />

        {/* Line */}
        <polyline
          points={points}
          fill="none"
          stroke={actualLineColor}
          strokeWidth={lineWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
        />

        {/* Start point */}
        <circle
          cx="0"
          cy={height - ((data[0] - minValue) / range) * height}
          r={lineWidth}
          fill={actualLineColor}
        />

        {/* End point */}
        <circle
          cx={width}
          cy={height - ((data[data.length - 1] - minValue) / range) * height}
          r={lineWidth + 0.5}
          fill={actualLineColor}
        />
      </svg>
    </Box>
  );
};

export default SparklineChart;
