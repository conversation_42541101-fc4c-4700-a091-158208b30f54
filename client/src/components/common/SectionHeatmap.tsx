import React, { useState } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  useTheme,
  alpha,
  Button,
  Collapse,
  List,
  ListItem,
  ListItemText
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import WarningIcon from '@mui/icons-material/Warning';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import EditIcon from '@mui/icons-material/Edit';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import ExpandLessIcon from '@mui/icons-material/ExpandLess';

interface SectionStatus {
  id: string;
  label: string;
  status: 'complete' | 'partial' | 'incomplete' | 'error';
  percentage: number;
  errorCount?: number;
  missingFields?: string[];
  validationErrors?: string[];
  tabIndex: number;
}

interface SectionHeatmapProps {
  sections: SectionStatus[];
  onClick?: (sectionId: string) => void;
  showDetails?: boolean;
}

const SectionHeatmap: React.FC<SectionHeatmapProps> = ({
  sections,
  onClick,
  showDetails = false
}) => {
  const theme = useTheme();
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});

  // Initialize expanded state for sections with errors
  React.useEffect(() => {
    const initialExpandedState: Record<string, boolean> = {};

    sections.forEach(section => {
      // Auto-expand sections with errors or incomplete required fields
      const shouldExpand = section.status === 'error' ||
        (section.status === 'incomplete' &&
         ['Basic Information', 'Contact Details', 'Medical Assignment'].includes(section.label));

      initialExpandedState[section.id] = shouldExpand;
    });

    setExpandedSections(initialExpandedState);
  }, [sections]);

  // Toggle section expansion
  const toggleSectionExpansion = (sectionId: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  // Get color for status
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'complete':
        return theme.palette.success.main;
      case 'partial':
        return theme.palette.warning.main;
      case 'error':
        return theme.palette.error.main;
      case 'incomplete':
      default:
        return theme.palette.grey[300];
    }
  };

  // Get background color (lighter version of status color)
  const getBackgroundColor = (status: string) => {
    switch (status) {
      case 'complete':
        return alpha(theme.palette.success.main, 0.1);
      case 'partial':
        return alpha(theme.palette.warning.main, 0.1);
      case 'error':
        return alpha(theme.palette.error.main, 0.1);
      case 'incomplete':
      default:
        return alpha(theme.palette.grey[300], 0.1);
    }
  };

  // Get icon for status
  const getStatusIcon = (status: string, errorCount?: number) => {
    switch (status) {
      case 'complete':
        return <CheckCircleIcon color="success" />;
      case 'partial':
        return <WarningIcon color="warning" />;
      case 'error':
        return (
          <>
            <ErrorIcon color="error" />
            {errorCount !== undefined && errorCount > 0 && (
              <Typography variant="caption" color="error" sx={{ ml: 0.5 }}>
                ({errorCount})
              </Typography>
            )}
          </>
        );
      case 'incomplete':
      default:
        return <HelpOutlineIcon color="disabled" />;
    }
  };

  return (
    <Grid container spacing={2}>
      {sections.map((section) => (
        <Grid item xs={12} sm={showDetails ? 12 : 6} md={showDetails ? 6 : 3} key={section.id}>
          <Paper
            elevation={1}
            sx={{
              borderRadius: 2,
              backgroundColor: getBackgroundColor(section.status),
              borderLeft: `4px solid ${getStatusColor(section.status)}`,
              transition: 'box-shadow 0.2s ease',
              '&:hover': {
                boxShadow: 3,
              },
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              overflow: 'hidden'
            }}
          >
            {/* Section Header */}
            <Box
              sx={{
                p: 2,
                display: 'flex',
                flexDirection: { xs: 'column', sm: 'row' },
                alignItems: { xs: 'flex-start', sm: 'center' },
                cursor: showDetails ? 'pointer' : 'default',
                borderBottom: showDetails && expandedSections[section.id] ? `1px dashed ${theme.palette.divider}` : 'none'
              }}
              onClick={showDetails ? () => toggleSectionExpansion(section.id) : undefined}
            >
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                mb: { xs: 1, sm: 0 },
                width: { xs: '100%', sm: 'auto' },
                flexGrow: 1
              }}>
                <Box sx={{ color: getStatusColor(section.status), mr: 1 }}>
                  {getStatusIcon(section.status, section.errorCount)}
                </Box>
                <Box sx={{ flexGrow: 1 }}>
                  <Typography variant="subtitle1" fontWeight="medium">
                    {section.label}
                    {showDetails && (
                      expandedSections[section.id] ?
                        <ExpandLessIcon sx={{ ml: 1, verticalAlign: 'middle', fontSize: '0.9rem' }} /> :
                        <ExpandMoreIcon sx={{ ml: 1, verticalAlign: 'middle', fontSize: '0.9rem' }} />
                    )}
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box sx={{
                      width: '100px',
                      height: '6px',
                      backgroundColor: theme.palette.grey[200],
                      borderRadius: 3,
                      mr: 1,
                      overflow: 'hidden',
                    }}>
                      <Box sx={{
                        width: `${section.percentage}%`,
                        height: '100%',
                        backgroundColor: getStatusColor(section.status),
                      }} />
                    </Box>
                    <Typography variant="caption" color="text.secondary">
                      {section.percentage}% Complete
                    </Typography>
                  </Box>
                </Box>
              </Box>

              <Button
                size="small"
                variant="outlined"
                color={section.status === 'complete' ? "success" : section.status === 'partial' ? "warning" : "error"}
                onClick={(e) => {
                  e.stopPropagation();
                  onClick && onClick(section.id);
                }}
                startIcon={section.status === 'complete' ? <EditIcon /> : <ArrowForwardIcon />}
                sx={{
                  ml: { xs: 0, sm: 2 },
                  mt: { xs: 1, sm: 0 },
                  whiteSpace: 'nowrap'
                }}
              >
                {section.status === 'complete' ? 'Review' : 'Complete Now'}
              </Button>
            </Box>

            {/* Expanded Details (only shown when showDetails is true) */}
            {showDetails && (
              <Collapse in={expandedSections[section.id]}>
                <Box sx={{ p: 2, pt: 1 }}>
                  {/* Missing fields */}
                  {section.missingFields && section.missingFields.length > 0 && (
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" fontWeight="medium" color="text.secondary">
                        Missing Fields:
                      </Typography>
                      <List dense disablePadding sx={{ mt: 0.5 }}>
                        {section.missingFields.map((field, idx) => (
                          <ListItem key={idx} disablePadding sx={{ py: 0.5 }}>
                            <ListItemText
                              primary={field}
                              primaryTypographyProps={{
                                variant: 'body2',
                                color: 'text.secondary'
                              }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </Box>
                  )}

                  {/* Validation errors */}
                  {section.validationErrors && section.validationErrors.length > 0 && (
                    <Box sx={{ mb: 1 }}>
                      <Typography variant="body2" fontWeight="medium" color="error">
                        Validation Issues:
                      </Typography>
                      <List dense disablePadding sx={{ mt: 0.5 }}>
                        {section.validationErrors.map((error, idx) => (
                          <ListItem key={idx} disablePadding sx={{ py: 0.5 }}>
                            <ListItemText
                              primary={error}
                              primaryTypographyProps={{
                                variant: 'body2',
                                color: 'error'
                              }}
                            />
                          </ListItem>
                        ))}
                      </List>
                    </Box>
                  )}

                  {/* No issues message */}
                  {(!section.missingFields || section.missingFields.length === 0) &&
                   (!section.validationErrors || section.validationErrors.length === 0) && (
                    <Typography variant="body2" color="success.main" sx={{ fontStyle: 'italic' }}>
                      This section is complete with no validation issues.
                    </Typography>
                  )}
                </Box>
              </Collapse>
            )}
          </Paper>
        </Grid>
      ))}
    </Grid>
  );
};

export default SectionHeatmap;
