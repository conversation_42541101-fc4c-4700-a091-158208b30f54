import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  IconButton,
  Collapse,
  Tooltip,
  CircularProgress
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import { getGuidanceByContext, ClinicalGuidance } from '../../services/clinicalGuidanceService';

interface CollapsibleGuidanceProps {
  title?: string;
  contextKey?: string;
  children?: React.ReactNode;
}

const CollapsibleGuidance: React.FC<CollapsibleGuidanceProps> = ({
  title = "Clinical Guidance",
  contextKey,
  children
}) => {
  const [expanded, setExpanded] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dynamicGuidance, setDynamicGuidance] = useState<ClinicalGuidance[]>([]);
  const [hasLoadedGuidance, setHasLoadedGuidance] = useState(false);

  // Fetch guidance content when expanded for the first time
  useEffect(() => {
    const fetchGuidance = async () => {
      if (!contextKey || hasLoadedGuidance || !expanded) return;

      try {
        setLoading(true);
        setError(null);
        const guidanceData = await getGuidanceByContext(contextKey);
        setDynamicGuidance(guidanceData);
        setHasLoadedGuidance(true);
      } catch (err) {
        console.error(`Error fetching guidance for context ${contextKey}:`, err);
        setError('Failed to load guidance content');
      } finally {
        setLoading(false);
      }
    };

    fetchGuidance();
  }, [contextKey, expanded, hasLoadedGuidance]);

  const toggleExpanded = () => {
    setExpanded(!expanded);
  };

  // Render guidance content
  const renderGuidanceContent = () => {
    if (loading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
          <CircularProgress size={24} />
        </Box>
      );
    }

    if (error) {
      return (
        <Typography color="error" variant="body2">
          {error}
        </Typography>
      );
    }

    if (contextKey && dynamicGuidance.length > 0) {
      // Render dynamic guidance content
      return dynamicGuidance.map((guidance) => (
        <Box key={guidance.guidance_id}>
          {guidance.title !== title && (
            <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
              {guidance.title}
            </Typography>
          )}
          <Box dangerouslySetInnerHTML={{ __html: guidance.content }} />
        </Box>
      ));
    }

    // Fallback to static content
    return children;
  };

  return (
    <Box sx={{ mb: 2 }}>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          cursor: 'pointer',
          color: 'primary.main',
          '&:hover': {
            color: 'primary.dark',
          }
        }}
        onClick={toggleExpanded}
      >
        <InfoIcon fontSize="small" sx={{ mr: 1 }} />
        <Typography variant="subtitle2" sx={{ fontWeight: 600 }}>
          {title}
        </Typography>
        <IconButton
          size="small"
          sx={{ ml: 1, p: 0 }}
          onClick={(e) => {
            e.stopPropagation();
            toggleExpanded();
          }}
        >
          {expanded ? <KeyboardArrowUpIcon fontSize="small" /> : <KeyboardArrowDownIcon fontSize="small" />}
        </IconButton>
      </Box>

      <Collapse in={expanded}>
        <Box
          sx={{
            backgroundColor: 'rgba(25, 118, 210, 0.05)',
            borderLeft: '4px solid #1976d2',
            p: 2,
            borderRadius: 1,
            mt: 1
          }}
        >
          {renderGuidanceContent()}
        </Box>
      </Collapse>
    </Box>
  );
};

export default CollapsibleGuidance;
