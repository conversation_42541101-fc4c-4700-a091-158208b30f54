import React from 'react';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  color?: string;
  fullScreen?: boolean;
  message?: string;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'medium',
  color = '#2C4B2B', // Default to primary color
  fullScreen = false,
  message
}) => {
  // Determine spinner size
  const spinnerSizes = {
    small: { width: '20px', height: '20px', border: '3px' },
    medium: { width: '40px', height: '40px', border: '4px' },
    large: { width: '60px', height: '60px', border: '5px' }
  };
  
  const { width, height, border } = spinnerSizes[size];
  
  // Container styles
  const containerStyle: React.CSSProperties = {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
    gap: '1rem',
    ...(fullScreen ? {
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(255, 255, 255, 0.8)',
      zIndex: 9999
    } : {})
  };
  
  // Spinner styles
  const spinnerStyle: React.CSSProperties = {
    width,
    height,
    border: `${border} solid rgba(44, 75, 43, 0.1)`,
    borderTop: `${border} solid ${color}`,
    borderRadius: '50%',
    animation: 'spin 1s linear infinite'
  };
  
  // Message styles
  const messageStyle: React.CSSProperties = {
    color: '#666',
    fontSize: size === 'small' ? '0.8rem' : size === 'large' ? '1.2rem' : '1rem',
    fontWeight: 500
  };
  
  return (
    <div style={containerStyle} className="loading-spinner-container">
      <div style={spinnerStyle} className="loading-spinner" />
      {message && <div style={messageStyle}>{message}</div>}
      <style>
        {`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}
      </style>
    </div>
  );
};

export default LoadingSpinner;
