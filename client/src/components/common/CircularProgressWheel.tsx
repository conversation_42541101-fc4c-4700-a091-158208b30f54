import React from 'react';
import { Box, Typography, useTheme } from '@mui/material';

interface SectionStatus {
  id: string;
  label: string;
  status: 'complete' | 'partial' | 'incomplete' | 'error';
  percentage: number;
}

interface CircularProgressWheelProps {
  sections: SectionStatus[];
  size?: number;
  thickness?: number;
  onClick?: (sectionId: string) => void;
}

const CircularProgressWheel: React.FC<CircularProgressWheelProps> = ({
  sections,
  size = 300,
  thickness = 30,
  onClick
}) => {
  const theme = useTheme();
  const radius = (size - thickness) / 2;
  const circumference = 2 * Math.PI * radius;
  const totalSections = sections.length;
  const sectionAngle = 360 / totalSections;
  
  // Calculate overall completion percentage
  const overallPercentage = Math.round(
    sections.reduce((sum, section) => sum + section.percentage, 0) / sections.length
  );

  // Get color for status
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'complete':
        return theme.palette.success.main;
      case 'partial':
        return theme.palette.warning.main;
      case 'error':
        return theme.palette.error.main;
      case 'incomplete':
      default:
        return theme.palette.grey[300];
    }
  };

  return (
    <Box
      sx={{
        position: 'relative',
        width: size,
        height: size,
        margin: '0 auto',
      }}
    >
      {/* Center text showing overall completion */}
      <Box
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          textAlign: 'center',
          zIndex: 1,
        }}
      >
        <Typography variant="h3" color="primary" fontWeight="bold">
          {overallPercentage}%
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Complete
        </Typography>
      </Box>

      {/* SVG for the circular progress wheel */}
      <svg
        width={size}
        height={size}
        viewBox={`0 0 ${size} ${size}`}
        style={{ transform: 'rotate(-90deg)' }}
      >
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          fill="none"
          stroke={theme.palette.grey[100]}
          strokeWidth={thickness}
        />

        {/* Section segments */}
        {sections.map((section, index) => {
          const startAngle = index * sectionAngle;
          const endAngle = startAngle + sectionAngle;
          
          // Calculate arc path
          const startX = size / 2 + radius * Math.cos((startAngle * Math.PI) / 180);
          const startY = size / 2 + radius * Math.sin((startAngle * Math.PI) / 180);
          const endX = size / 2 + radius * Math.cos((endAngle * Math.PI) / 180);
          const endY = size / 2 + radius * Math.sin((endAngle * Math.PI) / 180);
          
          // Calculate the arc length
          const arcLength = (section.percentage / 100) * (circumference / totalSections);
          
          // Determine if this is a large arc (> 180 degrees)
          const largeArcFlag = sectionAngle > 180 ? 1 : 0;
          
          return (
            <g key={section.id} onClick={() => onClick && onClick(section.id)} style={{ cursor: 'pointer' }}>
              {/* Section background */}
              <path
                d={`M ${size/2} ${size/2} L ${startX} ${startY} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${endX} ${endY} Z`}
                fill={theme.palette.grey[50]}
                stroke={theme.palette.grey[200]}
                strokeWidth="1"
              />
              
              {/* Section progress arc */}
              <path
                d={`M ${size/2} ${size/2} L ${startX} ${startY} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${endX} ${endY} Z`}
                fill={getStatusColor(section.status)}
                opacity={0.7}
                strokeWidth="0"
                style={{ 
                  clipPath: `polygon(${size/2}px ${size/2}px, ${startX}px ${startY}px, ${endX}px ${endY}px)`,
                  transition: 'fill 0.3s ease'
                }}
              />
              
              {/* Section label */}
              <text
                x={size/2 + (radius * 0.7) * Math.cos(((startAngle + sectionAngle/2) * Math.PI) / 180)}
                y={size/2 + (radius * 0.7) * Math.sin(((startAngle + sectionAngle/2) * Math.PI) / 180)}
                textAnchor="middle"
                dominantBaseline="middle"
                fill={theme.palette.text.primary}
                fontSize="10"
                fontWeight="bold"
                style={{ 
                  transform: `rotate(${startAngle + sectionAngle/2 + 90}deg)`,
                  transformOrigin: `${size/2 + (radius * 0.7) * Math.cos(((startAngle + sectionAngle/2) * Math.PI) / 180)}px ${size/2 + (radius * 0.7) * Math.sin(((startAngle + sectionAngle/2) * Math.PI) / 180)}px`
                }}
              >
                {section.label}
              </text>
            </g>
          );
        })}
      </svg>
    </Box>
  );
};

export default CircularProgressWheel;
