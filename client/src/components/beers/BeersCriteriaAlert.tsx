import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>ert<PERSON><PERSON><PERSON>,
  <PERSON>,
  Button,
  <PERSON>lapse,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  IconButton,
  Paper,
  TextField,
  Typography,
  useTheme
} from '@mui/material';
import {
  Close as CloseIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';
import { BeersCriteriaAlert as BeersCriteriaAlertType } from '../../services/beersCriteriaService';

interface BeersCriteriaAlertProps {
  alerts: BeersCriteriaAlertType[];
  onOverride: (alert: BeersCriteriaAlertType, reason: string) => void;
  onClose?: () => void;
}

const BeersCriteriaAlert: React.FC<BeersCriteriaAlertProps> = ({
  alerts,
  onOverride,
  onClose
}) => {
  const theme = useTheme();
  const [expandedAlerts, setExpandedAlerts] = useState<number[]>([]);
  const [overrideDialogOpen, setOverrideDialogOpen] = useState<boolean>(false);
  const [currentAlert, setCurrentAlert] = useState<BeersCriteriaAlertType | null>(null);
  const [overrideReason, setOverrideReason] = useState<string>('');

  if (!alerts || alerts.length === 0) {
    return null;
  }

  const handleExpandAlert = (alertIndex: number) => {
    setExpandedAlerts(prev => {
      if (prev.includes(alertIndex)) {
        return prev.filter(index => index !== alertIndex);
      } else {
        return [...prev, alertIndex];
      }
    });
  };

  const handleOverrideClick = (alert: BeersCriteriaAlertType) => {
    setCurrentAlert(alert);
    setOverrideReason('');
    setOverrideDialogOpen(true);
  };

  const handleOverrideConfirm = async () => {
    if (currentAlert && overrideReason.trim()) {
      try {
        // If we have a prescription ID, save the override to the database
        if (currentAlert.prescriptionId) {
          const { overrideBeersCriteriaAlert } = await import('../../services/beersCriteriaService');
          await overrideBeersCriteriaAlert(
            currentAlert.prescriptionId,
            currentAlert.criterion.criteria_id,
            overrideReason
          );
          console.log(`BEERS Criteria override saved for prescription ${currentAlert.prescriptionId}`);
        }

        // Call the onOverride callback to update the UI
        onOverride(currentAlert, overrideReason);

        // Close the dialog and reset state
        setOverrideDialogOpen(false);
        setCurrentAlert(null);
        setOverrideReason('');
      } catch (error) {
        console.error('Error saving BEERS Criteria override:', error);
        // Still close the dialog and update the UI even if the save fails
        onOverride(currentAlert, overrideReason);
        setOverrideDialogOpen(false);
        setCurrentAlert(null);
        setOverrideReason('');
      }
    }
  };

  const getCategoryLabel = (category: string): string => {
    switch (category) {
      case 'avoid':
        return 'Avoid in Older Adults';
      case 'disease_interaction':
        return 'Disease Interaction';
      case 'drug_interaction':
        return 'Drug Interaction';
      case 'adjust_for_kidney_function':
        return 'Adjust for Kidney Function';
      case 'use_with_caution':
        return 'Use with Caution';
      default:
        return category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  const getEvidenceLabel = (evidence: string): string => {
    return evidence.charAt(0).toUpperCase() + evidence.slice(1);
  };

  return (
    <Paper
      elevation={3}
      sx={{
        p: 2,
        mb: 3,
        border: `1px solid ${theme.palette.warning.main}`,
        borderRadius: 2
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" color="warning.main" sx={{ display: 'flex', alignItems: 'center' }}>
          <WarningIcon sx={{ mr: 1 }} />
          BEERS Criteria Alerts
        </Typography>
        {onClose && (
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        )}
      </Box>

      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        The following medications may be potentially inappropriate for older adults according to the BEERS criteria:
      </Typography>

      {alerts.map((alert, index) => (
        <Alert
          key={index}
          severity="warning"
          sx={{
            mb: 2,
            borderRadius: 1,
            '& .MuiAlert-message': {
              width: '100%'
            }
          }}
          action={
            <IconButton
              aria-label="show more"
              onClick={() => handleExpandAlert(index)}
              sx={{
                transform: expandedAlerts.includes(index) ? 'rotate(180deg)' : 'rotate(0deg)',
                transition: theme.transitions.create('transform')
              }}
            >
              <ExpandMoreIcon />
            </IconButton>
          }
        >
          <AlertTitle>{alert.medication}</AlertTitle>
          <Typography variant="body2">{alert.message}</Typography>

          <Collapse in={expandedAlerts.includes(index)}>
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Category: {getCategoryLabel(alert.criterion.category)}
              </Typography>

              {alert.criterion.condition_name && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  <strong>Condition:</strong> {alert.criterion.condition_name}
                </Typography>
              )}

              {alert.criterion.interacting_medication && (
                <Typography variant="body2" sx={{ mt: 1 }}>
                  <strong>Interacting Medication:</strong> {alert.criterion.interacting_medication}
                </Typography>
              )}

              <Typography variant="body2" sx={{ mt: 1 }}>
                <strong>Recommendation:</strong> {alert.criterion.recommendation}
              </Typography>

              <Typography variant="body2" sx={{ mt: 1 }}>
                <strong>Rationale:</strong> {alert.criterion.rationale}
              </Typography>

              <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
                <Typography variant="caption" sx={{
                  backgroundColor: 'info.light',
                  color: 'info.contrastText',
                  px: 1,
                  py: 0.5,
                  borderRadius: 1
                }}>
                  Evidence: {getEvidenceLabel(alert.criterion.quality_of_evidence)}
                </Typography>

                <Typography variant="caption" sx={{
                  backgroundColor: 'info.light',
                  color: 'info.contrastText',
                  px: 1,
                  py: 0.5,
                  borderRadius: 1
                }}>
                  Recommendation: {getEvidenceLabel(alert.criterion.strength_of_recommendation)}
                </Typography>
              </Box>

              <Button
                variant="outlined"
                color="warning"
                size="small"
                onClick={() => handleOverrideClick(alert)}
                sx={{ mt: 2 }}
              >
                Override Alert
              </Button>
            </Box>
          </Collapse>
        </Alert>
      ))}

      <Dialog open={overrideDialogOpen} onClose={() => setOverrideDialogOpen(false)}>
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <InfoIcon sx={{ mr: 1, color: 'warning.main' }} />
            Override BEERS Criteria Alert
          </Box>
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            You are about to override a BEERS criteria alert for {currentAlert?.medication}.
            Please provide a clinical reason for this override:
          </DialogContentText>
          <TextField
            autoFocus
            margin="dense"
            id="override-reason"
            label="Clinical Reason for Override"
            type="text"
            fullWidth
            multiline
            rows={4}
            value={overrideReason}
            onChange={(e) => setOverrideReason(e.target.value)}
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOverrideDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleOverrideConfirm}
            color="warning"
            variant="contained"
            disabled={!overrideReason.trim()}
          >
            Confirm Override
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default BeersCriteriaAlert;
