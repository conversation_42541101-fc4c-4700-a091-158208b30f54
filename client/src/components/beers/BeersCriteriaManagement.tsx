import React, { useState, useEffect, useContext } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  SelectChangeEvent,
  Snackbar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Tooltip,
  Typography,
  Alert,
  useTheme
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Search as SearchIcon,
  MedicalServices as MedicalServicesIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { 
  getAllBeersCriteria, 
  BeersCriterion,
  createBeersCriterion,
  updateBeersCriterion,
  deleteBeersCriterion
} from '../../services/beersCriteriaService';
import AuthContext from '../../context/AuthContext';

const BeersCriteriaManagement: React.FC = () => {
  const theme = useTheme();
  const { user } = useContext(AuthContext);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [criteria, setCriteria] = useState<BeersCriterion[]>([]);
  const [filteredCriteria, setFilteredCriteria] = useState<BeersCriterion[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [openDialog, setOpenDialog] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [currentCriterion, setCurrentCriterion] = useState<BeersCriterion | null>(null);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');
  const [confirmDeleteOpen, setConfirmDeleteOpen] = useState(false);
  const [criterionToDelete, setCriterionToDelete] = useState<BeersCriterion | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    medication_name: '',
    category: 'avoid',
    condition_name: '',
    interacting_medication: '',
    recommendation: '',
    rationale: '',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  });

  // Check if user has permission to manage BEERS criteria
  const hasPermission = user?.role === 'admin' || user?.role === 'doctor';

  // Fetch all BEERS criteria on component mount
  useEffect(() => {
    fetchCriteria();
  }, []);

  const fetchCriteria = async () => {
    try {
      setLoading(true);
      const data = await getAllBeersCriteria();
      setCriteria(data);
      setFilteredCriteria(data);
    } catch (err) {
      console.error('Error fetching BEERS criteria:', err);
      setError('Failed to load BEERS criteria data');
    } finally {
      setLoading(false);
    }
  };

  // Handle search term change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    filterCriteria(event.target.value, categoryFilter);
  };

  // Handle category filter change
  const handleCategoryChange = (event: SelectChangeEvent) => {
    setCategoryFilter(event.target.value);
    filterCriteria(searchTerm, event.target.value);
  };

  // Filter criteria based on search term and category
  const filterCriteria = (term: string, category: string) => {
    let filtered = criteria;

    // Filter by category
    if (category !== 'all') {
      filtered = filtered.filter(criterion => criterion.category === category);
    }

    // Filter by search term
    if (term) {
      const lowerTerm = term.toLowerCase();
      filtered = filtered.filter(criterion => 
        criterion.medication_name.toLowerCase().includes(lowerTerm) ||
        (criterion.condition_name && criterion.condition_name.toLowerCase().includes(lowerTerm)) ||
        (criterion.interacting_medication && criterion.interacting_medication.toLowerCase().includes(lowerTerm)) ||
        criterion.recommendation.toLowerCase().includes(lowerTerm) ||
        criterion.rationale.toLowerCase().includes(lowerTerm)
      );
    }

    setFilteredCriteria(filtered);
    setPage(0); // Reset to first page when filtering
  };

  // Handle pagination
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Get category label
  const getCategoryLabel = (category: string): string => {
    switch (category) {
      case 'avoid':
        return 'Avoid in Older Adults';
      case 'disease_interaction':
        return 'Disease Interaction';
      case 'drug_interaction':
        return 'Drug Interaction';
      case 'adjust_for_kidney_function':
        return 'Adjust for Kidney Function';
      case 'use_with_caution':
        return 'Use with Caution';
      default:
        return category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  // Get evidence label
  const getEvidenceLabel = (evidence: string): string => {
    return evidence.charAt(0).toUpperCase() + evidence.slice(1);
  };

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle select changes
  const handleSelectChange = (e: SelectChangeEvent) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Open dialog to add new criterion
  const handleAddNew = () => {
    setFormData({
      medication_name: '',
      category: 'avoid',
      condition_name: '',
      interacting_medication: '',
      recommendation: '',
      rationale: '',
      quality_of_evidence: 'moderate',
      strength_of_recommendation: 'strong'
    });
    setEditMode(false);
    setOpenDialog(true);
  };

  // Open dialog to edit criterion
  const handleEdit = (criterion: BeersCriterion) => {
    setCurrentCriterion(criterion);
    setFormData({
      medication_name: criterion.medication_name,
      category: criterion.category,
      condition_name: criterion.condition_name || '',
      interacting_medication: criterion.interacting_medication || '',
      recommendation: criterion.recommendation,
      rationale: criterion.rationale,
      quality_of_evidence: criterion.quality_of_evidence,
      strength_of_recommendation: criterion.strength_of_recommendation
    });
    setEditMode(true);
    setOpenDialog(true);
  };

  // Open confirm delete dialog
  const handleDeleteClick = (criterion: BeersCriterion) => {
    setCriterionToDelete(criterion);
    setConfirmDeleteOpen(true);
  };

  // Handle form submission
  const handleSubmit = async () => {
    try {
      if (editMode && currentCriterion) {
        // Update existing criterion
        await updateBeersCriterion(currentCriterion.criteria_id, formData);
        setSnackbarMessage('BEERS criterion updated successfully');
      } else {
        // Create new criterion
        await createBeersCriterion(formData);
        setSnackbarMessage('BEERS criterion added successfully');
      }
      setSnackbarSeverity('success');
      setOpenDialog(false);
      fetchCriteria(); // Refresh the list
    } catch (err) {
      console.error('Error saving BEERS criterion:', err);
      setSnackbarMessage('Failed to save BEERS criterion');
      setSnackbarSeverity('error');
    }
    setSnackbarOpen(true);
  };

  // Handle delete confirmation
  const handleConfirmDelete = async () => {
    if (!criterionToDelete) return;
    
    try {
      await deleteBeersCriterion(criterionToDelete.criteria_id);
      setSnackbarMessage('BEERS criterion deleted successfully');
      setSnackbarSeverity('success');
      fetchCriteria(); // Refresh the list
    } catch (err) {
      console.error('Error deleting BEERS criterion:', err);
      setSnackbarMessage('Failed to delete BEERS criterion');
      setSnackbarSeverity('error');
    }
    setConfirmDeleteOpen(false);
    setSnackbarOpen(true);
  };

  // Close snackbar
  const handleSnackbarClose = () => {
    setSnackbarOpen(false);
  };

  // Render loading state
  if (loading && criteria.length === 0) {
    return (
      <Container>
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 8 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  // Render error state
  if (error) {
    return (
      <Container>
        <Paper sx={{ p: 3, mt: 4 }}>
          <Typography color="error" variant="h6">
            {error}
          </Typography>
        </Paper>
      </Container>
    );
  }

  // If user doesn't have permission
  if (!hasPermission) {
    return (
      <Container>
        <Paper sx={{ p: 3, mt: 4 }}>
          <Typography color="error" variant="h6">
            You don't have permission to manage BEERS criteria
          </Typography>
        </Paper>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Paper 
        elevation={3} 
        sx={{ 
          p: 3, 
          mb: 4, 
          borderRadius: 2,
          background: theme.palette.mode === 'dark' 
            ? 'linear-gradient(to right, #1a237e, #283593)' 
            : 'linear-gradient(to right, #3f51b5, #5c6bc0)',
          color: 'white'
        }}
      >
        <Grid container spacing={2} alignItems="center">
          <Grid item>
            <MedicalServicesIcon sx={{ fontSize: 40 }} />
          </Grid>
          <Grid item xs>
            <Typography variant="h4">BEERS Criteria Management</Typography>
            <Typography variant="subtitle1">
              Add, edit, or remove BEERS criteria for medication safety in older adults
            </Typography>
          </Grid>
          <Grid item>
            <Button
              variant="contained"
              startIcon={<RefreshIcon />}
              onClick={fetchCriteria}
              sx={{
                bgcolor: 'rgba(255,255,255,0.9)',
                color: '#3f51b5',
                '&:hover': { bgcolor: 'white' }
              }}
            >
              Refresh
            </Button>
          </Grid>
        </Grid>
      </Paper>

      <Paper elevation={2} sx={{ mb: 4, p: 2, borderRadius: 2 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Search Medications"
              value={searchTerm}
              onChange={handleSearchChange}
              placeholder="Enter medication name, condition, or keyword"
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>Category</InputLabel>
              <Select
                value={categoryFilter}
                label="Category"
                onChange={handleCategoryChange}
              >
                <MenuItem value="all">All Categories</MenuItem>
                <MenuItem value="avoid">Avoid in Older Adults</MenuItem>
                <MenuItem value="use_with_caution">Use with Caution</MenuItem>
                <MenuItem value="disease_interaction">Disease Interaction</MenuItem>
                <MenuItem value="drug_interaction">Drug Interaction</MenuItem>
                <MenuItem value="adjust_for_kidney_function">Adjust for Kidney Function</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <Button
              fullWidth
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              onClick={handleAddNew}
            >
              Add New
            </Button>
          </Grid>
        </Grid>
      </Paper>

      <TableContainer component={Paper} sx={{ borderRadius: 2 }}>
        <Table>
          <TableHead>
            <TableRow sx={{ backgroundColor: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.05)' : 'rgba(0,0,0,0.02)' }}>
              <TableCell><Typography fontWeight="bold">Medication</Typography></TableCell>
              <TableCell><Typography fontWeight="bold">Category</Typography></TableCell>
              <TableCell><Typography fontWeight="bold">Condition/Interaction</Typography></TableCell>
              <TableCell><Typography fontWeight="bold">Recommendation</Typography></TableCell>
              <TableCell><Typography fontWeight="bold">Evidence</Typography></TableCell>
              <TableCell><Typography fontWeight="bold">Actions</Typography></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredCriteria
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((criterion) => (
                <TableRow key={criterion.criteria_id} hover>
                  <TableCell>{criterion.medication_name}</TableCell>
                  <TableCell>
                    <Tooltip title={criterion.category}>
                      <span>{getCategoryLabel(criterion.category)}</span>
                    </Tooltip>
                  </TableCell>
                  <TableCell>
                    {criterion.condition_name || criterion.interacting_medication || '-'}
                  </TableCell>
                  <TableCell>
                    <Tooltip title={criterion.rationale}>
                      <span>{criterion.recommendation.length > 50 
                        ? `${criterion.recommendation.substring(0, 50)}...` 
                        : criterion.recommendation}
                      </span>
                    </Tooltip>
                  </TableCell>
                  <TableCell>
                    {getEvidenceLabel(criterion.quality_of_evidence)}
                  </TableCell>
                  <TableCell>
                    <Tooltip title="Edit">
                      <IconButton onClick={() => handleEdit(criterion)} size="small">
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete">
                      <IconButton onClick={() => handleDeleteClick(criterion)} size="small" color="error">
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
            {filteredCriteria.length === 0 && (
              <TableRow>
                <TableCell colSpan={6} align="center">
                  <Typography variant="body1" sx={{ py: 2 }}>
                    No criteria found matching your search
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25, 50]}
          component="div"
          count={filteredCriteria.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </TableContainer>

      {/* Add/Edit Dialog */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editMode ? 'Edit BEERS Criterion' : 'Add New BEERS Criterion'}
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Medication Name"
                name="medication_name"
                value={formData.medication_name}
                onChange={handleInputChange}
                required
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Category</InputLabel>
                <Select
                  name="category"
                  value={formData.category}
                  label="Category"
                  onChange={handleSelectChange}
                >
                  <MenuItem value="avoid">Avoid in Older Adults</MenuItem>
                  <MenuItem value="use_with_caution">Use with Caution</MenuItem>
                  <MenuItem value="disease_interaction">Disease Interaction</MenuItem>
                  <MenuItem value="drug_interaction">Drug Interaction</MenuItem>
                  <MenuItem value="adjust_for_kidney_function">Adjust for Kidney Function</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Condition Name"
                name="condition_name"
                value={formData.condition_name}
                onChange={handleInputChange}
                helperText="Required for disease interactions"
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Interacting Medication"
                name="interacting_medication"
                value={formData.interacting_medication}
                onChange={handleInputChange}
                helperText="Required for drug interactions"
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Recommendation"
                name="recommendation"
                value={formData.recommendation}
                onChange={handleInputChange}
                required
                multiline
                rows={2}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Rationale"
                name="rationale"
                value={formData.rationale}
                onChange={handleInputChange}
                required
                multiline
                rows={2}
                margin="normal"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Quality of Evidence</InputLabel>
                <Select
                  name="quality_of_evidence"
                  value={formData.quality_of_evidence}
                  label="Quality of Evidence"
                  onChange={handleSelectChange}
                >
                  <MenuItem value="high">High</MenuItem>
                  <MenuItem value="moderate">Moderate</MenuItem>
                  <MenuItem value="low">Low</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth margin="normal">
                <InputLabel>Strength of Recommendation</InputLabel>
                <Select
                  name="strength_of_recommendation"
                  value={formData.strength_of_recommendation}
                  label="Strength of Recommendation"
                  onChange={handleSelectChange}
                >
                  <MenuItem value="strong">Strong</MenuItem>
                  <MenuItem value="weak">Weak</MenuItem>
                  <MenuItem value="insufficient">Insufficient</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>Cancel</Button>
          <Button 
            onClick={handleSubmit} 
            variant="contained" 
            color="primary"
            disabled={!formData.medication_name || !formData.recommendation || !formData.rationale}
          >
            {editMode ? 'Update' : 'Add'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={confirmDeleteOpen} onClose={() => setConfirmDeleteOpen(false)}>
        <DialogTitle>Confirm Deletion</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete the BEERS criterion for "{criterionToDelete?.medication_name}"?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDeleteOpen(false)}>Cancel</Button>
          <Button onClick={handleConfirmDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleSnackbarClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={handleSnackbarClose} severity={snackbarSeverity} sx={{ width: '100%' }}>
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Container>
  );
};

export default BeersCriteriaManagement;
