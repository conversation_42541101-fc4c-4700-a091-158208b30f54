import React, { useState, useEffect, useContext } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import {
  Box,
  Button,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Container,
  Divider,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Paper,
  Select,
  SelectChangeEvent,
  Tab,
  Tabs,
  TextField,
  Typography,
  useTheme
} from '@mui/material';
import {
  Search as SearchIcon,
  Info as InfoIcon,
  Warning as WarningIcon,
  MedicalServices as MedicalServicesIcon,
  Edit as EditIcon
} from '@mui/icons-material';
import { getAllBeersCriteria, getBeersCriteriaByCategory, getBeersCriteriaByMedication, BeersCriterion } from '../../services/beersCriteriaService';
import AuthContext from '../../context/AuthContext';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`beers-tabpanel-${index}`}
      aria-labelledby={`beers-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const BeersCriteriaReference: React.FC = () => {
  const theme = useTheme();
  const { user } = useContext(AuthContext);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [criteria, setCriteria] = useState<BeersCriterion[]>([]);
  const [filteredCriteria, setFilteredCriteria] = useState<BeersCriterion[]>([]);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [tabValue, setTabValue] = useState<number>(0);

  // Check if user has permission to manage BEERS criteria
  const hasManagePermission = user?.role === 'admin' || user?.role === 'doctor';

  // Fetch all BEERS criteria on component mount
  useEffect(() => {
    const fetchCriteria = async () => {
      try {
        setLoading(true);
        const data = await getAllBeersCriteria();
        setCriteria(data);
        setFilteredCriteria(data);
      } catch (err) {
        console.error('Error fetching BEERS criteria:', err);
        setError('Failed to load BEERS criteria data');
      } finally {
        setLoading(false);
      }
    };

    fetchCriteria();
  }, []);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle search term change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    filterCriteria(event.target.value, categoryFilter);
  };

  // Handle category filter change
  const handleCategoryChange = (event: SelectChangeEvent) => {
    const category = event.target.value;
    setCategoryFilter(category);
    filterCriteria(searchTerm, category);
  };

  // Filter criteria based on search term and category
  const filterCriteria = (term: string, category: string) => {
    let filtered = criteria;

    // Filter by category
    if (category !== 'all') {
      filtered = filtered.filter(criterion => criterion.category === category);
    }

    // Filter by search term
    if (term) {
      const lowerTerm = term.toLowerCase();
      filtered = filtered.filter(criterion =>
        criterion.medication_name.toLowerCase().includes(lowerTerm) ||
        (criterion.condition_name && criterion.condition_name.toLowerCase().includes(lowerTerm)) ||
        (criterion.interacting_medication && criterion.interacting_medication.toLowerCase().includes(lowerTerm)) ||
        criterion.recommendation.toLowerCase().includes(lowerTerm) ||
        criterion.rationale.toLowerCase().includes(lowerTerm)
      );
    }

    setFilteredCriteria(filtered);
  };

  // Get category label
  const getCategoryLabel = (category: string): string => {
    switch (category) {
      case 'avoid':
        return 'Avoid in Older Adults';
      case 'disease_interaction':
        return 'Disease Interaction';
      case 'drug_interaction':
        return 'Drug Interaction';
      case 'adjust_for_kidney_function':
        return 'Adjust for Kidney Function';
      case 'use_with_caution':
        return 'Use with Caution';
      default:
        return category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  // Get evidence label
  const getEvidenceLabel = (evidence: string): string => {
    return evidence.charAt(0).toUpperCase() + evidence.slice(1);
  };

  // Get category color
  const getCategoryColor = (category: string): string => {
    switch (category) {
      case 'avoid':
        return theme.palette.error.main;
      case 'disease_interaction':
        return theme.palette.warning.main;
      case 'drug_interaction':
        return theme.palette.warning.dark;
      case 'adjust_for_kidney_function':
        return theme.palette.info.main;
      case 'use_with_caution':
        return theme.palette.info.dark;
      default:
        return theme.palette.primary.main;
    }
  };

  // Render loading state
  if (loading) {
    return (
      <Container>
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  // Render error state
  if (error) {
    return (
      <Container>
        <Paper sx={{ p: 3, mt: 4 }}>
          <Typography color="error" variant="h6">
            {error}
          </Typography>
        </Paper>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Paper sx={{ p: 3, mb: 4 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <MedicalServicesIcon fontSize="large" sx={{ mr: 2, color: 'primary.main' }} />
            <Typography variant="h4">BEERS Criteria Reference</Typography>
          </Box>

          {hasManagePermission && (
            <Button
              component={RouterLink}
              to="/beers-criteria/manage"
              variant="contained"
              color="primary"
              startIcon={<EditIcon />}
            >
              Manage BEERS Criteria
            </Button>
          )}
        </Box>

        <Typography variant="body1" paragraph>
          The BEERS Criteria is a list of medications that are potentially inappropriate for older adults (65 years and older).
          It was developed by the American Geriatrics Society to improve medication safety in older patients.
        </Typography>

        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="BEERS criteria tabs">
            <Tab label="Search & Filter" />
            <Tab label="About BEERS Criteria" />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <Box sx={{ mb: 3 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Search Medications"
                  value={searchTerm}
                  onChange={handleSearchChange}
                  placeholder="Enter medication name, condition, or keyword"
                  InputProps={{
                    startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                  }}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel id="category-filter-label">Category</InputLabel>
                  <Select
                    labelId="category-filter-label"
                    value={categoryFilter}
                    label="Category"
                    onChange={handleCategoryChange}
                  >
                    <MenuItem value="all">All Categories</MenuItem>
                    <MenuItem value="avoid">Avoid in Older Adults</MenuItem>
                    <MenuItem value="disease_interaction">Disease Interaction</MenuItem>
                    <MenuItem value="drug_interaction">Drug Interaction</MenuItem>
                    <MenuItem value="adjust_for_kidney_function">Adjust for Kidney Function</MenuItem>
                    <MenuItem value="use_with_caution">Use with Caution</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </Box>

          <Typography variant="subtitle1" sx={{ mb: 2 }}>
            {filteredCriteria.length} criteria found
          </Typography>

          <Grid container spacing={3}>
            {filteredCriteria.map((criterion) => (
              <Grid item xs={12} key={criterion.criteria_id}>
                <Card
                  sx={{
                    borderLeft: `6px solid ${getCategoryColor(criterion.category)}`,
                    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
                  }}
                >
                  <CardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Typography variant="h6" component="div">
                        {criterion.medication_name}
                      </Typography>
                      <Chip
                        label={getCategoryLabel(criterion.category)}
                        size="small"
                        sx={{
                          backgroundColor: getCategoryColor(criterion.category),
                          color: '#fff'
                        }}
                      />
                    </Box>

                    {criterion.condition_name && (
                      <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                        Condition: {criterion.condition_name}
                      </Typography>
                    )}

                    {criterion.interacting_medication && (
                      <Typography variant="subtitle2" color="text.secondary" sx={{ mb: 1 }}>
                        Interacting Medication: {criterion.interacting_medication}
                      </Typography>
                    )}

                    <Divider sx={{ my: 2 }} />

                    <Typography variant="body2" sx={{ mb: 1 }}>
                      <strong>Recommendation:</strong> {criterion.recommendation}
                    </Typography>

                    <Typography variant="body2" sx={{ mb: 2 }}>
                      <strong>Rationale:</strong> {criterion.rationale}
                    </Typography>

                    <Box sx={{ display: 'flex', gap: 2 }}>
                      <Chip
                        size="small"
                        label={`Evidence: ${getEvidenceLabel(criterion.quality_of_evidence)}`}
                        sx={{ backgroundColor: 'rgba(0, 0, 0, 0.08)' }}
                      />
                      <Chip
                        size="small"
                        label={`Recommendation: ${getEvidenceLabel(criterion.strength_of_recommendation)}`}
                        sx={{ backgroundColor: 'rgba(0, 0, 0, 0.08)' }}
                      />
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}

            {filteredCriteria.length === 0 && (
              <Grid item xs={12}>
                <Paper sx={{ p: 3, textAlign: 'center' }}>
                  <Typography variant="body1">
                    No criteria found matching your search.
                  </Typography>
                </Paper>
              </Grid>
            )}
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Paper elevation={0} sx={{ p: 3, backgroundColor: 'rgba(0, 0, 0, 0.02)' }}>
            <Typography variant="h5" gutterBottom>
              About the BEERS Criteria
            </Typography>

            <Typography variant="body1" paragraph>
              The BEERS Criteria for Potentially Inappropriate Medication Use in Older Adults is a guideline for healthcare professionals to help improve the safety of prescribing medications for older adults.
            </Typography>

            <Typography variant="body1" paragraph>
              First published in 1991 by geriatrician Mark H. Beers, MD, the criteria are now maintained and updated by the American Geriatrics Society. The most recent update was published in 2023.
            </Typography>

            <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
              The BEERS Criteria includes:
            </Typography>

            <Box component="ul" sx={{ pl: 4 }}>
              <Box component="li">
                <Typography variant="body1" paragraph>
                  <strong>Medications to avoid in older adults regardless of diseases or conditions</strong> - These medications may cause adverse effects in older adults due to age-related physiological changes.
                </Typography>
              </Box>

              <Box component="li">
                <Typography variant="body1" paragraph>
                  <strong>Medications to avoid in older adults with specific diseases or conditions</strong> - These medications may exacerbate certain diseases or conditions.
                </Typography>
              </Box>

              <Box component="li">
                <Typography variant="body1" paragraph>
                  <strong>Medications to use with caution in older adults</strong> - These medications may be associated with increased risk of adverse effects in older adults but may be appropriate in some circumstances.
                </Typography>
              </Box>

              <Box component="li">
                <Typography variant="body1" paragraph>
                  <strong>Drug-drug interactions to avoid in older adults</strong> - These combinations of medications may result in harmful interactions.
                </Typography>
              </Box>

              <Box component="li">
                <Typography variant="body1" paragraph>
                  <strong>Medications to avoid or adjust dosage based on kidney function</strong> - These medications may require dosage adjustment or avoidance in older adults with reduced kidney function.
                </Typography>
              </Box>
            </Box>

            <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
              Benefits of Using the BEERS Criteria:
            </Typography>

            <Box component="ul" sx={{ pl: 4 }}>
              <Box component="li">
                <Typography variant="body1">Reduces adverse drug events and drug-related problems</Typography>
              </Box>
              <Box component="li">
                <Typography variant="body1">Improves medication safety in older adults</Typography>
              </Box>
              <Box component="li">
                <Typography variant="body1">Provides evidence-based recommendations for medication use</Typography>
              </Box>
              <Box component="li">
                <Typography variant="body1">Serves as a clinical decision support tool</Typography>
              </Box>
              <Box component="li">
                <Typography variant="body1">Helps optimize medication therapy in older adults</Typography>
              </Box>
            </Box>

            <Box sx={{ mt: 4, p: 2, backgroundColor: 'rgba(255, 152, 0, 0.1)', borderRadius: 1 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <InfoIcon sx={{ mr: 1, color: 'warning.main' }} />
                <Typography variant="subtitle1" color="warning.main">
                  Important Note
                </Typography>
              </Box>
              <Typography variant="body2">
                The BEERS Criteria is a guideline and not a substitute for clinical judgment. Medications listed in the criteria may be appropriate in some circumstances based on individual patient needs and clinical situations. Always use clinical judgment when making prescribing decisions.
              </Typography>
            </Box>
          </Paper>
        </TabPanel>
      </Paper>
    </Container>
  );
};

export default BeersCriteriaReference;
