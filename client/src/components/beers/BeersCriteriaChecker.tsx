import React, { useEffect, useState } from 'react';
import { Box, CircularProgress, Typography } from '@mui/material';
import { checkMedications, BeersCriteriaAlert as BeersCriteriaAlertType } from '../../services/beersCriteriaService';
import BeersCriteriaAlert from './BeersCriteriaAlert';

interface Prescription {
  id?: number;
  medication: string;
  dosage: string;
  frequency: string;
  duration: string;
  notes?: string;
}

interface BeersCriteriaCheckerProps {
  medications: string[];
  patientId: number;
  patientAge?: number;
  visitId?: number;
  prescriptions?: Prescription[];
  onOverride?: (alert: BeersCriteriaAlertType, reason: string) => void;
}

const BeersCriteriaChecker: React.FC<BeersCriteriaCheckerProps> = ({
  medications,
  patientId,
  patientAge,
  visitId,
  prescriptions = [],
  onOverride
}) => {
  const [alerts, setAlerts] = useState<BeersCriteriaAlertType[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Create a map of medication names to prescription IDs
  const medicationToPrescriptionMap = React.useMemo(() => {
    const map: Record<string, number> = {};
    prescriptions.forEach(prescription => {
      if (prescription.id && prescription.medication) {
        map[prescription.medication.trim().toLowerCase()] = prescription.id;
      }
    });
    return map;
  }, [prescriptions]);

  useEffect(() => {
    // Skip check if no medications or patient is under 65
    if (!medications.length || (patientAge && patientAge < 65)) {
      setAlerts([]);
      return;
    }

    const checkForAlerts = async () => {
      setLoading(true);
      setError(null);

      try {
        // Filter out empty medication names
        const validMedications = medications
          .filter(med => med && med.trim() !== '')
          .map(med => med.trim());

        if (validMedications.length === 0) {
          setAlerts([]);
          return;
        }

        const result = await checkMedications(validMedications, patientId, visitId);

        // Add prescription IDs to alerts if available
        const alertsWithPrescriptionIds = result.map(alert => {
          const prescriptionId = medicationToPrescriptionMap[alert.medication.toLowerCase()];
          return {
            ...alert,
            prescriptionId
          };
        });

        setAlerts(alertsWithPrescriptionIds);
      } catch (err: any) {
        console.error('Error checking medications against BEERS criteria:', err);

        // Extract detailed error message if available
        let errorMessage = 'Failed to check medications against BEERS criteria';

        if (err.response && err.response.data) {
          if (err.response.data.error) {
            errorMessage = `Error: ${err.response.data.error}`;
          }
          if (err.response.data.message) {
            errorMessage += ` - ${err.response.data.message}`;
          }
        } else if (err.message) {
          errorMessage = `Error: ${err.message}`;
        }

        setError(errorMessage);
        console.log('Medications being checked:', medications);
        console.log('Patient ID:', patientId);
        console.log('Visit ID:', visitId);
      } finally {
        setLoading(false);
      }
    };

    checkForAlerts();
  }, [medications, patientId, patientAge, visitId, medicationToPrescriptionMap]);

  const handleOverride = (alert: BeersCriteriaAlertType, reason: string) => {
    // Remove the overridden alert from the list
    setAlerts(prev => prev.filter(a => a !== alert));

    // Call the parent component's onOverride handler if provided
    if (onOverride) {
      onOverride(alert, reason);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
        <CircularProgress size={20} sx={{ mr: 1 }} />
        <Typography variant="body2">Checking medications against BEERS criteria...</Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Typography color="error" variant="body2" sx={{ mt: 2 }}>
        {error}
      </Typography>
    );
  }

  if (alerts.length === 0) {
    return null;
  }

  return (
    <Box sx={{ mt: 3 }}>
      <BeersCriteriaAlert alerts={alerts} onOverride={handleOverride} />
    </Box>
  );
};

export default BeersCriteriaChecker;
