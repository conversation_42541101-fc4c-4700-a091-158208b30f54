import React from 'react';
import { Typography, Box, Grid, Divider } from '@mui/material';
import { getGad7TreatmentRecommendation } from '../../utils/mentalHealthUtils';

interface SimpleAnxietyDisplayProps {
  value?: string;
  anxietyScore?: string;
  gad7Notes?: string;
}

// GAD-7 interpretation
const getGad7Interpretation = (totalScore: number): string => {
  if (totalScore >= 0 && totalScore <= 4) {
    return "Minimal anxiety";
  } else if (totalScore >= 5 && totalScore <= 9) {
    return "Mild anxiety";
  } else if (totalScore >= 10 && totalScore <= 14) {
    return "Moderate anxiety";
  } else if (totalScore >= 15) {
    return "Severe anxiety";
  } else {
    return "Invalid score";
  }
};

const SimpleAnxietyDisplay: React.FC<SimpleAnxietyDisplayProps> = ({
  value,
  anxietyScore,
  gad7Notes
}) => {
  // First try to use the individual fields if available
  if (anxietyScore !== undefined) {
    const totalScore = parseInt(anxietyScore);
    
    return (
      <Box>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Typography variant="body2" fontWeight="medium">
              <strong>1. GAD-7 Score:</strong> {totalScore}/21
            </Typography>
          </Grid>
          
          <Grid item xs={12}>
            <Typography variant="body2" fontWeight="medium">
              <strong>2. Interpretation:</strong> {getGad7Interpretation(totalScore)}
            </Typography>
          </Grid>
          
          <Grid item xs={12}>
            <Typography variant="body2" fontWeight="medium">
              <strong>3. GAD-7 Clinical Notes:</strong>
            </Typography>
            <Typography variant="body2" sx={{ mt: 0.5, whiteSpace: 'pre-wrap' }}>
              {gad7Notes || "No clinical notes available"}
            </Typography>
          </Grid>
          
          <Grid item xs={12}>
            <Typography variant="body2" fontWeight="medium">
              <strong>4. Treatment Recommendation:</strong>
            </Typography>
            <Typography variant="body2" sx={{ mt: 0.5, whiteSpace: 'pre-wrap' }}>
              {getGad7TreatmentRecommendation(totalScore)}
            </Typography>
          </Grid>
        </Grid>
      </Box>
    );
  }

  // Fall back to parsing the JSON value if individual fields are not available
  try {
    // Try to parse the JSON value if it exists
    if (!value) {
      return (
        <Box>
          <Typography variant="body2">No anxiety assessment data available</Typography>
        </Box>
      );
    }

    const assessmentData = JSON.parse(value as string);

    // Check if data is in the expected GAD-7 format
    if (assessmentData.gad7) {
      const totalScore =
        (assessmentData.gad7.feelingNervous || 0) +
        (assessmentData.gad7.stopWorrying || 0) +
        (assessmentData.gad7.worryingMuch || 0) +
        (assessmentData.gad7.troubleRelaxing || 0) +
        (assessmentData.gad7.restless || 0) +
        (assessmentData.gad7.annoyed || 0) +
        (assessmentData.gad7.feelingAfraid || 0);

      return (
        <Box>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="body2" fontWeight="medium">
                <strong>1. GAD-7 Score:</strong> {totalScore}/21
              </Typography>
            </Grid>
            
            <Grid item xs={12}>
              <Typography variant="body2" fontWeight="medium">
                <strong>2. Interpretation:</strong> {getGad7Interpretation(totalScore)}
              </Typography>
            </Grid>
            
            <Grid item xs={12}>
              <Typography variant="body2" fontWeight="medium">
                <strong>3. GAD-7 Clinical Notes:</strong>
              </Typography>
              <Typography variant="body2" sx={{ mt: 0.5, whiteSpace: 'pre-wrap' }}>
                {assessmentData.gad7.notes || "No clinical notes available"}
              </Typography>
            </Grid>
            
            <Grid item xs={12}>
              <Typography variant="body2" fontWeight="medium">
                <strong>4. Treatment Recommendation:</strong>
              </Typography>
              <Typography variant="body2" sx={{ mt: 0.5, whiteSpace: 'pre-wrap' }}>
                {getGad7TreatmentRecommendation(totalScore)}
              </Typography>
            </Grid>
          </Grid>
        </Box>
      );
    }
  } catch (e) {
    // If parsing fails, just display the raw value
    return (
      <Box>
        <Typography variant="body2">{value || "No anxiety assessment data available"}</Typography>
      </Box>
    );
  }

  // If we get here, we couldn't parse the data
  return (
    <Box>
      <Typography variant="body2">No anxiety assessment data available</Typography>
    </Box>
  );
};

export default SimpleAnxietyDisplay;
