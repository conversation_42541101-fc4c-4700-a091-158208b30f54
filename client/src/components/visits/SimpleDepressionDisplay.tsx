import React from 'react';
import { Typography, Box, Grid, Divider } from '@mui/material';
import { getPhq9TreatmentRecommendation } from '../../utils/mentalHealthUtils';

interface SimpleDepressionDisplayProps {
  value?: string;
  depressionScore?: string;
  phq9Notes?: string;
}

// PHQ-9 interpretation
const getPhq9Interpretation = (totalScore: number): string => {
  if (totalScore >= 0 && totalScore <= 4) {
    return "Minimal or no depression";
  } else if (totalScore >= 5 && totalScore <= 9) {
    return "Mild depression";
  } else if (totalScore >= 10 && totalScore <= 14) {
    return "Moderate depression";
  } else if (totalScore >= 15 && totalScore <= 19) {
    return "Moderately severe depression";
  } else if (totalScore >= 20) {
    return "Severe depression";
  } else {
    return "Invalid score";
  }
};

const SimpleDepressionDisplay: React.FC<SimpleDepressionDisplayProps> = ({
  value,
  depressionScore,
  phq9Notes
}) => {
  // Debug logs
  console.log('SimpleDepressionDisplay - Props:', { value, depressionScore, phq9Notes });
  // First try to use the individual fields if available
  if (depressionScore !== undefined) {
    const totalScore = parseInt(depressionScore);

    return (
      <Box>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Typography variant="body2" fontWeight="medium">
              <strong>1. PHQ-9 Score:</strong> {totalScore}/27
            </Typography>
          </Grid>

          <Grid item xs={12}>
            <Typography variant="body2" fontWeight="medium">
              <strong>2. Interpretation:</strong> {getPhq9Interpretation(totalScore)}
            </Typography>
          </Grid>

          <Grid item xs={12}>
            <Typography variant="body2" fontWeight="medium">
              <strong>3. PHQ-9 Clinical Notes:</strong>
            </Typography>
            <Typography variant="body2" sx={{ mt: 0.5, whiteSpace: 'pre-wrap' }}>
              {phq9Notes || "No clinical notes available"}
            </Typography>
          </Grid>

          <Grid item xs={12}>
            <Typography variant="body2" fontWeight="medium">
              <strong>4. Treatment Recommendation:</strong>
            </Typography>
            <Typography variant="body2" sx={{ mt: 0.5, whiteSpace: 'pre-wrap' }}>
              {getPhq9TreatmentRecommendation(totalScore)}
            </Typography>
          </Grid>
        </Grid>
      </Box>
    );
  }

  // Fall back to parsing the JSON value if individual fields are not available
  try {
    // Try to parse the JSON value if it exists
    if (!value) {
      return (
        <Box>
          <Typography variant="body2">No depression assessment data available</Typography>
        </Box>
      );
    }

    const assessmentData = JSON.parse(value as string);

    // Check if data is in the expected PHQ-9 format
    if (assessmentData.phq9) {
      const totalScore =
        (assessmentData.phq9.interestPleasure || 0) +
        (assessmentData.phq9.feelingDown || 0) +
        (assessmentData.phq9.sleepIssues || 0) +
        (assessmentData.phq9.tired || 0) +
        (assessmentData.phq9.appetite || 0) +
        (assessmentData.phq9.feelingBad || 0) +
        (assessmentData.phq9.concentration || 0) +
        (assessmentData.phq9.movingSpeaking || 0) +
        (assessmentData.phq9.thoughtsHurting || 0);

      return (
        <Box>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="body2" fontWeight="medium">
                <strong>1. PHQ-9 Score:</strong> {totalScore}/27
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <Typography variant="body2" fontWeight="medium">
                <strong>2. Interpretation:</strong> {getPhq9Interpretation(totalScore)}
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <Typography variant="body2" fontWeight="medium">
                <strong>3. PHQ-9 Clinical Notes:</strong>
              </Typography>
              <Typography variant="body2" sx={{ mt: 0.5, whiteSpace: 'pre-wrap' }}>
                {assessmentData.phq9.notes || "No clinical notes available"}
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <Typography variant="body2" fontWeight="medium">
                <strong>4. Treatment Recommendation:</strong>
              </Typography>
              <Typography variant="body2" sx={{ mt: 0.5, whiteSpace: 'pre-wrap' }}>
                {getPhq9TreatmentRecommendation(totalScore)}
              </Typography>
            </Grid>
          </Grid>
        </Box>
      );
    }
  } catch (e) {
    // If parsing fails, just display the raw value
    return (
      <Box>
        <Typography variant="body2">{value || "No depression assessment data available"}</Typography>
      </Box>
    );
  }

  // If we get here, we couldn't parse the data
  return (
    <Box>
      <Typography variant="body2">No depression assessment data available</Typography>
    </Box>
  );
};

export default SimpleDepressionDisplay;
