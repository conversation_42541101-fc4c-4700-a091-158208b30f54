import React from 'react';
import { Typography, Box, Grid, Divider } from '@mui/material';
import { getMiniCogRecommendation } from '../../utils/mentalHealthUtils';

interface SimpleCognitiveDisplayProps {
  value?: string;
  miniCogWordRecallScore?: string;
  miniCogClockDrawingScore?: string;
  miniCogNotes?: string;
}

// Mini-Cog interpretation
const getMiniCogInterpretation = (totalScore: number): string => {
  if (totalScore <= 2) {
    return "Positive screen for cognitive impairment";
  } else {
    return "Negative screen for cognitive impairment";
  }
};

const SimpleCognitiveDisplay: React.FC<SimpleCognitiveDisplayProps> = ({
  value,
  miniCogWordRecallScore,
  miniCogClockDrawingScore,
  miniCogNotes
}) => {
  // First try to use the individual fields if available
  if (miniCogWordRecallScore !== undefined || miniCogClockDrawingScore !== undefined) {
    const wordRecallScore = miniCogWordRecallScore ? parseInt(miniCogWordRecallScore) : 0;
    const clockDrawingScore = miniCogClockDrawingScore ? parseInt(miniCogClockDrawingScore) : 0;
    const totalScore = wordRecallScore + clockDrawingScore;

    return (
      <Box>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Typography variant="body2" fontWeight="medium">
              <strong>1. Mini-Cog Score:</strong> {totalScore}/5
            </Typography>
          </Grid>
          
          <Grid item xs={12}>
            <Typography variant="body2" fontWeight="medium">
              <strong>2. Interpretation:</strong> {getMiniCogInterpretation(totalScore)}
            </Typography>
          </Grid>
          
          <Grid item xs={12}>
            <Typography variant="body2" fontWeight="medium">
              <strong>3. Mini-Cog Clinical Notes:</strong>
            </Typography>
            <Typography variant="body2" sx={{ mt: 0.5, whiteSpace: 'pre-wrap' }}>
              {miniCogNotes || "No clinical notes available"}
            </Typography>
          </Grid>
        </Grid>
      </Box>
    );
  }

  // Fall back to parsing the JSON value if individual fields are not available
  try {
    // Try to parse the JSON value
    if (!value) {
      return (
        <Box>
          <Typography variant="body2">No cognitive assessment data available</Typography>
        </Box>
      );
    }

    const assessmentData = JSON.parse(value);

    // Check if data is in the expected Mini-Cog format
    if (assessmentData.miniCog) {
      const wordRecallScore = assessmentData.miniCog.wordRecallScore ||
                            (assessmentData.miniCog.words?.recalled?.filter(Boolean).length || 0);
      const clockDrawingScore = assessmentData.miniCog.clockDrawing || 0;
      const totalScore = wordRecallScore + clockDrawingScore;

      return (
        <Box>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="body2" fontWeight="medium">
                <strong>1. Mini-Cog Score:</strong> {totalScore}/5
              </Typography>
            </Grid>
            
            <Grid item xs={12}>
              <Typography variant="body2" fontWeight="medium">
                <strong>2. Interpretation:</strong> {getMiniCogInterpretation(totalScore)}
              </Typography>
            </Grid>
            
            <Grid item xs={12}>
              <Typography variant="body2" fontWeight="medium">
                <strong>3. Mini-Cog Clinical Notes:</strong>
              </Typography>
              <Typography variant="body2" sx={{ mt: 0.5, whiteSpace: 'pre-wrap' }}>
                {assessmentData.miniCog.notes || "No clinical notes available"}
              </Typography>
            </Grid>
          </Grid>
        </Box>
      );
    }
  } catch (e) {
    // If parsing fails, display the value as is
  }

  // Fallback for non-JSON values or missing data
  return (
    <Box>
      <Typography variant="body2">No cognitive assessment data available</Typography>
    </Box>
  );
};

export default SimpleCognitiveDisplay;
