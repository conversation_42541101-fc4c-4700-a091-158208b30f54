import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Divider,
  // Grid - Unused import
  Chip,
  // CircularProgress - Unused import
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import MedicationIcon from '@mui/icons-material/Medication';
import WarningIcon from '@mui/icons-material/Warning';
import { getPrescriptionsByVisitId } from '../../services/prescriptionService';
import LoadingSpinner from '../common/LoadingSpinner';

interface Prescription {
  prescription_id: number;
  medication: string;
  dosage: string;
  frequency: string;
  duration: string;
  notes?: string;
  beers_criteria_id?: number;
  beers_criteria_name?: string;
  beers_criteria_category?: string;
  beers_criteria_recommendation?: string;
  beers_criteria_rationale?: string;
  beers_override_reason?: string;
  beers_overridden_at?: string;
  beers_overridden_by?: number;
  overridden_by_username?: string;
  created_at: string;
}

interface VisitPrescriptionsProps {
  visitId: number;
}

const VisitPrescriptions: React.FC<VisitPrescriptionsProps> = ({ visitId }) => {
  const [prescriptions, setPrescriptions] = useState<Prescription[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchPrescriptions = async () => {
      if (!visitId) return;

      setLoading(true);
      try {
        const data = await getPrescriptionsByVisitId(visitId);
        console.log('Fetched prescriptions for visit:', data);
        setPrescriptions(data);
      } catch (err) {
        console.error('Error fetching prescriptions:', err);
        setError('Failed to load prescriptions');
      } finally {
        setLoading(false);
      }
    };

    fetchPrescriptions();
  }, [visitId]);

  if (loading) {
    return (
      <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
        <Box display="flex" alignItems="center" mb={2}>
          <MedicationIcon fontSize="large" sx={{ mr: 2 }} />
          <Typography variant="h5">Prescriptions</Typography>
        </Box>
        <Divider sx={{ mb: 3 }} />
        <Box display="flex" justifyContent="center" p={3}>
          <LoadingSpinner size="medium" message="Loading prescriptions..." />
        </Box>
      </Paper>
    );
  }

  if (error) {
    return (
      <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
        <Box display="flex" alignItems="center" mb={2}>
          <MedicationIcon fontSize="large" sx={{ mr: 2 }} />
          <Typography variant="h5">Prescriptions</Typography>
        </Box>
        <Divider sx={{ mb: 3 }} />
        <Alert severity="error">{error}</Alert>
      </Paper>
    );
  }

  if (prescriptions.length === 0) {
    return (
      <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
        <Box display="flex" alignItems="center" mb={2}>
          <MedicationIcon fontSize="large" sx={{ mr: 2 }} />
          <Typography variant="h5">Prescriptions</Typography>
        </Box>
        <Divider sx={{ mb: 3 }} />
        <Alert severity="info">No prescriptions found for this visit.</Alert>
      </Paper>
    );
  }

  return (
    <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
      <Box display="flex" alignItems="center" mb={2}>
        <MedicationIcon fontSize="large" sx={{ mr: 2 }} />
        <Typography variant="h5">Prescriptions</Typography>
      </Box>
      <Divider sx={{ mb: 3 }} />

      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Medication</TableCell>
              <TableCell>Dosage</TableCell>
              <TableCell>Frequency</TableCell>
              <TableCell>Duration</TableCell>
              <TableCell>Notes</TableCell>
              <TableCell>BEERS Status</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {prescriptions.map((prescription) => (
              <TableRow key={prescription.prescription_id}>
                <TableCell>{prescription.medication}</TableCell>
                <TableCell>{prescription.dosage}</TableCell>
                <TableCell>{prescription.frequency}</TableCell>
                <TableCell>{prescription.duration}</TableCell>
                <TableCell>{prescription.notes}</TableCell>
                <TableCell>
                  {prescription.beers_criteria_id ? (
                    <Box>
                      {prescription.beers_override_reason ? (
                        <Chip
                          icon={<WarningIcon />}
                          label="BEERS Override"
                          color="warning"
                          variant="outlined"
                          sx={{ mb: 1 }}
                        />
                      ) : (
                        <Chip
                          icon={<WarningIcon />}
                          label="BEERS Alert"
                          color="error"
                          variant="outlined"
                          sx={{ mb: 1 }}
                        />
                      )}
                      {prescription.beers_override_reason && (
                        <Typography variant="body2" sx={{ fontWeight: 'bold' }}>
                          Override reason: {prescription.beers_override_reason}
                        </Typography>
                      )}
                      {prescription.overridden_by_username && (
                        <Typography variant="body2">
                          Overridden by: {prescription.overridden_by_username}
                        </Typography>
                      )}
                    </Box>
                  ) : (
                    <Chip label="No BEERS issues" color="success" variant="outlined" />
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Paper>
  );
};

export default VisitPrescriptions;
