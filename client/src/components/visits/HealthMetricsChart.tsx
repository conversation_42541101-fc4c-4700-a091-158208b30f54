import React, { useState } from 'react';
import {
  Box,
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Typography,
  Paper,
  Grid,
  useTheme,
  alpha,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton,
  Tooltip as MuiTooltip
} from '@mui/material';
import { Visit } from '../../types';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import FavoriteIcon from '@mui/icons-material/Favorite';
import MonitorHeartIcon from '@mui/icons-material/MonitorHeart';
import ThermostatIcon from '@mui/icons-material/Thermostat';
import BloodtypeIcon from '@mui/icons-material/Bloodtype';
import ScaleIcon from '@mui/icons-material/Scale';
import WaterDropIcon from '@mui/icons-material/WaterDrop';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import MedicationIcon from '@mui/icons-material/Medication';
import BiotechIcon from '@mui/icons-material/Biotech';
import ScienceIcon from '@mui/icons-material/Science';
import HealthAndSafetyIcon from '@mui/icons-material/HealthAndSafety';
import BoltIcon from '@mui/icons-material/Bolt';
import HealingIcon from '@mui/icons-material/Healing';
import WbSunnyIcon from '@mui/icons-material/WbSunny';
import VaccinesIcon from '@mui/icons-material/Vaccines';

// Import Chart.js components
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Line } from 'react-chartjs-2';

// Register ChartJS components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface HealthMetricsChartProps {
  visits: Visit[];
}

interface Range {
  min: number;
  max: number;
  text: string;
}

interface BloodPressureRange {
  systolic: Range;
  diastolic: Range;
}

interface CholesterolRange {
  total: Range;
  hdl: Range;
  ldl: Range;
}

interface WeightBMIRange {
  bmi: Range;
}

interface KidneyFunctionRange {
  creatinine: Range;
  egfr: Range;
}

interface LiverFunctionRange {
  bilirubin_t: Range;
  bilirubin_d: Range;
  albumin: Range;
  total_protein: Range;
  alt: Range;
  ast: Range;
  alp: Range;
  ggt: Range;
}

interface CBCRange {
  hemoglobin: Range;
  hematocrit: Range;
  rbc: Range;
  wbc: Range;
  platelets: Range;
}

interface RBCIndicesRange {
  mcv: Range;
  mch: Range;
  mchc: Range;
  rdw: Range;
}

interface WBCDifferentialRange {
  neutrophils: Range;
  lymphocytes: Range;
  monocytes: Range;
  eosinophils: Range;
  basophils: Range;
}

interface ThyroidFunctionRange {
  tsh: Range;
  free_t3: Range;
  free_t4: Range;
}

interface InflammatoryMarkersRange {
  crp: Range;
  esr: Range;
}

interface ElectrolytesRange {
  sodium: Range;
  potassium: Range;
  calcium: Range;
  magnesium: Range;
}

interface VitaminIronRange {
  vitamin_d: Range;
  vitamin_b12: Range;
  folate: Range;
  ferritin: Range;
  iron: Range;
}

interface NormalRanges {
  'blood_pressure': BloodPressureRange;
  'heart_rate': {
    lying: Range;
    standing: Range;
  };
  'temperature': Range;
  'blood_glucose': Range;
  'cholesterol': CholesterolRange;
  'weight_bmi': WeightBMIRange;
  'kidney_function': KidneyFunctionRange;
  'liver_function': LiverFunctionRange;
  'cbc': CBCRange;
  'rbc_indices': RBCIndicesRange;
  'wbc_differential': WBCDifferentialRange;
  'thyroid_function': ThyroidFunctionRange;
  'inflammatory_markers': InflammatoryMarkersRange;
  'electrolytes': ElectrolytesRange;
  'vitamin_iron': VitaminIronRange;
}

interface ChartDataset {
  label: string;
  data: (number | null)[];
  borderColor: string;
  backgroundColor: string;
  tension?: number;
  spanGaps?: boolean;
  borderDash?: number[];
  fill?: boolean | number;
  pointRadius?: number;
  visitTypes?: string[]; // Added for storing visit types
}

const getBMIClassification = (bmi: number): string => {
  if (bmi < 18.5) return 'Underweight';
  if (bmi < 25) return 'Normal weight';
  if (bmi < 30) return 'Overweight';
  if (bmi < 35) return 'Obese Class I';
  if (bmi < 40) return 'Obese Class II';
  return 'Obese Class III';
};

// Define normal ranges for each metric
const normalRanges: NormalRanges = {
  'blood_pressure': {
    systolic: { min: 120, max: 129, text: 'Normal: 120-129 mmHg' },
    diastolic: { min: 80, max: 84, text: 'Normal: 80-84 mmHg' }
  },
  'heart_rate': {
    lying: { min: 60, max: 100, text: 'Normal lying: 60-100 bpm' },
    standing: { min: 60, max: 100, text: 'Normal standing: 60-100 bpm' }
  },
  'temperature': { min: 36.1, max: 37.2, text: 'Normal: 36.1-37.2°C' },
  'blood_glucose': { min: 70, max: 99, text: 'Normal: 70-99 mg/dL' },
  'cholesterol': {
    total: { min: 0, max: 200, text: 'Normal: <200 mg/dL' },
    hdl: { min: 40, max: 60, text: 'Normal: >40 mg/dL' },
    ldl: { min: 0, max: 100, text: 'Normal: <100 mg/dL' }
  },
  'weight_bmi': {
    bmi: {
      min: 18.5,
      max: 24.9,
      text: 'Classifications: <18.5 Underweight, 18.5-24.9 Normal, 25-29.9 Overweight, 30-34.9 Obese I, 35-39.9 Obese II, ≥40 Obese III'
    }
  },
  'kidney_function': {
    creatinine: { min: 0.7, max: 1.3, text: 'Normal: 0.7-1.3 mg/dL' },
    egfr: { min: 90, max: 120, text: 'Normal: >90 mL/min/1.73m²' }
  },
  'liver_function': {
    bilirubin_t: { min: 0.1, max: 1.2, text: 'Normal: 0.1-1.2 mg/dL' },
    bilirubin_d: { min: 0.0, max: 0.3, text: 'Normal: 0.0-0.3 mg/dL' },
    albumin: { min: 3.5, max: 5.0, text: 'Normal: 3.5-5.0 g/dL' },
    total_protein: { min: 6.0, max: 8.3, text: 'Normal: 6.0-8.3 g/dL' },
    alt: { min: 7, max: 56, text: 'Normal: 7-56 U/L (males), 7-45 U/L (females)' },
    ast: { min: 8, max: 48, text: 'Normal: 8-48 U/L (males), 8-43 U/L (females)' },
    alp: { min: 40, max: 129, text: 'Normal: 40-129 U/L' },
    ggt: { min: 8, max: 61, text: 'Normal: 8-61 U/L (males), 5-36 U/L (females)' }
  },
  'cbc': {
    hemoglobin: { min: 13.5, max: 17.5, text: 'Normal: 13.5-17.5 g/dL (males), 12.0-15.5 g/dL (females)' },
    hematocrit: { min: 41, max: 50, text: 'Normal: 41-50% (males), 36-48% (females)' },
    rbc: { min: 4.5, max: 5.9, text: 'Normal: 4.5-5.9 million cells/μL (males), 4.1-5.1 million cells/μL (females)' },
    wbc: { min: 4.5, max: 11.0, text: 'Normal: 4.5-11.0 thousand/μL' },
    platelets: { min: 150, max: 450, text: 'Normal: 150-450 thousand/μL' }
  },
  'rbc_indices': {
    mcv: { min: 80, max: 100, text: 'Normal: 80-100 fL' },
    mch: { min: 27, max: 33, text: 'Normal: 27-33 pg' },
    mchc: { min: 32, max: 36, text: 'Normal: 32-36 g/dL' },
    rdw: { min: 11.5, max: 14.5, text: 'Normal: 11.5-14.5%' }
  },
  'wbc_differential': {
    neutrophils: { min: 40, max: 60, text: 'Normal: 40-60%' },
    lymphocytes: { min: 20, max: 40, text: 'Normal: 20-40%' },
    monocytes: { min: 2, max: 8, text: 'Normal: 2-8%' },
    eosinophils: { min: 1, max: 4, text: 'Normal: 1-4%' },
    basophils: { min: 0.5, max: 1, text: 'Normal: 0.5-1%' }
  },
  'thyroid_function': {
    tsh: { min: 0.4, max: 4.0, text: 'Normal: 0.4-4.0 mIU/L' },
    free_t3: { min: 2.3, max: 4.2, text: 'Normal: 2.3-4.2 pg/mL' },
    free_t4: { min: 0.8, max: 1.8, text: 'Normal: 0.8-1.8 ng/dL' }
  },
  'inflammatory_markers': {
    crp: { min: 0, max: 3.0, text: 'Normal: <3.0 mg/L' },
    esr: { min: 0, max: 20, text: 'Normal: 0-20 mm/hr (males), 0-30 mm/hr (females)' }
  },
  'electrolytes': {
    sodium: { min: 135, max: 145, text: 'Normal: 135-145 mEq/L' },
    potassium: { min: 3.5, max: 5.0, text: 'Normal: 3.5-5.0 mEq/L' },
    calcium: { min: 8.5, max: 10.5, text: 'Normal: 8.5-10.5 mg/dL' },
    magnesium: { min: 1.7, max: 2.2, text: 'Normal: 1.7-2.2 mg/dL' }
  },
  'vitamin_iron': {
    vitamin_d: { min: 30, max: 100, text: 'Normal: 30-100 ng/mL' },
    vitamin_b12: { min: 200, max: 900, text: 'Normal: 200-900 pg/mL' },
    folate: { min: 2.7, max: 17.0, text: 'Normal: 2.7-17.0 ng/mL' },
    ferritin: { min: 20, max: 250, text: 'Normal: 20-250 ng/mL (males), 10-120 ng/mL (females)' },
    iron: { min: 60, max: 170, text: 'Normal: 60-170 μg/dL (males), 50-130 μg/dL (females)' }
  }
};

// Add metric descriptions
const metricDescriptions = {
  'blood_pressure': `Blood pressure is a vital sign that measures the force of blood pushing against artery walls. It's recorded as two numbers: systolic (pressure when heart beats) and diastolic (pressure between beats). Regular monitoring helps detect hypertension and assess cardiovascular health.

The shaded area between the yellow and blue dashed lines represents the normal range for this measurement.`,
  'heart_rate': `Heart rate, or pulse, indicates how many times your heart beats per minute. For geriatric assessment, we measure heart rate in both lying and standing positions to detect orthostatic changes. A significant increase in heart rate upon standing (>20 bpm) may indicate orthostatic intolerance or dehydration, while failure to increase may suggest autonomic dysfunction.

The shaded area between the yellow and blue dashed lines represents the normal range for this measurement.`,
  'temperature': `Body temperature is a measure of the body's ability to generate and get rid of heat. Monitoring temperature helps identify fever, infection, or other health conditions that affect body heat regulation.

The shaded area between the yellow and blue dashed lines represents the normal range for this measurement.`,
  'blood_glucose': `Blood glucose levels indicate the amount of sugar in your bloodstream. This metric is crucial for diagnosing and monitoring diabetes, as well as understanding how well your body processes sugar from the foods you eat.

The shaded area between the yellow and blue dashed lines represents the normal range for this measurement.`,
  'cholesterol': `Cholesterol levels, including total, HDL (good), and LDL (bad) cholesterol, are important indicators of cardiovascular health. These measurements help assess your risk of heart disease and stroke.

The shaded area between the yellow and blue dashed lines represents the normal range for this measurement.`,
  'weight_bmi': `Weight and Body Mass Index (BMI) track your body composition over time. BMI is calculated from height and weight, providing a rough estimate of body fat and potential health risks associated with weight.

The shaded area between the yellow and blue dashed lines represents the normal range for this measurement.`,
  'kidney_function': `Kidney function tests, including creatinine levels and eGFR (estimated Glomerular Filtration Rate), assess how well your kidneys filter waste from your blood. These metrics help detect and monitor kidney disease.

The shaded area between the yellow and blue dashed lines represents the normal range for this measurement.`,
  'liver_function': `Liver function tests measure various proteins, enzymes, and substances in the blood that reflect liver health. These tests help detect liver damage, inflammation, or impaired function. They include bilirubin (total and direct), albumin, total protein, and enzymes like ALT, AST, ALP, and GGT.

The shaded area between the yellow and blue dashed lines represents the normal range for this measurement.`,
  'cbc': `Complete Blood Count (CBC) is a panel of tests that evaluates the three types of cells in the blood: red blood cells, white blood cells, and platelets. CBC helps diagnose anemia, infections, and other blood disorders. Key measurements include hemoglobin, hematocrit, red blood cell count, white blood cell count, and platelet count.

The shaded area between the yellow and blue dashed lines represents the normal range for this measurement.`,
  'rbc_indices': `Red Blood Cell Indices provide detailed information about the size, shape, and hemoglobin content of red blood cells. These measurements help diagnose and classify different types of anemia. They include MCV (mean corpuscular volume), MCH (mean corpuscular hemoglobin), MCHC (mean corpuscular hemoglobin concentration), and RDW (red cell distribution width).

The shaded area between the yellow and blue dashed lines represents the normal range for this measurement.`,
  'wbc_differential': `White Blood Cell Differential counts the different types of white blood cells as percentages of the total white blood cell count. This helps diagnose infections, inflammation, leukemia, and immune disorders. The five main types are neutrophils, lymphocytes, monocytes, eosinophils, and basophils.

The shaded area between the yellow and blue dashed lines represents the normal range for this measurement.`,
  'thyroid_function': `Thyroid Function Tests measure the levels of hormones produced by the thyroid gland and the pituitary gland that controls it. These tests help diagnose and monitor thyroid disorders such as hypothyroidism and hyperthyroidism. Key measurements include TSH (Thyroid Stimulating Hormone), Free T3 (Triiodothyronine), and Free T4 (Thyroxine).

The shaded area between the yellow and blue dashed lines represents the normal range for this measurement.`,
  'inflammatory_markers': `Inflammatory Markers are blood tests that detect inflammation in the body. They are used to diagnose and monitor inflammatory conditions, infections, and autoimmune disorders. The two most common markers are C-Reactive Protein (CRP) and Erythrocyte Sedimentation Rate (ESR).

The shaded area between the yellow and blue dashed lines represents the normal range for this measurement.`,
  'electrolytes': `Electrolytes are minerals in the blood that carry an electric charge and are essential for many bodily functions. They help regulate fluid balance, nerve and muscle function, blood pH, and blood pressure. Key electrolytes include sodium, potassium, calcium, and magnesium.

The shaded area between the yellow and blue dashed lines represents the normal range for this measurement.`,
  'vitamin_iron': `Vitamin Status & Iron Studies assess the levels of essential vitamins and iron in the blood. These tests help diagnose deficiencies that can lead to anemia, neurological problems, and other health issues. Key measurements include Vitamin D, Vitamin B12, Folate, Ferritin (iron storage), and Iron.

The shaded area between the yellow and blue dashed lines represents the normal range for this measurement.`
};

// Define the available metrics and their configurations
const metricConfigs = {
  'blood_pressure': {
    label: 'Blood Pressure',
    icon: <MonitorHeartIcon />,
    charts: [
      {
        title: 'Systolic BP',
        key: 'lying_bp_systolic',
        color: '#D97B3A', // Orange (error.main)
        unit: 'mmHg'
      },
      {
        title: 'Diastolic BP',
        key: 'lying_bp_diastolic',
        color: '#F2A65A', // Light Orange (info.main)
        unit: 'mmHg'
      }
    ]
  },
  'heart_rate': {
    label: 'Heart Rate',
    icon: <FavoriteIcon />,
    charts: [
      {
        title: 'Lying Heart Rate',
        key: 'lying_heart_rate',
        color: '#D97B3A', // Orange (error.main)
        unit: 'bpm'
      },
      {
        title: 'Standing Heart Rate',
        key: 'standing_heart_rate',
        color: '#F2A65A', // Light Orange (info.main)
        unit: 'bpm'
      }
    ]
  },
  'temperature': {
    label: 'Temperature',
    icon: <ThermostatIcon />,
    charts: [{
      title: 'Temperature',
      key: 'temperature',
      color: '#D97B3A', // Orange (error.main)
      unit: '°C'
    }]
  },
  'blood_glucose': {
    label: 'Blood Glucose',
    icon: <BloodtypeIcon />,
    charts: [{
      title: 'Blood Glucose',
      key: 'blood_glucose',
      color: '#D97B3A', // Orange (error.main)
      unit: 'mg/dL'
    }]
  },
  'cholesterol': {
    label: 'Cholesterol',
    icon: <LocalHospitalIcon />,
    charts: [
      {
        title: 'Total Cholesterol',
        key: 'cholesterol_total',
        color: '#D97B3A', // Orange (error.main)
        unit: 'mg/dL'
      },
      {
        title: 'HDL Cholesterol',
        key: 'hdl_cholesterol',
        color: '#F2A65A', // Light Orange (info.main)
        unit: 'mg/dL'
      },
      {
        title: 'LDL Cholesterol',
        key: 'ldl_cholesterol',
        color: '#A1A43A', // Olive Green (secondary.main)
        unit: 'mg/dL'
      }
    ]
  },
  'weight_bmi': {
    label: 'Weight & BMI',
    icon: <ScaleIcon />,
    charts: [
      {
        title: 'Weight',
        key: 'weight',
        color: '#D97B3A', // Orange (error.main)
        unit: 'kg'
      },
      {
        title: 'BMI',
        key: 'bmi',
        color: '#F2A65A', // Light Orange (info.main)
        unit: 'BMI'
      }
    ]
  },
  'kidney_function': {
    label: 'Kidney Function',
    icon: <WaterDropIcon />,
    charts: [
      {
        title: 'Creatinine',
        key: 'creatinine',
        color: '#D97B3A', // Orange (error.main)
        unit: 'mg/dL'
      },
      {
        title: 'eGFR',
        key: 'egfr',
        color: '#F2A65A', // Light Orange (info.main)
        unit: 'mL/min/1.73m²'
      }
    ]
  },
  'liver_function': {
    label: 'Liver Function Tests',
    icon: <MedicationIcon />,
    charts: [
      {
        title: 'Total Bilirubin',
        key: 'bilirubin_t',
        color: '#D97B3A', // Orange (error.main)
        unit: 'mg/dL'
      },
      {
        title: 'Direct Bilirubin',
        key: 'bilirubin_d',
        color: '#F2A65A', // Light Orange (info.main)
        unit: 'mg/dL'
      },
      {
        title: 'Albumin',
        key: 'albumin',
        color: '#A1A43A', // Olive Green (secondary.main)
        unit: 'g/dL'
      },
      {
        title: 'Total Protein',
        key: 'total_protein',
        color: '#4CAF50', // Green
        unit: 'g/dL'
      },
      {
        title: 'ALT',
        key: 'alt',
        color: '#9C27B0', // Purple
        unit: 'U/L'
      },
      {
        title: 'AST',
        key: 'ast',
        color: '#2196F3', // Blue
        unit: 'U/L'
      },
      {
        title: 'ALP',
        key: 'alp',
        color: '#FF5722', // Deep Orange
        unit: 'U/L'
      },
      {
        title: 'GGT',
        key: 'ggt',
        color: '#795548', // Brown
        unit: 'U/L'
      }
    ]
  },
  'cbc': {
    label: 'Complete Blood Count',
    icon: <BiotechIcon />,
    charts: [
      {
        title: 'Hemoglobin',
        key: 'hemoglobin',
        color: '#D97B3A', // Orange (error.main)
        unit: 'g/dL'
      },
      {
        title: 'Hematocrit',
        key: 'hematocrit',
        color: '#F2A65A', // Light Orange (info.main)
        unit: '%'
      },
      {
        title: 'Red Blood Cell Count',
        key: 'rbc',
        color: '#A1A43A', // Olive Green (secondary.main)
        unit: 'million/μL'
      },
      {
        title: 'White Blood Cell Count',
        key: 'wbc',
        color: '#4CAF50', // Green
        unit: 'thousand/μL'
      },
      {
        title: 'Platelet Count',
        key: 'platelets',
        color: '#9C27B0', // Purple
        unit: 'thousand/μL'
      }
    ]
  },
  'rbc_indices': {
    label: 'RBC Indices',
    icon: <ScienceIcon />,
    charts: [
      {
        title: 'MCV',
        key: 'mcv',
        color: '#D97B3A', // Orange (error.main)
        unit: 'fL'
      },
      {
        title: 'MCH',
        key: 'mch',
        color: '#F2A65A', // Light Orange (info.main)
        unit: 'pg'
      },
      {
        title: 'MCHC',
        key: 'mchc',
        color: '#A1A43A', // Olive Green (secondary.main)
        unit: 'g/dL'
      },
      {
        title: 'RDW',
        key: 'rdw',
        color: '#4CAF50', // Green
        unit: '%'
      }
    ]
  },
  'wbc_differential': {
    label: 'WBC Differential',
    icon: <HealthAndSafetyIcon />,
    charts: [
      {
        title: 'Neutrophils',
        key: 'neutrophils',
        color: '#D97B3A', // Orange (error.main)
        unit: '%'
      },
      {
        title: 'Lymphocytes',
        key: 'lymphocytes',
        color: '#F2A65A', // Light Orange (info.main)
        unit: '%'
      },
      {
        title: 'Monocytes',
        key: 'monocytes',
        color: '#A1A43A', // Olive Green (secondary.main)
        unit: '%'
      },
      {
        title: 'Eosinophils',
        key: 'eosinophils',
        color: '#4CAF50', // Green
        unit: '%'
      },
      {
        title: 'Basophils',
        key: 'basophils',
        color: '#9C27B0', // Purple
        unit: '%'
      }
    ]
  },
  'thyroid_function': {
    label: 'Thyroid Function',
    icon: <VaccinesIcon />,
    charts: [
      {
        title: 'TSH',
        key: 'tsh',
        color: '#D97B3A', // Orange (error.main)
        unit: 'mIU/L'
      },
      {
        title: 'Free T3',
        key: 'free_t3',
        color: '#F2A65A', // Light Orange (info.main)
        unit: 'pg/mL'
      },
      {
        title: 'Free T4',
        key: 'free_t4',
        color: '#A1A43A', // Olive Green (secondary.main)
        unit: 'ng/dL'
      }
    ]
  },
  'inflammatory_markers': {
    label: 'Inflammatory Markers',
    icon: <HealingIcon />,
    charts: [
      {
        title: 'C-Reactive Protein',
        key: 'crp',
        color: '#D97B3A', // Orange (error.main)
        unit: 'mg/L'
      },
      {
        title: 'ESR',
        key: 'esr',
        color: '#F2A65A', // Light Orange (info.main)
        unit: 'mm/hr'
      }
    ]
  },
  'electrolytes': {
    label: 'Electrolytes',
    icon: <BoltIcon />,
    charts: [
      {
        title: 'Sodium',
        key: 'sodium',
        color: '#D97B3A', // Orange (error.main)
        unit: 'mEq/L'
      },
      {
        title: 'Potassium',
        key: 'potassium',
        color: '#F2A65A', // Light Orange (info.main)
        unit: 'mEq/L'
      },
      {
        title: 'Calcium',
        key: 'calcium',
        color: '#A1A43A', // Olive Green (secondary.main)
        unit: 'mg/dL'
      },
      {
        title: 'Magnesium',
        key: 'magnesium',
        color: '#4CAF50', // Green
        unit: 'mg/dL'
      }
    ]
  },
  'vitamin_iron': {
    label: 'Vitamin & Iron Status',
    icon: <WbSunnyIcon />,
    charts: [
      {
        title: 'Vitamin D',
        key: 'vitamin_d',
        color: '#D97B3A', // Orange (error.main)
        unit: 'ng/mL'
      },
      {
        title: 'Vitamin B12',
        key: 'vitamin_b12',
        color: '#F2A65A', // Light Orange (info.main)
        unit: 'pg/mL'
      },
      {
        title: 'Folate',
        key: 'folate',
        color: '#A1A43A', // Olive Green (secondary.main)
        unit: 'ng/mL'
      },
      {
        title: 'Ferritin',
        key: 'ferritin',
        color: '#4CAF50', // Green
        unit: 'ng/mL'
      },
      {
        title: 'Iron',
        key: 'iron',
        color: '#9C27B0', // Purple
        unit: 'μg/dL'
      }
    ]
  }
};

const HealthMetricsChart: React.FC<HealthMetricsChartProps> = ({ visits }) => {
  const theme = useTheme();
  const [selectedMetric, setSelectedMetric] = useState<string>('blood_pressure');
  const [expandedDescription, setExpandedDescription] = useState<boolean>(false);

  if (!visits || visits.length === 0) {
    return (
      <Box sx={{ width: '100%', mt: 2 }}>
        <Paper
          elevation={0}
          sx={{
            p: 3,
            borderRadius: 2,
            border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
            bgcolor: alpha(theme.palette.primary.main, 0.05)
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', flexDirection: 'column', gap: 1 }}>
            <InfoOutlinedIcon color="primary" sx={{ fontSize: 40, opacity: 0.7 }} />
            <Typography variant="h6" align="center" color="primary.main">
              No visit data available for visualization
            </Typography>
            <Typography variant="body2" align="center" color="text.secondary">
              Add visits with health metrics data to see charts
            </Typography>
          </Box>
        </Paper>
      </Box>
    );
  }

  const sortedVisits = [...visits].sort((a, b) =>
    new Date(a.visit_date).getTime() - new Date(b.visit_date).getTime()
  );

  const handleMetricChange = (event: SelectChangeEvent) => {
    setSelectedMetric(event.target.value);
  };

  const handleDescriptionToggle = () => {
    setExpandedDescription(!expandedDescription);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Helper function to get the value from a visit, handling field name inconsistencies
  const getValueFromVisit = (visit: Visit, key: string): number | null => {
    // Check for the primary key first
    if (visit[key as keyof Visit] !== undefined && visit[key as keyof Visit] !== null) {
      return visit[key as keyof Visit] as number;
    }

    // Handle field name inconsistencies
    const alternativeKeys: Record<string, string[]> = {
      'blood_glucose': ['glucose'],
      'glucose': ['blood_glucose'],
      'cholesterol_total': ['total_cholesterol'],
      'total_cholesterol': ['cholesterol_total'],
      'hdl_cholesterol': ['hdl'],
      'hdl': ['hdl_cholesterol'],
      'ldl_cholesterol': ['ldl'],
      'ldl': ['ldl_cholesterol'],
      'bilirubin_t': ['bilirubin_total', 'total_bilirubin'],
      'bilirubin_total': ['bilirubin_t', 'total_bilirubin'],
      'total_bilirubin': ['bilirubin_t', 'bilirubin_total'],
      'bilirubin_d': ['bilirubin_direct', 'direct_bilirubin'],
      'bilirubin_direct': ['bilirubin_d', 'direct_bilirubin'],
      'direct_bilirubin': ['bilirubin_d', 'bilirubin_direct'],
      'total_protein': ['protein_total'],
      'protein_total': ['total_protein'],
      'platelets': ['platelet_count'],
      'platelet_count': ['platelets'],
      'blood_urea_nitrogen': ['bun'],
      'bun': ['blood_urea_nitrogen'],
      'phosphate': ['phosphorus'],
      'phosphorus': ['phosphate'],
      'temperature': ['body_temperature'],
      'body_temperature': ['temperature'],
      'pulse_oximetry': ['oxygen_saturation'],
      'oxygen_saturation': ['pulse_oximetry']
    };

    // Check alternative keys
    if (alternativeKeys[key]) {
      for (const altKey of alternativeKeys[key]) {
        if (visit[altKey as keyof Visit] !== undefined && visit[altKey as keyof Visit] !== null) {
          return visit[altKey as keyof Visit] as number;
        }
      }
    }

    // If no value found, return null
    return null;
  };

  const createChartData = (chartConfig: any, normalRange?: Range) => {
    const visitsWithData = sortedVisits.filter(visit => {
      const value = getValueFromVisit(visit, chartConfig.key);
      return value !== null;
    });

    const data = {
      labels: visitsWithData.map(visit => formatDate(visit.visit_date)),
      datasets: [
        {
          label: chartConfig.title,
          data: visitsWithData.map(visit => getValueFromVisit(visit, chartConfig.key)),
          borderColor: chartConfig.color,
          backgroundColor: theme.palette.mode === 'dark'
            ? alpha(chartConfig.color, 0.2)  // Increased opacity in dark mode
            : alpha(chartConfig.color, 0.1),
          borderWidth: theme.palette.mode === 'dark' ? 3 : 2,
          tension: 0.3,
          spanGaps: true,
          // Store visit types for use in tooltip
          visitTypes: visitsWithData.map(visit => visit.visit_type),
          pointBackgroundColor: visitsWithData.map(visit => {
            const value = getValueFromVisit(visit, chartConfig.key);
            if (normalRange && value !== null) {
              // Highlight abnormal values with different point colors
              if (value < normalRange.min) return theme.palette.warning.main;
              if (value > normalRange.max) return theme.palette.error.main;
            }
            // Use a different color for initial assessment data points
            if (visit.visit_type === 'baseline') {
              return theme.palette.secondary.main;
            }
            return chartConfig.color;
          }),
          pointBorderColor: theme.palette.mode === 'dark' ? alpha('#fff', 0.8) : '#fff',
          pointRadius: theme.palette.mode === 'dark' ? 6 : 5,
          pointHoverRadius: theme.palette.mode === 'dark' ? 8 : 7,
          fill: true
        } as ChartDataset
      ]
    };

    if (normalRange && chartConfig.key !== 'weight') {
      // Add maximum normal range line
      data.datasets.push({
        label: 'Maximum Normal',
        data: Array(visitsWithData.length).fill(normalRange.max),
        borderColor: theme.palette.warning.main, // Yellow-Orange color for maximum
        backgroundColor: 'transparent',
        borderWidth: theme.palette.mode === 'dark' ? 3 : 2,
        borderDash: [5, 5],
        pointRadius: 0,
        fill: false
      } as ChartDataset);

      // Add minimum normal range line
      data.datasets.push({
        label: 'Minimum Normal',
        data: Array(visitsWithData.length).fill(normalRange.min),
        borderColor: theme.palette.info.main, // Light Orange color for minimum
        backgroundColor: theme.palette.mode === 'dark'
          ? 'rgba(46, 125, 50, 0.4)' // Darker green in dark mode with higher opacity
          : alpha(theme.palette.success.light, 0.15),
        borderWidth: theme.palette.mode === 'dark' ? 3 : 2,
        borderDash: [5, 5],
        pointRadius: 0,
        fill: 1 // Fill between min and max lines
      } as ChartDataset);

      // Add a tooltip dataset to explain the normal range area
      data.datasets.push({
        label: 'Normal Range Area',
        data: Array(visitsWithData.length).fill(null),
        borderColor: 'transparent',
        backgroundColor: 'transparent',
        borderWidth: 0,
        pointRadius: 0,
        fill: false
      } as ChartDataset);
    }

    return data;
  };

  const createChartOptions = (chartConfig: any, normalRange?: Range) => {
    return {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top' as const,
          labels: {
            usePointStyle: true,
            pointStyle: 'circle',
            boxWidth: 8,
            boxHeight: 8,
            padding: 20,
            font: {
              size: 12
            },
            color: theme.palette.mode === 'dark' ? theme.palette.common.white : theme.palette.text.primary,
            filter: function(item: any) {
              // Make sure the item is visible
              return true;
            }
          },
          // Ensure the legend is visible in dark mode
          display: true
        },
        title: {
          display: true,
          text: chartConfig.title,
          font: {
            size: 16,
            weight: 'bold' as const
          },
          padding: {
            top: 10,
            bottom: 20
          },
          color: theme.palette.mode === 'dark' ? theme.palette.common.white : theme.palette.text.primary
        },
        tooltip: {
          backgroundColor: theme.palette.mode === 'dark'
            ? alpha(theme.palette.grey[900], 0.95)
            : alpha(theme.palette.common.black, 0.85),
          titleColor: '#ffffff',
          bodyColor: '#ffffff',
          titleFont: {
            size: 14,
            weight: 'bold' as const
          },
          bodyFont: {
            size: 13
          },
          padding: 12,
          cornerRadius: 8,
          boxPadding: 6,
          usePointStyle: true,
          borderColor: theme.palette.mode === 'dark' ? alpha(theme.palette.common.white, 0.2) : undefined,
          borderWidth: theme.palette.mode === 'dark' ? 1 : 0,
          callbacks: {
            label: function(context: any) {
              // Handle the special case for the Normal Range Area dataset
              if (context.dataset.label === 'Normal Range Area') {
                return 'The shaded area represents the normal range for this measurement';
              }

              // Handle Maximum and Minimum normal lines
              if (context.dataset.label === 'Maximum Normal') {
                return `Maximum Normal: ${context.parsed.y} ${chartConfig.unit} (yellow dashed line)`;
              }

              if (context.dataset.label === 'Minimum Normal') {
                return `Minimum Normal: ${context.parsed.y} ${chartConfig.unit} (blue dashed line)`;
              }

              // Handle the main dataset
              const value = context.parsed.y;
              let label = `${context.dataset.label}: ${value} ${chartConfig.unit}`;

              // Only add classification for the main dataset (index 0)
              if (context.datasetIndex === 0) {
                // Add note for initial assessment data points
                const visitTypes = context.dataset.visitTypes;
                if (visitTypes && visitTypes[context.dataIndex] === 'baseline') {
                  label += ' (Initial Assessment)';
                }

                if (normalRange) {
                  if (chartConfig.key === 'bmi') {
                    const classification = getBMIClassification(value);
                    label += ` (${classification})`;

                    // Add status indicator
                    if (classification !== 'Normal weight') {
                      label += ' ⚠️';
                    } else {
                      label += ' ✓';
                    }
                  } else {
                    // Add status indicator based on normal range
                    if (value < normalRange.min) {
                      label += ` (Below normal) ⚠️`;
                    } else if (value > normalRange.max) {
                      label += ` (Above normal) ⚠️`;
                    } else {
                      label += ` (Normal) ✓`;
                    }
                  }
                }
              }
              return label;
            }
          }
        }
      },
      scales: {
        x: {
          grid: {
            display: false,
            color: alpha(theme.palette.mode === 'dark' ? '#fff' : '#000', 0.1)
          },
          ticks: {
            font: {
              size: 11
            },
            color: theme.palette.mode === 'dark' ? alpha(theme.palette.common.white, 0.7) : theme.palette.text.secondary
          },
          border: {
            color: alpha(theme.palette.text.secondary, 0.2)
          }
        },
        y: {
          beginAtZero: false,
          grid: {
            color: alpha(theme.palette.text.secondary, theme.palette.mode === 'dark' ? 0.2 : 0.1)
          },
          title: {
            display: true,
            text: chartConfig.unit,
            font: {
              size: 12,
              weight: 'bold' as const
            },
            color: theme.palette.mode === 'dark' ? theme.palette.common.white : theme.palette.text.primary
          },
          ticks: {
            font: {
              size: 11
            },
            color: theme.palette.mode === 'dark' ? alpha(theme.palette.common.white, 0.7) : theme.palette.text.secondary
          },
          border: {
            color: alpha(theme.palette.text.secondary, 0.2)
          }
        }
      }
    };
  };

  const getNormalRange = (chartConfig: any) => {
    if (selectedMetric === 'blood_pressure') {
      const bpRanges = normalRanges.blood_pressure;
      return chartConfig.key === 'lying_bp_systolic' ? bpRanges.systolic : bpRanges.diastolic;
    } else if (selectedMetric === 'heart_rate') {
      const hrRanges = normalRanges.heart_rate;
      return chartConfig.key === 'lying_heart_rate' ? hrRanges.lying : hrRanges.standing;
    } else if (selectedMetric === 'cholesterol') {
      const cholRanges = normalRanges.cholesterol;
      switch (chartConfig.key) {
        case 'cholesterol_total': return cholRanges.total;
        case 'hdl_cholesterol': return cholRanges.hdl;
        case 'ldl_cholesterol': return cholRanges.ldl;
      }
    } else if (selectedMetric === 'weight_bmi' && chartConfig.key === 'bmi') {
      return normalRanges.weight_bmi.bmi;
    } else if (selectedMetric === 'kidney_function') {
      const kidneyRanges = normalRanges.kidney_function;
      return chartConfig.key === 'creatinine' ? kidneyRanges.creatinine : kidneyRanges.egfr;
    } else if (selectedMetric === 'liver_function') {
      const liverRanges = normalRanges.liver_function;
      switch (chartConfig.key) {
        case 'bilirubin_t': return liverRanges.bilirubin_t;
        case 'bilirubin_d': return liverRanges.bilirubin_d;
        case 'albumin': return liverRanges.albumin;
        case 'total_protein': return liverRanges.total_protein;
        case 'alt': return liverRanges.alt;
        case 'ast': return liverRanges.ast;
        case 'alp': return liverRanges.alp;
        case 'ggt': return liverRanges.ggt;
      }
    } else if (selectedMetric === 'cbc') {
      const cbcRanges = normalRanges.cbc;
      switch (chartConfig.key) {
        case 'hemoglobin': return cbcRanges.hemoglobin;
        case 'hematocrit': return cbcRanges.hematocrit;
        case 'rbc': return cbcRanges.rbc;
        case 'wbc': return cbcRanges.wbc;
        case 'platelets': return cbcRanges.platelets;
      }
    } else if (selectedMetric === 'rbc_indices') {
      const rbcRanges = normalRanges.rbc_indices;
      switch (chartConfig.key) {
        case 'mcv': return rbcRanges.mcv;
        case 'mch': return rbcRanges.mch;
        case 'mchc': return rbcRanges.mchc;
        case 'rdw': return rbcRanges.rdw;
      }
    } else if (selectedMetric === 'wbc_differential') {
      const wbcRanges = normalRanges.wbc_differential;
      switch (chartConfig.key) {
        case 'neutrophils': return wbcRanges.neutrophils;
        case 'lymphocytes': return wbcRanges.lymphocytes;
        case 'monocytes': return wbcRanges.monocytes;
        case 'eosinophils': return wbcRanges.eosinophils;
        case 'basophils': return wbcRanges.basophils;
      }
    } else if (selectedMetric === 'thyroid_function') {
      const thyroidRanges = normalRanges.thyroid_function;
      switch (chartConfig.key) {
        case 'tsh': return thyroidRanges.tsh;
        case 'free_t3': return thyroidRanges.free_t3;
        case 'free_t4': return thyroidRanges.free_t4;
      }
    } else if (selectedMetric === 'inflammatory_markers') {
      const inflammatoryRanges = normalRanges.inflammatory_markers;
      switch (chartConfig.key) {
        case 'crp': return inflammatoryRanges.crp;
        case 'esr': return inflammatoryRanges.esr;
      }
    } else if (selectedMetric === 'electrolytes') {
      const electrolyteRanges = normalRanges.electrolytes;
      switch (chartConfig.key) {
        case 'sodium': return electrolyteRanges.sodium;
        case 'potassium': return electrolyteRanges.potassium;
        case 'calcium': return electrolyteRanges.calcium;
        case 'magnesium': return electrolyteRanges.magnesium;
      }
    } else if (selectedMetric === 'vitamin_iron') {
      const vitaminIronRanges = normalRanges.vitamin_iron;
      switch (chartConfig.key) {
        case 'vitamin_d': return vitaminIronRanges.vitamin_d;
        case 'vitamin_b12': return vitaminIronRanges.vitamin_b12;
        case 'folate': return vitaminIronRanges.folate;
        case 'ferritin': return vitaminIronRanges.ferritin;
        case 'iron': return vitaminIronRanges.iron;
      }
    } else {
      return normalRanges[selectedMetric as keyof NormalRanges] as Range;
    }
  };

  const selectedConfig = metricConfigs[selectedMetric as keyof typeof metricConfigs];

  return (
    <Box sx={{ width: '100%', mt: 2 }}>
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 3,
          borderRadius: 2,
          border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
          bgcolor: alpha(theme.palette.primary.main, 0.05)
        }}
      >
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Typography variant="h5" color="primary.main" fontWeight="500">
              Health Metrics Visualization
            </Typography>

            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <MuiTooltip title="View description">
                <IconButton
                  size="small"
                  onClick={handleDescriptionToggle}
                  sx={{
                    bgcolor: expandedDescription ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
                    '&:hover': { bgcolor: alpha(theme.palette.primary.main, 0.15) }
                  }}
                >
                  <InfoOutlinedIcon fontSize="small" />
                </IconButton>
              </MuiTooltip>
            </Box>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <FormControl
              variant="outlined"
              size="small"
              sx={{
                minWidth: 220,
                '& .MuiOutlinedInput-root': {
                  borderRadius: 2,
                  bgcolor: theme.palette.background.paper
                }
              }}
            >
              <InputLabel id="metric-select-label">Select Health Metric</InputLabel>
              <Select
                labelId="metric-select-label"
                id="metric-select"
                value={selectedMetric}
                label="Select Health Metric"
                onChange={handleMetricChange}
              >
                {Object.entries(metricConfigs).map(([key, config]) => (
                  <MenuItem key={key} value={key}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Box sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: theme.palette.primary.main,
                        width: 24,
                        height: 24
                      }}>
                        {config.icon}
                      </Box>
                      <Typography>{config.label}</Typography>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>

          <Accordion
            expanded={expandedDescription}
            onChange={handleDescriptionToggle}
            elevation={0}
            sx={{
              bgcolor: 'transparent',
              '&:before': { display: 'none' },
              border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
              borderRadius: '8px !important',
              overflow: 'hidden'
            }}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              sx={{
                bgcolor: alpha(theme.palette.primary.main, 0.05),
                minHeight: '48px !important',
                '& .MuiAccordionSummary-content': { margin: '8px 0 !important' }
              }}
            >
              <Typography variant="body2" fontWeight="500">
                About {metricConfigs[selectedMetric as keyof typeof metricConfigs].label}
              </Typography>
            </AccordionSummary>
            <AccordionDetails sx={{
              bgcolor: theme.palette.mode === 'dark'
                ? alpha(theme.palette.background.paper, 0.3)
                : alpha(theme.palette.background.default, 0.5),
              p: 2
            }}>
              <Typography
                variant="body2"
                sx={{
                  lineHeight: 1.6,
                  mb: 2,
                  color: theme.palette.mode === 'dark' ? alpha(theme.palette.common.white, 0.7) : theme.palette.text.secondary
                }}>
                {metricDescriptions[selectedMetric as keyof typeof metricDescriptions]}
              </Typography>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, mt: 2 }}>
                <Typography
                  variant="body2"
                  fontWeight="500"
                  sx={{
                    color: theme.palette.mode === 'dark' ? '#ffffff !important' : theme.palette.text.primary
                  }}
                >
                  Chart Legend:
                </Typography>
                <Box sx={{
                  display: 'flex',
                  flexWrap: 'wrap',
                  gap: 2,
                  '& .MuiTypography-root': {
                    color: theme.palette.mode === 'dark' ? '#ffffff !important' : theme.palette.text.primary
                  }
                }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Box sx={{
                      width: 16,
                      height: 16,
                      borderRadius: '50%',
                      bgcolor: metricConfigs[selectedMetric as keyof typeof metricConfigs].charts[0].color,
                      border: theme.palette.mode === 'dark' ? '1px solid rgba(255,255,255,0.3)' : 'none'
                    }} />
                    <Typography variant="body2">Measurement Value</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Box sx={{
                      width: 16,
                      height: 3,
                      bgcolor: theme.palette.warning.main,
                      border: theme.palette.mode === 'dark' ? '1px solid rgba(255,255,255,0.3)' : 'none'
                    }} />
                    <Typography variant="body2">Maximum Normal (Yellow)</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Box sx={{
                      width: 16,
                      height: 3,
                      bgcolor: theme.palette.info.main,
                      border: theme.palette.mode === 'dark' ? '1px solid rgba(255,255,255,0.3)' : 'none'
                    }} />
                    <Typography variant="body2">Minimum Normal (Blue)</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Box sx={{
                      width: 16,
                      height: 16,
                      bgcolor: theme.palette.mode === 'dark' ? 'rgba(46, 125, 50, 0.3)' : alpha(theme.palette.success.light, 0.15),
                      border: theme.palette.mode === 'dark' ? '1px solid rgba(76, 175, 80, 0.5)' : `1px solid ${alpha(theme.palette.success.light, 0.3)}`,
                      boxSizing: 'border-box'
                    }} />
                    <Typography variant="body2">Normal Range Area (Green)</Typography>
                  </Box>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Box sx={{
                      width: 16,
                      height: 16,
                      borderRadius: '50%',
                      bgcolor: theme.palette.secondary.main,
                      border: theme.palette.mode === 'dark' ? '1px solid rgba(255,255,255,0.3)' : 'none'
                    }} />
                    <Typography variant="body2">Initial Assessment Data</Typography>
                  </Box>
                </Box>
              </Box>
            </AccordionDetails>
          </Accordion>
        </Box>
      </Paper>

      <Grid container spacing={3}>
        {selectedConfig.charts.map((chartConfig, index) => {
          const normalRange = getNormalRange(chartConfig);
          const chartData = createChartData(chartConfig, normalRange);
          const chartOptions = createChartOptions(chartConfig, normalRange);

          return (
            <Grid item xs={12} md={selectedConfig.charts.length > 1 ? 6 : 12} key={index}>
              <Paper
                elevation={0}
                sx={{
                  p: 2,
                  height: { xs: 300, sm: 400 },
                  borderRadius: 2,
                  border: theme.palette.mode === 'dark'
                    ? `1px solid ${alpha(theme.palette.common.white, 0.15)}`
                    : `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                  overflow: 'hidden',
                  bgcolor: theme.palette.mode === 'dark'
                    ? alpha(theme.palette.background.paper, 0.8)
                    : theme.palette.background.paper,
                  '& canvas': {
                    // Ensure chart canvas is properly visible in dark mode
                    filter: theme.palette.mode === 'dark' ? 'brightness(1.1)' : 'none'
                  }
                }}
              >
                <Line data={chartData} options={chartOptions} />
              </Paper>
            </Grid>
          );
        })}
      </Grid>
    </Box>
  );
};

export default HealthMetricsChart;