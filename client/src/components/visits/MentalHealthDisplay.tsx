import React from 'react';
import { Typography, Box } from '@mui/material'; // Paper, Grid, and Divider are unused

interface MentalHealthDisplayProps {
  value: string;
}

const MentalHealthDisplay: React.FC<MentalHealthDisplayProps> = ({ value }) => {
  try {
    // Try to parse the JSON value
    const assessmentData = JSON.parse(value);

    // If it's the old format with PHQ-9 and GAD-7, we'll skip those
    // and just display other mental health information if available
    if (assessmentData.notes) {
      return (
        <Box>
          <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
            {assessmentData.notes}
          </Typography>
        </Box>
      );
    }
  } catch (e) {
    // If parsing fails, display the value as is
  }

  // Fallback for non-JSON values or missing data
  return <Typography variant="body1">{value}</Typography>;
};

export default MentalHealthDisplay;