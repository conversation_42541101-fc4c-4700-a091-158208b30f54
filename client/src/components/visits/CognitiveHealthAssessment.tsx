import React, { useState, useEffect } from 'react';
import {
  Typography,
  Paper,
  Grid,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  // Button - Unused import
  Divider,
  Box,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  TextField,
  Checkbox,
  FormGroup
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

interface CognitiveHealthAssessmentProps {
  value: string;
  onChange: (value: string) => void;
  // New props for individual Mini-Cog fields
  miniCogWordRecallScore?: string;
  miniCogClockDrawingScore?: string;
  miniCogWordsUsed?: string;
  miniCogWordsRecalled?: string;
  miniCogNotes?: string;
  onMiniCogChange?: (fields: {
    mini_cog_word_recall_score: string;
    mini_cog_clock_drawing_score: string;
    mini_cog_words_used: string;
    mini_cog_words_recalled: string;
    mini_cog_notes: string;
    cognitive_impairment_score: string;
    mini_cog_total: string; // Added this field to the interface
  }) => void;
}

// Mini-Cog interpretation
const getMiniCogInterpretation = (wordRecallScore: number, clockDrawingScore: number): string => {
  const totalScore = wordRecallScore + clockDrawingScore;
  if (totalScore <= 2) {
    return "Positive screen for cognitive impairment";
  } else {
    return "Negative screen for cognitive impairment";
  }
};

const CognitiveHealthAssessment: React.FC<CognitiveHealthAssessmentProps> = ({
  value,
  onChange,
  miniCogWordRecallScore,
  miniCogClockDrawingScore,
  miniCogWordsUsed,
  miniCogWordsRecalled,
  miniCogNotes,
  onMiniCogChange
}) => {
  // Parse the existing value or initialize empty scores
  const [assessmentState, setAssessmentState] = useState(() => {
    try {
      // First try to initialize from the individual fields
      if (miniCogWordRecallScore !== undefined || miniCogClockDrawingScore !== undefined ||
          miniCogWordsUsed !== undefined || miniCogWordsRecalled !== undefined) {

        // Parse words used and recalled status
        let selectedWords = ["", "", ""];
        let recalledStatus = [false, false, false];

        if (miniCogWordsUsed) {
          try {
            selectedWords = JSON.parse(miniCogWordsUsed);
          } catch {
            // If parsing fails, try comma-separated format
            selectedWords = miniCogWordsUsed.split(',').map(w => w.trim());
            // Ensure we have exactly 3 words
            while (selectedWords.length < 3) selectedWords.push("");
            if (selectedWords.length > 3) selectedWords = selectedWords.slice(0, 3);
          }
        }

        if (miniCogWordsRecalled) {
          try {
            recalledStatus = JSON.parse(miniCogWordsRecalled);
          } catch {
            // If parsing fails, try comma-separated format (1,0,1)
            recalledStatus = miniCogWordsRecalled.split(',').map(r => r.trim() === '1' || r.trim().toLowerCase() === 'true');
            // Ensure we have exactly 3 statuses
            while (recalledStatus.length < 3) recalledStatus.push(false);
            if (recalledStatus.length > 3) recalledStatus = recalledStatus.slice(0, 3);
          }
        }

        return {
          miniCog: {
            words: {
              selected: selectedWords,
              recalled: recalledStatus
            },
            clockDrawing: miniCogClockDrawingScore ? parseInt(miniCogClockDrawingScore) : 0,
            wordRecallScore: miniCogWordRecallScore ? parseInt(miniCogWordRecallScore) : 0,
            notes: miniCogNotes || ""
          },
          additionalNotes: ""
        };
      }

      // If individual fields are not available, try to parse from JSON
      return value ? JSON.parse(value) : {
        miniCog: {
          words: {
            selected: ["", "", ""],
            recalled: [false, false, false]
          },
          clockDrawing: 0, // 0 or 2 points
          wordRecallScore: 0,
          notes: ""
        },
        additionalNotes: ""
      };
    } catch (e) {
      // If parsing fails, initialize with empty scores
      return {
        miniCog: {
          words: {
            selected: ["", "", ""],
            recalled: [false, false, false]
          },
          clockDrawing: 0,
          wordRecallScore: 0,
          notes: ""
        },
        additionalNotes: value || ""
      };
    }
  });

  // No assessment date needed

  // Word set options for the Mini-Cog test
  const wordSets = [
    ["Banana", "Sunrise", "Chair"],
    ["Village", "Kitchen", "Baby"],
    ["River", "Nation", "Finger"],
    ["Captain", "Garden", "Picture"],
    ["Daughter", "Heaven", "Mountain"],
    ["Forest", "Yellow", "Table"]
  ];

  // Calculate Mini-Cog scores
  const calculateMiniCogScores = () => {
    const wordRecallScore = assessmentState.miniCog.words.recalled.filter(Boolean).length;
    const clockDrawingScore = assessmentState.miniCog.clockDrawing;
    const totalScore = wordRecallScore + clockDrawingScore;
    return { wordRecallScore, clockDrawingScore, totalScore };
  };

  // Update both the JSON state and individual fields
  const updateState = (newState: any) => {
    setAssessmentState(newState);

    // Update the JSON string for backward compatibility
    onChange(JSON.stringify(newState));

    // Update individual fields if the callback is provided
    if (onMiniCogChange) {
      const { wordRecallScore, clockDrawing } = newState.miniCog;
      const totalScore = wordRecallScore + clockDrawing;

      // Format the words and recall status for storage
      const wordsUsed = JSON.stringify(newState.miniCog.words.selected);
      const wordsRecalled = JSON.stringify(newState.miniCog.words.recalled);

      // Call the callback with all the fields
      onMiniCogChange({
        mini_cog_word_recall_score: wordRecallScore.toString(),
        mini_cog_clock_drawing_score: clockDrawing.toString(),
        mini_cog_words_used: wordsUsed,
        mini_cog_words_recalled: wordsRecalled,
        mini_cog_notes: newState.miniCog.notes || "",
        cognitive_impairment_score: totalScore.toString(),
        mini_cog_total: totalScore.toString() // Explicitly set mini_cog_total here
      });

      // Log the fields being sent to the parent component
      console.log('Sending Mini-Cog fields to parent:', {
        mini_cog_word_recall_score: wordRecallScore.toString(),
        mini_cog_clock_drawing_score: clockDrawing.toString(),
        mini_cog_total: totalScore.toString(),
        cognitive_impairment_score: totalScore.toString()
      });
    }
  };

  // No assessment date change handler needed

  // Handle word set selection
  const handleWordSetChange = (setIndex: number) => {
    const newState = {
      ...assessmentState,
      miniCog: {
        ...assessmentState.miniCog,
        words: {
          selected: [...wordSets[setIndex]],
          recalled: [false, false, false]
        },
        wordRecallScore: 0 // Reset word recall score when changing word set
      }
    };

    updateState(newState);
  };

  // Handle word recall change
  const handleWordRecallChange = (wordIndex: number, recalled: boolean) => {
    const newRecalled = [...assessmentState.miniCog.words.recalled];
    newRecalled[wordIndex] = recalled;
    const newWordRecallScore = newRecalled.filter(Boolean).length;

    const newState = {
      ...assessmentState,
      miniCog: {
        ...assessmentState.miniCog,
        words: {
          ...assessmentState.miniCog.words,
          recalled: newRecalled
        },
        wordRecallScore: newWordRecallScore
      }
    };

    updateState(newState);
  };

  // Handle clock drawing score change
  const handleClockDrawingChange = (score: number) => {
    const newState = {
      ...assessmentState,
      miniCog: {
        ...assessmentState.miniCog,
        clockDrawing: score
      }
    };

    updateState(newState);
  };

  // Handle notes change
  const handleNotesChange = (type: 'miniCog' | 'additional', notes: string) => {
    let newState;

    if (type === 'miniCog') {
      newState = {
        ...assessmentState,
        miniCog: {
          ...assessmentState.miniCog,
          notes
        }
      };
    } else {
      newState = {
        ...assessmentState,
        additionalNotes: notes
      };
    }

    updateState(newState);
  };

  // Initialize fields when component mounts or props change
  useEffect(() => {
    // Initialize state from props if available
    if (miniCogWordRecallScore || miniCogClockDrawingScore) {
      const wordRecallScore = parseInt(miniCogWordRecallScore || '0');
      const clockDrawingScore = parseInt(miniCogClockDrawingScore || '0');
      const totalScore = wordRecallScore + clockDrawingScore;

      // Also call onMiniCogChange to ensure all fields are properly set
      if (onMiniCogChange) {
        onMiniCogChange({
          mini_cog_word_recall_score: wordRecallScore.toString(),
          mini_cog_clock_drawing_score: clockDrawingScore.toString(),
          mini_cog_words_used: miniCogWordsUsed || '',
          mini_cog_words_recalled: miniCogWordsRecalled || '',
          mini_cog_notes: miniCogNotes || '',
          cognitive_impairment_score: totalScore.toString(),
          mini_cog_total: totalScore.toString()
        });

        console.log('Initialized Mini-Cog fields on mount:', {
          mini_cog_word_recall_score: wordRecallScore.toString(),
          mini_cog_clock_drawing_score: clockDrawingScore.toString(),
          mini_cog_total: totalScore.toString(),
          cognitive_impairment_score: totalScore.toString()
        });
      }
    }
  }, [miniCogWordRecallScore, miniCogClockDrawingScore, miniCogWordsUsed, miniCogWordsRecalled, miniCogNotes, onMiniCogChange]);

  const { wordRecallScore, clockDrawingScore, totalScore } = calculateMiniCogScores();

  return (
    <Paper elevation={0} sx={{ p: 2, mb: 2 }}>
      <Typography variant="h6" gutterBottom>Cognitive Health Assessment - Mini-Cog</Typography>

      {/* Word Recall Assessment */}
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle1">
            Mini-Cog Assessment - Score: {totalScore}/5 ({getMiniCogInterpretation(wordRecallScore, clockDrawingScore)})
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ mb: 3 }}>
            {/* Assessment date field removed */}

            <Typography variant="subtitle2" gutterBottom>
              Step 1: Three Word Registration
            </Typography>

            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Select a word set, then say to the patient: "I am going to say three words that I want you to remember now and later. The words are {assessmentState.miniCog.words.selected.join(", ")}. Please say them for me now."
            </Typography>

            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={12} sm={6}>
                <FormControl fullWidth>
                  <Typography variant="body2" gutterBottom>Select Word Set:</Typography>
                  <RadioGroup
                    row
                    value={wordSets.findIndex(set =>
                      set.every((word, i) => word === assessmentState.miniCog.words.selected[i])
                    )}
                    onChange={(e) => handleWordSetChange(parseInt(e.target.value))}
                  >
                    {wordSets.map((set, index) => (
                      <FormControlLabel
                        key={`word-set-${index}`}
                        value={index}
                        control={<Radio />}
                        label={`Set ${index + 1}: ${set.join(", ")}`}
                      />
                    ))}
                  </RadioGroup>
                </FormControl>
              </Grid>
            </Grid>

            <Typography variant="subtitle2" gutterBottom>
              Step 2: Clock Drawing Test
            </Typography>

            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Give the patient a piece of paper with a pre-drawn circle. Say: "Now, I want you to draw a clock on this paper. First, put in all of the numbers where they go." When that is completed, say: "Now, set the hands to 10 past 11."
            </Typography>

            <FormControl component="fieldset" sx={{ mb: 3 }}>
              <Typography variant="body2" gutterBottom>Clock Drawing Score:</Typography>
              <RadioGroup
                row
                value={assessmentState.miniCog.clockDrawing}
                onChange={(e) => handleClockDrawingChange(parseInt(e.target.value))}
              >
                <FormControlLabel value={0} control={<Radio />} label="Abnormal (0 points)" />
                <FormControlLabel value={2} control={<Radio />} label="Normal (2 points)" />
              </RadioGroup>
              <Typography variant="body2" color="text.secondary">
                Normal clock has all numbers placed in the correct sequence and approximately correct position with no missing or duplicate numbers. Hands are pointing to the 11 and 2 (11:10).
              </Typography>
            </FormControl>

            <Typography variant="subtitle2" gutterBottom>
              Step 3: Three Word Recall
            </Typography>

            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Ask the patient: "What were the three words I asked you to remember?"
            </Typography>

            <FormGroup sx={{ mb: 3 }}>
              {assessmentState.miniCog.words.selected.map((word: string, index: number) => (
                <FormControlLabel
                  key={`word-recall-${index}`}
                  control={
                    <Checkbox
                      checked={assessmentState.miniCog.words.recalled[index]}
                      onChange={(e) => handleWordRecallChange(index, e.target.checked)}
                      disabled={!word}
                    />
                  }
                  label={word || `Word ${index + 1} (select a word set first)`}
                />
              ))}
            </FormGroup>

            <TextField
              label="Mini-Cog Clinical Notes"
              multiline
              rows={2}
              fullWidth
              value={assessmentState.miniCog.notes}
              onChange={(e) => handleNotesChange('miniCog', e.target.value)}
              variant="outlined"
              sx={{ mt: 2 }}
            />

            <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
              <Typography variant="subtitle2">
                Mini-Cog Score: {totalScore}/5
              </Typography>
              <Typography variant="body2" sx={{ mt: 1 }}>
                Word Recall: {wordRecallScore}/3
              </Typography>
              <Typography variant="body2">
                Clock Drawing: {clockDrawingScore}/2
              </Typography>
              <Divider sx={{ my: 1 }} />
              <Typography variant="body2" color={totalScore <= 2 ? "error" : "success"}>
                Interpretation: {getMiniCogInterpretation(wordRecallScore, clockDrawingScore)}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                0-2: Positive screen for cognitive impairment | 3-5: Negative screen for cognitive impairment
              </Typography>
            </Box>
          </Box>
        </AccordionDetails>
      </Accordion>

      {/* No additional notes field as it's not being recorded */}

      {/* Summary */}
      <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
        <Typography variant="subtitle2" gutterBottom>
          Cognitive Assessment Summary
        </Typography>
        <Typography variant="body2" color={totalScore <= 2 ? "error" : "success"}>
          Mini-Cog Result: {getMiniCogInterpretation(wordRecallScore, clockDrawingScore)} (Score: {totalScore}/5)
        </Typography>
      </Box>
    </Paper>
  );
};

export default CognitiveHealthAssessment;