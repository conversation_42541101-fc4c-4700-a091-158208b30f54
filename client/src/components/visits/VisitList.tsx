import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { getPatientVisits } from '../../services/visitService';
import { getPatientById } from '../../services/patientService';
import { Visit, Patient } from '../../types';
import { Box, Button, Card, CardContent, CircularProgress, Container, Grid, Typography, Paper, Chip, Divider, IconButton, List, ListItem, ListItemText } from '@mui/material';
import LoadingSpinner from '../common/LoadingSpinner';
import AddIcon from '@mui/icons-material/Add';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import MedicalInformationIcon from '@mui/icons-material/MedicalInformation';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import PersonIcon from '@mui/icons-material/Person';

const VisitList: React.FC = () => {
  const { patientId } = useParams<{ patientId: string }>();
  const navigate = useNavigate();

  const [visits, setVisits] = useState<Visit[]>([]);
  const [patient, setPatient] = useState<Patient | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!patientId) return;

      setLoading(true);
      try {
        // Fetch patient details
        const patientData = await getPatientById(parseInt(patientId));
        setPatient(patientData);

        // Fetch visits for this patient
        const visitsData = await getPatientVisits(parseInt(patientId));
        setVisits(visitsData);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to fetch data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [patientId]);

  if (loading) {
    return <LoadingSpinner size="large" message="Loading patient visits..." />;
  }

  if (error) {
    return (
      <Container maxWidth="md">
        <Paper elevation={3} sx={{ p: 3, mt: 4 }}>
          <Typography color="error" variant="h6">
            {error}
          </Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={() => window.location.reload()}
            sx={{ mt: 2 }}
          >
            Retry
          </Button>
        </Paper>
      </Container>
    );
  }

  // Format date nicely
  const formatDate = (dateString: string) => {
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  // Format date and time
  const formatDateTime = (dateString: string) => {
    if (!dateString) return '';

    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    };
    return new Date(dateString).toLocaleDateString(undefined, options);
  };

  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate(`/patients/${patientId}`)}
            variant="outlined"
          >
            Back to Patient
          </Button>
          <Typography variant="h4" component="h1">
            Patient Visits
          </Typography>
          <Button
            startIcon={<AddIcon />}
            variant="contained"
            color="primary"
            component={Link}
            to={`/patients/${patientId}/visits/new`}
          >
            New Visit
          </Button>
        </Box>

        {patient && (
          <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
            <Box display="flex" alignItems="center" mb={2}>
              <PersonIcon fontSize="large" sx={{ mr: 2 }} />
              <Typography variant="h5">
                {patient.first_name} {patient.last_name}
              </Typography>
              <Chip
                label={patient.unique_id}
                color="primary"
                sx={{ ml: 2 }}
              />
            </Box>
            <Divider sx={{ mb: 2 }} />
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2" color="textSecondary">Date of Birth</Typography>
                <Typography variant="body1">
                  {patient.date_of_birth ? formatDate(patient.date_of_birth) : 'Not provided'}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2" color="textSecondary">Gender</Typography>
                <Typography variant="body1">
                  {patient.gender || 'Not provided'}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2" color="textSecondary">Phone</Typography>
                <Typography variant="body1">
                  {patient.phone || 'Not provided'}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Typography variant="body2" color="textSecondary">Email</Typography>
                <Typography variant="body1">
                  {patient.email || 'Not provided'}
                </Typography>
              </Grid>
            </Grid>
          </Paper>
        )}

        <Typography variant="h5" sx={{ mb: 3 }}>
          Visit History
        </Typography>

        {visits.length === 0 ? (
          <Paper elevation={2} sx={{ p: 4, textAlign: 'center' }}>
            <MedicalInformationIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="textSecondary">
              No visits recorded yet
            </Typography>
            <Typography variant="body1" color="textSecondary" sx={{ mb: 3 }}>
              This patient doesn't have any recorded visits. Create a new visit to start tracking their health data.
            </Typography>
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              component={Link}
              to={`/patients/${patientId}/visits/new`}
            >
              Record First Visit
            </Button>
          </Paper>
        ) : (
          <List>
            {visits.map((visit) => (
              <Paper
                key={visit.visit_id}
                elevation={2}
                sx={{
                  mb: 2,
                  transition: 'transform 0.2s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-3px)',
                    boxShadow: 4
                  }
                }}
              >
                <ListItem
                  component={Link}
                  to={`/patients/${patientId}/visits/${visit.visit_id}`}
                  sx={{
                    textDecoration: 'none',
                    color: 'inherit',
                    display: 'block',
                    p: 0
                  }}
                >
                  <Box sx={{ p: 2 }}>
                    <Grid container spacing={2} alignItems="center">
                      <Grid item xs={12} sm={3} md={2}>
                        <Box display="flex" flexDirection="column">
                          <Box display="flex" alignItems="center">
                            <CalendarMonthIcon color="primary" sx={{ mr: 1 }} />
                            <Typography variant="body1" fontWeight="bold">
                              {formatDate(visit.visit_date)}
                            </Typography>
                          </Box>
                          {/* Creation information */}
                          <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem', mt: 0.5, ml: 4 }}>
                            Created: {formatDateTime(visit.created_at)}
                          </Typography>
                        </Box>
                      </Grid>

                      <Grid item xs={12} sm={6} md={4}>
                        <Typography variant="body2" color="textSecondary">
                          Reason for Visit
                        </Typography>
                        <Typography variant="body1" noWrap sx={{ maxWidth: '100%' }}>
                          {visit.visit_reason || 'No reason specified'}
                        </Typography>
                      </Grid>

                      <Grid item xs={6} sm={3} md={3}>
                        <Box display="flex" alignItems="center">
                          <LocalHospitalIcon sx={{ mr: 1, color: 'text.secondary' }} />
                          <Box>
                            <Typography variant="body2" color="textSecondary">
                              Attending Doctor
                            </Typography>
                            <Typography variant="body1">
                              {visit.doctor_first_name && visit.doctor_last_name
                                ? `Dr. ${visit.doctor_first_name} ${visit.doctor_last_name}`
                                : 'No doctor assigned'}
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>

                      <Grid item xs={6} sm={12} md={3}>
                        <Box display="flex" flexDirection="column" alignItems={{ xs: 'flex-start', sm: 'flex-end' }}>
                          {visit.diagnosis && (
                            <Chip
                              label={visit.diagnosis.length > 20
                                ? visit.diagnosis.substring(0, 20) + '...'
                                : visit.diagnosis}
                              color="secondary"
                              variant="outlined"
                              size="small"
                              sx={{ mb: 1 }}
                            />
                          )}

                          {/* Edit information */}
                          {visit.updated_at && visit.updated_at !== visit.created_at && (
                            <Typography variant="caption" color="text.secondary" sx={{ fontSize: '0.7rem' }}>
                              Edited: {formatDateTime(visit.updated_at)}
                              {visit.last_edited_by_username && ` by ${visit.last_edited_by_username}`}
                            </Typography>
                          )}
                        </Box>
                      </Grid>
                    </Grid>
                  </Box>
                </ListItem>
              </Paper>
            ))}
          </List>
        )}
      </Box>
    </Container>
  );
};

export default VisitList;