import React, { useState } from 'react';
import {
  Typography,
  Paper,
  Grid,
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  Button,
  Divider,
  Box,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  TextField
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

interface MentalHealthAssessmentProps {
  value: string;
  onChange: (value: string) => void;
  depressionScore?: number;
  onDepressionScoreChange?: (score: number) => void;
  anxietyScore?: number;
  onAnxietyScoreChange?: (score: number) => void;
}

// PHQ-9 Questions for Depression Screening
const phq9Questions = [
  "Little interest or pleasure in doing things",
  "Feeling down, depressed, or hopeless",
  "Trouble falling or staying asleep, or sleeping too much",
  "Feeling tired or having little energy",
  "Poor appetite or overeating",
  "Feeling bad about yourself — or that you are a failure or have let yourself or your family down",
  "Trouble concentrating on things, such as reading the newspaper or watching television",
  "Moving or speaking so slowly that other people could have noticed? Or the opposite — being so fidgety or restless that you have been moving around a lot more than usual",
  "Thoughts that you would be better off dead or of hurting yourself in some way"
];

// GAD-7 Questions for Anxiety Screening
const gad7Questions = [
  "Feeling nervous, anxious, or on edge",
  "Not being able to stop or control worrying",
  "Worrying too much about different things",
  "Trouble relaxing",
  "Being so restless that it is hard to sit still",
  "Becoming easily annoyed or irritable",
  "Feeling afraid, as if something awful might happen"
];

// Response options for both questionnaires
const responseOptions = [
  { value: 0, label: "Not at all" },
  { value: 1, label: "Several days" },
  { value: 2, label: "More than half the days" },
  { value: 3, label: "Nearly every day" }
];

// Depression severity
const getDepressionSeverity = (score: number): string => {
  if (score >= 0 && score <= 4) return "None-minimal";
  if (score >= 5 && score <= 9) return "Mild";
  if (score >= 10 && score <= 14) return "Moderate";
  if (score >= 15 && score <= 19) return "Moderately severe";
  return "Severe";
};

// Anxiety severity
const getAnxietySeverity = (score: number): string => {
  if (score >= 0 && score <= 4) return "Minimal";
  if (score >= 5 && score <= 9) return "Mild";
  if (score >= 10 && score <= 14) return "Moderate";
  return "Severe";
};

const MentalHealthAssessment: React.FC<MentalHealthAssessmentProps> = ({
  value,
  onChange,
  depressionScore,
  onDepressionScoreChange,
  anxietyScore,
  onAnxietyScoreChange
}) => {
  // Parse the existing value or initialize empty scores
  const [assessmentState, setAssessmentState] = useState(() => {
    try {
      return value ? JSON.parse(value) : {
        phq9: Array(phq9Questions.length).fill(0),
        gad7: Array(gad7Questions.length).fill(0),
        depressionNotes: "",
        anxietyNotes: ""
      };
    } catch (e) {
      // If parsing fails, initialize with empty scores
      return {
        phq9: Array(phq9Questions.length).fill(0),
        gad7: Array(gad7Questions.length).fill(0),
        depressionNotes: "",
        anxietyNotes: ""
      };
    }
  });

  // Calculate total scores
  const phq9Total = assessmentState.phq9.reduce((sum: number, val: number) => sum + val, 0);
  const gad7Total = assessmentState.gad7.reduce((sum: number, val: number) => sum + val, 0);

  // Update external scores if provided
  React.useEffect(() => {
    if (onDepressionScoreChange) {
      onDepressionScoreChange(phq9Total);
    }
  }, [phq9Total, onDepressionScoreChange]);

  React.useEffect(() => {
    if (onAnxietyScoreChange) {
      onAnxietyScoreChange(gad7Total);
    }
  }, [gad7Total, onAnxietyScoreChange]);

  // Handle changes to PHQ-9 responses
  const handlePhq9Change = (index: number, value: number) => {
    const newPhq9 = [...assessmentState.phq9];
    newPhq9[index] = value;
    const newState = { ...assessmentState, phq9: newPhq9 };
    setAssessmentState(newState);
    onChange(JSON.stringify(newState));
  };

  // Handle changes to GAD-7 responses
  const handleGad7Change = (index: number, value: number) => {
    const newGad7 = [...assessmentState.gad7];
    newGad7[index] = value;
    const newState = { ...assessmentState, gad7: newGad7 };
    setAssessmentState(newState);
    onChange(JSON.stringify(newState));
  };

  // Handle changes to depression notes
  const handleDepressionNotesChange = (notes: string) => {
    const newState = { ...assessmentState, depressionNotes: notes };
    setAssessmentState(newState);
    onChange(JSON.stringify(newState));
  };

  // Handle changes to anxiety notes
  const handleAnxietyNotesChange = (notes: string) => {
    const newState = { ...assessmentState, anxietyNotes: notes };
    setAssessmentState(newState);
    onChange(JSON.stringify(newState));
  };

  return (
    <Paper elevation={0} sx={{ p: 2, mb: 2 }}>
      <Typography variant="h6" gutterBottom>Mental Health Assessment</Typography>

      {/* PHQ-9 Depression Screening */}
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle1">
            PHQ-9 Depression Screening - Score: {phq9Total} ({getDepressionSeverity(phq9Total)})
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography variant="body2" gutterBottom>
            Over the last 2 weeks, how often have you been bothered by any of the following problems?
          </Typography>

          <Grid container spacing={2}>
            {phq9Questions.map((question, index) => (
              <Grid item xs={12} key={`phq9-${index}`}>
                <Typography variant="body2" gutterBottom>{question}</Typography>
                <FormControl component="fieldset">
                  <RadioGroup
                    row
                    value={assessmentState.phq9[index]}
                    onChange={(e) => handlePhq9Change(index, parseInt(e.target.value))}
                  >
                    {responseOptions.map((option) => (
                      <FormControlLabel
                        key={`phq9-${index}-${option.value}`}
                        value={option.value}
                        control={<Radio size="small" />}
                        label={option.label}
                      />
                    ))}
                  </RadioGroup>
                </FormControl>
                {index < phq9Questions.length - 1 && <Divider sx={{ my: 1 }} />}
              </Grid>
            ))}
          </Grid>

          <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
            <Typography variant="subtitle2">
              Depression Severity: {getDepressionSeverity(phq9Total)} (Score: {phq9Total})
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              0-4: None-minimal | 5-9: Mild | 10-14: Moderate | 15-19: Moderately severe | 20-27: Severe
            </Typography>
          </Box>
        </AccordionDetails>
      </Accordion>

      {/* GAD-7 Anxiety Screening */}
      <Accordion defaultExpanded sx={{ mt: 2 }}>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle1">
            GAD-7 Anxiety Screening - Score: {gad7Total} ({getAnxietySeverity(gad7Total)})
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography variant="body2" gutterBottom>
            Over the last 2 weeks, how often have you been bothered by the following problems?
          </Typography>

          <Grid container spacing={2}>
            {gad7Questions.map((question, index) => (
              <Grid item xs={12} key={`gad7-${index}`}>
                <Typography variant="body2" gutterBottom>{question}</Typography>
                <FormControl component="fieldset">
                  <RadioGroup
                    row
                    value={assessmentState.gad7[index]}
                    onChange={(e) => handleGad7Change(index, parseInt(e.target.value))}
                  >
                    {responseOptions.map((option) => (
                      <FormControlLabel
                        key={`gad7-${index}-${option.value}`}
                        value={option.value}
                        control={<Radio size="small" />}
                        label={option.label}
                      />
                    ))}
                  </RadioGroup>
                </FormControl>
                {index < gad7Questions.length - 1 && <Divider sx={{ my: 1 }} />}
              </Grid>
            ))}
          </Grid>

          <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
            <Typography variant="subtitle2">
              Anxiety Severity: {getAnxietySeverity(gad7Total)} (Score: {gad7Total})
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              0-4: Minimal | 5-9: Mild | 10-14: Moderate | 15-21: Severe
            </Typography>
          </Box>
        </AccordionDetails>
      </Accordion>

      {/* Depression Clinical Notes */}
      <Box sx={{ mt: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Depression Clinical Notes
        </Typography>
        <TextField
          label="Depression Clinical Notes"
          multiline
          rows={3}
          fullWidth
          value={assessmentState.depressionNotes}
          onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => handleDepressionNotesChange(e.target.value)}
          variant="outlined"
          placeholder="Enter observations, concerns, or follow-up plans related to depression assessment"
        />
      </Box>

      {/* Anxiety Clinical Notes */}
      <Box sx={{ mt: 3 }}>
        <Typography variant="subtitle1" gutterBottom>
          Anxiety Clinical Notes
        </Typography>
        <TextField
          label="Anxiety Clinical Notes"
          multiline
          rows={3}
          fullWidth
          value={assessmentState.anxietyNotes}
          onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => handleAnxietyNotesChange(e.target.value)}
          variant="outlined"
          placeholder="Enter observations, concerns, or follow-up plans related to anxiety assessment"
        />
      </Box>

      {/* Summary */}
      <Box sx={{ mt: 2, p: 2, bgcolor: 'background.paper', borderRadius: 1 }}>
        <Typography variant="subtitle2" gutterBottom>
          Summary
        </Typography>
        <Typography variant="body2">
          Depression Assessment: {getDepressionSeverity(phq9Total)} (Score: {phq9Total}/27)
        </Typography>
        <Typography variant="body2">
          Anxiety Assessment: {getAnxietySeverity(gad7Total)} (Score: {gad7Total}/21)
        </Typography>
      </Box>
    </Paper>
  );
};

export default MentalHealthAssessment;