import React from 'react';
import { Typography, Box, Grid, Divider } from '@mui/material';

interface CognitiveHealthDisplayProps {
  value: string;
  // New props for individual Mini-Cog fields
  miniCogWordRecallScore?: string;
  miniCogClockDrawingScore?: string;
  miniCogWordsUsed?: string;
  miniCogWordsRecalled?: string;
  miniCogNotes?: string;
}

// Mini-Cog interpretation
const getMiniCogInterpretation = (totalScore: number): string => {
  if (totalScore <= 2) {
    return "Positive screen for cognitive impairment";
  } else {
    return "Negative screen for cognitive impairment";
  }
};

const CognitiveHealthDisplay: React.FC<CognitiveHealthDisplayProps> = ({
  value,
  miniCogWordRecallScore,
  miniCogClockDrawingScore,
  miniCogWordsUsed,
  miniCogWordsRecalled,
  miniCogNotes
}) => {
  // First try to use the individual fields if available
  if (miniCogWordRecallScore !== undefined || miniCogClockDrawingScore !== undefined) {
    const wordRecallScore = miniCogWordRecallScore ? parseInt(miniCogWordRecallScore) : 0;
    const clockDrawingScore = miniCogClockDrawingScore ? parseInt(miniCogClockDrawingScore) : 0;
    const totalScore = wordRecallScore + clockDrawingScore;

    // Parse words used and recalled status
    let selectedWords: string[] = [];
    let recalledStatus: boolean[] = [];

    if (miniCogWordsUsed) {
      try {
        selectedWords = JSON.parse(miniCogWordsUsed);
      } catch {
        // If parsing fails, try comma-separated format
        selectedWords = miniCogWordsUsed.split(',').map(w => w.trim());
      }
    }

    if (miniCogWordsRecalled) {
      try {
        recalledStatus = JSON.parse(miniCogWordsRecalled);
      } catch {
        // If parsing fails, try comma-separated format (1,0,1)
        recalledStatus = miniCogWordsRecalled.split(',').map(r => r.trim() === '1' || r.trim().toLowerCase() === 'true');
      }
    }

    return (
      <Box>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Typography variant="subtitle2" color="primary">Mini-Cog Assessment Results</Typography>
            <Divider sx={{ my: 1 }} />
          </Grid>

          <Grid item xs={12} sm={6}>
            <Typography variant="body2">
              <strong>Total Score:</strong> {totalScore}/5
            </Typography>
            <Typography variant="body2" sx={{ mt: 0.5 }}>
              <strong>Word Recall:</strong> {wordRecallScore}/3
            </Typography>
            <Typography variant="body2" sx={{ mt: 0.5 }}>
              <strong>Clock Drawing:</strong> {clockDrawingScore}/2
            </Typography>

            {/* Assessment date display removed */}

            <Box sx={{ mt: 1, p: 1, bgcolor: 'background.paper', borderRadius: 1, border: `1px solid ${totalScore <= 2 ? '#f44336' : '#4caf50'}` }}>
              <Typography variant="body2" color={totalScore <= 2 ? "error" : "success"} fontWeight="bold">
                {getMiniCogInterpretation(totalScore)}
              </Typography>
            </Box>

            {miniCogNotes && (
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2" fontWeight="medium">
                  <strong>Clinical Notes:</strong>
                </Typography>
                <Typography variant="body2" sx={{ mt: 0.5, whiteSpace: 'pre-wrap' }}>
                  {miniCogNotes}
                </Typography>
              </Box>
            )}
          </Grid>

          {selectedWords.length > 0 && recalledStatus.length > 0 && (
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" gutterBottom>
                <strong>Words Used:</strong>
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                {selectedWords.map((word, i) => (
                  <Typography key={i} variant="body2">
                    {word}: {recalledStatus[i] ? '✓ Recalled' : '✗ Not recalled'}
                  </Typography>
                ))}
              </Box>
            </Grid>
          )}
        </Grid>
      </Box>
    );
  }

  // Fall back to parsing the JSON value if individual fields are not available
  try {
    // Try to parse the JSON value
    const assessmentData = JSON.parse(value);

    // Check if data is in the expected Mini-Cog format
    if (assessmentData.miniCog) {
      const wordRecallScore = assessmentData.miniCog.wordRecallScore ||
                            (assessmentData.miniCog.words?.recalled?.filter(Boolean).length || 0);
      const clockDrawingScore = assessmentData.miniCog.clockDrawing || 0;
      const totalScore = wordRecallScore + clockDrawingScore;

      return (
        <Box>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="subtitle2" color="primary">Mini-Cog Assessment Results</Typography>
              <Divider sx={{ my: 1 }} />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="body2">
                <strong>Total Score:</strong> {totalScore}/5
              </Typography>
              <Typography variant="body2" sx={{ mt: 0.5 }}>
                <strong>Word Recall:</strong> {wordRecallScore}/3
              </Typography>
              <Typography variant="body2" sx={{ mt: 0.5 }}>
                <strong>Clock Drawing:</strong> {clockDrawingScore}/2
              </Typography>

              <Box sx={{ mt: 1, p: 1, bgcolor: 'background.paper', borderRadius: 1, border: `1px solid ${totalScore <= 2 ? '#f44336' : '#4caf50'}` }}>
                <Typography variant="body2" color={totalScore <= 2 ? "error" : "success"} fontWeight="bold">
                  {getMiniCogInterpretation(totalScore)}
                </Typography>
              </Box>
            </Grid>

            {assessmentData.miniCog.words?.selected?.length > 0 && (
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" gutterBottom>
                  <strong>Words Used:</strong>
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
                  {assessmentData.miniCog.words.selected.map((word: string, i: number) => (
                    <Typography key={i} variant="body2">
                      {word}: {assessmentData.miniCog.words.recalled[i] ? '✓ Recalled' : '✗ Not recalled'}
                    </Typography>
                  ))}
                </Box>
              </Grid>
            )}

            {assessmentData.miniCog.notes && (
              <Grid item xs={12}>
                <Typography variant="body2" gutterBottom>
                  <strong>Clinical Notes:</strong>
                </Typography>
                <Typography variant="body2" sx={{ fontStyle: 'italic' }}>
                  {assessmentData.miniCog.notes}
                </Typography>
              </Grid>
            )}

            {assessmentData.additionalNotes && (
              <Grid item xs={12}>
                <Typography variant="body2" gutterBottom>
                  <strong>Additional Notes:</strong>
                </Typography>
                <Typography variant="body2">
                  {assessmentData.additionalNotes}
                </Typography>
              </Grid>
            )}
          </Grid>
        </Box>
      );
    }
  } catch (e) {
    // If parsing fails, display the value as is
  }

  // Fallback for non-JSON values or missing data
  return <Typography variant="body1">{value}</Typography>;
};

export default CognitiveHealthDisplay;