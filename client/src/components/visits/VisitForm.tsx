import React, { useState, useEffect, useContext } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, Link } from 'react-router-dom';
import AuthContext from '../../context/AuthContext';
import {
  Box,
  Button,
  Container,
  Grid,
  LinearProgress,
  Paper,
  TextField,
  Typography,
  Alert,
  AlertTitle,
  CircularProgress,
  Divider,
  Avatar,
  MenuItem,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  FormHelperText,
  List,
  ListItem,
  ListItemText,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  Person as PersonIcon,
  ArrowBack as ArrowBackIcon,
  Favorite as FavoriteIcon,
  Science as ScienceIcon,
  ArrowForward as NextIcon,
  Save as SaveIcon,
  Psychology as PsychologyIcon,
  Vaccines as VaccinesIcon,
  Phone as PhoneIcon,
  MedicalInformation as MedicalIcon,
  HealthAndSafety as HealthIcon,
  Assessment as AssessmentIcon,
  Bed as BedIcon,
  DirectionsRun as FallsIcon,
  Visibility as VisibilityIcon,
  LocalHospital as PainIcon,
  Edit as EditIcon,
  Mood as MoodIcon,
  Medication as MedicationIcon,
  CheckCircle as CheckCircleIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  Info as InfoIcon,
  OpenInNew as JumpToPageIcon,
  Restaurant as RestaurantIcon
} from '@mui/icons-material';
import { getPatientById, updatePatient } from '../../services/patientService';
import { createVisit, updateVisit, getVisitById, getVaccinationData, savePrescriptionsForVisit } from '../../services/visitService';
import { getPrescriptionsByVisitId } from '../../services/prescriptionService';
import CognitiveHealthAssessment from '../visits/CognitiveHealthAssessment';
import DepressionAssessment from '../visits/DepressionAssessment';
import AnxietyAssessment from '../visits/AnxietyAssessment';
import PSQIAssessment from '../sleep/PSQIAssessment';
import BasicSleepInfo from '../sleep/BasicSleepInfo';
import VisitFrailtyAssessment from '../frailty/VisitFrailtyAssessment';
import FallsRiskAssessment from '../falls/FallsRiskAssessment';
import CollapsibleGuidance from '../common/CollapsibleGuidance';
import PrescriptionSection from '../prescriptions/PrescriptionSection';
import BeersCriteriaChecker from '../beers/BeersCriteriaChecker';
import { styled } from '@mui/material/styles';
import TwoPanelReview from '../common/TwoPanelReview';

// Styled components
const StyledPaper = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(4),
  marginBottom: theme.spacing(3),
  borderRadius: theme.shape.borderRadius * 2,
  boxShadow: '0 6px 20px rgba(0, 0, 0, 0.05)',
  overflow: 'hidden',
  border: '1px solid rgba(0, 0, 0, 0.04)'
}));

const FormSection = styled(Box)(({ theme }) => ({
  marginBottom: theme.spacing(4)
}));

const SectionTitle = styled(Typography)(({ theme }) => ({
  marginBottom: theme.spacing(2),
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  color: theme.palette.secondary.main, // Using secondary color (orange) for VisitForm
  fontWeight: 600,
  '& svg': {
    fontSize: '1.2rem'
  }
}));

// Custom Alert Title component removed (unused)

// Enhanced Card styling
const EnhancedCard = styled(Card)(({ theme }) => ({
  borderRadius: theme.shape.borderRadius * 1.5,
  boxShadow: '0 2px 8px rgba(0, 0, 0, 0.04)',
  transition: 'transform 0.2s ease, box-shadow 0.2s ease',
  overflow: 'hidden',
  border: '1px solid rgba(0, 0, 0, 0.04)',
  marginBottom: theme.spacing(3),
  '&:hover': {
    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
    transform: 'translateY(-2px)'
  }
}));

// Enhanced CardContent with better spacing
const EnhancedCardContent = styled(CardContent)(({ theme }) => ({
  padding: theme.spacing(3),
  '&:last-child': {
    paddingBottom: theme.spacing(3)
  }
}));

// Card title styling
const CardTitle = styled(Typography)(({ theme }) => ({
  fontSize: '1.125rem',
  fontWeight: 600,
  color: theme.palette.secondary.main, // Using secondary color (orange) for VisitForm
  marginBottom: theme.spacing(1.5),
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  '& svg': {
    fontSize: '1.25rem'
  }
}));

// Card subtitle styling
const CardSubtitle = styled(Typography)(({ theme }) => ({
  fontSize: '0.875rem',
  color: theme.palette.text.secondary,
  marginBottom: theme.spacing(2.5),
  maxWidth: '90%',
  lineHeight: 1.5
}));

// Add CSS for highlight-field class
const GlobalStyles = styled('style')({
  '@global': {
    '.highlight-field': {
      animation: 'highlight-pulse 2s ease-in-out',
      boxShadow: '0 0 0 2px #f57c00',
      borderRadius: '4px',
      transition: 'all 0.3s ease'
    },
    '@keyframes highlight-pulse': {
      '0%': {
        boxShadow: '0 0 0 0px rgba(245, 124, 0, 0.8)'
      },
      '50%': {
        boxShadow: '0 0 0 4px rgba(245, 124, 0, 0.4)'
      },
      '100%': {
        boxShadow: '0 0 0 0px rgba(245, 124, 0, 0)'
      }
    }
  }
});

// Normal range indicator component
interface NormalRangeProps {
  value: string;
  min: number;
  max: number;
  unit: string;
}

const NormalRangeIndicator = ({ value, min, max, unit }: NormalRangeProps) => {
  const numValue = parseFloat(value);

  if (isNaN(numValue)) return null;

  let color = 'success.main';
  let message = 'Normal';

  if (numValue < min) {
    color = 'warning.main';
    message = 'Low';
  } else if (numValue > max) {
    color = 'error.main';
    message = 'High';
  }

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
      <Box
        component="span"
        sx={{
          width: 8,
          height: 8,
          borderRadius: '50%',
          bgcolor: color,
          mr: 1
        }}
      />
      <Typography variant="caption" color={color}>
        {message} (Normal range: {min}-{max} {unit})
      </Typography>
    </Box>
  );
};

// Custom tab panel component
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`visit-edit-tabpanel-${index}`}
      aria-labelledby={`visit-edit-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

// Tab styling, Header container, and tab accessibility props removed (unused)

interface VisitFormProps {
  isEdit?: boolean;
}

interface Prescription {
  id?: number;
  medication: string;
  dosage: string;
  frequency: string;
  duration: string;
  notes?: string;
  beers_criteria_id?: number;
  beers_criteria_name?: string;
  beers_criteria_category?: string;
  beers_criteria_recommendation?: string;
  beers_criteria_rationale?: string;
  beers_override_reason?: string;
  beers_overridden_at?: string;
  beers_overridden_by?: number;
  overridden_by_username?: string;
}

interface VisitFormData {
  // Visit Information
  patient_id: number;
  visit_date: string;
  visit_reason: string;
  doctor_id: string;

  // Prescriptions
  prescriptions?: Prescription[];

  // Contact Details
  phone: string;
  email: string;
  address: string;
  emergency_contact_name: string;
  emergency_contact_phone: string;
  emergency_contact_relationship: string;
  emergency_contact_updated: string;

  // Vital Signs
  lying_bp_systolic: string;
  lying_bp_diastolic: string;
  sitting_bp_systolic: string;
  sitting_bp_diastolic: string;
  standing_bp_systolic: string;
  standing_bp_diastolic: string;
  lying_heart_rate: string;
  standing_heart_rate: string;
  sitting_heart_rate: string;
  heart_rhythm: string;
  temperature: string;
  respiratory_rate: string;
  pulse_oximetry: string;
  height: string;
  weight: string;
  bmi: string;

  // Additional vital signs properties for form validation
  blood_pressure_sitting_systolic: string;
  blood_pressure_sitting_diastolic: string;
  heart_rate_sitting: string;

  // Lab Results
  // Blood Glucose & Diabetes Markers
  blood_glucose: string;
  hba1c: string;

  // Lipid Profile
  cholesterol_total: string;
  hdl_cholesterol: string;
  ldl_cholesterol: string;
  vldl_cholesterol: string;
  triglycerides: string;

  // Kidney Function Tests
  creatinine: string;
  egfr: string;
  blood_urea_nitrogen: string;
  uric_acid: string;
  urine_albumin_creatinine_ratio: string;

  // Liver Function Tests
  // Liver Enzymes
  alt: string;
  ast: string;
  alp: string;
  ggt: string;
  // Liver Function
  bilirubin_t: string;
  bilirubin_d: string;
  albumin: string;
  total_protein: string;

  // Electrolytes and Minerals
  sodium: string;
  potassium: string;
  calcium: string;
  magnesium: string;

  // Complete Blood Count
  // Red Blood Cell Parameters
  rbc: string;
  hemoglobin: string;
  hematocrit: string;
  platelets: string;
  // Red Blood Cell Indices
  mcv: string;
  mch: string;
  mchc: string;
  rdw: string;
  // White Blood Cell Count & Differential
  wbc: string;
  neutrophils: string;
  lymphocytes: string;
  monocytes: string;
  eosinophils: string;
  basophils: string;

  // Iron Studies
  ferritin: string;
  iron: string;

  // Vitamin Status
  vitamin_b12: string;
  vitamin_d: string;
  folate: string;

  // Thyroid Function Tests
  tsh: string;
  t4: string;
  t3: string;

  // Inflammatory Markers
  crp: string;
  esr: string;

  // Urinalysis
  // Physical Properties
  urine_color: string;
  urine_transparency: string;
  urine_ph: string;
  // Chemical Tests
  urine_protein: string;
  urine_sugar: string;
  // Microscopic Examination
  urine_rbcs: string;
  urine_pus_cells: string;
  urine_epithelial_cells: string;
  urine_crystals: string;
  urine_casts: string;

  // Miscellaneous Screening
  psa: string;
  ca125: string;
  cancer_screening_results: string;

  // Mental Health
  depression_score: string;
  anxiety_score: string;
  depression_notes: string;
  anxiety_notes: string;

  // PHQ-9 Depression Assessment
  phq9_interest_pleasure: string;
  phq9_feeling_down: string;
  phq9_sleep_issues: string;
  phq9_tired: string;
  phq9_appetite: string;
  phq9_feeling_bad: string;
  phq9_concentration: string;
  phq9_moving_speaking: string;
  phq9_thoughts_hurting: string;
  phq9_difficulty_level: string;
  phq9_notes: string;
  depression_screening: string;

  // GAD-7 Anxiety Assessment
  gad7_feeling_nervous: string;
  gad7_stop_worrying: string;
  gad7_worrying_much: string;
  gad7_trouble_relaxing: string;
  gad7_restless: string;
  gad7_annoyed: string;
  gad7_feeling_afraid: string;
  gad7_difficulty_level: string;
  gad7_notes: string;
  anxiety_screening: string;

  // Sleep & Pain
  // Basic Sleep Info
  sleep_patterns: string;
  sleep_initiation_difficulties: string;
  sleep_quality_duration: string;
  sleep_duration: string;
  sleep_disturbances: string;
  sleep_quality: string;

  // PSQI Assessment
  psqi_hours_of_sleep: string;
  psqi_sleep_duration: string;
  psqi_sleep_latency: string;
  psqi_sleep_efficiency: string;
  psqi_sleep_medication: string;
  psqi_total_score: string;
  psqi_assessment_date: string;
  psqi_bedtime: string;
  psqi_minutes_to_fall_asleep: string;
  psqi_wake_up_time: string;
  psqi_notes: string;
  psqi_subjective_sleep_quality: string;
  psqi_sleep_disturbances: string;
  psqi_daytime_dysfunction: string;

  // Pain Assessment
  pain_level: string;
  pain_location: string;
  pain_character: string;
  safe_pain_medications: string;

  // Frailty Assessment
  calf_circumference: string;
  grip_strength: string;
  fall_detection_incidents: string;
  mobility_status: string;
  mobility_aids_used: string;

  // Falls Risk Assessment Tool (FRAT)
  frat_assessment_date: string;
  frat_fall_history: string;
  frat_fall_history_score: string;
  frat_medications: string;
  frat_medications_score: string;
  frat_psychological: string;
  frat_psychological_score: string;
  frat_cognitive: string;
  frat_cognitive_score: string;
  frat_total_score: string;
  frat_risk_level: string;
  frat_notes: string;

  // Health Status
  activity_level: string;
  nutritional_status: string;
  hydration_status: string;
  supplements: string;
  dietary_intake_quality: string;
  exercise_frequency: string;

  // Social & Environmental
  social_interaction_levels: string;
  social_support: string;
  social_support_network: string;
  living_conditions: string;
  living_situation: string;
  environmental_risks: string;
  age_friendly_environment: string;
  transportation_access: string;
  financial_concern: string;
  home_safety_evaluation: string;

  // Sensory
  vision_status: string;
  hearing_status: string;
  use_of_aid_vision: string;
  use_of_aid_hearing: string;

  // Diagnosis & Treatment
  diagnosis: string;
  treatment_plan: string;
  follow_up_instructions: string;
  referrals: string;
  notes: string;

  // Medications
  current_medications: string;
  medication_adherence: string;
  medication_side_effects: string;
  medication_changes: string;
  medication_allergies: string;
  pill_burden: string;

  // Cognitive Assessment (Mini-Cog)
  mini_cog_step1: string;
  mini_cog_step2: string;
  mini_cog_step3: string;
  mini_cog_total: string;
  mini_cog_notes: string;
  mini_cog_word_recall_score: string;
  mini_cog_clock_drawing_score: string;
  mini_cog_words_used: string;
  mini_cog_words_recalled: string;
  cognitive_impairment_score: string;
  cognitive_test_results: string;

  // Vaccinations
  influenza_vaccination_date: string;
  pneumococcal_vaccination_date: string;
  zoster_vaccination_date: string;
  tdap_vaccination_date: string;
  covid19_vaccination_date: string;
  covid19_booster_date: string;
  hepatitis_a_vaccination_date: string;
  hepatitis_b_vaccination_date: string;
  mmr_vaccination_date: string;
  varicella_vaccination_date: string;
  other_vaccinations: string;
}

const VisitForm: React.FC<VisitFormProps> = ({ isEdit = false }) => {
  const { patientId, visitId } = useParams();
  const navigate = useNavigate();
  const { user } = useContext(AuthContext);

  // Define steps
  const steps = [
    { label: 'Visit Information', icon: <MedicalIcon /> },
    { label: 'Contact Details', icon: <PhoneIcon /> },
    { label: 'Medical Information', icon: <MedicalIcon /> },
    { label: 'Vital Signs', icon: <FavoriteIcon /> },
    { label: 'Lab Results', icon: <ScienceIcon /> },
    { label: 'Cognitive Health', icon: <PsychologyIcon /> },
    { label: 'Depression', icon: <MoodIcon /> },
    { label: 'Anxiety', icon: <PsychologyIcon /> },
    { label: 'Sleep & Pain', icon: <BedIcon /> },
    { label: 'Frailty Assessment', icon: <FallsIcon /> },
    { label: 'Vaccinations', icon: <VaccinesIcon /> },
    { label: 'Health Status', icon: <HealthIcon /> },
    { label: 'Diagnosis & Prescriptions', icon: <MedicationIcon /> },
    { label: 'Review & Submit', icon: <AssessmentIcon /> }
  ];

  // State for the current step
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);
  const [patient, setPatient] = useState<any>(null);
  const [originalData, setOriginalData] = useState<VisitFormData | null>(null);

  // State for expanded sections in review tab
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    visitInfo: true,
    contactInfo: true,
    emergencyContact: true,
    medicalInfo: true,
    vitalSigns: true,
    labResults: true,
    mentalHealth: true,
    cognitiveHealth: true,
    depression: true,
    anxiety: true,
    sleepPain: true,
    frailtyAssessment: true,
    vaccinations: true,
    healthStatus: true,
    diagnosisPrescriptions: true,
    additionalNotes: true
  });

  // State for expanded detail sections in review tab removed (unused)

  // State for orthostatic hypotension result
  const [orthostaticResult, setOrthostaticResult] = useState<{
    hasSystolicDrop: boolean;
    hasDiastolicDrop: boolean;
    hasHeartRateIncrease: boolean;
    hasOrthostaticHypotension: boolean;
    hasOrthostaticTachycardia: boolean;
    message: string;
  }>({
    hasSystolicDrop: false,
    hasDiastolicDrop: false,
    hasHeartRateIncrease: false,
    hasOrthostaticHypotension: false,
    hasOrthostaticTachycardia: false,
    message: ''
  });

  // State for form data with default values
  const [formData, setFormData] = useState<VisitFormData>({
    // Visit Information
    patient_id: patientId ? parseInt(patientId) : 0,
    visit_date: new Date().toISOString().split('T')[0],
    visit_reason: '',
    doctor_id: '',

    // Contact Details
    phone: '',
    email: '',
    address: '',
    emergency_contact_name: '',
    emergency_contact_phone: '',
    emergency_contact_relationship: '',
    emergency_contact_updated: 'no',

    // Vital Signs
    lying_bp_systolic: '',
    lying_bp_diastolic: '',
    sitting_bp_systolic: '',
    sitting_bp_diastolic: '',
    standing_bp_systolic: '',
    standing_bp_diastolic: '',
    lying_heart_rate: '',
    standing_heart_rate: '',
    sitting_heart_rate: '',
    heart_rhythm: '',
    temperature: '',
    respiratory_rate: '',
    pulse_oximetry: '',
    height: '',
    weight: '',
    bmi: '',

    // Additional vital signs properties for form validation
    blood_pressure_sitting_systolic: '',
    blood_pressure_sitting_diastolic: '',
    heart_rate_sitting: '',

    // Lab Results
    // Blood Glucose & Diabetes Markers
    blood_glucose: '',
    hba1c: '',

    // Lipid Profile
    cholesterol_total: '',
    hdl_cholesterol: '',
    ldl_cholesterol: '',
    vldl_cholesterol: '',
    triglycerides: '',

    // Kidney Function Tests
    creatinine: '',
    egfr: '',
    blood_urea_nitrogen: '',
    uric_acid: '',
    urine_albumin_creatinine_ratio: '',

    // Liver Function Tests
    // Liver Enzymes
    alt: '',
    ast: '',
    alp: '',
    ggt: '',
    // Liver Function
    bilirubin_t: '',
    bilirubin_d: '',
    albumin: '',
    total_protein: '',

    // Electrolytes and Minerals
    sodium: '',
    potassium: '',
    calcium: '',
    magnesium: '',

    // Complete Blood Count
    // Red Blood Cell Parameters
    rbc: '',
    hemoglobin: '',
    hematocrit: '',
    platelets: '',
    // Red Blood Cell Indices
    mcv: '',
    mch: '',
    mchc: '',
    rdw: '',
    // White Blood Cell Count & Differential
    wbc: '',
    neutrophils: '',
    lymphocytes: '',
    monocytes: '',
    eosinophils: '',
    basophils: '',

    // Iron Studies
    ferritin: '',
    iron: '',

    // Vitamin Status
    vitamin_b12: '',
    vitamin_d: '',
    folate: '',

    // Thyroid Function Tests
    tsh: '',
    t4: '',
    t3: '',

    // Inflammatory Markers
    crp: '',
    esr: '',

    // Urinalysis
    // Physical Properties
    urine_color: '',
    urine_transparency: '',
    urine_ph: '',
    // Chemical Tests
    urine_protein: '',
    urine_sugar: '',
    // Microscopic Examination
    urine_rbcs: '',
    urine_pus_cells: '',
    urine_epithelial_cells: '',
    urine_crystals: '',
    urine_casts: '',

    // Miscellaneous Screening
    psa: '',
    ca125: '',
    cancer_screening_results: '',

    // Mental Health
    depression_score: '',
    anxiety_score: '',
    depression_notes: '',
    anxiety_notes: '',

    // PHQ-9 Depression Assessment
    phq9_interest_pleasure: '',
    phq9_feeling_down: '',
    phq9_sleep_issues: '',
    phq9_tired: '',
    phq9_appetite: '',
    phq9_feeling_bad: '',
    phq9_concentration: '',
    phq9_moving_speaking: '',
    phq9_thoughts_hurting: '',
    phq9_difficulty_level: '',
    phq9_notes: '',
    depression_screening: '',

    // GAD-7 Anxiety Assessment
    gad7_feeling_nervous: '',
    gad7_stop_worrying: '',
    gad7_worrying_much: '',
    gad7_trouble_relaxing: '',
    gad7_restless: '',
    gad7_annoyed: '',
    gad7_feeling_afraid: '',
    gad7_difficulty_level: '',
    gad7_notes: '',
    anxiety_screening: '',

    // Sleep & Pain
    // Basic Sleep Info
    sleep_patterns: '',
    sleep_initiation_difficulties: '',
    sleep_quality_duration: '',
    sleep_duration: '',
    sleep_disturbances: '',
    sleep_quality: '',

    // PSQI Assessment
    psqi_hours_of_sleep: '',
    psqi_sleep_duration: '',
    psqi_sleep_latency: '',
    psqi_sleep_efficiency: '',
    psqi_sleep_medication: '',
    psqi_total_score: '',
    psqi_assessment_date: new Date().toISOString().split('T')[0], // Default to current date
    psqi_bedtime: '',
    psqi_minutes_to_fall_asleep: '',
    psqi_wake_up_time: '',
    psqi_notes: '',
    psqi_subjective_sleep_quality: '',
    psqi_sleep_disturbances: '',
    psqi_daytime_dysfunction: '',

    // Pain Assessment
    pain_level: '',
    pain_location: '',
    pain_character: '',
    safe_pain_medications: '',

    // Frailty Assessment
    calf_circumference: '',
    grip_strength: '',
    fall_detection_incidents: '',
    mobility_status: '',
    mobility_aids_used: '',

    // Falls Risk Assessment Tool (FRAT)
    frat_assessment_date: new Date().toISOString().split('T')[0], // Default to current date
    frat_fall_history: '',
    frat_fall_history_score: '',
    frat_medications: '',
    frat_medications_score: '',
    frat_psychological: '',
    frat_psychological_score: '',
    frat_cognitive: '',
    frat_cognitive_score: '',
    frat_total_score: '',
    frat_risk_level: '',
    frat_notes: '',

    // Health Status
    activity_level: '',
    nutritional_status: '',
    hydration_status: '',
    supplements: '',
    dietary_intake_quality: '',
    exercise_frequency: '',

    // Social & Environmental
    social_interaction_levels: '',
    social_support: '',
    social_support_network: '',
    living_conditions: '',
    living_situation: '',
    environmental_risks: '',
    age_friendly_environment: '',
    transportation_access: '',
    financial_concern: '',
    home_safety_evaluation: '',

    // Sensory
    vision_status: '',
    hearing_status: '',
    use_of_aid_vision: '',
    use_of_aid_hearing: '',

    // Diagnosis & Treatment
    diagnosis: '',
    treatment_plan: '',
    follow_up_instructions: '',
    referrals: '',
    notes: '',

    // Medications
    current_medications: '',
    medication_adherence: '',
    medication_side_effects: '',
    medication_changes: '',
    medication_allergies: '',
    pill_burden: '',
    prescriptions: [],

    // Cognitive Assessment (Mini-Cog)
    mini_cog_step1: '',
    mini_cog_step2: '',
    mini_cog_step3: '',
    mini_cog_total: '',
    mini_cog_notes: '',
    mini_cog_word_recall_score: '',
    mini_cog_clock_drawing_score: '',
    mini_cog_words_used: '',
    mini_cog_words_recalled: '',
    cognitive_impairment_score: '',
    cognitive_test_results: '',

    // Vaccinations
    influenza_vaccination_date: '',
    pneumococcal_vaccination_date: '',
    zoster_vaccination_date: '',
    tdap_vaccination_date: '',
    covid19_vaccination_date: '',
    covid19_booster_date: '',
    hepatitis_a_vaccination_date: '',
    hepatitis_b_vaccination_date: '',
    mmr_vaccination_date: '',
    varicella_vaccination_date: '',
    other_vaccinations: ''
  });

  // Helper function to calculate BMI
  const calculateBMI = (weight: string, height: string): string => {
    if (!weight || !height) return '';

    const weightKg = parseFloat(weight);
    const heightM = parseFloat(height) / 100; // Convert cm to m

    if (isNaN(weightKg) || isNaN(heightM) || heightM === 0) return '';

    const bmi = (weightKg / (heightM * heightM)).toFixed(1);
    return bmi;
  };

  // Helper function to calculate PSQI sleep duration score
  const calculateSleepDurationScore = (hoursOfSleep: string): string => {
    if (!hoursOfSleep) return '';

    const hours = parseFloat(hoursOfSleep);

    if (isNaN(hours)) return '';

    if (hours > 7) return '0';
    if (hours >= 6 && hours <= 7) return '1';
    if (hours >= 5 && hours < 6) return '2';
    return '3'; // Less than 5 hours
  };

  // Helper function to calculate PSQI sleep latency score
  const calculateSleepLatencyScore = (minutesToFallAsleep: string): string => {
    if (!minutesToFallAsleep) return '';

    const minutes = parseFloat(minutesToFallAsleep);

    if (isNaN(minutes)) return '';

    if (minutes <= 15) return '0';
    if (minutes <= 30) return '1';
    if (minutes <= 60) return '2';
    return '3'; // More than 60 minutes
  };

  // Helper function to calculate PSQI sleep efficiency
  const calculateSleepEfficiency = (
    bedtime: string,
    wakeUpTime: string,
    hoursOfSleep: string
  ): string => {
    if (!bedtime || !wakeUpTime || !hoursOfSleep) return '';

    try {
      // Parse bedtime and wake-up time
      const [bedHours, bedMinutes] = bedtime.split(':').map(Number);
      const [wakeHours, wakeMinutes] = wakeUpTime.split(':').map(Number);

      // Calculate total time in bed in hours
      let timeInBedHours;
      if (wakeHours > bedHours || (wakeHours === bedHours && wakeMinutes >= bedMinutes)) {
        // Same day
        timeInBedHours = (wakeHours - bedHours) + (wakeMinutes - bedMinutes) / 60;
      } else {
        // Overnight
        timeInBedHours = (24 - bedHours + wakeHours) + (wakeMinutes - bedMinutes) / 60;
      }

      // Calculate sleep efficiency
      const actualSleepHours = parseFloat(hoursOfSleep);
      if (isNaN(actualSleepHours) || timeInBedHours <= 0) return '';

      const efficiency = (actualSleepHours / timeInBedHours) * 100;

      // Score based on efficiency percentage
      if (efficiency >= 85) return '0';
      if (efficiency >= 75) return '1';
      if (efficiency >= 65) return '2';
      return '3'; // Less than 65%
    } catch (error) {
      console.error('Error calculating sleep efficiency:', error);
      return '';
    }
  };

  // Helper function to calculate PSQI total score
  const calculatePSQITotalScore = (
    subjectiveSleepQuality: string,
    sleepLatency: string,
    sleepDuration: string,
    sleepEfficiency: string,
    sleepDisturbances: string,
    sleepMedication: string,
    daytimeDysfunction: string
  ): string => {
    // Check if all component scores are available
    if (
      !subjectiveSleepQuality ||
      !sleepLatency ||
      !sleepDuration ||
      !sleepEfficiency ||
      !sleepDisturbances ||
      !sleepMedication ||
      !daytimeDysfunction
    ) {
      return '';
    }

    try {
      // Sum all component scores
      const totalScore =
        parseInt(subjectiveSleepQuality) +
        parseInt(sleepLatency) +
        parseInt(sleepDuration) +
        parseInt(sleepEfficiency) +
        parseInt(sleepDisturbances) +
        parseInt(sleepMedication) +
        parseInt(daytimeDysfunction);

      return totalScore.toString();
    } catch (error) {
      console.error('Error calculating PSQI total score:', error);
      return '';
    }
  };

  // Calculate FRAT total score removed (unused)

  // Determine FRAT risk level removed (unused)

  // Helper function to calculate age from date of birth
  const calculateAge = (dateOfBirth: string): number => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();
    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  // Calculate orthostatic hypotension
  const calculateOrthostaticHypotension = (
    lyingSystolic: string,
    standingSystolic: string,
    lyingDiastolic: string,
    standingDiastolic: string,
    lyingHeartRate: string,
    standingHeartRate: string
  ) => {
    // Calculate drops from lying to standing
    const systolicDrop = parseFloat(lyingSystolic) - parseFloat(standingSystolic);
    const diastolicDrop = parseFloat(lyingDiastolic) - parseFloat(standingDiastolic);
    const heartRateIncrease = parseFloat(standingHeartRate) - parseFloat(lyingHeartRate);

    let result = {
      hasSystolicDrop: false,
      hasDiastolicDrop: false,
      hasHeartRateIncrease: false,
      hasOrthostaticHypotension: false,
      hasOrthostaticTachycardia: false,
      message: ''
    };

    // Check for orthostatic hypotension (systolic drop ≥ 20 mmHg or diastolic drop ≥ 10 mmHg)
    if (!isNaN(systolicDrop) && systolicDrop >= 20) {
      result.hasSystolicDrop = true;
      result.hasOrthostaticHypotension = true;
    }

    if (!isNaN(diastolicDrop) && diastolicDrop >= 10) {
      result.hasDiastolicDrop = true;
      result.hasOrthostaticHypotension = true;
    }

    // Check for orthostatic tachycardia (heart rate increase ≥ 30 bpm)
    if (!isNaN(heartRateIncrease) && heartRateIncrease >= 30) {
      result.hasHeartRateIncrease = true;
      result.hasOrthostaticTachycardia = true;
    }

    // Generate message
    if (result.hasOrthostaticHypotension && result.hasOrthostaticTachycardia) {
      result.message = 'Patient shows signs of both orthostatic hypotension and orthostatic tachycardia.';
    } else if (result.hasOrthostaticHypotension) {
      result.message = 'Patient shows signs of orthostatic hypotension.';
    } else if (result.hasOrthostaticTachycardia) {
      result.message = 'Patient shows signs of orthostatic tachycardia.';
    } else if (!isNaN(systolicDrop) && !isNaN(diastolicDrop) && !isNaN(heartRateIncrease)) {
      result.message = 'No significant orthostatic changes detected.';
    }

    return result;
  };

  // Handle step change
  const handleStepChange = (step: number) => {
    setActiveStep(step);
  };

  // Toggle section expansion in review tab
  const toggleSectionExpansion = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Navigate to a specific field in a tab
  const navigateToField = (tabIndex: number, fieldId?: string) => {
    // Navigate to the tab
    setActiveStep(tabIndex);

    // If a field ID is provided, scroll to it and highlight it
    if (fieldId) {
      setTimeout(() => {
        const element = document.getElementById(fieldId);
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' });
          element.classList.add('highlight-field');
          setTimeout(() => {
            element.classList.remove('highlight-field');
          }, 3000);
        }
      }, 100);
    }
  };

  // Toggle detail section expansion in review tab removed (unused)

  // This function is now defined above

  // Helper function to get field navigation info for validation errors
  const getFieldNavigationInfo = (error: string, fieldName: string): { suggestion: string | null, fieldId: string, tabIndex: number } => {
    let fieldId = fieldName;
    let tabIndex = -1;
    let suggestion = null;

    // Determine which tab to navigate to
    if (fieldName.includes('visit_date') || fieldName.includes('visit_reason')) {
      tabIndex = 0; // Visit Information
    } else if (fieldName.includes('phone') || fieldName.includes('email') || fieldName.includes('address')) {
      tabIndex = 1; // Contact Details
    } else if (fieldName.includes('emergency_contact')) {
      tabIndex = 2; // Emergency Contact
    } else if (fieldName.includes('current_medications') || fieldName.includes('medication_')) {
      tabIndex = 3; // Medical Information
    } else if (fieldName.includes('height') || fieldName.includes('weight') ||
              fieldName.includes('bp_') || fieldName.includes('heart_rate') ||
              fieldName.includes('temperature') || fieldName.includes('respiratory')) {
      tabIndex = 4; // Vital Signs
    } else if (fieldName.includes('glucose') || fieldName.includes('cholesterol') ||
              fieldName.includes('hemoglobin') || fieldName.includes('creatinine') ||
              fieldName.includes('egfr') || fieldName.includes('alt') ||
              fieldName.includes('ast') || fieldName.includes('bilirubin')) {
      tabIndex = 5; // Lab Results
    } else if (fieldName.includes('mini_cog')) {
      tabIndex = 6; // Cognitive Health
    } else if (fieldName.includes('depression')) {
      tabIndex = 7; // Depression
    } else if (fieldName.includes('anxiety')) {
      tabIndex = 8; // Anxiety
    } else if (fieldName.includes('sleep') || fieldName.includes('pain')) {
      tabIndex = 9; // Sleep & Pain
    } else if (fieldName.includes('grip') || fieldName.includes('calf') ||
              fieldName.includes('mobility') || fieldName.includes('fall')) {
      tabIndex = 10; // Frailty Assessment
    } else if (fieldName.includes('vision') || fieldName.includes('hearing') ||
              fieldName.includes('dietary') || fieldName.includes('nutritional') ||
              fieldName.includes('hydration') || fieldName.includes('exercise')) {
      tabIndex = 12; // Health Status
    } else if (fieldName.includes('diagnosis') || fieldName.includes('prescription')) {
      tabIndex = 13; // Diagnosis & Prescriptions
    }

    // Get smart validation suggestion
    suggestion = getSmartValidationSuggestion(error, fieldName);

    return { suggestion, fieldId, tabIndex };
  };

  // Smart validation suggestion function for specific error messages
  const getSmartValidationSuggestion = (error: string, fieldName: string): string | null => {
    // Email format suggestions
    if (error.includes('Email address format is invalid')) {
      return "Make sure your email follows the format '<EMAIL>'";
    }

    // Height suggestions
    if (error.includes('Height value seems unusually high')) {
      return "Please verify the height is in centimeters, not inches. For example, 175 cm (5'9\") is a typical adult height.";
    }

    // Weight suggestions
    if (error.includes('Weight value seems unusually high')) {
      return "Please verify the weight is in kilograms, not pounds. For example, 70 kg (154 lbs) is a typical adult weight.";
    }

    // Temperature suggestions
    if (error.includes('Temperature is outside normal human range')) {
      return "Normal body temperature is around 37°C (98.6°F). Please verify the temperature is in Celsius.";
    }

    // Blood pressure suggestions
    if (error.includes('Systolic blood pressure value seems unusually high')) {
      return "Normal systolic blood pressure is typically between 90-140 mmHg. Please verify the reading.";
    }

    if (error.includes('Diastolic blood pressure value seems unusually high')) {
      return "Normal diastolic blood pressure is typically between 60-90 mmHg. Please verify the reading.";
    }

    // Heart rate suggestions
    if (error.includes('Heart rate is outside normal human range')) {
      return "Normal resting heart rate for adults is typically between 60-100 beats per minute.";
    }

    // Required field suggestions
    if (error.includes('is required')) {
      if (fieldName === 'visit_date') {
        return "Visit date is needed for proper record-keeping and tracking patient history.";
      }
      if (fieldName === 'visit_reason') {
        return "Reason for visit helps document the purpose of the patient's visit.";
      }
      if (fieldName === 'phone') {
        return "A contact phone number is needed to reach the patient for follow-ups.";
      }
      if (fieldName === 'temperature' || fieldName === 'sitting_heart_rate' ||
          fieldName === 'respiratory_rate' || fieldName === 'sitting_bp_systolic' ||
          fieldName === 'sitting_bp_diastolic') {
        return "Vital signs are essential for assessing the patient's current health status.";
      }
      if (fieldName === 'height' || fieldName === 'weight') {
        return "Height and weight are needed to calculate BMI and track physical changes.";
      }
      if (fieldName === 'mini_cog_total' || fieldName === 'depression_score' || fieldName === 'anxiety_score') {
        return "Assessment scores are important for tracking mental health status.";
      }
      if (fieldName === 'sleep_quality' || fieldName === 'pain_level') {
        return "Sleep and pain assessments help evaluate quality of life and symptom management.";
      }
      if (fieldName === 'grip_strength' || fieldName === 'calf_circumference') {
        return "Physical measurements help assess frailty risk in older adults.";
      }
      if (fieldName === 'vision_status' || fieldName === 'hearing_status') {
        return "Sensory status information helps identify potential functional limitations.";
      }
      if (fieldName === 'diagnosis') {
        return "Diagnosis is essential for documenting the clinical assessment.";
      }
    }

    // Emergency contact suggestions
    if (error.includes('Both emergency contact name and phone must be provided')) {
      return "If you provide emergency contact information, both name and phone number are required.";
    }

    // Blood pressure validation
    if (error.includes('Both systolic and diastolic blood pressure values must be provided')) {
      return "Blood pressure readings require both systolic (upper) and diastolic (lower) values.";
    }

    // No specific suggestion available
    return null;
  };



  // Format field value for display
  const formatFieldValue = (fieldName: string, value: string): string => {
    // Handle special cases
    if (fieldName === 'doctor_id' && value) {
      // Since we don't have access to the doctors array here,
      // just return the doctor ID value
      return `Doctor ID: ${value}`;
    }

    if (fieldName === 'emergency_contact_updated') {
      return value === 'yes' ? 'Yes' : value === 'no' ? 'No' : value;
    }

    // Blood pressure fields
    if (fieldName.includes('bp_') && value) {
      return `${value} mmHg`;
    }

    // Heart rate fields
    if (fieldName.includes('heart_rate') && value) {
      return `${value} bpm`;
    }

    // Temperature
    if (fieldName === 'temperature' && value) {
      return `${value} °C`;
    }

    // Height
    if (fieldName === 'height' && value) {
      return `${value} cm`;
    }

    // Weight
    if (fieldName === 'weight' && value) {
      return `${value} kg`;
    }

    // BMI
    if (fieldName === 'bmi' && value) {
      return `${value} kg/m²`;
    }

    // Pain level
    if (fieldName === 'pain_level' && value) {
      return `${value}/10`;
    }

    // Blood glucose
    if (fieldName === 'blood_glucose' && value) {
      return `${value} mg/dL`;
    }

    // Cholesterol values
    if ((fieldName.includes('cholesterol') || fieldName.includes('triglycerides')) && value) {
      return `${value} mg/dL`;
    }

    // Respiratory rate
    if (fieldName === 'respiratory_rate' && value) {
      return `${value} breaths/min`;
    }

    // Pulse oximetry
    if (fieldName === 'pulse_oximetry' && value) {
      return `${value}%`;
    }

    // Assessment scores
    if ((fieldName.includes('score') || fieldName.includes('total')) && value) {
      // Determine the appropriate suffix based on the field name
      if (fieldName.includes('phq9') || fieldName.includes('depression')) {
        return `${value}/27`;
      } else if (fieldName.includes('gad7') || fieldName.includes('anxiety')) {
        return `${value}/21`;
      } else if (fieldName.includes('mini_cog') || fieldName.includes('cognitive')) {
        return `${value}/5`;
      } else if (fieldName.includes('psqi')) {
        return `${value}/21`;
      } else if (fieldName.includes('frat')) {
        return `${value}/20`;
      }
    }

    return value;
  };

  // Helper function to normalize values for comparison
  const normalizeValue = (value: any): any => {
    // Handle null or undefined
    if (value === null || value === undefined) {
      return '';
    }

    // Handle numbers and strings that represent numbers
    if (typeof value === 'number' || (typeof value === 'string' && !isNaN(Number(value)))) {
      // Convert to string for consistent comparison
      return String(value);
    }

    // Return other values as is
    return value;
  };

  // Helper function to check if two values are equivalent
  const areValuesEquivalent = (value1: any, value2: any): boolean => {
    // Normalize both values
    const normalizedValue1 = normalizeValue(value1);
    const normalizedValue2 = normalizeValue(value2);

    // Compare normalized values
    return normalizedValue1 === normalizedValue2;
  };

  // Get changed fields for edit mode
  const getChangedFields = (): { field: string; oldValue: any; newValue: any }[] => {
    if (!isEdit || !originalData) {
      // If not in edit mode or no original data, all fields are considered new
      return Object.entries(formData)
        .filter(([key, value]) => value && value !== '')
        .map(([key, value]) => {
          // Special handling for prescriptions array
          if (key === 'prescriptions' && Array.isArray(value)) {
            return {
              field: key,
              oldValue: 'None',
              newValue: `${value.length} prescription(s)`
            };
          }
          return {
            field: key,
            oldValue: '',
            newValue: value
          };
        });
    }

    // Compare original data with current form data
    const changedFields = Object.entries(formData)
      .filter(([key, value]) => {
        // Skip empty values
        if (!value && value !== 0) return false;

        // Skip cognitive_impairment_score as it's synchronized with mini_cog_total
        if (key === 'cognitive_impairment_score') return false;

        // Get original value
        const originalValue = (originalData as any)[key];

        // Special handling for prescriptions array
        if (key === 'prescriptions') {
          if (Array.isArray(value) && Array.isArray(originalValue)) {
            // Compare arrays by length for simplicity
            return value.length !== originalValue.length;
          }
          // If one is array and other isn't, they're different
          return true;
        }

        // Use equivalent comparison instead of strict comparison
        return !areValuesEquivalent(value, originalValue);
      })
      .map(([key, value]) => {
        // Special handling for prescriptions in the display
        if (key === 'prescriptions') {
          const originalPrescriptions = (originalData as any)[key];
          return {
            field: key,
            oldValue: Array.isArray(originalPrescriptions)
              ? `${originalPrescriptions.length} prescription(s)`
              : 'None',
            newValue: Array.isArray(value)
              ? `${value.length} prescription(s)`
              : 'None'
          };
        }

        return {
          field: key,
          oldValue: (originalData as any)[key] || '',
          newValue: value
        };
      });

    return changedFields;
  };

  // Get field label for better readability
  const getFieldLabel = (field: string): string => {
    const fieldLabels: Record<string, string> = {
      // Visit Information
      visit_date: 'Visit Date',
      visit_reason: 'Reason for Visit',
      visit_notes: 'Visit Notes',

      // Contact Details
      phone: 'Phone Number',
      email: 'Email Address',
      address: 'Address',
      city: 'City',
      state: 'State',
      zip_code: 'ZIP Code',
      emergency_contact_name: 'Emergency Contact Name',
      emergency_contact_phone: 'Emergency Contact Phone',
      emergency_contact_relationship: 'Emergency Contact Relationship',
      emergency_contact_updated: 'Emergency Contact Updated Status',

      // Medical Information
      allergies: 'Allergies',
      current_medications: 'Current Medications',
      medication_adherence: 'Medication Adherence',
      medication_side_effects: 'Medication Side Effects',
      medication_allergies: 'Medication Allergies',
      pill_burden: 'Pill Burden',
      medical_conditions: 'Medical Conditions',
      surgical_history: 'Surgical History',
      family_history: 'Family History',

      // Vital Signs
      temperature: 'Temperature',
      heart_rate_sitting: 'Heart Rate (Sitting)',
      heart_rate_standing: 'Heart Rate (Standing)',
      respiratory_rate: 'Respiratory Rate',
      blood_pressure_sitting_systolic: 'Blood Pressure Sitting (Systolic)',
      blood_pressure_sitting_diastolic: 'Blood Pressure Sitting (Diastolic)',
      blood_pressure_standing_systolic: 'Blood Pressure Standing (Systolic)',
      blood_pressure_standing_diastolic: 'Blood Pressure Standing (Diastolic)',
      height: 'Height',
      weight: 'Weight',

      // Lab Results
      blood_glucose: 'Blood Glucose',
      hba1c: 'HbA1c',
      cholesterol_total: 'Total Cholesterol',
      hdl_cholesterol: 'HDL Cholesterol',
      ldl_cholesterol: 'LDL Cholesterol',
      vldl_cholesterol: 'VLDL Cholesterol',
      triglycerides: 'Triglycerides',
      hemoglobin: 'Hemoglobin',
      hematocrit: 'Hematocrit',
      rbc: 'Red Blood Cell Count',
      platelets: 'Platelets',
      mcv: 'Mean Corpuscular Volume',
      creatinine: 'Creatinine',
      egfr: 'eGFR',
      blood_urea_nitrogen: 'Blood Urea Nitrogen',
      uric_acid: 'Uric Acid',
      urine_albumin_creatinine_ratio: 'Urine Albumin-Creatinine Ratio',
      alt: 'ALT',
      ast: 'AST',
      alp: 'Alkaline Phosphatase',
      ggt: 'Gamma-Glutamyl Transferase',
      bilirubin_t: 'Total Bilirubin',
      bilirubin_d: 'Direct Bilirubin',
      albumin: 'Albumin',
      total_protein: 'Total Protein',
      sodium: 'Sodium',
      potassium: 'Potassium',
      calcium: 'Calcium',
      magnesium: 'Magnesium',

      // Cognitive Health
      mini_cog_total: 'Mini-Cog Total Score',
      mini_cog_word_recall_score: 'Word Recall Score',
      mini_cog_clock_drawing_score: 'Clock Drawing Score',
      mini_cog_words_used: 'Words Used',
      mini_cog_words_recalled: 'Words Recalled',
      mini_cog_notes: 'Cognitive Assessment Notes',
      cognitive_impairment_score: 'Cognitive Impairment Score',

      // Depression
      depression_score: 'Depression Score (PHQ-9)',
      phq9_interest_pleasure: 'Little interest or pleasure in doing things',
      phq9_feeling_down: 'Feeling down, depressed, or hopeless',
      phq9_sleep_issues: 'Trouble falling or staying asleep',
      phq9_tired: 'Feeling tired or having little energy',
      phq9_appetite: 'Poor appetite or overeating',
      phq9_feeling_bad: 'Feeling bad about yourself',
      phq9_concentration: 'Trouble concentrating',
      phq9_moving_speaking: 'Moving or speaking slowly',
      phq9_thoughts_hurting: 'Thoughts of hurting yourself',
      phq9_difficulty_level: 'Difficulty Level',
      phq9_notes: 'Depression Assessment Notes',

      // Anxiety
      anxiety_score: 'Anxiety Score (GAD-7)',
      gad7_feeling_nervous: 'Feeling nervous, anxious, or on edge',
      gad7_stop_worrying: 'Not being able to stop worrying',
      gad7_worrying_much: 'Worrying too much about different things',
      gad7_trouble_relaxing: 'Trouble relaxing',
      gad7_restless: 'Being so restless that it is hard to sit still',
      gad7_annoyed: 'Becoming easily annoyed or irritable',
      gad7_feeling_afraid: 'Feeling afraid as if something awful might happen',
      gad7_difficulty_level: 'Difficulty Level',
      gad7_notes: 'Anxiety Assessment Notes',

      // Sleep & Pain
      sleep_quality: 'Sleep Quality',
      sleep_duration: 'Sleep Duration',
      sleep_disturbances: 'Sleep Disturbances',
      psqi_sleep_medication: 'Sleep Medication',
      psqi_total_score: 'PSQI Total Score',
      pain_level: 'Pain Level',
      pain_location: 'Pain Location',
      pain_character: 'Pain Character',
      safe_pain_medications: 'Safe Pain Medications',

      // Frailty Assessment
      grip_strength: 'Grip Strength',
      calf_circumference: 'Calf Circumference',
      fall_detection_incidents: 'Fall Incidents',
      mobility_status: 'Mobility Status',
      mobility_aids_used: 'Mobility Aids Used',
      frat_total_score: 'FRAT Total Score',
      frat_risk_level: 'Falls Risk Level',
      frat_fall_history: 'Fall History',
      frat_medications: 'Medications',
      frat_psychological: 'Psychological',
      frat_cognitive: 'Cognitive',

      // Vaccinations
      influenza_date: 'Influenza Vaccination Date',
      pneumonia_date: 'Pneumonia Vaccination Date',
      shingles_date: 'Shingles Vaccination Date',
      tdap_date: 'Tdap Vaccination Date',
      covid19_date: 'COVID-19 Vaccination Date',

      // Health Status
      vision_status: 'Vision Status',
      hearing_status: 'Hearing Status',
      use_of_aid_vision: 'Use of Vision Aid',
      use_of_aid_hearing: 'Use of Hearing Aid',
      dietary_intake_quality: 'Dietary Intake Quality',
      nutritional_status: 'Nutritional Status',
      hydration_status: 'Hydration Status',
      supplements: 'Supplements',
      exercise_frequency: 'Exercise Frequency',

      // Diagnosis & Prescriptions
      diagnosis: 'Diagnosis',
      prescriptions: 'Prescriptions'
    };

    return fieldLabels[field] || field;
  };



  // Validate data for a specific section removed (unused)

  // Calculate section completion percentage
  const calculateSectionCompletion = (section: string): {
    isComplete: boolean;
    percentage: number;
    missingFields: string[];
    tabIndex: number;
  } => {
    let sectionFields: string[] = [];
    let tabIndex = 0;

    switch (section) {
      case 'Visit Information':
        sectionFields = ['visit_date', 'visit_reason'];
        tabIndex = 0;
        break;
      case 'Contact Information':
        sectionFields = ['phone', 'email', 'address'];
        tabIndex = 1;
        break;
      case 'Emergency Contact':
        sectionFields = ['emergency_contact_name', 'emergency_contact_phone', 'emergency_contact_relationship', 'emergency_contact_updated'];
        tabIndex = 1;
        break;
      case 'Medical Information':
        sectionFields = ['current_medications', 'medication_adherence', 'medication_side_effects', 'medication_allergies', 'pill_burden'];
        tabIndex = 2;
        break;
      case 'Vital Signs':
        sectionFields = ['temperature', 'sitting_heart_rate', 'standing_heart_rate', 'respiratory_rate', 'sitting_bp_systolic', 'sitting_bp_diastolic', 'standing_bp_systolic', 'standing_bp_diastolic', 'height', 'weight', 'bmi', 'pulse_oximetry'];
        tabIndex = 3;
        break;
      case 'Lab Results':
        // Include all the lab result fields from the form
        sectionFields = [
          // Diabetes Markers
          'blood_glucose', 'hba1c',
          // Lipid Profile
          'cholesterol_total', 'hdl_cholesterol', 'ldl_cholesterol', 'vldl_cholesterol', 'triglycerides',
          // Kidney Function Tests
          'creatinine', 'egfr', 'blood_urea_nitrogen', 'uric_acid', 'urine_albumin_creatinine_ratio',
          // Liver Function Tests
          'alt', 'ast', 'alp', 'ggt', 'bilirubin_t', 'bilirubin_d', 'albumin', 'total_protein',
          // Electrolytes and Minerals
          'sodium', 'potassium', 'calcium', 'magnesium',
          // Complete Blood Count
          'rbc', 'hemoglobin', 'hematocrit', 'platelets', 'mcv',
          // Urinalysis
          'urine_color', 'urine_transparency', 'urine_ph', 'urine_protein', 'urine_sugar',
          'urine_rbcs', 'urine_pus_cells', 'urine_epithelial_cells', 'urine_crystals', 'urine_casts',
          // Cancer Screening
          'psa', 'ca125', 'cancer_screening_results'
        ];
        tabIndex = 4;
        break;
      case 'Cognitive Health':
        sectionFields = ['mini_cog_total', 'mini_cog_word_recall_score', 'mini_cog_clock_drawing_score', 'mini_cog_words_used', 'mini_cog_words_recalled', 'mini_cog_notes', 'cognitive_impairment_score'];

        // Ensure mini_cog_total and cognitive_impairment_score are synchronized
        const hasMiniCogTotal = formData.mini_cog_total && formData.mini_cog_total !== '';
        const hasCognitiveImpairmentScore = formData.cognitive_impairment_score && formData.cognitive_impairment_score !== '';

        if (hasMiniCogTotal && !hasCognitiveImpairmentScore) {
          setFormData(prev => ({
            ...prev,
            cognitive_impairment_score: formData.mini_cog_total
          }));
        } else if (!hasMiniCogTotal && hasCognitiveImpairmentScore) {
          setFormData(prev => ({
            ...prev,
            mini_cog_total: formData.cognitive_impairment_score
          }));
        }

        tabIndex = 5;
        break;
      case 'Depression':
        sectionFields = ['depression_score', 'phq9_interest_pleasure', 'phq9_feeling_down', 'phq9_sleep_issues', 'phq9_tired', 'phq9_appetite', 'phq9_feeling_bad', 'phq9_concentration', 'phq9_moving_speaking', 'phq9_thoughts_hurting', 'phq9_difficulty_level', 'phq9_notes', 'depression_screening'];
        tabIndex = 6;
        break;
      case 'Anxiety':
        sectionFields = ['anxiety_score', 'gad7_feeling_nervous', 'gad7_stop_worrying', 'gad7_worrying_much', 'gad7_trouble_relaxing', 'gad7_restless', 'gad7_annoyed', 'gad7_feeling_afraid', 'gad7_difficulty_level', 'gad7_notes', 'anxiety_screening'];
        tabIndex = 7;
        break;
      case 'Sleep & Pain':
        sectionFields = ['sleep_quality', 'sleep_duration', 'sleep_disturbances', 'psqi_total_score', 'psqi_subjective_sleep_quality', 'psqi_sleep_latency', 'psqi_sleep_duration', 'psqi_sleep_efficiency', 'psqi_sleep_disturbances', 'psqi_sleep_medication', 'psqi_daytime_dysfunction', 'pain_level', 'pain_location', 'pain_character', 'safe_pain_medications'];
        tabIndex = 8;
        break;
      case 'Frailty Assessment':
        sectionFields = ['grip_strength', 'calf_circumference', 'fall_detection_incidents', 'mobility_status', 'mobility_aids_used', 'frat_total_score', 'frat_risk_level', 'frat_fall_history', 'frat_medications', 'frat_psychological', 'frat_cognitive'];
        tabIndex = 9;
        break;
      case 'Vaccinations':
        sectionFields = ['influenza_vaccination_date', 'pneumococcal_vaccination_date', 'zoster_vaccination_date', 'tdap_vaccination_date', 'covid19_vaccination_date', 'covid19_booster_date', 'hepatitis_a_vaccination_date', 'hepatitis_b_vaccination_date', 'mmr_vaccination_date', 'varicella_vaccination_date', 'other_vaccinations'];
        tabIndex = 10;
        break;
      case 'Health Status':
        sectionFields = [
          // Activity & Nutrition
          'activity_level',
          'exercise_frequency',

          // Nutrition & Hydration
          'nutritional_status',
          'dietary_intake_quality',
          'hydration_status',
          'supplements',

          // Sensory Assessment
          'vision_status',
          'use_of_aid_vision',
          'hearing_status',
          'use_of_aid_hearing',

          // Social & Environmental Factors
          'social_interaction_levels',
          'social_support',
          'social_support_network',
          'living_situation',
          'living_conditions',
          'environmental_risks',
          'age_friendly_environment',
          'transportation_access',
          'financial_concern',
          'home_safety_evaluation'
        ];
        tabIndex = 11;
        break;
      case 'Diagnosis & Prescriptions':
        sectionFields = ['diagnosis', 'treatment_plan', 'follow_up_instructions', 'referrals', 'prescriptions'];
        tabIndex = 12;
        break;
      default:
        sectionFields = [];
        tabIndex = 0;
    }

    // If no fields defined for this section, return 0%
    if (sectionFields.length === 0) {
      return { isComplete: false, percentage: 0, missingFields: [], tabIndex };
    }

    const missingFields: string[] = [];
    let filledCount = 0;

    // Check each field in the section - using the same simple approach as PatientForm
    for (const field of sectionFields) {
      // Special handling for prescriptions array
      if (field === 'prescriptions') {
        if (formData.prescriptions && formData.prescriptions.length > 0) {
          filledCount++;
        } else {
          missingFields.push(getFieldLabel(field));
        }
        continue;
      }

      const value = (formData as any)[field];

      // Simple check: if value exists and is not empty, count as filled
      if (value && value !== '') {
        filledCount++;
      } else {
        missingFields.push(getFieldLabel(field));
      }
    }

    const percentage = Math.round((filledCount / sectionFields.length) * 100);
    const isComplete = percentage === 100;

    return { isComplete, percentage, missingFields, tabIndex };
  };

  // Helper function to delete prescriptions that match BEERS criteria but don't have an override
  const deleteUnresolvedBeersPrescriptions = (alerts: any[]) => {
    if (!formData.prescriptions) return;

    // Get the medications that match BEERS criteria but don't have an override
    const medicationsToDelete = alerts.filter(alert => {
      const prescription = formData.prescriptions?.find(
        p => p.medication.toLowerCase() === alert.medication.toLowerCase()
      );
      return prescription && !prescription.beers_override_reason;
    }).map(alert => alert.medication.toLowerCase());

    // Filter out prescriptions that match BEERS criteria but don't have an override
    const updatedPrescriptions = formData.prescriptions.filter(prescription => {
      return !medicationsToDelete.includes(prescription.medication.toLowerCase());
    });

    // Update the form data with the filtered prescriptions
    setFormData(prev => ({
      ...prev,
      prescriptions: updatedPrescriptions
    }));

    // Show a message about the deleted prescriptions
    if (medicationsToDelete.length > 0) {
      alert(`The following medications were removed because they match BEERS criteria and don't have an override reason: ${medicationsToDelete.join(', ')}`);
    }
  };

  // Handle next step
  const handleNext = () => {
    // If we're on the Prescriptions tab (index 12), check for unresolved BEERS criteria alerts
    if (activeStep === 12 && formData.prescriptions && formData.prescriptions.length > 0 && patient?.date_of_birth) {
      // Check if patient is 65 or older
      const patientAge = calculateAge(patient.date_of_birth);
      if (patientAge >= 65) {
        // Check if there are any prescriptions with BEERS criteria alerts but no override
        const { checkMedications } = require('../../services/beersCriteriaService');

        // Get all medications
        const medications = formData.prescriptions.map(p => p.medication);

        // Check for BEERS criteria alerts
        checkMedications(medications, parseInt(patientId || '0'), isEdit && visitId ? parseInt(visitId) : undefined)
          .then((alerts: any[]) => {
            if (alerts.length > 0) {
              // Find prescriptions that have BEERS alerts but no override
              const unresolvedAlerts = alerts.filter((alert: any) => {
                const prescription = formData.prescriptions?.find(
                  p => p.medication.toLowerCase() === alert.medication.toLowerCase()
                );
                return prescription && !prescription.beers_override_reason;
              });

              if (unresolvedAlerts.length > 0) {
                // Show warning about unresolved BEERS alerts
                const medicationNames = unresolvedAlerts.map((alert: any) => alert.medication).join(", ");
                // Show alert about the unresolved BEERS criteria
                const confirmDelete = window.confirm(
                  `The following medications have BEERS criteria alerts but no override reasons: ${medicationNames}. \n\n` +
                  `You must either provide an override reason or delete these medications before proceeding. \n\n` +
                  `Click OK to automatically delete these medications, or Cancel to go back and provide override reasons.`
                );

                if (confirmDelete) {
                  // User chose to delete the medications
                  deleteUnresolvedBeersPrescriptions(alerts);
                  // Now proceed to next step
                  setActiveStep((prevStep) => Math.min(prevStep + 1, steps.length - 1));
                } else {
                  // User chose to go back and provide override reasons
                  return;
                }
              }
            }

            // If no unresolved alerts or user chose to proceed, go to next step
            setActiveStep((prevStep) => Math.min(prevStep + 1, steps.length - 1));
          })
          .catch((error: any) => {
            console.error('Error checking BEERS criteria:', error);
            // Proceed to next step even if check fails
            setActiveStep((prevStep) => Math.min(prevStep + 1, steps.length - 1));
          });
      } else {
        // Patient is under 65, no need to check BEERS criteria
        setActiveStep((prevStep) => Math.min(prevStep + 1, steps.length - 1));
      }
    } else {
      // Not on Prescriptions tab, proceed normally
      setActiveStep((prevStep) => Math.min(prevStep + 1, steps.length - 1));
    }
  };

  // Handle previous step
  const handleBack = () => {
    setActiveStep((prevStep) => Math.max(prevStep - 1, 0));
  };

  // Log form data changes during development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Form data changed:', formData);
    }
  }, [formData]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Always fetch patient data
        if (patientId) {
          const patientData = await getPatientById(parseInt(patientId));
          console.log('Fetched patient data:', patientData);
          console.log('Contact details from patient data:', {
            phone: patientData.phone,
            email: patientData.email,
            address: patientData.address,
            emergency_contact_name: patientData.emergency_contact_name,
            emergency_contact_phone: patientData.emergency_contact_phone,
            emergency_contact_relationship: patientData.emergency_contact_relationship
          });
          setPatient(patientData);

          // Initialize updated form data with patient information
          // Always copy contact information from patient record to ensure it's stored in the visit
          let updatedFormData = {
            ...formData,
            doctor_id: patientData.doctor_id ? patientData.doctor_id.toString() : '',
            // Contact details - always copy from patient record to visit record
            phone: patientData.phone || '',
            email: patientData.email || '',
            address: patientData.address || '',
            emergency_contact_name: patientData.emergency_contact_name || '',
            emergency_contact_phone: patientData.emergency_contact_phone || '',
            emergency_contact_relationship: patientData.emergency_contact_relationship || '',
            emergency_contact_updated: 'no',
            // Vital signs - height and weight
            height: patientData.height ? patientData.height.toString() : '',
            weight: patientData.weight ? patientData.weight.toString() : ''
            // Vaccination dates will be initialized from the most recent visit
          };

          // For new visits, get vaccination data from either the most recent visit or patient record
          // For edit visits, we'll use the existing visit data which is loaded later
          if (!isEdit) {
            try {
              // Use the new getVaccinationData function to get vaccination data from either
              // the most recent visit or the patient record if no visits exist
              const vaccinationData = await getVaccinationData(parseInt(patientId));

              if (Object.keys(vaccinationData).length > 0) {
                console.log('Found vaccination data:', vaccinationData);

                // Define vaccination fields to copy
                const vaccinationFields = [
                  'hepatitis_b_vaccination_date',
                  'mmr_vaccination_date',
                  'varicella_vaccination_date',
                  'influenza_vaccination_date',
                  'pneumococcal_vaccination_date',
                  'zoster_vaccination_date',
                  'tdap_vaccination_date',
                  'covid19_vaccination_date',
                  'covid19_booster_date',
                  'hepatitis_a_vaccination_date',
                  'other_vaccinations'
                ];

                // Create a typed updatedFormData for dynamic property access
                const typedFormData = updatedFormData as Record<string, any>;

                // Copy vaccination fields to form data
                vaccinationFields.forEach(field => {
                  if (field in vaccinationData && vaccinationData[field]) {
                    typedFormData[field] = vaccinationData[field].toString();
                    console.log(`Copied ${field}: ${vaccinationData[field]} to form data`);
                  }
                });

                // Update the original updatedFormData with the modified values
                updatedFormData = typedFormData as typeof updatedFormData;

                console.log('Updated vaccination data:');
                vaccinationFields.forEach(field => {
                  console.log(`${field}: ${updatedFormData[field as keyof typeof updatedFormData] || 'not set'}`);
                });
              } else {
                console.log('No vaccination data found for this patient');
              }
            } catch (err) {
              console.error('Error fetching vaccination data:', err);
              // Continue with patient data if there's an error fetching vaccination data
            }
          }

          // Calculate BMI if both height and weight are available
          if (updatedFormData.height && updatedFormData.weight) {
            updatedFormData.bmi = calculateBMI(updatedFormData.weight, updatedFormData.height);
          }

          // Ensure vaccination fields are initialized
          const vaccinationFields = [
            'hepatitis_b_vaccination_date',
            'mmr_vaccination_date',
            'varicella_vaccination_date',
            'influenza_vaccination_date',
            'pneumococcal_vaccination_date',
            'zoster_vaccination_date',
            'tdap_vaccination_date',
            'covid19_vaccination_date',
            'covid19_booster_date',
            'hepatitis_a_vaccination_date',
            'other_vaccinations'
          ];

          console.log('Checking vaccination fields in form data before setting state:');
          vaccinationFields.forEach(field => {
            console.log(`${field}: ${updatedFormData[field as keyof typeof updatedFormData] || 'not set'}`);
          });

          console.log('Updated form data with contact details and vaccination history:', updatedFormData);
          setFormData(updatedFormData);
        }

        // If editing an existing visit, fetch the visit data and prescriptions
        if (isEdit && visitId) {
          const visitData = await getVisitById(parseInt(visitId));

          // Convert numeric values to strings for form fields
          const processedVisitData: any = {};
          Object.entries(visitData).forEach(([key, value]) => {
            if (value !== null && value !== undefined) {
              processedVisitData[key] = value.toString();
            }
          });

          // Keep the original visit_date when editing a visit
          // We'll track edit time separately in the database with updated_at

          // Fetch prescriptions for this visit
          try {
            const prescriptions = await getPrescriptionsByVisitId(parseInt(visitId));
            console.log(`Fetched ${prescriptions.length} prescriptions for visit ${visitId}:`, prescriptions);

            // Add prescriptions to the form data
            processedVisitData.prescriptions = prescriptions.map(p => ({
              id: p.prescription_id,
              medication: p.medication,
              dosage: p.dosage,
              frequency: p.frequency,
              duration: p.duration,
              notes: p.notes,
              beers_criteria_id: p.beers_criteria_id,
              beers_criteria_name: p.beers_criteria_name,
              beers_criteria_category: p.beers_criteria_category,
              beers_criteria_recommendation: p.beers_criteria_recommendation,
              beers_criteria_rationale: p.beers_criteria_rationale,
              beers_override_reason: p.beers_override_reason,
              beers_overridden_at: p.beers_overridden_at,
              beers_overridden_by: p.beers_overridden_by,
              overridden_by_username: p.overridden_by_username
            }));
          } catch (err) {
            console.error(`Error fetching prescriptions for visit ${visitId}:`, err);
            // Continue without prescriptions
          }

          // Create a copy of the default form data to ensure all fields exist
          const defaultFormData = { ...formData };

          // Merge the processed visit data with the default form data
          const mergedData = { ...defaultFormData, ...processedVisitData };

          // Ensure emergency_contact_updated is initialized
          if (!mergedData.emergency_contact_updated) {
            mergedData.emergency_contact_updated = 'no';
          }

          // Ensure mini_cog_total and cognitive_impairment_score are synchronized
          if (mergedData.mini_cog_total && !mergedData.cognitive_impairment_score) {
            mergedData.cognitive_impairment_score = mergedData.mini_cog_total;
          } else if (!mergedData.mini_cog_total && mergedData.cognitive_impairment_score) {
            mergedData.mini_cog_total = mergedData.cognitive_impairment_score;
          } else if (mergedData.mini_cog_total && mergedData.cognitive_impairment_score &&
                    mergedData.mini_cog_total !== mergedData.cognitive_impairment_score) {
            // If both exist but are different, use mini_cog_total as the source of truth
            mergedData.cognitive_impairment_score = mergedData.mini_cog_total;
          }

          // Store the original data for comparison in the review section
          // This ensures originalData has the same structure as formData
          setOriginalData(mergedData);

          // Update the form data
          setFormData(mergedData);
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isEdit, visitId, patientId]);

  // Handle input changes
  const onChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    const updatedFormData = {
      ...formData,
      [name]: value,
    };

    // Auto-calculate BMI when height or weight changes
    if (name === 'height' || name === 'weight') {
      updatedFormData.bmi = calculateBMI(updatedFormData.weight, updatedFormData.height);
    }

    // Auto-calculate PSQI sleep duration score
    if (name === 'psqi_hours_of_sleep') {
      updatedFormData.psqi_sleep_duration = calculateSleepDurationScore(updatedFormData.psqi_hours_of_sleep);
    }

    // Auto-calculate PSQI sleep latency score
    if (name === 'psqi_minutes_to_fall_asleep') {
      updatedFormData.psqi_sleep_latency = calculateSleepLatencyScore(updatedFormData.psqi_minutes_to_fall_asleep);
    }

    // Auto-calculate PSQI sleep efficiency score
    const psqiEfficiencyFields = ['psqi_bedtime', 'psqi_wake_up_time', 'psqi_hours_of_sleep'];
    if (psqiEfficiencyFields.includes(name)) {
      if (updatedFormData.psqi_bedtime && updatedFormData.psqi_wake_up_time && updatedFormData.psqi_hours_of_sleep) {
        updatedFormData.psqi_sleep_efficiency = calculateSleepEfficiency(
          updatedFormData.psqi_bedtime,
          updatedFormData.psqi_wake_up_time,
          updatedFormData.psqi_hours_of_sleep
        );
      }
    }

    // Auto-calculate PSQI total score
    const psqiScoreFields = [
      'psqi_subjective_sleep_quality',
      'psqi_sleep_latency',
      'psqi_sleep_duration',
      'psqi_sleep_efficiency',
      'psqi_sleep_disturbances',
      'psqi_sleep_medication',
      'psqi_daytime_dysfunction'
    ];

    if (psqiScoreFields.includes(name) || psqiEfficiencyFields.includes(name)) {
      if (
        updatedFormData.psqi_subjective_sleep_quality &&
        updatedFormData.psqi_sleep_latency &&
        updatedFormData.psqi_sleep_duration &&
        updatedFormData.psqi_sleep_efficiency &&
        updatedFormData.psqi_sleep_disturbances &&
        updatedFormData.psqi_sleep_medication &&
        updatedFormData.psqi_daytime_dysfunction
      ) {
        updatedFormData.psqi_total_score = calculatePSQITotalScore(
          updatedFormData.psqi_subjective_sleep_quality,
          updatedFormData.psqi_sleep_latency,
          updatedFormData.psqi_sleep_duration,
          updatedFormData.psqi_sleep_efficiency,
          updatedFormData.psqi_sleep_disturbances,
          updatedFormData.psqi_sleep_medication,
          updatedFormData.psqi_daytime_dysfunction
        );
      }
    }

    // Check for orthostatic hypotension when BP or HR values change
    const orthostaticFields = [
      'lying_bp_systolic', 'lying_bp_diastolic', 'lying_heart_rate',
      'standing_bp_systolic', 'standing_bp_diastolic', 'standing_heart_rate'
    ];

    if (orthostaticFields.includes(name)) {
      // Only calculate if all required values are present
      if (
        updatedFormData.lying_bp_systolic &&
        updatedFormData.standing_bp_systolic &&
        updatedFormData.lying_bp_diastolic &&
        updatedFormData.standing_bp_diastolic &&
        updatedFormData.lying_heart_rate &&
        updatedFormData.standing_heart_rate
      ) {
        const result = calculateOrthostaticHypotension(
          updatedFormData.lying_bp_systolic,
          updatedFormData.standing_bp_systolic,
          updatedFormData.lying_bp_diastolic,
          updatedFormData.standing_bp_diastolic,
          updatedFormData.lying_heart_rate,
          updatedFormData.standing_heart_rate
        );

        setOrthostaticResult(result);
      }
    }

    setFormData(updatedFormData);
  };

  // Handle direct value changes removed (unused)

  // Wrapper for depression assessment onChange with individual field values
  const handleDepressionChange = (value: string) => {
    setFormData({
      ...formData,
      depression_score: value,
    });
  };

  // Enhanced depression assessment handler with individual field values
  const handleDepressionFieldsChange = (fields: {
    phq9_interest_pleasure: string;
    phq9_feeling_down: string;
    phq9_sleep_issues: string;
    phq9_tired: string;
    phq9_appetite: string;
    phq9_feeling_bad: string;
    phq9_concentration: string;
    phq9_moving_speaking: string;
    phq9_thoughts_hurting: string;
    phq9_difficulty_level: string;
    phq9_notes: string;
    depression_score: string;
    depression_screening: string;
  }) => {
    setFormData({
      ...formData,
      ...fields
    });
  };

  // Wrapper for anxiety assessment onChange with individual field values
  const handleAnxietyChange = (value: string) => {
    setFormData({
      ...formData,
      anxiety_score: value,
    });
  };

  // Enhanced anxiety assessment handler with individual field values
  const handleAnxietyFieldsChange = (fields: {
    gad7_feeling_nervous: string;
    gad7_stop_worrying: string;
    gad7_worrying_much: string;
    gad7_trouble_relaxing: string;
    gad7_restless: string;
    gad7_annoyed: string;
    gad7_feeling_afraid: string;
    gad7_difficulty_level: string;
    gad7_notes: string;
    anxiety_score: string;
    anxiety_screening: string;
  }) => {
    setFormData({
      ...formData,
      ...fields
    });
  };

  // Wrapper for cognitive assessment onChange
  const handleCognitiveChange = (value: string) => {
    setFormData({
      ...formData,
      mini_cog_total: value,
      cognitive_impairment_score: value, // Set both fields to the same value
    });
    console.log('Setting both mini_cog_total and cognitive_impairment_score to:', value);
  };

  // Enhanced cognitive assessment handler with individual field values
  const handleCognitiveFieldsChange = (fields: {
    mini_cog_word_recall_score: string;
    mini_cog_clock_drawing_score: string;
    mini_cog_words_used: string;
    mini_cog_words_recalled: string;
    mini_cog_notes: string;
    cognitive_impairment_score: string;
    mini_cog_total: string;
  }) => {
    // Calculate the total score from the component scores
    const wordRecallScore = parseInt(fields.mini_cog_word_recall_score) || 0;
    const clockDrawingScore = parseInt(fields.mini_cog_clock_drawing_score) || 0;
    const totalScore = wordRecallScore + clockDrawingScore;

    // Set both mini_cog_total and cognitive_impairment_score to the same value
    // This ensures both fields have the total score for validation and display
    const updatedFields = {
      ...fields,
      mini_cog_total: totalScore.toString(),
      cognitive_impairment_score: totalScore.toString()
    };

    setFormData({
      ...formData,
      ...updatedFields
    });

    console.log('Updated cognitive fields:', updatedFields);
    console.log('Mini-Cog Total Score:', totalScore.toString());
    console.log('Cognitive Impairment Score:', totalScore.toString());
  };

  // Handle select changes removed (unused)

  // Handle radio button changes
  const handleRadioChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const updatedFormData = {
      ...formData,
      [name]: value,
    };

    // Auto-update individual FRAT scores based on standard FRAT scoring
    if (name === 'frat_fall_history') {
      // Fall history scoring: 1=2, 2=4, 3=6, 4=8
      const fallScores = ['0', '2', '4', '6', '8'];
      updatedFormData.frat_fall_history_score = fallScores[parseInt(value)] || '0';
    } else if (name === 'frat_medications') {
      // Medications scoring: 1=1, 2=2, 3=3, 4=4
      const medScores = ['0', '1', '2', '3', '4'];
      updatedFormData.frat_medications_score = medScores[parseInt(value)] || '0';
    } else if (name === 'frat_psychological') {
      // Psychological scoring: 1=1, 2=2, 3=3, 4=4
      const psychScores = ['0', '1', '2', '3', '4'];
      updatedFormData.frat_psychological_score = psychScores[parseInt(value)] || '0';
    } else if (name === 'frat_cognitive') {
      // Cognitive scoring: 1=1, 2=2, 3=3, 4=4
      const cogScores = ['0', '1', '2', '3', '4'];
      updatedFormData.frat_cognitive_score = cogScores[parseInt(value)] || '0';
    }

    // Auto-calculate total FRAT score when any component changes
    if (
      name === 'frat_fall_history' ||
      name === 'frat_medications' ||
      name === 'frat_psychological' ||
      name === 'frat_cognitive'
    ) {
      // Update the total score if all components are filled
      if (
        updatedFormData.frat_fall_history &&
        updatedFormData.frat_medications &&
        updatedFormData.frat_psychological &&
        updatedFormData.frat_cognitive
      ) {
        // Calculate total score using the individual component scores
        const fallScore = parseInt(updatedFormData.frat_fall_history_score) || 0;
        const medScore = parseInt(updatedFormData.frat_medications_score) || 0;
        const psychScore = parseInt(updatedFormData.frat_psychological_score) || 0;
        const cogScore = parseInt(updatedFormData.frat_cognitive_score) || 0;

        const totalScore = fallScore + medScore + psychScore + cogScore;
        updatedFormData.frat_total_score = totalScore.toString();

        // Determine risk level based on total score
        const score = parseInt(totalScore.toString());
        if (score >= 5 && score <= 11) {
          updatedFormData.frat_risk_level = 'Low Risk';
        } else if (score >= 12 && score <= 15) {
          updatedFormData.frat_risk_level = 'Medium Risk';
        } else if (score >= 16) {
          updatedFormData.frat_risk_level = 'High Risk';
        } else {
          updatedFormData.frat_risk_level = 'Minimal Risk';
        }
      }
    }

    setFormData(updatedFormData);
  };

  // Handle MUI select changes
  const handleMuiSelectChange = (e: any) => {
    const { name, value } = e.target;
    const updatedFormData = {
      ...formData,
      [name]: value,
    };

    // Auto-calculate PSQI total score for select fields
    const psqiScoreFields = [
      'psqi_subjective_sleep_quality',
      'psqi_sleep_disturbances',
      'psqi_sleep_medication',
      'psqi_daytime_dysfunction'
    ];

    if (psqiScoreFields.includes(name)) {
      console.log(`PSQI select field changed: ${name} = ${value}`);

      // Make sure all required fields are present
      if (
        updatedFormData.psqi_subjective_sleep_quality &&
        updatedFormData.psqi_sleep_latency &&
        updatedFormData.psqi_sleep_duration &&
        updatedFormData.psqi_sleep_efficiency &&
        updatedFormData.psqi_sleep_disturbances &&
        updatedFormData.psqi_sleep_medication &&
        updatedFormData.psqi_daytime_dysfunction
      ) {
        updatedFormData.psqi_total_score = calculatePSQITotalScore(
          updatedFormData.psqi_subjective_sleep_quality,
          updatedFormData.psqi_sleep_latency,
          updatedFormData.psqi_sleep_duration,
          updatedFormData.psqi_sleep_efficiency,
          updatedFormData.psqi_sleep_disturbances,
          updatedFormData.psqi_sleep_medication,
          updatedFormData.psqi_daytime_dysfunction
        );
        console.log('Calculated total PSQI score from select change:', updatedFormData.psqi_total_score);
      } else {
        console.log('Not all PSQI fields are filled in yet:', {
          subjective_sleep_quality: updatedFormData.psqi_subjective_sleep_quality,
          sleep_latency: updatedFormData.psqi_sleep_latency,
          sleep_duration: updatedFormData.psqi_sleep_duration,
          sleep_efficiency: updatedFormData.psqi_sleep_efficiency,
          sleep_disturbances: updatedFormData.psqi_sleep_disturbances,
          sleep_medication: updatedFormData.psqi_sleep_medication,
          daytime_dysfunction: updatedFormData.psqi_daytime_dysfunction
        });
      }
    }

    setFormData(updatedFormData);
  };

  // Wrapper for BasicSleepInfo onChange
  const handleSleepInfoChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    onChange(e);
  };

  // Wrapper for PSQIAssessment onChange with special handling for PSQI fields
  const handlePSQIChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    const updatedFormData = {
      ...formData,
      [name]: value,
    };

    // Auto-calculate PSQI sleep duration score
    if (name === 'psqi_hours_of_sleep') {
      updatedFormData.psqi_sleep_duration = calculateSleepDurationScore(updatedFormData.psqi_hours_of_sleep);
    }

    // Auto-calculate PSQI sleep latency score
    if (name === 'psqi_minutes_to_fall_asleep') {
      updatedFormData.psqi_sleep_latency = calculateSleepLatencyScore(updatedFormData.psqi_minutes_to_fall_asleep);
      console.log('Calculated sleep latency:', updatedFormData.psqi_sleep_latency);
    }

    // Auto-calculate PSQI sleep efficiency score
    const psqiEfficiencyFields = ['psqi_bedtime', 'psqi_wake_up_time', 'psqi_hours_of_sleep'];
    if (psqiEfficiencyFields.includes(name)) {
      if (updatedFormData.psqi_bedtime && updatedFormData.psqi_wake_up_time && updatedFormData.psqi_hours_of_sleep) {
        updatedFormData.psqi_sleep_efficiency = calculateSleepEfficiency(
          updatedFormData.psqi_bedtime,
          updatedFormData.psqi_wake_up_time,
          updatedFormData.psqi_hours_of_sleep
        );
        console.log('Calculated sleep efficiency:', updatedFormData.psqi_sleep_efficiency);
      }
    }

    // Auto-calculate PSQI total score
    const psqiScoreFields = [
      'psqi_subjective_sleep_quality',
      'psqi_sleep_latency',
      'psqi_sleep_duration',
      'psqi_sleep_efficiency',
      'psqi_sleep_disturbances',
      'psqi_sleep_medication',
      'psqi_daytime_dysfunction'
    ];

    if (psqiScoreFields.includes(name) || psqiEfficiencyFields.includes(name)) {
      if (
        updatedFormData.psqi_subjective_sleep_quality &&
        updatedFormData.psqi_sleep_latency &&
        updatedFormData.psqi_sleep_duration &&
        updatedFormData.psqi_sleep_efficiency &&
        updatedFormData.psqi_sleep_disturbances &&
        updatedFormData.psqi_sleep_medication &&
        updatedFormData.psqi_daytime_dysfunction
      ) {
        updatedFormData.psqi_total_score = calculatePSQITotalScore(
          updatedFormData.psqi_subjective_sleep_quality,
          updatedFormData.psqi_sleep_latency,
          updatedFormData.psqi_sleep_duration,
          updatedFormData.psqi_sleep_efficiency,
          updatedFormData.psqi_sleep_disturbances,
          updatedFormData.psqi_sleep_medication,
          updatedFormData.psqi_daytime_dysfunction
        );
        console.log('Calculated total PSQI score:', updatedFormData.psqi_total_score);
      }
    }

    setFormData(updatedFormData);
  };

  // Wrapper for FrailtyAssessment onChange
  const handleFrailtyChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    onChange(e);
  };

  // Wrapper for FrailtyAssessment handleRadioChange
  const handleFrailtyRadioChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  // Validate form data before submission
  const validateVisitData = (): string[] => {
    const errors: string[] = [];

    // Required fields validation
    if (!formData.visit_date) errors.push('Visit date is required');
    if (!formData.visit_reason) errors.push('Reason for visit is required');
    // Phone number is now optional

    // Email validation if provided
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.push('Email address format is invalid');
    }

    // Emergency Contact validation - only validate if at least one field is provided
    // Skip validation if both fields are empty
    if (formData.emergency_contact_name || formData.emergency_contact_phone) {
      // If one field is provided but the other is missing
      if (!formData.emergency_contact_name || !formData.emergency_contact_phone) {
        errors.push('Both emergency contact name and phone must be provided');
      }

      // Emergency contact updated is only required if both name and phone are provided
      if (formData.emergency_contact_name && formData.emergency_contact_phone && !formData.emergency_contact_updated) {
        errors.push('Emergency Contact Updated is required when emergency contact information is provided');
      }
    }

    // Validate numeric fields
    if (formData.height && isNaN(parseFloat(formData.height))) {
      errors.push('Height must be a valid number');
    }

    if (formData.weight && isNaN(parseFloat(formData.weight))) {
      errors.push('Weight must be a valid number');
    }

    if (formData.temperature && isNaN(parseFloat(formData.temperature))) {
      errors.push('Temperature must be a valid number');
    }

    // Validate blood pressure values if provided
    if ((formData.sitting_bp_systolic && !formData.sitting_bp_diastolic) ||
        (!formData.sitting_bp_systolic && formData.sitting_bp_diastolic)) {
      errors.push('Both systolic and diastolic blood pressure values must be provided');
    }

    // Validate heart rate if provided
    if (formData.sitting_heart_rate && isNaN(parseFloat(formData.sitting_heart_rate))) {
      errors.push('Heart rate must be a valid number');
    }

    // Check for reasonable values
    if (formData.height && parseFloat(formData.height) > 250) {
      errors.push('Height value seems unusually high (> 250 cm)');
    }

    if (formData.weight && parseFloat(formData.weight) > 300) {
      errors.push('Weight value seems unusually high (> 300 kg)');
    }

    if (formData.temperature &&
        (parseFloat(formData.temperature) < 35 || parseFloat(formData.temperature) > 42)) {
      errors.push('Temperature is outside normal human range (35-42°C)');
    }

    if (formData.sitting_bp_systolic && parseFloat(formData.sitting_bp_systolic) > 250) {
      errors.push('Systolic blood pressure value seems unusually high (> 250 mmHg)');
    }

    if (formData.sitting_bp_diastolic && parseFloat(formData.sitting_bp_diastolic) > 150) {
      errors.push('Diastolic blood pressure value seems unusually high (> 150 mmHg)');
    }

    if (formData.sitting_heart_rate &&
        (parseFloat(formData.sitting_heart_rate) < 30 || parseFloat(formData.sitting_heart_rate) > 220)) {
      errors.push('Heart rate is outside normal human range (30-220 bpm)');
    }

    // These fields are now optional
    // We only require the basic visit information (date, reason, phone)
    // All other fields are optional to allow for partial form completion

    return errors;
  };

  // Handle form submission
  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);
    setSuccess(null);

    // Validate form data before submission
    const validationErrors = validateVisitData();
    if (validationErrors.length > 0) {
      // If we're not on the review tab, navigate to it to show errors
      if (activeStep !== 13) {
        setActiveStep(13);
      }
      setError('Please fix the validation errors before submitting.');
      setSubmitting(false);
      return;
    }

    // Store prescriptions for saving after visit is created/updated
    const prescriptionsToSave = formData.prescriptions || [];

    // Log prescriptions before saving
    console.log('DETAILED DEBUG: Prescriptions before saving:', {
      count: prescriptionsToSave.length,
      prescriptions: JSON.stringify(prescriptionsToSave, null, 2)
    });

    // Check for unresolved BEERS criteria alerts
    if (formData.prescriptions && formData.prescriptions.length > 0 && patient?.date_of_birth) {
      // Check if patient is 65 or older
      const patientAge = calculateAge(patient.date_of_birth);
      if (patientAge >= 65) {
        try {
          // Import the service dynamically to avoid circular dependencies
          const { checkMedications } = await import('../../services/beersCriteriaService');

          // Get all medications
          const medications = formData.prescriptions.map(p => p.medication);

          // Check for BEERS criteria alerts
          const alerts: any[] = await checkMedications(medications, parseInt(patientId || '0'));

          if (alerts.length > 0) {
            // Find prescriptions that have BEERS alerts but no override
            const unresolvedAlerts = alerts.filter((alert: any) => {
              const prescription = formData.prescriptions?.find(
                p => p.medication.toLowerCase() === alert.medication.toLowerCase()
              );
              return prescription && !prescription.beers_override_reason;
            });

            if (unresolvedAlerts.length > 0) {
              // Show warning about unresolved BEERS alerts
              const medicationNames = unresolvedAlerts.map((alert: any) => alert.medication).join(", ");
              // Show alert about the unresolved BEERS criteria
              const confirmDelete = window.confirm(
                `The following medications have BEERS criteria alerts but no override reasons: ${medicationNames}. \n\n` +
                `You must either provide an override reason or delete these medications before submitting. \n\n` +
                `Click OK to automatically delete these medications, or Cancel to go back and provide override reasons.`
              );

              if (confirmDelete) {
                // User chose to delete the medications
                deleteUnresolvedBeersPrescriptions(alerts);
                // Continue with submission
              } else {
                // User chose to go back and provide override reasons
                setActiveStep(12); // Go to prescriptions tab
                setSubmitting(false);
                return;
              }
            }
          }
        } catch (error: any) {
          console.error('Error checking BEERS criteria:', error);
          // Continue with submission even if BEERS check fails
        }
      }
    }

    try {
      // Helper function to convert string values to numbers where needed
      const convertFormDataToVisitData = (data: VisitFormData) => {
        const result: any = { ...data };

        // Convert numeric fields from strings to numbers
        const numericFields = [
          'patient_id', 'doctor_id',
          'lying_bp_systolic', 'lying_bp_diastolic',
          'sitting_bp_systolic', 'sitting_bp_diastolic',
          'standing_bp_systolic', 'standing_bp_diastolic',
          'lying_heart_rate', 'standing_heart_rate', 'sitting_heart_rate',
          'temperature', 'respiratory_rate', 'pulse_oximetry',
          'height', 'weight', 'bmi',
          // Blood Glucose & Diabetes Markers
          'blood_glucose', 'hba1c',
          // Lipid Profile
          'cholesterol_total', 'hdl_cholesterol', 'ldl_cholesterol',
          'vldl_cholesterol', 'triglycerides',
          // Kidney Function Tests
          'creatinine', 'egfr', 'blood_urea_nitrogen', 'uric_acid',
          'urine_albumin_creatinine_ratio',
          // Liver Function Tests
          'alt', 'ast', 'alp', 'ggt', 'bilirubin_t', 'bilirubin_d', 'albumin', 'total_protein',
          // Electrolytes and Minerals
          'sodium', 'potassium', 'calcium', 'magnesium',
          // Complete Blood Count - Red Blood Cell Parameters
          'rbc', 'hemoglobin', 'hematocrit', 'platelets',
          // Complete Blood Count - Red Blood Cell Indices
          'mcv', 'mch', 'mchc', 'rdw',
          // Complete Blood Count - White Blood Cell Count & Differential
          'wbc', 'neutrophils', 'lymphocytes', 'monocytes', 'eosinophils', 'basophils',
          // Iron Studies
          'ferritin', 'iron',
          // Vitamin Status
          'vitamin_b12', 'vitamin_d', 'folate',
          // Thyroid Function Tests
          'tsh', 't4', 't3',
          // Inflammatory Markers
          'crp', 'esr',
          // Urinalysis - only numeric fields
          'urine_ph',
          // Miscellaneous Screening
          'psa', 'ca125',
          // PSQI Assessment
          'psqi_hours_of_sleep', 'psqi_minutes_to_fall_asleep', 'psqi_sleep_duration',
          'psqi_sleep_latency', 'psqi_sleep_efficiency', 'psqi_total_score',
          'psqi_subjective_sleep_quality', 'psqi_sleep_disturbances',
          'psqi_sleep_medication', 'psqi_daytime_dysfunction',
          // Other
          'pain_level', 'depression_score', 'anxiety_score',
          'calf_circumference', 'grip_strength', 'frailty_score',
          // FRAT Assessment
          'frat_fall_history', 'frat_fall_history_score',
          'frat_medications', 'frat_medications_score',
          'frat_psychological', 'frat_psychological_score',
          'frat_cognitive', 'frat_cognitive_score',
          'frat_total_score',
          // Mini-Cog Assessment
          'mini_cog_word_recall_score', 'mini_cog_clock_drawing_score', 'cognitive_impairment_score',
          // PHQ-9 Assessment
          'phq9_interest_pleasure', 'phq9_feeling_down', 'phq9_sleep_issues', 'phq9_tired',
          'phq9_appetite', 'phq9_feeling_bad', 'phq9_concentration', 'phq9_moving_speaking',
          'phq9_thoughts_hurting', 'phq9_difficulty_level',
          // GAD-7 Assessment
          'gad7_feeling_nervous', 'gad7_stop_worrying', 'gad7_worrying_much', 'gad7_trouble_relaxing',
          'gad7_restless', 'gad7_annoyed', 'gad7_feeling_afraid', 'gad7_difficulty_level'
        ];

        // Convert emergency_contact_updated to boolean
        if (result.emergency_contact_updated === 'yes') {
          result.emergency_contact_updated = true;
        } else if (result.emergency_contact_updated === 'no') {
          result.emergency_contact_updated = false;
        } else {
          result.emergency_contact_updated = null;
        }

        numericFields.forEach(field => {
          if (result[field] && result[field] !== '') {
            result[field] = parseFloat(result[field]);
          } else if (result[field] === '') {
            result[field] = null;
          }
        });

        return result;
      };

      // Prepare data for submission
      const visitData = {
        ...convertFormDataToVisitData(formData),
        patient_id: parseInt(patientId || '0'),
        doctor_id: formData.doctor_id ? parseInt(formData.doctor_id) : undefined,
        // Contact Information - explicitly include these fields
        phone: formData.phone,
        email: formData.email,
        address: formData.address,
        emergency_contact_name: formData.emergency_contact_name,
        emergency_contact_phone: formData.emergency_contact_phone,
        emergency_contact_relationship: formData.emergency_contact_relationship,
        // Include individual assessment fields
        // Mini-Cog
        mini_cog_word_recall_score: formData.mini_cog_word_recall_score,
        mini_cog_clock_drawing_score: formData.mini_cog_clock_drawing_score,
        mini_cog_words_used: formData.mini_cog_words_used,
        mini_cog_words_recalled: formData.mini_cog_words_recalled,
        mini_cog_notes: formData.mini_cog_notes,
        cognitive_impairment_score: formData.cognitive_impairment_score,
        cognitive_test_results: formData.cognitive_test_results,
        // PHQ-9
        phq9_interest_pleasure: formData.phq9_interest_pleasure,
        phq9_feeling_down: formData.phq9_feeling_down,
        phq9_sleep_issues: formData.phq9_sleep_issues,
        phq9_tired: formData.phq9_tired,
        phq9_appetite: formData.phq9_appetite,
        phq9_feeling_bad: formData.phq9_feeling_bad,
        phq9_concentration: formData.phq9_concentration,
        phq9_moving_speaking: formData.phq9_moving_speaking,
        phq9_thoughts_hurting: formData.phq9_thoughts_hurting,
        phq9_difficulty_level: formData.phq9_difficulty_level,
        phq9_notes: formData.phq9_notes,
        depression_screening: formData.depression_screening,
        // GAD-7
        gad7_feeling_nervous: formData.gad7_feeling_nervous,
        gad7_stop_worrying: formData.gad7_stop_worrying,
        gad7_worrying_much: formData.gad7_worrying_much,
        gad7_trouble_relaxing: formData.gad7_trouble_relaxing,
        gad7_restless: formData.gad7_restless,
        gad7_annoyed: formData.gad7_annoyed,
        gad7_feeling_afraid: formData.gad7_feeling_afraid,
        gad7_difficulty_level: formData.gad7_difficulty_level,
        gad7_notes: formData.gad7_notes,
        anxiety_screening: formData.anxiety_screening,

        // PSQI Assessment - Explicitly convert to numbers where needed
        psqi_subjective_sleep_quality: formData.psqi_subjective_sleep_quality ? parseInt(formData.psqi_subjective_sleep_quality) : null,
        psqi_sleep_latency: formData.psqi_sleep_latency ? parseInt(formData.psqi_sleep_latency) : null,
        psqi_sleep_duration: formData.psqi_sleep_duration ? parseInt(formData.psqi_sleep_duration) : null,
        psqi_sleep_efficiency: formData.psqi_sleep_efficiency ? parseInt(formData.psqi_sleep_efficiency) : null,
        psqi_sleep_disturbances: formData.psqi_sleep_disturbances ? parseInt(formData.psqi_sleep_disturbances) : null,
        psqi_sleep_medication: formData.psqi_sleep_medication ? parseInt(formData.psqi_sleep_medication) : null,
        psqi_daytime_dysfunction: formData.psqi_daytime_dysfunction ? parseInt(formData.psqi_daytime_dysfunction) : null,
        psqi_total_score: formData.psqi_total_score ? parseInt(formData.psqi_total_score) : null,
        psqi_assessment_date: formData.psqi_assessment_date,
        psqi_bedtime: formData.psqi_bedtime,
        psqi_minutes_to_fall_asleep: formData.psqi_minutes_to_fall_asleep,
        psqi_wake_up_time: formData.psqi_wake_up_time,
        psqi_hours_of_sleep: formData.psqi_hours_of_sleep,
        psqi_notes: formData.psqi_notes,

        // Pain Assessment
        pain_character: formData.pain_character,
        safe_pain_medications: formData.safe_pain_medications,

        // Falls Risk Assessment Tool (FRAT)
        frat_assessment_date: formData.frat_assessment_date,
        frat_fall_history: formData.frat_fall_history,
        frat_fall_history_score: formData.frat_fall_history_score,
        frat_medications: formData.frat_medications,
        frat_medications_score: formData.frat_medications_score,
        frat_psychological: formData.frat_psychological,
        frat_psychological_score: formData.frat_psychological_score,
        frat_cognitive: formData.frat_cognitive,
        frat_cognitive_score: formData.frat_cognitive_score,
        frat_total_score: formData.frat_total_score,
        frat_risk_level: formData.frat_risk_level,
        frat_notes: formData.frat_notes,

        // Prescriptions
        prescriptions: formData.prescriptions
      };

      // Log CBC and Iron Studies fields in development mode
      if (process.env.NODE_ENV === 'development') {
        // Log contact information fields for debugging
        console.log('Contact information fields being sent to server:', {
          phone: visitData.phone,
          email: visitData.email,
          address: visitData.address,
          emergency_contact_name: visitData.emergency_contact_name,
          emergency_contact_phone: visitData.emergency_contact_phone,
          emergency_contact_relationship: visitData.emergency_contact_relationship
        });

        console.log('CBC and Iron Studies fields being sent to server:', {
          // Red Blood Cell Parameters
          rbc: visitData.rbc,
          hemoglobin: visitData.hemoglobin,
          hematocrit: visitData.hematocrit,
          platelets: visitData.platelets,
          // Red Blood Cell Indices
          mcv: visitData.mcv,
          mch: visitData.mch,
          mchc: visitData.mchc,
          rdw: visitData.rdw,
          // White Blood Cell Count & Differential
          wbc: visitData.wbc,
          neutrophils: visitData.neutrophils,
          lymphocytes: visitData.lymphocytes,
          monocytes: visitData.monocytes,
          eosinophils: visitData.eosinophils,
          basophils: visitData.basophils,
          // Iron Studies
          ferritin: visitData.ferritin,
          iron: visitData.iron
        });

        // Log PSQI fields for debugging
        console.log('PSQI fields being sent to server:', {
          psqi_subjective_sleep_quality: visitData.psqi_subjective_sleep_quality,
          psqi_sleep_latency: visitData.psqi_sleep_latency,
          psqi_sleep_duration: visitData.psqi_sleep_duration,
          psqi_sleep_efficiency: visitData.psqi_sleep_efficiency,
          psqi_sleep_disturbances: visitData.psqi_sleep_disturbances,
          psqi_sleep_medication: visitData.psqi_sleep_medication,
          psqi_daytime_dysfunction: visitData.psqi_daytime_dysfunction,
          psqi_total_score: visitData.psqi_total_score,
          psqi_assessment_date: visitData.psqi_assessment_date,
          psqi_bedtime: visitData.psqi_bedtime,
          psqi_minutes_to_fall_asleep: visitData.psqi_minutes_to_fall_asleep,
          psqi_wake_up_time: visitData.psqi_wake_up_time,
          psqi_hours_of_sleep: visitData.psqi_hours_of_sleep,
          psqi_notes: visitData.psqi_notes
        });

        // Log FRAT fields for debugging
        console.log('FRAT fields being sent to server:', {
          frat_assessment_date: visitData.frat_assessment_date,
          frat_fall_history: visitData.frat_fall_history,
          frat_fall_history_score: visitData.frat_fall_history_score,
          frat_medications: visitData.frat_medications,
          frat_medications_score: visitData.frat_medications_score,
          frat_psychological: visitData.frat_psychological,
          frat_psychological_score: visitData.frat_psychological_score,
          frat_cognitive: visitData.frat_cognitive,
          frat_cognitive_score: visitData.frat_cognitive_score,
          frat_total_score: visitData.frat_total_score,
          frat_risk_level: visitData.frat_risk_level,
          frat_notes: visitData.frat_notes
        });

        // Log prescriptions for debugging
        console.log('Prescriptions to save after visit creation/update:', {
          count: prescriptionsToSave.length,
          prescriptions: prescriptionsToSave
        });

        // Log BEERS overrides for debugging
        const overriddenPrescriptions = prescriptionsToSave.filter((p: Prescription) => p.beers_override_reason) || [];
        console.log('BEERS overrides to save:', {
          count: overriddenPrescriptions.length,
          overrides: overriddenPrescriptions.map((p: Prescription) => ({
            medication: p.medication,
            beers_criteria_id: p.beers_criteria_id,
            beers_override_reason: p.beers_override_reason,
            beers_overridden_at: p.beers_overridden_at
          }))
        });

        // Remove prescriptions from visitData as they'll be saved separately
        delete visitData.prescriptions;
      }

      let res;
      if (isEdit && visitId) {
        // Update existing visit
        res = await updateVisit(parseInt(visitId), visitData);

        // Check if height or weight has changed and update patient record if needed
        if (patient && (
          (patient.height !== parseFloat(formData.height) && formData.height) ||
          (patient.weight !== parseFloat(formData.weight) && formData.weight)
        )) {
          console.log('Height or weight has changed, updating patient record');

          // Prepare patient update data
          const patientUpdateData: any = {
            patient_id: patient.patient_id
          };

          // Only include fields that have changed
          if (patient.height !== parseFloat(formData.height) && formData.height) {
            patientUpdateData.height = parseFloat(formData.height);
          }

          if (patient.weight !== parseFloat(formData.weight) && formData.weight) {
            patientUpdateData.weight = parseFloat(formData.weight);
          }

          // Calculate and update BMI if both height and weight are available
          if (formData.height && formData.weight) {
            patientUpdateData.bmi = parseFloat(calculateBMI(formData.weight, formData.height));
          }

          try {
            // Update patient record with new height/weight
            await updatePatient(patient.patient_id, patientUpdateData);
            console.log('Patient record updated with new height/weight');
          } catch (err) {
            console.error('Error updating patient record:', err);
            // Don't show error to user as the visit was updated successfully
          }
        }

        // Save prescriptions for the updated visit if any exist
        if (prescriptionsToSave.length > 0) {
          try {
            // Make sure patientId is a number
            const patientIdNum = patientId ? parseInt(patientId) : undefined;

            console.log('DETAILED DEBUG: About to save prescriptions for updated visit:', {
              visitId: parseInt(visitId),
              patientId: patientIdNum,
              prescriptionsCount: prescriptionsToSave.length,
              prescriptions: JSON.stringify(prescriptionsToSave, null, 2)
            });

            const savedPrescriptions = await savePrescriptionsForVisit(parseInt(visitId), prescriptionsToSave, patientIdNum);
            console.log('Prescriptions saved successfully for updated visit:', savedPrescriptions);
          } catch (err) {
            console.error('Error saving prescriptions for updated visit:', err);
            // Don't show error to user as the visit was updated successfully
          }
        }

        setSuccess('Visit updated successfully');
      } else {
        // Create new visit
        res = await createVisit(visitData);

        // Check if height or weight has changed and update patient record if needed
        if (patient && (
          (patient.height !== parseFloat(formData.height) && formData.height) ||
          (patient.weight !== parseFloat(formData.weight) && formData.weight)
        )) {
          console.log('Height or weight has changed, updating patient record');

          // Prepare patient update data
          const patientUpdateData: any = {
            patient_id: patient.patient_id
          };

          // Only include fields that have changed
          if (patient.height !== parseFloat(formData.height) && formData.height) {
            patientUpdateData.height = parseFloat(formData.height);
          }

          if (patient.weight !== parseFloat(formData.weight) && formData.weight) {
            patientUpdateData.weight = parseFloat(formData.weight);
          }

          // Calculate and update BMI if both height and weight are available
          if (formData.height && formData.weight) {
            patientUpdateData.bmi = parseFloat(calculateBMI(formData.weight, formData.height));
          }

          try {
            // Update patient record with new height/weight
            await updatePatient(patient.patient_id, patientUpdateData);
            console.log('Patient record updated with new height/weight');
          } catch (err) {
            console.error('Error updating patient record:', err);
            // Don't show error to user as the visit was created successfully
          }
        }

        // Save prescriptions for the new visit if any exist
        if (prescriptionsToSave.length > 0 && res && res.visit_id) {
          try {
            // Make sure patientId is a number
            const patientIdNum = patientId ? parseInt(patientId) : undefined;

            console.log('DETAILED DEBUG: About to save prescriptions for new visit:', {
              visitId: res.visit_id,
              patientId: patientIdNum,
              prescriptionsCount: prescriptionsToSave.length,
              prescriptions: JSON.stringify(prescriptionsToSave, null, 2)
            });

            const savedPrescriptions = await savePrescriptionsForVisit(res.visit_id, prescriptionsToSave, patientIdNum);
            console.log('Prescriptions saved successfully for new visit:', savedPrescriptions);
          } catch (err) {
            console.error('Error saving prescriptions for new visit:', err);
            // Don't show error to user as the visit was created successfully
          }
        }

        setSuccess('Visit created successfully');
      }

      // Wait 1.5 seconds before redirecting to give user time to see success message
      setTimeout(() => {
        navigate(`/patients/${patientId}/visits`);
      }, 1500);
    } catch (err: any) {
      // Handle API error responses
      let errorMessage = `Failed to ${isEdit ? 'update' : 'create'} visit. Please try again.`;

      console.error('Detailed error information:', err);

      if (err.response) {
        console.error('Response status:', err.response.status);
        console.error('Response data:', err.response.data);

        if (err.response.status === 400) {
          errorMessage = 'Invalid visit data. Please check your entries.';
        } else if (err.response.status === 401) {
          errorMessage = `You are not authorized to ${isEdit ? 'update' : 'create'} visits.`;
        } else if (err.response.status === 404) {
          errorMessage = 'Patient or visit not found.';
        } else if (err.response.status === 500) {
          // For server errors, include the server's error message if available
          if (err.response.data && err.response.data.error) {
            errorMessage = `Server error: ${err.response.data.error}`;
          } else {
            errorMessage = 'Server error occurred. Please try again later.';
          }
        }
      } else if (err.message) {
        // If we have a direct error message, include it
        errorMessage = `Error: ${err.message}`;
      }

      setError(errorMessage);
      console.error('Error saving visit:', err);

      // Log the form data that caused the error (excluding sensitive information)
      const sanitizedFormData = { ...formData };
      delete sanitizedFormData.prescriptions; // Remove potentially large data
      console.error('Form data that caused the error:', sanitizedFormData);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <Container>
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      </Container>
    );
  }

  if (!patient) {
    return (
      <Container maxWidth="lg">
        <StyledPaper>
          <Typography variant="h5" color="error">
            Patient not found
          </Typography>
          <Button
            component={Link}
            to="/patients"
            variant="contained"
            sx={{ mt: 2 }}
          >
            Back to Patients
          </Button>
        </StyledPaper>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <GlobalStyles />
      <StyledPaper>
        {/* Header */}
        <Box sx={{
          p: 3,
          background: 'linear-gradient(to right, rgba(217, 123, 58, 0.05), rgba(217, 123, 58, 0.01))', // Using secondary color (orange) for VisitForm
          borderRadius: '16px 16px 0 0',
          display: 'flex',
          flexDirection: 'column',
          gap: 1,
          mb: 4
        }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
            <Box>
              <Button
                component={Link}
                to={`/patients/${patientId}/visits`}
                variant="outlined"
                sx={{
                  mb: 2,
                  borderRadius: '8px',
                  textTransform: 'none',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.05)'
                }}
                startIcon={<ArrowBackIcon />}
              >
                Back to Visits
              </Button>

              <Typography
                variant="h4"
                component="h1"
                gutterBottom
                sx={{
                  fontWeight: 600,
                  color: 'secondary.main', // Using secondary color (orange) for VisitForm
                  fontSize: { xs: '1.75rem', md: '2.25rem' }
                }}
              >
                {isEdit ? 'Edit Visit' : 'Add New Visit'}
              </Typography>
            </Box>
          </Box>

          {/* Patient Info */}
          <EnhancedCard>
            <EnhancedCardContent>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Avatar sx={{ bgcolor: 'secondary.main', mr: 2 }}> {/* Using secondary color (orange) for VisitForm */}
                  <PersonIcon />
                </Avatar>
                <Box>
                  <Typography variant="h6">
                    {patient.first_name} {patient.last_name}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    ID: {patient.unique_id} | DOB: {new Date(patient.date_of_birth).toLocaleDateString()} | Gender: {patient.gender} | Blood Type: {patient.blood_type || 'Not recorded'}
                  </Typography>
                </Box>
              </Box>
            </EnhancedCardContent>
          </EnhancedCard>

          <Typography variant="subtitle1" color="text.secondary" sx={{ mt: 1 }}>
            {isEdit ? 'Update visit information' : 'Enter visit information'}
          </Typography>
        </Box>

        {/* Alert Messages */}
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert severity="success" sx={{ mb: 3 }}>
            {success}
          </Alert>
        )}

        {/* Step Progress */}
        <Box sx={{ width: '100%', mb: 4 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="body2" color="text.secondary">
              Step {activeStep + 1} of {steps.length}
            </Typography>
            <Typography variant="body2" color="secondary">
              {steps[activeStep].label}
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={(activeStep / (steps.length - 1)) * 100}
            sx={{ height: 8, borderRadius: 4, bgcolor: 'rgba(217, 123, 58, 0.1)' }}
            color="secondary"
          />
        </Box>

        {/* Step Navigation */}
        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 4 }}>
          {steps.map((step, index) => (
            <Button
              key={index}
              variant={activeStep === index ? 'contained' : 'outlined'}
              color="secondary" // Using secondary color (orange) for VisitForm
              size="small"
              startIcon={step.icon}
              onClick={() => handleStepChange(index)}
              sx={{
                borderRadius: 2,
                mb: 1,
                textTransform: 'none',
                fontSize: '0.85rem'
              }}
            >
              {step.label}
            </Button>
          ))}
        </Box>

        {/* Form */}
        <form onSubmit={onSubmit}>
          {/* Visit Information Tab */}
          <TabPanel value={activeStep} index={0}>
            <FormSection>
              <EnhancedCard>
                <EnhancedCardContent>
                  <CardTitle>
                    <MedicalIcon /> Visit Details
                  </CardTitle>
                  <CardSubtitle>
                    Enter the date and reason for this visit
                  </CardSubtitle>
                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        required
                        id="visit_date"
                        name="visit_date"
                        label="Visit Date"
                        type="date"
                        value={formData.visit_date}
                        onChange={onChange}
                        variant="outlined"
                        InputLabelProps={{ shrink: true }}
                        disabled={isEdit} // Disable the field when editing an existing visit
                        helperText={isEdit ? "Visit date cannot be changed after creation" : ""}
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        required
                        id="visit_reason"
                        name="visit_reason"
                        label="Reason for Visit"
                        value={formData.visit_reason}
                        onChange={onChange}
                        variant="outlined"
                        multiline
                        rows={3}
                      />
                    </Grid>
                  </Grid>
                </EnhancedCardContent>
              </EnhancedCard>
            </FormSection>
          </TabPanel>

          {/* Contact Details Tab */}
          <TabPanel value={activeStep} index={1}>
            <FormSection>
              <EnhancedCard>
                <EnhancedCardContent>
                  <CardTitle>
                    <PhoneIcon /> Contact Information
                  </CardTitle>
                  <CardSubtitle>
                    Please verify and update the patient's contact information if needed.
                  </CardSubtitle>
                  {/* Current Contact Info */}
                  <Box sx={{ mb: 2, p: 1, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                    <Typography variant="subtitle2" component="div">
                      Current Contact Information:
                    </Typography>
                    <Typography variant="body2" component="div">
                      Phone: {patient?.phone || 'Not set'}
                    </Typography>
                    <Typography variant="body2" component="div">
                      Email: {patient?.email || 'Not set'}
                    </Typography>
                    <Typography variant="body2" component="div">
                      Address: {patient?.address || 'Not set'}
                    </Typography>
                  </Box>
                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        id="phone"
                        name="phone"
                        label="Phone Number"
                        value={formData.phone}
                        onChange={onChange}
                        variant="outlined"
                        helperText="Optional: Update if needed"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        id="email"
                        name="email"
                        label="Email Address"
                        type="email"
                        value={formData.email}
                        onChange={onChange}
                        variant="outlined"
                      />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        id="address"
                        name="address"
                        label="Address"
                        value={formData.address}
                        onChange={onChange}
                        variant="outlined"
                        multiline
                        rows={3}
                      />
                    </Grid>
                  </Grid>
                </EnhancedCardContent>
              </EnhancedCard>
            </FormSection>

            <FormSection>
              <EnhancedCard>
                <EnhancedCardContent>
                  <CardTitle>
                    <PhoneIcon /> Emergency Contact
                  </CardTitle>
                  <CardSubtitle>
                    Update emergency contact information if needed
                  </CardSubtitle>
                  {/* Current Emergency Contact Info */}
                  <Box sx={{ mb: 2, p: 1, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                    <Typography variant="subtitle2" component="div">
                      Current Emergency Contact Information:
                    </Typography>
                    <Typography variant="body2" component="div">
                      Name: {patient?.emergency_contact_name || 'Not set'}
                    </Typography>
                    <Typography variant="body2" component="div">
                      Phone: {patient?.emergency_contact_phone || 'Not set'}
                    </Typography>
                    <Typography variant="body2" component="div">
                      Relationship: {patient?.emergency_contact_relationship || 'Not set'}
                    </Typography>
                  </Box>
                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        id="emergency_contact_name"
                        name="emergency_contact_name"
                        label="Emergency Contact Name"
                        value={formData.emergency_contact_name}
                        onChange={onChange}
                        variant="outlined"
                        helperText="Optional: If provided, phone number is also required"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        id="emergency_contact_phone"
                        name="emergency_contact_phone"
                        label="Emergency Contact Phone"
                        value={formData.emergency_contact_phone}
                        onChange={onChange}
                        variant="outlined"
                        helperText="Optional: If provided, name is also required"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        id="emergency_contact_relationship"
                        name="emergency_contact_relationship"
                        label="Relationship to Patient"
                        value={formData.emergency_contact_relationship}
                        onChange={onChange}
                        variant="outlined"
                      />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        id="emergency_contact_updated"
                        name="emergency_contact_updated"
                        label="Emergency Contact Updated?"
                        select
                        value={formData.emergency_contact_updated || 'no'}
                        onChange={onChange}
                        variant="outlined"
                        SelectProps={{ native: true }}
                        helperText="Has the emergency contact information been updated during this visit?"
                        required={!!(formData.emergency_contact_name && formData.emergency_contact_phone)}
                      >
                        <option value="yes">Yes</option>
                        <option value="no">No</option>
                      </TextField>
                    </Grid>
                  </Grid>
                </EnhancedCardContent>
              </EnhancedCard>
            </FormSection>
          </TabPanel>

          {/* Medical Information Tab */}
          <TabPanel value={activeStep} index={2}>
            <FormSection>
              <SectionTitle variant="h6">
                <MedicalIcon /> Medication Information
              </SectionTitle>

              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="current_medications"
                    name="current_medications"
                    label="Current Medications"
                    value={formData.current_medications}
                    onChange={onChange}
                    variant="outlined"
                    multiline
                    rows={3}
                    helperText="List all current medications with dosages"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="medication_adherence"
                    name="medication_adherence"
                    label="Medication Adherence"
                    value={formData.medication_adherence}
                    onChange={onChange}
                    variant="outlined"
                    helperText="How well does the patient adhere to medication schedule?"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="medication_side_effects"
                    name="medication_side_effects"
                    label="Medication Side Effects"
                    value={formData.medication_side_effects}
                    onChange={onChange}
                    variant="outlined"
                    helperText="Any reported side effects from medications"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="medication_allergies"
                    name="medication_allergies"
                    label="Medication Allergies"
                    value={formData.medication_allergies}
                    onChange={onChange}
                    variant="outlined"
                    helperText="Specific allergies to medications"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="pill_burden"
                    name="pill_burden"
                    label="Pill Burden"
                    value={formData.pill_burden}
                    onChange={onChange}
                    variant="outlined"
                    helperText="Number of pills taken daily"
                  />
                </Grid>
              </Grid>
            </FormSection>
          </TabPanel>

          {/* Vital Signs Tab */}
          <TabPanel value={activeStep} index={3}>
            <FormSection>
              <EnhancedCard>
                <EnhancedCardContent>
                  <CardTitle>
                    <FavoriteIcon /> Basic Measurements
                  </CardTitle>
                  <CardSubtitle>
                    Record the patient's height, weight, temperature, and respiratory rate
                  </CardSubtitle>
                  <CollapsibleGuidance
                    title="Clinical Guidance - Basic Measurements"
                    contextKey="vital_signs_basic"
                  />

                  {/* Current Vital Signs */}
                  {process.env.NODE_ENV === 'development' && (
                    <Box sx={{ mb: 2, p: 1, bgcolor: '#f5f5f5', borderRadius: 1 }}>
                      <Typography variant="caption" component="div">
                        Current Vital Signs Information:
                      </Typography>
                      <Typography variant="caption" component="div">
                        Height: {formData.height || 'Not set'} cm
                      </Typography>
                      <Typography variant="caption" component="div">
                        Weight: {formData.weight || 'Not set'} kg
                      </Typography>
                      <Typography variant="caption" component="div">
                        BMI: {formData.bmi || 'Not calculated'}
                      </Typography>
                    </Box>
                  )}
                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={4}>
                      <TextField
                        fullWidth
                        id="height"
                        name="height"
                        label="Height (cm)"
                        type="number"
                        value={formData.height}
                        onChange={onChange}
                        variant="outlined"
                        InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <TextField
                        fullWidth
                        id="weight"
                        name="weight"
                        label="Weight (kg)"
                        type="number"
                        value={formData.weight}
                        onChange={onChange}
                        variant="outlined"
                        InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                      />
                    </Grid>
                    <Grid item xs={12} sm={4}>
                      <TextField
                        fullWidth
                        id="bmi"
                        name="bmi"
                        label="BMI"
                        value={formData.bmi}
                        disabled
                        variant="outlined"
                        helperText="Auto-calculated from height and weight"
                      />
                      {formData.bmi && (
                        <NormalRangeIndicator value={formData.bmi} min={18.5} max={24.9} unit="" />
                      )}
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        id="temperature"
                        name="temperature"
                        label="Temperature (°C)"
                        type="number"
                        value={formData.temperature}
                        onChange={onChange}
                        variant="outlined"
                        InputProps={{ inputProps: { min: 30, max: 45, step: 0.1 } }}
                      />
                      <NormalRangeIndicator value={formData.temperature} min={36.1} max={37.2} unit="°C" />
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        id="respiratory_rate"
                        name="respiratory_rate"
                        label="Respiratory Rate (breaths/min)"
                        type="number"
                        value={formData.respiratory_rate}
                        onChange={onChange}
                        variant="outlined"
                        InputProps={{ inputProps: { min: 0, max: 60 } }}
                      />
                      <NormalRangeIndicator value={formData.respiratory_rate} min={12} max={20} unit="breaths/min" />
                    </Grid>
                  </Grid>
                </EnhancedCardContent>
              </EnhancedCard>
            </FormSection>

            <FormSection>
              <EnhancedCard>
                <EnhancedCardContent>
                  <CardTitle>
                    <FavoriteIcon /> Vital Signs
                  </CardTitle>
                  <CardSubtitle>
                    Record the patient's pulse oximetry and blood pressure measurements
                  </CardSubtitle>
                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        id="pulse_oximetry"
                        name="pulse_oximetry"
                        label="Oxygen Saturation (%)"
                        type="number"
                        value={formData.pulse_oximetry}
                        onChange={onChange}
                        variant="outlined"
                        InputProps={{ inputProps: { min: 0, max: 100 } }}
                      />
                      <NormalRangeIndicator value={formData.pulse_oximetry} min={95} max={100} unit="%" />
                    </Grid>
                  </Grid>
                </EnhancedCardContent>
              </EnhancedCard>
            </FormSection>

            <FormSection>
              <EnhancedCard>
                <EnhancedCardContent>
                  <CardTitle>
                    <FavoriteIcon /> Blood Pressure & Heart Rate
                  </CardTitle>
                  <CardSubtitle>
                    Record the patient's blood pressure and heart rate in different positions
                  </CardSubtitle>
                  <CollapsibleGuidance
                    title="Clinical Guidance - Blood Pressure & Heart Rate"
                    contextKey="vital_signs_bp_hr"
                  />
                  <Grid container spacing={3}>
                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle1" gutterBottom>
                        Lying Position
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={6}>
                          <TextField
                            fullWidth
                            id="lying_bp_systolic"
                            name="lying_bp_systolic"
                            label="Systolic (mmHg)"
                            type="number"
                            value={formData.lying_bp_systolic}
                            onChange={onChange}
                            variant="outlined"
                            InputProps={{ inputProps: { min: 0, max: 300 } }}
                          />
                          <NormalRangeIndicator value={formData.lying_bp_systolic} min={90} max={120} unit="mmHg" />
                        </Grid>
                        <Grid item xs={6}>
                          <TextField
                            fullWidth
                            id="lying_bp_diastolic"
                            name="lying_bp_diastolic"
                            label="Diastolic (mmHg)"
                            type="number"
                            value={formData.lying_bp_diastolic}
                            onChange={onChange}
                            variant="outlined"
                            InputProps={{ inputProps: { min: 0, max: 200 } }}
                          />
                          <NormalRangeIndicator value={formData.lying_bp_diastolic} min={60} max={80} unit="mmHg" />
                        </Grid>
                        <Grid item xs={12}>
                          <TextField
                            fullWidth
                            id="lying_heart_rate"
                            name="lying_heart_rate"
                            label="Heart Rate (bpm)"
                            type="number"
                            value={formData.lying_heart_rate}
                            onChange={onChange}
                            variant="outlined"
                            InputProps={{ inputProps: { min: 0, max: 250 } }}
                          />
                          <NormalRangeIndicator value={formData.lying_heart_rate} min={60} max={100} unit="bpm" />
                        </Grid>
                      </Grid>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle1" gutterBottom>
                        Standing Position
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={6}>
                          <TextField
                            fullWidth
                            id="standing_bp_systolic"
                            name="standing_bp_systolic"
                            label="Systolic (mmHg)"
                            type="number"
                            value={formData.standing_bp_systolic}
                            onChange={onChange}
                            variant="outlined"
                            InputProps={{ inputProps: { min: 0, max: 300 } }}
                          />
                          <NormalRangeIndicator value={formData.standing_bp_systolic} min={90} max={120} unit="mmHg" />
                        </Grid>
                        <Grid item xs={6}>
                          <TextField
                            fullWidth
                            id="standing_bp_diastolic"
                            name="standing_bp_diastolic"
                            label="Diastolic (mmHg)"
                            type="number"
                            value={formData.standing_bp_diastolic}
                            onChange={onChange}
                            variant="outlined"
                            InputProps={{ inputProps: { min: 0, max: 200 } }}
                          />
                          <NormalRangeIndicator value={formData.standing_bp_diastolic} min={60} max={80} unit="mmHg" />
                        </Grid>
                        <Grid item xs={12}>
                          <TextField
                            fullWidth
                            id="standing_heart_rate"
                            name="standing_heart_rate"
                            label="Heart Rate (bpm)"
                            type="number"
                            value={formData.standing_heart_rate}
                            onChange={onChange}
                            variant="outlined"
                            InputProps={{ inputProps: { min: 0, max: 250 } }}
                          />
                          <NormalRangeIndicator value={formData.standing_heart_rate} min={60} max={100} unit="bpm" />
                        </Grid>
                      </Grid>
                    </Grid>

                    <Grid item xs={12} sm={6}>
                      <Typography variant="subtitle1" gutterBottom>
                        Sitting Position
                      </Typography>
                      <Grid container spacing={2}>
                        <Grid item xs={6}>
                          <TextField
                            fullWidth
                            id="sitting_bp_systolic"
                            name="sitting_bp_systolic"
                        label="Systolic (mmHg)"
                        type="number"
                        value={formData.sitting_bp_systolic}
                        onChange={onChange}
                        variant="outlined"
                        InputProps={{ inputProps: { min: 0, max: 300 } }}
                      />
                      <NormalRangeIndicator value={formData.sitting_bp_systolic} min={90} max={120} unit="mmHg" />
                    </Grid>
                    <Grid item xs={6}>
                      <TextField
                        fullWidth
                        id="sitting_bp_diastolic"
                        name="sitting_bp_diastolic"
                        label="Diastolic (mmHg)"
                        type="number"
                        value={formData.sitting_bp_diastolic}
                        onChange={onChange}
                        variant="outlined"
                        InputProps={{ inputProps: { min: 0, max: 200 } }}
                      />
                      <NormalRangeIndicator value={formData.sitting_bp_diastolic} min={60} max={80} unit="mmHg" />
                    </Grid>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        id="sitting_heart_rate"
                        name="sitting_heart_rate"
                        label="Heart Rate (bpm)"
                        type="number"
                        value={formData.sitting_heart_rate}
                        onChange={onChange}
                        variant="outlined"
                        InputProps={{ inputProps: { min: 0, max: 250 } }}
                      />
                      <NormalRangeIndicator value={formData.sitting_heart_rate} min={60} max={100} unit="bpm" />
                    </Grid>
                  </Grid>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="heart_rhythm"
                    name="heart_rhythm"
                    label="Heart Rhythm"
                    value={formData.heart_rhythm}
                    onChange={onChange}
                    variant="outlined"
                    select
                    SelectProps={{ native: true }}
                  >
                    <option value=""></option>
                    <option value="Regular">Regular</option>
                    <option value="Irregular">Irregular</option>
                    <option value="Atrial Fibrillation">Atrial Fibrillation</option>
                    <option value="Bradycardia">Bradycardia</option>
                    <option value="Tachycardia">Tachycardia</option>
                  </TextField>
                </Grid>
              </Grid>

              {/* Orthostatic Hypotension Result */}
              {orthostaticResult.message && (
                <Box sx={{ mt: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, boxShadow: 1 }}>
                  <Typography variant="subtitle1" gutterBottom fontWeight="bold">
                    Orthostatic Assessment
                  </Typography>
                  <Typography variant="body2" color={orthostaticResult.hasOrthostaticHypotension || orthostaticResult.hasOrthostaticTachycardia ? 'error' : 'success.main'}>
                    {orthostaticResult.message}
                  </Typography>
                  {orthostaticResult.hasSystolicDrop && (
                    <Typography variant="body2" color="error">
                      • Systolic BP drop ≥ 20 mmHg detected
                    </Typography>
                  )}
                  {orthostaticResult.hasDiastolicDrop && (
                    <Typography variant="body2" color="error">
                      • Diastolic BP drop ≥ 10 mmHg detected
                    </Typography>
                  )}
                  {orthostaticResult.hasHeartRateIncrease && (
                    <Typography variant="body2" color="error">
                      • Heart rate increase ≥ 30 bpm detected
                    </Typography>
                  )}
                </Box>
              )}
              </EnhancedCardContent>
              </EnhancedCard>
            </FormSection>
          </TabPanel>

          {/* Lab Results Tab */}
          <TabPanel value={activeStep} index={4}>
            <FormSection>
              <SectionTitle variant="h6">
                <ScienceIcon /> Blood Glucose & Diabetes Markers
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Diabetes Markers"
                contextKey="lab_results_diabetes"
              />
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="blood_glucose"
                    name="blood_glucose"
                    label="Blood Glucose (mg/dL)"
                    type="number"
                    value={formData.blood_glucose}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0 } }}
                  />
                  <NormalRangeIndicator value={formData.blood_glucose} min={70} max={99} unit="mg/dL" />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="hba1c"
                    name="hba1c"
                    label="HbA1c (%)"
                    type="number"
                    value={formData.hba1c}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, max: 20, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.hba1c} min={4.0} max={5.6} unit="%" />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <ScienceIcon /> Lipid Profile
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Lipid Profile"
                contextKey="lab_results_lipids"
              />
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="cholesterol_total"
                    name="cholesterol_total"
                    label="Total Cholesterol (mg/dL)"
                    type="number"
                    value={formData.cholesterol_total}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0 } }}
                  />
                  <NormalRangeIndicator value={formData.cholesterol_total} min={125} max={200} unit="mg/dL" />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="hdl_cholesterol"
                    name="hdl_cholesterol"
                    label="HDL Cholesterol (mg/dL)"
                    type="number"
                    value={formData.hdl_cholesterol}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0 } }}
                  />
                  <NormalRangeIndicator value={formData.hdl_cholesterol} min={40} max={60} unit="mg/dL" />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="ldl_cholesterol"
                    name="ldl_cholesterol"
                    label="LDL Cholesterol (mg/dL)"
                    type="number"
                    value={formData.ldl_cholesterol}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0 } }}
                  />
                  <NormalRangeIndicator value={formData.ldl_cholesterol} min={0} max={100} unit="mg/dL" />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="vldl_cholesterol"
                    name="vldl_cholesterol"
                    label="VLDL Cholesterol (mg/dL)"
                    type="number"
                    value={formData.vldl_cholesterol}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0 } }}
                  />
                  <NormalRangeIndicator value={formData.vldl_cholesterol} min={0} max={30} unit="mg/dL" />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="triglycerides"
                    name="triglycerides"
                    label="Triglycerides (mg/dL)"
                    type="number"
                    value={formData.triglycerides}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0 } }}
                  />
                  <NormalRangeIndicator value={formData.triglycerides} min={0} max={150} unit="mg/dL" />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <ScienceIcon /> Kidney Function Tests
              </SectionTitle>
              <Box sx={{ mb: 2, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
                <Typography variant="subtitle2" color="info.contrastText">
                  <strong>Important for BEERS Criteria:</strong> Entering kidney function data (eGFR and creatinine) is essential for proper medication alerts. Some medications like Amiloride hydrochloride will only trigger BEERS Criteria alerts when kidney function is impaired.
                </Typography>
              </Box>
              <CollapsibleGuidance
                title="Clinical Guidance - Kidney Function Tests"
                contextKey="lab_results_kidney"
              />
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="creatinine"
                    name="creatinine"
                    label="Creatinine (mg/dL)"
                    type="number"
                    value={formData.creatinine}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.creatinine} min={0.6} max={1.3} unit="mg/dL" />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="egfr"
                    name="egfr"
                    label="eGFR (mL/min/1.73m²)"
                    type="number"
                    value={formData.egfr}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0 } }}
                  />
                  <NormalRangeIndicator value={formData.egfr} min={90} max={120} unit="mL/min/1.73m²" />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="blood_urea_nitrogen"
                    name="blood_urea_nitrogen"
                    label="Blood Urea Nitrogen (mg/dL)"
                    type="number"
                    value={formData.blood_urea_nitrogen}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0 } }}
                  />
                  <NormalRangeIndicator value={formData.blood_urea_nitrogen} min={7} max={20} unit="mg/dL" />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="uric_acid"
                    name="uric_acid"
                    label="Uric Acid (mg/dL)"
                    type="number"
                    value={formData.uric_acid}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.uric_acid} min={2.6} max={7.2} unit="mg/dL" />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="urine_albumin_creatinine_ratio"
                    name="urine_albumin_creatinine_ratio"
                    label="Urine Albumin-Creatinine Ratio (mg/g)"
                    type="number"
                    value={formData.urine_albumin_creatinine_ratio}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0 } }}
                  />
                  <NormalRangeIndicator value={formData.urine_albumin_creatinine_ratio} min={0} max={30} unit="mg/g" />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <ScienceIcon /> Liver Function Tests
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Liver Function Tests"
                contextKey="lab_results_liver"
              />
              <Grid container spacing={3}>
                <Typography variant="subtitle1" gutterBottom sx={{ ml: 2, mt: 2 }}>
                  Liver Enzymes
                </Typography>
                <Grid container spacing={3} sx={{ ml: 0 }}>
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      id="alt"
                      name="alt"
                      label="ALT (U/L)"
                      type="number"
                      value={formData.alt}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{ inputProps: { min: 0 } }}
                    />
                    <NormalRangeIndicator value={formData.alt} min={7} max={56} unit="U/L" />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      id="ast"
                      name="ast"
                      label="AST (U/L)"
                      type="number"
                      value={formData.ast}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{ inputProps: { min: 0 } }}
                    />
                    <NormalRangeIndicator value={formData.ast} min={8} max={48} unit="U/L" />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      id="alp"
                      name="alp"
                      label="ALP (U/L)"
                      type="number"
                      value={formData.alp}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{ inputProps: { min: 0 } }}
                    />
                    <NormalRangeIndicator value={formData.alp} min={40} max={129} unit="U/L" />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      id="ggt"
                      name="ggt"
                      label="GGT (U/L)"
                      type="number"
                      value={formData.ggt}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{ inputProps: { min: 0 } }}
                    />
                    <NormalRangeIndicator value={formData.ggt} min={8} max={61} unit="U/L" />
                  </Grid>
                </Grid>

                <Typography variant="subtitle1" gutterBottom sx={{ ml: 2, mt: 2 }}>
                  Liver Function
                </Typography>
                <Grid container spacing={3} sx={{ ml: 0 }}>
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      id="bilirubin_t"
                      name="bilirubin_t"
                      label="Total Bilirubin (mg/dL)"
                      type="number"
                      value={formData.bilirubin_t}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    />
                    <NormalRangeIndicator value={formData.bilirubin_t} min={0.1} max={1.2} unit="mg/dL" />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      id="bilirubin_d"
                      name="bilirubin_d"
                      label="Direct Bilirubin (mg/dL)"
                      type="number"
                      value={formData.bilirubin_d}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    />
                    <NormalRangeIndicator value={formData.bilirubin_d} min={0.0} max={0.3} unit="mg/dL" />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      id="albumin"
                      name="albumin"
                      label="Albumin (g/dL)"
                      type="number"
                      value={formData.albumin}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    />
                    <NormalRangeIndicator value={formData.albumin} min={3.5} max={5.0} unit="g/dL" />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      id="total_protein"
                      name="total_protein"
                      label="Total Protein (g/dL)"
                      type="number"
                      value={formData.total_protein}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    />
                    <NormalRangeIndicator value={formData.total_protein} min={6.0} max={8.3} unit="g/dL" />
                  </Grid>
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <ScienceIcon /> Electrolytes and Minerals
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Electrolytes and Minerals"
                contextKey="lab_results_electrolytes"
              />
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="sodium"
                    name="sodium"
                    label="Sodium (mEq/L)"
                    type="number"
                    value={formData.sodium}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0 } }}
                  />
                  <NormalRangeIndicator value={formData.sodium} min={135} max={145} unit="mEq/L" />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="potassium"
                    name="potassium"
                    label="Potassium (mEq/L)"
                    type="number"
                    value={formData.potassium}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.potassium} min={3.5} max={5.0} unit="mEq/L" />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="calcium"
                    name="calcium"
                    label="Calcium (mg/dL)"
                    type="number"
                    value={formData.calcium}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.calcium} min={8.5} max={10.5} unit="mg/dL" />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="magnesium"
                    name="magnesium"
                    label="Magnesium (mg/dL)"
                    type="number"
                    value={formData.magnesium}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.magnesium} min={1.7} max={2.2} unit="mg/dL" />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <ScienceIcon /> Complete Blood Count
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Complete Blood Count"
                contextKey="lab_results_cbc"
              />

              <Grid container spacing={3}>
                <Typography variant="subtitle1" gutterBottom sx={{ ml: 2, mt: 2 }}>
                  Red Blood Cell Parameters
                </Typography>
                <Grid container spacing={3} sx={{ ml: 0 }}>
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      id="rbc"
                      name="rbc"
                      label="RBC (million cells/μL)"
                      type="number"
                      value={formData.rbc}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    />
                    <NormalRangeIndicator value={formData.rbc} min={4.1} max={5.9} unit="million cells/μL" />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      id="hemoglobin"
                      name="hemoglobin"
                      label="Hemoglobin (g/dL)"
                      type="number"
                      value={formData.hemoglobin}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    />
                    <NormalRangeIndicator value={formData.hemoglobin} min={12.0} max={17.5} unit="g/dL" />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      id="hematocrit"
                      name="hematocrit"
                      label="Hematocrit (%)"
                      type="number"
                      value={formData.hematocrit}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    />
                    <NormalRangeIndicator value={formData.hematocrit} min={36} max={50} unit="%" />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      id="platelets"
                      name="platelets"
                      label="Platelets (thousand/μL)"
                      type="number"
                      value={formData.platelets}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{ inputProps: { min: 0 } }}
                    />
                    <NormalRangeIndicator value={formData.platelets} min={150} max={450} unit="thousand/μL" />
                  </Grid>
                </Grid>

                <Typography variant="subtitle1" gutterBottom sx={{ ml: 2, mt: 2 }}>
                  Red Blood Cell Indices
                </Typography>
                <Grid container spacing={3} sx={{ ml: 0 }}>
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      id="mcv"
                      name="mcv"
                      label="MCV (fL)"
                      type="number"
                      value={formData.mcv}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    />
                    <NormalRangeIndicator value={formData.mcv} min={80} max={100} unit="fL" />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      id="mch"
                      name="mch"
                      label="MCH (pg)"
                      type="number"
                      value={formData.mch}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    />
                    <NormalRangeIndicator value={formData.mch} min={27} max={33} unit="pg" />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      id="mchc"
                      name="mchc"
                      label="MCHC (g/dL)"
                      type="number"
                      value={formData.mchc}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    />
                    <NormalRangeIndicator value={formData.mchc} min={32} max={36} unit="g/dL" />
                  </Grid>
                  <Grid item xs={12} sm={6} md={3}>
                    <TextField
                      fullWidth
                      id="rdw"
                      name="rdw"
                      label="RDW (%)"
                      type="number"
                      value={formData.rdw}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    />
                    <NormalRangeIndicator value={formData.rdw} min={11.5} max={14.5} unit="%" />
                  </Grid>
                </Grid>

                <Typography variant="subtitle1" gutterBottom sx={{ ml: 2, mt: 2 }}>
                  White Blood Cell Count & Differential
                </Typography>
                <Grid container spacing={3} sx={{ ml: 0 }}>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      fullWidth
                      id="wbc"
                      name="wbc"
                      label="WBC (thousand/μL)"
                      type="number"
                      value={formData.wbc}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    />
                    <NormalRangeIndicator value={formData.wbc} min={4.5} max={11.0} unit="thousand/μL" />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      fullWidth
                      id="neutrophils"
                      name="neutrophils"
                      label="Neutrophils (%)"
                      type="number"
                      value={formData.neutrophils}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{ inputProps: { min: 0, max: 100, step: 0.1 } }}
                    />
                    <NormalRangeIndicator value={formData.neutrophils} min={40} max={60} unit="%" />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      fullWidth
                      id="lymphocytes"
                      name="lymphocytes"
                      label="Lymphocytes (%)"
                      type="number"
                      value={formData.lymphocytes}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{ inputProps: { min: 0, max: 100, step: 0.1 } }}
                    />
                    <NormalRangeIndicator value={formData.lymphocytes} min={20} max={40} unit="%" />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      fullWidth
                      id="monocytes"
                      name="monocytes"
                      label="Monocytes (%)"
                      type="number"
                      value={formData.monocytes}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{ inputProps: { min: 0, max: 100, step: 0.1 } }}
                    />
                    <NormalRangeIndicator value={formData.monocytes} min={2} max={8} unit="%" />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      fullWidth
                      id="eosinophils"
                      name="eosinophils"
                      label="Eosinophils (%)"
                      type="number"
                      value={formData.eosinophils}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{ inputProps: { min: 0, max: 100, step: 0.1 } }}
                    />
                    <NormalRangeIndicator value={formData.eosinophils} min={1} max={4} unit="%" />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      fullWidth
                      id="basophils"
                      name="basophils"
                      label="Basophils (%)"
                      type="number"
                      value={formData.basophils}
                      onChange={onChange}
                      variant="outlined"
                      InputProps={{ inputProps: { min: 0, max: 100, step: 0.1 } }}
                    />
                    <NormalRangeIndicator value={formData.basophils} min={0.5} max={1} unit="%" />
                  </Grid>
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <ScienceIcon /> Iron Studies
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Iron Studies"
                contextKey="lab_results_iron"
              />
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="ferritin"
                    name="ferritin"
                    label="Ferritin (ng/mL)"
                    type="number"
                    value={formData.ferritin}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.ferritin} min={10} max={250} unit="ng/mL" />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="iron"
                    name="iron"
                    label="Iron (μg/dL)"
                    type="number"
                    value={formData.iron}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.iron} min={50} max={175} unit="μg/dL" />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <ScienceIcon /> Vitamin Status
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Vitamin Status"
                contextKey="lab_results_vitamins"
              />
              <Grid container spacing={3}>
                <Grid item xs={12} sm={4}>
                  <TextField
                    fullWidth
                    id="vitamin_b12"
                    name="vitamin_b12"
                    label="Vitamin B12 (pg/mL)"
                    type="number"
                    value={formData.vitamin_b12}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 1 } }}
                  />
                  <NormalRangeIndicator value={formData.vitamin_b12} min={200} max={900} unit="pg/mL" />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    fullWidth
                    id="vitamin_d"
                    name="vitamin_d"
                    label="Vitamin D (25-OH) (ng/mL)"
                    type="number"
                    value={formData.vitamin_d}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 1 } }}
                  />
                  <NormalRangeIndicator value={formData.vitamin_d} min={30} max={80} unit="ng/mL" />
                </Grid>
                <Grid item xs={12} sm={4}>
                  <TextField
                    fullWidth
                    id="folate"
                    name="folate"
                    label="Folate (ng/mL)"
                    type="number"
                    value={formData.folate}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.folate} min={2.7} max={17.0} unit="ng/mL" />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <ScienceIcon /> Thyroid Function Tests
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Thyroid Function Tests"
                contextKey="lab_results_thyroid"
              />
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="tsh"
                    name="tsh"
                    label="TSH (mIU/L)"
                    type="number"
                    value={formData.tsh}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                  />
                  <NormalRangeIndicator value={formData.tsh} min={0.4} max={4.0} unit="mIU/L" />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="t4"
                    name="t4"
                    label="T4 (μg/dL)"
                    type="number"
                    value={formData.t4}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.t4} min={4.5} max={12.0} unit="μg/dL" />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="t3"
                    name="t3"
                    label="T3 (ng/dL)"
                    type="number"
                    value={formData.t3}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 1 } }}
                  />
                  <NormalRangeIndicator value={formData.t3} min={80} max={200} unit="ng/dL" />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <ScienceIcon /> Inflammatory Markers
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Inflammatory Markers"
                contextKey="lab_results_inflammation"
              />
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="crp"
                    name="crp"
                    label="CRP (mg/L)"
                    type="number"
                    value={formData.crp}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    helperText="C-reactive protein"
                  />
                  <NormalRangeIndicator value={formData.crp} min={0} max={3.0} unit="mg/L" />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="esr"
                    name="esr"
                    label="ESR (mm/hr)"
                    type="number"
                    value={formData.esr}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 1 } }}
                    helperText="Erythrocyte sedimentation rate"
                  />
                  <NormalRangeIndicator value={formData.esr} min={0} max={20} unit="mm/hr" />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <ScienceIcon /> Urinalysis
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Urinalysis"
                contextKey="lab_results_urinalysis"
              />
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'medium', mb: 1 }}>Physical Properties</Typography>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="urine_color"
                    name="urine_color"
                    label="Color"
                    value={formData.urine_color}
                    onChange={onChange}
                    variant="outlined"
                    select
                  >
                    <MenuItem value="Pale Yellow">Pale Yellow</MenuItem>
                    <MenuItem value="Yellow">Yellow</MenuItem>
                    <MenuItem value="Dark Yellow">Dark Yellow</MenuItem>
                    <MenuItem value="Amber">Amber</MenuItem>
                    <MenuItem value="Red/Pink">Red/Pink</MenuItem>
                    <MenuItem value="Orange">Orange</MenuItem>
                    <MenuItem value="Blue/Green">Blue/Green</MenuItem>
                    <MenuItem value="Brown">Brown</MenuItem>
                    <MenuItem value="Colorless">Colorless</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="urine_transparency"
                    name="urine_transparency"
                    label="Transparency"
                    value={formData.urine_transparency}
                    onChange={onChange}
                    variant="outlined"
                    select
                  >
                    <MenuItem value="Clear">Clear</MenuItem>
                    <MenuItem value="Slightly Cloudy">Slightly Cloudy</MenuItem>
                    <MenuItem value="Cloudy">Cloudy</MenuItem>
                    <MenuItem value="Turbid">Turbid</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="urine_ph"
                    name="urine_ph"
                    label="pH"
                    type="number"
                    value={formData.urine_ph}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 4.5, max: 9.0, step: 0.1 } }}
                  />
                  <NormalRangeIndicator value={formData.urine_ph} min={4.5} max={8.0} unit="" />
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'medium', mb: 1, mt: 2 }}>Chemical Tests</Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="urine_protein"
                    name="urine_protein"
                    label="Protein"
                    value={formData.urine_protein}
                    onChange={onChange}
                    variant="outlined"
                    select
                  >
                    <MenuItem value="Negative">Negative</MenuItem>
                    <MenuItem value="Trace">Trace</MenuItem>
                    <MenuItem value="1+">1+</MenuItem>
                    <MenuItem value="2+">2+</MenuItem>
                    <MenuItem value="3+">3+</MenuItem>
                    <MenuItem value="4+">4+</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="urine_sugar"
                    name="urine_sugar"
                    label="Glucose"
                    value={formData.urine_sugar}
                    onChange={onChange}
                    variant="outlined"
                    select
                  >
                    <MenuItem value="Negative">Negative</MenuItem>
                    <MenuItem value="Trace">Trace</MenuItem>
                    <MenuItem value="1+">1+</MenuItem>
                    <MenuItem value="2+">2+</MenuItem>
                    <MenuItem value="3+">3+</MenuItem>
                    <MenuItem value="4+">4+</MenuItem>
                  </TextField>
                </Grid>

                <Grid item xs={12}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 'medium', mb: 1, mt: 2 }}>Microscopic Examination</Typography>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="urine_rbcs"
                    name="urine_rbcs"
                    label="RBCs"
                    value={formData.urine_rbcs}
                    onChange={onChange}
                    variant="outlined"
                    select
                  >
                    <MenuItem value="0-2/HPF">0-2/HPF</MenuItem>
                    <MenuItem value="3-5/HPF">3-5/HPF</MenuItem>
                    <MenuItem value="6-10/HPF">6-10/HPF</MenuItem>
                    <MenuItem value="11-20/HPF">11-20/HPF</MenuItem>
                    <MenuItem value=">20/HPF">&gt;20/HPF</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="urine_pus_cells"
                    name="urine_pus_cells"
                    label="Pus Cells (WBCs)"
                    value={formData.urine_pus_cells}
                    onChange={onChange}
                    variant="outlined"
                    select
                  >
                    <MenuItem value="0-5/HPF">0-5/HPF</MenuItem>
                    <MenuItem value="6-10/HPF">6-10/HPF</MenuItem>
                    <MenuItem value="11-20/HPF">11-20/HPF</MenuItem>
                    <MenuItem value="21-50/HPF">21-50/HPF</MenuItem>
                    <MenuItem value=">50/HPF">&gt;50/HPF</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="urine_epithelial_cells"
                    name="urine_epithelial_cells"
                    label="Epithelial Cells"
                    value={formData.urine_epithelial_cells}
                    onChange={onChange}
                    variant="outlined"
                    select
                  >
                    <MenuItem value="None">None</MenuItem>
                    <MenuItem value="Few">Few</MenuItem>
                    <MenuItem value="Moderate">Moderate</MenuItem>
                    <MenuItem value="Many">Many</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={6} md={6}>
                  <TextField
                    fullWidth
                    id="urine_crystals"
                    name="urine_crystals"
                    label="Crystals"
                    value={formData.urine_crystals}
                    onChange={onChange}
                    variant="outlined"
                    select
                  >
                    <MenuItem value="None">None</MenuItem>
                    <MenuItem value="Calcium Oxalate">Calcium Oxalate</MenuItem>
                    <MenuItem value="Uric Acid">Uric Acid</MenuItem>
                    <MenuItem value="Triple Phosphate">Triple Phosphate</MenuItem>
                    <MenuItem value="Amorphous Phosphate">Amorphous Phosphate</MenuItem>
                    <MenuItem value="Amorphous Urate">Amorphous Urate</MenuItem>
                    <MenuItem value="Other">Other</MenuItem>
                  </TextField>
                </Grid>
                <Grid item xs={12} sm={6} md={6}>
                  <TextField
                    fullWidth
                    id="urine_casts"
                    name="urine_casts"
                    label="Casts"
                    value={formData.urine_casts}
                    onChange={onChange}
                    variant="outlined"
                    select
                  >
                    <MenuItem value="None">None</MenuItem>
                    <MenuItem value="Hyaline">Hyaline</MenuItem>
                    <MenuItem value="Granular">Granular</MenuItem>
                    <MenuItem value="Waxy">Waxy</MenuItem>
                    <MenuItem value="RBC">RBC</MenuItem>
                    <MenuItem value="WBC">WBC</MenuItem>
                    <MenuItem value="Epithelial">Epithelial</MenuItem>
                    <MenuItem value="Fatty">Fatty</MenuItem>
                    <MenuItem value="Other">Other</MenuItem>
                  </TextField>
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <ScienceIcon /> Miscellaneous Screening
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Cancer Screening"
                contextKey="lab_results_cancer"
              />
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="psa"
                    name="psa"
                    label="PSA (ng/mL)"
                    type="number"
                    value={formData.psa}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.01 } }}
                    helperText="Prostate-specific antigen (for male patients)"
                  />
                  <NormalRangeIndicator value={formData.psa} min={0} max={4.0} unit="ng/mL" />
                </Grid>
                <Grid item xs={12} sm={6} md={4}>
                  <TextField
                    fullWidth
                    id="ca125"
                    name="ca125"
                    label="CA-125 (U/mL)"
                    type="number"
                    value={formData.ca125}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, step: 0.1 } }}
                    helperText="Cancer antigen 125 (for ovarian cancer)"
                  />
                  <NormalRangeIndicator value={formData.ca125} min={0} max={35} unit="U/mL" />
                </Grid>
                <Grid item xs={12} sm={12} md={4}>
                  <TextField
                    fullWidth
                    id="cancer_screening_results"
                    name="cancer_screening_results"
                    label="Other Cancer Screening Results"
                    value={formData.cancer_screening_results}
                    onChange={onChange}
                    variant="outlined"
                    multiline
                    rows={2}
                    helperText="Results from mammogram, colonoscopy, etc."
                  />
                </Grid>
              </Grid>
            </FormSection>
          </TabPanel>

          {/* Cognitive Health Tab */}
          <TabPanel value={activeStep} index={5}>
            <FormSection>
              <SectionTitle variant="h6" color="secondary">
                <PsychologyIcon /> Cognitive Assessment (Mini-Cog)
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Cognitive Assessment"
                contextKey="mental_health_cognitive"
              />
              <CognitiveHealthAssessment
                value={formData.mini_cog_total || ''}
                onChange={handleCognitiveChange}
                miniCogWordRecallScore={formData.mini_cog_word_recall_score}
                miniCogClockDrawingScore={formData.mini_cog_clock_drawing_score}
                miniCogWordsUsed={formData.mini_cog_words_used}
                miniCogWordsRecalled={formData.mini_cog_words_recalled}
                miniCogNotes={formData.mini_cog_notes}
                onMiniCogChange={handleCognitiveFieldsChange}
              />
            </FormSection>
          </TabPanel>

          {/* Depression Tab */}
          <TabPanel value={activeStep} index={6}>
            <FormSection>
              <SectionTitle variant="h6" color="secondary">
                <MoodIcon /> Depression Assessment (PHQ-9)
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Depression Assessment (PHQ-9)"
                contextKey="mental_health_depression"
              />
              <DepressionAssessment
                value={formData.depression_score || ''}
                onChange={handleDepressionChange}
                phq9InterestPleasure={formData.phq9_interest_pleasure}
                phq9FeelingDown={formData.phq9_feeling_down}
                phq9SleepIssues={formData.phq9_sleep_issues}
                phq9Tired={formData.phq9_tired}
                phq9Appetite={formData.phq9_appetite}
                phq9FeelingBad={formData.phq9_feeling_bad}
                phq9Concentration={formData.phq9_concentration}
                phq9MovingSpeaking={formData.phq9_moving_speaking}
                phq9ThoughtsHurting={formData.phq9_thoughts_hurting}
                phq9DifficultyLevel={formData.phq9_difficulty_level}
                phq9Notes={formData.phq9_notes}
                depressionScore={formData.depression_score}
                onPhq9Change={handleDepressionFieldsChange}
              />
            </FormSection>
          </TabPanel>

          {/* Anxiety Tab */}
          <TabPanel value={activeStep} index={7}>
            <FormSection>
              <SectionTitle variant="h6" color="secondary">
                <PsychologyIcon /> Anxiety Assessment (GAD-7)
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Anxiety Assessment (GAD-7)"
                contextKey="mental_health_anxiety"
              />
              <AnxietyAssessment
                value={formData.anxiety_score || ''}
                onChange={handleAnxietyChange}
                gad7FeelingNervous={formData.gad7_feeling_nervous}
                gad7StopWorrying={formData.gad7_stop_worrying}
                gad7WorryingMuch={formData.gad7_worrying_much}
                gad7TroubleRelaxing={formData.gad7_trouble_relaxing}
                gad7Restless={formData.gad7_restless}
                gad7Annoyed={formData.gad7_annoyed}
                gad7FeelingAfraid={formData.gad7_feeling_afraid}
                gad7DifficultyLevel={formData.gad7_difficulty_level}
                gad7Notes={formData.gad7_notes}
                anxietyScore={formData.anxiety_score}
                onGad7Change={handleAnxietyFieldsChange}
              />
            </FormSection>
          </TabPanel>

          {/* Sleep & Pain Tab */}
          <TabPanel value={activeStep} index={8}>
            <FormSection>
              <BasicSleepInfo
                formData={formData as any}
                onChange={handleSleepInfoChange}
                handleSelectChange={handleMuiSelectChange}
              />
            </FormSection>

            <FormSection>
              <PSQIAssessment
                formData={formData as any}
                onChange={handlePSQIChange}
                handleSelectChange={handleMuiSelectChange}
              />
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <PainIcon /> Pain Assessment
              </SectionTitle>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="pain_level"
                    name="pain_level"
                    label="Pain Level (0-10)"
                    type="number"
                    value={formData.pain_level}
                    onChange={onChange}
                    variant="outlined"
                    InputProps={{ inputProps: { min: 0, max: 10, step: 1 } }}
                    helperText="0 = No pain, 10 = Worst possible pain"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="pain_location"
                    name="pain_location"
                    label="Pain Location"
                    value={formData.pain_location}
                    onChange={onChange}
                    variant="outlined"
                    multiline
                    rows={2}
                    helperText="Describe where the pain is located"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="pain_character"
                    name="pain_character"
                    label="Pain Character"
                    value={formData.pain_character}
                    onChange={onChange}
                    variant="outlined"
                    multiline
                    rows={2}
                    helperText="Describe the character of the pain (e.g., sharp, dull, burning, etc.)"
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="safe_pain_medications"
                    name="safe_pain_medications"
                    label="Safe Pain Medications"
                    value={formData.safe_pain_medications}
                    onChange={onChange}
                    variant="outlined"
                    multiline
                    rows={2}
                    helperText="List medications that are safe for pain management"
                  />
                </Grid>
              </Grid>
            </FormSection>
          </TabPanel>

          {/* Frailty Assessment Tab */}
          <TabPanel value={activeStep} index={9}>
            <FormSection>
              <VisitFrailtyAssessment
                formData={formData as any}
                onChange={handleFrailtyChange}
                handleSelectChange={handleMuiSelectChange}
                handleRadioChange={handleFrailtyRadioChange}
              />
            </FormSection>

            <FormSection>
              <FallsRiskAssessment
                formData={formData as any}
                onChange={onChange}
                handleSelectChange={handleMuiSelectChange}
                handleRadioChange={handleRadioChange}
              />
            </FormSection>
          </TabPanel>

          {/* Vaccinations Tab */}
          <TabPanel value={activeStep} index={10}>
            <FormSection>
              <SectionTitle variant="h6">
                <VaccinesIcon /> Vaccination Records
              </SectionTitle>
              <CollapsibleGuidance
                title="Clinical Guidance - Vaccinations for Older Adults"
                contextKey="vaccinations_older_adults"
              />
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="influenza_vaccination_date"
                    name="influenza_vaccination_date"
                    label="Influenza (Flu) Vaccination Date"
                    type="date"
                    value={formData.influenza_vaccination_date}
                    onChange={onChange}
                    variant="outlined"
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="pneumococcal_vaccination_date"
                    name="pneumococcal_vaccination_date"
                    label="Pneumococcal Vaccination Date"
                    type="date"
                    value={formData.pneumococcal_vaccination_date}
                    onChange={onChange}
                    variant="outlined"
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="zoster_vaccination_date"
                    name="zoster_vaccination_date"
                    label="Zoster (Shingles) Vaccination Date"
                    type="date"
                    value={formData.zoster_vaccination_date}
                    onChange={onChange}
                    variant="outlined"
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="tdap_vaccination_date"
                    name="tdap_vaccination_date"
                    label="Tdap Vaccination Date"
                    type="date"
                    value={formData.tdap_vaccination_date}
                    onChange={onChange}
                    variant="outlined"
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="covid19_vaccination_date"
                    name="covid19_vaccination_date"
                    label="COVID-19 Vaccination Date"
                    type="date"
                    value={formData.covid19_vaccination_date}
                    onChange={onChange}
                    variant="outlined"
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="covid19_booster_date"
                    name="covid19_booster_date"
                    label="COVID-19 Booster Date"
                    type="date"
                    value={formData.covid19_booster_date}
                    onChange={onChange}
                    variant="outlined"
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="hepatitis_a_vaccination_date"
                    name="hepatitis_a_vaccination_date"
                    label="Hepatitis A Vaccination Date"
                    type="date"
                    value={formData.hepatitis_a_vaccination_date}
                    onChange={onChange}
                    variant="outlined"
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="hepatitis_b_vaccination_date"
                    name="hepatitis_b_vaccination_date"
                    label="Hepatitis B Vaccination Date"
                    type="date"
                    value={formData.hepatitis_b_vaccination_date}
                    onChange={onChange}
                    variant="outlined"
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="mmr_vaccination_date"
                    name="mmr_vaccination_date"
                    label="MMR Vaccination Date"
                    type="date"
                    value={formData.mmr_vaccination_date}
                    onChange={onChange}
                    variant="outlined"
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="varicella_vaccination_date"
                    name="varicella_vaccination_date"
                    label="Varicella Vaccination Date"
                    type="date"
                    value={formData.varicella_vaccination_date}
                    onChange={onChange}
                    variant="outlined"
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="other_vaccinations"
                    name="other_vaccinations"
                    label="Other Vaccinations"
                    value={formData.other_vaccinations}
                    onChange={onChange}
                    variant="outlined"
                    multiline
                    rows={2}
                  />
                </Grid>
              </Grid>
            </FormSection>
          </TabPanel>

          {/* Health Status Tab */}
          <TabPanel value={activeStep} index={11}>
            <FormSection>
              <SectionTitle variant="h6">
                <HealthIcon /> General Health Status
              </SectionTitle>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="activity-level-label">Activity Level</InputLabel>
                    <Select
                      labelId="activity-level-label"
                      id="activity_level"
                      name="activity_level"
                      value={formData.activity_level}
                      onChange={handleMuiSelectChange}
                      label="Activity Level"
                    >
                      <MenuItem value="">Select Activity Level</MenuItem>
                      <MenuItem value="Sedentary">Sedentary</MenuItem>
                      <MenuItem value="Light">Light</MenuItem>
                      <MenuItem value="Moderate">Moderate</MenuItem>
                      <MenuItem value="Active">Active</MenuItem>
                      <MenuItem value="Very Active">Very Active</MenuItem>
                    </Select>
                    <FormHelperText>Patient's current physical activity level</FormHelperText>
                  </FormControl>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="exercise-frequency-label">Exercise Frequency</InputLabel>
                    <Select
                      labelId="exercise-frequency-label"
                      id="exercise_frequency"
                      name="exercise_frequency"
                      value={formData.exercise_frequency}
                      onChange={handleMuiSelectChange}
                      label="Exercise Frequency"
                    >
                      <MenuItem value="">Select Exercise Frequency</MenuItem>
                      <MenuItem value="Never">Never</MenuItem>
                      <MenuItem value="Rarely (1-2 times/month)">Rarely (1-2 times/month)</MenuItem>
                      <MenuItem value="Occasionally (1-2 times/week)">Occasionally (1-2 times/week)</MenuItem>
                      <MenuItem value="Regularly (3-4 times/week)">Regularly (3-4 times/week)</MenuItem>
                      <MenuItem value="Daily">Daily</MenuItem>
                      <MenuItem value="Multiple times daily">Multiple times daily</MenuItem>
                    </Select>
                    <FormHelperText>How often the patient exercises</FormHelperText>
                  </FormControl>
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <RestaurantIcon /> Nutrition & Hydration
              </SectionTitle>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="nutritional-status-label">Nutritional Status</InputLabel>
                    <Select
                      labelId="nutritional-status-label"
                      id="nutritional_status"
                      name="nutritional_status"
                      value={formData.nutritional_status}
                      onChange={handleMuiSelectChange}
                      label="Nutritional Status"
                    >
                      <MenuItem value="">Select Nutritional Status</MenuItem>
                      <MenuItem value="Good">Good</MenuItem>
                      <MenuItem value="Fair">Fair</MenuItem>
                      <MenuItem value="Poor">Poor</MenuItem>
                      <MenuItem value="Malnourished">Malnourished</MenuItem>
                      <MenuItem value="Severely malnourished">Severely malnourished</MenuItem>
                    </Select>
                    <FormHelperText>Patient's current nutritional status</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="dietary-intake-label">Dietary Intake Quality</InputLabel>
                    <Select
                      labelId="dietary-intake-label"
                      id="dietary_intake_quality"
                      name="dietary_intake_quality"
                      value={formData.dietary_intake_quality}
                      onChange={handleMuiSelectChange}
                      label="Dietary Intake Quality"
                    >
                      <MenuItem value="">Select Dietary Intake Quality</MenuItem>
                      <MenuItem value="Excellent">Excellent</MenuItem>
                      <MenuItem value="Good">Good</MenuItem>
                      <MenuItem value="Fair">Fair</MenuItem>
                      <MenuItem value="Poor">Poor</MenuItem>
                      <MenuItem value="Very poor">Very poor</MenuItem>
                    </Select>
                    <FormHelperText>Quality of patient's daily food intake</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="hydration-status-label">Hydration Status</InputLabel>
                    <Select
                      labelId="hydration-status-label"
                      id="hydration_status"
                      name="hydration_status"
                      value={formData.hydration_status}
                      onChange={handleMuiSelectChange}
                      label="Hydration Status"
                    >
                      <MenuItem value="">Select Hydration Status</MenuItem>
                      <MenuItem value="Well Hydrated">Well Hydrated</MenuItem>
                      <MenuItem value="Adequately Hydrated">Adequately Hydrated</MenuItem>
                      <MenuItem value="Mildly Dehydrated">Mildly Dehydrated</MenuItem>
                      <MenuItem value="Moderately Dehydrated">Moderately Dehydrated</MenuItem>
                      <MenuItem value="Severely Dehydrated">Severely Dehydrated</MenuItem>
                      <MenuItem value="Dehydrated">Dehydrated</MenuItem>
                    </Select>
                    <FormHelperText>Patient's current hydration status</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="supplements"
                    name="supplements"
                    label="Supplements"
                    value={formData.supplements}
                    onChange={onChange}
                    variant="outlined"
                    multiline
                    rows={2}
                    helperText="Vitamins, minerals, or other supplements taken regularly"
                  />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <VisibilityIcon /> Sensory Assessment
              </SectionTitle>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="vision-status-label">Vision Status</InputLabel>
                    <Select
                      labelId="vision-status-label"
                      id="vision_status"
                      name="vision_status"
                      value={formData.vision_status}
                      onChange={handleMuiSelectChange}
                      label="Vision Status"
                    >
                      <MenuItem value="">Select Vision Status</MenuItem>
                      <MenuItem value="Normal">Normal</MenuItem>
                      <MenuItem value="Mild impairment">Mild impairment</MenuItem>
                      <MenuItem value="Moderate impairment">Moderate impairment</MenuItem>
                      <MenuItem value="Severe impairment">Severe impairment</MenuItem>
                      <MenuItem value="Legally blind">Legally blind</MenuItem>
                    </Select>
                    <FormHelperText>Patient's current vision status</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="vision-aid-label">Vision Aid</InputLabel>
                    <Select
                      labelId="vision-aid-label"
                      id="use_of_aid_vision"
                      name="use_of_aid_vision"
                      value={formData.use_of_aid_vision}
                      onChange={handleMuiSelectChange}
                      label="Vision Aid"
                    >
                      <MenuItem value="">Select Vision Aid</MenuItem>
                      <MenuItem value="None">None</MenuItem>
                      <MenuItem value="Glasses">Glasses</MenuItem>
                      <MenuItem value="Contact lenses">Contact lenses</MenuItem>
                      <MenuItem value="Magnifier">Magnifier</MenuItem>
                      <MenuItem value="Multiple aids">Multiple aids</MenuItem>
                    </Select>
                    <FormHelperText>Vision aids used by patient</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="hearing-status-label">Hearing Status</InputLabel>
                    <Select
                      labelId="hearing-status-label"
                      id="hearing_status"
                      name="hearing_status"
                      value={formData.hearing_status}
                      onChange={handleMuiSelectChange}
                      label="Hearing Status"
                    >
                      <MenuItem value="">Select Hearing Status</MenuItem>
                      <MenuItem value="Normal">Normal</MenuItem>
                      <MenuItem value="Mild Impairment">Mild Impairment</MenuItem>
                      <MenuItem value="Moderate Impairment">Moderate Impairment</MenuItem>
                      <MenuItem value="Severe Impairment">Severe Impairment</MenuItem>
                      <MenuItem value="Profound Loss">Profound Loss</MenuItem>
                      <MenuItem value="Uses hearing aid">Uses hearing aid</MenuItem>
                    </Select>
                    <FormHelperText>Patient's current hearing status</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="hearing-aid-label">Hearing Aid</InputLabel>
                    <Select
                      labelId="hearing-aid-label"
                      id="use_of_aid_hearing"
                      name="use_of_aid_hearing"
                      value={formData.use_of_aid_hearing}
                      onChange={handleMuiSelectChange}
                      label="Hearing Aid"
                    >
                      <MenuItem value="">Select Hearing Aid</MenuItem>
                      <MenuItem value="None">None</MenuItem>
                      <MenuItem value="Hearing aid">Hearing aid</MenuItem>
                      <MenuItem value="Cochlear implant">Cochlear implant</MenuItem>
                      <MenuItem value="Assistive listening device">Assistive listening device</MenuItem>
                      <MenuItem value="Multiple aids">Multiple aids</MenuItem>
                    </Select>
                    <FormHelperText>Hearing aids used by patient</FormHelperText>
                  </FormControl>
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6">
                <PersonIcon /> Social & Environmental Factors
              </SectionTitle>
              <Grid container spacing={3}>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="social-interaction-label">Social Interaction Levels</InputLabel>
                    <Select
                      labelId="social-interaction-label"
                      id="social_interaction_levels"
                      name="social_interaction_levels"
                      value={formData.social_interaction_levels}
                      onChange={handleMuiSelectChange}
                      label="Social Interaction Levels"
                    >
                      <MenuItem value="">Select Social Interaction Level</MenuItem>
                      <MenuItem value="Very active">Very active</MenuItem>
                      <MenuItem value="Active">Active</MenuItem>
                      <MenuItem value="Regular">Regular</MenuItem>
                      <MenuItem value="Moderate">Moderate</MenuItem>
                      <MenuItem value="Limited">Limited</MenuItem>
                      <MenuItem value="Isolated">Isolated</MenuItem>
                    </Select>
                    <FormHelperText>Patient's level of social engagement</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="social-support-label">Social Support</InputLabel>
                    <Select
                      labelId="social-support-label"
                      id="social_support"
                      name="social_support"
                      value={formData.social_support}
                      onChange={handleMuiSelectChange}
                      label="Social Support"
                    >
                      <MenuItem value="">Select Social Support</MenuItem>
                      <MenuItem value="Strong">Strong (extensive support network)</MenuItem>
                      <MenuItem value="Adequate">Adequate (sufficient support available)</MenuItem>
                      <MenuItem value="Limited">Limited (minimal support available)</MenuItem>
                      <MenuItem value="None">None (no support system)</MenuItem>
                    </Select>
                    <FormHelperText>Level of social support available to patient</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="social_support_network"
                    name="social_support_network"
                    label="Social Support Network"
                    value={formData.social_support_network}
                    onChange={onChange}
                    variant="outlined"
                    multiline
                    rows={2}
                    helperText="Details about family, friends, community support"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="living-situation-label">Living Situation</InputLabel>
                    <Select
                      labelId="living-situation-label"
                      id="living_situation"
                      name="living_situation"
                      value={formData.living_situation}
                      onChange={handleMuiSelectChange}
                      label="Living Situation"
                    >
                      <MenuItem value="">Select Living Situation</MenuItem>
                      <MenuItem value="Lives alone">Lives alone</MenuItem>
                      <MenuItem value="Lives with spouse/partner">Lives with spouse/partner</MenuItem>
                      <MenuItem value="Lives with family">Lives with family</MenuItem>
                      <MenuItem value="Lives with caregiver">Lives with caregiver</MenuItem>
                      <MenuItem value="Assisted living">Assisted living facility</MenuItem>
                      <MenuItem value="Nursing home">Nursing home</MenuItem>
                      <MenuItem value="Other">Other</MenuItem>
                    </Select>
                    <FormHelperText>Patient's current living arrangement</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="living_conditions"
                    name="living_conditions"
                    label="Living Conditions"
                    value={formData.living_conditions}
                    onChange={onChange}
                    variant="outlined"
                    multiline
                    rows={2}
                    helperText="Description of home environment and conditions"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="environmental_risks"
                    name="environmental_risks"
                    label="Environmental Risks"
                    value={formData.environmental_risks}
                    onChange={onChange}
                    variant="outlined"
                    multiline
                    rows={2}
                    helperText="Potential hazards in the patient's environment"
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="age-friendly-environment-label">Age-Friendly Environment</InputLabel>
                    <Select
                      labelId="age-friendly-environment-label"
                      id="age_friendly_environment"
                      name="age_friendly_environment"
                      value={formData.age_friendly_environment}
                      onChange={handleMuiSelectChange}
                      label="Age-Friendly Environment"
                    >
                      <MenuItem value="">Select</MenuItem>
                      <MenuItem value="true">Yes</MenuItem>
                      <MenuItem value="false">No</MenuItem>
                    </Select>
                    <FormHelperText>Is the patient's environment adapted for older adults?</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="transportation-access-label">Transportation Access</InputLabel>
                    <Select
                      labelId="transportation-access-label"
                      id="transportation_access"
                      name="transportation_access"
                      value={formData.transportation_access}
                      onChange={handleMuiSelectChange}
                      label="Transportation Access"
                    >
                      <MenuItem value="">Select Transportation Access</MenuItem>
                      <MenuItem value="Independent driver">Independent driver</MenuItem>
                      <MenuItem value="Relies on others">Relies on others</MenuItem>
                      <MenuItem value="Relies on family">Relies on family</MenuItem>
                      <MenuItem value="Public transportation">Public transportation</MenuItem>
                      <MenuItem value="Medical transportation">Medical transportation</MenuItem>
                      <MenuItem value="Limited access">Limited access</MenuItem>
                      <MenuItem value="No access">No access</MenuItem>
                    </Select>
                    <FormHelperText>Patient's access to transportation</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth variant="outlined">
                    <InputLabel id="financial-concern-label">Financial Concerns</InputLabel>
                    <Select
                      labelId="financial-concern-label"
                      id="financial_concern"
                      name="financial_concern"
                      value={formData.financial_concern}
                      onChange={handleMuiSelectChange}
                      label="Financial Concerns"
                    >
                      <MenuItem value="">Select Financial Status</MenuItem>
                      <MenuItem value="None">None</MenuItem>
                      <MenuItem value="Minor">Minor</MenuItem>
                      <MenuItem value="Mild">Mild</MenuItem>
                      <MenuItem value="Moderate">Moderate</MenuItem>
                      <MenuItem value="Significant">Significant</MenuItem>
                      <MenuItem value="Severe">Severe</MenuItem>
                    </Select>
                    <FormHelperText>Level of financial concerns affecting care</FormHelperText>
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="home_safety_evaluation"
                    name="home_safety_evaluation"
                    label="Home Safety Evaluation"
                    value={formData.home_safety_evaluation}
                    onChange={onChange}
                    variant="outlined"
                    multiline
                    rows={2}
                    helperText="Results of home safety assessment"
                  />
                </Grid>
              </Grid>
            </FormSection>
          </TabPanel>

          {/* Diagnosis & Prescriptions Tab */}
          <TabPanel value={activeStep} index={12}>
            <FormSection>
              <SectionTitle variant="h6" color="secondary">
                <MedicalIcon /> Diagnosis
              </SectionTitle>
              <TextField
                fullWidth
                id="diagnosis"
                name="diagnosis"
                label="Diagnosis"
                multiline
                rows={4}
                value={formData.diagnosis || ""}
                onChange={onChange}
                variant="outlined"
                placeholder="Enter patient diagnosis information"
                helperText="Enter the patient's diagnosis, including primary and secondary conditions"
              />
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6" color="secondary">
                <MedicalIcon /> Treatment Plan
              </SectionTitle>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="treatment_plan"
                    name="treatment_plan"
                    label="Treatment Plan"
                    multiline
                    rows={4}
                    value={formData.treatment_plan || ""}
                    onChange={onChange}
                    variant="outlined"
                    placeholder="Enter treatment plan details"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="follow_up_instructions"
                    name="follow_up_instructions"
                    label="Follow-up Instructions"
                    multiline
                    rows={3}
                    value={formData.follow_up_instructions || ""}
                    onChange={onChange}
                    variant="outlined"
                    placeholder="Enter follow-up instructions"
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="referrals"
                    name="referrals"
                    label="Referrals"
                    multiline
                    rows={2}
                    value={formData.referrals || ""}
                    onChange={onChange}
                    variant="outlined"
                    placeholder="Enter any referrals to specialists or other healthcare providers"
                  />
                </Grid>
              </Grid>
            </FormSection>

            <FormSection>
              <SectionTitle variant="h6" color="secondary">
                <MedicationIcon /> Prescriptions
              </SectionTitle>
              <PrescriptionSection
                prescriptions={formData.prescriptions || []}
                onPrescriptionsChange={(prescriptions) => {
                  setFormData(prev => ({
                    ...prev,
                    prescriptions
                  }));
                }}
                patientId={parseInt(patientId || '0')}
                patientAge={patient?.date_of_birth ? calculateAge(patient.date_of_birth) : undefined}
              />

              {/* BEERS Criteria Checker */}
              {formData.prescriptions && formData.prescriptions.length > 0 && (
                <BeersCriteriaChecker
                  medications={formData.prescriptions.map((p: Prescription) => p.medication)}
                  patientId={parseInt(patientId || '0')}
                  patientAge={patient?.date_of_birth ? calculateAge(patient.date_of_birth) : undefined}
                  visitId={isEdit && visitId ? parseInt(visitId) : undefined}
                  prescriptions={formData.prescriptions}
                  onOverride={(alert, reason) => {
                    // Find the prescription that matches the alert
                    const updatedPrescriptions = [...(formData.prescriptions || [])];
                    const prescriptionIndex = updatedPrescriptions.findIndex(
                      p => p.medication.toLowerCase() === alert.medication.toLowerCase()
                    );

                    if (prescriptionIndex !== -1) {
                      // Get the current user ID from the auth context
                      const currentUserId = user?.user_id;

                      // Update the prescription with the override information
                      updatedPrescriptions[prescriptionIndex] = {
                        ...updatedPrescriptions[prescriptionIndex],
                        beers_criteria_id: alert.criterion.criteria_id,
                        beers_override_reason: reason,
                        beers_overridden_by: currentUserId, // Set the user ID who overrode the alert
                        beers_overridden_at: new Date().toISOString()
                      };

                      // Log the user ID for debugging
                      console.log(`Setting beers_overridden_by to user ID: ${currentUserId} for medication: ${alert.medication}`);

                      // Update the form data with the updated prescriptions
                      setFormData(prev => ({
                        ...prev,
                        prescriptions: updatedPrescriptions
                      }));

                      console.log(`BEERS override saved for ${alert.medication}: ${reason} by user ${currentUserId}`);
                    }
                  }}
                />
              )}
            </FormSection>
          </TabPanel>

          {/* Review & Submit Tab */}
          <TabPanel value={activeStep} index={13}>
            <FormSection>
              <SectionTitle variant="h6" color="secondary">
                <AssessmentIcon /> Review Visit Information
              </SectionTitle>
              <Typography variant="body1" sx={{ mb: 2 }}>
                Please review all the information you've entered before submitting. Make sure all required fields are filled correctly.
              </Typography>
              <Alert severity="info" sx={{ mb: 3 }}>
                Once submitted, you'll be redirected to the patient's visits page where you can make additional changes if needed.
              </Alert>

              {/* Form Completion Progress */}
              <Box sx={{ mb: 4 }}>
                <Typography variant="h6" gutterBottom>
                  <CheckCircleIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Form Completion Status
                </Typography>

                {/* Two-panel review component */}
                <TwoPanelReview
                  sections={(() => {
                    // Create section data for visualization
                    const sectionData = [
                      // Personal Information
                      'Visit Information',
                      'Contact Information',
                      'Emergency Contact',

                      // Medical Data
                      'Medical Information',
                      'Vital Signs',
                      'Lab Results',

                      // Mental Health
                      'Cognitive Health',
                      'Depression',
                      'Anxiety',

                      // Assessments
                      'Sleep & Pain',
                      'Frailty Assessment',

                      // Health Status
                      'Health Status',

                      // Preventive Care
                      'Vaccinations',

                      // Medications
                      'Diagnosis & Prescriptions'
                    ].map((section) => {
                      // Calculate completion for this section
                      const { isComplete, percentage, missingFields, tabIndex } = calculateSectionCompletion(section);

                      // Determine status
                      let status: 'complete' | 'partial' | 'incomplete' | 'error' = 'incomplete';
                      if (isComplete) {
                        status = 'complete';
                      } else if (percentage > 0) {
                        status = 'partial';
                      }

                      // We're already using field labels in the missingFields array
                      const formattedMissingFields = missingFields;

                      // Determine which category this section belongs to
                      let category = '';
                      if (['Visit Information', 'Contact Information', 'Emergency Contact'].includes(section)) {
                        category = 'Personal Information';
                      } else if (['Medical Information', 'Vital Signs', 'Lab Results'].includes(section)) {
                        category = 'Medical Data';
                      } else if (['Cognitive Health', 'Depression', 'Anxiety'].includes(section)) {
                        category = 'Mental Health';
                      } else if (['Sleep & Pain', 'Frailty Assessment'].includes(section)) {
                        category = 'Assessments';
                      } else if (['Health Status'].includes(section)) {
                        category = 'Health Status';
                      } else if (['Vaccinations'].includes(section)) {
                        category = 'Preventive Care';
                      } else if (['Diagnosis & Prescriptions'].includes(section)) {
                        category = 'Medications';
                      }

                      return {
                        id: section.toLowerCase().replace(/\s+/g, '_'),
                        label: section,
                        status,
                        percentage,
                        missingFields: formattedMissingFields,
                        errorCount: 0, // No validation errors in the TwoPanelReview component
                        tabIndex,
                        category
                      };
                    });

                    return sectionData;
                  })()}
                  onNavigate={handleStepChange}
                />
              </Box>

              {/* Validation Summary */}
              {validateVisitData().length > 0 && (
                <Alert severity="warning" sx={{ mb: 3 }}>
                  <AlertTitle>Please address the following issues:</AlertTitle>
                  <List dense sx={{ mt: 1, mb: 0 }}>
                    {validateVisitData().map((error, index) => {
                      // Extract field name from error message
                      const fieldMatch = error.match(/^([A-Za-z\s]+) (is required|must be|format is invalid|value seems)/);
                      const fieldName = fieldMatch ? fieldMatch[1].toLowerCase().replace(/\s+/g, '_') : '';

                      // Use the new navigateToField helper function to get tab index and field ID
                      const { suggestion, tabIndex } = getFieldNavigationInfo(error, fieldName); // fieldId is unused

                      return (
                        <ListItem key={index} sx={{
                          py: 0.5,
                          borderLeft: '3px solid',
                          borderColor: 'warning.main',
                          bgcolor: 'warning.lightest',
                          pl: 2,
                          mb: 1,
                          borderRadius: '0 4px 4px 0'
                        }}>
                          <ListItemText
                            primary={error}
                            secondary={suggestion ? (
                              <Typography variant="body2" sx={{ mt: 0.5, color: 'text.secondary', fontStyle: 'italic' }}>
                                <InfoIcon sx={{ fontSize: '0.9rem', mr: 0.5, verticalAlign: 'text-top' }} />
                                Suggestion: {suggestion}
                              </Typography>
                            ) : null}
                          />
                          {tabIndex !== -1 && (
                            <Button
                              size="small"
                              variant="outlined"
                              color="warning"
                              startIcon={<JumpToPageIcon />}
                              onClick={() => navigateToField(tabIndex, fieldName)}
                              sx={{ ml: 2, whiteSpace: 'nowrap' }}
                            >
                              Jump to Field
                            </Button>
                          )}
                        </ListItem>
                      );
                    })}
                  </List>
                </Alert>
              )}

              <Divider sx={{ my: 3 }} />

              {/* Changes Summary (Edit Mode) */}
              {isEdit && originalData && (
                <EnhancedCard sx={{ mb: 3 }}>
                  <EnhancedCardContent>
                    <CardTitle>
                      <EditIcon /> Changes Summary
                    </CardTitle>
                    <Typography variant="body2" sx={{ mb: 2 }}>
                      The following fields have been modified:
                    </Typography>

                    {getChangedFields().length === 0 ? (
                      <Alert severity="info">No changes have been made yet.</Alert>
                    ) : (
                      <TableContainer component={Paper} variant="outlined">
                        <Table size="small">
                          <TableHead>
                            <TableRow>
                              <TableCell><strong>Field</strong></TableCell>
                              <TableCell><strong>Original Value</strong></TableCell>
                              <TableCell><strong>New Value</strong></TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {getChangedFields().map((change, index) => (
                              <TableRow key={index}>
                                <TableCell>{getFieldLabel(change.field)}</TableCell>
                                <TableCell>{formatFieldValue(change.field, change.oldValue)}</TableCell>
                                <TableCell>
                                  <Typography color="primary">
                                    <strong>{formatFieldValue(change.field, change.newValue)}</strong>
                                  </Typography>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </TableContainer>
                    )}
                  </EnhancedCardContent>
                </EnhancedCard>
              )}

              <Divider sx={{ my: 3 }} />

              {/* Visit Summary */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" gutterBottom>
                  <AssessmentIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Visit Information
                </Typography>

                {/* Visit Information */}
                <EnhancedCard sx={{ mb: 2 }}>
                  <EnhancedCardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          cursor: 'pointer'
                        }}
                        onClick={() => toggleSectionExpansion('visitInfo')}
                      >
                        <Typography variant="h6" component="h3" color="primary">
                          Visit Information
                        </Typography>
                        {expandedSections.visitInfo ?
                          <ExpandLessIcon sx={{ ml: 1, color: 'primary.main' }} /> :
                          <ExpandMoreIcon sx={{ ml: 1, color: 'primary.main' }} />
                        }
                      </Box>
                      <Button
                        size="small"
                        startIcon={<EditIcon />}
                        onClick={() => handleStepChange(0)}
                        color="primary"
                      >
                        Edit
                      </Button>
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    {expandedSections.visitInfo && (
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Visit Date:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.visit_date || <span style={{ color: 'red' }}>Not specified</span>}
                          </Typography>
                        </Grid>

                        <Grid item xs={12}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Reason for Visit:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.visit_reason || <span style={{ color: 'red' }}>Not specified</span>}
                          </Typography>
                        </Grid>
                      </Grid>
                    )}
                  </EnhancedCardContent>
                </EnhancedCard>

                {/* Contact Information */}
                <EnhancedCard sx={{ mb: 2 }}>
                  <EnhancedCardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          cursor: 'pointer'
                        }}
                        onClick={() => toggleSectionExpansion('contactInfo')}
                      >
                        <Typography variant="h6" component="h3" color="primary">
                          Contact Information
                        </Typography>
                        {expandedSections.contactInfo ?
                          <ExpandLessIcon sx={{ ml: 1, color: 'primary.main' }} /> :
                          <ExpandMoreIcon sx={{ ml: 1, color: 'primary.main' }} />
                        }
                      </Box>
                      <Button
                        size="small"
                        startIcon={<EditIcon />}
                        onClick={() => handleStepChange(1)}
                        color="primary"
                      >
                        Edit
                      </Button>
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    {expandedSections.contactInfo && (
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Phone:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.phone || <span style={{ color: 'red' }}>Not specified</span>}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Email:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.email || 'Not specified'}
                          </Typography>
                        </Grid>
                        <Grid item xs={12}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Address:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.address || 'Not specified'}
                          </Typography>
                        </Grid>
                      </Grid>
                    )}
                  </EnhancedCardContent>
                </EnhancedCard>

                {/* Emergency Contact */}
                <EnhancedCard sx={{ mb: 2 }}>
                  <EnhancedCardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          cursor: 'pointer'
                        }}
                        onClick={() => toggleSectionExpansion('emergencyContact')}
                      >
                        <Typography variant="h6" component="h3" color="primary">
                          Emergency Contact
                        </Typography>
                        {expandedSections.emergencyContact ?
                          <ExpandLessIcon sx={{ ml: 1, color: 'primary.main' }} /> :
                          <ExpandMoreIcon sx={{ ml: 1, color: 'primary.main' }} />
                        }
                      </Box>
                      <Button
                        size="small"
                        startIcon={<EditIcon />}
                        onClick={() => handleStepChange(1)}
                        color="primary"
                      >
                        Edit
                      </Button>
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    {expandedSections.emergencyContact && (
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Name:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.emergency_contact_name || <span style={{ color: 'red' }}>Not specified</span>}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Phone:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.emergency_contact_phone || <span style={{ color: 'red' }}>Not specified</span>}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Relationship to Patient:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.emergency_contact_relationship || <span style={{ color: 'red' }}>Not specified</span>}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Updated During Visit:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.emergency_contact_updated === 'yes' ? 'Yes' :
                             formData.emergency_contact_updated === 'no' ? 'No' :
                             <span style={{ color: 'red' }}>Not specified</span>}
                          </Typography>
                        </Grid>
                      </Grid>
                    )}
                  </EnhancedCardContent>
                </EnhancedCard>

                {/* Medical Information */}
                <EnhancedCard sx={{ mb: 2 }}>
                  <EnhancedCardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          cursor: 'pointer'
                        }}
                        onClick={() => toggleSectionExpansion('medicalInfo')}
                      >
                        <Typography variant="h6" component="h3" color="primary">
                          Medical Information
                        </Typography>
                        {expandedSections.medicalInfo ?
                          <ExpandLessIcon sx={{ ml: 1, color: 'primary.main' }} /> :
                          <ExpandMoreIcon sx={{ ml: 1, color: 'primary.main' }} />
                        }
                      </Box>
                      <Button
                        size="small"
                        startIcon={<EditIcon />}
                        onClick={() => handleStepChange(2)}
                        color="primary"
                      >
                        Edit
                      </Button>
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    {expandedSections.medicalInfo && (
                      <Grid container spacing={2}>
                        <Grid item xs={12}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Current Medications:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.current_medications || <span style={{ color: 'red' }}>Not specified</span>}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Medication Adherence:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.medication_adherence || <span style={{ color: 'red' }}>Not specified</span>}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Medication Side Effects:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.medication_side_effects || <span style={{ color: 'red' }}>Not specified</span>}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Medication Allergies:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.medication_allergies || <span style={{ color: 'red' }}>Not specified</span>}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Pill Burden:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.pill_burden || <span style={{ color: 'red' }}>Not specified</span>}
                          </Typography>
                        </Grid>
                      </Grid>
                    )}
                  </EnhancedCardContent>
                </EnhancedCard>

                {/* Vital Signs */}
                <EnhancedCard sx={{ mb: 2 }}>
                  <EnhancedCardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          cursor: 'pointer'
                        }}
                        onClick={() => toggleSectionExpansion('vitalSigns')}
                      >
                        <Typography variant="h6" component="h3" color="primary">
                          Vital Signs
                        </Typography>
                        {expandedSections.vitalSigns ?
                          <ExpandLessIcon sx={{ ml: 1, color: 'primary.main' }} /> :
                          <ExpandMoreIcon sx={{ ml: 1, color: 'primary.main' }} />
                        }
                      </Box>
                      <Button
                        size="small"
                        startIcon={<EditIcon />}
                        onClick={() => handleStepChange(3)}
                        color="primary"
                      >
                        Edit
                      </Button>
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    {expandedSections.vitalSigns && (
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Height:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.height ? `${formData.height} cm` : <span style={{ color: 'red' }}>Not recorded</span>}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Weight:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.weight ? `${formData.weight} kg` : <span style={{ color: 'red' }}>Not recorded</span>}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            BMI:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.bmi || 'Not calculated'}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Temperature:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.temperature ? `${formData.temperature} °C` : <span style={{ color: 'red' }}>Not recorded</span>}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Blood Pressure (Sitting):
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.sitting_bp_systolic && formData.sitting_bp_diastolic ?
                              `${formData.sitting_bp_systolic}/${formData.sitting_bp_diastolic} mmHg` :
                              <span style={{ color: 'red' }}>Not recorded</span>}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Heart Rate (Sitting):
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.sitting_heart_rate ? `${formData.sitting_heart_rate} bpm` : <span style={{ color: 'red' }}>Not recorded</span>}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Respiratory Rate:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.respiratory_rate ? `${formData.respiratory_rate} breaths/min` : <span style={{ color: 'red' }}>Not recorded</span>}
                          </Typography>
                        </Grid>
                      </Grid>
                    )}
                  </EnhancedCardContent>
                </EnhancedCard>

                {/* Lab Results */}
                <EnhancedCard sx={{ mb: 2 }}>
                  <EnhancedCardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          cursor: 'pointer'
                        }}
                        onClick={() => toggleSectionExpansion('labResults')}
                      >
                        <Typography variant="h6" component="h3" color="primary">
                          Lab Results
                        </Typography>
                        {expandedSections.labResults ?
                          <ExpandLessIcon sx={{ ml: 1, color: 'primary.main' }} /> :
                          <ExpandMoreIcon sx={{ ml: 1, color: 'primary.main' }} />
                        }
                      </Box>
                      <Button
                        size="small"
                        startIcon={<EditIcon />}
                        onClick={() => handleStepChange(4)}
                        color="primary"
                      >
                        Edit
                      </Button>
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    {expandedSections.labResults && (
                      <Grid container spacing={2}>
                        <Grid item xs={12}>
                          <Typography variant="subtitle1" gutterBottom>
                            Blood Glucose & Diabetes Markers
                          </Typography>
                          <Grid container spacing={2}>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="subtitle2" color="text.secondary">
                                Blood Glucose:
                              </Typography>
                              <Typography variant="body1" sx={{ mb: 1 }}>
                                {formData.blood_glucose ? `${formData.blood_glucose} mg/dL` : 'Not recorded'}
                              </Typography>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="subtitle2" color="text.secondary">
                                HbA1c:
                              </Typography>
                              <Typography variant="body1" sx={{ mb: 1 }}>
                                {formData.hba1c ? `${formData.hba1c}%` : 'Not recorded'}
                              </Typography>
                            </Grid>
                          </Grid>
                        </Grid>

                        <Grid item xs={12}>
                          <Typography variant="subtitle1" gutterBottom>
                            Lipid Profile
                          </Typography>
                          <Grid container spacing={2}>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="subtitle2" color="text.secondary">
                                Total Cholesterol:
                              </Typography>
                              <Typography variant="body1" sx={{ mb: 1 }}>
                                {formData.cholesterol_total ? `${formData.cholesterol_total} mg/dL` : 'Not recorded'}
                              </Typography>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="subtitle2" color="text.secondary">
                                HDL Cholesterol:
                              </Typography>
                              <Typography variant="body1" sx={{ mb: 1 }}>
                                {formData.hdl_cholesterol ? `${formData.hdl_cholesterol} mg/dL` : 'Not recorded'}
                              </Typography>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="subtitle2" color="text.secondary">
                                LDL Cholesterol:
                              </Typography>
                              <Typography variant="body1" sx={{ mb: 1 }}>
                                {formData.ldl_cholesterol ? `${formData.ldl_cholesterol} mg/dL` : 'Not recorded'}
                              </Typography>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="subtitle2" color="text.secondary">
                                Triglycerides:
                              </Typography>
                              <Typography variant="body1" sx={{ mb: 1 }}>
                                {formData.triglycerides ? `${formData.triglycerides} mg/dL` : 'Not recorded'}
                              </Typography>
                            </Grid>
                          </Grid>
                        </Grid>

                        <Grid item xs={12}>
                          <Typography variant="subtitle1" gutterBottom>
                            Kidney Function
                          </Typography>
                          <Grid container spacing={2}>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="subtitle2" color="text.secondary">
                                Creatinine:
                              </Typography>
                              <Typography variant="body1" sx={{ mb: 1 }}>
                                {formData.creatinine ? `${formData.creatinine} mg/dL` : 'Not recorded'}
                              </Typography>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="subtitle2" color="text.secondary">
                                eGFR:
                              </Typography>
                              <Typography variant="body1" sx={{ mb: 1 }}>
                                {formData.egfr ? `${formData.egfr} mL/min/1.73m²` : 'Not recorded'}
                              </Typography>
                            </Grid>
                          </Grid>
                        </Grid>

                        <Grid item xs={12}>
                          <Typography variant="subtitle1" gutterBottom>
                            Liver Function
                          </Typography>
                          <Grid container spacing={2}>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="subtitle2" color="text.secondary">
                                ALT:
                              </Typography>
                              <Typography variant="body1" sx={{ mb: 1 }}>
                                {formData.alt ? `${formData.alt} U/L` : 'Not recorded'}
                              </Typography>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="subtitle2" color="text.secondary">
                                AST:
                              </Typography>
                              <Typography variant="body1" sx={{ mb: 1 }}>
                                {formData.ast ? `${formData.ast} U/L` : 'Not recorded'}
                              </Typography>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="subtitle2" color="text.secondary">
                                Total Bilirubin:
                              </Typography>
                              <Typography variant="body1" sx={{ mb: 1 }}>
                                {formData.bilirubin_t ? `${formData.bilirubin_t} mg/dL` : 'Not recorded'}
                              </Typography>
                            </Grid>
                          </Grid>
                        </Grid>

                        <Grid item xs={12}>
                          <Typography variant="subtitle1" gutterBottom>
                            Complete Blood Count
                          </Typography>
                          <Grid container spacing={2}>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="subtitle2" color="text.secondary">
                                Hemoglobin:
                              </Typography>
                              <Typography variant="body1" sx={{ mb: 1 }}>
                                {formData.hemoglobin ? `${formData.hemoglobin} g/dL` : 'Not recorded'}
                              </Typography>
                            </Grid>
                            <Grid item xs={12} sm={6}>
                              <Typography variant="subtitle2" color="text.secondary">
                                WBC:
                              </Typography>
                              <Typography variant="body1" sx={{ mb: 1 }}>
                                {formData.wbc ? `${formData.wbc} thousand/μL` : 'Not recorded'}
                              </Typography>
                            </Grid>
                          </Grid>
                        </Grid>
                      </Grid>
                    )}
                  </EnhancedCardContent>
                </EnhancedCard>

                {/* Mental Health */}
                <EnhancedCard sx={{ mb: 2 }}>
                  <EnhancedCardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          cursor: 'pointer'
                        }}
                        onClick={() => toggleSectionExpansion('mentalHealth')}
                      >
                        <Typography variant="h6" component="h3" color="primary">
                          Mental Health
                        </Typography>
                        {expandedSections.mentalHealth ?
                          <ExpandLessIcon sx={{ ml: 1, color: 'primary.main' }} /> :
                          <ExpandMoreIcon sx={{ ml: 1, color: 'primary.main' }} />
                        }
                      </Box>
                      <Box>
                        <Button
                          size="small"
                          startIcon={<EditIcon />}
                          onClick={() => handleStepChange(5)}
                          color="primary"
                          sx={{ mr: 1 }}
                        >
                          Cognitive
                        </Button>
                        <Button
                          size="small"
                          startIcon={<EditIcon />}
                          onClick={() => handleStepChange(6)}
                          color="primary"
                          sx={{ mr: 1 }}
                        >
                          Depression
                        </Button>
                        <Button
                          size="small"
                          startIcon={<EditIcon />}
                          onClick={() => handleStepChange(7)}
                          color="primary"
                        >
                          Anxiety
                        </Button>
                      </Box>
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    {expandedSections.mentalHealth && (
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={4}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Cognitive Health (Mini-Cog):
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.mini_cog_total ? `Score: ${formData.mini_cog_total}` : <span style={{ color: 'red' }}>Not assessed</span>}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={4}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Depression (PHQ-9):
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.depression_score ? `Score: ${formData.depression_score}` : <span style={{ color: 'red' }}>Not assessed</span>}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={4}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Anxiety (GAD-7):
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.anxiety_score ? `Score: ${formData.anxiety_score}` : <span style={{ color: 'red' }}>Not assessed</span>}
                          </Typography>
                        </Grid>
                      </Grid>
                    )}
                  </EnhancedCardContent>
                </EnhancedCard>

                {/* Assessments Section Header */}
                <Typography variant="h6" gutterBottom sx={{ mt: 4, mb: 2, display: 'flex', alignItems: 'center' }}>
                  <AssessmentIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Assessments
                </Typography>

                {/* Sleep & Pain */}
                <EnhancedCard sx={{ mb: 2 }}>
                  <EnhancedCardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          cursor: 'pointer'
                        }}
                        onClick={() => toggleSectionExpansion('sleepPain')}
                      >
                        <Typography variant="h6" component="h3" color="primary">
                          Sleep & Pain
                        </Typography>
                        {expandedSections.sleepPain ?
                          <ExpandLessIcon sx={{ ml: 1, color: 'primary.main' }} /> :
                          <ExpandMoreIcon sx={{ ml: 1, color: 'primary.main' }} />
                        }
                      </Box>
                      <Button
                        size="small"
                        startIcon={<EditIcon />}
                        onClick={() => handleStepChange(8)}
                        color="primary"
                      >
                        Edit
                      </Button>
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    {expandedSections.sleepPain && (
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Sleep Quality:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.sleep_quality || <span style={{ color: 'red' }}>Not assessed</span>}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Pain Level:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.pain_level ? `${formData.pain_level}/10` : <span style={{ color: 'red' }}>Not assessed</span>}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Pain Location:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.pain_location || 'Not specified'}
                          </Typography>
                        </Grid>
                      </Grid>
                    )}
                  </EnhancedCardContent>
                </EnhancedCard>

                {/* Frailty Assessment */}
                <EnhancedCard sx={{ mb: 2 }}>
                  <EnhancedCardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          cursor: 'pointer'
                        }}
                        onClick={() => toggleSectionExpansion('frailtyAssessment')}
                      >
                        <Typography variant="h6" component="h3" color="primary">
                          Frailty Assessment
                        </Typography>
                        {expandedSections.frailtyAssessment ?
                          <ExpandLessIcon sx={{ ml: 1, color: 'primary.main' }} /> :
                          <ExpandMoreIcon sx={{ ml: 1, color: 'primary.main' }} />
                        }
                      </Box>
                      <Button
                        size="small"
                        startIcon={<EditIcon />}
                        onClick={() => handleStepChange(9)}
                        color="primary"
                      >
                        Edit
                      </Button>
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    {expandedSections.frailtyAssessment && (
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Grip Strength:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.grip_strength ? `${formData.grip_strength} kg` : <span style={{ color: 'red' }}>Not assessed</span>}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Calf Circumference:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.calf_circumference ? `${formData.calf_circumference} cm` : <span style={{ color: 'red' }}>Not assessed</span>}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Mobility Status:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.mobility_status || <span style={{ color: 'red' }}>Not assessed</span>}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Mobility Aids Used:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.mobility_aids_used || 'None'}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Fall Incidents (past 12 months):
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.fall_detection_incidents || <span style={{ color: 'red' }}>Not assessed</span>}
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Falls Risk Level:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.frat_risk_level || <span style={{ color: 'red' }}>Not assessed</span>}
                          </Typography>
                        </Grid>
                      </Grid>
                    )}
                  </EnhancedCardContent>
                </EnhancedCard>

                {/* Diagnosis & Prescriptions */}
                <EnhancedCard sx={{ mb: 2 }}>
                  <EnhancedCardContent>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                      <Box
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          cursor: 'pointer'
                        }}
                        onClick={() => toggleSectionExpansion('diagnosisPrescriptions')}
                      >
                        <Typography variant="h6" component="h3" color="primary">
                          Diagnosis & Prescriptions
                        </Typography>
                        {expandedSections.diagnosisPrescriptions ?
                          <ExpandLessIcon sx={{ ml: 1, color: 'primary.main' }} /> :
                          <ExpandMoreIcon sx={{ ml: 1, color: 'primary.main' }} />
                        }
                      </Box>
                      <Button
                        size="small"
                        startIcon={<EditIcon />}
                        onClick={() => handleStepChange(12)}
                        color="primary"
                      >
                        Edit
                      </Button>
                    </Box>
                    <Divider sx={{ mb: 2 }} />
                    {expandedSections.diagnosisPrescriptions && (
                      <Grid container spacing={2}>
                        <Grid item xs={12}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Diagnosis:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.diagnosis || <span style={{ color: 'red' }}>Not specified</span>}
                          </Typography>
                        </Grid>
                        <Grid item xs={12}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Treatment Plan:
                          </Typography>
                          <Typography variant="body1" sx={{ mb: 1 }}>
                            {formData.treatment_plan || 'Not specified'}
                          </Typography>
                        </Grid>
                        <Grid item xs={12}>
                          <Typography variant="subtitle2" color="text.secondary">
                            Prescriptions:
                          </Typography>
                          {formData.prescriptions && formData.prescriptions.length > 0 ? (
                            <List dense>
                              {formData.prescriptions.map((prescription, index) => (
                                <ListItem key={index} disablePadding sx={{ py: 0.5 }}>
                                  <ListItemText
                                    primary={`${prescription.medication} - ${prescription.dosage} ${prescription.frequency}`}
                                    secondary={prescription.notes}
                                  />
                                </ListItem>
                              ))}
                            </List>
                          ) : (
                            <Typography variant="body1" sx={{ mb: 1 }}>
                              No prescriptions
                            </Typography>
                          )}
                        </Grid>
                      </Grid>
                    )}
                  </EnhancedCardContent>
                </EnhancedCard>
              </Box>
            </FormSection>

            <FormSection>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    cursor: 'pointer'
                  }}
                  onClick={() => toggleSectionExpansion('additionalNotes')}
                >
                  <SectionTitle variant="h6" color="secondary" sx={{ mb: 0 }}>
                    <MedicalIcon /> Additional Notes
                  </SectionTitle>
                  {expandedSections.additionalNotes ?
                    <ExpandLessIcon sx={{ ml: 1, color: 'secondary.main' }} /> :
                    <ExpandMoreIcon sx={{ ml: 1, color: 'secondary.main' }} />
                  }
                </Box>
                <Button
                  size="small"
                  variant="outlined"
                  color="primary"
                  startIcon={<EditIcon />}
                  onClick={() => navigateToField(12, 'notes')}
                >
                  Edit
                </Button>
              </Box>

              {expandedSections.additionalNotes && (
                <EnhancedCard>
                  <EnhancedCardContent>
                    <Typography variant="body1">
                      {formData.notes ? formData.notes : 'No additional notes provided.'}
                    </Typography>
                  </EnhancedCardContent>
                </EnhancedCard>
              )}
            </FormSection>
          </TabPanel>

          {/* Form Actions */}
          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'space-between' }}>
            {/* Left side - Back button or Cancel */}
            <Box>
              {activeStep > 0 ? (
                <Button
                  variant="outlined"
                  onClick={handleBack}
                  startIcon={<ArrowBackIcon />}
                  sx={{ minWidth: 120 }}
                >
                  Back
                </Button>
              ) : (
                <Button
                  variant="outlined"
                  color="secondary"
                  component={Link}
                  to={`/patients/${patientId}/visits`}
                  sx={{ minWidth: 120 }}
                >
                  Cancel
                </Button>
              )}
            </Box>

            {/* Right side - Next or Submit */}
            <Box>
              {activeStep < steps.length - 1 ? (
                <Button
                  variant="contained"
                  color="secondary"
                  onClick={handleNext}
                  endIcon={<NextIcon />}
                  sx={{ minWidth: 120 }}
                >
                  Next
                </Button>
              ) : (
                <Button
                  variant="contained"
                  color="secondary"
                  type="submit"
                  disabled={submitting}
                  startIcon={submitting ? <CircularProgress size={20} color="inherit" /> : <SaveIcon />}
                  sx={{
                    minWidth: 180,
                    fontSize: '1rem',
                    py: 1.2,
                    boxShadow: 3
                  }}
                >
                  {submitting ? 'Saving...' : isEdit ? 'Update Visit' : 'Save Visit'}
                </Button>
              )}
            </Box>
          </Box>
        </form>
      </StyledPaper>
    </Container>
  );
};

export default VisitForm;
