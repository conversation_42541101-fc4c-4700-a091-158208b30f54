import React from 'react';
import { Typography, Box, Grid, Divider } from '@mui/material';

interface DepressionDisplayProps {
  value?: string;
  // Individual PHQ-9 fields
  phq9InterestPleasure?: string;
  phq9FeelingDown?: string;
  phq9SleepIssues?: string;
  phq9Tired?: string;
  phq9Appetite?: string;
  phq9FeelingBad?: string;
  phq9Concentration?: string;
  phq9MovingSpeaking?: string;
  phq9ThoughtsHurting?: string;
  phq9DifficultyLevel?: string;
  phq9Notes?: string;
  depressionScore?: string;
}

// PHQ-9 interpretation
const getPhq9Interpretation = (totalScore: number): string => {
  if (totalScore >= 0 && totalScore <= 4) {
    return "Minimal or no depression";
  } else if (totalScore >= 5 && totalScore <= 9) {
    return "Mild depression";
  } else if (totalScore >= 10 && totalScore <= 14) {
    return "Moderate depression";
  } else if (totalScore >= 15 && totalScore <= 19) {
    return "Moderately severe depression";
  } else if (totalScore >= 20) {
    return "Severe depression";
  } else {
    return "Invalid score";
  }
};

// PHQ-9 severity color
const getPhq9SeverityColor = (totalScore: number): string => {
  if (totalScore >= 0 && totalScore <= 4) {
    return "#4caf50"; // Green - Minimal
  } else if (totalScore >= 5 && totalScore <= 9) {
    return "#8bc34a"; // Light Green - Mild
  } else if (totalScore >= 10 && totalScore <= 14) {
    return "#ffc107"; // Amber - Moderate
  } else if (totalScore >= 15 && totalScore <= 19) {
    return "#ff9800"; // Orange - Moderately Severe
  } else if (totalScore >= 20) {
    return "#f44336"; // Red - Severe
  } else {
    return "#9e9e9e"; // Grey - Invalid
  }
};

const DepressionDisplay: React.FC<DepressionDisplayProps> = ({
  value,
  phq9InterestPleasure,
  phq9FeelingDown,
  phq9SleepIssues,
  phq9Tired,
  phq9Appetite,
  phq9FeelingBad,
  phq9Concentration,
  phq9MovingSpeaking,
  phq9ThoughtsHurting,
  phq9DifficultyLevel,
  phq9Notes,
  depressionScore
}) => {
  // First try to use the individual fields if available
  if (depressionScore !== undefined ||
      phq9InterestPleasure !== undefined ||
      phq9FeelingDown !== undefined) {

    const totalScore = depressionScore ? parseInt(depressionScore) :
      (phq9InterestPleasure ? parseInt(phq9InterestPleasure) : 0) +
      (phq9FeelingDown ? parseInt(phq9FeelingDown) : 0) +
      (phq9SleepIssues ? parseInt(phq9SleepIssues) : 0) +
      (phq9Tired ? parseInt(phq9Tired) : 0) +
      (phq9Appetite ? parseInt(phq9Appetite) : 0) +
      (phq9FeelingBad ? parseInt(phq9FeelingBad) : 0) +
      (phq9Concentration ? parseInt(phq9Concentration) : 0) +
      (phq9MovingSpeaking ? parseInt(phq9MovingSpeaking) : 0) +
      (phq9ThoughtsHurting ? parseInt(phq9ThoughtsHurting) : 0);

    const difficultyLevel = phq9DifficultyLevel ? parseInt(phq9DifficultyLevel) : 0;
    const difficultyText =
      difficultyLevel === 0 ? "Not difficult at all" :
      difficultyLevel === 1 ? "Somewhat difficult" :
      difficultyLevel === 2 ? "Very difficult" :
      difficultyLevel === 3 ? "Extremely difficult" : "Not specified";

    return (
      <Box>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Typography variant="subtitle2" color="primary">PHQ-9 Depression Assessment Results</Typography>
            <Divider sx={{ my: 1 }} />
          </Grid>

          <Grid item xs={12} sm={6}>
            <Typography variant="body2">
              <strong>Total Score:</strong> {totalScore}/27
            </Typography>
            <Typography variant="body2" sx={{ mt: 0.5 }}>
              <strong>Difficulty Level:</strong> {difficultyText}
            </Typography>

            <Box sx={{ mt: 1, p: 1, bgcolor: 'background.paper', borderRadius: 1, border: `1px solid ${getPhq9SeverityColor(totalScore)}` }}>
              <Typography variant="body2" sx={{ color: getPhq9SeverityColor(totalScore) }} fontWeight="bold">
                {getPhq9Interpretation(totalScore)}
              </Typography>
            </Box>
          </Grid>

          {phq9Notes && (
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" fontWeight="medium">
                <strong>Clinical Notes:</strong>
              </Typography>
              <Typography variant="body2" sx={{ mt: 0.5, whiteSpace: 'pre-wrap' }}>
                {phq9Notes}
              </Typography>
            </Grid>
          )}

          {/* Display individual question scores if available */}
          {phq9InterestPleasure !== undefined && (
            <Grid item xs={12}>
              <Typography variant="body2" fontWeight="medium" sx={{ mt: 1 }}>
                <strong>Question Responses:</strong>
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, mt: 0.5 }}>
                <Typography variant="body2">
                  1. Little interest or pleasure: {phq9InterestPleasure}
                </Typography>
                <Typography variant="body2">
                  2. Feeling down, depressed: {phq9FeelingDown}
                </Typography>
                <Typography variant="body2">
                  3. Sleep issues: {phq9SleepIssues}
                </Typography>
                <Typography variant="body2">
                  4. Feeling tired: {phq9Tired}
                </Typography>
                <Typography variant="body2">
                  5. Appetite issues: {phq9Appetite}
                </Typography>
                <Typography variant="body2">
                  6. Feeling bad about yourself: {phq9FeelingBad}
                </Typography>
                <Typography variant="body2">
                  7. Trouble concentrating: {phq9Concentration}
                </Typography>
                <Typography variant="body2">
                  8. Moving/speaking slowly or restless: {phq9MovingSpeaking}
                </Typography>
                <Typography variant="body2">
                  9. Thoughts of self-harm: {phq9ThoughtsHurting}
                </Typography>
              </Box>
            </Grid>
          )}
        </Grid>
      </Box>
    );
  }

  // Fall back to parsing the JSON value if individual fields are not available
  try {
    // Try to parse the JSON value if it exists
    if (!value) {
      return (
        <Box>
          <Typography variant="body2">No depression assessment data available</Typography>
        </Box>
      );
    }

    const assessmentData = JSON.parse(value as string);

    // Check if data is in the expected PHQ-9 format
    if (assessmentData.phq9) {
      const totalScore =
        (assessmentData.phq9.interestPleasure || 0) +
        (assessmentData.phq9.feelingDown || 0) +
        (assessmentData.phq9.sleepIssues || 0) +
        (assessmentData.phq9.tired || 0) +
        (assessmentData.phq9.appetite || 0) +
        (assessmentData.phq9.feelingBad || 0) +
        (assessmentData.phq9.concentration || 0) +
        (assessmentData.phq9.movingSpeaking || 0) +
        (assessmentData.phq9.thoughtsHurting || 0);

      const difficultyLevel = assessmentData.phq9.difficultyLevel || 0;
      const difficultyText =
        difficultyLevel === 0 ? "Not difficult at all" :
        difficultyLevel === 1 ? "Somewhat difficult" :
        difficultyLevel === 2 ? "Very difficult" :
        difficultyLevel === 3 ? "Extremely difficult" : "Not specified";

      return (
        <Box>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="subtitle2" color="primary">PHQ-9 Depression Assessment Results</Typography>
              <Divider sx={{ my: 1 }} />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="body2">
                <strong>Total Score:</strong> {totalScore}/27
              </Typography>
              <Typography variant="body2" sx={{ mt: 0.5 }}>
                <strong>Difficulty Level:</strong> {difficultyText}
              </Typography>

              <Box sx={{ mt: 1, p: 1, bgcolor: 'background.paper', borderRadius: 1, border: `1px solid ${getPhq9SeverityColor(totalScore)}` }}>
                <Typography variant="body2" sx={{ color: getPhq9SeverityColor(totalScore) }} fontWeight="bold">
                  {getPhq9Interpretation(totalScore)}
                </Typography>
              </Box>
            </Grid>

            {assessmentData.phq9.notes && (
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" fontWeight="medium">
                  <strong>Clinical Notes:</strong>
                </Typography>
                <Typography variant="body2" sx={{ mt: 0.5, whiteSpace: 'pre-wrap' }}>
                  {assessmentData.phq9.notes}
                </Typography>
              </Grid>
            )}
          </Grid>
        </Box>
      );
    }
  } catch (e) {
    // If parsing fails, just display the raw value
    return (
      <Box>
        <Typography variant="body2">{value || "No depression assessment data available"}</Typography>
      </Box>
    );
  }

  // If we get here, we couldn't parse the data
  return (
    <Box>
      <Typography variant="body2">No depression assessment data available</Typography>
    </Box>
  );
};

export default DepressionDisplay;
