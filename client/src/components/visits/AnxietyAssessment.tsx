import React, { useState } from 'react'; // useEffect is unused
import {
  Typography,
  Paper,
  // Grid is unused
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  // Button is unused
  Divider,
  Box,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  TextField,
  FormLabel,
  // FormHelperText is unused
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

interface AnxietyAssessmentProps {
  value: string;
  onChange: (value: string) => void;
  // Individual GAD-7 fields
  gad7FeelingNervous?: string;
  gad7StopWorrying?: string;
  gad7WorryingMuch?: string;
  gad7TroubleRelaxing?: string;
  gad7Restless?: string;
  gad7Annoyed?: string;
  gad7FeelingAfraid?: string;
  gad7DifficultyLevel?: string;
  gad7Notes?: string;
  anxietyScore?: string;
  onGad7Change?: (fields: {
    gad7_feeling_nervous: string;
    gad7_stop_worrying: string;
    gad7_worrying_much: string;
    gad7_trouble_relaxing: string;
    gad7_restless: string;
    gad7_annoyed: string;
    gad7_feeling_afraid: string;
    gad7_difficulty_level: string;
    gad7_notes: string;
    anxiety_score: string;
    anxiety_screening: string;
  }) => void;
}

// GAD-7 interpretation
const getGad7Interpretation = (totalScore: number): string => {
  if (totalScore >= 0 && totalScore <= 4) {
    return "Minimal anxiety";
  } else if (totalScore >= 5 && totalScore <= 9) {
    return "Mild anxiety";
  } else if (totalScore >= 10 && totalScore <= 14) {
    return "Moderate anxiety";
  } else if (totalScore >= 15) {
    return "Severe anxiety";
  } else {
    return "Invalid score";
  }
};

// GAD-7 severity color
const getGad7SeverityColor = (totalScore: number): string => {
  if (totalScore >= 0 && totalScore <= 4) {
    return "#4caf50"; // Green - Minimal
  } else if (totalScore >= 5 && totalScore <= 9) {
    return "#8bc34a"; // Light Green - Mild
  } else if (totalScore >= 10 && totalScore <= 14) {
    return "#ffc107"; // Amber - Moderate
  } else if (totalScore >= 15) {
    return "#f44336"; // Red - Severe
  } else {
    return "#9e9e9e"; // Grey - Invalid
  }
};

const AnxietyAssessment: React.FC<AnxietyAssessmentProps> = ({
  value,
  onChange,
  gad7FeelingNervous,
  gad7StopWorrying,
  gad7WorryingMuch,
  gad7TroubleRelaxing,
  gad7Restless,
  gad7Annoyed,
  gad7FeelingAfraid,
  gad7DifficultyLevel,
  gad7Notes,
  anxietyScore,
  onGad7Change
}) => {
  // Initialize state from props or default values
  const [assessmentState, setAssessmentState] = useState(() => {
    // If we have individual field values, use those
    if (
      gad7FeelingNervous !== undefined ||
      gad7StopWorrying !== undefined ||
      gad7WorryingMuch !== undefined ||
      gad7TroubleRelaxing !== undefined ||
      gad7Restless !== undefined ||
      gad7Annoyed !== undefined ||
      gad7FeelingAfraid !== undefined
    ) {
      return {
        gad7: {
          feelingNervous: gad7FeelingNervous ? parseInt(gad7FeelingNervous) : 0,
          stopWorrying: gad7StopWorrying ? parseInt(gad7StopWorrying) : 0,
          worryingMuch: gad7WorryingMuch ? parseInt(gad7WorryingMuch) : 0,
          troubleRelaxing: gad7TroubleRelaxing ? parseInt(gad7TroubleRelaxing) : 0,
          restless: gad7Restless ? parseInt(gad7Restless) : 0,
          annoyed: gad7Annoyed ? parseInt(gad7Annoyed) : 0,
          feelingAfraid: gad7FeelingAfraid ? parseInt(gad7FeelingAfraid) : 0,
          difficultyLevel: gad7DifficultyLevel ? parseInt(gad7DifficultyLevel) : 0,
          notes: gad7Notes || ""
        }
      };
    }

    // Try to parse from JSON if available
    try {
      return value ? JSON.parse(value) : {
        gad7: {
          feelingNervous: 0,
          stopWorrying: 0,
          worryingMuch: 0,
          troubleRelaxing: 0,
          restless: 0,
          annoyed: 0,
          feelingAfraid: 0,
          difficultyLevel: 0,
          notes: ""
        }
      };
    } catch (e) {
      // If parsing fails, initialize with empty scores
      return {
        gad7: {
          feelingNervous: 0,
          stopWorrying: 0,
          worryingMuch: 0,
          troubleRelaxing: 0,
          restless: 0,
          annoyed: 0,
          feelingAfraid: 0,
          difficultyLevel: 0,
          notes: value || ""
        }
      };
    }
  });

  // Calculate total score
  const totalScore =
    assessmentState.gad7.feelingNervous +
    assessmentState.gad7.stopWorrying +
    assessmentState.gad7.worryingMuch +
    assessmentState.gad7.troubleRelaxing +
    assessmentState.gad7.restless +
    assessmentState.gad7.annoyed +
    assessmentState.gad7.feelingAfraid;

  // Update both the JSON state and individual fields
  const updateState = (newState: any) => {
    setAssessmentState(newState);

    // Update the JSON string for backward compatibility
    onChange(JSON.stringify(newState));

    // Calculate the new total score
    const newTotalScore =
      newState.gad7.feelingNervous +
      newState.gad7.stopWorrying +
      newState.gad7.worryingMuch +
      newState.gad7.troubleRelaxing +
      newState.gad7.restless +
      newState.gad7.annoyed +
      newState.gad7.feelingAfraid;

    // Update individual fields if the callback is provided
    if (onGad7Change) {
      // Create a structured notes text for anxiety_screening
      const interpretation = getGad7Interpretation(newTotalScore);

      const notes = `GAD-7 Anxiety Assessment Results:
Total Score: ${newTotalScore}/21
Interpretation: ${interpretation}
Difficulty Level: ${newState.gad7.difficultyLevel === 0 ? "Not difficult at all" :
                   newState.gad7.difficultyLevel === 1 ? "Somewhat difficult" :
                   newState.gad7.difficultyLevel === 2 ? "Very difficult" :
                   newState.gad7.difficultyLevel === 3 ? "Extremely difficult" : "Not specified"}
Clinical Notes: ${newState.gad7.notes || "None"}`;

      // Call the callback with all the fields
      onGad7Change({
        gad7_feeling_nervous: newState.gad7.feelingNervous.toString(),
        gad7_stop_worrying: newState.gad7.stopWorrying.toString(),
        gad7_worrying_much: newState.gad7.worryingMuch.toString(),
        gad7_trouble_relaxing: newState.gad7.troubleRelaxing.toString(),
        gad7_restless: newState.gad7.restless.toString(),
        gad7_annoyed: newState.gad7.annoyed.toString(),
        gad7_feeling_afraid: newState.gad7.feelingAfraid.toString(),
        gad7_difficulty_level: newState.gad7.difficultyLevel.toString(),
        gad7_notes: newState.gad7.notes,
        anxiety_score: newTotalScore.toString(),
        anxiety_screening: notes
      });
    }
  };

  // Handle question response change
  const handleResponseChange = (question: string, value: number) => {
    const newState = {
      ...assessmentState,
      gad7: {
        ...assessmentState.gad7,
        [question]: value
      }
    };

    updateState(newState);
  };

  // Handle difficulty level change
  const handleDifficultyChange = (value: number) => {
    const newState = {
      ...assessmentState,
      gad7: {
        ...assessmentState.gad7,
        difficultyLevel: value
      }
    };

    updateState(newState);
  };

  // Handle notes change
  const handleNotesChange = (notes: string) => {
    const newState = {
      ...assessmentState,
      gad7: {
        ...assessmentState.gad7,
        notes
      }
    };

    updateState(newState);
  };

  // Question options
  const frequencyOptions = [
    { value: 0, label: "Not at all" },
    { value: 1, label: "Several days" },
    { value: 2, label: "More than half the days" },
    { value: 3, label: "Nearly every day" }
  ];

  // Difficulty options
  const difficultyOptions = [
    { value: 0, label: "Not difficult at all" },
    { value: 1, label: "Somewhat difficult" },
    { value: 2, label: "Very difficult" },
    { value: 3, label: "Extremely difficult" }
  ];

  return (
    <Paper elevation={0} sx={{ p: 2 }}>
      <Typography variant="h6" gutterBottom>
        GAD-7 Anxiety Assessment
      </Typography>

      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        Over the last 2 weeks, how often have you been bothered by any of the following problems?
      </Typography>

      <Divider sx={{ mb: 3 }} />

      {/* Question 1 */}
      <Box sx={{ mb: 3 }}>
        <FormControl component="fieldset">
          <FormLabel component="legend">
            1. Feeling nervous, anxious, or on edge
          </FormLabel>
          <RadioGroup
            row
            value={assessmentState.gad7.feelingNervous}
            onChange={(e) => handleResponseChange('feelingNervous', parseInt(e.target.value))}
          >
            {frequencyOptions.map((option) => (
              <FormControlLabel
                key={option.value}
                value={option.value}
                control={<Radio />}
                label={option.label}
              />
            ))}
          </RadioGroup>
        </FormControl>
      </Box>

      {/* Question 2 */}
      <Box sx={{ mb: 3 }}>
        <FormControl component="fieldset">
          <FormLabel component="legend">
            2. Not being able to stop or control worrying
          </FormLabel>
          <RadioGroup
            row
            value={assessmentState.gad7.stopWorrying}
            onChange={(e) => handleResponseChange('stopWorrying', parseInt(e.target.value))}
          >
            {frequencyOptions.map((option) => (
              <FormControlLabel
                key={option.value}
                value={option.value}
                control={<Radio />}
                label={option.label}
              />
            ))}
          </RadioGroup>
        </FormControl>
      </Box>

      {/* Question 3 */}
      <Box sx={{ mb: 3 }}>
        <FormControl component="fieldset">
          <FormLabel component="legend">
            3. Worrying too much about different things
          </FormLabel>
          <RadioGroup
            row
            value={assessmentState.gad7.worryingMuch}
            onChange={(e) => handleResponseChange('worryingMuch', parseInt(e.target.value))}
          >
            {frequencyOptions.map((option) => (
              <FormControlLabel
                key={option.value}
                value={option.value}
                control={<Radio />}
                label={option.label}
              />
            ))}
          </RadioGroup>
        </FormControl>
      </Box>

      {/* Question 4 */}
      <Box sx={{ mb: 3 }}>
        <FormControl component="fieldset">
          <FormLabel component="legend">
            4. Trouble relaxing
          </FormLabel>
          <RadioGroup
            row
            value={assessmentState.gad7.troubleRelaxing}
            onChange={(e) => handleResponseChange('troubleRelaxing', parseInt(e.target.value))}
          >
            {frequencyOptions.map((option) => (
              <FormControlLabel
                key={option.value}
                value={option.value}
                control={<Radio />}
                label={option.label}
              />
            ))}
          </RadioGroup>
        </FormControl>
      </Box>

      {/* Question 5 */}
      <Box sx={{ mb: 3 }}>
        <FormControl component="fieldset">
          <FormLabel component="legend">
            5. Being so restless that it's hard to sit still
          </FormLabel>
          <RadioGroup
            row
            value={assessmentState.gad7.restless}
            onChange={(e) => handleResponseChange('restless', parseInt(e.target.value))}
          >
            {frequencyOptions.map((option) => (
              <FormControlLabel
                key={option.value}
                value={option.value}
                control={<Radio />}
                label={option.label}
              />
            ))}
          </RadioGroup>
        </FormControl>
      </Box>

      {/* Question 6 */}
      <Box sx={{ mb: 3 }}>
        <FormControl component="fieldset">
          <FormLabel component="legend">
            6. Becoming easily annoyed or irritable
          </FormLabel>
          <RadioGroup
            row
            value={assessmentState.gad7.annoyed}
            onChange={(e) => handleResponseChange('annoyed', parseInt(e.target.value))}
          >
            {frequencyOptions.map((option) => (
              <FormControlLabel
                key={option.value}
                value={option.value}
                control={<Radio />}
                label={option.label}
              />
            ))}
          </RadioGroup>
        </FormControl>
      </Box>

      {/* Question 7 */}
      <Box sx={{ mb: 3 }}>
        <FormControl component="fieldset">
          <FormLabel component="legend">
            7. Feeling afraid as if something awful might happen
          </FormLabel>
          <RadioGroup
            row
            value={assessmentState.gad7.feelingAfraid}
            onChange={(e) => handleResponseChange('feelingAfraid', parseInt(e.target.value))}
          >
            {frequencyOptions.map((option) => (
              <FormControlLabel
                key={option.value}
                value={option.value}
                control={<Radio />}
                label={option.label}
              />
            ))}
          </RadioGroup>
        </FormControl>
      </Box>

      <Divider sx={{ my: 3 }} />

      {/* Total Score */}
      <Box sx={{ mb: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: `1px solid ${getGad7SeverityColor(totalScore)}` }}>
        <Typography variant="subtitle1" gutterBottom>
          Total Score: {totalScore}/21
        </Typography>
        <Typography variant="body1" sx={{ color: getGad7SeverityColor(totalScore), fontWeight: 'bold' }}>
          Interpretation: {getGad7Interpretation(totalScore)}
        </Typography>
      </Box>

      {/* Difficulty Question */}
      <Box sx={{ mb: 3 }}>
        <FormControl component="fieldset">
          <FormLabel component="legend">
            If you checked off any problems, how difficult have these problems made it for you to do your work, take care of things at home, or get along with other people?
          </FormLabel>
          <RadioGroup
            row
            value={assessmentState.gad7.difficultyLevel}
            onChange={(e) => handleDifficultyChange(parseInt(e.target.value))}
          >
            {difficultyOptions.map((option) => (
              <FormControlLabel
                key={option.value}
                value={option.value}
                control={<Radio />}
                label={option.label}
              />
            ))}
          </RadioGroup>
        </FormControl>
      </Box>

      {/* Clinical Notes */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          label="Clinical Notes"
          multiline
          rows={4}
          value={assessmentState.gad7.notes}
          onChange={(e) => handleNotesChange(e.target.value)}
          variant="outlined"
        />
      </Box>

      {/* Treatment Recommendations */}
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography>Treatment Recommendations</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography variant="subtitle2" gutterBottom>
            Based on GAD-7 Score:
          </Typography>
          <Box component="ul" sx={{ pl: 2 }}>
            <Box component="li" sx={{ mb: 1 }}>
              <Typography variant="body2">
                <strong>0-4 (Minimal):</strong> Monitor; may not require treatment
              </Typography>
            </Box>
            <Box component="li" sx={{ mb: 1 }}>
              <Typography variant="body2">
                <strong>5-9 (Mild):</strong> Watchful waiting; consider repeating GAD-7 at follow-up
              </Typography>
            </Box>
            <Box component="li" sx={{ mb: 1 }}>
              <Typography variant="body2">
                <strong>10-14 (Moderate):</strong> Consider treatment with psychotherapy, medication, or both
              </Typography>
            </Box>
            <Box component="li">
              <Typography variant="body2">
                <strong>15-21 (Severe):</strong> Active treatment with psychotherapy, medication, or both; consider referral to mental health specialist
              </Typography>
            </Box>
          </Box>
        </AccordionDetails>
      </Accordion>
    </Paper>
  );
};

export default AnxietyAssessment;
