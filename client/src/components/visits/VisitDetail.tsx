import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { getVisitById, deleteVisit, getPatientVisits } from '../../services/visitService';
import { Visit } from '../../types';
import useSystemSettings from '../../hooks/useSystemSettings';
import { formatDate, formatDateTime } from '../../utils/dateTimeUtils';
import { Box, Button, Container, Dialog, DialogActions, DialogContent, DialogContentText, DialogTitle, Divider, Grid, Paper, Typography, Chip, IconButton, Alert } from '@mui/material';
import LoadingSpinner from '../common/LoadingSpinner';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import PersonIcon from '@mui/icons-material/Person';
import LocalHospitalIcon from '@mui/icons-material/LocalHospital';
import MonitorHeartIcon from '@mui/icons-material/MonitorHeart';
import ScienceIcon from '@mui/icons-material/Science';
import MedicationIcon from '@mui/icons-material/Medication';
import FitnessCenterIcon from '@mui/icons-material/FitnessCenter';
import NoteAltIcon from '@mui/icons-material/NoteAlt';
import HealthMetricsChart from './HealthMetricsChart';
import MentalHealthDisplay from './MentalHealthDisplay';
import VisitPrescriptions from './VisitPrescriptions';
import CognitiveHealthDisplay from './CognitiveHealthDisplay';

// Add these type definitions before the getNormalRange function
type Range = {
  min: number;
  max: number;
  text: string;
};

type AgeSpecificRange = {
  [key: string]: Range;
};

type MetricRanges = {
  [key: string]: AgeSpecificRange | { all: Range };
};

const getNormalRange = (metric: string, value: number | undefined, age: string = 'adult') => {
  if (!value) return '';

  const ranges: MetricRanges = {
    bp_systolic: {
      adult: { min: 120, max: 129, text: '<120 Optimal, 120-129 Normal' },
      elderly: { min: 130, max: 139, text: '<130 Optimal, 130-139 Normal' }
    },
    bp_diastolic: {
      adult: { min: 80, max: 84, text: '<80 Optimal, 80-84 Normal' },
      elderly: { min: 80, max: 89, text: '<80 Optimal, 80-89 Normal' }
    },
    heart_rate: {
      adult: { min: 60, max: 100, text: '60-100 bpm' },
      athlete: { min: 40, max: 60, text: '40-60 bpm' },
      elderly: { min: 60, max: 100, text: '60-100 bpm' }
    },
    cholesterol_total: {
      all: { min: 0, max: 200, text: '<200 Optimal, 200-239 Borderline' }
    },
    hdl_cholesterol: {
      male: { min: 40, max: 60, text: '≥40 Normal, ≥60 Optimal' },
      female: { min: 50, max: 60, text: '≥50 Normal, ≥60 Optimal' }
    },
    ldl_cholesterol: {
      all: { min: 0, max: 100, text: '<100 Optimal, 100-129 Near Optimal' }
    },
    triglycerides: {
      all: { min: 0, max: 150, text: '<150 Normal, 150-199 Borderline' }
    },
    blood_glucose: {
      all: { min: 70, max: 99, text: '70-99 Normal, 100-125 Prediabetes' }
    },
    hba1c: {
      all: { min: 0, max: 5.7, text: '<5.7 Normal, 5.7-6.4 Prediabetes' }
    },
    bmi: {
      all: { min: 18.5, max: 24.9, text: '18.5-24.9 Normal, 25-29.9 Overweight' }
    },
    temperature: {
      all: { min: 36.1, max: 37.2, text: '36.1-37.2°C Normal' }
    },
    respiratory_rate: {
      adult: { min: 12, max: 20, text: '12-20 breaths/min' },
      elderly: { min: 12, max: 28, text: '12-28 breaths/min' }
    },
    pulse_oximetry: {
      all: { min: 95, max: 100, text: '95-100% Normal' }
    },
    creatinine: {
      all: { min: 0.7, max: 1.3, text: '0.7-1.3 mg/dL Normal' }
    },
    egfr: {
      all: { min: 90, max: 120, text: '≥90 Normal, 60-89 Mild Decrease, <60 Moderate-Severe Decrease' }
    }
  };

  const metricRanges = ranges[metric];
  if (!metricRanges) return '';

  const range = 'all' in metricRanges
    ? metricRanges.all
    : metricRanges[age as keyof typeof metricRanges];

  if (!range) return '';

  const status = value < range.min ? 'Low' :
                 value > range.max ? 'High' :
                 'Normal';

  return `(${range.text}) - ${status}`;
};

const VisitDetail: React.FC = () => {
  const { patientId, visitId } = useParams<{ patientId: string; visitId: string }>();
  const navigate = useNavigate();
  const { settings } = useSystemSettings();

  const [visit, setVisit] = useState<Visit | null>(null);
  const [allVisits, setAllVisits] = useState<Visit[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [openDeleteDialog, setOpenDeleteDialog] = useState<boolean>(false);

  useEffect(() => {
    const fetchData = async () => {
      if (!visitId || !patientId) return;

      setLoading(true);
      try {
        const [visitData, allVisitsData] = await Promise.all([
          getVisitById(parseInt(visitId)),
          getPatientVisits(parseInt(patientId))
        ]);
        setVisit(visitData);
        setAllVisits(allVisitsData);
      } catch (err) {
        console.error('Error fetching visit:', err);
        setError('Failed to fetch visit data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [visitId, patientId]);

  const handleDelete = async () => {
    if (!visitId) return;

    try {
      await deleteVisit(parseInt(visitId));
      navigate(`/patients/${patientId}/visits`);
    } catch (err) {
      console.error('Error deleting visit:', err);
      setError('Failed to delete visit. Please try again.');
    } finally {
      setOpenDeleteDialog(false);
    }
  };

  // Format date using the system timezone setting
  const formatDateWithTimezone = (dateString: string) => {
    return formatDate(dateString, settings.timezone);
  };

  // Format date and time using the system timezone setting
  const formatDateTimeWithTimezone = (dateString: string) => {
    return formatDateTime(dateString, settings.timezone);
  };

  if (loading) {
    return <LoadingSpinner size="large" message="Loading visit details..." />;
  }

  if (error || !visit) {
    return (
      <Container maxWidth="md">
        <Paper elevation={3} sx={{ p: 3, mt: 4 }}>
          <Typography color="error" variant="h6">
            {error || 'Visit not found'}
          </Typography>
          <Button
            variant="contained"
            onClick={() => navigate(`/patients/${patientId}/visits`)}
            sx={{
              mt: 2,
              backgroundColor: '#2C4B2B',
              '&:hover': {
                backgroundColor: '#3E6A3D'
              }
            }}
          >
            Back to Visits List
          </Button>
        </Paper>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg">
      <Box sx={{ mt: 4, mb: 4 }}>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Button
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate(`/patients/${patientId}/visits`)}
            variant="outlined"
            sx={{
              borderRadius: 2,
              borderColor: '#2C4B2B',
              color: '#2C4B2B',
              '&:hover': {
                borderColor: '#3E6A3D',
                backgroundColor: 'rgba(44, 75, 43, 0.04)'
              }
            }}
          >
            Back to Visits
          </Button>
          <Typography variant="h4" component="h1">
            Visit Details
          </Typography>
          <Box>
            <IconButton
              component={Link}
              to={`/patients/${patientId}/visits/${visitId}/edit`}
              title="Edit Visit"
              sx={{ color: '#2C4B2B' }}
            >
              <EditIcon />
            </IconButton>
            <IconButton
              onClick={() => setOpenDeleteDialog(true)}
              title="Delete Visit"
              sx={{ color: '#D97B3A' }}
            >
              <DeleteIcon />
            </IconButton>
          </Box>
        </Box>

        {/* Basic Visit Information */}
        <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
          <Box display="flex" alignItems="center" mb={2}>
            <CalendarMonthIcon fontSize="large" sx={{ mr: 2, color: '#2C4B2B' }} />
            <Typography variant="h5">
              Visit on {formatDateWithTimezone(visit.visit_date)}
            </Typography>
            {visit.diagnosis && (
              <Chip
                label={visit.diagnosis}
                sx={{
                  ml: 2,
                  backgroundColor: 'rgba(44, 75, 43, 0.1)',
                  color: '#2C4B2B',
                  borderColor: 'rgba(44, 75, 43, 0.3)'
                }}
              />
            )}
          </Box>
          <Divider sx={{ mb: 3 }} />

          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <Box display="flex" alignItems="center" mb={1}>
                <PersonIcon sx={{ mr: 1, color: '#2C4B2B' }} />
                <Typography variant="subtitle1">Patient</Typography>
              </Box>
              <Typography variant="body1">
                {visit.patient_first_name} {visit.patient_last_name} ({visit.patient_unique_id})
              </Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Box display="flex" alignItems="center" mb={1}>
                <LocalHospitalIcon sx={{ mr: 1, color: '#2C4B2B' }} />
                <Typography variant="subtitle1">Attending Doctor</Typography>
              </Box>
              <Typography variant="body1">
                {visit.doctor_first_name && visit.doctor_last_name
                  ? `Dr. ${visit.doctor_first_name} ${visit.doctor_last_name}`
                  : 'No doctor assigned'}
                {visit.doctor_specialty && ` (${visit.doctor_specialty})`}
              </Typography>
            </Grid>

            {visit.visit_reason && (
              <Grid item xs={12}>
                <Typography variant="subtitle1" gutterBottom>Reason for Visit</Typography>
                <Typography variant="body1" paragraph>
                  {visit.visit_reason}
                </Typography>
              </Grid>
            )}
          </Grid>
        </Paper>

        {/* Vital Signs */}
        <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
          <Box display="flex" alignItems="center" mb={2}>
            <MonitorHeartIcon fontSize="large" sx={{ mr: 2, color: '#2C4B2B' }} />
            <Typography variant="h5">Vital Signs</Typography>
          </Box>
          <Divider sx={{ mb: 3 }} />

          <Grid container spacing={3}>
            {/* Blood Pressure */}
            {visit.lying_bp_systolic && visit.lying_bp_diastolic && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Lying Blood Pressure</Typography>
                <Typography variant="body1">
                  {visit.lying_bp_systolic}/{visit.lying_bp_diastolic} mmHg
                  <Typography variant="body2" color="textSecondary">
                    Systolic {getNormalRange('bp_systolic', visit.lying_bp_systolic, 'adult')}
                    <br />
                    Diastolic {getNormalRange('bp_diastolic', visit.lying_bp_diastolic, 'adult')}
                  </Typography>
                </Typography>
              </Grid>
            )}

            {visit.lying_heart_rate && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Lying Heart Rate</Typography>
                <Typography variant="body1">
                  {visit.lying_heart_rate} bpm
                  <Typography variant="body2" color="textSecondary">
                    {getNormalRange('heart_rate', visit.lying_heart_rate, 'adult')}
                  </Typography>
                </Typography>
              </Grid>
            )}

            {(visit.sitting_bp_systolic || visit.sitting_bp_diastolic) && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Sitting Blood Pressure</Typography>
                <Typography variant="body1">
                  {visit.sitting_bp_systolic || '-'}/{visit.sitting_bp_diastolic || '-'} mmHg
                </Typography>
              </Grid>
            )}

            {(visit.standing_bp_systolic || visit.standing_bp_diastolic) && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Standing Blood Pressure</Typography>
                <Typography variant="body1">
                  {visit.standing_bp_systolic || '-'}/{visit.standing_bp_diastolic || '-'} mmHg
                </Typography>
              </Grid>
            )}

            {visit.standing_heart_rate && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Standing Heart Rate</Typography>
                <Typography variant="body1">
                  {visit.standing_heart_rate} bpm
                  <Typography variant="body2" color="textSecondary">
                    {getNormalRange('heart_rate', visit.standing_heart_rate, 'adult')}
                  </Typography>
                </Typography>
              </Grid>
            )}

            {/* Orthostatic Hypotension Assessment */}
            {visit.lying_bp_systolic && visit.standing_bp_systolic &&
              visit.lying_bp_diastolic && visit.standing_bp_diastolic &&
              visit.lying_heart_rate && visit.standing_heart_rate && (
              <Grid item xs={12}>
                <Paper elevation={1} sx={{ p: 2, mt: 2, bgcolor: 'background.default' }}>
                  <Typography variant="subtitle1" gutterBottom>
                    Orthostatic Hypotension Assessment
                  </Typography>

                  {(() => {
                    const systolicDiff = visit.lying_bp_systolic - visit.standing_bp_systolic;
                    const diastolicDiff = visit.lying_bp_diastolic - visit.standing_bp_diastolic;
                    const heartRateDiff = visit.standing_heart_rate - visit.lying_heart_rate;

                    const hasOrthostaticHypotension = systolicDiff >= 20 || diastolicDiff >= 10;
                    const hasCompensatoryTachycardia = heartRateDiff >= 20;

                    let assessmentText = '';
                    let severity = 'info';

                    if (hasOrthostaticHypotension) {
                      assessmentText = `Orthostatic Hypotension detected: Systolic BP drop of ${systolicDiff} mmHg (≥20 mmHg is significant) and/or Diastolic BP drop of ${diastolicDiff} mmHg (≥10 mmHg is significant).`;
                      severity = 'warning';

                      if (hasCompensatoryTachycardia) {
                        assessmentText += ` Compensatory tachycardia present: Heart rate increase of ${heartRateDiff} bpm (≥20 bpm).`;
                      }
                    } else if (hasCompensatoryTachycardia) {
                      assessmentText = `No significant blood pressure drop, but compensatory tachycardia present: Heart rate increase of ${heartRateDiff} bpm (≥20 bpm). Further monitoring recommended.`;
                      severity = 'info';
                    } else {
                      assessmentText = `No orthostatic hypotension detected: Systolic BP drop of ${systolicDiff} mmHg (<20 mmHg), Diastolic BP drop of ${diastolicDiff} mmHg (<10 mmHg), Heart rate increase of ${heartRateDiff} bpm (<20 bpm).`;
                      severity = 'success';
                    }

                    return (
                      <Alert severity={severity as any} sx={{ mt: 1 }}>
                        {assessmentText}
                      </Alert>
                    );
                  })()}
                </Paper>
              </Grid>
            )}

            {/* Other Vitals */}
            {visit.heart_rhythm && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Heart Rhythm</Typography>
                <Typography variant="body1">{visit.heart_rhythm}</Typography>
              </Grid>
            )}

            {visit.temperature && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Temperature</Typography>
                <Typography variant="body1">{visit.temperature} °C</Typography>
              </Grid>
            )}

            {visit.respiratory_rate && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Respiratory Rate</Typography>
                <Typography variant="body1">{visit.respiratory_rate} breaths/min</Typography>
              </Grid>
            )}

            {visit.pulse_oximetry && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Pulse Oximetry</Typography>
                <Typography variant="body1">{visit.pulse_oximetry}%</Typography>
              </Grid>
            )}

            {/* If no vital signs recorded */}
            {!(visit.lying_bp_systolic || visit.lying_bp_diastolic ||
               visit.sitting_bp_systolic || visit.sitting_bp_diastolic ||
               visit.standing_bp_systolic || visit.standing_bp_diastolic ||
               visit.heart_rhythm || visit.temperature || visit.respiratory_rate ||
               visit.pulse_oximetry) && (
              <Grid item xs={12}>
                <Typography variant="body1" color="textSecondary">No vital signs recorded for this visit.</Typography>
              </Grid>
            )}
          </Grid>
        </Paper>

        {/* Lab Results */}
        <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
          <Box display="flex" alignItems="center" mb={2}>
            <ScienceIcon fontSize="large" sx={{ mr: 2, color: '#2C4B2B' }} />
            <Typography variant="h5">Lab Results</Typography>
          </Box>
          <Divider sx={{ mb: 3 }} />

          <Grid container spacing={3}>
            {visit.blood_glucose && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Blood Glucose</Typography>
                <Typography variant="body1">
                  {visit.blood_glucose} mg/dL
                  <Typography variant="body2" color="textSecondary">
                    {getNormalRange('blood_glucose', visit.blood_glucose)}
                  </Typography>
                </Typography>
              </Grid>
            )}

            {visit.hba1c && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">HbA1c</Typography>
                <Typography variant="body1">
                  {visit.hba1c}%
                  <Typography variant="body2" color="textSecondary">
                    {getNormalRange('hba1c', visit.hba1c)}
                  </Typography>
                </Typography>
              </Grid>
            )}

            {visit.cholesterol_total && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Total Cholesterol</Typography>
                <Typography variant="body1">
                  {visit.cholesterol_total} mg/dL
                  <Typography variant="body2" color="textSecondary">
                    {getNormalRange('cholesterol_total', visit.cholesterol_total)}
                  </Typography>
                </Typography>
              </Grid>
            )}

            {visit.hdl_cholesterol && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">HDL Cholesterol</Typography>
                <Typography variant="body1">
                  {visit.hdl_cholesterol} mg/dL
                  <Typography variant="body2" color="textSecondary">
                    {getNormalRange('hdl_cholesterol', visit.hdl_cholesterol, 'male')}
                  </Typography>
                </Typography>
              </Grid>
            )}

            {visit.ldl_cholesterol && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">LDL Cholesterol</Typography>
                <Typography variant="body1">
                  {visit.ldl_cholesterol} mg/dL
                  <Typography variant="body2" color="textSecondary">
                    {getNormalRange('ldl_cholesterol', visit.ldl_cholesterol)}
                  </Typography>
                </Typography>
              </Grid>
            )}

            {visit.triglycerides && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Triglycerides</Typography>
                <Typography variant="body1">
                  {visit.triglycerides} mg/dL
                  <Typography variant="body2" color="textSecondary">
                    {getNormalRange('triglycerides', visit.triglycerides)}
                  </Typography>
                </Typography>
              </Grid>
            )}

            {visit.creatinine && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Creatinine</Typography>
                <Typography variant="body1">
                  {visit.creatinine} mg/dL
                  <Typography variant="body2" color="textSecondary">
                    {getNormalRange('creatinine', visit.creatinine)}
                  </Typography>
                </Typography>
              </Grid>
            )}

            {visit.egfr && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">eGFR</Typography>
                <Typography variant="body1">
                  {visit.egfr} mL/min/1.73m²
                  <Typography variant="body2" color="textSecondary">
                    {getNormalRange('egfr', visit.egfr)}
                  </Typography>
                </Typography>
              </Grid>
            )}

            {/* Additional Lab Results */}
            {visit.bilirubin_t && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Bilirubin (Total)</Typography>
                <Typography variant="body1">{visit.bilirubin_t} mg/dL</Typography>
              </Grid>
            )}

            {visit.magnesium && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Magnesium</Typography>
                <Typography variant="body1">{visit.magnesium} mg/dL</Typography>
              </Grid>
            )}

            {visit.albumin && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Albumin</Typography>
                <Typography variant="body1">{visit.albumin} g/dL</Typography>
              </Grid>
            )}

            {visit.parathyroid_hormone && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Parathyroid Hormone</Typography>
                <Typography variant="body1">{visit.parathyroid_hormone} pg/mL</Typography>
              </Grid>
            )}

            {visit.alkaline_phosphatase_bone && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Alkaline Phosphatase (Bone)</Typography>
                <Typography variant="body1">{visit.alkaline_phosphatase_bone} U/L</Typography>
              </Grid>
            )}

            {visit.hemoglobin && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Hemoglobin</Typography>
                <Typography variant="body1">{visit.hemoglobin} g/dL</Typography>
              </Grid>
            )}

            {visit.ferritin && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Ferritin</Typography>
                <Typography variant="body1">{visit.ferritin} ng/mL</Typography>
              </Grid>
            )}

            {visit.vitamin_b12 && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Vitamin B12</Typography>
                <Typography variant="body1">{visit.vitamin_b12} pg/mL</Typography>
              </Grid>
            )}

            {visit.folate && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Folate</Typography>
                <Typography variant="body1">{visit.folate} ng/mL</Typography>
              </Grid>
            )}

            {visit.free_t4 && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Free T4</Typography>
                <Typography variant="body1">{visit.free_t4} ng/dL</Typography>
              </Grid>
            )}

            {visit.free_t3 && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Free T3</Typography>
                <Typography variant="body1">{visit.free_t3} pg/mL</Typography>
              </Grid>
            )}

            {/* If no lab results recorded */}
            {!(visit.blood_glucose || visit.hba1c || visit.cholesterol_total ||
               visit.hdl_cholesterol || visit.ldl_cholesterol || visit.triglycerides ||
               visit.creatinine || visit.egfr || visit.bilirubin_t ||
               visit.magnesium || visit.albumin || visit.parathyroid_hormone ||
               visit.alkaline_phosphatase_bone || visit.hemoglobin || visit.ferritin ||
               visit.vitamin_b12 || visit.folate || visit.free_t4 || visit.free_t3) && (
              <Grid item xs={12}>
                <Typography variant="body1" color="textSecondary">No lab results recorded for this visit.</Typography>
              </Grid>
            )}
          </Grid>
        </Paper>

        {/* Medications */}
        <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
          <Box display="flex" alignItems="center" mb={2}>
            <MedicationIcon fontSize="large" sx={{ mr: 2 }} />
            <Typography variant="h5">Medications</Typography>
          </Box>
          <Divider sx={{ mb: 3 }} />

          <Grid container spacing={3}>
            {visit.current_medications && (
              <Grid item xs={12}>
                <Typography variant="subtitle2" color="textSecondary">Current Medications</Typography>
                <Typography variant="body1" style={{ whiteSpace: 'pre-line' }}>
                  {visit.current_medications}
                </Typography>
              </Grid>
            )}

            {visit.medication_adherence && (
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">Medication Adherence (1-10)</Typography>
                <Typography variant="body1">{visit.medication_adherence}</Typography>
              </Grid>
            )}

            {visit.medication_side_effects && (
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="textSecondary">Medication Side Effects</Typography>
                <Typography variant="body1">{visit.medication_side_effects}</Typography>
              </Grid>
            )}

            {/* If no medication info recorded */}
            {!(visit.current_medications || visit.medication_adherence || visit.medication_side_effects) && (
              <Grid item xs={12}>
                <Typography variant="body1" color="textSecondary">No medication information recorded for this visit.</Typography>
              </Grid>
            )}
          </Grid>
        </Paper>

        {/* Prescriptions */}
        {visitId && <VisitPrescriptions visitId={parseInt(visitId)} />}

        {/* Health Assessment */}
        <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
          <Box display="flex" alignItems="center" mb={2}>
            <FitnessCenterIcon fontSize="large" sx={{ mr: 2 }} />
            <Typography variant="h5">Health Assessment</Typography>
          </Box>
          <Divider sx={{ mb: 3 }} />

          <Grid container spacing={3}>
            {visit.weight && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Weight</Typography>
                <Typography variant="body1">
                  {visit.weight} kg
                </Typography>
              </Grid>
            )}

            {visit.bmi && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">BMI</Typography>
                <Typography variant="body1">
                  {visit.bmi}
                  <Typography variant="body2" color="textSecondary">
                    {getNormalRange('bmi', visit.bmi)}
                  </Typography>
                </Typography>
              </Grid>
            )}

            {visit.pain_level && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Pain Level (1-10)</Typography>
                <Typography variant="body1">{visit.pain_level}</Typography>
              </Grid>
            )}

            {visit.pain_location && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Pain Location</Typography>
                <Typography variant="body1">{visit.pain_location}</Typography>
              </Grid>
            )}

            {visit.cognitive_status && (
              <Grid item xs={12}>
                <Typography variant="subtitle2" color="textSecondary">Cognitive Assessment</Typography>
                <CognitiveHealthDisplay value={visit.cognitive_status} />
              </Grid>
            )}

            {visit.mental_health_assessment && (
              <Grid item xs={12}>
                <Typography variant="subtitle2" color="textSecondary">Mental Health Assessment</Typography>
                <MentalHealthDisplay value={visit.mental_health_assessment} />
              </Grid>
            )}

            {visit.activity_level && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Activity Level</Typography>
                <Typography variant="body1">{visit.activity_level}</Typography>
              </Grid>
            )}

            {visit.nutritional_status && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Nutritional Status</Typography>
                <Typography variant="body1">{visit.nutritional_status}</Typography>
              </Grid>
            )}

            {visit.hydration_status && (
              <Grid item xs={12} sm={4}>
                <Typography variant="subtitle2" color="textSecondary">Hydration Status</Typography>
                <Typography variant="body1">{visit.hydration_status}</Typography>
              </Grid>
            )}

            {/* If no assessment info recorded */}
            {!(visit.weight || visit.bmi || visit.pain_level || visit.pain_location ||
               visit.cognitive_status || visit.mental_health_assessment || visit.activity_level ||
               visit.nutritional_status || visit.hydration_status) && (
              <Grid item xs={12}>
                <Typography variant="body1" color="textSecondary">No health assessment data recorded for this visit.</Typography>
              </Grid>
            )}
          </Grid>
        </Paper>

        {/* Diagnosis & Plan */}
        <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
          <Box display="flex" alignItems="center" mb={2}>
            <NoteAltIcon fontSize="large" sx={{ mr: 2 }} />
            <Typography variant="h5">Diagnosis & Treatment Plan</Typography>
          </Box>
          <Divider sx={{ mb: 3 }} />

          <Grid container spacing={3}>
            {visit.notes && (
              <Grid item xs={12}>
                <Typography variant="subtitle2" color="textSecondary">Notes</Typography>
                <Typography variant="body1" paragraph style={{ whiteSpace: 'pre-line' }}>
                  {visit.notes}
                </Typography>
              </Grid>
            )}

            {visit.diagnosis && (
              <Grid item xs={12}>
                <Typography variant="subtitle2" color="textSecondary">Diagnosis</Typography>
                <Typography variant="body1" paragraph style={{ whiteSpace: 'pre-line' }}>
                  {visit.diagnosis}
                </Typography>
              </Grid>
            )}

            {visit.treatment_plan && (
              <Grid item xs={12}>
                <Typography variant="subtitle2" color="textSecondary">Treatment Plan</Typography>
                <Typography variant="body1" paragraph style={{ whiteSpace: 'pre-line' }}>
                  {visit.treatment_plan}
                </Typography>
              </Grid>
            )}

            {visit.follow_up_instructions && (
              <Grid item xs={12}>
                <Typography variant="subtitle2" color="textSecondary">Follow-up Instructions</Typography>
                <Typography variant="body1" paragraph style={{ whiteSpace: 'pre-line' }}>
                  {visit.follow_up_instructions}
                </Typography>
              </Grid>
            )}

            {/* If no diagnosis/plan recorded */}
            {!(visit.notes || visit.diagnosis || visit.treatment_plan || visit.follow_up_instructions) && (
              <Grid item xs={12}>
                <Typography variant="body1" color="textSecondary">No diagnosis or treatment plan recorded for this visit.</Typography>
              </Grid>
            )}
          </Grid>
        </Paper>

        {/* Health Metrics Visualization */}
        <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
          <Box display="flex" alignItems="center" mb={2}>
            <MonitorHeartIcon fontSize="large" sx={{ mr: 2, color: '#2C4B2B' }} />
            <Typography variant="h5">Health Metrics Over Time</Typography>
          </Box>
          <Divider sx={{ mb: 3 }} />

          {allVisits.length > 1 ? (
            <HealthMetricsChart visits={allVisits} />
          ) : (
            <Box sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="body1" color="textSecondary">
                Health metrics visualization requires at least two visits to show trends over time.
              </Typography>
            </Box>
          )}
        </Paper>

        {/* Visit Record Information */}
        <Paper elevation={3} sx={{ p: 3, mb: 3 }}>
          <Box display="flex" alignItems="center" mb={2}>
            <NoteAltIcon fontSize="large" sx={{ mr: 2, color: '#2C4B2B' }} />
            <Typography variant="h5">Record Information</Typography>
          </Box>
          <Divider sx={{ mb: 3 }} />

          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="textSecondary">Created By</Typography>
              <Typography variant="body1">{visit.created_by_username || 'Unknown'}</Typography>
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="subtitle2" color="textSecondary">Created On</Typography>
              <Typography variant="body1">{formatDateTimeWithTimezone(visit.created_at)}</Typography>
            </Grid>

            {visit.updated_at && visit.updated_at !== visit.created_at && (
              <>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="textSecondary">Last Edited By</Typography>
                  <Typography variant="body1">{visit.last_edited_by_username || 'Unknown'}</Typography>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2" color="textSecondary">Last Edited On</Typography>
                  <Typography variant="body1">{formatDateTimeWithTimezone(visit.updated_at)}</Typography>
                </Grid>
              </>
            )}
          </Grid>
        </Paper>

        {/* Delete Confirmation Dialog */}
        <Dialog
          open={openDeleteDialog}
          onClose={() => setOpenDeleteDialog(false)}
          aria-labelledby="alert-dialog-title"
          aria-describedby="alert-dialog-description"
        >
          <DialogTitle id="alert-dialog-title">
            Confirm Delete Visit
          </DialogTitle>
          <DialogContent>
            <DialogContentText id="alert-dialog-description">
              Are you sure you want to delete this visit? This action cannot be undone.
            </DialogContentText>
          </DialogContent>
          <DialogActions>
            <Button
              onClick={() => setOpenDeleteDialog(false)}
              sx={{
                color: '#2C4B2B',
                '&:hover': {
                  backgroundColor: 'rgba(44, 75, 43, 0.04)'
                }
              }}
            >
              Cancel
            </Button>
            <Button
              onClick={handleDelete}
              sx={{
                color: '#D97B3A',
                '&:hover': {
                  backgroundColor: 'rgba(217, 123, 58, 0.04)'
                }
              }}
              autoFocus
            >
              Delete
            </Button>
          </DialogActions>
        </Dialog>
      </Box>
    </Container>
  );
};

export default VisitDetail;