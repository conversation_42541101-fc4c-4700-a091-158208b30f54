import React, { useState } from 'react';
import {
  Box,
  Button,
  Divider,
  Grid,
  Paper,
  TextField,
  Typography,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Alert
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import MedicationIcon from '@mui/icons-material/Medication';
import WarningIcon from '@mui/icons-material/Warning';
import BeersCriteriaAlert from '../beers/BeersCriteriaAlert';
import {
  checkMedications,
  saveAlerts,
  saveOverrides,
  BeersCriteriaAlert as BeersCriteriaAlertType,
  BeersCriteriaOverride
} from '../../services/beersCriteriaService';

interface MedicationSectionProps {
  formData: any;
  onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  patientId: number;
  visitId?: string;
}

const MedicationSection: React.FC<MedicationSectionProps> = ({
  formData,
  onChange,
  patientId,
  visitId
}) => {
  const [checking, setChecking] = useState<boolean>(false);
  const [alerts, setAlerts] = useState<BeersCriteriaAlertType[]>([]);
  const [overrides, setOverrides] = useState<BeersCriteriaOverride[]>([]);
  const [error, setError] = useState<string | null>(null);

  // Parse medications from the current_medications field
  const parseMedications = (): string[] => {
    if (!formData.current_medications) return [];

    // Split by newlines, commas, or semicolons
    return formData.current_medications
      .split(/[\n,;]+/)
      .map((med: string) => med.trim())
      .filter((med: string) => med.length > 0);
  };

  // Check medications against BEERS criteria
  const handleCheckMedications = async () => {
    const medications = parseMedications();

    if (medications.length === 0) {
      setError('Please enter at least one medication to check');
      return;
    }

    setChecking(true);
    setError(null);

    try {
      const result = await checkMedications(medications, patientId);
      setAlerts(result);

      // If we have a visitId, save the alerts
      if (visitId) {
        await saveAlerts(parseInt(visitId), result);
      }
    } catch (err) {
      console.error('Error checking medications:', err);
      setError('Failed to check medications against BEERS criteria');
    } finally {
      setChecking(false);
    }
  };

  // Handle override of a BEERS criteria alert
  const handleOverride = async (alert: BeersCriteriaAlertType, reason: string) => {
    const override: BeersCriteriaOverride = {
      alert,
      reason,
      overridden_by: 0, // This will be set by the server based on the authenticated user
      overridden_at: new Date().toISOString()
    };

    // Add to local overrides
    setOverrides(prev => [...prev, override]);

    // Remove from alerts
    setAlerts(prev => prev.filter(a =>
      a.medication !== alert.medication ||
      a.criterion.criteria_id !== alert.criterion.criteria_id
    ));

    // If we have a visitId, save the overrides
    if (visitId) {
      try {
        await saveOverrides(parseInt(visitId), [...overrides, override]);
      } catch (err) {
        console.error('Error saving overrides:', err);
        // Don't show an error to the user, as the override was still applied locally
      }
    }
  };

  return (
    <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
      <Box display="flex" alignItems="center" mb={2}>
        <MedicationIcon fontSize="large" sx={{ mr: 2 }} />
        <Typography variant="h5">Medications</Typography>
      </Box>
      <Divider sx={{ mb: 3 }} />

      {/* BEERS Criteria Alerts */}
      {alerts.length > 0 && (
        <BeersCriteriaAlert
          alerts={alerts}
          onOverride={handleOverride}
        />
      )}

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <TextField
            fullWidth
            id="current_medications"
            name="current_medications"
            label="Current Medications"
            value={formData.current_medications || ''}
            onChange={onChange}
            variant="outlined"
            multiline
            rows={4}
            helperText="List each medication on a new line or separated by commas"
          />
          <Box sx={{ mt: 1, display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              variant="outlined"
              color="primary"
              onClick={handleCheckMedications}
              disabled={checking || !formData.current_medications}
              startIcon={checking ? <CircularProgress size={20} /> : <WarningIcon />}
            >
              {checking ? 'Checking...' : 'Check BEERS Criteria'}
            </Button>
          </Box>
          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            id="medication_adherence"
            name="medication_adherence"
            label="Medication Adherence"
            value={formData.medication_adherence || ''}
            onChange={onChange}
            variant="outlined"
            helperText="How well does the patient adhere to medication schedule?"
          />
        </Grid>

        <Grid item xs={12} sm={6}>
          <TextField
            fullWidth
            id="medication_side_effects"
            name="medication_side_effects"
            label="Medication Side Effects"
            value={formData.medication_side_effects || ''}
            onChange={onChange}
            variant="outlined"
            helperText="Any side effects reported by the patient"
          />
        </Grid>

        <Grid item xs={12}>
          <TextField
            fullWidth
            id="medication_changes"
            name="medication_changes"
            label="Medication Changes"
            value={formData.medication_changes || ''}
            onChange={onChange}
            variant="outlined"
            multiline
            rows={3}
            helperText="Document any changes to medications during this visit"
          />
        </Grid>

        <Grid item xs={12}>
          <TextField
            fullWidth
            id="medication_allergies"
            name="medication_allergies"
            label="Medication Allergies"
            value={formData.medication_allergies || ''}
            onChange={onChange}
            variant="outlined"
            multiline
            rows={2}
            helperText="Document any medication allergies"
          />
        </Grid>
      </Grid>

      <Box mt={3}>
        <Accordion>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Typography variant="subtitle1">About BEERS Criteria</Typography>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body2" paragraph>
              The BEERS Criteria is a list of medications that are potentially inappropriate for older adults (65 years and older).
              It was developed by the American Geriatrics Society to improve medication safety in older patients.
            </Typography>
            <Typography variant="body2" paragraph>
              The criteria identifies medications that:
            </Typography>
            <ul>
              <li>
                <Typography variant="body2">Should be avoided in older adults regardless of condition</Typography>
              </li>
              <li>
                <Typography variant="body2">Should be avoided with certain medical conditions</Typography>
              </li>
              <li>
                <Typography variant="body2">Should be used with caution or at reduced dosages</Typography>
              </li>
              <li>
                <Typography variant="body2">May cause harmful drug-drug interactions</Typography>
              </li>
              <li>
                <Typography variant="body2">Should be adjusted based on kidney function</Typography>
              </li>
            </ul>
            <Typography variant="body2">
              Using the BEERS Criteria can help reduce adverse drug events and improve medication safety in older adults.
            </Typography>
          </AccordionDetails>
        </Accordion>
      </Box>
    </Paper>
  );
};

export default MedicationSection;
