import React from 'react';
import { Typography, Box, Grid, Divider } from '@mui/material';

interface AnxietyDisplayProps {
  value?: string;
  // Individual GAD-7 fields
  gad7FeelingNervous?: string;
  gad7StopWorrying?: string;
  gad7WorryingMuch?: string;
  gad7TroubleRelaxing?: string;
  gad7Restless?: string;
  gad7Annoyed?: string;
  gad7FeelingAfraid?: string;
  gad7DifficultyLevel?: string;
  gad7Notes?: string;
  anxietyScore?: string;
}

// GAD-7 interpretation
const getGad7Interpretation = (totalScore: number): string => {
  if (totalScore >= 0 && totalScore <= 4) {
    return "Minimal anxiety";
  } else if (totalScore >= 5 && totalScore <= 9) {
    return "Mild anxiety";
  } else if (totalScore >= 10 && totalScore <= 14) {
    return "Moderate anxiety";
  } else if (totalScore >= 15) {
    return "Severe anxiety";
  } else {
    return "Invalid score";
  }
};

// GAD-7 severity color
const getGad7SeverityColor = (totalScore: number): string => {
  if (totalScore >= 0 && totalScore <= 4) {
    return "#4caf50"; // Green - Minimal
  } else if (totalScore >= 5 && totalScore <= 9) {
    return "#8bc34a"; // Light Green - Mild
  } else if (totalScore >= 10 && totalScore <= 14) {
    return "#ffc107"; // Amber - Moderate
  } else if (totalScore >= 15) {
    return "#f44336"; // Red - Severe
  } else {
    return "#9e9e9e"; // Grey - Invalid
  }
};

const AnxietyDisplay: React.FC<AnxietyDisplayProps> = ({
  value,
  gad7FeelingNervous,
  gad7StopWorrying,
  gad7WorryingMuch,
  gad7TroubleRelaxing,
  gad7Restless,
  gad7Annoyed,
  gad7FeelingAfraid,
  gad7DifficultyLevel,
  gad7Notes,
  anxietyScore
}) => {
  // First try to use the individual fields if available
  if (anxietyScore !== undefined ||
      gad7FeelingNervous !== undefined ||
      gad7StopWorrying !== undefined) {

    const totalScore = anxietyScore ? parseInt(anxietyScore) :
      (gad7FeelingNervous ? parseInt(gad7FeelingNervous) : 0) +
      (gad7StopWorrying ? parseInt(gad7StopWorrying) : 0) +
      (gad7WorryingMuch ? parseInt(gad7WorryingMuch) : 0) +
      (gad7TroubleRelaxing ? parseInt(gad7TroubleRelaxing) : 0) +
      (gad7Restless ? parseInt(gad7Restless) : 0) +
      (gad7Annoyed ? parseInt(gad7Annoyed) : 0) +
      (gad7FeelingAfraid ? parseInt(gad7FeelingAfraid) : 0);

    const difficultyLevel = gad7DifficultyLevel ? parseInt(gad7DifficultyLevel) : 0;
    const difficultyText =
      difficultyLevel === 0 ? "Not difficult at all" :
      difficultyLevel === 1 ? "Somewhat difficult" :
      difficultyLevel === 2 ? "Very difficult" :
      difficultyLevel === 3 ? "Extremely difficult" : "Not specified";

    return (
      <Box>
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Typography variant="subtitle2" color="primary">GAD-7 Anxiety Assessment Results</Typography>
            <Divider sx={{ my: 1 }} />
          </Grid>

          <Grid item xs={12} sm={6}>
            <Typography variant="body2">
              <strong>Total Score:</strong> {totalScore}/21
            </Typography>
            <Typography variant="body2" sx={{ mt: 0.5 }}>
              <strong>Difficulty Level:</strong> {difficultyText}
            </Typography>

            <Box sx={{ mt: 1, p: 1, bgcolor: 'background.paper', borderRadius: 1, border: `1px solid ${getGad7SeverityColor(totalScore)}` }}>
              <Typography variant="body2" sx={{ color: getGad7SeverityColor(totalScore) }} fontWeight="bold">
                {getGad7Interpretation(totalScore)}
              </Typography>
            </Box>
          </Grid>

          {gad7Notes && (
            <Grid item xs={12} sm={6}>
              <Typography variant="body2" fontWeight="medium">
                <strong>Clinical Notes:</strong>
              </Typography>
              <Typography variant="body2" sx={{ mt: 0.5, whiteSpace: 'pre-wrap' }}>
                {gad7Notes}
              </Typography>
            </Grid>
          )}

          {/* Display individual question scores if available */}
          {gad7FeelingNervous !== undefined && (
            <Grid item xs={12}>
              <Typography variant="body2" fontWeight="medium" sx={{ mt: 1 }}>
                <strong>Question Responses:</strong>
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, mt: 0.5 }}>
                <Typography variant="body2">
                  1. Feeling nervous, anxious: {gad7FeelingNervous}
                </Typography>
                <Typography variant="body2">
                  2. Unable to stop worrying: {gad7StopWorrying}
                </Typography>
                <Typography variant="body2">
                  3. Worrying too much: {gad7WorryingMuch}
                </Typography>
                <Typography variant="body2">
                  4. Trouble relaxing: {gad7TroubleRelaxing}
                </Typography>
                <Typography variant="body2">
                  5. Being restless: {gad7Restless}
                </Typography>
                <Typography variant="body2">
                  6. Easily annoyed: {gad7Annoyed}
                </Typography>
                <Typography variant="body2">
                  7. Feeling afraid: {gad7FeelingAfraid}
                </Typography>
              </Box>
            </Grid>
          )}
        </Grid>
      </Box>
    );
  }

  // Fall back to parsing the JSON value if individual fields are not available
  try {
    // Try to parse the JSON value if it exists
    if (!value) {
      return (
        <Box>
          <Typography variant="body2">No anxiety assessment data available</Typography>
        </Box>
      );
    }

    const assessmentData = JSON.parse(value as string);

    // Check if data is in the expected GAD-7 format
    if (assessmentData.gad7) {
      const totalScore =
        (assessmentData.gad7.feelingNervous || 0) +
        (assessmentData.gad7.stopWorrying || 0) +
        (assessmentData.gad7.worryingMuch || 0) +
        (assessmentData.gad7.troubleRelaxing || 0) +
        (assessmentData.gad7.restless || 0) +
        (assessmentData.gad7.annoyed || 0) +
        (assessmentData.gad7.feelingAfraid || 0);

      const difficultyLevel = assessmentData.gad7.difficultyLevel || 0;
      const difficultyText =
        difficultyLevel === 0 ? "Not difficult at all" :
        difficultyLevel === 1 ? "Somewhat difficult" :
        difficultyLevel === 2 ? "Very difficult" :
        difficultyLevel === 3 ? "Extremely difficult" : "Not specified";

      return (
        <Box>
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Typography variant="subtitle2" color="primary">GAD-7 Anxiety Assessment Results</Typography>
              <Divider sx={{ my: 1 }} />
            </Grid>

            <Grid item xs={12} sm={6}>
              <Typography variant="body2">
                <strong>Total Score:</strong> {totalScore}/21
              </Typography>
              <Typography variant="body2" sx={{ mt: 0.5 }}>
                <strong>Difficulty Level:</strong> {difficultyText}
              </Typography>

              <Box sx={{ mt: 1, p: 1, bgcolor: 'background.paper', borderRadius: 1, border: `1px solid ${getGad7SeverityColor(totalScore)}` }}>
                <Typography variant="body2" sx={{ color: getGad7SeverityColor(totalScore) }} fontWeight="bold">
                  {getGad7Interpretation(totalScore)}
                </Typography>
              </Box>
            </Grid>

            {assessmentData.gad7.notes && (
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" fontWeight="medium">
                  <strong>Clinical Notes:</strong>
                </Typography>
                <Typography variant="body2" sx={{ mt: 0.5, whiteSpace: 'pre-wrap' }}>
                  {assessmentData.gad7.notes}
                </Typography>
              </Grid>
            )}
          </Grid>
        </Box>
      );
    }
  } catch (e) {
    // If parsing fails, just display the raw value
    return (
      <Box>
        <Typography variant="body2">{value || "No anxiety assessment data available"}</Typography>
      </Box>
    );
  }

  // If we get here, we couldn't parse the data
  return (
    <Box>
      <Typography variant="body2">No anxiety assessment data available</Typography>
    </Box>
  );
};

export default AnxietyDisplay;
