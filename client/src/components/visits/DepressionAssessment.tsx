import React, { useState } from 'react'; // useEffect is unused
import {
  Typography,
  Paper,
  // Grid is unused
  FormControl,
  RadioGroup,
  FormControlLabel,
  Radio,
  // Button is unused
  Divider,
  Box,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  TextField,
  FormLabel,
  FormHelperText
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';

interface DepressionAssessmentProps {
  value: string;
  onChange: (value: string) => void;
  // Individual PHQ-9 fields
  phq9InterestPleasure?: string;
  phq9FeelingDown?: string;
  phq9SleepIssues?: string;
  phq9Tired?: string;
  phq9Appetite?: string;
  phq9FeelingBad?: string;
  phq9Concentration?: string;
  phq9MovingSpeaking?: string;
  phq9ThoughtsHurting?: string;
  phq9DifficultyLevel?: string;
  phq9Notes?: string;
  depressionScore?: string;
  onPhq9Change?: (fields: {
    phq9_interest_pleasure: string;
    phq9_feeling_down: string;
    phq9_sleep_issues: string;
    phq9_tired: string;
    phq9_appetite: string;
    phq9_feeling_bad: string;
    phq9_concentration: string;
    phq9_moving_speaking: string;
    phq9_thoughts_hurting: string;
    phq9_difficulty_level: string;
    phq9_notes: string;
    depression_score: string;
    depression_screening: string;
  }) => void;
}

// PHQ-9 interpretation
const getPhq9Interpretation = (totalScore: number): string => {
  if (totalScore >= 0 && totalScore <= 4) {
    return "Minimal or no depression";
  } else if (totalScore >= 5 && totalScore <= 9) {
    return "Mild depression";
  } else if (totalScore >= 10 && totalScore <= 14) {
    return "Moderate depression";
  } else if (totalScore >= 15 && totalScore <= 19) {
    return "Moderately severe depression";
  } else if (totalScore >= 20) {
    return "Severe depression";
  } else {
    return "Invalid score";
  }
};

// PHQ-9 severity color
const getPhq9SeverityColor = (totalScore: number): string => {
  if (totalScore >= 0 && totalScore <= 4) {
    return "#4caf50"; // Green - Minimal
  } else if (totalScore >= 5 && totalScore <= 9) {
    return "#8bc34a"; // Light Green - Mild
  } else if (totalScore >= 10 && totalScore <= 14) {
    return "#ffc107"; // Amber - Moderate
  } else if (totalScore >= 15 && totalScore <= 19) {
    return "#ff9800"; // Orange - Moderately Severe
  } else if (totalScore >= 20) {
    return "#f44336"; // Red - Severe
  } else {
    return "#9e9e9e"; // Grey - Invalid
  }
};

const DepressionAssessment: React.FC<DepressionAssessmentProps> = ({
  value,
  onChange,
  phq9InterestPleasure,
  phq9FeelingDown,
  phq9SleepIssues,
  phq9Tired,
  phq9Appetite,
  phq9FeelingBad,
  phq9Concentration,
  phq9MovingSpeaking,
  phq9ThoughtsHurting,
  phq9DifficultyLevel,
  phq9Notes,
  depressionScore,
  onPhq9Change
}) => {
  // Initialize state from props or default values
  const [assessmentState, setAssessmentState] = useState(() => {
    // If we have individual field values, use those
    if (
      phq9InterestPleasure !== undefined ||
      phq9FeelingDown !== undefined ||
      phq9SleepIssues !== undefined ||
      phq9Tired !== undefined ||
      phq9Appetite !== undefined ||
      phq9FeelingBad !== undefined ||
      phq9Concentration !== undefined ||
      phq9MovingSpeaking !== undefined ||
      phq9ThoughtsHurting !== undefined
    ) {
      return {
        phq9: {
          interestPleasure: phq9InterestPleasure ? parseInt(phq9InterestPleasure) : 0,
          feelingDown: phq9FeelingDown ? parseInt(phq9FeelingDown) : 0,
          sleepIssues: phq9SleepIssues ? parseInt(phq9SleepIssues) : 0,
          tired: phq9Tired ? parseInt(phq9Tired) : 0,
          appetite: phq9Appetite ? parseInt(phq9Appetite) : 0,
          feelingBad: phq9FeelingBad ? parseInt(phq9FeelingBad) : 0,
          concentration: phq9Concentration ? parseInt(phq9Concentration) : 0,
          movingSpeaking: phq9MovingSpeaking ? parseInt(phq9MovingSpeaking) : 0,
          thoughtsHurting: phq9ThoughtsHurting ? parseInt(phq9ThoughtsHurting) : 0,
          difficultyLevel: phq9DifficultyLevel ? parseInt(phq9DifficultyLevel) : 0,
          notes: phq9Notes || ""
        }
      };
    }

    // Try to parse from JSON if available
    try {
      return value ? JSON.parse(value) : {
        phq9: {
          interestPleasure: 0,
          feelingDown: 0,
          sleepIssues: 0,
          tired: 0,
          appetite: 0,
          feelingBad: 0,
          concentration: 0,
          movingSpeaking: 0,
          thoughtsHurting: 0,
          difficultyLevel: 0,
          notes: ""
        }
      };
    } catch (e) {
      // If parsing fails, initialize with empty scores
      return {
        phq9: {
          interestPleasure: 0,
          feelingDown: 0,
          sleepIssues: 0,
          tired: 0,
          appetite: 0,
          feelingBad: 0,
          concentration: 0,
          movingSpeaking: 0,
          thoughtsHurting: 0,
          difficultyLevel: 0,
          notes: value || ""
        }
      };
    }
  });

  // Calculate total score
  const totalScore =
    assessmentState.phq9.interestPleasure +
    assessmentState.phq9.feelingDown +
    assessmentState.phq9.sleepIssues +
    assessmentState.phq9.tired +
    assessmentState.phq9.appetite +
    assessmentState.phq9.feelingBad +
    assessmentState.phq9.concentration +
    assessmentState.phq9.movingSpeaking +
    assessmentState.phq9.thoughtsHurting;

  // Update both the JSON state and individual fields
  const updateState = (newState: any) => {
    setAssessmentState(newState);

    // Update the JSON string for backward compatibility
    onChange(JSON.stringify(newState));

    // Calculate the new total score
    const newTotalScore =
      newState.phq9.interestPleasure +
      newState.phq9.feelingDown +
      newState.phq9.sleepIssues +
      newState.phq9.tired +
      newState.phq9.appetite +
      newState.phq9.feelingBad +
      newState.phq9.concentration +
      newState.phq9.movingSpeaking +
      newState.phq9.thoughtsHurting;

    // Update individual fields if the callback is provided
    if (onPhq9Change) {
      // Create a structured notes text for depression_screening
      const interpretation = getPhq9Interpretation(newTotalScore);

      const notes = `PHQ-9 Depression Assessment Results:
Total Score: ${newTotalScore}/27
Interpretation: ${interpretation}
Difficulty Level: ${newState.phq9.difficultyLevel === 0 ? "Not difficult at all" :
                   newState.phq9.difficultyLevel === 1 ? "Somewhat difficult" :
                   newState.phq9.difficultyLevel === 2 ? "Very difficult" :
                   newState.phq9.difficultyLevel === 3 ? "Extremely difficult" : "Not specified"}
Clinical Notes: ${newState.phq9.notes || "None"}`;

      // Call the callback with all the fields
      onPhq9Change({
        phq9_interest_pleasure: newState.phq9.interestPleasure.toString(),
        phq9_feeling_down: newState.phq9.feelingDown.toString(),
        phq9_sleep_issues: newState.phq9.sleepIssues.toString(),
        phq9_tired: newState.phq9.tired.toString(),
        phq9_appetite: newState.phq9.appetite.toString(),
        phq9_feeling_bad: newState.phq9.feelingBad.toString(),
        phq9_concentration: newState.phq9.concentration.toString(),
        phq9_moving_speaking: newState.phq9.movingSpeaking.toString(),
        phq9_thoughts_hurting: newState.phq9.thoughtsHurting.toString(),
        phq9_difficulty_level: newState.phq9.difficultyLevel.toString(),
        phq9_notes: newState.phq9.notes,
        depression_score: newTotalScore.toString(),
        depression_screening: notes
      });
    }
  };

  // Handle question response change
  const handleResponseChange = (question: string, value: number) => {
    const newState = {
      ...assessmentState,
      phq9: {
        ...assessmentState.phq9,
        [question]: value
      }
    };

    updateState(newState);
  };

  // Handle difficulty level change
  const handleDifficultyChange = (value: number) => {
    const newState = {
      ...assessmentState,
      phq9: {
        ...assessmentState.phq9,
        difficultyLevel: value
      }
    };

    updateState(newState);
  };

  // Handle notes change
  const handleNotesChange = (notes: string) => {
    const newState = {
      ...assessmentState,
      phq9: {
        ...assessmentState.phq9,
        notes
      }
    };

    updateState(newState);
  };

  // Question options
  const frequencyOptions = [
    { value: 0, label: "Not at all" },
    { value: 1, label: "Several days" },
    { value: 2, label: "More than half the days" },
    { value: 3, label: "Nearly every day" }
  ];

  // Difficulty options
  const difficultyOptions = [
    { value: 0, label: "Not difficult at all" },
    { value: 1, label: "Somewhat difficult" },
    { value: 2, label: "Very difficult" },
    { value: 3, label: "Extremely difficult" }
  ];

  return (
    <Paper elevation={0} sx={{ p: 2 }}>
      <Typography variant="h6" gutterBottom>
        PHQ-9 Depression Assessment
      </Typography>

      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        Over the last 2 weeks, how often have you been bothered by any of the following problems?
      </Typography>

      <Divider sx={{ mb: 3 }} />

      {/* Question 1 */}
      <Box sx={{ mb: 3 }}>
        <FormControl component="fieldset">
          <FormLabel component="legend">
            1. Little interest or pleasure in doing things
          </FormLabel>
          <RadioGroup
            row
            value={assessmentState.phq9.interestPleasure}
            onChange={(e) => handleResponseChange('interestPleasure', parseInt(e.target.value))}
          >
            {frequencyOptions.map((option) => (
              <FormControlLabel
                key={option.value}
                value={option.value}
                control={<Radio />}
                label={option.label}
              />
            ))}
          </RadioGroup>
        </FormControl>
      </Box>

      {/* Question 2 */}
      <Box sx={{ mb: 3 }}>
        <FormControl component="fieldset">
          <FormLabel component="legend">
            2. Feeling down, depressed, or hopeless
          </FormLabel>
          <RadioGroup
            row
            value={assessmentState.phq9.feelingDown}
            onChange={(e) => handleResponseChange('feelingDown', parseInt(e.target.value))}
          >
            {frequencyOptions.map((option) => (
              <FormControlLabel
                key={option.value}
                value={option.value}
                control={<Radio />}
                label={option.label}
              />
            ))}
          </RadioGroup>
        </FormControl>
      </Box>

      {/* Question 3 */}
      <Box sx={{ mb: 3 }}>
        <FormControl component="fieldset">
          <FormLabel component="legend">
            3. Trouble falling or staying asleep, or sleeping too much
          </FormLabel>
          <RadioGroup
            row
            value={assessmentState.phq9.sleepIssues}
            onChange={(e) => handleResponseChange('sleepIssues', parseInt(e.target.value))}
          >
            {frequencyOptions.map((option) => (
              <FormControlLabel
                key={option.value}
                value={option.value}
                control={<Radio />}
                label={option.label}
              />
            ))}
          </RadioGroup>
        </FormControl>
      </Box>

      {/* Question 4 */}
      <Box sx={{ mb: 3 }}>
        <FormControl component="fieldset">
          <FormLabel component="legend">
            4. Feeling tired or having little energy
          </FormLabel>
          <RadioGroup
            row
            value={assessmentState.phq9.tired}
            onChange={(e) => handleResponseChange('tired', parseInt(e.target.value))}
          >
            {frequencyOptions.map((option) => (
              <FormControlLabel
                key={option.value}
                value={option.value}
                control={<Radio />}
                label={option.label}
              />
            ))}
          </RadioGroup>
        </FormControl>
      </Box>

      {/* Question 5 */}
      <Box sx={{ mb: 3 }}>
        <FormControl component="fieldset">
          <FormLabel component="legend">
            5. Poor appetite or overeating
          </FormLabel>
          <RadioGroup
            row
            value={assessmentState.phq9.appetite}
            onChange={(e) => handleResponseChange('appetite', parseInt(e.target.value))}
          >
            {frequencyOptions.map((option) => (
              <FormControlLabel
                key={option.value}
                value={option.value}
                control={<Radio />}
                label={option.label}
              />
            ))}
          </RadioGroup>
        </FormControl>
      </Box>

      {/* Question 6 */}
      <Box sx={{ mb: 3 }}>
        <FormControl component="fieldset">
          <FormLabel component="legend">
            6. Feeling bad about yourself — or that you are a failure or have let yourself or your family down
          </FormLabel>
          <RadioGroup
            row
            value={assessmentState.phq9.feelingBad}
            onChange={(e) => handleResponseChange('feelingBad', parseInt(e.target.value))}
          >
            {frequencyOptions.map((option) => (
              <FormControlLabel
                key={option.value}
                value={option.value}
                control={<Radio />}
                label={option.label}
              />
            ))}
          </RadioGroup>
        </FormControl>
      </Box>

      {/* Question 7 */}
      <Box sx={{ mb: 3 }}>
        <FormControl component="fieldset">
          <FormLabel component="legend">
            7. Trouble concentrating on things, such as reading the newspaper or watching television
          </FormLabel>
          <RadioGroup
            row
            value={assessmentState.phq9.concentration}
            onChange={(e) => handleResponseChange('concentration', parseInt(e.target.value))}
          >
            {frequencyOptions.map((option) => (
              <FormControlLabel
                key={option.value}
                value={option.value}
                control={<Radio />}
                label={option.label}
              />
            ))}
          </RadioGroup>
        </FormControl>
      </Box>

      {/* Question 8 */}
      <Box sx={{ mb: 3 }}>
        <FormControl component="fieldset">
          <FormLabel component="legend">
            8. Moving or speaking so slowly that other people could have noticed. Or the opposite — being so fidgety or restless that you have been moving around a lot more than usual
          </FormLabel>
          <RadioGroup
            row
            value={assessmentState.phq9.movingSpeaking}
            onChange={(e) => handleResponseChange('movingSpeaking', parseInt(e.target.value))}
          >
            {frequencyOptions.map((option) => (
              <FormControlLabel
                key={option.value}
                value={option.value}
                control={<Radio />}
                label={option.label}
              />
            ))}
          </RadioGroup>
        </FormControl>
      </Box>

      {/* Question 9 */}
      <Box sx={{ mb: 3 }}>
        <FormControl component="fieldset">
          <FormLabel component="legend">
            9. Thoughts that you would be better off dead, or of hurting yourself in some way
          </FormLabel>
          <RadioGroup
            row
            value={assessmentState.phq9.thoughtsHurting}
            onChange={(e) => handleResponseChange('thoughtsHurting', parseInt(e.target.value))}
          >
            {frequencyOptions.map((option) => (
              <FormControlLabel
                key={option.value}
                value={option.value}
                control={<Radio />}
                label={option.label}
              />
            ))}
          </RadioGroup>
          {assessmentState.phq9.thoughtsHurting > 0 && (
            <FormHelperText error>
              <strong>Important:</strong> If the patient has selected any option other than "Not at all" for this question,
              please conduct a thorough suicide risk assessment and consider immediate referral to mental health services.
            </FormHelperText>
          )}
        </FormControl>
      </Box>

      <Divider sx={{ my: 3 }} />

      {/* Total Score */}
      <Box sx={{ mb: 3, p: 2, bgcolor: 'background.paper', borderRadius: 1, border: `1px solid ${getPhq9SeverityColor(totalScore)}` }}>
        <Typography variant="subtitle1" gutterBottom>
          Total Score: {totalScore}/27
        </Typography>
        <Typography variant="body1" sx={{ color: getPhq9SeverityColor(totalScore), fontWeight: 'bold' }}>
          Interpretation: {getPhq9Interpretation(totalScore)}
        </Typography>
      </Box>

      {/* Difficulty Question */}
      <Box sx={{ mb: 3 }}>
        <FormControl component="fieldset">
          <FormLabel component="legend">
            If you checked off any problems, how difficult have these problems made it for you to do your work, take care of things at home, or get along with other people?
          </FormLabel>
          <RadioGroup
            row
            value={assessmentState.phq9.difficultyLevel}
            onChange={(e) => handleDifficultyChange(parseInt(e.target.value))}
          >
            {difficultyOptions.map((option) => (
              <FormControlLabel
                key={option.value}
                value={option.value}
                control={<Radio />}
                label={option.label}
              />
            ))}
          </RadioGroup>
        </FormControl>
      </Box>

      {/* Clinical Notes */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          label="Clinical Notes"
          multiline
          rows={4}
          value={assessmentState.phq9.notes}
          onChange={(e) => handleNotesChange(e.target.value)}
          variant="outlined"
        />
      </Box>

      {/* Treatment Recommendations */}
      <Accordion>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography>Treatment Recommendations</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography variant="subtitle2" gutterBottom>
            Based on PHQ-9 Score:
          </Typography>
          <Box component="ul" sx={{ pl: 2 }}>
            <Box component="li" sx={{ mb: 1 }}>
              <Typography variant="body2">
                <strong>0-4 (Minimal):</strong> Monitor; may not require treatment
              </Typography>
            </Box>
            <Box component="li" sx={{ mb: 1 }}>
              <Typography variant="body2">
                <strong>5-9 (Mild):</strong> Watchful waiting; repeat PHQ-9 at follow-up
              </Typography>
            </Box>
            <Box component="li" sx={{ mb: 1 }}>
              <Typography variant="body2">
                <strong>10-14 (Moderate):</strong> Treatment plan, considering counseling, follow-up and/or pharmacotherapy
              </Typography>
            </Box>
            <Box component="li" sx={{ mb: 1 }}>
              <Typography variant="body2">
                <strong>15-19 (Moderately Severe):</strong> Active treatment with pharmacotherapy and/or psychotherapy
              </Typography>
            </Box>
            <Box component="li">
              <Typography variant="body2">
                <strong>20-27 (Severe):</strong> Immediate initiation of pharmacotherapy and, if severe impairment or poor response to therapy, expedited referral to a mental health specialist for psychotherapy and/or collaborative management
              </Typography>
            </Box>
          </Box>
        </AccordionDetails>
      </Accordion>
    </Paper>
  );
};

export default DepressionAssessment;
