/* Specific dark mode overrides for the landing page */
body.dark-mode .modern-landing {
  background-color: #121212 !important;
  color: #fff !important;
}

body.dark-mode .landing-content {
  background-color: transparent !important;
}

body.dark-mode .landing-left {
  background-color: transparent !important;
}

body.dark-mode .landing-title {
  color: #fff !important;
}

body.dark-mode .landing-description {
  color: #aaa !important;
}

body.dark-mode .landing-btn {
  background-color: #2a2a2a !important;
  color: #fff !important;
}

body.dark-mode .landing-btn.login-btn,
body.dark-mode .modern-landing .landing-btn.login-btn,
body.dark-mode .landing-content .landing-btn.login-btn,
html.dark-mode .landing-btn.login-btn,
.dark-mode .landing-btn.login-btn {
  background-color: #D97B3A !important; /* Orange from logo */
  color: #fff !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3) !important;
}

body.dark-mode .landing-btn.login-btn:hover,
body.dark-mode .modern-landing .landing-btn.login-btn:hover,
body.dark-mode .landing-content .landing-btn.login-btn:hover,
html.dark-mode .landing-btn.login-btn:hover,
.dark-mode .landing-btn.login-btn:hover {
  background-color: #F2A65A !important; /* Light Orange from logo */
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4) !important;
}
