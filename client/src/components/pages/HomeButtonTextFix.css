/* Fix for the Login button text color in both light and dark modes on the Home page */

/* Ensure login button text is white in both light and dark modes */
.MuiButton-root[href="/login"],
a[href="/login"].MuiButton-root,
.MuiButton-contained[href="/login"] {
  color: #ffffff !important;
}

/* Dark mode specific styles (already implemented, but included for completeness) */
body.dark-mode .MuiButton-root[href="/login"],
html.dark-mode .MuiButton-root[href="/login"],
.dark-mode .MuiButton-root[href="/login"],
body.dark-mode a[href="/login"].MuiButton-root,
html.dark-mode a[href="/login"].MuiButton-root,
.dark-mode a[href="/login"].MuiButton-root {
  color: #ffffff !important;
}

/* Light mode specific styles */
body:not(.dark-mode) .MuiButton-root[href="/login"],
html:not(.dark-mode) .MuiButton-root[href="/login"],
:not(.dark-mode) .MuiButton-root[href="/login"],
body:not(.dark-mode) a[href="/login"].MuiButton-root,
html:not(.dark-mode) a[href="/login"].MuiButton-root,
:not(.dark-mode) a[href="/login"].MuiButton-root {
  color: #ffffff !important;
}
