/* Modern Landing Page Styles */
.modern-landing {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-color);
  color: var(--text-color);
  padding: 2rem;
  position: relative;
  overflow: hidden;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Force dark mode background */
body.dark-mode .modern-landing {
  background-color: #121212 !important;
}

body.dark-mode .landing-content {
  background-color: transparent !important;
}

body.dark-mode .landing-left {
  background-color: transparent !important;
}

body.dark-mode .landing-btn {
  background-color: #2a2a2a !important;
  color: #fff !important;
}

body.dark-mode .login-btn {
  background-color: #D97B3A !important; /* Orange from logo */
  color: #fff !important;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3) !important;
}

body.dark-mode .landing-title {
  color: #fff !important;
}

body.dark-mode .landing-description {
  color: #aaa !important;
}

.landing-content {
  display: flex;
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  align-items: center;
  justify-content: space-between;
  gap: 2rem;
  z-index: 1;
  background-color: transparent;
  transition: background-color 0.3s ease;
}

.landing-left {
  flex: 1;
  max-width: 600px;
  padding: 2rem;
  background-color: transparent;
  color: var(--text-color);
  transition: background-color 0.3s ease, color 0.3s ease;
}

.landing-right {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.logo-container {
  margin-bottom: 2rem;
  max-width: 250px;
}

.landing-logo {
  width: 100%;
  height: auto;
}

.landing-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.2;
  color: var(--primary-color);
  transition: color 0.3s ease;
}

.dark-mode .modern-landing {
  background-color: #121212;
  color: #fff;
}

.dark-mode .landing-title {
  color: #fff;
}

.dark-mode .landing-description {
  color: #aaa;
}

.dark-mode .landing-content {
  background-color: transparent;
}

.dark-mode .landing-left {
  background-color: transparent;
}

.landing-description {
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  color: var(--text-secondary-color);
  transition: color 0.3s ease;
}

.landing-buttons {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
}

.landing-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
}

.login-btn {
  background-color: var(--primary-color);
  color: white;
  box-shadow: 0 4px 6px rgba(44, 75, 43, 0.2);
}

.dark-mode .login-btn {
  background-color: #D97B3A; /* Orange from logo */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

.login-btn:hover {
  background-color: var(--dark-color);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(44, 75, 43, 0.3);
}

.dark-mode .login-btn:hover {
  background-color: #F2A65A; /* Light Orange from logo */
}

.register-btn {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.dark-mode .register-btn {
  color: #fff;
  border: 2px solid var(--primary-color);
}

.register-btn:hover {
  background-color: var(--primary-color-lightest);
  transform: translateY(-2px);
}

.dark-mode .register-btn:hover {
  background-color: rgba(58, 99, 57, 0.2);
}

.dashboard-preview {
  max-width: 100%;
  height: auto;
  border-radius: 16px;
  box-shadow: 0 20px 40px var(--shadow-color);
  transition: all 0.3s ease;
  transform: perspective(1000px) rotateY(-10deg) rotateX(5deg);
}

.dashboard-preview:hover {
  transform: perspective(1000px) rotateY(-5deg) rotateX(2deg) translateY(-5px);
}

.dark-mode .dashboard-preview {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.4);
}

/* Responsive styles */
@media (max-width: 992px) {
  .landing-content {
    flex-direction: column;
    text-align: center;
  }

  .landing-left {
    order: 2;
    padding: 1rem;
  }

  .landing-right {
    order: 1;
    margin-bottom: 2rem;
  }

  .logo-container {
    margin: 0 auto 2rem;
  }

  .landing-buttons {
    justify-content: center;
  }

  .landing-title {
    font-size: 2rem;
  }
}

@media (max-width: 576px) {
  .landing-title {
    font-size: 1.75rem;
  }

  .landing-description {
    font-size: 1rem;
  }

  .landing-buttons {
    flex-direction: column;
    width: 100%;
  }

  .landing-btn {
    width: 100%;
  }
}
