/* Dark mode overrides for Home component */

/* Direct override for all Box components in dark mode */
body.dark-mode .MuiBox-root {
  background-image: none !important;
}

/* Don't override icon backgrounds */
body.dark-mode .MuiSvgIcon-root {
  background: transparent !important;
  background-color: transparent !important;
}

/* Override for the hero section */
body.dark-mode .MuiBox-root:first-child {
  background: #121212 !important;
}

/* Override for the hero section's first child */
body.dark-mode .MuiBox-root:first-child > .MuiBox-root:first-child {
  background: #121212 !important;
}

/* Override for the features section */
body.dark-mode #features {
  background-color: #121212 !important;
}

/* Override for feature section title and subtitle */
body.dark-mode #features .MuiBox-root {
  background-color: transparent !important;
}

/* Override for feature icons */
body.dark-mode .MuiSvgIcon-root {
  background-color: transparent !important;
}

/* Override for the footer */
body.dark-mode .MuiBox-root[style*="py: 5"] {
  background-color: #121212 !important;
}

/* Override text colors in dark mode */
body.dark-mode .MuiTypography-root[style*="color: '#333'"],
body.dark-mode .MuiTypography-root[style*="color: '#555'"] {
  color: #aaa !important;
}

body.dark-mode .MuiTypography-root[style*="color: '#2C4B2B'"],
body.dark-mode .MuiTypography-root[style*="color: '#2e7d32'"] {
  color: #4caf50 !important;
}

/* Override card backgrounds in dark mode */
body.dark-mode .MuiCard-root {
  background-color: #1e1e1e !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
}

/* Specific override for feature cards in dark mode */
body.dark-mode #features .MuiCard-root {
  background-color: #1e1e1e !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

body.dark-mode #features .MuiCard-root:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3) !important;
}

/* Override button colors in dark mode */
body.dark-mode .MuiButton-outlined {
  color: #4caf50 !important;
  border-color: rgba(76, 175, 80, 0.5) !important;
}

body.dark-mode .MuiButton-outlined:hover {
  background-color: rgba(76, 175, 80, 0.1) !important;
  border-color: #4caf50 !important;
}

/* Override divider color in dark mode */
body.dark-mode .MuiDivider-root {
  border-color: #d68910 !important;
}
