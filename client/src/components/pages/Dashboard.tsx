import React, { useContext, useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import AuthContext from '../../context/AuthContext';
import DoctorDashboard from '../doctors/DoctorDashboard';
import LoadingSpinner from '../common/LoadingSpinner';

interface Patient {
  patient_id: number;
  first_name: string;
  last_name: string;
  unique_id: string;
  date_of_birth: string;
  gender: string;
}

interface MedicalRecord {
  record_id: number;
  patient_id: number;
  first_name: string;
  last_name: string;
  record_date: string;
  diagnosis: string;
  created_at: string;
}

interface HealthMetric {
  label: string;
  value: string;
  icon: string;
  color: string;
}

const Dashboard: React.FC = () => {
  const { user } = useContext(AuthContext);
  const [recentPatients, setRecentPatients] = useState<Patient[]>([]);
  const [recentRecords, setRecentRecords] = useState<MedicalRecord[]>([]);
  const [patientCount, setPatientCount] = useState<number>(0);
  const [recordCount, setRecordCount] = useState<number>(0);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get recent patients
        const patientsRes = await axios.get('http://localhost:5001/api/patients?limit=5');
        setRecentPatients(patientsRes.data);
        setPatientCount(patientsRes.data.length);

        // Get recent records
        const recordsRes = await axios.get('http://localhost:5001/api/records/recent/all?limit=5');
        setRecentRecords(recordsRes.data);
        setRecordCount(recordsRes.data.length);

        setLoading(false);
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setLoading(false);
      }
    };

    // Only fetch data if user is not a doctor
    if (user && user.role !== 'doctor') {
      fetchData();
    }
  }, [user]);

  // If user is a doctor, render the DoctorDashboard
  if (user?.role === 'doctor') {
    return <DoctorDashboard />;
  }

  // If user is an admin, redirect to admin dashboard
  if (user?.role === 'admin') {
    window.location.href = '/admin';
    return <LoadingSpinner size="medium" message="Redirecting to admin dashboard..." />;
  }

  // If user is a kin, redirect to kin dashboard
  if (user?.role === 'kin') {
    window.location.href = '/kin/dashboard';
    return <LoadingSpinner size="medium" message="Redirecting to family dashboard..." />;
  }

  const healthMetrics: HealthMetric[] = [
    {
      label: 'Total Patients',
      value: patientCount.toString(),
      icon: 'fa-users',
      color: 'primary'
    },
    {
      label: 'Records',
      value: recordCount.toString(),
      icon: 'fa-notes-medical',
      color: 'success'
    },
    {
      label: 'Active Medications',
      value: '32',
      icon: 'fa-pills',
      color: 'warning'
    },
    {
      label: 'Appointments Today',
      value: '8',
      icon: 'fa-calendar-check',
      color: 'info'
    }
  ];

  if (loading) {
    return <LoadingSpinner size="medium" message="Loading dashboard data..." />;
  }

  return (
    <div className="dashboard">
      <h1 className="large text-primary">Dashboard</h1>
      <p className="lead">
        <i className="fas fa-user"></i> Welcome {user && user.username}
      </p>

      <div className="dash-metrics">
        {healthMetrics.map((metric, index) => (
          <div key={index} className={`metric-card bg-${metric.color}`}>
            <div className="metric-icon">
              <i className={`fas ${metric.icon}`}></i>
            </div>
            <div className="metric-info">
              <h3>{metric.value}</h3>
              <p>{metric.label}</p>
            </div>
          </div>
        ))}
      </div>

      <div className="dash-buttons">
        <Link to="/patients" className="btn btn-light">
          <i className="fas fa-user-injured text-primary"></i> All Patients
        </Link>
        <Link to="/patients/new" className="btn btn-light">
          <i className="fas fa-plus text-primary"></i> Add Patient
        </Link>
      </div>

      <div className="dash-container">
        <div className="dash-item">
          <h2 className="my-2">Recent Patients</h2>
          {recentPatients.length > 0 ? (
            <table className="table">
              <thead>
                <tr>
                  <th>Name</th>
                  <th className="hide-sm">ID</th>
                  <th className="hide-sm">Date of Birth</th>
                  <th className="hide-sm">Actions</th>
                </tr>
              </thead>
              <tbody>
                {recentPatients.map((patient) => (
                  <tr key={patient.patient_id}>
                    <td>{patient.first_name} {patient.last_name}</td>
                    <td className="hide-sm">{patient.unique_id || '-'}</td>
                    <td className="hide-sm">{new Date(patient.date_of_birth).toLocaleDateString()}</td>
                    <td className="hide-sm">
                      <Link to={`/patients/${patient.patient_id}`} className="btn btn-primary btn-sm">
                        <i className="fas fa-eye"></i> View
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <p>No patients found</p>
          )}
        </div>

        <div className="dash-item">
          <h2 className="my-2">Recent Medical Records</h2>
          {recentRecords.length > 0 ? (
            <table className="table">
              <thead>
                <tr>
                  <th>Patient</th>
                  <th>Date</th>
                  <th className="hide-sm">Diagnosis</th>
                  <th className="hide-sm">Actions</th>
                </tr>
              </thead>
              <tbody>
                {recentRecords.map((record) => (
                  <tr key={record.record_id}>
                    <td>{record.first_name} {record.last_name}</td>
                    <td>{new Date(record.record_date).toLocaleDateString()}</td>
                    <td className="hide-sm">{record.diagnosis.substring(0, 30)}...</td>
                    <td className="hide-sm">
                      <Link to={`/patients/${record.patient_id}`} className="btn btn-primary btn-sm">
                        <i className="fas fa-eye"></i> View
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          ) : (
            <p>No records found</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;