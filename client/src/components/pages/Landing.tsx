import React, { useContext } from 'react';
import { Link } from 'react-router-dom';
import ThemeContext from '../../context/ThemeContext';
import './Landing.css';
import './LandingDarkMode.css';
import './LoginButtonFix.css';
import './LandingButtonTextFix.css';

const Landing: React.FC = () => {
  const { mode } = useContext(ThemeContext);

  return (
    <section className="modern-landing">
      <div className="landing-content">
        <div className="landing-left">
          <div className="logo-container">
            <img src="/assets/wise-age-logo.svg" alt="WiseAge Wellness Logo" className="landing-logo" />
          </div>
          <h1 className="landing-title">Electronic Health Records<br />for Senior Care</h1>
          <p className="landing-description">
            Our EHR platform is specifically designed for geriatric care, helping healthcare providers manage patient information, track health metrics, and improve quality of life for older adults.
          </p>
          <div className="landing-buttons">
            <Link
              to="/login"
              className="landing-btn login-btn"
              style={mode === 'dark' ? { backgroundColor: '#D97B3A', color: '#fff' } : {}}
            >
              <i className="fas fa-sign-in-alt"></i> Login
            </Link>
            <Link to="/register" className="landing-btn register-btn">
              Learn more
            </Link>
          </div>
        </div>
        <div className="landing-right">
          <img
            src={mode === 'dark'
              ? "/assets/healthcare-dashboard-dark.svg"
              : "/assets/healthcare-dashboard-light.svg"}
            alt="WiseAge Wellness Dashboard Preview"
            className="dashboard-preview"
          />
        </div>
      </div>
    </section>
  );
};

export default Landing;