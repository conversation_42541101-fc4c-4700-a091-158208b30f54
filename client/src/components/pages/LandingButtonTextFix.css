/* Fix for the Login button text color in both light and dark modes on the landing page */

/* Ensure text is white in both light and dark modes */
.landing-btn.login-btn,
.modern-landing .landing-btn.login-btn,
.landing-content .landing-btn.login-btn,
.landing-buttons .landing-btn.login-btn {
  color: #ffffff !important;
}

/* Dark mode specific styles (already implemented, but included for completeness) */
body.dark-mode .landing-btn.login-btn,
html.dark-mode .landing-btn.login-btn,
.dark-mode .landing-btn.login-btn,
.dark-mode .modern-landing .landing-btn.login-btn,
.dark-mode .landing-content .landing-btn.login-btn,
.dark-mode .landing-buttons .landing-btn.login-btn {
  color: #ffffff !important;
}

/* Light mode specific styles */
body:not(.dark-mode) .landing-btn.login-btn,
html:not(.dark-mode) .landing-btn.login-btn,
:not(.dark-mode) .landing-btn.login-btn,
:not(.dark-mode) .modern-landing .landing-btn.login-btn,
:not(.dark-mode) .landing-content .landing-btn.login-btn,
:not(.dark-mode) .landing-buttons .landing-btn.login-btn {
  color: #ffffff !important;
}
