import React, { useContext, useState, useEffect } from 'react';
import { Link, Navigate } from 'react-router-dom';
import AuthContext from '../../context/AuthContext';
import './HomeDarkMode.css';
import './HomeButtonTextFix.css';
import {
  Box,
  Typography,
  Button,
  Container,
  Grid,
  Card,
  CardContent,
  Fade,
  Grow,
  Divider,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Security as SecurityIcon,
  Speed as SpeedIcon,
  Analytics as AnalyticsIcon,
  Login as LoginIcon,
  Elderly as ElderlyIcon,
  AccessibilityNew as AccessibilityIcon,
  MonitorHeart as HeartIcon
} from '@mui/icons-material';

const Home: React.FC = () => {
  const { isAuthenticated } = useContext(AuthContext);
  const [loaded, setLoaded] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  useEffect(() => {
    setLoaded(true);
  }, []);

  if (isAuthenticated) {
    return <Navigate to="/dashboard" />;
  }

  // Create icon components with proper styling for dark mode
  const createStyledIcon = (Icon: any, color: string) => (
    <Box sx={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      width: 60,
      height: 60,
      borderRadius: '50%',
      bgcolor: theme.palette.mode === 'dark' ? '#2a2a2a' : '#f8f9fa',
      boxShadow: theme.palette.mode === 'dark'
        ? '0 4px 8px rgba(0,0,0,0.3)'
        : '0 4px 8px rgba(0,0,0,0.1)'
    }}>
      <Icon fontSize="large" sx={{ color, background: 'transparent' }} />
    </Box>
  );

  const features = [
    {
      title: "Geriatric-Focused Care",
      description: "Specialized tools and workflows designed specifically for older adult healthcare needs",
      icon: createStyledIcon(ElderlyIcon, theme.palette.mode === 'dark' ? '#4caf50' : '#2e7d32')
    },
    {
      title: "Comprehensive Health Tracking",
      description: "Monitor vital signs, medications, and health indicators specific to senior wellness",
      icon: createStyledIcon(HeartIcon, theme.palette.mode === 'dark' ? '#f0ad4e' : '#d68910')
    },
    {
      title: "Family Connection",
      description: "Keep family members informed and involved in care with secure access for kin",
      icon: createStyledIcon(AccessibilityIcon, theme.palette.mode === 'dark' ? '#ff8a65' : '#c75b39')
    },
    {
      title: "Advanced Security",
      description: "HIPAA-compliant security with role-based access control and detailed audit logs",
      icon: createStyledIcon(SecurityIcon, theme.palette.mode === 'dark' ? '#8bc34a' : '#6e8b3d')
    },
    {
      title: "Intuitive Interface",
      description: "Age-friendly design with clear typography and accessible features",
      icon: createStyledIcon(SpeedIcon, theme.palette.mode === 'dark' ? '#f0ad4e' : '#d68910')
    },
    {
      title: "Comprehensive Analytics",
      description: "Track health trends and outcomes to improve geriatric care quality",
      icon: createStyledIcon(AnalyticsIcon, theme.palette.mode === 'dark' ? '#4caf50' : '#2e7d32')
    }
  ];

  return (
    <Box sx={{
      overflow: 'hidden',
      background: theme.palette.mode === 'dark'
        ? 'linear-gradient(135deg, #121212 0%, #1e1e1e 100%)'
        : 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)'
    }}>
      {/* Hero Section */}
      <Box
        sx={{
          position: 'relative',
          height: { xs: '100vh', md: '85vh' },
          display: 'flex',
          alignItems: 'center',
          background: theme.palette.mode === 'dark'
            ? 'linear-gradient(135deg, #121212 0%, #1e1e1e 100%)'
            : 'linear-gradient(135deg, rgba(245, 245, 245, 1) 0%, rgba(235, 235, 235, 1) 100%)',
          overflow: 'hidden',
          boxShadow: theme.palette.mode === 'dark'
            ? '0 4px 20px rgba(0,0,0,0.2)'
            : '0 4px 20px rgba(0,0,0,0.05)'
        }}
      >
        {/* Background Pattern */}
        <Box sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          opacity: 0.1,
          backgroundImage: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%232C4B2B" fill-opacity="1"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")',
        }} />

        <Container maxWidth="lg">
          <Grid container spacing={4} alignItems="center">
            <Grid item xs={12} md={6}>
              <Fade in={loaded} timeout={1000}>
                <Box>
                  <Box
                    component="img"
                    src="/assets/wise-age-logo.svg"
                    alt="WiseAge Wellness Logo"
                    sx={{
                      width: '280px',
                      height: 'auto',
                      mb: 3,
                      display: 'block'
                    }}
                  />
                  <Typography
                    variant="h2"
                    sx={{
                      color: theme.palette.mode === 'dark' ? '#4caf50' : '#2C4B2B', // Dark Green from logo
                      fontWeight: 300,
                      fontSize: { xs: '1.5rem', sm: '2rem', md: '2.5rem' },
                      mb: 3
                    }}
                  >
                    Electronic Health Records for Senior Care
                  </Typography>
                  <Typography
                    variant="body1"
                    sx={{
                      color: theme.palette.mode === 'dark' ? '#aaa' : '#333',
                      fontSize: { xs: '1rem', md: '1.25rem' },
                      mb: 4,
                      maxWidth: '600px'
                    }}
                  >
                    A specialized healthcare platform designed for geriatric care professionals to manage senior patient information,
                    monitor health indicators, and improve quality of life for older adults.
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
                    <Button
                      component={Link}
                      to="/login"
                      variant="contained"
                      size="large"
                      startIcon={<LoginIcon />}
                      sx={{
                        bgcolor: '#D97B3A', // Orange from logo
                        color: 'white',
                        fontWeight: 600,
                        px: 4,
                        py: 1.5,
                        borderRadius: 2,
                        boxShadow: '0 4px 12px rgba(217, 123, 58, 0.3)',
                        '&:hover': {
                          bgcolor: '#F2A65A', // Light Orange from logo
                          boxShadow: '0 6px 16px rgba(217, 123, 58, 0.4)',
                        }
                      }}
                    >
                      Login
                    </Button>
                    <Button
                      variant="outlined"
                      size="large"
                      sx={{
                        color: theme.palette.mode === 'dark' ? '#4caf50' : '#2C4B2B', // Dark Green from logo
                        borderColor: theme.palette.mode === 'dark' ? 'rgba(76, 175, 80, 0.5)' : 'rgba(44, 75, 43, 0.5)',
                        fontWeight: 600,
                        px: 4,
                        py: 1.5,
                        borderRadius: 2,
                        '&:hover': {
                          borderColor: theme.palette.mode === 'dark' ? '#8bc34a' : '#A1A43A', // Olive Green from logo
                          color: theme.palette.mode === 'dark' ? '#8bc34a' : '#A1A43A',
                          bgcolor: theme.palette.mode === 'dark' ? 'rgba(139, 195, 74, 0.1)' : 'rgba(161, 164, 58, 0.05)',
                        }
                      }}
                      onClick={() => window.scrollTo({
                        top: document.getElementById('features')?.offsetTop || 0,
                        behavior: 'smooth'
                      })}
                    >
                      Learn More
                    </Button>
                  </Box>
                </Box>
              </Fade>
            </Grid>

            {!isMobile && (
              <Grid item xs={12} md={6}>
                <Grow in={loaded} timeout={1500}>
                  <Box sx={{
                    position: 'relative',
                    width: '100%',
                    maxWidth: '600px',
                    height: 'auto',
                    ml: 'auto',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center'
                  }}>
                    <Box
                      component="img"
                      src={theme.palette.mode === 'dark'
                        ? "/assets/healthcare-dashboard-dark.svg"
                        : "/assets/healthcare-dashboard-light.svg"}
                      alt="Senior Care Dashboard"
                      sx={{
                        width: '100%',
                        height: 'auto',
                        borderRadius: 4,
                        boxShadow: theme.palette.mode === 'dark'
                          ? '0 20px 40px rgba(0,0,0,0.4)'
                          : '0 20px 40px rgba(0,0,0,0.2)',
                        transform: 'perspective(1000px) rotateY(-10deg) rotateX(5deg)',
                        display: 'block'
                      }}
                    />
                  </Box>
                </Grow>
              </Grid>
            )}
          </Grid>
        </Container>
      </Box>

      {/* Features Section */}
      <Box id="features" sx={{
        py: 10,
        bgcolor: theme.palette.mode === 'dark' ? '#121212' : '#fff'
      }}>
        <Container maxWidth="lg">
          <Box sx={{ textAlign: 'center', mb: 8 }}>
            <Typography
              variant="h2"
              sx={{
                fontWeight: 700,
                mb: 2,
                fontSize: { xs: '2rem', md: '2.5rem' },
                color: theme.palette.mode === 'dark' ? '#4caf50' : '#2e7d32'
              }}
            >
              Specialized for Senior Care
            </Typography>
            <Typography
              variant="h5"
              sx={{
                fontWeight: 300,
                mb: 2,
                maxWidth: '800px',
                mx: 'auto',
                color: theme.palette.mode === 'dark' ? '#aaa' : '#555'
              }}
            >
              Comprehensive tools designed specifically for geriatric healthcare needs
            </Typography>
            <Divider sx={{ width: '80px', mx: 'auto', my: 3, borderColor: '#d68910', borderWidth: 2 }} />
          </Box>

          <Grid container spacing={4}>
            {features.map((feature, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Grow in={loaded} timeout={1000 + (index * 200)}>
                  <Card
                    elevation={0}
                    sx={{
                      height: '100%',
                      borderRadius: 4,
                      p: 2,
                      transition: 'transform 0.3s, box-shadow 0.3s',
                      bgcolor: theme.palette.mode === 'dark' ? '#1e1e1e' : '#fff',
                      '&:hover': {
                        transform: 'translateY(-8px)',
                        boxShadow: theme.palette.mode === 'dark'
                          ? '0 10px 30px rgba(0,0,0,0.3)'
                          : '0 10px 30px rgba(0,0,0,0.1)'
                      },
                      border: theme.palette.mode === 'dark'
                        ? '1px solid rgba(255,255,255,0.05)'
                        : '1px solid rgba(0,0,0,0.05)'
                    }}
                  >
                    <CardContent sx={{ p: 3 }}>
                      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'center' }}>
                        {feature.icon}
                      </Box>
                      <Typography
                        variant="h5"
                        component="h3"
                        sx={{
                          mb: 2,
                          fontWeight: 600,
                          textAlign: 'center',
                          color: theme.palette.mode === 'dark' ? '#4caf50' : '#2e7d32'
                        }}
                      >
                        {feature.title}
                      </Typography>
                      <Typography
                        variant="body1"
                        sx={{
                          textAlign: 'center',
                          color: theme.palette.mode === 'dark' ? '#aaa' : '#555',
                          fontSize: '1.05rem'
                        }}
                      >
                        {feature.description}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grow>
              </Grid>
            ))}
          </Grid>
        </Container>
      </Box>

      {/* Footer */}
      <Box sx={{
        py: 5,
        bgcolor: theme.palette.mode === 'dark' ? '#121212' : '#f0f0f0',
        color: theme.palette.mode === 'dark' ? '#fff' : '#333'
      }}>
        <Container maxWidth="lg">
          <Grid container spacing={2} justifyContent="space-between" alignItems="center">
            <Grid item>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Box
                  component="img"
                  src="/assets/wise-age-logo.svg"
                  alt="WiseAge Wellness Logo"
                  sx={{
                    width: '120px',
                    height: 'auto',
                    mr: 2
                  }}
                />
                <Typography variant="body2" sx={{
                  color: theme.palette.mode === 'dark' ? '#4caf50' : '#2C4B2B',
                  fontWeight: 500
                }}>
                  © {new Date().getFullYear()} WiseAge Wellness. All rights reserved.
                </Typography>
              </Box>
            </Grid>
            <Grid item>
              <Typography variant="body2" sx={{
                color: theme.palette.mode === 'dark' ? '#8bc34a' : '#A1A43A',
                fontWeight: 500
              }}>
                Specialized • Secure • Senior-Focused
              </Typography>
            </Grid>
          </Grid>
        </Container>
      </Box>
    </Box>
  );
};

export default Home;