/* Modern Appointment Form Styles */
.appointment-form-container {
  max-width: 1000px;
  margin: 2rem auto;
  background-color: var(--card-bg-color, #ffffff);
  border-radius: 16px;
  box-shadow: 0 4px 20px var(--shadow-color, rgba(0, 0, 0, 0.08));
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.appointment-form-header {
  display: flex;
  align-items: center;
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, #2C4B2B, #3E6A3D); /* Primary colors from theme */
  color: white;
  position: relative;
  overflow: hidden;
}

.appointment-form-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.3;
}

.back-button {
  display: flex;
  align-items: center;
  color: white;
  font-weight: 500;
  font-size: 0.95rem;
  margin-right: 1.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.15);
  transition: all 0.2s ease;
  text-decoration: none;
}

.back-button:hover {
  background-color: rgba(255, 255, 255, 0.25);
  color: white;
}

.back-button i {
  margin-right: 0.5rem;
}

.form-title-container {
  flex: 1;
}

.form-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  letter-spacing: -0.5px;
}

.form-subtitle {
  margin: 0.5rem 0 0;
  font-size: 1rem;
  font-weight: 400;
  opacity: 0.9;
  display: flex;
  align-items: center;
}

.form-subtitle i {
  margin-right: 0.5rem;
}

.form-alert {
  margin: 1.5rem 2rem 0;
  padding: 1rem;
  border-radius: 8px;
  display: flex;
  align-items: center;
  font-size: 0.95rem;
}

.form-alert.error {
  background-color: #FCF1E9; /* Lightest error color from theme */
  color: #D97B3A; /* Error color from theme */
  border-left: 4px solid #D97B3A; /* Error color from theme */
}

.form-alert i {
  margin-right: 0.75rem;
  font-size: 1.1rem;
}

.form-content {
  padding: 2rem;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-field {
  position: relative;
}

.form-label {
  display: block;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-color, #1e293b);
  margin-bottom: 0.5rem;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color, #e2e8f0);
  border-radius: 8px;
  font-size: 0.95rem;
  transition: all 0.2s ease;
  background-color: var(--bg-color, #f8fafc);
  color: var(--text-color, #1e293b);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-color, #2C4B2B); /* Primary color from theme */
  box-shadow: 0 0 0 3px var(--primary-color-light, rgba(44, 75, 43, 0.1)); /* Primary color with opacity */
  background-color: var(--input-focus-bg-color, white);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: var(--placeholder-color, #94a3b8);
}

.form-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%2364748b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.9rem center;
  padding-right: 2.5rem;
}

.new-patient-option {
  font-weight: 600;
  color: #2C4B2B; /* Primary color from theme */
  background-color: #EFF5EF; /* Lightest primary color from theme */
  border-bottom: 1px solid #e2e8f0;
}

optgroup {
  font-weight: 600;
  color: #64748b;
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.form-hint {
  font-size: 0.8rem;
  margin-top: 0.5rem;
  color: #64748b;
}

.form-hint.error {
  color: #D97B3A; /* Error color from theme */
}

.patient-info-box {
  background-color: var(--card-bg-color, white);
  border-radius: 12px;
  margin-bottom: 2rem;
  overflow: hidden;
  box-shadow: 0 2px 8px var(--shadow-color, rgba(0, 0, 0, 0.05));
  border: 1px solid var(--border-color, #f1f5f9);
  transition: all 0.3s ease;
}

.patient-info-box:hover {
  box-shadow: 0 4px 12px var(--shadow-color, rgba(0, 0, 0, 0.08));
  transform: translateY(-2px);
}

.patient-info-header {
  background-color: var(--primary-color, #2C4B2B); /* Primary color from theme */
  padding: 0.6rem 1rem;
  display: flex;
  align-items: center;
  color: white;
}

.patient-info-header i {
  font-size: 0.9rem;
  color: white;
  margin-right: 0.5rem;
}

.patient-info-header h3 {
  margin: 0;
  font-size: 0.85rem;
  font-weight: 500;
  color: white;
  letter-spacing: 0.3px;
}

.patient-info-content {
  padding: 1.25rem;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  background-color: var(--card-bg-color, white);
}

.info-item {
  display: flex;
  flex-direction: column;
  position: relative;
  padding: 1rem;
  background-color: var(--card-bg-color, white);
  border-radius: 10px;
  border-top: 3px solid var(--primary-color, #2C4B2B); /* Primary color from theme */
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px var(--shadow-color, rgba(0, 0, 0, 0.03));
}

.info-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 3px 6px var(--shadow-color, rgba(0, 0, 0, 0.06));
}

.info-label {
  font-size: 0.7rem;
  font-weight: 600;
  color: var(--text-secondary-color, #64748b);
  margin-bottom: 0.5rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.info-label::before, .info-label::after {
  content: '';
  height: 1px;
  background-color: var(--border-color, #e2e8f0);
  flex: 1;
  margin: 0 0.5rem;
}

.info-value {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-color, #334155);
  text-align: center;
  margin-top: 0.25rem;
}

.btn-link {
  color: var(--primary-color, #2C4B2B);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-size: 0.85rem;
  font-weight: 600;
  margin-top: 1.25rem;
  transition: all 0.2s ease;
  padding: 0.6rem 1.25rem;
  border-radius: 8px;
  background-color: var(--primary-color-lightest, rgba(44, 75, 43, 0.08));
  border: 1px solid var(--primary-color-light, rgba(44, 75, 43, 0.2));
  grid-column: 1 / -1;
  width: fit-content;
  justify-self: center;
  box-shadow: 0 1px 3px var(--shadow-color, rgba(0, 0, 0, 0.05));
}

.btn-link i {
  margin-right: 0.4rem;
  font-size: 0.75rem;
  color: var(--primary-color, #2C4B2B);
}

.btn-link:hover {
  color: var(--primary-color-hover, #3E6A3D);
  background-color: var(--primary-color-light, rgba(44, 75, 43, 0.15));
  transform: translateY(-1px);
  box-shadow: 0 2px 5px var(--shadow-color, rgba(0, 0, 0, 0.1));
}

.btn-link:active {
  color: var(--primary-color-active, #1E3A1D);
  transform: translateY(0);
  box-shadow: 0 1px 2px var(--shadow-color, rgba(0, 0, 0, 0.05));
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--border-color, #e2e8f0);
}

.btn-cancel {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  background-color: var(--button-secondary-bg, #f1f5f9);
  color: var(--text-secondary-color, #64748b);
  border: 1px solid var(--border-color, #e2e8f0);
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
}

.btn-cancel:hover {
  background-color: var(--button-secondary-hover-bg, #e2e8f0);
  color: var(--text-color, #1e293b);
}

.btn-save {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 600;
  background-color: var(--primary-color, #2C4B2B); /* Primary color from theme */
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  box-shadow: 0 4px 6px var(--primary-color-light, rgba(44, 75, 43, 0.2)); /* Primary color with opacity */
}

.btn-save:hover:not(:disabled) {
  background-color: var(--primary-color-hover, #3E6A3D); /* Light primary color from theme */
  transform: translateY(-2px);
  box-shadow: 0 6px 12px var(--primary-color-light, rgba(44, 75, 43, 0.3)); /* Primary color with opacity */
}

.btn-save:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 4px var(--primary-color-light, rgba(44, 75, 43, 0.2)); /* Primary color with opacity */
}

.btn-save:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-danger {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 600;
  background-color: var(--error-color, #D97B3A); /* Error color from theme */
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  box-shadow: 0 4px 6px var(--error-color-light, rgba(217, 123, 58, 0.2)); /* Error color with opacity */
}

.btn-danger:hover:not(:disabled) {
  background-color: var(--error-color-hover, #B5652F); /* Dark error color from theme */
  transform: translateY(-2px);
  box-shadow: 0 6px 12px var(--error-color-light, rgba(217, 123, 58, 0.3)); /* Error color with opacity */
}

.btn-danger:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 4px var(--error-color-light, rgba(217, 123, 58, 0.2)); /* Error color with opacity */
}

.btn-save i,
.btn-cancel i,
.btn-danger i {
  margin-right: 0.5rem;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  font-size: 1.1rem;
  color: var(--text-secondary-color, #64748b);
}

.loading-container i {
  margin-right: 0.75rem;
  color: var(--primary-color, #2C4B2B); /* Primary color from theme */
}

/* Time slot grid */
.time-slot-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 0.75rem;
  margin-top: 0.5rem;
}

.time-slot {
  padding: 0.5rem;
  border-radius: 6px;
  border: 1px solid var(--border-color, #e2e8f0);
  text-align: center;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  background-color: var(--bg-color, #f8fafc);
}

.time-slot:hover {
  border-color: var(--primary-color, #2C4B2B); /* Primary color from theme */
  background-color: var(--primary-color-lightest, #EFF5EF); /* Lightest primary color from theme */
}

.time-slot.selected {
  background-color: var(--primary-color, #2C4B2B); /* Primary color from theme */
  color: white;
  border-color: var(--primary-color, #2C4B2B); /* Primary color from theme */
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .appointment-form-header {
    flex-direction: column;
    align-items: flex-start;
    padding: 1.25rem 1.5rem;
  }

  .back-button {
    margin-right: 0;
    margin-bottom: 1rem;
  }

  .form-content {
    padding: 1.5rem;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
  }

  .patient-info-content {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  @media (max-width: 480px) {
    .patient-info-content {
      grid-template-columns: 1fr;
    }
  }

  .form-actions {
    flex-direction: column-reverse;
    gap: 0.75rem;
  }

  .btn-cancel,
  .btn-save {
    width: 100%;
    justify-content: center;
  }
}

/* Modal Overlay */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: var(--card-bg-color, white);
  border-radius: 8px;
  padding: 1.5rem;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 10px 25px var(--shadow-color, rgba(0, 0, 0, 0.1));
}

.modal-content h3 {
  display: flex;
  align-items: center;
  font-size: 1.2rem;
  margin-top: 0;
  color: var(--error-color, #D97B3A); /* Error color from theme */
}

.modal-content h3 i {
  margin-right: 0.75rem;
  color: var(--error-color, #D97B3A); /* Error color from theme */
}

.modal-content p {
  margin-bottom: 1.5rem;
  color: var(--text-color, #334155);
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}
