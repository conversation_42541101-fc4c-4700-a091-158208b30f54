import React, { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import axios from 'axios';
import { Appointment, Doctor, Patient } from '../../types';
import AuthContext from '../../context/AuthContext';
import { API_URL } from '../../config';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import Button from '@mui/material/Button';
import LoadingSpinner from '../common/LoadingSpinner';
import './PatientDialog.css';
import './AppointmentForm.css';

interface AppointmentFormData {
  patient_id: string;
  doctor_id: string;
  title: string;
  date: string;
  start_time: string;
  end_time: string;
  status: string;
  type: string;
  notes: string;
}

interface NewPatientData {
  first_name: string;
  last_name: string;
  date_of_birth: string;
  gender: string;
  phone: string;
  email: string;
  address: string;
}

const AppointmentForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useContext(AuthContext);
  const isEditMode = !!id;

  // States
  const [formData, setFormData] = useState<AppointmentFormData>({
    patient_id: '',
    doctor_id: '',
    title: '',
    date: '',
    start_time: '',
    end_time: '',
    status: isEditMode ? '' : 'scheduled', // Default to scheduled for new appointments
    type: '',
    notes: ''
  });

  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(isEditMode);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [showConfirmDelete, setShowConfirmDelete] = useState(false);

  // New patient dialog states
  const [newPatientDialogOpen, setNewPatientDialogOpen] = useState(false);
  const [newPatientData, setNewPatientData] = useState<NewPatientData>({
    first_name: '',
    last_name: '',
    date_of_birth: '',
    gender: '',
    phone: '',
    email: '',
    address: ''
  });
  const [creatingPatient, setCreatingPatient] = useState(false);
  const [patientError, setPatientError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch doctors list
        const doctorsRes = await axios.get(`${API_URL}/api/doctors`);
        setDoctors(doctorsRes.data);

        // Fetch patients list
        const patientsRes = await axios.get(`${API_URL}/api/patients`);
        setPatients(patientsRes.data);

        // If in edit mode, fetch the appointment details
        if (isEditMode) {
          console.log(`Fetching appointment details for ID: ${id}`);
          const appointmentRes = await axios.get(`${API_URL}/api/appointments/${id}`);
          const appointment = appointmentRes.data;
          console.log('Appointment data received:', appointment);

          // Map server fields to form fields
          const formattedData = {
            patient_id: appointment.patient_id.toString(),
            doctor_id: appointment.doctor_id.toString(),
            title: appointment.title || appointment.reason || '',
            date: appointment.date || '',
            start_time: appointment.start_time || appointment.time || '',
            end_time: appointment.end_time || '',
            status: appointment.status || 'scheduled',
            type: appointment.type || 'checkup',
            notes: appointment.notes || ''
          };

          console.log('Formatted form data:', formattedData);
          setFormData(formattedData);

          // Find the selected patient
          const patient = patientsRes.data.find((p: Patient) => p.patient_id === appointment.patient_id);
          if (patient) {
            console.log('Selected patient:', patient);
            setSelectedPatient(patient);
          } else {
            console.warn('Patient not found for ID:', appointment.patient_id);
          }
        }

        setLoading(false);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again later.');
        setLoading(false);
      }
    };

    fetchData();
  }, [id, isEditMode]);

  const onChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // If changing the patient, update selectedPatient state
    if (name === 'patient_id') {
      if (value === 'new') {
        // Check if doctor is selected before opening new patient dialog
        if (!formData.doctor_id) {
          setError('Please select a doctor before adding a new patient');
          return; // Don't update formData
        }

        // Reset any previous patient error
        setPatientError(null);

        // Open the new patient dialog
        setNewPatientDialogOpen(true);
        return; // Don't update formData yet
      } else if (value) {
        const patient = patients.find(p => p.patient_id.toString() === value);
        if (patient) {
          setSelectedPatient(patient);
        } else {
          setSelectedPatient(null);
        }
      } else {
        setSelectedPatient(null);
      }
    }

    setFormData({ ...formData, [name]: value });
  };

  // Handler for new patient form changes
  const onNewPatientChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setNewPatientData({ ...newPatientData, [name]: value });
  };

  // Function to delete an appointment
  const deleteAppointment = async () => {
    try {
      // Get auth token
      const token = localStorage.getItem('token');
      if (!token) {
        setError('Authentication error. Please log in again.');
        return;
      }

      const headers = {
        'Content-Type': 'application/json',
        'x-auth-token': token
      };

      await axios.delete(`${API_URL}/api/appointments/${id}`, { headers });
      alert('Appointment deleted successfully!');
      navigate('/appointments');
    } catch (err) {
      console.error('Error deleting appointment:', err);
      setError('Failed to delete appointment. Please try again.');
      setShowConfirmDelete(false);
    }
  };

  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);

    try {
      // Validate form data
      const validationErrors = [];

      if (!formData.patient_id) {
        validationErrors.push('Please select a patient');
      }

      if (!formData.doctor_id) {
        validationErrors.push('Please select a doctor');
      }

      if (!formData.date) {
        validationErrors.push('Please select a date');
      }

      if (!formData.start_time) {
        validationErrors.push('Please select a start time');
      }

      if (!formData.end_time) {
        validationErrors.push('Please select an end time');
      }

      if (formData.start_time && formData.end_time && formData.start_time >= formData.end_time) {
        validationErrors.push('End time must be after start time');
      }

      if (validationErrors.length > 0) {
        setError(validationErrors.join('. '));
        setSubmitting(false);
        return;
      }

      console.log('Form data being submitted:', formData);

      // Generate a default reason if title is empty
      const defaultReason = formData.title || `${formData.type.charAt(0).toUpperCase() + formData.type.slice(1)} appointment`;

      // Map client form fields to server expected fields
      const serverAppointmentData = {
        patient_id: parseInt(formData.patient_id),
        doctor_id: parseInt(formData.doctor_id),
        reason: defaultReason, // Use title as reason, or generate from type if empty
        title: formData.title, // Also include title directly
        date: formData.date,
        time: formData.start_time, // Map start_time to time
        start_time: formData.start_time, // Also include start_time directly
        end_time: formData.end_time, // Make sure end_time is included
        status: formData.status,
        notes: formData.notes,
        type: formData.type
      };

      console.log('End time being sent to server:', formData.end_time);

      console.log('Submitting appointment data to server:', serverAppointmentData);

      // Get auth token
      const token = localStorage.getItem('token');
      if (!token) {
        setError('Authentication error. Please log in again.');
        setSubmitting(false);
        return;
      }

      const headers = {
        'Content-Type': 'application/json',
        'x-auth-token': token
      };

      let response;

      // If editing an existing appointment
      if (isEditMode) {
        console.log(`Updating appointment ${id}...`);
        response = await axios.put(
          `${API_URL}/api/appointments/${id}`,
          serverAppointmentData,
          { headers }
        );
        console.log('Update successful:', response.data);

        // Show success message
        alert('Appointment updated successfully!');
        navigate(`/appointments/${id}`);
      } else {
        // If creating a new appointment
        console.log('Creating new appointment...');
        response = await axios.post(
          `${API_URL}/api/appointments`,
          serverAppointmentData,
          { headers }
        );
        console.log('Creation successful:', response.data);

        // Show success message
        alert('Appointment created successfully!');
        navigate(`/appointments/${response.data.appointment_id}`);
      }
    } catch (err: any) {
      console.error('Error saving appointment:', err);

      // More detailed error reporting
      if (err.response) {
        console.error('Error response data:', err.response.data);
        console.error('Error response status:', err.response.status);

        if (err.response.data.msg) {
          setError(`Failed to save appointment: ${err.response.data.msg}`);
        } else if (err.response.data.errors && err.response.data.errors.length > 0) {
          setError(`Failed to save appointment: ${err.response.data.errors[0].msg}`);
        } else {
          setError('Failed to save appointment. Please check your inputs and try again.');
        }
      } else if (err.request) {
        // The request was made but no response was received
        console.error('Error request:', err.request);
        setError('Network error - no response from server. Please check your internet connection and try again.');
      } else {
        // Something happened in setting up the request
        setError(`Error: ${err.message}`);
      }

      setSubmitting(false);
    }
  };

  // Function to create a new patient
  const createNewPatient = async () => {
    setCreatingPatient(true);
    setPatientError(null);

    try {
      // Validate required fields
      if (!newPatientData.first_name || !newPatientData.last_name || !newPatientData.date_of_birth || !newPatientData.gender) {
        setPatientError('Please fill in all required fields');
        setCreatingPatient(false);
        return;
      }

      // Get auth token
      const token = localStorage.getItem('token');
      if (!token) {
        setPatientError('Authentication error. Please log in again.');
        setCreatingPatient(false);
        return;
      }

      // Create a unique ID for the patient (could be handled by the server)
      const uniqueId = `P${Date.now().toString().slice(-6)}`;

      // Get the doctor ID from the form data
      const doctorId = formData.doctor_id;

      if (!doctorId) {
        setPatientError('Doctor ID is required. Please select a doctor first.');
        setCreatingPatient(false);
        return;
      }

      // Find the selected doctor to get their details
      const selectedDoctor = doctors.find(d => d.doctor_id.toString() === doctorId);

      if (!selectedDoctor) {
        setPatientError('Selected doctor not found. Please try again.');
        setCreatingPatient(false);
        return;
      }

      console.log('Creating patient with doctor:', selectedDoctor);

      // Prepare patient data
      const patientData = {
        ...newPatientData,
        unique_id: uniqueId,
        doctor_id: parseInt(doctorId) // Make sure to use the correct doctor_id
      };

      // Send request to create patient
      const response = await axios.post(
        `${API_URL}/api/patients`,
        patientData,
        {
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          }
        }
      );

      console.log('New patient created:', response.data);

      // Add the new patient to the patients list
      const newPatient = response.data;
      setPatients([...patients, newPatient]);

      // Select the new patient
      setSelectedPatient(newPatient);
      setFormData({
        ...formData,
        patient_id: newPatient.patient_id.toString()
      });

      // Close the dialog
      setNewPatientDialogOpen(false);

      // Reset the form
      setNewPatientData({
        first_name: '',
        last_name: '',
        date_of_birth: '',
        gender: '',
        phone: '',
        email: '',
        address: ''
      });

    } catch (err: any) {
      console.error('Error creating patient:', err);

      // More detailed error logging
      if (err.response) {
        console.error('Error response data:', err.response.data);
        console.error('Error response status:', err.response.status);

        if (err.response.data.msg) {
          setPatientError(`Failed to create patient: ${err.response.data.msg}`);
        } else if (err.response.data.errors && err.response.data.errors.length > 0) {
          setPatientError(`Failed to create patient: ${err.response.data.errors[0].msg}`);
        } else {
          setPatientError('Failed to create patient. Server returned an error.');
        }
      } else if (err.request) {
        // The request was made but no response was received
        console.error('Error request:', err.request);
        setPatientError('Network error - no response from server');
      } else {
        // Something happened in setting up the request
        setPatientError(`Error: ${err.message}`);
      }
    }

    setCreatingPatient(false);
  };

  // Function to calculate age from date of birth
  const calculateAge = (dateOfBirth: string): number => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }

    return age;
  };

  // Generate time slot options (30-minute intervals)
  const getTimeOptions = () => {
    const options = [];
    for (let hour = 8; hour < 18; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const formattedHour = hour.toString().padStart(2, '0');
        const formattedMinute = minute.toString().padStart(2, '0');
        const time = `${formattedHour}:${formattedMinute}`;
        options.push(
          <option key={time} value={time}>
            {hour > 12 ? (hour - 12) : hour}:{formattedMinute} {hour >= 12 ? 'PM' : 'AM'}
          </option>
        );
      }
    }
    return options;
  };

  if (loading) {
    return <LoadingSpinner size="medium" message="Loading appointment form..." />;
  }

  return (
    <div className="appointment-form-container">
      <div className="appointment-form-header">
        <div className="form-title-container">
          <h1 className="form-title">
            {isEditMode ? 'Edit Appointment' : 'New Appointment'}
          </h1>
          <p className="form-subtitle">
            <i className="fas fa-calendar-alt"></i>{' '}
            {isEditMode
              ? `Update appointment details`
              : 'Schedule a new appointment for a patient'}
          </p>
        </div>
      </div>

      {error && (
        <div className="form-alert error">
          <i className="fas fa-exclamation-circle"></i> {error}
        </div>
      )}

      {/* New Patient Dialog - Modern, Clean, Minimalist, Sleek Design */}
      <Dialog
        open={newPatientDialogOpen}
        onClose={() => setNewPatientDialogOpen(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          style: { borderRadius: '8px', overflow: 'hidden' }
        }}
      >
        <div className="patient-dialog-title">
          <i className="fas fa-user-plus patient-dialog-title-icon" style={{ color: 'var(--primary-color, #2C4B2B)' }}></i>
          <span className="patient-dialog-title-text">Add New Patient</span>
        </div>

        {formData.doctor_id && (
          <div className="patient-dialog-subtitle">
            <i className="fas fa-user-md patient-dialog-subtitle-icon" style={{ color: 'var(--primary-color, #2C4B2B)' }}></i>
            <span>
              Patient will be assigned to: {doctors.find(d => d.doctor_id.toString() === formData.doctor_id) ?
                `Dr. ${doctors.find(d => d.doctor_id.toString() === formData.doctor_id)?.first_name} ${doctors.find(d => d.doctor_id.toString() === formData.doctor_id)?.last_name}` :
                `Doctor ID: ${formData.doctor_id}`}
            </span>
          </div>
        )}

        <DialogContent className="patient-dialog-content">
          {patientError && (
            <div className="patient-dialog-error">
              <i className="fas fa-exclamation-circle"></i> {patientError}
            </div>
          )}

          <div className="patient-form-row">
            <div className="patient-form-field">
              <label className="patient-form-label" htmlFor="first_name">
                <i className="fas fa-user"></i> First Name<span className="patient-form-required">*</span>
              </label>
              <input
                type="text"
                id="first_name"
                name="first_name"
                value={newPatientData.first_name}
                onChange={onNewPatientChange}
                required
                className="patient-form-input"
                placeholder="Enter first name"
              />
            </div>

            <div className="patient-form-field">
              <label className="patient-form-label" htmlFor="last_name">
                <i className="fas fa-user"></i> Last Name<span className="patient-form-required">*</span>
              </label>
              <input
                type="text"
                id="last_name"
                name="last_name"
                value={newPatientData.last_name}
                onChange={onNewPatientChange}
                required
                className="patient-form-input"
                placeholder="Enter last name"
              />
            </div>
          </div>

          <div className="patient-form-row">
            <div className="patient-form-field">
              <label className="patient-form-label" htmlFor="date_of_birth">
                <i className="fas fa-calendar-alt"></i> Date of Birth<span className="patient-form-required">*</span>
              </label>
              <input
                type="date"
                id="date_of_birth"
                name="date_of_birth"
                value={newPatientData.date_of_birth}
                onChange={onNewPatientChange}
                required
                className="patient-form-input"
                max={new Date().toISOString().split('T')[0]}
              />
            </div>

            <div className="patient-form-field">
              <label className="patient-form-label" htmlFor="gender">
                <i className="fas fa-venus-mars"></i> Gender<span className="patient-form-required">*</span>
              </label>
              <select
                id="gender"
                name="gender"
                value={newPatientData.gender}
                onChange={onNewPatientChange}
                required
                className="patient-form-select"
              >
                <option value="">Select gender</option>
                <option value="Male">Male</option>
                <option value="Female">Female</option>
                <option value="Other">Other</option>
                <option value="Prefer not to say">Prefer not to say</option>
              </select>
            </div>
          </div>

          <div className="patient-form-row">
            <div className="patient-form-field">
              <label className="patient-form-label" htmlFor="phone">
                <i className="fas fa-phone"></i> Phone Number
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={newPatientData.phone}
                onChange={onNewPatientChange}
                className="patient-form-input"
                placeholder="Enter phone number"
              />
            </div>

            <div className="patient-form-field">
              <label className="patient-form-label" htmlFor="email">
                <i className="fas fa-envelope"></i> Email Address
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={newPatientData.email}
                onChange={onNewPatientChange}
                className="patient-form-input"
                placeholder="Enter email address"
              />
            </div>
          </div>

          <div className="patient-form-row single-field">
            <div className="patient-form-field">
              <label className="patient-form-label" htmlFor="address">
                <i className="fas fa-home"></i> Address
              </label>
              <input
                type="text"
                id="address"
                name="address"
                value={newPatientData.address}
                onChange={onNewPatientChange}
                className="patient-form-input"
                placeholder="Enter address"
              />
            </div>
          </div>
        </DialogContent>

        <div className="patient-dialog-actions">
          <button
            className="patient-dialog-btn-cancel"
            onClick={() => setNewPatientDialogOpen(false)}
          >
            Cancel
          </button>
          <button
            className="patient-dialog-btn-create"
            onClick={createNewPatient}
            disabled={creatingPatient}
            style={{
              padding: '0.5rem 1rem',
              borderRadius: '6px',
              fontSize: '0.85rem',
              fontWeight: 500,
              backgroundColor: 'var(--primary-color, #2C4B2B)',
              color: 'white',
              border: 'none',
              cursor: creatingPatient ? 'not-allowed' : 'pointer',
              display: 'inline-flex',
              alignItems: 'center',
              opacity: creatingPatient ? 0.6 : 1
            }}
          >
            {creatingPatient ? (
              <>
                <i className="fas fa-spinner fa-spin"></i>
                Creating...
              </>
            ) : (
              <>
                <i className="fas fa-plus"></i>
                Create Patient
              </>
            )}
          </button>
        </div>
      </Dialog>

      <div className="form-content">
        <form onSubmit={onSubmit}>
          <div className="form-row">
            <div className="form-field">
              <label className="form-label" htmlFor="patient_id">
                <i className="fas fa-user-injured"></i> Patient
              </label>
              {isEditMode ? (
                // In edit mode, show a disabled select with just the selected patient
                <select
                  id="patient_id"
                  name="patient_id"
                  value={formData.patient_id}
                  disabled
                  className="form-select"
                >
                  {selectedPatient && (
                    <option value={selectedPatient.patient_id}>
                      {selectedPatient.first_name} {selectedPatient.last_name} ({selectedPatient.unique_id})
                    </option>
                  )}
                </select>
              ) : (
                // In create mode, show the full patient selection
                <select
                  id="patient_id"
                  name="patient_id"
                  value={formData.patient_id}
                  onChange={onChange}
                  required
                  className="form-select"
                >
                  <option value="">-- Select Patient --</option>
                  <option value="new" className="new-patient-option">+ Add New Patient</option>
                  <optgroup label="Existing Patients">
                    {patients.map(patient => (
                      <option key={patient.patient_id} value={patient.patient_id}>
                        {patient.first_name} {patient.last_name} ({patient.unique_id})
                      </option>
                    ))}
                  </optgroup>
                </select>
              )}
            </div>

            <div className="form-field">
              <label className="form-label" htmlFor="doctor_id">
                <i className="fas fa-user-md"></i> Doctor
              </label>
              <select
                id="doctor_id"
                name="doctor_id"
                value={formData.doctor_id}
                onChange={onChange}
                required
                className="form-select"
              >
                <option value="">-- Select Doctor --</option>
                {doctors.map(doctor => (
                  <option key={doctor.doctor_id} value={doctor.doctor_id}>
                    Dr. {doctor.first_name} {doctor.last_name} ({doctor.specialty})
                  </option>
                ))}
              </select>
            </div>
          </div>

          {selectedPatient && (
            <div className="patient-info-box">
              <div className="patient-info-header">
                <i className="fas fa-user-circle"></i>
                <h3>Patient Profile</h3>
              </div>
              <div className="patient-info-content">
                <div className="info-item">
                  <span className="info-label">Patient ID</span>
                  <span className="info-value">{selectedPatient.unique_id}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Full Name</span>
                  <span className="info-value">{selectedPatient.first_name} {selectedPatient.last_name}</span>
                </div>

                <div className="info-item">
                  <span className="info-label">Gender</span>
                  <span className="info-value">{selectedPatient.gender}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Age</span>
                  <span className="info-value">
                    {calculateAge(selectedPatient.date_of_birth)} years
                  </span>
                </div>
                <div className="info-item">
                  <span className="info-label">Contact</span>
                  <span className="info-value">{selectedPatient.phone || 'No phone number'}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Email Address</span>
                  <span className="info-value">{selectedPatient.email || 'Not provided'}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Address</span>
                  <span className="info-value">{selectedPatient.address || 'Not provided'}</span>
                </div>
                <div className="info-item">
                  <span className="info-label">Doctor</span>
                  <span className="info-value">
                    {doctors.find(d => d.doctor_id === selectedPatient.doctor_id)
                      ? `Dr. ${doctors.find(d => d.doctor_id === selectedPatient.doctor_id)?.first_name} ${doctors.find(d => d.doctor_id === selectedPatient.doctor_id)?.last_name}`
                      : 'Not assigned'}
                  </span>
                </div>
                <Link to={`/patients/${selectedPatient.patient_id}`} className="btn-link">
                  <i className="fas fa-external-link-alt"></i> View Complete Patient Record
                </Link>
              </div>
            </div>
          )}

          <div className="form-row">
            <div className="form-field">
              <label className="form-label" htmlFor="type">
                <i className="fas fa-tag"></i> Appointment Type
              </label>
              <select
                id="type"
                name="type"
                value={formData.type}
                onChange={onChange}
                required
                className="form-select"
              >
                <option value="">-- Select Appointment Type --</option>
                <option value="checkup">Checkup</option>
                <option value="follow-up">Follow-up</option>
                <option value="urgent">Urgent</option>
                <option value="consultation">Consultation</option>
                <option value="lab-work">Lab Work</option>
                <option value="vaccination">Vaccination</option>
                <option value="physical-therapy">Physical Therapy</option>
                <option value="mental-health">Mental Health</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div className="form-field">
              <label className="form-label" htmlFor="title">
                <i className="fas fa-heading"></i> Description/Notes
              </label>
              <input
                type="text"
                id="title"
                name="title"
                value={formData.title}
                onChange={onChange}
                className="form-input"
                placeholder="E.g., Annual Checkup, Follow-up for Diabetes, Blood Work"
              />
              <p className="form-hint">Additional details about the appointment (optional)</p>
            </div>
          </div>

          <div className="form-row">
            <div className="form-field">
              <label className="form-label" htmlFor="date">
                <i className="fas fa-calendar-day"></i> Date
              </label>
              <input
                type="date"
                id="date"
                name="date"
                value={formData.date}
                onChange={onChange}
                required
                className="form-input"
                min={new Date().toISOString().split('T')[0]}
              />
            </div>

            <div className="form-field">
              <label className="form-label" htmlFor="status">
                <i className="fas fa-clipboard-check"></i> Status
              </label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={onChange}
                required
                className="form-select"
              >
                {isEditMode ? (
                  // Show all status options in edit mode
                  <>
                    <option value="scheduled">Scheduled</option>
                    <option value="confirmed">Confirmed</option>
                    <option value="in-progress">In Progress</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                    <option value="no-show">No-Show</option>
                    <option value="rescheduled">Rescheduled</option>
                  </>
                ) : (
                  // Show only relevant options for new appointments
                  <>
                    <option value="scheduled">Scheduled</option>
                    <option value="confirmed">Confirmed</option>
                    <option value="pending">Pending Confirmation</option>
                  </>
                )}
              </select>
              <p className="form-hint">
                {isEditMode
                  ? "Current status of the appointment"
                  : "New appointments are typically 'Scheduled' or 'Pending Confirmation'"}
              </p>
            </div>
          </div>

          <div className="form-row">
            <div className="form-field">
              <label className="form-label" htmlFor="start_time">
                <i className="fas fa-clock"></i> Start Time
              </label>
              <select
                id="start_time"
                name="start_time"
                value={formData.start_time}
                onChange={onChange}
                required
                className="form-select"
              >
                <option value="">-- Select Start Time --</option>
                {getTimeOptions()}
              </select>
            </div>

            <div className="form-field">
              <label className="form-label" htmlFor="end_time">
                <i className="fas fa-hourglass-end"></i> End Time
              </label>
              <select
                id="end_time"
                name="end_time"
                value={formData.end_time}
                onChange={onChange}
                required
                className="form-select"
              >
                <option value="">-- Select End Time --</option>
                {getTimeOptions()}
              </select>
              {formData.start_time && formData.end_time && formData.start_time >= formData.end_time && (
                <p className="form-hint error">
                  <i className="fas fa-exclamation-triangle"></i> End time must be after start time
                </p>
              )}
            </div>
          </div>

          <div className="form-row">
            <div className="form-field">
              <label className="form-label" htmlFor="notes">
                <i className="fas fa-sticky-note"></i> Notes
              </label>
              <textarea
                id="notes"
                name="notes"
                value={formData.notes}
                onChange={onChange}
                className="form-textarea"
                rows={4}
                placeholder="Add any relevant notes about the appointment..."
              ></textarea>
            </div>
          </div>

          <div className="form-actions">
            <Link to="/appointments" className="btn-cancel" style={{
              padding: '0.75rem 1.5rem',
              borderRadius: '8px',
              fontSize: '0.95rem',
              fontWeight: 500,
              backgroundColor: '#f1f5f9',
              color: '#64748b',
              border: '1px solid #e2e8f0',
              cursor: 'pointer',
              transition: 'all 0.2s ease',
              textDecoration: 'none',
              display: 'inline-flex',
              alignItems: 'center'
            }}>
              <i className="fas fa-times" style={{ marginRight: '0.5rem' }}></i> Cancel
            </Link>

            {isEditMode && (
              <button
                type="button"
                className="btn-danger"
                onClick={() => setShowConfirmDelete(true)}
                style={{
                  padding: '0.75rem 1.5rem',
                  borderRadius: '8px',
                  fontSize: '0.95rem',
                  fontWeight: 600,
                  backgroundColor: '#D97B3A',
                  color: 'white',
                  border: 'none',
                  cursor: 'pointer',
                  display: 'inline-flex',
                  alignItems: 'center',
                  boxShadow: '0 4px 6px rgba(217, 123, 58, 0.2)'
                }}
              >
                <i className="fas fa-trash-alt" style={{ marginRight: '0.5rem' }}></i> Delete Appointment
              </button>
            )}

            <button
              type="submit"
              className="btn-save"
              disabled={!!submitting || !!(formData.start_time && formData.end_time && formData.start_time >= formData.end_time)}
              style={{
                padding: '0.75rem 1.5rem',
                borderRadius: '8px',
                fontSize: '0.95rem',
                fontWeight: 600,
                backgroundColor: '#2C4B2B',
                color: 'white',
                border: 'none',
                cursor: submitting || (formData.start_time && formData.end_time && formData.start_time >= formData.end_time) ? 'not-allowed' : 'pointer',
                display: 'inline-flex',
                alignItems: 'center',
                boxShadow: '0 4px 6px rgba(44, 75, 43, 0.2)',
                opacity: submitting || (formData.start_time && formData.end_time && formData.start_time >= formData.end_time) ? 0.6 : 1
              }}
            >
              {submitting ? (
                <>
                  <i className="fas fa-spinner fa-spin"></i> Saving...
                </>
              ) : (
                <>
                  <i className="fas fa-save"></i> {isEditMode ? 'Update Appointment' : 'Schedule Appointment'}
                </>
              )}
            </button>
          </div>

          {/* Delete Confirmation Modal */}
          {showConfirmDelete && (
            <div className="modal-overlay">
              <div className="modal-content">
                <h3><i className="fas fa-exclamation-triangle"></i> Confirm Delete</h3>
                <p>Are you sure you want to delete this appointment? This action cannot be undone.</p>
                <div className="modal-actions">
                  <button
                    className="btn-cancel"
                    onClick={() => setShowConfirmDelete(false)}
                    style={{
                      padding: '0.75rem 1.5rem',
                      borderRadius: '8px',
                      fontSize: '0.95rem',
                      fontWeight: 500,
                      backgroundColor: '#f1f5f9',
                      color: '#64748b',
                      border: '1px solid #e2e8f0',
                      cursor: 'pointer',
                      display: 'inline-flex',
                      alignItems: 'center'
                    }}
                  >
                    Cancel
                  </button>
                  <button
                    className="btn-danger"
                    onClick={deleteAppointment}
                    style={{
                      padding: '0.75rem 1.5rem',
                      borderRadius: '8px',
                      fontSize: '0.95rem',
                      fontWeight: 600,
                      backgroundColor: '#D97B3A',
                      color: 'white',
                      border: 'none',
                      cursor: 'pointer',
                      display: 'inline-flex',
                      alignItems: 'center',
                      boxShadow: '0 4px 6px rgba(217, 123, 58, 0.2)'
                    }}
                  >
                    Delete Appointment
                  </button>
                </div>
              </div>
            </div>
          )}
        </form>
      </div>
    </div>
  );
};

export default AppointmentForm;