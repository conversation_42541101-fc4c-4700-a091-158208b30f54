import React, { useState, useEffect, useContext, useMemo } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import AuthContext from '../../context/AuthContext';
import { formatDate, formatTime, getCurrentDate } from '../../utils/dateUtils';
import { API_URL } from '../../config';
import Dialog from '@mui/material/Dialog';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogContentText from '@mui/material/DialogContentText';
import DialogTitle from '@mui/material/DialogTitle';
import Button from '@mui/material/Button';
import './AppointmentDashboard.modern.css';

interface Appointment {
  appointment_id: number;
  patient_id: number;
  patient_name: string;
  date: string;
  reason?: string;
  doctor_id?: number;
  doctor_name?: string;
  // These fields are expected by the UI but may not be in the server response
  appointment_date?: string;
  start_time?: string;
  end_time?: string;
  status?: string;
  notes?: string;
  doctor_specialty?: string;
}

const AppointmentDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [filteredAppointments, setFilteredAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [dateFilter, setDateFilter] = useState<string>('');
  const [viewMode, setViewMode] = useState<string>('card');
  const [updatingStatus, setUpdatingStatus] = useState<number | null>(null);

  // Dialog states
  const [actionDialogOpen, setActionDialogOpen] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  const [processingAction, setProcessingAction] = useState(false);

  const { user } = useContext(AuthContext);

  // Get current date for the greeting
  const currentDate = useMemo(() => {
    const now = new Date();
    return now.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }, []);

  // Stats calculations
  const todayAppointments = filteredAppointments.filter(
    app => {
      if (!app.date) return false;
      return new Date(app.date).toDateString() === new Date().toDateString();
    }
  ).length;

  const upcomingAppointments = filteredAppointments.filter(
    app => {
      if (!app.date) return false;
      return new Date(app.date) > new Date() && (app.status === 'scheduled' || !app.status);
    }
  ).length;

  const completedAppointments = filteredAppointments.filter(
    app => app.status === 'completed'
  ).length;

  const cancelledAppointments = filteredAppointments.filter(
    app => app.status === 'cancelled' || app.status === 'no-show'
  ).length;

  useEffect(() => {
    const fetchAppointments = async () => {
      try {
        setLoading(true);
        console.log('Fetching appointments from:', `${API_URL}/api/appointments`);

        // Get token from localStorage
        const token = localStorage.getItem('token');
        console.log('Auth token available:', token ? 'Yes' : 'No');

        if (!token) {
          throw new Error('No authentication token found');
        }

        const response = await axios.get(`${API_URL}/api/appointments`, {
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': token
          },
        });

        console.log('Appointments response:', response.data);

        // Map the server response to the client structure
        const mappedAppointments = response.data.map((app: any) => {
          console.log('Processing appointment:', app);
          console.log('Start time:', app.start_time);
          console.log('End time:', app.end_time);

          return {
            appointment_id: app.appointment_id,
            patient_id: app.patient_id,
            patient_name: app.patient_name,
            doctor_id: app.doctor_id,
            doctor_name: app.doctor_name,
            // Map date to appointment_date for compatibility
            appointment_date: app.date || app.appointment_date,
            date: app.date,
            // Include other fields
            reason: app.reason,
            notes: app.notes,
            status: app.status || 'scheduled',
            start_time: app.start_time,
            end_time: app.end_time,
            doctor_specialty: app.doctor_specialty
          };
        });

        setAppointments(mappedAppointments);
        setFilteredAppointments(mappedAppointments);
        setLoading(false);
      } catch (err: any) {
        console.error('Error fetching appointments:', err);

        // More detailed error reporting
        if (err.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          console.error('Error response data:', err.response.data);
          console.error('Error response status:', err.response.status);
          console.error('Error response headers:', err.response.headers);
          setError(`Failed to fetch appointments: ${err.response.status} - ${err.response.data.msg || 'Unknown error'}`);
        } else if (err.request) {
          // The request was made but no response was received
          console.error('Error request:', err.request);
          setError('Failed to fetch appointments: No response from server');
        } else {
          // Something happened in setting up the request that triggered an Error
          console.error('Error message:', err.message);
          setError(`Failed to fetch appointments: ${err.message}`);
        }

        setLoading(false);
      }
    };

    fetchAppointments();
  }, []);

  useEffect(() => {
    let result = [...appointments];

    // Apply tab filter
    if (activeTab === 'today') {
      result = result.filter(app => {
        if (!app.date) return false;
        return new Date(app.date).toDateString() === new Date().toDateString();
      });
    } else if (activeTab === 'upcoming') {
      result = result.filter(app => {
        if (!app.date) return false;
        return new Date(app.date) > new Date() && (app.status === 'scheduled' || !app.status);
      });
    } else if (activeTab === 'completed') {
      result = result.filter(app => app.status === 'completed');
    } else if (activeTab === 'cancelled') {
      result = result.filter(app => app.status === 'cancelled' || app.status === 'no-show');
    }

    // Apply search filter
    if (searchTerm) {
      result = result.filter(app =>
        app.patient_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (app.doctor_name && app.doctor_name.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Apply status filter
    if (statusFilter) {
      result = result.filter(app => app.status === statusFilter);
    }

    // Apply date filter
    if (dateFilter) {
      result = result.filter(app => app.date === dateFilter || app.appointment_date === dateFilter);
    }

    setFilteredAppointments(result);
  }, [appointments, activeTab, searchTerm, statusFilter, dateFilter]);

  const handleStatusChange = async (appointmentId: number, newStatus: string) => {
    try {
      setUpdatingStatus(appointmentId);

      await axios.put(`${API_URL}/api/appointments/${appointmentId}`, {
        status: newStatus
      }, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': localStorage.getItem('token')
        },
      });

      // Update local state
      const updatedAppointments = appointments.map(app =>
        app.appointment_id === appointmentId ? { ...app, status: newStatus } : app
      );

      setAppointments(updatedAppointments);

      // Small delay to show the success animation
      setTimeout(() => {
        setUpdatingStatus(null);
      }, 500);
    } catch (err: any) {
      console.error('Error updating appointment status:', err);
      setError(err.response?.data?.message || 'Failed to update appointment status');
      setUpdatingStatus(null);
    }
  };

  const clearFilters = () => {
    setActiveTab('all');
    setSearchTerm('');
    setStatusFilter('');
    setDateFilter('');
  };

  // Function to open the action dialog for an appointment
  const openActionDialog = (appointment: Appointment) => {
    setSelectedAppointment(appointment);
    setActionDialogOpen(true);
  };

  // Function to close the action dialog
  const closeActionDialog = () => {
    setActionDialogOpen(false);
    setSelectedAppointment(null);
  };

  // Function to convert an appointment to a visit
  const convertToVisit = async () => {
    if (!selectedAppointment) return;

    setProcessingAction(true);

    try {
      // First update the appointment status to "in-progress"
      await handleStatusChange(selectedAppointment.appointment_id, "in-progress");

      // Navigate to the new visit form with the patient ID
      navigate(`/patients/${selectedAppointment.patient_id}/visits/new`, {
        state: {
          fromAppointment: true,
          appointmentId: selectedAppointment.appointment_id,
          appointmentReason: selectedAppointment.reason || ''
        }
      });
    } catch (err) {
      console.error('Error converting appointment to visit:', err);
      setError('Failed to convert appointment to visit');
    } finally {
      setProcessingAction(false);
      closeActionDialog();
    }
  };

  // Function to navigate to patient profile
  const goToPatientProfile = () => {
    if (!selectedAppointment) return;
    navigate(`/patients/${selectedAppointment.patient_id}`);
    closeActionDialog();
  };

  if (loading) {
    return (
      <div className="modern-dashboard">
        <div className="empty-state">
          <div className="empty-state-icon">
            <i className="fas fa-spinner fa-spin"></i>
          </div>
          <h3>Loading appointments...</h3>
          <p>Please wait while we fetch your appointment data.</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="modern-dashboard">
        <div className="empty-state">
          <div className="empty-state-icon">
            <i className="fas fa-exclamation-triangle"></i>
          </div>
          <h3>Error Loading Appointments</h3>
          <p>{error}</p>
          <div className="empty-state-actions">
            <button className="btn-primary" onClick={() => window.location.reload()}>
              <i className="fas fa-sync"></i> Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="modern-dashboard" style={{
      padding: '2rem',
      maxWidth: '1400px',
      margin: '0 auto',
      color: 'var(--text-color)',
      backgroundColor: 'var(--bg-color)',
      minHeight: 'calc(100vh - 64px)'
    }}>
      {/* Dashboard Header */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '2rem'
      }}>
        <div>
          <h1 style={{
            fontSize: '1.75rem',
            fontWeight: 600,
            margin: 0,
            color: 'var(--text-color)',
            letterSpacing: '-0.02em'
          }}>Appointments</h1>
          <p style={{
            color: 'var(--text-secondary-color)',
            margin: '0.5rem 0 0',
            fontSize: '0.95rem',
            fontWeight: 400
          }}>Manage and track patient appointments</p>
        </div>
        <Link
          to="/appointments/new"
          style={{
            backgroundColor: '#2C4B2B',
            color: 'white',
            padding: '0.7rem 1.2rem',
            borderRadius: '8px',
            fontWeight: 500,
            fontSize: '0.9rem',
            textDecoration: 'none',
            display: 'flex',
            alignItems: 'center',
            boxShadow: '0 2px 4px rgba(44, 75, 43, 0.15)',
            transition: 'all 0.2s ease'
          }}
        >
          <i className="fas fa-plus" style={{ marginRight: '0.5rem' }}></i> New Appointment
        </Link>
      </div>

      {/* Stats Cards */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(240px, 1fr))',
        gap: '1.5rem',
        marginBottom: '2rem'
      }}>
        <div style={{
          backgroundColor: 'var(--card-bg-color)',
          borderRadius: '12px',
          padding: '1.5rem',
          display: 'flex',
          alignItems: 'center',
          boxShadow: '0 1px 3px var(--shadow-color)',
          borderTop: '3px solid #2C4B2B'
        }}>
          <div style={{
            width: '48px',
            height: '48px',
            borderRadius: '12px',
            backgroundColor: 'rgba(44, 75, 43, 0.1)',
            color: '#2C4B2B',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: '1rem',
            fontSize: '1.25rem'
          }}>
            <i className="fas fa-calendar-day"></i>
          </div>
          <div>
            <h3 style={{
              fontSize: '1.75rem',
              fontWeight: 700,
              margin: 0,
              color: 'var(--text-color)',
              lineHeight: 1.2
            }}>{todayAppointments}</h3>
            <p style={{
              margin: '0.25rem 0 0',
              color: 'var(--text-secondary-color)',
              fontSize: '0.875rem',
              fontWeight: 500
            }}>Today's Appointments</p>
          </div>
        </div>

        <div style={{
          backgroundColor: 'var(--card-bg-color)',
          borderRadius: '12px',
          padding: '1.5rem',
          display: 'flex',
          alignItems: 'center',
          boxShadow: '0 1px 3px var(--shadow-color)',
          borderTop: '3px solid #A1A43A'
        }}>
          <div style={{
            width: '48px',
            height: '48px',
            borderRadius: '12px',
            backgroundColor: 'rgba(161, 164, 58, 0.1)',
            color: '#A1A43A',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: '1rem',
            fontSize: '1.25rem'
          }}>
            <i className="fas fa-calendar-alt"></i>
          </div>
          <div>
            <h3 style={{
              fontSize: '1.75rem',
              fontWeight: 700,
              margin: 0,
              color: 'var(--text-color)',
              lineHeight: 1.2
            }}>{upcomingAppointments}</h3>
            <p style={{
              margin: '0.25rem 0 0',
              color: 'var(--text-secondary-color)',
              fontSize: '0.875rem',
              fontWeight: 500
            }}>Upcoming</p>
          </div>
        </div>

        <div style={{
          backgroundColor: 'var(--card-bg-color)',
          borderRadius: '12px',
          padding: '1.5rem',
          display: 'flex',
          alignItems: 'center',
          boxShadow: '0 1px 3px var(--shadow-color)',
          borderTop: '3px solid #2C4B2B'
        }}>
          <div style={{
            width: '48px',
            height: '48px',
            borderRadius: '12px',
            backgroundColor: 'rgba(44, 75, 43, 0.1)',
            color: '#2C4B2B',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: '1rem',
            fontSize: '1.25rem'
          }}>
            <i className="fas fa-check-circle"></i>
          </div>
          <div>
            <h3 style={{
              fontSize: '1.75rem',
              fontWeight: 700,
              margin: 0,
              color: 'var(--text-color)',
              lineHeight: 1.2
            }}>{completedAppointments}</h3>
            <p style={{
              margin: '0.25rem 0 0',
              color: 'var(--text-secondary-color)',
              fontSize: '0.875rem',
              fontWeight: 500
            }}>Completed</p>
          </div>
        </div>

        <div style={{
          backgroundColor: 'var(--card-bg-color)',
          borderRadius: '12px',
          padding: '1.5rem',
          display: 'flex',
          alignItems: 'center',
          boxShadow: '0 1px 3px var(--shadow-color)',
          borderTop: '3px solid #D97B3A'
        }}>
          <div style={{
            width: '48px',
            height: '48px',
            borderRadius: '12px',
            backgroundColor: 'rgba(217, 123, 58, 0.1)',
            color: '#D97B3A',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: '1rem',
            fontSize: '1.25rem'
          }}>
            <i className="fas fa-times-circle"></i>
          </div>
          <div>
            <h3 style={{
              fontSize: '1.75rem',
              fontWeight: 700,
              margin: 0,
              color: 'var(--text-color)',
              lineHeight: 1.2
            }}>{cancelledAppointments}</h3>
            <p style={{
              margin: '0.25rem 0 0',
              color: 'var(--text-secondary-color)',
              fontSize: '0.875rem',
              fontWeight: 500
            }}>Cancelled</p>
          </div>
        </div>
      </div>

      {/* Main Content Container */}
      <div style={{
        backgroundColor: 'var(--card-bg-color)',
        borderRadius: '12px',
        boxShadow: '0 1px 3px var(--shadow-color)',
        overflow: 'hidden',
        marginBottom: '2rem'
      }}>
        {/* Tab Controls */}
        <div style={{
          display: 'flex',
          borderBottom: '1px solid var(--border-color)',
          backgroundColor: 'var(--card-bg-color)'
        }}>
          <button
            onClick={() => setActiveTab('all')}
            style={{
              background: 'none',
              border: 'none',
              padding: '1rem 1.5rem',
              fontSize: '0.95rem',
              fontWeight: activeTab === 'all' ? 600 : 500,
              color: activeTab === 'all' ? '#2C4B2B' : '#64748b',
              cursor: 'pointer',
              position: 'relative',
              borderBottom: activeTab === 'all' ? '2px solid #2C4B2B' : 'none',
              transition: 'all 0.2s ease'
            }}
          >
            <i className="fas fa-th-list" style={{ marginRight: '0.5rem' }}></i> All
          </button>
          <button
            onClick={() => setActiveTab('today')}
            style={{
              background: 'none',
              border: 'none',
              padding: '1rem 1.5rem',
              fontSize: '0.95rem',
              fontWeight: activeTab === 'today' ? 600 : 500,
              color: activeTab === 'today' ? '#2C4B2B' : '#64748b',
              cursor: 'pointer',
              position: 'relative',
              borderBottom: activeTab === 'today' ? '2px solid #2C4B2B' : 'none',
              transition: 'all 0.2s ease'
            }}
          >
            <i className="fas fa-calendar-day" style={{ marginRight: '0.5rem' }}></i> Today
          </button>
          <button
            onClick={() => setActiveTab('upcoming')}
            style={{
              background: 'none',
              border: 'none',
              padding: '1rem 1.5rem',
              fontSize: '0.95rem',
              fontWeight: activeTab === 'upcoming' ? 600 : 500,
              color: activeTab === 'upcoming' ? '#2C4B2B' : '#64748b',
              cursor: 'pointer',
              position: 'relative',
              borderBottom: activeTab === 'upcoming' ? '2px solid #2C4B2B' : 'none',
              transition: 'all 0.2s ease'
            }}
          >
            <i className="fas fa-calendar-alt" style={{ marginRight: '0.5rem' }}></i> Upcoming
          </button>
          <button
            onClick={() => setActiveTab('completed')}
            style={{
              background: 'none',
              border: 'none',
              padding: '1rem 1.5rem',
              fontSize: '0.95rem',
              fontWeight: activeTab === 'completed' ? 600 : 500,
              color: activeTab === 'completed' ? '#2C4B2B' : '#64748b',
              cursor: 'pointer',
              position: 'relative',
              borderBottom: activeTab === 'completed' ? '2px solid #2C4B2B' : 'none',
              transition: 'all 0.2s ease'
            }}
          >
            <i className="fas fa-check-circle" style={{ marginRight: '0.5rem' }}></i> Completed
          </button>
          <button
            onClick={() => setActiveTab('cancelled')}
            style={{
              background: 'none',
              border: 'none',
              padding: '1rem 1.5rem',
              fontSize: '0.95rem',
              fontWeight: activeTab === 'cancelled' ? 600 : 500,
              color: activeTab === 'cancelled' ? '#2C4B2B' : '#64748b',
              cursor: 'pointer',
              position: 'relative',
              borderBottom: activeTab === 'cancelled' ? '2px solid #2C4B2B' : 'none',
              transition: 'all 0.2s ease'
            }}
          >
            <i className="fas fa-times-circle" style={{ marginRight: '0.5rem' }}></i> Cancelled
          </button>

          <div style={{ flexGrow: 1 }}></div>

          <div style={{
            display: 'flex',
            padding: '0.5rem',
            alignItems: 'center'
          }}>
            <button
              onClick={() => setViewMode('card')}
              style={{
                width: '36px',
                height: '36px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '6px',
                border: 'none',
                background: viewMode === 'card' ? '#2C4B2B' : 'transparent',
                color: viewMode === 'card' ? 'white' : '#64748b',
                cursor: 'pointer',
                marginRight: '0.5rem'
              }}
              title="Card View"
            >
              <i className="fas fa-th-large"></i>
            </button>
            <button
              onClick={() => setViewMode('calendar')}
              style={{
                width: '36px',
                height: '36px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: '6px',
                border: 'none',
                background: viewMode === 'calendar' ? '#2C4B2B' : 'transparent',
                color: viewMode === 'calendar' ? 'white' : '#64748b',
                cursor: 'pointer',
                marginRight: '0.5rem'
              }}
              title="Calendar View"
            >
              <i className="fas fa-calendar-week"></i>
            </button>
          </div>
        </div>

        {/* Filter Controls */}
        <div style={{
          padding: '1rem 1.5rem',
          display: 'flex',
          alignItems: 'center',
          gap: '1rem',
          borderBottom: '1px solid var(--border-color)'
        }}>
          <div style={{
            position: 'relative',
            flexGrow: 1
          }}>
            <i className="fas fa-search" style={{
              position: 'absolute',
              left: '1rem',
              top: '50%',
              transform: 'translateY(-50%)',
              color: '#94a3b8',
              fontSize: '0.9rem'
            }}></i>
            <input
              type="text"
              placeholder="Search by patient or doctor name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              style={{
                width: '100%',
                padding: '0.7rem 1rem 0.7rem 2.5rem',
                borderRadius: '8px',
                border: '1px solid var(--border-color)',
                fontSize: '0.9rem',
                backgroundColor: 'var(--bg-color)',
                color: 'var(--text-color)',
                outline: 'none',
                transition: 'all 0.2s ease'
              }}
            />
            {searchTerm && (
              <button
                onClick={() => setSearchTerm('')}
                title="Clear search"
                style={{
                  position: 'absolute',
                  right: '0.75rem',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  background: 'none',
                  border: 'none',
                  color: '#94a3b8',
                  cursor: 'pointer',
                  fontSize: '0.9rem',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '24px',
                  height: '24px',
                  borderRadius: '50%'
                }}
              >
                <i className="fas fa-times"></i>
              </button>
            )}
          </div>

          <div style={{
            position: 'relative'
          }}>
            <i className="fas fa-filter" style={{
              position: 'absolute',
              left: '0.75rem',
              top: '50%',
              transform: 'translateY(-50%)',
              color: '#94a3b8',
              pointerEvents: 'none',
              fontSize: '0.9rem'
            }}></i>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              style={{
                padding: '0.7rem 1rem 0.7rem 2.25rem',
                borderRadius: '8px',
                border: '1px solid var(--border-color)',
                fontSize: '0.9rem',
                backgroundColor: 'var(--bg-color)',
                color: 'var(--text-color)',
                minWidth: '160px',
                appearance: 'none',
                outline: 'none',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
            >
              <option value="">Status: All</option>
              <option value="scheduled">Scheduled</option>
              <option value="confirmed">Confirmed</option>
              <option value="in-progress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
              <option value="no-show">No Show</option>
              <option value="rescheduled">Rescheduled</option>
            </select>
          </div>

          <div style={{
            position: 'relative'
          }}>
            <i className="fas fa-calendar" style={{
              position: 'absolute',
              left: '0.75rem',
              top: '50%',
              transform: 'translateY(-50%)',
              color: '#94a3b8',
              pointerEvents: 'none',
              fontSize: '0.9rem'
            }}></i>
            <input
              type="date"
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              placeholder="Select date"
              style={{
                padding: '0.7rem 1rem 0.7rem 2.25rem',
                borderRadius: '8px',
                border: '1px solid var(--border-color)',
                fontSize: '0.9rem',
                backgroundColor: 'var(--bg-color)',
                color: 'var(--text-color)',
                minWidth: '160px',
                outline: 'none',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
            />
          </div>

          {(searchTerm || statusFilter || dateFilter || activeTab !== 'all') && (
            <button
              onClick={clearFilters}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                background: 'none',
                border: '1px solid #e2e8f0',
                padding: '0.7rem 1rem',
                borderRadius: '8px',
                fontSize: '0.9rem',
                fontWeight: 500,
                color: '#64748b',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
            >
              <i className="fas fa-times-circle" style={{ marginRight: '0.5rem' }}></i> Clear Filters
            </button>
          )}
        </div>



        {/* Active Filters Display */}
        {(searchTerm || statusFilter || dateFilter || activeTab !== 'all') && (
          <div style={{
            padding: '0.75rem 1.5rem',
            display: 'flex',
            flexWrap: 'wrap',
            gap: '0.5rem',
            alignItems: 'center',
            borderBottom: '1px solid var(--border-color)'
          }}>
            <span style={{
              fontSize: '0.85rem',
              color: '#64748b',
              fontWeight: 500
            }}>Active filters:</span>

            {activeTab !== 'all' && (
              <div style={{
                display: 'flex',
                alignItems: 'center',
                backgroundColor: 'rgba(44, 75, 43, 0.08)',
                color: '#2C4B2B',
                padding: '0.4rem 0.75rem',
                borderRadius: '6px',
                fontSize: '0.85rem',
                fontWeight: 500
              }}>
                <i className={`fas ${
                  activeTab === 'today' ? 'fa-calendar-day' :
                  activeTab === 'upcoming' ? 'fa-calendar-alt' :
                  activeTab === 'completed' ? 'fa-check-circle' :
                  'fa-times-circle'
                }`} style={{ marginRight: '0.5rem' }}></i>
                {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}
                <button
                  onClick={() => setActiveTab('all')}
                  style={{
                    background: 'none',
                    border: 'none',
                    color: '#2C4B2B',
                    marginLeft: '0.5rem',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '16px',
                    height: '16px',
                    borderRadius: '50%',
                    padding: 0
                  }}
                >
                  <i className="fas fa-times" style={{ fontSize: '0.75rem' }}></i>
                </button>
              </div>
            )}

            {searchTerm && (
              <div style={{
                display: 'flex',
                alignItems: 'center',
                backgroundColor: 'rgba(44, 75, 43, 0.08)',
                color: '#2C4B2B',
                padding: '0.4rem 0.75rem',
                borderRadius: '6px',
                fontSize: '0.85rem',
                fontWeight: 500
              }}>
                <i className="fas fa-search" style={{ marginRight: '0.5rem' }}></i>
                {searchTerm.length > 20 ? searchTerm.substring(0, 20) + '...' : searchTerm}
                <button
                  onClick={() => setSearchTerm('')}
                  style={{
                    background: 'none',
                    border: 'none',
                    color: '#2C4B2B',
                    marginLeft: '0.5rem',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '16px',
                    height: '16px',
                    borderRadius: '50%',
                    padding: 0
                  }}
                >
                  <i className="fas fa-times" style={{ fontSize: '0.75rem' }}></i>
                </button>
              </div>
            )}

            {statusFilter && (
              <div style={{
                display: 'flex',
                alignItems: 'center',
                backgroundColor: 'rgba(44, 75, 43, 0.08)',
                color: '#2C4B2B',
                padding: '0.4rem 0.75rem',
                borderRadius: '6px',
                fontSize: '0.85rem',
                fontWeight: 500
              }}>
                <i className="fas fa-filter" style={{ marginRight: '0.5rem' }}></i>
                {statusFilter.charAt(0).toUpperCase() + statusFilter.slice(1)}
                <button
                  onClick={() => setStatusFilter('')}
                  style={{
                    background: 'none',
                    border: 'none',
                    color: '#2C4B2B',
                    marginLeft: '0.5rem',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '16px',
                    height: '16px',
                    borderRadius: '50%',
                    padding: 0
                  }}
                >
                  <i className="fas fa-times" style={{ fontSize: '0.75rem' }}></i>
                </button>
              </div>
            )}

            {dateFilter && (
              <div style={{
                display: 'flex',
                alignItems: 'center',
                backgroundColor: 'rgba(44, 75, 43, 0.08)',
                color: '#2C4B2B',
                padding: '0.4rem 0.75rem',
                borderRadius: '6px',
                fontSize: '0.85rem',
                fontWeight: 500
              }}>
                <i className="fas fa-calendar" style={{ marginRight: '0.5rem' }}></i>
                {formatDate(dateFilter)}
                <button
                  onClick={() => setDateFilter('')}
                  style={{
                    background: 'none',
                    border: 'none',
                    color: '#2C4B2B',
                    marginLeft: '0.5rem',
                    cursor: 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '16px',
                    height: '16px',
                    borderRadius: '50%',
                    padding: 0
                  }}
                >
                  <i className="fas fa-times" style={{ fontSize: '0.75rem' }}></i>
                </button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Appointments Container */}
      <div style={{
        marginTop: '1.5rem'
      }}>
        {viewMode === 'card' ? (
          filteredAppointments.length > 0 ? (
            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fill, minmax(350px, 1fr))',
              gap: '1.25rem'
            }}>
              {filteredAppointments.map((appointment) => (
                <div
                  key={appointment.appointment_id}
                  style={{
                    backgroundColor: 'var(--card-bg-color)',
                    borderRadius: '12px',
                    overflow: 'hidden',
                    boxShadow: '0 1px 3px var(--shadow-color)',
                    display: 'flex',
                    flexDirection: 'column',
                    borderLeft: `3px solid ${
                      appointment.status === 'scheduled' ? '#2C4B2B' :
                      appointment.status === 'confirmed' ? '#A1A43A' :
                      appointment.status === 'in-progress' ? '#F6B21A' :
                      appointment.status === 'completed' ? '#2C4B2B' :
                      appointment.status === 'cancelled' || appointment.status === 'no-show' ? '#D97B3A' :
                      appointment.status === 'rescheduled' ? '#F2A65A' :
                      '#2C4B2B'
                    }`,
                    transition: 'transform 0.2s ease, box-shadow 0.2s ease'
                  }}
                >
                  <div className="appointment-header" style={{
                    padding: '1rem 1.25rem',
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    borderBottom: '1px solid var(--border-color)'
                  }}>
                    <div className="appointment-date" style={{
                      display: 'flex',
                      alignItems: 'center',
                      fontWeight: 600,
                      color: 'var(--text-color)',
                      fontSize: '1rem'
                    }}>
                      <i className="fas fa-calendar-day" style={{
                        marginRight: '0.75rem',
                        color: '#2C4B2B',
                        fontSize: '1rem'
                      }}></i>
                      {appointment.appointment_date
                        ? formatDate(appointment.appointment_date)
                        : appointment.date
                          ? formatDate(appointment.date)
                          : 'No date'}
                    </div>
                    <div className="appointment-status-badge" style={{
                      padding: '0.35rem 1rem',
                      borderRadius: '20px',
                      fontSize: '0.8rem',
                      fontWeight: 500,
                      textTransform: 'capitalize',
                      backgroundColor: appointment.status === 'scheduled' ? 'rgba(44, 75, 43, 0.1)' :
                        appointment.status === 'confirmed' ? 'rgba(161, 164, 58, 0.1)' :
                        appointment.status === 'in-progress' ? 'rgba(246, 178, 26, 0.1)' :
                        appointment.status === 'completed' ? 'rgba(44, 75, 43, 0.1)' :
                        appointment.status === 'cancelled' || appointment.status === 'no-show' ? 'rgba(217, 123, 58, 0.1)' :
                        appointment.status === 'rescheduled' ? 'rgba(242, 166, 90, 0.1)' :
                        'rgba(44, 75, 43, 0.1)',
                      color: appointment.status === 'scheduled' ? '#2C4B2B' :
                        appointment.status === 'confirmed' ? '#A1A43A' :
                        appointment.status === 'in-progress' ? '#F6B21A' :
                        appointment.status === 'completed' ? '#2C4B2B' :
                        appointment.status === 'cancelled' || appointment.status === 'no-show' ? '#D97B3A' :
                        appointment.status === 'rescheduled' ? '#F2A65A' :
                        '#2C4B2B'
                    }}>
                      {appointment.status || 'Scheduled'}
                    </div>
                  </div>

                  <div className="appointment-body">
                    {/* Patient Name */}
                    <Link to={`/patients/${appointment.patient_id}`} className="patient-name">
                      <i className="fas fa-user-circle"></i>
                      {appointment.patient_name}
                    </Link>

                    {/* Patient Meta Info */}
                    <div className="patient-meta">
                      <div className="meta-item">
                        <i className="fas fa-id-card"></i>
                        ID: {appointment.patient_id}
                      </div>
                      {appointment.start_time && (
                        <div className="meta-item">
                          <i className="fas fa-clock"></i>
                          {formatTime(appointment.start_time)}
                          {appointment.end_time ? ` - ${formatTime(appointment.end_time)}` : ''}
                        </div>
                      )}
                    </div>

                    {/* Reason Section */}
                    {appointment.reason && (
                      <div className="reason-section">
                        <div className="reason-label">REASON</div>
                        <div className="reason-content">{appointment.reason}</div>
                      </div>
                    )}
                  </div>

                  <div className="appointment-footer">
                    <div className="status-control">
                      <label className="status-label">STATUS</label>
                      <div className="select-wrapper">
                        {updatingStatus === appointment.appointment_id && (
                          <div className="status-loading">
                            <i className="fas fa-spinner fa-spin"></i>
                          </div>
                        )}
                        <select
                          className="status-select"
                          value={appointment.status || 'scheduled'}
                          onChange={(e) => handleStatusChange(appointment.appointment_id, e.target.value)}
                          disabled={updatingStatus === appointment.appointment_id}
                        >
                          <option value="scheduled">Scheduled</option>
                          <option value="confirmed">Confirmed</option>
                          <option value="in-progress">In Progress</option>
                          <option value="completed">Completed</option>
                          <option value="cancelled">Cancelled</option>
                          <option value="no-show">No Show</option>
                          <option value="rescheduled">Rescheduled</option>
                        </select>
                      </div>
                    </div>

                    <div className="action-buttons">
                      <Link
                        to={`/appointments/${appointment.appointment_id}`}
                        className="action-btn view"
                        title="View Details"
                      >
                        <i className="fas fa-eye"></i>
                      </Link>
                      <Link
                        to={`/appointments/${appointment.appointment_id}/edit`}
                        className="action-btn edit"
                        title="Edit Appointment"
                      >
                        <i className="fas fa-edit"></i>
                      </Link>
                      <button
                        className="action-btn convert"
                        onClick={() => openActionDialog(appointment)}
                        title="Patient Actions"
                      >
                        <i className="fas fa-user-md"></i>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              padding: '4rem 2rem',
              textAlign: 'center',
              backgroundColor: 'var(--card-bg-color)',
              borderRadius: '12px',
              boxShadow: '0 1px 3px var(--shadow-color)'
            }}>
              <div style={{
                width: '80px',
                height: '80px',
                borderRadius: '50%',
                backgroundColor: 'rgba(44, 75, 43, 0.08)',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginBottom: '1.5rem',
                color: '#2C4B2B',
                fontSize: '2rem'
              }}>
                <i className="fas fa-calendar-xmark"></i>
              </div>
              <h3 style={{
                fontSize: '1.25rem',
                fontWeight: 600,
                color: 'var(--text-color)',
                margin: '0 0 0.75rem'
              }}>No appointments found</h3>
              <p style={{
                fontSize: '0.95rem',
                color: 'var(--text-secondary-color)',
                marginBottom: '1.5rem',
                maxWidth: '400px'
              }}>Try adjusting your filters or use the "New Appointment" button above.</p>
              <button
                onClick={clearFilters}
                style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  padding: '0.75rem 1.5rem',
                  backgroundColor: 'var(--card-bg-color)',
                  color: 'var(--primary-color, #2C4B2B)',
                  border: '1px solid var(--primary-color, #2C4B2B)',
                  borderRadius: '8px',
                  fontWeight: 500,
                  fontSize: '0.95rem',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease'
                }}
              >
                <i className="fas fa-filter" style={{ marginRight: '0.5rem' }}></i> Clear Filters
              </button>
            </div>
          )
        ) : (
          <div style={{
            backgroundColor: 'var(--card-bg-color)',
            borderRadius: '12px',
            boxShadow: '0 1px 3px var(--shadow-color)',
            padding: '4rem 2rem',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            textAlign: 'center'
          }}>
            <div style={{
              width: '80px',
              height: '80px',
              borderRadius: '50%',
              backgroundColor: 'rgba(44, 75, 43, 0.08)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginBottom: '1.5rem',
              color: '#2C4B2B',
              fontSize: '2rem'
            }}>
              <i className="fas fa-calendar-week"></i>
            </div>
            <h3 style={{
              fontSize: '1.25rem',
              fontWeight: 600,
              color: 'var(--text-color)',
              margin: '0 0 0.75rem'
            }}>Calendar View Coming Soon</h3>
            <p style={{
              fontSize: '0.95rem',
              color: 'var(--text-secondary-color)',
              marginBottom: '1.5rem',
              maxWidth: '400px'
            }}>We're working on a calendar view for appointments.</p>
            <button
              onClick={() => setViewMode('card')}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                padding: '0.75rem 1.5rem',
                backgroundColor: 'var(--primary-color, #2C4B2B)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontWeight: 500,
                fontSize: '0.95rem',
                cursor: 'pointer',
                transition: 'all 0.2s ease',
                boxShadow: '0 2px 4px var(--shadow-color)'
              }}
            >
              <i className="fas fa-th-large" style={{ marginRight: '0.5rem' }}></i> Switch to Card View
            </button>
          </div>
        )}
      </div>

      {/* Action Dialog */}
      <Dialog
        open={actionDialogOpen}
        onClose={closeActionDialog}
        aria-labelledby="appointment-action-dialog-title"
        PaperProps={{
          style: { borderRadius: '8px', overflow: 'hidden' }
        }}
      >
        <div className="dialog-title">
          <i className="fas fa-user-md dialog-title-icon" style={{ color: 'var(--primary-color, #2C4B2B)' }}></i>
          <span className="dialog-title-text">Patient Actions</span>
        </div>

        <div className="dialog-content">
          {selectedAppointment && (
            <>
              <div style={{ marginBottom: '1.5rem' }}>
                <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0.75rem' }}>
                  <i className="fas fa-user-circle" style={{ color: 'var(--primary-color, #2C4B2B)', marginRight: '0.75rem', fontSize: '1.5rem' }}></i>
                  <span style={{ fontSize: '1.1rem', fontWeight: '600', color: 'var(--text-color)' }}>{selectedAppointment.patient_name}</span>
                </div>

                <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '0.75rem', fontSize: '0.9rem' }}>
                  <div style={{ padding: '0.75rem', backgroundColor: 'var(--bg-color)', borderRadius: '6px', borderLeft: '3px solid var(--primary-color, #2C4B2B)' }}>
                    <div style={{ color: 'var(--text-secondary-color)', marginBottom: '0.25rem', fontSize: '0.75rem', fontWeight: '500', textTransform: 'uppercase', letterSpacing: '0.5px' }}>Appointment</div>
                    <div style={{ color: 'var(--text-color)' }}>{selectedAppointment.reason || 'No reason provided'}</div>
                  </div>

                  <div style={{ padding: '0.75rem', backgroundColor: 'var(--bg-color)', borderRadius: '6px', borderLeft: '3px solid var(--primary-color, #2C4B2B)' }}>
                    <div style={{ color: 'var(--text-secondary-color)', marginBottom: '0.25rem', fontSize: '0.75rem', fontWeight: '500', textTransform: 'uppercase', letterSpacing: '0.5px' }}>Date & Time</div>
                    <div style={{ color: 'var(--text-color)' }}>
                      {selectedAppointment.date ? formatDate(selectedAppointment.date) : 'No date'}{' '}
                      {selectedAppointment.start_time ? formatTime(selectedAppointment.start_time) : ''}
                      {selectedAppointment.end_time ? ` - ${formatTime(selectedAppointment.end_time)}` : ''}
                    </div>
                  </div>
                </div>
              </div>

              <p style={{ color: 'var(--text-secondary-color)', fontSize: '0.9rem', marginBottom: '0.5rem' }}>What would you like to do with this patient?</p>
            </>
          )}
        </div>

        <div className="dialog-actions">
          <button
            className="dialog-btn dialog-btn-cancel"
            onClick={closeActionDialog}
            style={{
              background: 'none',
              border: '1px solid var(--border-color)',
              color: 'var(--text-secondary-color)',
              padding: '0.6rem 1rem',
              borderRadius: '6px',
              fontSize: '0.9rem',
              fontWeight: 500,
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center'
            }}
          >
            Cancel
          </button>

          <button
            className="dialog-btn dialog-btn-primary"
            onClick={goToPatientProfile}
            style={{
              backgroundColor: 'var(--card-bg-color)',
              color: 'var(--primary-color, #2C4B2B)',
              border: '1px solid var(--border-color)',
              padding: '0.6rem 1rem',
              borderRadius: '6px',
              fontSize: '0.9rem',
              fontWeight: 500,
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center'
            }}
          >
            <i className="fas fa-user" style={{ marginRight: '0.5rem' }}></i> View Patient Profile
          </button>

          <button
            className="dialog-btn dialog-btn-action"
            onClick={convertToVisit}
            disabled={processingAction}
            style={{
              backgroundColor: 'var(--primary-color, #2C4B2B)',
              color: 'white',
              border: 'none',
              padding: '0.6rem 1rem',
              borderRadius: '6px',
              fontSize: '0.9rem',
              fontWeight: 500,
              cursor: processingAction ? 'not-allowed' : 'pointer',
              display: 'flex',
              alignItems: 'center',
              opacity: processingAction ? 0.7 : 1
            }}
          >
            {processingAction ? (
              <>
                <i className="fas fa-spinner fa-spin"></i> Processing...
              </>
            ) : (
              <>
                <i className="fas fa-notes-medical"></i> Start Visit
              </>
            )}
          </button>
        </div>
      </Dialog>
    </div>
  );
};

export default AppointmentDashboard;