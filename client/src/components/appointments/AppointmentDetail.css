/* Modern, Clean, Minimalist Appointment Detail Styles */

.appointment-detail-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 1.5rem;
  color: #374151;
  background-color: #f9fafb;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.5;
}

/* Loading State */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  font-size: 1.1rem;
  color: #64748b;
}

.loading-container i {
  margin-right: 0.75rem;
  color: #2C4B2B; /* Primary color from theme */
}

/* Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  flex-direction: column;
}

.back-button {
  display: inline-flex;
  align-items: center;
  color: #2C4B2B; /* Primary color from theme */
  font-size: 0.9rem;
  font-weight: 500;
  margin-bottom: 0.75rem;
  text-decoration: none;
  transition: color 0.2s ease;
}

.back-button:hover {
  color: #1E3A1D; /* Dark primary color from theme */
}

.back-button i {
  margin-right: 0.5rem;
}

.form-title {
  font-size: 1.75rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.btn-primary {
  display: inline-flex;
  align-items: center;
  padding: 0.6rem 1.25rem;
  background-color: #2C4B2B; /* Primary color from theme */
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background-color: #3E6A3D; /* Light primary color from theme */
  transform: translateY(-2px);
}

.btn-danger {
  display: inline-flex;
  align-items: center;
  padding: 0.6rem 1.25rem;
  background-color: #D97B3A; /* Error color from theme */
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-danger:hover {
  background-color: #B5652F; /* Dark error color from theme */
  transform: translateY(-2px);
}

.btn-success {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: #2C4B2B; /* Success color from theme */
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 0.75rem;
}

.btn-success:hover {
  background-color: #3E6A3D; /* Light success color from theme */
  transform: translateY(-2px);
}

.btn-success i, .btn-primary i, .btn-danger i {
  margin-right: 0.5rem;
}

.btn-sm {
  padding: 0.4rem 0.75rem;
  font-size: 0.8rem;
}

/* Appointment Summary Card */
.appointment-summary {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 2rem;
}

.appointment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #f1f5f9;
}

.appointment-title h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 0.5rem 0;
}

.appointment-type {
  display: inline-block;
  padding: 0.35rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  background-color: #EFF5EF; /* Lightest primary color from theme */
  color: #2C4B2B; /* Primary color from theme */
}

.appointment-status {
  display: flex;
  align-items: center;
}

.appointment-status label {
  margin-right: 0.75rem;
  font-weight: 500;
  color: #64748b;
}

.status-select {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%2364748b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
  padding-right: 2.5rem;
}

.status-select:focus {
  outline: none;
  border-color: #2C4B2B; /* Primary color from theme */
  box-shadow: 0 0 0 3px rgba(44, 75, 43, 0.1);
}

/* Status Badge Colors */
.badge-primary {
  background-color: #EFF5EF; /* Lightest primary color from theme */
  color: #2C4B2B; /* Primary color from theme */
}

.badge-success {
  background-color: #EFF5EF; /* Lightest success color from theme */
  color: #2C4B2B; /* Success color from theme */
}

.badge-danger {
  background-color: #FCF1E9; /* Lightest error color from theme */
  color: #D97B3A; /* Error color from theme */
}

.badge-warning {
  background-color: #FEF7E8; /* Lightest warning color from theme */
  color: #F6B21A; /* Warning color from theme */
}

.badge-secondary {
  background-color: #F7F7E8; /* Lightest secondary color from theme */
  color: #A1A43A; /* Secondary color from theme */
}

/* Appointment Info Grid */
.appointment-info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  padding: 1.5rem;
}

.info-column {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.info-group {
  background-color: #f8fafc;
  border-radius: 8px;
  overflow: hidden;
}

.info-group h3 {
  margin: 0;
  padding: 1rem;
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  background-color: #f1f5f9;
  display: flex;
  align-items: center;
}

.info-group h3 i {
  margin-right: 0.75rem;
  color: #2C4B2B; /* Primary color from theme */
}

.info-content {
  padding: 1rem;
}

.info-row {
  display: flex;
  margin-bottom: 0.75rem;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 100px;
  font-weight: 500;
  color: #64748b;
}

.info-value {
  flex: 1;
  color: #1e293b;
}

.info-value a {
  color: #2C4B2B; /* Primary color from theme */
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.info-value a:hover {
  color: #3E6A3D; /* Light primary color from theme */
  text-decoration: underline;
}

/* Appointment Notes */
.appointment-notes {
  padding: 0 1.5rem 1.5rem;
}

.appointment-notes h3 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
}

.appointment-notes h3 i {
  margin-right: 0.75rem;
  color: #2C4B2B; /* Primary color from theme */
}

.notes-content {
  background-color: #f8fafc;
  border-radius: 8px;
  padding: 1rem;
  color: #334155;
  white-space: pre-wrap;
  line-height: 1.6;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 12px;
  padding: 1.5rem;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.modal-content h3 {
  margin: 0 0 1rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
}

.modal-content h3 i {
  margin-right: 0.75rem;
  color: #D97B3A; /* Error color from theme */
}

.modal-content p {
  margin-bottom: 1.5rem;
  color: #334155;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.btn-cancel {
  padding: 0.6rem 1.25rem;
  background-color: #f1f5f9;
  color: #64748b;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-cancel:hover {
  background-color: #e2e8f0;
}

/* Alert Styles */
.alert {
  padding: 1rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  font-weight: 500;
}

.alert-danger {
  background-color: #FCF1E9; /* Lightest error color from theme */
  color: #D97B3A; /* Error color from theme */
  border-left: 4px solid #D97B3A; /* Error color from theme */
}

.alert-warning {
  background-color: #FEF7E8; /* Lightest warning color from theme */
  color: #F6B21A; /* Warning color from theme */
  border-left: 4px solid #F6B21A; /* Warning color from theme */
}

/* Responsive Styles */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .header-actions {
    margin-top: 1rem;
  }
  
  .appointment-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .appointment-status {
    margin-top: 1rem;
  }
  
  .appointment-info-grid {
    grid-template-columns: 1fr;
  }
  
  .info-label {
    width: 80px;
  }
}
