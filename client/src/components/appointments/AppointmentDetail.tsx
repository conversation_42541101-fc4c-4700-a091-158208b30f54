import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { API_URL } from '../../config';
import { formatDate, formatTime } from '../../utils/dateUtils';
import './AppointmentDetail.css';

interface AppointmentData {
  appointment_id: number;
  patient_id: number;
  doctor_id: number;
  title?: string;
  reason?: string;
  date: string;
  time?: string;
  start_time?: string;
  end_time?: string;
  status: string;
  type?: string;
  notes?: string;
  patient_first_name?: string;
  patient_last_name?: string;
  patient_name?: string;
  patient_unique_id?: string;
  doctor_first_name?: string;
  doctor_last_name?: string;
  doctor_name?: string;
  doctor_specialty?: string;
  created_at: string;
  updated_at?: string;
}

const AppointmentDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [appointment, setAppointment] = useState<AppointmentData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showConfirmDelete, setShowConfirmDelete] = useState(false);

  useEffect(() => {
    const fetchAppointment = async () => {
      try {
        console.log('Fetching appointment details for ID:', id);
        const res = await axios.get(`${API_URL}/api/appointments/${id}`, {
          headers: {
            'Content-Type': 'application/json',
            'x-auth-token': localStorage.getItem('token')
          }
        });
        console.log('Appointment data received:', res.data);
        setAppointment(res.data);
        setLoading(false);
      } catch (err: any) {
        console.error('Error fetching appointment:', err);
        if (err.response) {
          console.error('Error response data:', err.response.data);
          console.error('Error response status:', err.response.status);
          setError(`Failed to load appointment details: ${err.response.data.msg || 'Unknown error'}`);
        } else {
          setError('Failed to load appointment details. Please try again later.');
        }
        setLoading(false);
      }
    };

    fetchAppointment();
  }, [id]);

  const handleStatusChange = async (newStatus: string) => {
    try {
      console.log('Updating appointment status to:', newStatus);
      await axios.put(`${API_URL}/api/appointments/${id}`, {
        status: newStatus
      }, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': localStorage.getItem('token')
        }
      });

      // Update the appointment in the local state
      if (appointment) {
        setAppointment({
          ...appointment,
          status: newStatus
        });
      }
    } catch (err: any) {
      console.error('Error updating appointment status:', err);
      setError('Failed to update appointment status. Please try again.');
    }
  };

  const deleteAppointment = async () => {
    try {
      await axios.delete(`${API_URL}/api/appointments/${id}`, {
        headers: {
          'Content-Type': 'application/json',
          'x-auth-token': localStorage.getItem('token')
        }
      });
      navigate('/appointments');
    } catch (err) {
      console.error('Error deleting appointment:', err);
      setError('Failed to delete appointment. Please try again.');
      setShowConfirmDelete(false);
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'badge-primary';
      case 'completed':
        return 'badge-success';
      case 'cancelled':
        return 'badge-danger';
      case 'no-show':
        return 'badge-warning';
      default:
        return 'badge-secondary';
    }
  };

  if (loading) {
    return <div className="loading-container"><i className="fas fa-spinner fa-spin"></i> Loading appointment details...</div>;
  }

  if (error) {
    return <div className="alert alert-danger">{error}</div>;
  }

  if (!appointment) {
    return <div className="alert alert-warning">Appointment not found</div>;
  }

  // Extract patient name from either separate fields or combined field
  const patientFirstName = appointment.patient_first_name || (appointment.patient_name ? appointment.patient_name.split(' ')[0] : '');
  const patientLastName = appointment.patient_last_name || (appointment.patient_name ? appointment.patient_name.split(' ')[1] || '' : '');
  const patientFullName = appointment.patient_name || `${patientFirstName} ${patientLastName}`.trim();

  // Extract doctor name from either separate fields or combined field
  const doctorFirstName = appointment.doctor_first_name || (appointment.doctor_name ? appointment.doctor_name.split(' ')[0] : '');
  const doctorLastName = appointment.doctor_last_name || (appointment.doctor_name ? appointment.doctor_name.split(' ')[1] || '' : '');
  const doctorFullName = appointment.doctor_name || `${doctorFirstName} ${doctorLastName}`.trim();

  return (
    <div className="appointment-detail-container">
      <div className="page-header">
        <div className="header-content">
          <Link to="/appointments" className="back-button">
            <i className="fas fa-arrow-left"></i>
            Back to Appointments
          </Link>
          <h1 className="form-title">Appointment Details</h1>
        </div>
        <div className="header-actions">
          <Link to={`/appointments/${id}/edit`} className="btn-primary">
            <i className="fas fa-edit"></i> Edit
          </Link>
          <button
            className="btn-danger"
            onClick={() => setShowConfirmDelete(true)}
          >
            <i className="fas fa-trash-alt"></i> Delete
          </button>
        </div>
      </div>

      {/* Appointment Summary Card */}
      <div className="appointment-summary">
        <div className="appointment-header">
          <div className="appointment-title">
            <h2>{appointment.title || appointment.reason || 'Appointment'}</h2>
            {appointment.type && (
              <span className={`appointment-type ${appointment.type}`}>
                {appointment.type.charAt(0).toUpperCase() + appointment.type.slice(1)}
              </span>
            )}
          </div>
          <div className="appointment-status">
            <label>Status:</label>
            <select
              className={`status-select ${getStatusBadgeClass(appointment.status)}`}
              value={appointment.status}
              onChange={(e) => handleStatusChange(e.target.value)}
            >
              <option value="scheduled">Scheduled</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
              <option value="no-show">No-Show</option>
            </select>
          </div>
        </div>

        <div className="appointment-info-grid">
          <div className="info-column">
            <div className="info-group">
              <h3><i className="fas fa-user-injured"></i> Patient Information</h3>
              <div className="info-content">
                <div className="info-row">
                  <span className="info-label">Name:</span>
                  <span className="info-value">
                    <Link to={`/patients/${appointment.patient_id}`}>
                      {patientFullName || 'Unknown'}
                    </Link>
                  </span>
                </div>
                {appointment.patient_unique_id && (
                  <div className="info-row">
                    <span className="info-label">ID:</span>
                    <span className="info-value">{appointment.patient_unique_id}</span>
                  </div>
                )}
                <Link
                  to={`/visits/new?patient_id=${appointment.patient_id}&appointment_id=${appointment.appointment_id}`}
                  className="btn-success btn-sm"
                >
                  <i className="fas fa-notes-medical"></i> Create Visit Record
                </Link>
              </div>
            </div>

            <div className="info-group">
              <h3><i className="fas fa-user-md"></i> Doctor Information</h3>
              <div className="info-content">
                <div className="info-row">
                  <span className="info-label">Name:</span>
                  <span className="info-value">
                    Dr. {doctorFullName || 'Unknown'}
                  </span>
                </div>
                {appointment.doctor_specialty && (
                  <div className="info-row">
                    <span className="info-label">Specialty:</span>
                    <span className="info-value">{appointment.doctor_specialty}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="info-column">
            <div className="info-group">
              <h3><i className="fas fa-calendar-alt"></i> Date & Time</h3>
              <div className="info-content">
                <div className="info-row">
                  <span className="info-label">Date:</span>
                  <span className="info-value">{formatDate(appointment.date)}</span>
                </div>
                <div className="info-row">
                  <span className="info-label">Time:</span>
                  <span className="info-value">
                    {appointment.time
                      ? formatTime(appointment.time)
                      : appointment.start_time
                        ? `${formatTime(appointment.start_time)}${appointment.end_time ? ` - ${formatTime(appointment.end_time)}` : ''}`
                        : 'Not specified'
                    }
                  </span>
                </div>
                {appointment.start_time && appointment.end_time && (
                  <div className="info-row">
                    <span className="info-label">Duration:</span>
                    <span className="info-value">
                      {calculateDuration(appointment.start_time, appointment.end_time)} minutes
                    </span>
                  </div>
                )}
              </div>
            </div>

            <div className="info-group">
              <h3><i className="fas fa-info-circle"></i> Additional Information</h3>
              <div className="info-content">
                {appointment.created_at && (
                  <div className="info-row">
                    <span className="info-label">Created:</span>
                    <span className="info-value">{new Date(appointment.created_at).toLocaleString()}</span>
                  </div>
                )}
                {appointment.updated_at && (
                  <div className="info-row">
                    <span className="info-label">Updated:</span>
                    <span className="info-value">{new Date(appointment.updated_at).toLocaleString()}</span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {appointment.notes && (
          <div className="appointment-notes">
            <h3><i className="fas fa-sticky-note"></i> Notes</h3>
            <div className="notes-content">
              {appointment.notes}
            </div>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      {showConfirmDelete && (
        <div className="modal-overlay">
          <div className="modal-content">
            <h3><i className="fas fa-exclamation-triangle"></i> Confirm Delete</h3>
            <p>Are you sure you want to delete this appointment? This action cannot be undone.</p>
            <div className="modal-actions">
              <button className="btn-cancel" onClick={() => setShowConfirmDelete(false)}>
                Cancel
              </button>
              <button className="btn-danger" onClick={deleteAppointment}>
                Delete Appointment
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Helper function to calculate duration between start and end times
const calculateDuration = (startTime: string, endTime: string): number => {
  if (!startTime || !endTime) return 0;

  const [startHour, startMinute] = startTime.split(':').map(Number);
  const [endHour, endMinute] = endTime.split(':').map(Number);

  const startInMinutes = startHour * 60 + startMinute;
  const endInMinutes = endHour * 60 + endMinute;

  return endInMinutes - startInMinutes;
};

export default AppointmentDetail;