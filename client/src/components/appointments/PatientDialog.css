/* Modern, clean, minimalist, sleek styles for the Add New Patient dialog */

.patient-dialog-title {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--border-color, #f1f5f9);
}

.patient-dialog-title-icon {
  color: var(--primary-color, #2C4B2B); /* Primary color from theme */
  margin-right: 0.75rem;
  font-size: 1rem;
}

.patient-dialog-title-text {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-color, #0f172a);
  letter-spacing: 0.2px;
}

.patient-dialog-subtitle {
  padding: 0.5rem 1.5rem;
  margin-top: -0.5rem;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  color: var(--text-secondary-color, #64748b);
  font-size: 0.8rem;
  background-color: var(--bg-color, #f8fafc);
  border-bottom: 1px solid var(--border-color, #f1f5f9);
}

.patient-dialog-subtitle-icon {
  color: var(--text-secondary-color, #64748b);
  margin-right: 0.5rem;
  font-size: 0.8rem;
}

.patient-dialog-content {
  padding: 1.25rem 1.5rem;
}

.patient-form-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 1rem;
}

.patient-form-field {
  position: relative;
}

.patient-form-label {
  display: block;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-secondary-color, #64748b);
  margin-bottom: 0.4rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.patient-form-label i {
  font-size: 0.75rem;
  margin-right: 0.3rem;
  color: var(--placeholder-color, #94a3b8);
}

.patient-form-input,
.patient-form-select {
  width: 100%;
  padding: 0.6rem 0.75rem;
  border: 1px solid var(--border-color, #e2e8f0);
  border-radius: 6px;
  font-size: 0.85rem;
  transition: all 0.2s ease;
  background-color: var(--bg-color, #f8fafc);
  color: var(--text-color, #334155);
}

.patient-form-input:focus,
.patient-form-select:focus {
  outline: none;
  border-color: var(--primary-color, #0ea5e9);
  box-shadow: 0 0 0 2px var(--primary-color-light, rgba(14, 165, 233, 0.1));
  background-color: var(--input-focus-bg-color, white);
}

.patient-form-input::placeholder {
  color: var(--placeholder-color, #cbd5e1);
}

.patient-form-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%2394a3b8' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  padding-right: 2rem;
}

.patient-form-required {
  color: var(--primary-color, #0ea5e9);
  margin-left: 0.2rem;
}

.patient-dialog-actions {
  display: flex;
  justify-content: flex-end;
  padding: 1rem 1.5rem;
  background-color: var(--bg-color, #f8fafc);
  border-top: 1px solid var(--border-color, #f1f5f9);
}

.patient-dialog-btn-cancel {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  background-color: transparent;
  color: var(--text-secondary-color, #64748b);
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-right: 0.75rem;
}

.patient-dialog-btn-cancel:hover {
  background-color: var(--button-secondary-hover-bg, #f1f5f9);
  color: var(--text-color, #334155);
}

.patient-dialog-btn-create {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  background-color: var(--primary-color, #2C4B2B); /* Primary color from theme */
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
}

.patient-dialog-btn-create:hover:not(:disabled) {
  background-color: var(--primary-color-hover, #3E6A3D); /* Light primary color from theme */
}

.patient-dialog-btn-create:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.patient-dialog-btn-create i {
  margin-right: 0.4rem;
  font-size: 0.85rem;
}

.patient-dialog-error {
  background-color: var(--error-bg-color, #FCF1E9); /* Lightest error color from theme */
  color: var(--error-color, #D97B3A); /* Error color from theme */
  padding: 0.75rem;
  border-radius: 6px;
  margin-bottom: 1rem;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
}

.patient-dialog-error i {
  margin-right: 0.5rem;
  color: var(--error-color, #D97B3A); /* Error color from theme */
}

/* Full width for single field rows */
.patient-form-row.single-field {
  grid-template-columns: 1fr;
}

/* Responsive adjustments */
@media (max-width: 600px) {
  .patient-form-row {
    grid-template-columns: 1fr;
  }

  .patient-dialog-actions {
    flex-direction: column-reverse;
    gap: 0.5rem;
  }

  .patient-dialog-btn-cancel,
  .patient-dialog-btn-create {
    width: 100%;
    justify-content: center;
    margin-right: 0;
  }
}
