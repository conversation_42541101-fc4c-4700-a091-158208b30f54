/* Modern Dashboard Styles */
.modern-dashboard {
  padding: 1.5rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* Dashboard Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.header-content h1 {
  font-size: 1.8rem;
  margin: 0;
  color: #1e293b;
}

.header-subtitle {
  color: #64748b;
  margin: 0.25rem 0 0;
}

.new-appointment-btn {
  display: flex;
  align-items: center;
  background-color: #2C4B2B; /* Primary color from theme */
  color: white;
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.2s ease;
  box-shadow: 0 4px 6px rgba(44, 75, 43, 0.2); /* Primary color with opacity */
}

.new-appointment-btn:hover {
  background-color: #3E6A3D; /* Light primary color from theme */
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(44, 75, 43, 0.3); /* Primary color with opacity */
}

.new-appointment-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(44, 75, 43, 0.2); /* Primary color with opacity */
}

.new-appointment-btn i {
  margin-right: 0.5rem;
}

/* Stats Cards */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-card {
  background-color: white;
  border-radius: 12px;
  padding: 1.25rem;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  font-size: 1.5rem;
}

.stat-content {
  flex: 1;
}

.stat-content h3 {
  font-size: 1.75rem;
  margin: 0;
  font-weight: 700;
}

.stat-content p {
  margin: 0.25rem 0 0;
  color: #64748b;
  font-size: 0.9rem;
  font-weight: 500;
}

.stat-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background-color: rgba(0, 0, 0, 0.05);
}

.progress-bar {
  height: 100%;
  transition: width 0.5s ease;
}

/* Stat Card Variants */
.stat-card.today {
  border-left: 4px solid #2C4B2B; /* Primary color from theme */
}

.stat-card.today .stat-icon {
  background-color: rgba(44, 75, 43, 0.1); /* Primary color with opacity */
  color: #2C4B2B; /* Primary color from theme */
}

.stat-card.today .progress-bar {
  background-color: #2C4B2B; /* Primary color from theme */
}

.stat-card.upcoming {
  border-left: 4px solid #A1A43A; /* Secondary color from theme */
}

.stat-card.upcoming .stat-icon {
  background-color: rgba(161, 164, 58, 0.1); /* Secondary color with opacity */
  color: #A1A43A; /* Secondary color from theme */
}

.stat-card.upcoming .progress-bar {
  background-color: #A1A43A; /* Secondary color from theme */
}

.stat-card.completed {
  border-left: 4px solid #2C4B2B; /* Success color from theme (same as primary) */
}

.stat-card.completed .stat-icon {
  background-color: rgba(44, 75, 43, 0.1); /* Success color with opacity */
  color: #2C4B2B; /* Success color from theme */
}

.stat-card.completed .progress-bar {
  background-color: #2C4B2B; /* Success color from theme */
}

.stat-card.cancelled {
  border-left: 4px solid #D97B3A; /* Error color from theme */
}

.stat-card.cancelled .stat-icon {
  background-color: rgba(217, 123, 58, 0.1); /* Error color with opacity */
  color: #D97B3A; /* Error color from theme */
}

.stat-card.cancelled .progress-bar {
  background-color: #D97B3A; /* Error color from theme */
}

/* Dashboard Controls */
.dashboard-controls-wrapper {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 1.5rem;
  overflow: hidden;
}

.dashboard-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.tab-controls {
  display: flex;
  gap: 0.5rem;
}

.tab-btn {
  background: none;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.tab-btn i {
  margin-right: 0.5rem;
}

.tab-btn:hover {
  background-color: #f1f5f9;
  color: #1e293b;
}

.tab-btn.active {
  background-color: #2C4B2B; /* Primary color from theme */
  color: white;
  box-shadow: 0 2px 4px rgba(44, 75, 43, 0.2); /* Primary color with opacity */
}

.view-toggle {
  display: flex;
  gap: 0.25rem;
}

.view-btn {
  background: none;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-btn:hover {
  background-color: #f1f5f9;
  color: #1e293b;
}

.view-btn.active {
  background-color: #2C4B2B; /* Primary color from theme */
  color: white;
}

/* Filter Controls */
.filter-controls {
  padding: 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
}

.search-container {
  flex: 1;
  min-width: 250px;
  position: relative;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
}

.search-input {
  width: 100%;
  padding: 0.75rem 2.5rem;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  font-size: 0.95rem;
  background-color: #f8fafc;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #2C4B2B; /* Primary color from theme */
  box-shadow: 0 0 0 3px rgba(44, 75, 43, 0.1); /* Primary color with opacity */
  background-color: white;
}

.clear-search {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #94a3b8;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.clear-search:hover {
  background-color: #f1f5f9;
  color: #64748b;
}

.filter-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  align-items: center;
}

.filter-item {
  position: relative;
}

.filter-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
  pointer-events: none;
}

.filter-select,
.date-filter {
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  font-size: 0.95rem;
  background-color: #f8fafc;
  transition: all 0.2s ease;
  min-width: 180px;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%2364748b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.9rem center;
  padding-right: 2.5rem;
}

.filter-select:focus,
.date-filter:focus {
  outline: none;
  border-color: #2C4B2B; /* Primary color from theme */
  box-shadow: 0 0 0 3px rgba(44, 75, 43, 0.1); /* Primary color with opacity */
  background-color: white;
}

.clear-filters-btn {
  display: flex;
  align-items: center;
  background: none;
  border: 1px solid #e2e8f0;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-filters-btn:hover {
  background-color: #f1f5f9;
  color: #1e293b;
  border-color: #cbd5e1;
}

.clear-filters-btn i {
  margin-right: 0.5rem;
}

/* Active Filters */
.active-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  align-items: center;
}

.active-filters-label {
  font-size: 0.9rem;
  color: #64748b;
  margin-right: 0.5rem;
}

.filter-tag {
  display: flex;
  align-items: center;
  background-color: #EFF5EF; /* Lightest primary color from theme */
  border: 1px solid #2C4B2B; /* Primary color from theme */
  padding: 0.35rem 0.75rem;
  border-radius: 6px;
  font-size: 0.85rem;
  color: #2C4B2B; /* Primary color from theme */
}

.filter-tag i {
  margin-right: 0.5rem;
}

.remove-filter {
  background: none;
  border: none;
  color: #2C4B2B; /* Primary color from theme */
  margin-left: 0.5rem;
  cursor: pointer;
  padding: 0.15rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-filter:hover {
  background-color: rgba(44, 75, 43, 0.1); /* Primary color with opacity */
}

/* Appointments Container */
.appointments-container {
  margin-bottom: 2rem;
}

.appointment-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.25rem;
}

.appointment-card {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  display: flex;
  flex-direction: column;
  border-top: 4px solid #2C4B2B; /* Primary color from theme */
}

.appointment-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

/* Card Status Colors */
.appointment-card.scheduled {
  border-top-color: #2C4B2B; /* Primary color from theme */
}

.appointment-card.confirmed {
  border-top-color: #A1A43A; /* Secondary color from theme */
}

.appointment-card.in-progress {
  border-top-color: #F6B21A; /* Warning color from theme */
}

.appointment-card.completed {
  border-top-color: #2C4B2B; /* Success color from theme (same as primary) */
}

.appointment-card.cancelled,
.appointment-card.no-show {
  border-top-color: #D97B3A; /* Error color from theme */
}

.appointment-card.rescheduled {
  border-top-color: #F2A65A; /* Info color from theme */
}

/* Card Header */
.appointment-header {
  padding: 1.25rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e2e8f0;
}

.appointment-date {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #1e293b;
}

.appointment-date i {
  margin-right: 0.5rem;
  color: #64748b;
}

.appointment-status-badge {
  padding: 0.35rem 0.75rem;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: capitalize;
}

.appointment-status-badge.scheduled {
  background-color: #EFF5EF; /* Lightest primary color from theme */
  color: #2C4B2B; /* Primary color from theme */
}

.appointment-status-badge.confirmed {
  background-color: #F7F7E8; /* Lightest secondary color from theme */
  color: #A1A43A; /* Secondary color from theme */
}

.appointment-status-badge.in-progress {
  background-color: #FEF7E8; /* Lightest warning color from theme */
  color: #F6B21A; /* Warning color from theme */
}

.appointment-status-badge.completed {
  background-color: #EFF5EF; /* Lightest success color from theme */
  color: #2C4B2B; /* Success color from theme (same as primary) */
}

.appointment-status-badge.cancelled,
.appointment-status-badge.no-show {
  background-color: #FCF1E9; /* Lightest error color from theme */
  color: #D97B3A; /* Error color from theme */
}

.appointment-status-badge.rescheduled {
  background-color: #FDF5EC; /* Lightest info color from theme */
  color: #F2A65A; /* Info color from theme */
}

/* Card Body */
.appointment-body {
  padding: 1.25rem;
  flex: 1;
}

.patient-info h3 {
  margin: 0 0 0.75rem;
  font-size: 1.1rem;
}

.patient-info h3 a {
  color: #1e293b;
  text-decoration: none;
  display: flex;
  align-items: center;
  transition: color 0.2s ease;
}

.patient-info h3 a:hover {
  color: #2C4B2B; /* Primary color from theme */
}

.patient-icon {
  margin-right: 0.5rem;
  color: #64748b;
}

.patient-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.patient-id,
.appointment-time {
  display: flex;
  align-items: center;
  font-size: 0.85rem;
  color: #64748b;
}

.patient-id i,
.appointment-time i {
  margin-right: 0.35rem;
}

.appointment-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.detail-item {
  display: flex;
  align-items: flex-start;
}

.detail-item i {
  margin-right: 0.75rem;
  margin-top: 0.25rem;
  color: #64748b;
}

.detail-content {
  flex: 1;
}

.detail-label {
  display: block;
  font-size: 0.8rem;
  color: #64748b;
  margin-bottom: 0.25rem;
}

.detail-value {
  font-size: 0.95rem;
  color: #1e293b;
}

.doctor-specialty {
  display: inline-block;
  font-size: 0.8rem;
  color: #64748b;
  background-color: #f1f5f9;
  padding: 0.15rem 0.5rem;
  border-radius: 4px;
  margin-left: 0.5rem;
}

/* Card Footer */
.appointment-footer {
  padding: 1rem 1.25rem;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-control {
  display: flex;
  align-items: center;
}

.status-label {
  font-size: 0.9rem;
  color: #64748b;
  margin-right: 0.75rem;
}

.select-wrapper {
  position: relative;
}

.status-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  z-index: 1;
}

.status-loading i {
  color: #2C4B2B; /* Primary color from theme */
}

.status-select {
  padding: 0.5rem 2rem 0.5rem 0.75rem;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  font-size: 0.9rem;
  background-color: #f8fafc;
  transition: all 0.2s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%2364748b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.5rem center;
  min-width: 140px;
  text-transform: capitalize;
}

.status-select:focus {
  outline: none;
  border-color: #2C4B2B; /* Primary color from theme */
  box-shadow: 0 0 0 3px rgba(44, 75, 43, 0.1); /* Primary color with opacity */
  background-color: white;
}

/* Status Select Colors */
.status-select.scheduled {
  color: #2C4B2B; /* Primary color from theme */
  border-color: #EFF5EF; /* Lightest primary color from theme */
}

.status-select.confirmed {
  color: #A1A43A; /* Secondary color from theme */
  border-color: #F7F7E8; /* Lightest secondary color from theme */
}

.status-select.in-progress {
  color: #F6B21A; /* Warning color from theme */
  border-color: #FEF7E8; /* Lightest warning color from theme */
}

.status-select.completed {
  color: #2C4B2B; /* Success color from theme (same as primary) */
  border-color: #EFF5EF; /* Lightest success color from theme */
}

.status-select.cancelled,
.status-select.no-show {
  color: #D97B3A; /* Error color from theme */
  border-color: #FCF1E9; /* Lightest error color from theme */
}

.status-select.rescheduled {
  color: #F2A65A; /* Info color from theme */
  border-color: #FDF5EC; /* Lightest info color from theme */
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  width: 36px;
  height: 36px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  background: none;
  text-decoration: none;
}

.action-btn.view {
  background-color: #EFF5EF; /* Lightest primary color from theme */
  color: #2C4B2B; /* Primary color from theme */
}

.action-btn.view:hover {
  background-color: #3E6A3D; /* Light primary color from theme */
  color: white;
}

.action-btn.edit {
  background-color: #F7F7E8; /* Lightest secondary color from theme */
  color: #A1A43A; /* Secondary color from theme */
}

.action-btn.edit:hover {
  background-color: #A1A43A; /* Secondary color from theme */
  color: white;
}

.action-btn.convert {
  background-color: #EFF5EF; /* Lightest success color from theme (same as primary) */
  color: #2C4B2B; /* Success color from theme (same as primary) */
}

.action-btn.convert:hover {
  background-color: #2C4B2B; /* Success color from theme (same as primary) */
  color: white;
}

.action-btn.status {
  background-color: #FEF7E8; /* Lightest warning color from theme */
  color: #F6B21A; /* Warning color from theme */
}

.action-btn.status:hover {
  background-color: #F6B21A; /* Warning color from theme */
  color: white;
}

.status-dropdown {
  position: relative;
}

.dropdown-content {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 0.5rem;
  min-width: 180px;
  z-index: 10;
  display: none;
  flex-direction: column;
  gap: 0.25rem;
}

.status-dropdown:hover .dropdown-content {
  display: flex;
}

.status-option {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.9rem;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.status-option:hover:not(:disabled) {
  background-color: #EFF5EF; /* Lightest primary color from theme */
}

.status-option.active {
  background-color: #EFF5EF; /* Lightest primary color from theme */
  color: #2C4B2B; /* Primary color from theme */
  font-weight: 500;
}

.status-option i {
  margin-right: 0.5rem;
}

.status-option:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.empty-state-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #f0f9ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  font-size: 2rem;
  color: #0ea5e9;
}

.empty-state h3 {
  font-size: 1.25rem;
  margin: 0 0 0.5rem;
  color: #1e293b;
}

.empty-state p {
  color: #64748b;
  margin: 0 0 1.5rem;
  max-width: 400px;
}

.empty-state-actions {
  display: flex;
  gap: 1rem;
}

.btn-primary,
.btn-secondary {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.25rem;
  border-radius: 8px;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.btn-primary {
  background-color: #0ea5e9;
  color: white;
  box-shadow: 0 4px 6px rgba(14, 165, 233, 0.2);
}

.btn-primary:hover {
  background-color: #0284c7;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(14, 165, 233, 0.3);
}

.btn-secondary {
  background-color: #f1f5f9;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn-secondary:hover {
  background-color: #e2e8f0;
  color: #1e293b;
}

.btn-primary i,
.btn-secondary i {
  margin-right: 0.5rem;
}

/* Calendar View Placeholder */
.calendar-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.placeholder-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: #f0f9ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  font-size: 2rem;
  color: #0ea5e9;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .new-appointment-btn {
    width: 100%;
    justify-content: center;
  }

  .dashboard-stats {
    grid-template-columns: 1fr;
  }

  .dashboard-controls {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .tab-controls {
    width: 100%;
    overflow-x: auto;
    padding-bottom: 0.5rem;
  }

  .view-toggle {
    align-self: flex-end;
  }

  .filter-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group {
    flex-direction: column;
    align-items: stretch;
  }

  .appointment-cards {
    grid-template-columns: 1fr;
  }
}
