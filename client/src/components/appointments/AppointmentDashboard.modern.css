/* Modern, Clean, Minimalist Appointment Dashboard Styles */

/* Dark mode variables */
:root {
  --text-color: #334155;
  --text-secondary-color: #64748b;
  --bg-color: #f8fafc;
  --card-bg-color: #ffffff;
  --border-color: rgba(0, 0, 0, 0.1);
  --shadow-color: rgba(0, 0, 0, 0.05);
  --hover-bg-color: rgba(0, 0, 0, 0.05);
}

/* Dark mode overrides */
.dark-mode {
  --text-color: #e2e8f0;
  --text-secondary-color: #94a3b8;
  --bg-color: #121212;
  --card-bg-color: #1e1e1e;
  --border-color: rgba(255, 255, 255, 0.1);
  --shadow-color: rgba(0, 0, 0, 0.2);
  --hover-bg-color: rgba(255, 255, 255, 0.05);
}

/* Base Dashboard Container */
.modern-dashboard {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  color: var(--text-color);
  background-color: var(--bg-color);
  min-height: calc(100vh - 64px);
}

/* Dashboard Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.header-content h1 {
  font-size: 1.75rem;
  font-weight: 600;
  margin: 0;
  color: var(--text-color);
  letter-spacing: -0.02em;
}

.header-subtitle {
  color: var(--text-secondary-color);
  margin: 0.5rem 0 0;
  font-size: 0.95rem;
  font-weight: 400;
}

.new-appointment-btn {
  display: flex;
  align-items: center;
  background-color: #2C4B2B; /* Primary color from theme */
  color: white;
  padding: 0.7rem 1.2rem;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.9rem;
  text-decoration: none;
  transition: all 0.2s ease;
  box-shadow: 0 1px 2px rgba(44, 75, 43, 0.1); /* Primary color with opacity */
}

.new-appointment-btn:hover {
  background-color: #3E6A3D; /* Light primary color from theme */
  transform: translateY(-1px);
}

.new-appointment-btn i {
  margin-right: 0.5rem;
  font-size: 0.9rem;
}

/* Stats Cards */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background-color: var(--card-bg-color);
  border-radius: 8px;
  padding: 1.25rem;
  display: flex;
  align-items: center;
  position: relative;
  box-shadow: 0 1px 3px var(--shadow-color);
  transition: transform 0.2s ease;
  border-left: 3px solid transparent;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  font-size: 1.2rem;
}

.stat-content {
  flex: 1;
}

.stat-content h3 {
  font-size: 1.5rem;
  margin: 0;
  font-weight: 600;
  letter-spacing: -0.02em;
  color: var(--text-color);
}

.stat-content p {
  margin: 0.25rem 0 0;
  color: var(--text-secondary-color);
  font-size: 0.85rem;
  font-weight: 500;
}

.stat-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: rgba(0, 0, 0, 0.03);
}

.progress-bar {
  height: 100%;
  transition: width 0.5s ease;
}

/* Stat Card Variants */
.stat-card.today {
  border-left-color: #2C4B2B; /* Primary color from theme */
}

.stat-card.today .stat-icon {
  background-color: rgba(44, 75, 43, 0.1); /* Primary color with opacity */
  color: #2C4B2B; /* Primary color from theme */
}

.stat-card.today .progress-bar {
  background-color: #2C4B2B; /* Primary color from theme */
}

.stat-card.upcoming {
  border-left-color: #A1A43A; /* Secondary color from theme */
}

.stat-card.upcoming .stat-icon {
  background-color: rgba(161, 164, 58, 0.1); /* Secondary color with opacity */
  color: #A1A43A; /* Secondary color from theme */
}

.stat-card.upcoming .progress-bar {
  background-color: #A1A43A; /* Secondary color from theme */
}

.stat-card.completed {
  border-left-color: #2C4B2B; /* Success color from theme (same as primary) */
}

.stat-card.completed .stat-icon {
  background-color: rgba(44, 75, 43, 0.1); /* Success color with opacity */
  color: #2C4B2B; /* Success color from theme */
}

.stat-card.completed .progress-bar {
  background-color: #2C4B2B; /* Success color from theme */
}

.stat-card.cancelled {
  border-left-color: #D97B3A; /* Error color from theme */
}

.stat-card.cancelled .stat-icon {
  background-color: rgba(217, 123, 58, 0.1); /* Error color with opacity */
  color: #D97B3A; /* Error color from theme */
}

.stat-card.cancelled .progress-bar {
  background-color: #D97B3A; /* Error color from theme */
}

/* Controls Container */
.controls-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
  overflow: hidden;
}

/* Tab Controls */
.tab-controls {
  display: flex;
  border-bottom: 1px solid #f1f5f9;
  padding: 0 1rem;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.tab-controls::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

.tab-btn {
  background: none;
  border: none;
  padding: 1rem 1.25rem;
  font-size: 0.9rem;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  position: relative;
  white-space: nowrap;
}

.tab-btn i {
  margin-right: 0.5rem;
  font-size: 0.9rem;
}

.tab-btn:hover {
  color: #2C4B2B; /* Primary color from theme */
}

.tab-btn.active {
  color: #2C4B2B; /* Primary color from theme */
  font-weight: 600;
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #2C4B2B; /* Primary color from theme */
}

/* Filter Controls */
.filter-controls {
  padding: 1rem;
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
  border-bottom: 1px solid #f1f5f9;
}

.search-container {
  flex: 1;
  min-width: 250px;
  position: relative;
}

.search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
  font-size: 0.9rem;
}

.search-input {
  width: 100%;
  padding: 0.7rem 1rem 0.7rem 2.5rem;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  font-size: 0.9rem;
  background-color: #f8fafc;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #2C4B2B; /* Primary color from theme */
  box-shadow: 0 0 0 2px rgba(44, 75, 43, 0.1); /* Primary color with opacity */
  background-color: white;
}

.clear-search {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #94a3b8;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.clear-search:hover {
  color: #64748b;
}

.filter-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  align-items: center;
}

.filter-item {
  position: relative;
}

.filter-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
  pointer-events: none;
  font-size: 0.9rem;
}

.filter-select,
.date-filter {
  padding: 0.7rem 1rem 0.7rem 2.25rem;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  font-size: 0.9rem;
  background-color: #f8fafc;
  transition: all 0.2s ease;
  min-width: 160px;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='14' viewBox='0 0 24 24' fill='none' stroke='%2364748b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  padding-right: 2.25rem;
}

.filter-select:focus,
.date-filter:focus {
  outline: none;
  border-color: #2C4B2B; /* Primary color from theme */
  box-shadow: 0 0 0 2px rgba(44, 75, 43, 0.1); /* Primary color with opacity */
  background-color: white;
}

.clear-filters-btn {
  display: flex;
  align-items: center;
  background: none;
  border: 1px solid #e2e8f0;
  padding: 0.7rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-filters-btn:hover {
  background-color: #f1f5f9;
  color: #334155;
}

.clear-filters-btn i {
  margin-right: 0.5rem;
  font-size: 0.9rem;
}

/* View Toggle */
.view-toggle {
  display: flex;
  padding: 0.75rem 1rem;
  gap: 0.5rem;
  border-bottom: 1px solid #f1f5f9;
}

.view-btn {
  background: none;
  border: 1px solid #e2e8f0;
  width: 36px;
  height: 36px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
}

.view-btn:hover {
  border-color: #cbd5e1;
  color: #334155;
}

.view-btn.active {
  background-color: #2C4B2B; /* Primary color from theme */
  color: white;
  border-color: #2C4B2B; /* Primary color from theme */
}

/* Active Filters */
.active-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  align-items: center;
  background-color: #f8fafc;
}

.active-filters-label {
  font-size: 0.85rem;
  color: #64748b;
  margin-right: 0.5rem;
}

.filter-tag {
  display: flex;
  align-items: center;
  background-color: var(--card-bg-color);
  border: 1px solid var(--border-color);
  padding: 0.35rem 0.75rem;
  border-radius: 6px;
  font-size: 0.85rem;
  color: var(--text-color);
}

.filter-tag i {
  margin-right: 0.5rem;
  color: var(--text-secondary-color);
  font-size: 0.85rem;
}

.remove-filter {
  background: none;
  border: none;
  color: #64748b;
  margin-left: 0.5rem;
  cursor: pointer;
  padding: 0.15rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-filter:hover {
  color: #ef4444;
}

/* Appointments Container */
.appointments-container {
  margin-bottom: 2rem;
}

.appointment-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
}

.appointment-card {
  background-color: var(--card-bg-color);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px var(--shadow-color);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  display: flex;
  flex-direction: column;
  border-left: 2px solid #2C4B2B; /* Primary color from theme */
}

.appointment-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px var(--shadow-color);
}

/* Card Status Colors */
.appointment-card.scheduled {
  border-left-color: #2C4B2B; /* Primary color from theme */
}

.appointment-card.confirmed {
  border-left-color: #A1A43A; /* Secondary color from theme */
}

.appointment-card.in-progress {
  border-left-color: #F6B21A; /* Warning color from theme */
}

.appointment-card.completed {
  border-left-color: #2C4B2B; /* Success color from theme (same as primary) */
}

.appointment-card.cancelled,
.appointment-card.no-show {
  border-left-color: #D97B3A; /* Error color from theme */
}

.appointment-card.rescheduled {
  border-left-color: #F2A65A; /* Info color from theme */
}

/* Card Header */
.appointment-header {
  padding: 1rem 1.25rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border-color);
}

.appointment-date {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: var(--text-color);
  font-size: 1rem;
}

.appointment-date i {
  margin-right: 0.75rem;
  color: #2C4B2B; /* Primary color from theme */
  font-size: 1rem;
}

.appointment-status-badge {
  padding: 0.35rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: capitalize;
  background-color: rgba(44, 75, 43, 0.1); /* Primary color with opacity */
  color: #2C4B2B; /* Primary color from theme */
}

/* Status Badge Colors */
.appointment-card.scheduled .appointment-status-badge {
  background-color: rgba(44, 75, 43, 0.1); /* Primary color with opacity */
  color: #2C4B2B; /* Primary color from theme */
}

.appointment-card.confirmed .appointment-status-badge {
  background-color: rgba(161, 164, 58, 0.1); /* Secondary color with opacity */
  color: #A1A43A; /* Secondary color from theme */
}

.appointment-card.in-progress .appointment-status-badge {
  background-color: rgba(246, 178, 26, 0.1); /* Warning color with opacity */
  color: #F6B21A; /* Warning color from theme */
}

.appointment-card.completed .appointment-status-badge {
  background-color: rgba(44, 75, 43, 0.1); /* Success color with opacity */
  color: #2C4B2B; /* Success color from theme */
}

.appointment-card.cancelled .appointment-status-badge,
.appointment-card.no-show .appointment-status-badge {
  background-color: rgba(217, 123, 58, 0.1); /* Error color with opacity */
  color: #D97B3A; /* Error color from theme */
}

.appointment-card.rescheduled .appointment-status-badge {
  background-color: rgba(242, 166, 90, 0.1); /* Info color with opacity */
  color: #F2A65A; /* Info color from theme */
}

/* Card Body */
.appointment-body {
  padding: 1.5rem 1.25rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Patient Info */
.patient-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.patient-name {
  display: flex;
  align-items: center;
  font-size: 1.1rem;
  font-weight: 600;
  color: #0f172a;
  text-decoration: none;
}

.patient-name:hover {
  color: #3E6A3D; /* Light primary color from theme */
}

.patient-name i {
  margin-right: 0.75rem;
  color: #2C4B2B; /* Primary color from theme */
  font-size: 1.25rem;
}

.patient-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  background-color: #f8fafc;
  padding: 0.75rem 1rem;
  border-radius: 8px;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: #64748b;
}

.meta-item i {
  margin-right: 0.5rem;
  color: #2C4B2B; /* Primary color from theme */
  font-size: 0.9rem;
}

/* Reason Section */
.reason-section {
  background-color: #f8fafc;
  padding: 1rem;
  border-radius: 8px;
}

.reason-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.5rem;
}

.reason-content {
  color: #334155;
  font-size: 0.95rem;
}

/* Card Footer */
.appointment-footer {
  padding: 1rem 1.25rem;
  border-top: 1px solid #f1f5f9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-control {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.status-label {
  font-size: 0.85rem;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
}

.select-wrapper {
  position: relative;
}

.status-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 6px;
  z-index: 1;
}

.status-select {
  padding: 0.5rem 2rem 0.5rem 1rem;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  font-size: 0.9rem;
  background-color: white;
  transition: all 0.2s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%2364748b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  min-width: 140px;
  color: #334155;
}

.status-select:focus {
  outline: none;
  border-color: #2C4B2B; /* Primary color from theme */
  box-shadow: 0 0 0 2px rgba(44, 75, 43, 0.1); /* Primary color with opacity */
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 1rem;
  text-decoration: none;
  border: none;
}

.action-btn.view {
  background-color: #2C4B2B; /* Primary color from theme */
  color: white;
}

.action-btn.view:hover {
  background-color: #3E6A3D; /* Light primary color from theme */
}

.action-btn.edit {
  background-color: #A1A43A; /* Secondary color from theme */
  color: white;
}

.action-btn.edit:hover {
  background-color: #B5B84E; /* Light secondary color from theme */
}

.action-btn.convert {
  background-color: #2C4B2B; /* Success color from theme (same as primary) */
  color: white;
}

.action-btn.convert:hover {
  background-color: #3E6A3D; /* Light success color from theme */
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.empty-state-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: rgba(44, 75, 43, 0.1); /* Primary color with opacity */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  color: #2C4B2B; /* Primary color from theme */
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #0f172a;
}

.empty-state p {
  margin: 0 0 1.5rem 0;
  color: #64748b;
  max-width: 400px;
}

.empty-state-actions {
  display: flex;
  gap: 1rem;
}

.btn-primary {
  background-color: #2C4B2B; /* Primary color from theme */
  color: white;
  border: none;
  padding: 0.7rem 1.2rem;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.btn-primary:hover {
  background-color: #3E6A3D; /* Light primary color from theme */
}

.btn-primary i {
  margin-right: 0.5rem;
}

.btn-secondary {
  background-color: white;
  color: #64748b;
  border: 1px solid #e2e8f0;
  padding: 0.7rem 1.2rem;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.btn-secondary:hover {
  background-color: #f8fafc;
  color: #334155;
  border-color: #cbd5e1;
}

/* Custom styles for themed secondary button */
.btn-secondary[style*="color: #2C4B2B"] {
  color: #2C4B2B;
  border-color: #2C4B2B;
}

.btn-secondary[style*="color: #2C4B2B"]:hover {
  background-color: rgba(44, 75, 43, 0.04);
  color: #2C4B2B;
  border-color: #2C4B2B;
}

.btn-secondary i {
  margin-right: 0.5rem;
}

/* Calendar Placeholder */
.calendar-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.placeholder-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background-color: rgba(44, 75, 43, 0.1); /* Primary color with opacity */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  font-size: 1.5rem;
  color: #2C4B2B; /* Primary color from theme */
}

/* Dialog Styles */
.dialog-title {
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #f1f5f9;
}

.dialog-title-icon {
  color: #2C4B2B; /* Primary color from theme */
  margin-right: 0.75rem;
  font-size: 1.1rem;
}

.dialog-title-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: #0f172a;
}

.dialog-content {
  padding: 1.5rem;
}

.dialog-actions {
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  border-top: 1px solid #f1f5f9;
}

.dialog-btn {
  padding: 0.6rem 1rem;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
}

.dialog-btn-cancel {
  background: none;
  border: 1px solid #e2e8f0;
  color: #64748b;
}

.dialog-btn-cancel:hover {
  background-color: #f8fafc;
  color: #334155;
  border-color: #cbd5e1;
}

.dialog-btn-primary {
  background-color: white;
  color: #2C4B2B; /* Primary color from theme */
  border: 1px solid #e2e8f0;
}

.dialog-btn-primary:hover {
  background-color: rgba(44, 75, 43, 0.05); /* Primary color with opacity */
  border-color: rgba(44, 75, 43, 0.2); /* Primary color with opacity */
}

.dialog-btn-primary i {
  margin-right: 0.5rem;
}

.dialog-btn-action {
  background-color: #2C4B2B; /* Primary color from theme */
  color: white;
  border: none;
}

.dialog-btn-action:hover {
  background-color: #3E6A3D; /* Light primary color from theme */
}

.dialog-btn-action i {
  margin-right: 0.5rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .modern-dashboard {
    padding: 1rem;
  }

  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .new-appointment-btn {
    width: 100%;
    justify-content: center;
  }

  .tab-controls {
    padding: 0 0.5rem;
  }

  .tab-btn {
    padding: 0.75rem 0.75rem;
    font-size: 0.85rem;
  }

  .filter-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group {
    flex-direction: column;
    align-items: stretch;
  }

  .appointment-footer {
    flex-direction: column;
    gap: 1rem;
  }

  .status-control {
    width: 100%;
  }

  .action-buttons {
    width: 100%;
    justify-content: flex-end;
  }
}
