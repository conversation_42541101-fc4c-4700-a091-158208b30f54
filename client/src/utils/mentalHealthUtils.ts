/**
 * Utility functions for mental health assessments
 */

/**
 * Get treatment recommendation for PHQ-9 depression assessment
 * @param score PHQ-9 score (0-27)
 * @returns Treatment recommendation text
 */
export const getPhq9TreatmentRecommendation = (score: number): string => {
  if (score >= 0 && score <= 4) {
    return "No treatment recommended. Monitor and reassess if symptoms worsen.";
  } else if (score >= 5 && score <= 9) {
    return "Watchful waiting; consider counseling, self-help resources, or follow-up within 1 month.";
  } else if (score >= 10 && score <= 14) {
    return "Consider psychotherapy, counseling, or pharmacotherapy; regular follow-up recommended.";
  } else if (score >= 15 && score <= 19) {
    return "Active treatment with pharmacotherapy and/or psychotherapy strongly recommended.";
  } else if (score >= 20) {
    return "Immediate initiation of pharmacotherapy and expedited referral to mental health specialist. Consider hospitalization if safety concerns exist.";
  } else {
    return "Unable to provide recommendation due to invalid score.";
  }
};

/**
 * Get treatment recommendation for GAD-7 anxiety assessment
 * @param score GAD-7 score (0-21)
 * @returns Treatment recommendation text
 */
export const getGad7TreatmentRecommendation = (score: number): string => {
  if (score >= 0 && score <= 4) {
    return "No treatment recommended. Monitor and reassess if symptoms worsen.";
  } else if (score >= 5 && score <= 9) {
    return "Consider supportive counseling, self-help resources, or follow-up within 1 month.";
  } else if (score >= 10 && score <= 14) {
    return "Consider cognitive behavioral therapy, other psychotherapy, or pharmacotherapy; regular follow-up recommended.";
  } else if (score >= 15) {
    return "Active treatment with pharmacotherapy and/or psychotherapy strongly recommended. Consider referral to mental health specialist.";
  } else {
    return "Unable to provide recommendation due to invalid score.";
  }
};

/**
 * Get Mini-Cog treatment recommendation
 * @param score Mini-Cog score (0-5)
 * @returns Treatment recommendation text
 */
export const getMiniCogRecommendation = (score: number): string => {
  if (score <= 2) {
    return "Further cognitive evaluation recommended. Consider referral to specialist for comprehensive cognitive assessment.";
  } else {
    return "No specific cognitive intervention needed at this time. Continue routine monitoring.";
  }
};
