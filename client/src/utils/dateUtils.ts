/**
 * Format a date string to a more readable format
 * @param dateString - The date string to format (YYYY-MM-DD or any valid date string)
 * @returns Formatted date string (e.g., "Mon, Jan 15")
 */
export const formatDate = (dateString: string): string => {
  if (!dateString) return '';

  try {
    // If the date string contains time information, extract just the date part
    let dateToFormat = dateString;

    if (dateString.includes(' ')) {
      // Handle format like "2025-04-28 09:00:00"
      dateToFormat = dateString.split(' ')[0];
    } else if (dateString.includes('T')) {
      // Handle ISO format like "2025-04-28T09:00:00Z"
      dateToFormat = dateString.split('T')[0];
    }

    const date = new Date(dateToFormat);

    // Check if the date is valid
    if (isNaN(date.getTime())) {
      console.error('Invalid date:', dateString);
      return 'Invalid date';
    }

    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  } catch (error) {
    console.error('Error formatting date:', error, 'Input was:', dateString);
    return 'Invalid date';
  }
};

/**
 * Format a time string to a more readable format
 * @param timeString - The time string to format (HH:MM:SS or ISO date string)
 * @returns Formatted time string (e.g., "2:30 PM")
 */
export const formatTime = (timeString: string): string => {
  if (!timeString) return '';

  try {
    // Check if it's a full ISO date string (contains T and possibly Z)
    if (timeString.includes('T')) {
      const date = new Date(timeString);
      if (isNaN(date.getTime())) {
        return 'Invalid time';
      }

      return date.toLocaleTimeString('en-US', {
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      });
    }

    // If the time is in HH:MM:SS format, extract just HH:MM
    const timeParts = timeString.split(':');
    if (timeParts.length >= 2) {
      // Make sure we have valid numbers
      const hours = parseInt(timeParts[0], 10);
      if (isNaN(hours) || hours < 0 || hours > 23) {
        return 'Invalid time';
      }

      const minutes = parseInt(timeParts[1], 10);
      if (isNaN(minutes) || minutes < 0 || minutes > 59) {
        return 'Invalid time';
      }

      const ampm = hours >= 12 ? 'PM' : 'AM';
      const formattedHours = hours % 12 || 12; // Convert 0 to 12 for 12 AM
      const formattedMinutes = minutes.toString().padStart(2, '0');

      return `${formattedHours}:${formattedMinutes} ${ampm}`;
    }

    return 'Invalid format';
  } catch (error) {
    console.error('Error formatting time:', error, 'Input was:', timeString);
    return 'Invalid time';
  }
};

/**
 * Get the current date in YYYY-MM-DD format
 * @returns Current date in YYYY-MM-DD format
 */
export const getCurrentDate = (): string => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0');
  const day = String(today.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
};

/**
 * Check if a date is in the past
 * @param dateString - The date string to check (YYYY-MM-DD)
 * @returns True if the date is in the past, false otherwise
 */
export const isDateInPast = (dateString: string): boolean => {
  if (!dateString) return false;

  const date = new Date(dateString);
  const today = new Date();
  today.setHours(0, 0, 0, 0); // Set to beginning of day for accurate comparison

  return date < today;
};

/**
 * Calculate the difference in days between two dates
 * @param date1 - First date string (YYYY-MM-DD)
 * @param date2 - Second date string (YYYY-MM-DD), defaults to current date
 * @returns Number of days between the two dates
 */
export const daysBetweenDates = (date1: string, date2?: string): number => {
  if (!date1) return 0;

  const firstDate = new Date(date1);
  const secondDate = date2 ? new Date(date2) : new Date();

  // Set both dates to the beginning of the day for accurate calculation
  firstDate.setHours(0, 0, 0, 0);
  secondDate.setHours(0, 0, 0, 0);

  const diffTime = Math.abs(secondDate.getTime() - firstDate.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  return diffDays;
};