/**
 * Utility functions for data processing
 */

/**
 * Process date fields in data, converting empty strings to null
 * @param data Object containing date fields
 * @param dateFields Array of field names that should be dates
 * @returns Processed data with empty date strings converted to null
 */
export const processDateFields = <T extends Record<string, any>>(
  data: T,
  dateFields: string[]
): T => {
  const processedData = { ...data } as Record<string, any>;

  dateFields.forEach(field => {
    if (field in processedData && (processedData[field] === '' || processedData[field] === undefined)) {
      processedData[field] = null;
    }
  });

  return processedData as T;
};

/**
 * Ensure required text fields are present with default values
 * @param data Object containing text fields
 * @param textFields Object mapping field names to default values
 * @returns Processed data with default values for missing fields
 */
export const ensureTextFields = <T extends Record<string, any>>(
  data: T,
  textFields: Record<string, string>
): T => {
  const processedData = { ...data } as Record<string, any>;

  Object.entries(textFields).forEach(([field, defaultValue]) => {
    if (field in processedData && (processedData[field] === undefined)) {
      processedData[field] = defaultValue;
    }
  });

  return processedData as T;
};

/**
 * Process numeric fields in data, converting empty strings to null
 * @param data Object containing numeric fields
 * @param numericFields Array of field names that should be numeric
 * @returns Processed data with empty numeric strings converted to null
 */
export const processNumericFields = <T extends Record<string, any>>(
  data: T,
  numericFields: string[]
): T => {
  const processedData = { ...data } as Record<string, any>;

  numericFields.forEach(field => {
    if (field in processedData && processedData[field] === '') {
      processedData[field] = null;
    }
  });

  return processedData as T;
};

/**
 * Process time fields in data, removing empty time fields to avoid database errors
 * @param data Object containing time fields
 * @param timeFields Array of field names that should be times
 * @returns Processed data with empty time fields removed
 */
export const processTimeFields = <T extends Record<string, any>>(
  data: T,
  timeFields: string[]
): T => {
  const processedData = { ...data } as Record<string, any>;

  timeFields.forEach(field => {
    if (field in processedData && (processedData[field] === '' || processedData[field] === undefined)) {
      // Remove the field entirely to let the server handle default values
      delete processedData[field];
    }
  });

  return processedData as T;
};
