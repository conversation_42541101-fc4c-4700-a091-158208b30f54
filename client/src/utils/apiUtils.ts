import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import { API_URL } from '../config';

// Create an axios instance with default config
const api = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 10000, // 10 seconds timeout
});

// Add request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['x-auth-token'] = token;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error: AxiosError) => {
    // Handle session expiration
    if (error.response?.status === 401) {
      // Clear token and redirect to login if unauthorized
      localStorage.removeItem('token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

/**
 * Generic API request function with error handling
 * @param method HTTP method
 * @param url API endpoint
 * @param data Request data (for POST, PUT, etc.)
 * @param config Additional axios config
 * @returns Promise with response data
 */
export const apiRequest = async <T = any>(
  method: string,
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<T> => {
  try {
    let response: AxiosResponse<T>;

    switch (method.toUpperCase()) {
      case 'GET':
        response = await api.get<T>(url, config);
        break;
      case 'POST':
        response = await api.post<T>(url, data, config);
        break;
      case 'PUT':
        response = await api.put<T>(url, data, config);
        break;
      case 'DELETE':
        response = await api.delete<T>(url, config);
        break;
      default:
        throw new Error(`Unsupported method: ${method}`);
    }

    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError<{ msg?: string }>;
      const errorMessage = 
        axiosError.response?.data?.msg || 
        axiosError.message || 
        'An unknown error occurred';
      
      console.error(`API Error (${method} ${url}):`, errorMessage);
      throw new Error(errorMessage);
    }
    
    // For non-axios errors
    console.error(`API Error (${method} ${url}):`, error);
    throw error;
  }
};

// Convenience methods
export const get = <T = any>(url: string, config?: AxiosRequestConfig) => 
  apiRequest<T>('GET', url, undefined, config);

export const post = <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => 
  apiRequest<T>('POST', url, data, config);

export const put = <T = any>(url: string, data?: any, config?: AxiosRequestConfig) => 
  apiRequest<T>('PUT', url, data, config);

export const del = <T = any>(url: string, config?: AxiosRequestConfig) => 
  apiRequest<T>('DELETE', url, undefined, config);

export default api;
