/**
 * Date and time utility functions that respect the system's timezone setting
 */

// Time zone offset mapping (simplified)
const TIME_ZONE_OFFSETS: { [key: string]: number } = {
  'UTC': 0,
  'EST': -5,
  'CST': -6,
  'MST': -7,
  'PST': -8,
  'Asia/Kathmandu': 5.75, // Nepal Time is UTC+5:45 (5 hours and 45 minutes)
  'NPT': 5.75, // Nepal Time is UTC+5:45 (5 hours and 45 minutes)
};

/**
 * Convert a date string to a Date object in the specified timezone
 * @param dateString - The date string to convert
 * @param timezone - The timezone to use (from system settings)
 * @returns Date object adjusted for the timezone
 */
export const convertToTimezone = (dateString: string, timezone: string = 'UTC'): Date => {
  if (!dateString) return new Date();

  // Create a date object from the string - this will be in local timezone
  const date = new Date(dateString);

  if (isNaN(date.getTime())) {
    console.error('Invalid date:', dateString);
    return new Date();
  }

  // If timezone is UTC, convert to UTC
  if (timezone === 'UTC') {
    // Create a new date object with UTC time values
    return new Date(Date.UTC(
      date.getFullYear(),
      date.getMonth(),
      date.getDate(),
      date.getHours(),
      date.getMinutes(),
      date.getSeconds(),
      date.getMilliseconds()
    ));
  }

  // For Nepal Time (NPT) or other timezones, we need to handle it differently
  // First, get the date in UTC
  const year = date.getUTCFullYear();
  const month = date.getUTCMonth();
  const day = date.getUTCDate();
  const hours = date.getUTCHours();
  const minutes = date.getUTCMinutes();
  const seconds = date.getUTCSeconds();
  const ms = date.getUTCMilliseconds();

  // Get the offset in hours for the target timezone
  const offsetHours = TIME_ZONE_OFFSETS[timezone] || 0;

  // For Nepal Time, we need to handle the 45 minutes part separately
  let offsetMinutes = 0;
  if (timezone === 'NPT' || timezone === 'Asia/Kathmandu') {
    // Extract the fractional part of the hours (0.75 for Nepal)
    const fractionalHours = offsetHours % 1;
    // Convert to minutes (0.75 * 60 = 45 minutes)
    offsetMinutes = Math.round(fractionalHours * 60);
  }

  // Calculate the adjusted hours and handle day changes
  let adjustedHours = hours + Math.floor(offsetHours);
  let adjustedMinutes = minutes + offsetMinutes;
  let adjustedDay = day;

  // Handle minute overflow
  if (adjustedMinutes >= 60) {
    adjustedHours += 1;
    adjustedMinutes -= 60;
  }

  // Handle hour overflow and day changes
  if (adjustedHours >= 24) {
    adjustedDay += 1;
    adjustedHours -= 24;
  } else if (adjustedHours < 0) {
    adjustedDay -= 1;
    adjustedHours += 24;
  }

  // Create a new date with the adjusted values
  const adjustedDate = new Date(Date.UTC(
    year,
    month,
    adjustedDay,
    adjustedHours,
    adjustedMinutes,
    seconds,
    ms
  ));

  console.log(`Converted ${dateString} to ${adjustedDate.toISOString()} for timezone ${timezone}`);

  return adjustedDate;
};

/**
 * Format a date string according to the specified timezone
 * @param dateString - The date string to format
 * @param timezone - The timezone to use (from system settings)
 * @param options - Intl.DateTimeFormatOptions for formatting
 * @returns Formatted date string
 */
export const formatDate = (
  dateString: string,
  timezone: string = 'UTC',
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }
): string => {
  if (!dateString) return '';

  try {
    // Convert the date to the specified timezone
    const date = convertToTimezone(dateString, timezone);

    // Format the date
    const formatter = new Intl.DateTimeFormat('en-US', options);
    const formattedDate = formatter.format(date);

    console.log(`Formatted ${dateString} to ${formattedDate} for timezone ${timezone}`);

    return formattedDate;
  } catch (error) {
    console.error('Error formatting date:', error, 'Input was:', dateString);
    return 'Invalid date';
  }
};

/**
 * Format a date and time string according to the specified timezone
 * @param dateTimeString - The date and time string to format
 * @param timezone - The timezone to use (from system settings)
 * @returns Formatted date and time string
 */
export const formatDateTime = (
  dateTimeString: string,
  timezone: string = 'UTC'
): string => {
  if (!dateTimeString) return '';

  try {
    // Define options for date and time formatting
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    };

    const formattedDateTime = formatDate(dateTimeString, timezone, options);

    // Add timezone abbreviation to the formatted date
    const timezoneSuffix = timezone === 'UTC' ? 'UTC' :
                          timezone === 'Asia/Kathmandu' || timezone === 'NPT' ? 'NPT' :
                          timezone;

    return `${formattedDateTime} (${timezoneSuffix})`;
  } catch (error) {
    console.error('Error formatting date and time:', error, 'Input was:', dateTimeString);
    return 'Invalid date/time';
  }
};

/**
 * Format a date and time string according to the specified timezone
 * This is a wrapper around formatDateTime that is used by components
 * @param dateTimeString - The date and time string to format
 * @returns Formatted date and time string
 */
export const formatDateTimeWithTimezone = (
  dateTimeString: string
): string => {
  // Get the timezone from localStorage if available
  let timezone = 'UTC';
  try {
    const settingsStr = localStorage.getItem('systemSettings');
    if (settingsStr) {
      const settings = JSON.parse(settingsStr);
      if (settings && settings.timezone) {
        timezone = settings.timezone;
        console.log('Using timezone from localStorage:', timezone);
      }
    }
  } catch (error) {
    console.error('Error getting timezone from localStorage:', error);
  }

  return formatDateTime(dateTimeString, timezone);
};

/**
 * Get the current date in YYYY-MM-DD format
 * @returns Current date in YYYY-MM-DD format
 */
export const getCurrentDate = (): string => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, '0');
  const day = String(today.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
};

/**
 * Format a time string to a more readable format
 * @param timeString - The time string to format (HH:MM:SS or ISO date string)
 * @param timezone - The timezone to use (from system settings)
 * @returns Formatted time string (e.g., "2:30 PM")
 */
export const formatTime = (timeString: string, timezone: string = 'UTC'): string => {
  if (!timeString) return '';

  try {
    // If it's a full ISO date string, use the date formatting with time-only options
    if (timeString.includes('T') || timeString.includes(' ')) {
      const options: Intl.DateTimeFormatOptions = {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      };

      return formatDate(timeString, timezone, options);
    }

    // If it's just a time string (HH:MM:SS), parse and format it
    if (timeString.includes(':')) {
      const [hours, minutes] = timeString.split(':').map(Number);

      if (isNaN(hours) || isNaN(minutes)) {
        return 'Invalid time';
      }

      const ampm = hours >= 12 ? 'PM' : 'AM';
      const hour12 = hours % 12 || 12;
      return `${hour12}:${minutes.toString().padStart(2, '0')} ${ampm}`;
    }

    return 'Invalid time format';
  } catch (error) {
    console.error('Error formatting time:', error, 'Input was:', timeString);
    return 'Invalid time';
  }
};
