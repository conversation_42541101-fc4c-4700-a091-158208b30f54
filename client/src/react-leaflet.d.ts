declare module 'react-leaflet' {
  import { ReactNode } from 'react';
  import * as L from 'leaflet';

  export interface MapContainerProps {
    center: L.LatLngExpression;
    zoom: number;
    children?: ReactNode;
    style?: React.CSSProperties;
    className?: string;
  }

  export interface TileLayerProps {
    attribution: string;
    url: string;
    children?: ReactNode;
  }

  export interface CircleMarkerProps {
    center: L.LatLngExpression;
    radius: number;
    pathOptions?: L.PathOptions;
    children?: ReactNode;
  }

  export interface PopupProps {
    children?: ReactNode;
  }

  export interface MarkerProps {
    position: L.LatLngExpression;
    children?: ReactNode;
  }

  export const MapContainer: React.FC<MapContainerProps>;
  export const TileLayer: React.FC<TileLayerProps>;
  export const CircleMarker: React.FC<CircleMarkerProps>;
  export const Popup: React.FC<PopupProps>;
  export const Marker: React.FC<MarkerProps>;
}
