import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import { SystemSettingsProvider } from './context/SystemSettingsContext';
import { AdminLayoutProvider } from './context/AdminLayoutContext';
import Navbar from './components/layout/Navbar';
import Home from './components/pages/Home';
import Login from './components/auth/Login';
import ResetPassword from './components/auth/ResetPassword';
import ChangePassword from './components/auth/ChangePassword';
import Dashboard from './components/pages/Dashboard';
import AdminDashboard from './components/admin/AdminDashboard';
import EnhancedAdminDashboard from './components/admin/dashboard/EnhancedAdminDashboard';
import ClinicalGuidanceManagement from './components/admin/ClinicalGuidanceManagement';
import DoctorManagement from './components/admin/DoctorManagement';
import SecurityLogs from './components/admin/SecurityLogs';
import UserManagement from './components/admin/UserManagement';
import SystemStatistics from './components/admin/SystemStatistics';
import SystemSettings from './components/admin/SystemSettings';
import PasswordPolicySettings from './components/admin/PasswordPolicySettings';
import IpRestrictions from './components/admin/IpRestrictions';
import MfaManagement from './components/admin/MfaManagement';
import IntegrationDashboard from './components/admin/integrations/IntegrationDashboard';
import ApiManagementConsole from './components/admin/integrations/ApiManagementConsole';
import ExternalSystemConnectivity from './components/admin/integrations/ExternalSystemConnectivity';
import PatientManagement from './components/admin/PatientManagement';
import DatabaseDebug from './components/admin/DatabaseDebug';
import AdminRoute from './components/routing/AdminRoute';
import PatientList from './components/patients/PatientList';
import PatientDetail from './components/patients/PatientDetail';
import PatientForm from './components/patients/PatientForm';
import RecordForm from './components/records/RecordForm';
import VisitDetail from './components/visits/VisitDetail';
import VisitForm from './components/visits/VisitForm';
import PatientVisitList from './components/visits/PatientVisitList';
import KinPatientVisitList from './components/kin/KinPatientVisitList';
import KinVisitDetail from './components/kin/KinVisitDetail';
import KinMedicationList from './components/kin/KinMedicationList';
import KinAppointmentForm from './components/kin/KinAppointmentForm';
import PrivateRoute from './components/routing/PrivateRoute';
import NonKinRoute from './components/routing/NonKinRoute';
import PatientDashboardPage from './pages/PatientDashboardPage';
import KinDashboardPage from './pages/KinDashboardPage';
import AppointmentDashboard from './components/appointments/AppointmentDashboard';
import AppointmentForm from './components/appointments/AppointmentForm';
import AppointmentDetail from './components/appointments/AppointmentDetail';
import BeersCriteriaReference from './components/beers/BeersCriteriaReference';
import BeersCriteriaManagement from './components/beers/BeersCriteriaManagement';
import './App.css';
import './components/common/DarkModeOverrides.css';
import './AppDarkMode.css';
import './components/pages/LoginButtonFix.css';
import './components/doctors/DoctorDashboardStatCardFix.css';

const App: React.FC = () => {
  // Add console debugging
  if (process.env.NODE_ENV === 'development') {
    const oldConsoleLog = console.log;
    console.log = function(...args) {
      oldConsoleLog.apply(console, [`[${new Date().toISOString()}]`, ...args]);
    };
  }

  return (
    <AuthProvider>
      <SystemSettingsProvider>
        <AdminLayoutProvider>
          <Router>
            <div className="App">
              <a href="#main-content" className="skip-to-content">Skip to content</a>
              <Navbar />
              <div id="main-content" className="container">
              <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/login" element={<Login />} />
              <Route path="/reset-password" element={<ResetPassword />} />
              <Route
                path="/change-password"
                element={
                  <PrivateRoute exemptFromPasswordChange={true}>
                    <ChangePassword />
                  </PrivateRoute>
                }
              />
              <Route
                path="/dashboard"
                element={
                  <NonKinRoute>
                    <Dashboard />
                  </NonKinRoute>
                }
              />
              <Route
                path="/admin"
                element={
                  <AdminRoute>
                    <AdminDashboard />
                  </AdminRoute>
                }
              />
              <Route
                path="/admin_exp"
                element={
                  <AdminRoute>
                    <EnhancedAdminDashboard />
                  </AdminRoute>
                }
              />
              <Route
                path="/admin/users"
                element={
                  <AdminRoute>
                    <UserManagement />
                  </AdminRoute>
                }
              />
              <Route
                path="/admin/doctors"
                element={
                  <AdminRoute>
                    <DoctorManagement />
                  </AdminRoute>
                }
              />
              <Route
                path="/admin/security"
                element={
                  <AdminRoute>
                    <SecurityLogs />
                  </AdminRoute>
                }
              />
              <Route
                path="/admin/patients"
                element={
                  <AdminRoute>
                    <PatientManagement />
                  </AdminRoute>
                }
              />
              <Route
                path="/admin/statistics"
                element={
                  <AdminRoute>
                    <SystemStatistics />
                  </AdminRoute>
                }
              />
              <Route
                path="/admin/settings"
                element={
                  <AdminRoute>
                    <SystemSettings />
                  </AdminRoute>
                }
              />
              <Route
                path="/admin/password-policy"
                element={
                  <AdminRoute>
                    <PasswordPolicySettings />
                  </AdminRoute>
                }
              />
              <Route
                path="/admin/ip-restrictions"
                element={
                  <AdminRoute>
                    <IpRestrictions />
                  </AdminRoute>
                }
              />
              <Route
                path="/admin/mfa-management"
                element={
                  <AdminRoute>
                    <MfaManagement />
                  </AdminRoute>
                }
              />
              <Route
                path="/admin/database-debug"
                element={
                  <AdminRoute>
                    <DatabaseDebug />
                  </AdminRoute>
                }
              />
              <Route
                path="/admin/clinical-guidance"
                element={
                  <AdminRoute>
                    <ClinicalGuidanceManagement />
                  </AdminRoute>
                }
              />
              <Route
                path="/admin/integrations"
                element={
                  <AdminRoute>
                    <IntegrationDashboard />
                  </AdminRoute>
                }
              />
              <Route
                path="/admin/integrations/api-management"
                element={
                  <AdminRoute>
                    <ApiManagementConsole />
                  </AdminRoute>
                }
              />
              <Route
                path="/admin/integrations/external-systems"
                element={
                  <AdminRoute>
                    <ExternalSystemConnectivity />
                  </AdminRoute>
                }
              />
              <Route
                path="/patients"
                element={
                  <NonKinRoute>
                    <PatientList />
                  </NonKinRoute>
                }
              />
              <Route
                path="/patients/new"
                element={
                  <NonKinRoute>
                    <PatientForm />
                  </NonKinRoute>
                }
              />
              <Route
                path="/patients/safe-edit/:id"
                element={
                  <NonKinRoute>
                    <PatientForm />
                  </NonKinRoute>
                }
              />
              <Route
                path="/patients/:id"
                element={
                  <NonKinRoute>
                    <PatientDetail />
                  </NonKinRoute>
                }
              />
              <Route
                path="/records/new/:patientId"
                element={
                  <NonKinRoute>
                    <RecordForm />
                  </NonKinRoute>
                }
              />
              <Route
                path="/records/edit/:id"
                element={
                  <NonKinRoute>
                    <RecordForm />
                  </NonKinRoute>
                }
              />

              {/* Visit Routes */}
              <Route
                path="/patients/:patientId/visits"
                element={
                  <NonKinRoute>
                    <PatientVisitList />
                  </NonKinRoute>
                }
              />
              <Route
                path="/patients/:patientId/visits/new"
                element={
                  <NonKinRoute>
                    <VisitForm isEdit={false} />
                  </NonKinRoute>
                }
              />
              <Route
                path="/patients/:patientId/visits/:visitId"
                element={
                  <NonKinRoute>
                    <VisitDetail />
                  </NonKinRoute>
                }
              />
              <Route
                path="/patients/:patientId/visits/:visitId/edit"
                element={
                  <NonKinRoute>
                    <VisitForm isEdit={true} />
                  </NonKinRoute>
                }
              />

              {/* Patient Dashboard */}
              <Route
                path="/patient/dashboard"
                element={
                  <PrivateRoute>
                    <PatientDashboardPage />
                  </PrivateRoute>
                }
              />

              {/* Kin Dashboard */}
              <Route
                path="/kin/dashboard"
                element={
                  <PrivateRoute>
                    <KinDashboardPage />
                  </PrivateRoute>
                }
              />

              {/* Kin Visit Routes */}
              <Route
                path="/kin/patients/:patientId/visits"
                element={
                  <PrivateRoute>
                    <KinPatientVisitList />
                  </PrivateRoute>
                }
              />
              <Route
                path="/kin/patients/:patientId/visits/:visitId"
                element={
                  <PrivateRoute>
                    <KinVisitDetail />
                  </PrivateRoute>
                }
              />

              {/* Kin Medication Routes */}
              <Route
                path="/kin/patients/:patientId/medications"
                element={
                  <PrivateRoute>
                    <KinMedicationList />
                  </PrivateRoute>
                }
              />

              {/* Kin Appointment Routes */}
              <Route
                path="/kin/patients/:patientId/schedule-appointment"
                element={
                  <PrivateRoute>
                    <KinAppointmentForm />
                  </PrivateRoute>
                }
              />

              {/* Add appointment routes after the existing routes */}
              <Route
                path="/appointments"
                element={
                  <NonKinRoute>
                    <AppointmentDashboard />
                  </NonKinRoute>
                }
              />
              <Route
                path="/appointments/new"
                element={
                  <NonKinRoute>
                    <AppointmentForm />
                  </NonKinRoute>
                }
              />
              <Route
                path="/appointments/:id"
                element={
                  <NonKinRoute>
                    <AppointmentDetail />
                  </NonKinRoute>
                }
              />
              <Route
                path="/appointments/:id/edit"
                element={
                  <NonKinRoute>
                    <AppointmentForm />
                  </NonKinRoute>
                }
              />

              {/* BEERS Criteria Reference */}
              <Route
                path="/beers-criteria"
                element={
                  <NonKinRoute>
                    <BeersCriteriaReference />
                  </NonKinRoute>
                }
              />

              {/* BEERS Criteria Management */}
              <Route
                path="/beers-criteria/manage"
                element={
                  <PrivateRoute>
                    <BeersCriteriaManagement />
                  </PrivateRoute>
                }
              />

              <Route path="*" element={<Navigate to="/" />} />
            </Routes>
              </div>
            </div>
          </Router>
        </AdminLayoutProvider>
      </SystemSettingsProvider>
    </AuthProvider>
  );
};

export default App;
