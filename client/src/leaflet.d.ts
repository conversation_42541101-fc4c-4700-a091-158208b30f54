declare module 'leaflet' {
  export type LatLngExpression = LatLng | [number, number] | { lat: number; lng: number };

  export interface PathOptions {
    stroke?: boolean;
    color?: string;
    weight?: number;
    opacity?: number;
    lineCap?: string;
    lineJoin?: string;
    dashArray?: string | number[];
    dashOffset?: string;
    fill?: boolean;
    fillColor?: string;
    fillOpacity?: number;
    fillRule?: string;
    className?: string;
  }

  export class LatLng {
    constructor(lat: number, lng: number, alt?: number);
    lat: number;
    lng: number;
    alt?: number;
  }

  export class Icon {
    static Default: {
      prototype: {
        _getIconUrl?: string;
      };
      mergeOptions(options: any): void;
    };
  }
}
