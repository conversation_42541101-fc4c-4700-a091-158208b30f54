export interface Visit {
  visit_id: number;
  patient_id: number;
  doctor_id?: number;
  visit_date: string;
  visit_reason?: string;
  visit_type?: 'baseline' | 'visit'; // Added for distinguishing between patient baseline data and visit data

  // Vital Signs
  lying_bp_systolic?: number;
  lying_bp_diastolic?: number;
  sitting_bp_systolic?: number;
  sitting_bp_diastolic?: number;
  standing_bp_systolic?: number;
  standing_bp_diastolic?: number;
  lying_heart_rate?: number;
  sitting_heart_rate?: number; // Added sitting heart rate
  standing_heart_rate?: number;
  heart_rate?: number;
  heart_rhythm?: string;
  temperature?: number;
  body_temperature?: number; // Alternative field name
  respiratory_rate?: number;
  pulse_oximetry?: number;
  oxygen_saturation?: number; // Alternative field name

  // Lab Results: Metabolic Panel
  blood_glucose?: number;
  glucose?: number; // Alternative field name
  hba1c?: number;

  // Lab Results: Lipid Panel
  cholesterol_total?: number;
  total_cholesterol?: number; // Alternative field name
  hdl?: number;
  ldl?: number;
  hdl_cholesterol?: number;
  ldl_cholesterol?: number;
  triglycerides?: number;
  vldl?: number; // Added VLDL

  // Lab Results: Kidney Function
  creatinine?: number;
  egfr?: number;
  bun?: number;
  blood_urea_nitrogen?: number;
  urine_albumin_creatinine_ratio?: number;
  uric_acid?: number; // Added uric acid

  // Lab Results: Electrolytes
  sodium?: number;
  potassium?: number;
  chloride?: number;
  co2?: number;
  calcium?: number;
  magnesium?: number;
  phosphorus?: number;
  phosphate?: number; // Alternative field name

  // Lab Results: Liver Function Tests
  alt?: number;
  ast?: number;
  alp?: number;
  ggt?: number;
  bilirubin_t?: number;
  bilirubin_total?: number; // Alternative field name
  total_bilirubin?: number; // Alternative field name
  bilirubin_d?: number;
  bilirubin_direct?: number; // Alternative field name
  direct_bilirubin?: number; // Alternative field name
  albumin?: number;
  total_protein?: number; // Added total protein
  protein_total?: number; // Alternative field name

  // Lab Results: Bone Health Markers
  vitamin_d?: number;
  parathyroid_hormone?: number;
  alkaline_phosphatase_bone?: number;

  // Lab Results: Thyroid Function
  tsh?: number;
  free_t4?: number;
  free_t3?: number;

  // Lab Results: Inflammation Markers
  crp?: number;
  esr?: number;

  // Lab Results: Complete Blood Count
  wbc?: number;
  rbc?: number;
  hemoglobin?: number;
  hematocrit?: number;
  platelets?: number;
  platelet_count?: number; // Alternative field name

  // Lab Results: RBC Indices
  mcv?: number;
  mch?: number;
  mchc?: number;
  rdw?: number;

  // Lab Results: WBC Differential
  neutrophils?: number;
  lymphocytes?: number;
  monocytes?: number;
  eosinophils?: number;
  basophils?: number;

  // Lab Results: Iron Studies
  ferritin?: number;
  iron?: number;

  // Lab Results: Vitamin Status
  vitamin_b12?: number;
  folate?: number;

  // Lab Results: Cancer Screening
  cancer_screening_results?: string;
  ca125?: number; // Added CA-125 cancer marker
  ca_125?: number; // Alternative field name

  // Lab Results: Urinalysis
  urineColor?: string;
  urine_color?: string; // Alternative field name
  urineTransparency?: string;
  urine_transparency?: string; // Alternative field name
  urine_clarity?: string; // Alternative field name
  urinePh?: number;
  urine_ph?: number; // Alternative field name
  urineProtein?: string;
  urine_protein?: string; // Alternative field name
  urineSugar?: string;
  urine_sugar?: string; // Alternative field name
  urine_glucose?: string; // Alternative field name
  urineRbcs?: string;
  urine_rbcs?: string; // Alternative field name
  urinePusCells?: string;
  urine_pus_cells?: string; // Alternative field name
  urineCrystals?: string;
  urine_crystals?: string; // Alternative field name
  urineCasts?: string;
  urine_casts?: string; // Alternative field name

  // Medical Information
  current_medical_conditions?: string;
  current_medications?: string;
  medication_adherence?: string;
  medication_side_effects?: string;
  allergies?: string;
  medication_allergies?: string;
  pain_level?: number;
  pain_location?: string;
  pain_character?: string;
  safe_pain_medications?: string;

  // Health Assessment
  weight?: number;
  height?: number;
  bmi?: number;
  cognitive_status?: string;
  cognitive_test_results?: string;
  mental_health_assessment?: string;
  depression_score?: number;
  anxiety_score?: number;

  // Health Status - Functional Status
  activity_level?: string;
  fall_risk?: string;
  assistive_devices_used?: string;
  adl_status?: string;

  // Health Status - Nutrition & Hydration
  nutritional_status?: string;
  hydration_status?: string;
  dietary_restrictions?: string;

  // Health Status - Sleep & Environment
  sleep_duration?: number;
  sleep_disturbances?: string;
  age_friendly_environment?: string;
  living_conditions?: string;

  // Social & Environment Factors
  social_interaction_levels?: string;
  social_support_network?: string;
  transportation_access?: string;
  financial_concerns?: string;

  // Safety & Emergency
  emergency_contact_name?: string;
  emergency_contact_number?: string;
  emergency_contact_relationship?: string;
  fall_risk_assessment?: string;
  home_safety_evaluation?: string;

  // Vaccination Status
  influenza_vaccination_date?: string;
  pneumococcal_vaccination_date?: string;
  zoster_vaccination_date?: string;
  tdap_vaccination_date?: string;
  covid19_vaccination_date?: string;
  covid19_booster_date?: string;
  hepatitis_a_vaccination_date?: string;
  hepatitis_b_vaccination_date?: string;
  mmr_vaccination_date?: string;
  varicella_vaccination_date?: string;
  other_vaccinations?: string;

  // Notes and diagnosis
  notes?: string;
  diagnosis?: string;
  treatment_plan?: string;
  follow_up_instructions?: string;
  assessment_plan?: string;
  referrals?: string;
  medication_changes?: string;

  // Related entity info
  patient_first_name?: string;
  patient_last_name?: string;
  patient_unique_id?: string;
  doctor_first_name?: string;
  doctor_last_name?: string;
  doctor_specialty?: string;
  created_by_username?: string;

  // System fields
  created_by?: number;
  created_at: string;
  updated_at?: string;
  last_edited_by?: number;
  last_edited_by_username?: string;
}

export interface Doctor {
  doctor_id: number;
  first_name: string;
  last_name: string;
  specialty?: string;
  email?: string;
  phone?: string;
  created_at: string;
}

export interface Patient {
  patient_id: number;
  first_name: string;
  last_name: string;
  unique_id: string;
  date_of_birth: string;
  gender: string;
  phone: string;
  email: string;
  address: string;
  created_at: string;
  doctor_id: number;
  doctor_name?: string;
  doctor_specialty?: string;

  // Emergency contact fields
  emergency_contact_name?: string;
  emergency_contact_phone?: string;
  emergency_contact_relationship?: string;
  emergency_contact_updated?: boolean;

  // Vaccination fields - these might be in the patient record for first visit
  influenza_vaccination_date?: string;
  pneumococcal_vaccination_date?: string;
  zoster_vaccination_date?: string;
  tdap_vaccination_date?: string;
  covid19_vaccination_date?: string;
  covid19_booster_date?: string;
  hepatitis_a_vaccination_date?: string;
  hepatitis_b_vaccination_date?: string;
  mmr_vaccination_date?: string;
  varicella_vaccination_date?: string;
  other_vaccinations?: string;

  // Vital signs and health metrics
  height?: number;
  weight?: number;
  bmi?: number;
  lying_bp_systolic?: number;
  lying_bp_diastolic?: number;
  sitting_bp_systolic?: number;
  sitting_bp_diastolic?: number;
  standing_bp_systolic?: number;
  standing_bp_diastolic?: number;
  lying_heart_rate?: number;
  standing_heart_rate?: number;
  heart_rate?: number;
  heart_rhythm?: string;
  temperature?: number;
  body_temperature?: number;
  respiratory_rate?: number;
  pulse_oximetry?: number;
  blood_type?: string;

  // Lab Results: Metabolic Panel
  blood_glucose?: number;
  hba1c?: number;

  // Lab Results: Lipid Panel
  cholesterol_total?: number;
  cholesterol_levels?: number;
  hdl_cholesterol?: number;
  ldl_cholesterol?: number;
  triglycerides?: number;
  vldl?: number;

  // Lab Results: Kidney Function
  creatinine?: number;
  egfr?: number;
  blood_urea_nitrogen?: number;
  uric_acid?: number;
  urine_albumin_creatinine_ratio?: number;

  // Lab Results: Electrolytes
  sodium?: number;
  potassium?: number;
  calcium?: number;
  magnesium?: number;
  phosphorus?: number;
  chloride?: number;

  // Lab Results: Liver Function Tests
  alt?: number;
  ast?: number;
  alp?: number;
  ggt?: number;
  bilirubin_t?: number;
  bilirubin_d?: number;
  albumin?: number;
  sgpt?: number;
  sgot?: number;
  alkaline_phosphatase?: number;

  // Lab Results: Complete Blood Count
  hemoglobin?: number;
  hematocrit?: number;
  rbc?: number;
  wbc?: number;
  platelets?: number;
  mcv?: number;
  mch?: number;
  mchc?: number;
  rdw?: number;
  neutrophils?: number;
  lymphocytes?: number;
  monocytes?: number;
  eosinophils?: number;
  basophils?: number;

  // Lab Results: Iron Studies
  ferritin?: number;
  iron?: number;

  // Lab Results: Vitamin Status
  vitamin_b12?: number;
  vitamin_d?: number;
  folate?: number;

  // Lab Results: Thyroid Function
  tsh?: number;
  t4?: number;
  t3?: number;
  free_t4?: number;
  free_t3?: number;

  // Lab Results: Inflammatory Markers
  crp?: number;
  esr?: number;

  // Lab Results: Urinalysis
  urine_color?: string;
  urine_transparency?: string;
  urine_ph?: string;
  urine_sugar?: string;
  urine_pus_cells?: string;
  urine_rbcs?: string;
  urine_epithelial_cells?: string;
  urine_crystals?: string;
  urine_casts?: string;
  urine_protein?: string;

  // Lab Results: Cancer Screening
  psa?: number;
  ca125?: number;
  cancer_screening_results?: string;

  // Medication
  medication_adherence?: number | string;
  medication_side_effects?: string;
  pill_burden?: number;

  // Physical Activity
  daily_activity_levels?: number | string;
  fall_detection_incidents?: number;
  mobility_aids_used?: string;
  calf_circumference?: number;

  // Pain Assessment
  pain_level?: string | number;
  pain_location?: string;
  pain_character?: string;
  safe_pain_medications?: string | boolean;
  pain_levels_locations?: string;

  // Nutrition & Hydration
  dietary_intake_quality?: string;
  hydration_levels?: number | string;
  vitamin_mineral_levels?: string;

  // Sleep
  sleep_initiation_difficulties?: boolean;
  sleep_quality_duration?: number | string;

  // Sensory
  vision_status?: string;
  hearing_status?: string;
  use_of_aids?: string;

  // Social & Environmental
  social_interaction_levels?: string;
  living_conditions?: string;
  age_friendly_environment?: boolean;

  // Safety & Emergency
  sos_alerts?: boolean;

  // Preventive Care
  health_checkup_adherence?: number | string;
  vaccination_updated?: boolean;

  // Additional Health Concerns
  urinary_bowel_issues?: string;
  substance_abuse?: boolean;

  // Cognitive & Mental Health
  cognitive_test_results?: string;
  cognitive_impairment_score?: string;
  mental_health_assessment?: string;
  depression_screening?: string;
  anxiety_screening?: string;
  depression_score?: string;
  anxiety_score?: string;

  // Mini-Cog Assessment
  mini_cog_word_recall_score?: string;
  mini_cog_clock_drawing_score?: string;
  mini_cog_words_used?: string;
  mini_cog_words_recalled?: string;
  mini_cog_notes?: string;

  // PHQ-9 Depression Assessment
  phq9_interest_pleasure?: string;
  phq9_feeling_down?: string;
  phq9_sleep_issues?: string;
  phq9_tired?: string;
  phq9_appetite?: string;
  phq9_feeling_bad?: string;
  phq9_concentration?: string;
  phq9_moving_speaking?: string;
  phq9_thoughts_hurting?: string;
  phq9_difficulty_level?: string;
  phq9_notes?: string;

  // GAD-7 Anxiety Assessment
  gad7_feeling_nervous?: string;
  gad7_stop_worrying?: string;
  gad7_worrying_much?: string;
  gad7_trouble_relaxing?: string;
  gad7_restless?: string;
  gad7_annoyed?: string;
  gad7_feeling_afraid?: string;
  gad7_difficulty_level?: string;
  gad7_notes?: string;

  // Tracking fields
  last_edited_by?: number;
  last_edited_at?: string;
  last_edited_by_username?: string;
}

export interface Appointment {
  appointment_id: number;
  patient_id: number;
  doctor_id: number;
  title: string;
  date: string;
  start_time: string;
  end_time: string;
  status: 'scheduled' | 'completed' | 'cancelled' | 'no-show';
  type: 'checkup' | 'follow-up' | 'urgent' | 'consultation' | 'other';
  notes?: string;
  reason?: string;

  // Related entity info
  patient_first_name?: string;
  patient_last_name?: string;
  patient_unique_id?: string;
  patient_name?: string;
  doctor_first_name?: string;
  doctor_last_name?: string;
  doctor_specialty?: string;
  doctor_name?: string;

  // System fields
  created_by?: number;
  created_at: string;
  updated_at?: string;
}