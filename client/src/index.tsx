import React from 'react'; // useEffect is unused
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import reportWebVitals from './reportWebVitals';
import '@fortawesome/fontawesome-free/css/all.min.css';
import { ThemeProvider as MuiThemeProvider } from '@mui/material/styles';
import { lightTheme, darkTheme } from './theme';
import './theme/custom-components.css';
import { ThemeProvider } from './context/ThemeContext';
import { useContext } from 'react';
import ThemeContext from './context/ThemeContext';

// Add keyboard navigation detection
const detectKeyboardNavigation = () => {
  // Add class to body when mouse is used
  document.body.addEventListener('mousedown', () => {
    document.body.classList.add('using-mouse');
  });

  // Remove class from body when keyboard is used
  document.body.addEventListener('keydown', (event) => {
    if (event.key === 'Tab') {
      document.body.classList.remove('using-mouse');
    }
  });
};

// Execute the detection function
detectKeyboardNavigation();

// Create a component that will select the appropriate theme based on the theme context
const ThemedApp = () => {
  const { mode } = useContext(ThemeContext);

  return (
    <MuiThemeProvider theme={mode === 'dark' ? darkTheme : lightTheme}>
      <App />
    </MuiThemeProvider>
  );
};

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(
  <React.StrictMode>
    <ThemeProvider>
      <ThemedApp />
    </ThemeProvider>
  </React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
