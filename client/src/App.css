/* Global Styles - Using logo colors */
:root {
  /* Logo Colors */
  --primary-color: #2C4B2B; /* Dark Green */
  --secondary-color: #A1A43A; /* Olive Green */
  --warning-color: #F6B21A; /* Yellow-Orange */
  --accent-color: #D97B3A; /* Orange */
  --light-accent-color: #F2A65A; /* Light Orange */

  /* Derived Colors */
  --dark-color: #1E3A1D; /* Darker shade of primary */
  --light-color: #F8FAF8; /* Very light green tint */
  --danger-color: #D97B3A; /* Orange */
  --success-color: #2C4B2B; /* Dark Green */

  /* RGB Values for rgba usage */
  --primary-rgb: 44, 75, 43; /* Dark Green */
  --secondary-rgb: 161, 164, 58; /* Olive Green */
  --warning-rgb: 246, 178, 26; /* Yellow-Orange */
  --accent-rgb: 217, 123, 58; /* Orange */
  --light-accent-rgb: 242, 166, 90; /* Light Orange */

  /* Light Theme Colors */
  --bg-color: #fff;
  --text-color: #333;
  --text-secondary-color: #666;
  --card-bg-color: #fff;
  --card-header-bg: #f8fafc;
  --border-color: rgba(0, 0, 0, 0.1);
  --shadow-color: rgba(0, 0, 0, 0.05);
  --hover-bg-color: rgba(0, 0, 0, 0.05);
  --input-focus-bg-color: #fff;
  --placeholder-color: #94a3b8;
  --primary-color-light: rgba(44, 75, 43, 0.1);
  --primary-color-lightest: rgba(44, 75, 43, 0.05);
  --button-secondary-bg: #f1f5f9;
  --button-secondary-hover-bg: #e2e8f0;
  --error-bg-color: #FCF1E9;
  --error-color-light: rgba(217, 123, 58, 0.2);
}

/* Dark Theme Colors */
.dark-mode {
  --bg-color: #121212;
  --text-color: #fff;
  --text-secondary-color: #aaa;
  --card-bg-color: #1e1e1e; /* Match the color used in styled components */
  --card-header-bg: #1e1e1e; /* Match card background for consistency */
  --border-color: rgba(255, 255, 255, 0.1);
  --shadow-color: rgba(0, 0, 0, 0.2);
  --hover-bg-color: rgba(255, 255, 255, 0.05);
  --input-focus-bg-color: #2c2c2c;
  --placeholder-color: #666;
  --primary-color-light: rgba(44, 75, 43, 0.3);
  --primary-color-lightest: rgba(44, 75, 43, 0.15);
  --button-secondary-bg: #2c2c2c;
  --button-secondary-hover-bg: #3a3a3a;
  --error-bg-color: rgba(217, 123, 58, 0.2);
  --error-color-light: rgba(217, 123, 58, 0.3);
  --primary-color: #90caf9; /* Add primary color for dark mode */
}

/* Ensure body background changes in dark mode */
.dark-mode body,
.dark-mode #root,
.dark-mode .modern-landing,
.dark-mode section {
  background-color: #121212 !important;
  color: #fff !important;
}

/* Force dark mode for landing page */
.dark-mode .modern-landing {
  background-color: #121212 !important;
}

.dark-mode .landing-content {
  background-color: transparent !important;
}

.dark-mode .landing-left {
  background-color: transparent !important;
}

/* Force dark mode for container */
.dark-mode .container {
  background-color: #121212 !important;
}

/* Force dark mode for App */
.dark-mode .App {
  background-color: #121212 !important;
}

/* Force dark mode for html */
html.dark-mode {
  background-color: #121212 !important;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Roboto', sans-serif;
  font-size: 1rem;
  line-height: 1.6;
  background-color: var(--bg-color);
  color: var(--text-color);
  padding-top: 4rem;
  transition: background-color 0.3s ease, color 0.3s ease;
}

a {
  color: var(--primary-color);
  text-decoration: none;
}

a:hover {
  color: #000;
}

ul {
  list-style: none;
}

img {
  width: 100%;
}

/* Container */
.container {
  max-width: 1100px;
  margin: auto;
  overflow: hidden;
  padding: 0 2rem;
  margin-top: 6rem;
  margin-bottom: 3rem;
}

/* Text Styles */
.x-large {
  font-size: 4rem;
  line-height: 1.2;
  margin-bottom: 1rem;
}

.large {
  font-size: 3rem;
  line-height: 1.2;
  margin-bottom: 1rem;
}

.lead {
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.text-primary {
  color: var(--primary-color);
}

.text-dark {
  color: var(--dark-color);
}

.text-success {
  color: var(--success-color);
}

.text-danger {
  color: var(--danger-color);
}

.text-center {
  text-align: center;
}

.my-1 {
  margin: 1rem 0;
}

.my-2 {
  margin: 2rem 0;
}

.p-1 {
  padding: 1rem;
}

.p-2 {
  padding: 2rem;
}

/* Grid */
.grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 25px;
}

.grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 25px;
}

/* Navbar */
.navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 2rem;
  height: 64px;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.navbar-brand a {
  color: #fff;
  font-size: 1.5rem;
  font-weight: 700;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
  height: 64px;
  transition: all 0.2s ease;
}

.navbar-brand a:hover {
  transform: translateY(-1px);
  text-shadow: 0 2px 10px rgba(255, 255, 255, 0.2);
}

.navbar-brand a i {
  margin-right: 0.75rem;
  font-size: 1.6rem;
  color: rgba(255, 255, 255, 0.95);
}

.navbar-nav {
  display: flex;
  align-items: center;
  height: 100%;
}

.nav-item {
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
}

.nav-item a {
  color: rgba(255, 255, 255, 0.9);
  padding: 0 1.2rem;
  height: 100%;
  display: flex;
  align-items: center;
  font-weight: 500;
  transition: all 0.2s ease;
  position: relative;
  letter-spacing: 0.01em;
}

.nav-item.active a {
  color: #fff;
  font-weight: 600;
}

.nav-item.active a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.nav-item a:hover {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.1);
}

.nav-item i {
  margin-right: 0.5rem;
  font-size: 1rem;
}

.nav-item.logout {
  margin-left: 0.5rem;
}

.nav-item.logout a {
  color: rgba(255, 255, 255, 0.8);
}

.nav-item.logout a:hover {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.15);
}

.nav-item.theme-toggle {
  margin-left: 0.5rem;
}

.nav-item.theme-toggle a {
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.nav-item.theme-toggle a:hover {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.15);
}

.nav-item.theme-toggle i {
  font-size: 1.1rem;
}

.user-info {
  margin-left: 1.5rem;
  color: #fff;
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: rgba(0, 0, 0, 0.1);
  height: 70%;
  border-radius: 8px;
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.user-info:hover {
  background-color: rgba(var(--light-accent-rgb), 0.15);
  transform: translateY(-1px);
}

.user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: var(--light-accent-color);
  color: var(--dark-color);
  margin-right: 0.75rem;
}

.user-avatar i {
  font-size: 1.1rem;
  color: var(--dark-color);
}

.user-details {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 600;
  font-size: 0.9rem;
  line-height: 1.2;
  white-space: nowrap;
}

.loading-placeholder {
  display: flex;
  align-items: center;
  opacity: 0.8;
  animation: pulse 1.5s infinite ease-in-out;
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

.user-role {
  font-size: 0.7rem;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

@media (max-width: 768px) {
  .navbar {
    padding: 0 1rem;
    height: 60px;
  }

  .navbar-brand a {
    font-size: 1.3rem;
    height: 60px;
  }

  .hide-sm {
    display: none;
  }

  .nav-item {
    padding: 0;
  }

  .nav-item a {
    padding: 0 0.8rem;
  }

  .user-info {
    margin-left: 0.75rem;
    padding: 0.4rem 0.75rem;
  }

  .user-avatar {
    width: 28px;
    height: 28px;
    margin-right: 0.5rem;
  }

  .user-name {
    font-size: 0.8rem;
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

/* Alert */
.alert {
  padding: 0.8rem;
  margin: 1rem 0;
  opacity: 0.9;
  background: var(--light-color);
  color: #333;
}

.alert-primary {
  background: var(--primary-color);
  color: #fff;
}

.alert-dark {
  background: var(--dark-color);
  color: #fff;
}

.alert-success {
  background: var(--success-color);
  color: #fff;
}

.alert-danger {
  background: var(--danger-color);
  color: #fff;
}

/* Background */
.bg-primary {
  background: var(--primary-color);
  color: #fff;
}

.bg-light {
  background: var(--light-color);
  color: #333;
  border: #ccc 1px solid;
}

.bg-dark {
  background: var(--dark-color);
  color: #fff;
}

.bg-success {
  background: var(--success-color);
  color: #fff;
}

.bg-danger {
  background: var(--danger-color);
  color: #fff;
}

.bg-white {
  background: #fff;
  color: #333;
  border: #ccc 1px solid;
}

/* Forms */
.form-container {
  max-width: 500px;
  margin: 2rem auto;
  overflow: hidden;
  padding: 0 2rem;
}

.form {
  margin: 1.2rem 0;
}

.form .form-group {
  margin: 1.2rem 0;
}

.form .form-text {
  display: block;
  margin-top: 0.3rem;
  color: #888;
}

.form input[type='text'],
.form input[type='email'],
.form input[type='password'],
.form input[type='date'],
.form select,
.form textarea {
  display: block;
  width: 100%;
  padding: 0.4rem;
  font-size: 1.2rem;
  border: 1px solid #ccc;
}

.form input[type='submit'] {
  font: inherit;
}

.form label {
  display: block;
  margin-bottom: 0.3rem;
}

.form .social-input {
  display: flex;
}

.form .social-input i {
  padding: 0.5rem;
  width: 4rem;
}

.form .social-input i.fa-twitter {
  color: #38a1f3;
}

.form .social-input i.fa-facebook {
  color: #3b5998;
}

.form .social-input i.fa-instagram {
  color: #3f729b;
}

.form .social-input i.fa-youtube {
  color: #c4302b;
}

.form .social-input i.fa-linkedin {
  color: #0077b5;
}

/* Buttons */
.btn {
  display: inline-block;
  background: var(--light-color);
  color: #333;
  padding: 0.4rem 1.3rem;
  font-size: 1rem;
  border: none;
  cursor: pointer;
  margin-right: 0.5rem;
  transition: opacity 0.2s ease-in;
  outline: none;
}

.btn-sm {
  padding: 0.2rem 0.8rem;
  font-size: 0.8rem;
}

.btn-link {
  background: none;
  padding: 0;
  margin: 0;
}

.btn-primary {
  background: var(--primary-color);
  color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(var(--primary-rgb), 0.2);
}

.dark-mode .btn-primary {
  box-shadow: 0 2px 4px rgba(var(--primary-rgb), 0.4);
}

.btn-secondary {
  background: var(--secondary-color);
  color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(var(--secondary-rgb), 0.2);
}

.btn-light {
  background: var(--light-color);
  color: var(--dark-color);
  border-radius: 6px;
  border: 1px solid rgba(var(--primary-rgb), 0.1);
}

.dark-mode .btn-light {
  background: var(--card-bg-color);
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.btn-dark {
  background: var(--dark-color);
  color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.btn-danger {
  background: var(--danger-color);
  color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(var(--accent-rgb), 0.2);
}

.btn-success {
  background: var(--success-color);
  color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(var(--primary-rgb), 0.2);
}

.btn-warning {
  background: var(--warning-color);
  color: var(--dark-color);
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(var(--warning-rgb), 0.2);
}

.btn-accent {
  background: var(--accent-color);
  color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(var(--accent-rgb), 0.2);
}

.btn:hover {
  opacity: 0.9;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Tables */
.table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

.table th,
.table td {
  padding: 0.75rem;
  border-bottom: 1px solid #ccc;
  text-align: left;
}

.table th {
  background: var(--light-color);
}

/* Landing Page styles moved to Landing.css */

/* Dashboard */
.dash-buttons {
  display: flex;
  margin-top: 1rem;
}

.dash-buttons .btn {
  margin-right: 0.5rem;
}

.dash-container {
  margin: 2rem 0;
}

.dash-item {
  margin-bottom: 2rem;
}

/* Patient Detail */
.patient-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 1.5rem 0;
}

.patient-info {
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 5px;
}

.info-group {
  margin-bottom: 0.8rem;
}

.records-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.record-item {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 5px;
  border-left: 4px solid var(--primary-color);
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.8rem;
}

.record-date {
  font-weight: bold;
  color: var(--primary-color);
}

.record-field {
  margin-bottom: 0.5rem;
}

.record-meta {
  margin-top: 0.5rem;
  font-size: 0.9rem;
  color: #666;
}

/* Search */
.search-container {
  position: relative;
  margin-bottom: 1.5rem;
}

.search-input {
  width: 100%;
  padding: 10px 40px 10px 15px;
  border-radius: 4px;
  border: 1px solid #ddd;
  font-size: 1rem;
}

.search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #888;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(17, 82, 147, 0.2);
}

.alert-info {
  background: #e8f4fd;
  color: #0c5f9e;
  padding: 0.8rem;
  border-radius: 5px;
  margin-bottom: 1rem;
  border-left: 4px solid #0c5f9e;
}

/* Mobile Styles */
@media (max-width: 700px) {
  .hide-sm {
    display: none;
  }

  /* Text Styles */
  .x-large {
    font-size: 3rem;
  }

  .large {
    font-size: 2rem;
  }

  .lead {
    font-size: 1rem;
  }

  /* Navbar */
  .navbar {
    display: block;
    text-align: center;
    padding: 0.5rem;
  }

  .navbar ul {
    text-align: center;
    justify-content: center;
  }

  /* Grid */
  .grid-2,
  .grid-3 {
    grid-template-columns: 1fr;
  }

  /* Actions */
  .actions {
    margin-top: 1rem;
  }
}

/* Tabs System */
.tabs {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 1rem;
  border-bottom: 1px solid rgba(var(--primary-rgb), 0.1);
}

.tab-btn {
  background: var(--light-color);
  border: none;
  padding: 0.6rem 1.2rem;
  margin-right: 0.3rem;
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--dark-color);
  transition: all 0.2s ease;
  border: 1px solid transparent;
  border-bottom: none;
}

.tab-btn:hover {
  background: rgba(var(--secondary-rgb), 0.1);
}

.tab-btn.active {
  background: #fff;
  color: var(--primary-color);
  border-color: rgba(var(--primary-rgb), 0.1);
  border-bottom: 2px solid #fff;
  position: relative;
}

.tab-btn.active::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--primary-color);
  border-radius: 3px 3px 0 0;
}

.tab-content {
  display: none;
  padding: 1rem 0;
}

.tab-content.active {
  display: block;
}

.form-grid-2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.form-grid-3 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

/* Patient Form Sections */
.tab-content h2 {
  color: var(--primary-color);
  margin-bottom: 1rem;
  font-size: 1.3rem;
  border-bottom: 1px solid #f1f1f1;
  padding-bottom: 0.5rem;
}

.tab-content h3 {
  color: var(--dark-color);
  margin: 1.5rem 0 0.5rem 0;
  font-size: 1.1rem;
}

@media (max-width: 768px) {
  .form-grid-2,
  .form-grid-3 {
    grid-template-columns: 1fr;
  }

  .tabs {
    flex-direction: column;
    border-bottom: none;
  }

  .tab-btn {
    margin-bottom: 0.2rem;
    border-radius: 5px;
  }
}

/* Patient Detail Page Group Styling */
.patient-id {
  font-size: 0.9rem;
  color: #666;
  margin-left: 10px;
  font-weight: normal;
}

.vitals-group,
.lab-group,
.health-group,
.social-group {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  border: 1px solid #f0f0f0;
}

.vitals-group h3,
.lab-group h3,
.health-group h3,
.social-group h3 {
  color: var(--primary-color);
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.1rem;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Redesigned info groups */
.info-group {
  margin-bottom: 15px;
  display: flex;
  flex-direction: column;
}

.info-group strong {
  font-size: 0.9rem;
  color: #6c757d;
  margin-bottom: 5px;
  font-weight: 500;
}

.grid-2,
.grid-3 {
  gap: 20px;
}

.medical-records {
  margin-top: 20px;
}

.records-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.record-item {
  border: 1px solid rgba(var(--primary-rgb), 0.1);
  border-radius: 10px;
  padding: 18px;
  margin-bottom: 18px;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
  transition: all 0.2s ease;
  border-left: 3px solid var(--primary-color);
}

.record-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.record-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  border-bottom: 1px solid rgba(var(--primary-rgb), 0.08);
  padding-bottom: 12px;
}

.record-date {
  color: var(--primary-color);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.record-date i {
  color: var(--secondary-color);
  font-size: 0.9rem;
}

/* Patient List Header */
.patient-list-header {
  margin-bottom: 2rem;
  background-color: var(--card-bg-color, #fff);
  border-radius: 12px;
  box-shadow: 0 2px 10px var(--shadow-color, rgba(0, 0, 0, 0.05));
  padding: 1.5rem;
  border: 1px solid var(--border-color, rgba(0, 0, 0, 0.05));
  transition: all 0.3s ease;
}

.patient-list-header:hover {
  box-shadow: 0 4px 15px var(--shadow-color, rgba(0, 0, 0, 0.08));
}

.patient-header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.patient-list-title {
  display: flex;
  align-items: center;
  gap: 1.25rem;
}

.patient-list-title h1 {
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--text-color, #333);
  margin: 0;
  letter-spacing: -0.02em;
  position: relative;
}

.patient-list-title h1::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

.patient-count-badge {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(var(--primary-rgb), 0.1);
  color: var(--primary-color);
  border-radius: 10px;
  padding: 0.4rem 1rem;
  font-weight: 600;
  font-size: 1.2rem;
  line-height: 1.2;
  border: 1px solid rgba(var(--primary-rgb), 0.15);
  box-shadow: 0 2px 6px rgba(var(--primary-rgb), 0.08);
  transition: all 0.3s ease;
}

.patient-count-badge:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(var(--primary-rgb), 0.12);
}

.count-label {
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  opacity: 0.8;
  background-color: rgba(var(--primary-rgb), 0.2);
  padding: 0.2rem 0.5rem;
  border-radius: 4px;
  margin-top: 0.2rem;
}

.patient-actions-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.add-patient-btn {
  white-space: nowrap;
  padding: 0.6rem 1.2rem;
  border-radius: 8px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: var(--accent-color);
  color: white;
  box-shadow: 0 2px 6px rgba(var(--accent-rgb), 0.3);
  transition: all 0.2s ease;
}

.add-patient-btn:hover {
  background-color: var(--light-accent-color);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(var(--accent-rgb), 0.4);
}

.filter-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #495057;
}

/* Search Container */
.search-container {
  position: relative;
  width: 100%;
  margin-top: 1.25rem;
  margin-bottom: 1.5rem;
}

.search-input {
  width: 100%;
  padding: 1rem 3rem 1rem 3rem;
  border: 2px solid var(--border-color, rgba(var(--primary-rgb), 0.15));
  border-radius: 12px;
  font-size: 1rem;
  background-color: var(--bg-color, #fff);
  color: var(--text-color, #333);
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px var(--shadow-color, rgba(0, 0, 0, 0.03));
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.12);
  background-color: var(--input-focus-bg-color, #fff);
}

.search-icon-left {
  position: absolute;
  left: 1.2rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary-color, var(--secondary-color));
  opacity: 0.8;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.search-input:focus + .search-icon-left {
  color: var(--primary-color);
  opacity: 1;
}

.search-input::placeholder {
  color: var(--placeholder-color, #aaa);
  opacity: 0.8;
}

.search-clear-btn {
  position: absolute;
  right: 1.2rem;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(var(--accent-rgb), 0.1);
  border: none;
  color: var(--accent-color);
  cursor: pointer;
  padding: 0.4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  transition: all 0.3s ease;
}

.search-clear-btn:hover {
  background: rgba(var(--accent-rgb), 0.2);
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 2px 4px rgba(var(--accent-rgb), 0.2);
}

/* Patient Cards */
.patient-cards-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.75rem;
  margin-top: 1.75rem;
}

.patient-card {
  background-color: var(--card-bg-color, #fff);
  border-radius: 8px;
  box-shadow: 0 2px 8px var(--shadow-color, rgba(0, 0, 0, 0.05));
  overflow: hidden;
  transition: all 0.2s ease;
  border: 1px solid var(--border-color, rgba(0, 0, 0, 0.06));
  position: relative;
}

.patient-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 3px;
  height: 100%;
  background-color: var(--primary-color);
  opacity: 0.7;
}

.patient-card.female::before {
  background-color: var(--accent-color);
}

.patient-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px var(--shadow-color, rgba(0, 0, 0, 0.08));
}

.patient-card-header {
  padding: 1.25rem;
  border-bottom: 1px solid var(--border-color, rgba(0, 0, 0, 0.06));
  background-color: var(--card-header-bg, rgba(var(--primary-rgb), 0.02));
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.patient-name-container {
  display: flex;
  flex-direction: column;
}

.patient-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-color, var(--primary-color));
  text-decoration: none;
  margin-bottom: 0.25rem;
  transition: all 0.2s ease;
}

.patient-name:hover {
  color: var(--primary-color);
}

.patient-id {
  font-size: 0.75rem;
  color: var(--text-secondary-color, #6c757d);
  display: inline-flex;
  align-items: center;
}

.patient-badges {
  display: flex;
  gap: 0.5rem;
}

.patient-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.patient-age {
  background-color: rgba(var(--secondary-rgb), 0.1);
  color: var(--secondary-color);
}

.patient-gender {
  background-color: rgba(var(--accent-rgb), 0.1);
  color: var(--accent-color);
}

.patient-badge i {
  margin-right: 0.4rem;
  font-size: 0.75rem;
}

.patient-card-body {
  padding: 1.25rem;
  background-color: var(--card-bg-color, #fff);
}

.patient-info-item {
  display: flex;
  margin-bottom: 0.75rem;
  align-items: center;
  padding: 0.5rem 0;
  transition: all 0.2s ease;
  border-bottom: 1px solid var(--border-color, rgba(0, 0, 0, 0.03));
}

.patient-info-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.info-label {
  width: 80px;
  font-size: 0.8rem;
  color: var(--text-secondary-color, #6c757d);
  font-weight: 500;
  display: flex;
  align-items: center;
}

.info-label i {
  margin-right: 0.5rem;
  width: 16px;
  text-align: center;
  color: var(--primary-color);
  font-size: 0.85rem;
}

.info-value {
  flex: 1;
  font-size: 0.9rem;
  color: var(--text-color, #333);
  font-weight: 500;
}

.patient-card-footer {
  display: flex;
  border-top: 1px solid var(--border-color, rgba(0, 0, 0, 0.06));
  background-color: var(--card-bg-color, #fff);
}

.card-action-button {
  flex: 1;
  padding: 0.75rem 0;
  text-align: center;
  color: var(--text-secondary-color, #6c757d);
  font-size: 0.8rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.4rem;
}

.card-action-button:not(:last-child) {
  border-right: 1px solid var(--border-color, rgba(0, 0, 0, 0.06));
}

.card-action-button i {
  font-size: 0.75rem;
}

.view-button:hover {
  background-color: rgba(var(--primary-rgb), 0.05);
  color: var(--primary-color);
}

.edit-button:hover {
  background-color: rgba(var(--secondary-rgb), 0.05);
  color: var(--secondary-color);
}

.visit-button:hover {
  background-color: rgba(var(--accent-rgb), 0.05);
  color: var(--accent-color);
}

/* Search */
.search-container {
  position: relative;
  min-width: 300px;
  margin-bottom: 0;
}

.search-input {
  width: 100%;
  padding: 10px 40px 10px 15px;
  border-radius: 6px;
  border: 1px solid #e0e0e0;
  font-size: 0.9rem;
  background-color: #f9f9f9;
  transition: all 0.2s ease;
}

.search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #888;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
  background-color: #fff;
}

.filter-container {
  display: flex;
  align-items: center;
}

.alert-info {
  background: #e8f4fd;
  color: #0c5f9e;
  padding: 0.8rem;
  border-radius: 5px;
  margin-bottom: 1rem;
  border-left: 4px solid #0c5f9e;
}

/* Mobile Styles */
@media (max-width: 700px) {
  .hide-sm {
    display: none;
  }

  /* Text Styles */
  .x-large {
    font-size: 3rem;
  }

  .large {
    font-size: 2rem;
  }

  .lead {
    font-size: 1rem;
  }

  /* Navbar */
  .navbar {
    display: block;
    text-align: center;
    padding: 0.5rem;
  }

  .navbar ul {
    text-align: center;
    justify-content: center;
  }

  /* Grid */
  .grid-2,
  .grid-3 {
    grid-template-columns: 1fr;
  }

  /* Patient List Header */
  .patient-list-header {
    margin-bottom: 1.5rem;
    padding: 1.25rem;
  }

  .patient-header-top {
    flex-direction: column;
    align-items: flex-start;
    gap: 1.25rem;
  }

  .patient-list-title {
    width: 100%;
    justify-content: space-between;
  }

  .patient-list-title h1 {
    font-size: 1.5rem;
  }

  .patient-list-title h1::after {
    width: 30px;
    height: 2px;
    bottom: -6px;
  }

  .patient-actions-right {
    flex-direction: column;
    width: 100%;
    gap: 1rem;
  }

  .search-container {
    width: 100%;
  }

  .search-input {
    padding: 0.9rem 2.8rem;
    font-size: 0.95rem;
  }

  .filter-container {
    width: 100%;
    justify-content: center;
  }

  .add-patient-btn {
    width: 100%;
    justify-content: center;
    padding: 0.8rem 1rem;
  }

  /* Patient Cards Responsive */
  .patient-cards-container {
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-top: 1rem;
  }

  .patient-card {
    box-shadow: 0 2px 6px var(--shadow-color, rgba(0, 0, 0, 0.05));
  }

  .patient-card-header {
    padding: 1rem;
  }

  .patient-badges {
    display: flex;
    flex-wrap: wrap;
  }

  .patient-card-body {
    padding: 1rem;
  }

  .patient-info-item {
    display: grid;
    grid-template-columns: 80px 1fr;
    align-items: center;
    gap: 0.5rem;
  }

  .info-label {
    width: auto;
  }

  .info-value {
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .card-action-button {
    padding: 0.6rem 0;
    font-size: 0.8rem;
  }

  /* Actions */
  .actions {
    margin-top: 1rem;
  }
}

/* Modern Patient Detail Redesign */
.modern-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.header-content {
  max-width: 70%;
}

.back-link {
  display: inline-block;
  color: #6c757d;
  margin-bottom: 10px;
  font-size: 0.9rem;
  text-decoration: none;
}

.back-link:hover {
  color: var(--primary-color);
}

.patient-name {
  font-size: 1.8rem;
  margin: 0 0 5px 0;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}

.patient-badge {
  background-color: #e9ecef;
  border-radius: 15px;
  padding: 2px 10px;
  font-size: 0.8rem;
  margin-left: 15px;
  color: #495057;
  font-weight: normal;
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.btn-icon {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 5px;
  padding: 8px 12px;
  color: #495057;
  font-size: 0.9rem;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.2s;
  cursor: pointer;
}

.btn-icon:hover {
  background-color: #e9ecef;
  color: #212529;
}

.btn-icon.btn-success {
  background-color: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.btn-icon.btn-success:hover {
  background-color: #c3e6cb;
}

.btn-icon.btn-danger {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.btn-icon.btn-danger:hover {
  background-color: #f5c6cb;
}

.patient-overview {
  display: flex;
  justify-content: space-between;
  gap: 15px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.overview-card {
  flex: 1;
  min-width: 180px;
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  display: flex;
  align-items: center;
  gap: 15px;
}

.overview-icon {
  width: 45px;
  height: 45px;
  background-color: rgba(var(--primary-rgb), 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 1.2rem;
}

.overview-details h3 {
  font-size: 0.9rem;
  color: #6c757d;
  margin: 0 0 5px 0;
  font-weight: 500;
}

.overview-details p {
  font-size: 1.1rem;
  margin: 0;
  color: #212529;
  font-weight: 500;
}

.content-card {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 25px;
  margin-top: 20px;
}

.info-item {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.2s ease;
}

.info-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.info-item label {
  display: block;
  font-size: 0.85rem;
  color: #6c757d;
  margin-bottom: 8px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.info-item p {
  margin: 0;
  font-size: 1rem;
  color: #212529;
  font-weight: 400;
  line-height: 1.5;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-item i {
  color: var(--primary-color);
  font-size: 1.1rem;
}

/* Responsive adjustments for modern patient detail */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
  }

  .header-content {
    max-width: 100%;
    margin-bottom: 15px;
  }

  .patient-overview {
    flex-direction: column;
  }

  .overview-card {
    width: 100%;
  }

  .action-buttons {
    width: 100%;
    justify-content: space-between;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }
}

/* Modern Tabs Design */
.modern-tabs {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 30px;
  border-bottom: 1px solid #e9ecef;
  gap: 5px;
}

.modern-tab {
  background: transparent;
  border: none;
  padding: 12px 20px;
  cursor: pointer;
  font-weight: 500;
  color: #6c757d;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  font-size: 0.95rem;
  margin-bottom: -1px;
}

.modern-tab:hover {
  color: var(--primary-color);
  background-color: rgba(var(--primary-rgb), 0.05);
}

.modern-tab.active {
  color: var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
  background-color: rgba(var(--primary-rgb), 0.08);
}

.modern-tab i {
  font-size: 1rem;
}

.modern-tab-content {
  display: none;
  padding: 25px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
  margin-bottom: 30px;
}

.modern-tab-content.active {
  display: block;
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@media (max-width: 768px) {
  .modern-tabs {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
  }

  .modern-tab {
    flex: 1 1 calc(33.333% - 8px);
    min-width: 100px;
    justify-content: center;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #e9ecef;
    margin-bottom: 8px;
  }
}

/* Modern Tab Content Styles */
.modern-tab-content h2 {
  color: var(--primary-color);
  margin-bottom: 20px;
  font-size: 1.3rem;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.modern-tab-content h2 i {
  color: var(--primary-color);
  font-size: 1.2rem;
}

.modern-tab-content h3 {
  color: #495057;
  margin: 20px 0 10px 0;
  font-size: 1.1rem;
  font-weight: 500;
}

.modern-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
  border: 1px solid #f0f0f0;
}

@media (max-width: 768px) {
  .grid-2,
  .grid-3 {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}

/* Modern Records */
.records-count {
  margin-bottom: 20px;
  color: #6c757d;
  font-size: 0.95rem;
}

.record-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #f0f0f0;
}

.record-date {
  font-weight: 500;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: 6px;
}

.record-meta {
  color: #6c757d;
  font-size: 0.85rem;
}

/* Info item styling */
.info-item label {
  display: block;
  font-size: 0.85rem;
  color: #6c757d;
  margin-bottom: 4px;
  font-weight: 500;
}

.info-item p {
  margin: 0 0 12px 0;
  font-size: 0.95rem;
  color: #212529;
  line-height: 1.4;
}

/* Modern Patient Form Styling */
.patient-form-container {
  max-width: 1200px;
  margin: 0 auto;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.form-header {
  display: flex;
  align-items: center;
  padding: 24px 32px;
  border-bottom: 1px solid #f1f1f1;
}

.back-button {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #666;
  margin-right: 20px;
  transition: all 0.2s;
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 6px;
}

.back-button:hover {
  background: #f5f5f5;
  color: #333;
}

.back-button i {
  margin-right: 8px;
}

.form-title-container {
  flex: 1;
}

.form-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0;
  color: #333;
}

.form-subtitle {
  font-size: 16px;
  color: #666;
  margin: 4px 0 0 0;
}

/* Modern Tabs */
.patient-tabs {
  display: flex;
  border-bottom: 1px solid #f1f1f1;
  background: #fafafa;
  padding: 0 32px;
  overflow-x: auto;
  scrollbar-width: none; /* Firefox */
}

.patient-tabs::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Edge */
}

.patient-tab {
  padding: 16px 20px;
  font-size: 15px;
  color: #666;
  background: transparent;
  border: none;
  border-bottom: 3px solid transparent;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  white-space: nowrap;
}

.patient-tab:hover {
  color: var(--primary-color);
}

.patient-tab.active {
  color: var(--primary-color);
  border-bottom: 3px solid var(--primary-color);
}

.patient-tab i {
  margin-right: 10px;
  font-size: 18px;
}

/* Form Content */
.form-content {
  padding: 32px;
}

.tab-panel {
  display: none;
}

.tab-panel.active {
  display: block;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.panel-title {
  font-size: 20px;
  font-weight: 500;
  margin: 0 0 24px 0;
  color: #333;
  display: flex;
  align-items: center;
}

.panel-title i {
  margin-right: 12px;
  color: var(--primary-color);
  font-size: 20px;
}

/* Form Controls */
.form-row {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
}

.form-field {
  flex: 1;
  min-width: 0; /* Ensures proper sizing in flex containers */
}

.form-label {
  display: block;
  font-size: 14px;
  color: #555;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #e1e1e1;
  border-radius: 8px;
  font-size: 15px;
  color: #333;
  background: #fff;
  transition: all 0.2s;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
  outline: none;
}

.form-textarea {
  min-height: 100px;
  resize: vertical;
}

.form-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23555' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 16px center;
  padding-right: 40px;
}

.form-hint {
  font-size: 13px;
  color: #777;
  margin-top: 6px;
}

.form-field.disabled .form-input {
  background-color: #f5f5f5;
  color: #888;
  cursor: not-allowed;
}

/* Form Actions */
.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #f1f1f1;
}

.btn-cancel {
  padding: 12px 24px;
  background: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 8px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-cancel:hover {
  background: #e9e9e9;
}

.btn-save {
  padding: 12px 24px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 15px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
}

.btn-save i {
  margin-right: 8px;
}

.btn-save:hover {
  background: #1492a5;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 16px;
  }

  .form-header {
    padding: 20px;
  }

  .form-content {
    padding: 20px;
  }

  .patient-tabs {
    padding: 0 20px;
  }

  .patient-tab {
    padding: 12px 16px;
    font-size: 14px;
  }

  .form-title {
    font-size: 24px;
  }
}

/* Alert Styling */
.form-alert {
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
}

.form-alert i {
  margin-right: 12px;
  font-size: 18px;
}

.form-alert.error {
  background-color: #FFEAEA;
  color: #D63031;
  border: 1px solid #FACCCC;
}

.form-alert.success {
  background-color: #E4F8EF;
  color: #27AE60;
  border: 1px solid #BCECD7;
}

/* Appointment Dashboard Styles */
.appointment-dashboard {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  margin-bottom: 30px;
  overflow: hidden;
}

.view-toggle {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.view-btn {
  padding: 8px 16px;
  background: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 6px;
  margin-left: 10px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  transition: all 0.2s;
}

.view-btn i {
  margin-right: 8px;
}

.view-btn.active {
  background: var(--primary-color);
  color: #fff;
}

.appointments-list {
  margin-top: 20px;
}

.appointment-time {
  font-size: 13px;
  color: #666;
  margin-top: 4px;
}

.doctor-specialty {
  font-size: 13px;
  color: #666;
  margin-top: 4px;
}

.status-select {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  border: none;
  color: #fff;
  cursor: pointer;
  appearance: none;
  text-align: center;
  min-width: 120px;
}

.badge-primary {
  background-color: var(--primary-color);
}

.badge-success {
  background-color: #28a745;
}

.badge-danger {
  background-color: #dc3545;
}

.badge-warning {
  background-color: #ffc107;
  color: #333;
}

.badge-secondary {
  background-color: #6c757d;
}

.appointment-actions {
  display: flex;
  gap: 8px;
}

.no-appointments {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px 0;
  text-align: center;
  color: #666;
}

.no-appointments i {
  font-size: 50px;
  color: #ddd;
  margin-bottom: 15px;
}

.filters-container {
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.search-container {
  position: relative;
}

.search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

/* Calendar View */
.calendar-view {
  margin-top: 20px;
}

.calendar-notice {
  display: flex;
  align-items: center;
  background-color: #e4f8ef;
  color: #27AE60;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.calendar-notice i {
  font-size: 24px;
  margin-right: 15px;
}

.calendar-placeholder {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  min-height: 400px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.calendar-grid {
  width: 100%;
}

.calendar-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  font-weight: 500;
  color: #666;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.calendar-body {
  min-height: 350px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.calendar-message {
  text-align: center;
  color: #999;
}

.calendar-message i {
  font-size: 50px;
  color: #ddd;
  margin-bottom: 15px;
}

/* Appointment Detail Styles */
.appointment-detail-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  margin-bottom: 30px;
  overflow: hidden;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.btn-primary {
  display: inline-flex;
  align-items: center;
  padding: 10px 20px;
  background-color: var(--primary-color);
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary i {
  margin-right: 8px;
}

.btn-danger {
  display: inline-flex;
  align-items: center;
  padding: 10px 20px;
  background-color: #dc3545;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-danger i {
  margin-right: 8px;
}

.btn-success {
  display: inline-flex;
  align-items: center;
  padding: 10px 20px;
  background-color: #28a745;
  color: #fff;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-success i {
  margin-right: 8px;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 13px;
}

.appointment-summary {
  padding: 20px;
}

.appointment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f1f1f1;
}

.appointment-title h2 {
  margin: 0 0 10px 0;
  font-size: 24px;
  color: #333;
}

.appointment-type {
  font-size: 14px;
  padding: 4px 12px;
  border-radius: 20px;
  background-color: #f8f9fa;
  color: #666;
  display: inline-block;
}

.appointment-type.checkup {
  background-color: #e4f8ef;
  color: #27AE60;
}

.appointment-type.follow-up {
  background-color: #e3ecff;
  color: #4268F6;
}

.appointment-type.urgent {
  background-color: #ffe9e9;
  color: #d63031;
}

.appointment-type.consultation {
  background-color: #fff3e0;
  color: #f39c12;
}

.appointment-status {
  display: flex;
  align-items: center;
}

.appointment-status label {
  margin-right: 10px;
  font-weight: 500;
}

.appointment-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.info-group {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.info-group h3 {
  font-size: 16px;
  margin: 0 0 15px 0;
  color: #333;
  display: flex;
  align-items: center;
}

.info-group h3 i {
  margin-right: 10px;
  color: var(--primary-color);
}

.info-content {
  margin-left: 25px;
}

.info-row {
  margin-bottom: 10px;
  display: flex;
}

.info-label {
  font-weight: 500;
  width: 100px;
  color: #666;
}

.info-value {
  flex: 1;
}

.appointment-notes {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
}

.appointment-notes h3 {
  font-size: 16px;
  margin: 0 0 15px 0;
  color: #333;
  display: flex;
  align-items: center;
}

.appointment-notes h3 i {
  margin-right: 10px;
  color: var(--primary-color);
}

.notes-content {
  margin-left: 25px;
  white-space: pre-line;
  color: #555;
  line-height: 1.5;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.modal-content h3 {
  font-size: 18px;
  margin: 0 0 15px 0;
  color: #333;
  display: flex;
  align-items: center;
}

.modal-content h3 i {
  margin-right: 10px;
  color: #dc3545;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

/* Appointment Form Styles */
.patient-info-box {
  background-color: #f9f9f9;
  border-radius: 8px;
  margin-bottom: 24px;
  overflow: hidden;
}

.patient-info-header {
  background-color: var(--primary-color);
  color: #fff;
  padding: 12px 15px;
  display: flex;
  align-items: center;
}

.patient-info-header i {
  margin-right: 10px;
  font-size: 18px;
}

.patient-info-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.patient-info-content {
  padding: 15px;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.info-item {
  display: flex;
  align-items: center;
}

.info-label {
  font-weight: 500;
  margin-right: 8px;
  color: #666;
}

.btn-link {
  grid-column: span 2;
  color: var(--primary-color);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  margin-top: 10px;
  transition: color 0.2s;
}

.btn-link i {
  margin-right: 8px;
}

.btn-link:hover {
  color: #0d8496;
  text-decoration: underline;
}

@media (max-width: 768px) {
  .appointment-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .appointment-info-grid {
    grid-template-columns: 1fr;
  }

  .patient-info-content {
    grid-template-columns: 1fr;
  }

  .view-toggle {
    justify-content: center;
  }
}

/* Modern Appointment Dashboard Styles */
.modern-dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  color: #333;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: #f8fafc;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.modern-dashboard .dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.modern-dashboard .header-content h1 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  color: #1e293b;
  letter-spacing: -0.5px;
}

.modern-dashboard .header-subtitle {
  color: #64748b;
  margin: 0.5rem 0 0;
  font-size: 1rem;
  font-weight: 400;
}

.modern-dashboard .new-appointment-btn {
  background-color: #0ea5e9;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  font-size: 0.9rem;
  display: inline-flex;
  align-items: center;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  box-shadow: 0 4px 6px rgba(14, 165, 233, 0.2);
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.modern-dashboard .new-appointment-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.2));
  transform: translateX(-100%);
  transition: transform 0.6s ease;
  z-index: -1;
}

.modern-dashboard .new-appointment-btn:hover {
  background-color: #0284c7;
  box-shadow: 0 6px 12px rgba(14, 165, 233, 0.3);
  transform: translateY(-3px);
}

.modern-dashboard .new-appointment-btn:hover::before {
  transform: translateX(0);
}

.modern-dashboard .new-appointment-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(14, 165, 233, 0.2);
}

.modern-dashboard .new-appointment-btn i {
  margin-right: 0.6rem;
  font-size: 1rem;
  transition: transform 0.3s ease;
}

.modern-dashboard .new-appointment-btn:hover i {
  transform: rotate(90deg);
}

.modern-dashboard .dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2.5rem;
}

.modern-dashboard .stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.modern-dashboard .stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  transition: all 0.3s ease;
}

.modern-dashboard .stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.modern-dashboard .stat-card.today::before {
  background-color: #0ea5e9;
}

.modern-dashboard .stat-card.upcoming::before {
  background-color: #8b5cf6;
}

.modern-dashboard .stat-card.completed::before {
  background-color: #10b981;
}

.modern-dashboard .stat-card.cancelled::before {
  background-color: #ef4444;
}

.modern-dashboard .stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
  font-size: 1.25rem;
}

.modern-dashboard .stat-card.today .stat-icon {
  background: rgba(14, 165, 233, 0.1);
  color: #0ea5e9;
}

.modern-dashboard .stat-card.upcoming .stat-icon {
  background: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
}

.modern-dashboard .stat-card.completed .stat-icon {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.modern-dashboard .stat-card.cancelled .stat-icon {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.modern-dashboard .stat-content {
  flex: 1;
}

.modern-dashboard .stat-content h3 {
  font-size: 1.75rem;
  font-weight: 700;
  margin: 0;
  line-height: 1.2;
  color: #1e293b;
}

.modern-dashboard .stat-content p {
  margin: 0.25rem 0 0;
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.modern-dashboard .stat-progress {
  height: 4px;
  background-color: #e2e8f0;
  border-radius: 2px;
  margin-top: 0.75rem;
  overflow: hidden;
  width: 100%;
}

.modern-dashboard .stat-progress .progress-bar {
  height: 100%;
  border-radius: 2px;
  transition: width 0.5s ease;
}

.modern-dashboard .stat-card.today .progress-bar {
  background-color: #0ea5e9;
}

.modern-dashboard .stat-card.upcoming .progress-bar {
  background-color: #8b5cf6;
}

.modern-dashboard .stat-card.completed .progress-bar {
  background-color: #10b981;
}

.modern-dashboard .stat-card.cancelled .progress-bar {
  background-color: #ef4444;
}

.modern-dashboard .dashboard-controls-wrapper {
  background-color: white;
  border-radius: 12px;
  padding: 1.25rem;
  margin-bottom: 2rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.03);
}

.modern-dashboard .dashboard-controls {
  margin-bottom: 1.5rem;
}

.modern-dashboard .tab-controls {
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
  padding-bottom: 1rem;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.modern-dashboard .tab-btn {
  background: none;
  border: none;
  padding: 0.75rem 1.25rem;
  font-size: 0.9rem;
  color: #64748b;
  cursor: pointer;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  position: relative;
}

.modern-dashboard .tab-btn i {
  margin-right: 0.5rem;
  font-size: 0.9rem;
}

.modern-dashboard .tab-btn:hover {
  background-color: #f1f5f9;
  color: #0ea5e9;
}

.modern-dashboard .tab-btn.active {
  background-color: #0ea5e9;
  color: white;
}

.modern-dashboard .tab-btn.active::after {
  content: '';
  position: absolute;
  bottom: -1rem;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #0ea5e9;
}

.modern-dashboard .view-toggle {
  margin-left: auto;
  display: flex;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.modern-dashboard .view-btn {
  background: white;
  border: none;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #64748b;
}

.modern-dashboard .view-btn:hover {
  background-color: #f1f5f9;
  color: #0ea5e9;
}

.modern-dashboard .view-btn.active {
  background-color: #0ea5e9;
  color: white;
}

.modern-dashboard .filter-controls {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.modern-dashboard .search-container {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.modern-dashboard .search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  background-color: #f8fafc;
  color: #1e293b;
}

.modern-dashboard .search-input:focus {
  outline: none;
  border-color: #0ea5e9;
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
  background-color: white;
}

.modern-dashboard .search-input::placeholder {
  color: #94a3b8;
}

.modern-dashboard .search-icon {
  position: absolute;
  left: 0.9rem;
  top: 50%;
  transform: translateY(-50%);
  color: #64748b;
  font-size: 0.9rem;
}

.modern-dashboard .clear-search {
  position: absolute;
  right: 0.9rem;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #64748b;
  cursor: pointer;
  padding: 0;
  font-size: 0.9rem;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.modern-dashboard .clear-search:hover {
  opacity: 1;
}

.modern-dashboard .filter-group {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  align-items: center;
}

.modern-dashboard .filter-item {
  position: relative;
}

.modern-dashboard .filter-icon {
  position: absolute;
  left: 0.9rem;
  top: 50%;
  transform: translateY(-50%);
  color: #64748b;
  font-size: 0.9rem;
  pointer-events: none;
}

.modern-dashboard .filter-select,
.modern-dashboard .date-filter {
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.9rem;
  color: #1e293b;
  background-color: #f8fafc;
  min-width: 180px;
  transition: all 0.2s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%2364748b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.9rem center;
  padding-right: 2.5rem;
}

.modern-dashboard .filter-select:focus,
.modern-dashboard .date-filter:focus {
  outline: none;
  border-color: #0ea5e9;
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
  background-color: white;
}

.modern-dashboard .clear-filters-btn {
  background: none;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 0.75rem 1.25rem;
  color: #64748b;
  cursor: pointer;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
  font-weight: 500;
}

.modern-dashboard .clear-filters-btn:hover {
  background-color: #fee2e2;
  color: #ef4444;
  border-color: #fecaca;
}

.modern-dashboard .clear-filters-btn i {
  margin-right: 0.5rem;
  font-size: 0.9rem;
}

.modern-dashboard .active-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding: 1rem 1.25rem;
  background-color: white;
  border-radius: 12px;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.03);
  border-left: 4px solid #0ea5e9;
}

.modern-dashboard .active-filters-label {
  font-size: 0.9rem;
  color: #64748b;
  margin-right: 0.5rem;
  font-weight: 500;
}

.modern-dashboard .filter-tag {
  display: flex;
  align-items: center;
  padding: 0.35rem 0.85rem;
  background-color: #f1f5f9;
  border-radius: 20px;
  font-size: 0.85rem;
  color: #1e293b;
  font-weight: 500;
  transition: all 0.2s ease;
  border: 1px solid #e2e8f0;
}

.modern-dashboard .filter-tag:hover {
  background-color: #e2e8f0;
}

.modern-dashboard .filter-tag i {
  margin-right: 0.5rem;
  color: #0ea5e9;
  font-size: 0.8rem;
}

.modern-dashboard .remove-filter {
  background: none;
  border: none;
  color: #64748b;
  margin-left: 0.6rem;
  cursor: pointer;
  padding: 0;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.modern-dashboard .remove-filter:hover {
  background-color: #fee2e2;
  color: #ef4444;
  opacity: 1;
}

.modern-dashboard .appointments-container {
  margin-top: 2rem;
}

.modern-dashboard .appointment-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;
}

.modern-dashboard .appointment-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.03);
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  position: relative;
  border: 1px solid #e2e8f0;
}

.modern-dashboard .appointment-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  transition: all 0.3s ease;
}

.modern-dashboard .appointment-card.scheduled::before {
  background-color: #0ea5e9;
}

.modern-dashboard .appointment-card.completed::before {
  background-color: #10b981;
}

.modern-dashboard .appointment-card.cancelled::before {
  background-color: #ef4444;
}

.modern-dashboard .appointment-card.no-show::before {
  background-color: #f59e0b;
}

.modern-dashboard .appointment-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.modern-dashboard .appointment-card.scheduled:hover {
  border-color: rgba(14, 165, 233, 0.4);
}

.modern-dashboard .appointment-card.completed:hover {
  border-color: rgba(16, 185, 129, 0.4);
}

.modern-dashboard .appointment-card.cancelled:hover {
  border-color: rgba(239, 68, 68, 0.4);
}

.modern-dashboard .appointment-card.no-show:hover {
  border-color: rgba(245, 158, 11, 0.4);
}

.modern-dashboard .appointment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.25rem;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.modern-dashboard .appointment-date {
  display: flex;
  align-items: center;
  color: #1e293b;
  font-weight: 600;
  font-size: 0.95rem;
}

.modern-dashboard .appointment-date i {
  margin-right: 0.5rem;
  color: #0ea5e9;
  font-size: 0.9rem;
}

.modern-dashboard .appointment-status-badge {
  padding: 0.35rem 0.85rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: capitalize;
  letter-spacing: 0.5px;
}

.modern-dashboard .appointment-status-badge.scheduled {
  background-color: rgba(14, 165, 233, 0.1);
  color: #0ea5e9;
  border: 1px solid rgba(14, 165, 233, 0.2);
}

.modern-dashboard .appointment-status-badge.completed {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
  border: 1px solid rgba(16, 185, 129, 0.2);
}

.modern-dashboard .appointment-status-badge.cancelled {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.modern-dashboard .appointment-status-badge.no-show {
  background-color: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.modern-dashboard .appointment-body {
  padding: 1.25rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.modern-dashboard .patient-info {
  position: relative;
}

.modern-dashboard .patient-info h3 {
  margin: 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.4;
}

.modern-dashboard .patient-info h3 a {
  color: #1e293b;
  text-decoration: none;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.modern-dashboard .patient-info h3 a:hover {
  color: #0ea5e9;
}

.modern-dashboard .patient-icon {
  color: #0ea5e9;
  font-size: 1.1rem;
}

.modern-dashboard .patient-meta {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 0.5rem;
}

.modern-dashboard .patient-id {
  font-size: 0.8rem;
  color: #64748b;
  font-weight: 500;
  display: flex;
  align-items: center;
  background-color: #f1f5f9;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  gap: 0.3rem;
}

.modern-dashboard .patient-id i {
  color: #0ea5e9;
  font-size: 0.8rem;
}

.modern-dashboard .appointment-time {
  font-size: 0.8rem;
  color: #64748b;
  font-weight: 500;
  display: flex;
  align-items: center;
  background-color: #f1f5f9;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  gap: 0.3rem;
}

.modern-dashboard .appointment-time i {
  color: #0ea5e9;
  font-size: 0.8rem;
}

.modern-dashboard .appointment-details {
  display: flex;
  flex-direction: column;
  gap: 0.85rem;
  background-color: #f8fafc;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.modern-dashboard .appointment-card:hover .appointment-details {
  border-color: #cbd5e1;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.modern-dashboard .detail-item {
  display: flex;
  align-items: flex-start;
  font-size: 0.9rem;
  color: #1e293b;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.modern-dashboard .detail-item:hover {
  background-color: #f1f5f9;
}

.modern-dashboard .detail-item i {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
  color: white;
  background-color: #0ea5e9;
  border-radius: 6px;
  font-size: 0.8rem;
}

.modern-dashboard .detail-item.doctor i {
  background-color: #8b5cf6;
}

.modern-dashboard .detail-item.reason i {
  background-color: #10b981;
}

.modern-dashboard .detail-item.notes i {
  background-color: #f59e0b;
}

.modern-dashboard .detail-content {
  flex: 1;
}

.modern-dashboard .detail-label {
  font-weight: 600;
  color: #64748b;
  font-size: 0.75rem;
  display: block;
  margin-bottom: 0.2rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.modern-dashboard .detail-value {
  color: #1e293b;
  line-height: 1.4;
  font-weight: 500;
}

.modern-dashboard .doctor-specialty {
  color: #64748b;
  font-size: 0.8rem;
  margin-top: 0.2rem;
  font-style: italic;
  background-color: rgba(139, 92, 246, 0.1);
  display: inline-block;
  padding: 0.1rem 0.4rem;
  border-radius: 4px;
}

.modern-dashboard .appointment-footer {
  padding: 1.25rem;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f8fafc;
}

.modern-dashboard .status-control {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.modern-dashboard .status-label {
  font-size: 0.85rem;
  font-weight: 600;
  color: #64748b;
}

.modern-dashboard .select-wrapper {
  position: relative;
  flex: 1;
  max-width: 180px;
}

.modern-dashboard .status-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  z-index: 1;
  color: #0ea5e9;
  font-size: 1rem;
  animation: fadeIn 0.2s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.modern-dashboard .status-select {
  width: 100%;
  padding: 0.6rem 0.75rem;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  font-size: 0.85rem;
  background-color: white;
  cursor: pointer;
  font-weight: 500;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%2364748b' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  padding-right: 2.5rem;
  transition: all 0.2s ease;
}

.modern-dashboard .status-select:focus {
  outline: none;
  border-color: #0ea5e9;
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.modern-dashboard .status-select.scheduled {
  border-color: #0ea5e9;
  color: #0ea5e9;
  background-color: rgba(14, 165, 233, 0.05);
}

.modern-dashboard .status-select.completed {
  border-color: #10b981;
  color: #10b981;
  background-color: rgba(16, 185, 129, 0.05);
}

.modern-dashboard .status-select.cancelled {
  border-color: #ef4444;
  color: #ef4444;
  background-color: rgba(239, 68, 68, 0.05);
}

.modern-dashboard .status-select.no-show {
  border-color: #f59e0b;
  color: #f59e0b;
  background-color: rgba(245, 158, 11, 0.05);
}

.modern-dashboard .action-buttons {
  display: flex;
  gap: 0.75rem;
}

.modern-dashboard .action-btn {
  width: 38px;
  height: 38px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  text-decoration: none;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  position: relative;
  overflow: hidden;
}

.modern-dashboard .action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.modern-dashboard .action-btn.view {
  background-color: #0ea5e9;
}

.modern-dashboard .action-btn.edit {
  background-color: #8b5cf6;
}

.modern-dashboard .action-btn.create {
  background-color: #10b981;
}

.modern-dashboard .action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modern-dashboard .action-btn:hover::before {
  transform: translateY(0);
}

.modern-dashboard .action-btn:active {
  transform: translateY(0);
  box-shadow: none;
}

.modern-dashboard .empty-state {
  text-align: center;
  padding: 4rem 2rem;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.03);
  border: 1px solid #e2e8f0;
}

.modern-dashboard .empty-state-icon {
  width: 80px;
  height: 80px;
  background-color: #f1f5f9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.modern-dashboard .empty-state-icon i {
  font-size: 2.5rem;
  color: #94a3b8;
}

.modern-dashboard .empty-state h3 {
  font-size: 1.5rem;
  color: #1e293b;
  margin: 0 0 0.75rem;
  font-weight: 600;
}

.modern-dashboard .empty-state p {
  color: #64748b;
  margin: 0 0 2rem;
  font-size: 1rem;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.modern-dashboard .empty-state-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.modern-dashboard .btn-secondary {
  background-color: #f1f5f9;
  color: #64748b;
  border: 1px solid #e2e8f0;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
}

.modern-dashboard .btn-secondary i {
  margin-right: 0.5rem;
  font-size: 0.9rem;
}

.modern-dashboard .btn-secondary:hover {
  background-color: #e2e8f0;
  color: #1e293b;
}

.modern-dashboard .btn-primary {
  background-color: #0ea5e9;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
}

.modern-dashboard .btn-primary i {
  margin-right: 0.5rem;
  font-size: 0.9rem;
}

.modern-dashboard .btn-primary:hover {
  background-color: #0284c7;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modern-dashboard .reset-filters-btn {
  background-color: #0ea5e9;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
}

.modern-dashboard .reset-filters-btn i {
  margin-right: 0.5rem;
  font-size: 0.9rem;
}

.modern-dashboard .reset-filters-btn:hover {
  background-color: #0284c7;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modern-dashboard .calendar-view {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.03);
  padding: 3rem 2rem;
  border: 1px solid #e2e8f0;
}

.modern-dashboard .calendar-placeholder {
  text-align: center;
  max-width: 500px;
  margin: 0 auto;
}

.modern-dashboard .placeholder-icon {
  width: 80px;
  height: 80px;
  background-color: #f1f5f9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
}

.modern-dashboard .placeholder-icon i {
  font-size: 2.5rem;
  color: #94a3b8;
}

.modern-dashboard .calendar-placeholder h3 {
  font-size: 1.5rem;
  color: #1e293b;
  margin: 0 0 0.75rem;
  font-weight: 600;
}

.modern-dashboard .calendar-placeholder p {
  color: #64748b;
  margin: 0 0 2rem;
  font-size: 1rem;
}

/* Responsive styles */
@media (max-width: 768px) {
  .modern-dashboard {
    padding: 1.25rem;
    border-radius: 0;
  }

  .modern-dashboard .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .modern-dashboard .header-content {
    width: 100%;
  }

  .modern-dashboard .header-greeting {
    width: 100%;
  }

  .modern-dashboard .new-appointment-btn {
    width: 100%;
    justify-content: center;
  }

  .modern-dashboard .dashboard-stats {
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 1rem;
  }

  .modern-dashboard .stat-card {
    padding: 1rem;
  }

  .modern-dashboard .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .modern-dashboard .stat-content h3 {
    font-size: 1.5rem;
  }

  .modern-dashboard .dashboard-controls-wrapper {
    padding: 1rem;
  }

  .modern-dashboard .tab-controls {
    justify-content: flex-start;
    overflow-x: auto;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none; /* Firefox */
  }

  .modern-dashboard .tab-controls::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Edge */
  }

  .modern-dashboard .tab-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
    white-space: nowrap;
  }

  .modern-dashboard .tab-btn.active::after {
    display: none;
  }

  .modern-dashboard .view-toggle {
    margin-left: 0;
    margin-top: 0.5rem;
  }

  .modern-dashboard .filter-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .modern-dashboard .search-container {
    width: 100%;
  }

  .modern-dashboard .filter-group {
    flex-direction: column;
    width: 100%;
    gap: 0.75rem;
  }

  .modern-dashboard .filter-item {
    width: 100%;
  }

  .modern-dashboard .filter-select,
  .modern-dashboard .date-filter {
    width: 100%;
    max-width: none;
  }

  .modern-dashboard .active-filters {
    padding: 0.75rem 1rem;
  }

  .modern-dashboard .appointment-cards {
    grid-template-columns: 1fr;
  }

  .modern-dashboard .appointment-footer {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .modern-dashboard .status-control {
    max-width: none;
  }

  .modern-dashboard .status-select {
    max-width: none;
  }

  .modern-dashboard .action-buttons {
    justify-content: flex-end;
    margin-left: 0;
  }

  .modern-dashboard .empty-state-actions {
    flex-direction: column;
    gap: 0.75rem;
  }

  .modern-dashboard .btn-secondary,
  .modern-dashboard .btn-primary {
    width: 100%;
    justify-content: center;
  }
}

/* General Mobile Styles */
@media (max-width: 576px) {
  /* Navbar */
  .navbar {
    display: flex;
    text-align: center;
    padding: 0.5rem 1rem;
  }

  .navbar ul {
    display: flex;
    text-align: center;
    justify-content: flex-end;
  }

  .user-role {
    display: none;
  }

  .user-info {
    padding: 0.3rem 0.6rem;
  }

  .user-avatar {
    width: 26px;
    height: 26px;
  }
}