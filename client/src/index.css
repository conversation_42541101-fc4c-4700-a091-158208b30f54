/* Import accessibility styles */
@import './components/admin/layout/AccessibilityStyles.css';

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Keyboard navigation detection */
body:not(.using-mouse) *:focus {
  outline: 2px solid var(--primary-color, #2C4B2B) !important;
  outline-offset: 2px !important;
}

/* Add keyboard navigation detection script */
body.using-mouse *:focus {
  outline: none !important;
}
