import React from 'react';
import { Container } from '@mui/material';
import PatientDashboard from '../components/patients/PatientDashboard';
import ProtectedRoute from '../components/routing/ProtectedRoute';

const PatientDashboardPage: React.FC = () => {
  return (
    <ProtectedRoute allowedRoles={['patient']}>
      <Container maxWidth="lg">
        <PatientDashboard />
      </Container>
    </ProtectedRoute>
  );
};

export default PatientDashboardPage; 