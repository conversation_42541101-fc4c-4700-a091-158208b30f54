import React from 'react';
import { Container } from '@mui/material';
import KinDashboard from '../components/kin/KinDashboard';
import ProtectedRoute from '../components/routing/ProtectedRoute';

const KinDashboardPage: React.FC = () => {
  return (
    <ProtectedRoute allowedRoles={['kin']}>
      <Container maxWidth="lg">
        <KinDashboard />
      </Container>
    </ProtectedRoute>
  );
};

export default KinDashboardPage;
