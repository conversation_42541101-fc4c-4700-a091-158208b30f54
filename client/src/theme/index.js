import { createTheme } from '@mui/material/styles';

// Create a custom theme with extended palette using logo colors:
// Dark Green (leaves, text): #2C4B2B
// Olive Green (leaf): #A1A43A
// Yellow-Orange (leaf): #F6B21A
// Orange (hands, leaf): #D97B3A
// Light Orange (hands): #F2A65A

// Common palette values for both light and dark modes
const commonPalette = {
  primary: {
    main: '#2C4B2B', // Dark Green
    light: '#3E6A3D', // Lighter shade of Dark Green
    dark: '#1E3A1D', // Darker shade of Dark Green
    lightest: '#EFF5EF', // Custom lightest shade for primary
    contrastText: '#fff',
  },
  secondary: {
    main: '#A1A43A', // Olive Green
    light: '#B8BB4F', // Lighter shade of Olive Green
    dark: '#7D7F2D', // Darker shade of Olive Green
    lightest: '#F7F7E8', // Custom lightest shade for secondary
    contrastText: '#fff',
  },
  success: {
    main: '#2C4B2B', // Dark Green (same as primary)
    light: '#3E6A3D', // Lighter shade of Dark Green
    dark: '#1E3A1D', // Darker shade of Dark Green
    lightest: '#EFF5EF', // Custom lightest shade for success
    contrastText: '#fff',
  },
  warning: {
    main: '#F6B21A', // Yellow-Orange
    light: '#F8C44A', // Lighter shade of Yellow-Orange
    dark: '#D99A0E', // Darker shade of Yellow-Orange
    lightest: '#FEF7E8', // Custom lightest shade for warning
    contrastText: '#333',
  },
  error: {
    main: '#D97B3A', // Orange
    light: '#E19559', // Lighter shade of Orange
    dark: '#B5652F', // Darker shade of Orange
    lightest: '#FCF1E9', // Custom lightest shade for error
    contrastText: '#fff',
  },
  info: {
    main: '#F2A65A', // Light Orange
    light: '#F5B979', // Lighter shade of Light Orange
    dark: '#D98A3A', // Darker shade of Light Orange
    lightest: '#FDF5EC', // Custom lightest shade for info
    contrastText: '#333',
  },
};

// Common typography settings
const commonTypography = {
  fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
  h1: { fontWeight: 500 },
  h2: { fontWeight: 500 },
  h3: { fontWeight: 500 },
  h4: { fontWeight: 500 },
  h5: { fontWeight: 500 },
  h6: { fontWeight: 500 },
};

// Common component overrides
const commonComponents = {
  MuiButton: {
    styleOverrides: {
      root: {
        textTransform: 'none',
        fontWeight: 500,
      },
      contained: {
        color: '#FFFFFF',
      },
      containedPrimary: {
        color: '#FFFFFF',
      },
    },
  },
  MuiCard: {
    styleOverrides: {
      root: {
        borderRadius: 12,
      },
    },
  },
  MuiPaper: {
    styleOverrides: {
      root: {
        backgroundImage: 'none',
      },
    },
  },
};

// Create light theme
export const lightTheme = createTheme({
  palette: {
    mode: 'light',
    ...commonPalette,
    background: {
      default: '#F8FAF8', // Very light green tint
      paper: '#ffffff',
    },
    text: {
      primary: '#333333',
      secondary: '#666666',
    },
  },
  typography: commonTypography,
  shape: {
    borderRadius: 8,
  },
  components: commonComponents,
});

// Create dark theme
export const darkTheme = createTheme({
  palette: {
    mode: 'dark',
    ...commonPalette,
    background: {
      default: '#121212', // Dark background
      paper: '#1E1E1E',   // Slightly lighter dark background for cards/papers
    },
    text: {
      primary: '#FFFFFF',
      secondary: '#AAAAAA',
    },
  },
  typography: commonTypography,
  shape: {
    borderRadius: 8,
  },
  components: {
    ...commonComponents,
    MuiPaper: {
      styleOverrides: {
        root: {
          backgroundImage: 'none',
          backgroundColor: '#1E1E1E',
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          backgroundColor: '#1A1A1A',
        },
      },
    },
    MuiDrawer: {
      styleOverrides: {
        paper: {
          backgroundColor: '#1A1A1A',
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
        },
      },
    },
  },
});

// Default export is the light theme for backward compatibility
export default lightTheme;
