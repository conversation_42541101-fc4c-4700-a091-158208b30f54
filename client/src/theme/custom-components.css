/* Custom Component Styles using the logo colors
 * <PERSON> Green (leaves, text): #2C4B2B
 * <PERSON> (leaf): #A1A43A
 * Yellow-Orange (leaf): #F6B21A
 * Orange (hands, leaf): #D97B3A
 * Light Orange (hands): #F2A65A
 */

/* Dark Mode Styles */
.dark-mode .theme-card {
  background-color: #1E1E1E;
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.dark-mode .theme-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.dark-mode .theme-card-header {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .theme-card-title {
  color: #fff;
}

.dark-mode .theme-card-footer {
  background-color: rgba(255, 255, 255, 0.05);
  border-top-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .theme-stat-card {
  background-color: #1E1E1E;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.dark-mode .theme-stat-card:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
}

.dark-mode .theme-stat-value {
  color: #fff;
}

.dark-mode .theme-stat-label {
  color: #aaa;
}

.dark-mode .theme-progress {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Dark Mode Badge Styles */
.dark-mode .theme-badge.primary {
  background-color: rgba(44, 75, 43, 0.3);
  color: #5c9c5a;
  border-color: rgba(44, 75, 43, 0.4);
}

.dark-mode .theme-badge.secondary {
  background-color: rgba(161, 164, 58, 0.3);
  color: #c8cc5a;
  border-color: rgba(161, 164, 58, 0.4);
}

.dark-mode .theme-badge.warning {
  background-color: rgba(246, 178, 26, 0.3);
  color: #f8c44a;
  border-color: rgba(246, 178, 26, 0.4);
}

.dark-mode .theme-badge.accent {
  background-color: rgba(217, 123, 58, 0.3);
  color: #e19559;
  border-color: rgba(217, 123, 58, 0.4);
}

.dark-mode .theme-badge.light-accent {
  background-color: rgba(242, 166, 90, 0.3);
  color: #f5b979;
  border-color: rgba(242, 166, 90, 0.4);
}

/* Dark Mode Alert Styles */
.dark-mode .theme-alert.primary {
  background-color: rgba(44, 75, 43, 0.2);
  color: #5c9c5a;
  border-color: rgba(44, 75, 43, 0.3);
}

.dark-mode .theme-alert.secondary {
  background-color: rgba(161, 164, 58, 0.2);
  color: #c8cc5a;
  border-color: rgba(161, 164, 58, 0.3);
}

.dark-mode .theme-alert.warning {
  background-color: rgba(246, 178, 26, 0.2);
  color: #f8c44a;
  border-color: rgba(246, 178, 26, 0.3);
}

.dark-mode .theme-alert.accent {
  background-color: rgba(217, 123, 58, 0.2);
  color: #e19559;
  border-color: rgba(217, 123, 58, 0.3);
}

/* Card Styles */
.theme-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  background-color: #fff;
  border: 1px solid rgba(44, 75, 43, 0.1);
  transition: all 0.3s ease;
  overflow: hidden;
}

.theme-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.theme-card-header {
  padding: 1.25rem;
  border-bottom: 1px solid rgba(44, 75, 43, 0.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.theme-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #2C4B2B;
  margin: 0;
}

.theme-card-body {
  padding: 1.25rem;
}

.theme-card-footer {
  padding: 1rem 1.25rem;
  background-color: rgba(44, 75, 43, 0.03);
  border-top: 1px solid rgba(44, 75, 43, 0.08);
}

/* Accent Cards */
.theme-card.primary {
  border-top: 4px solid #2C4B2B;
}

.theme-card.secondary {
  border-top: 4px solid #A1A43A;
}

.theme-card.warning {
  border-top: 4px solid #F6B21A;
}

.theme-card.accent {
  border-top: 4px solid #D97B3A;
}

.theme-card.light-accent {
  border-top: 4px solid #F2A65A;
}

/* Badge Styles */
.theme-badge {
  display: inline-block;
  padding: 0.35em 0.65em;
  font-size: 0.75em;
  font-weight: 600;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 6px;
}

.theme-badge.primary {
  background-color: rgba(44, 75, 43, 0.1);
  color: #2C4B2B;
  border: 1px solid rgba(44, 75, 43, 0.2);
}

.theme-badge.secondary {
  background-color: rgba(161, 164, 58, 0.1);
  color: #A1A43A;
  border: 1px solid rgba(161, 164, 58, 0.2);
}

.theme-badge.warning {
  background-color: rgba(246, 178, 26, 0.1);
  color: #F6B21A;
  border: 1px solid rgba(246, 178, 26, 0.2);
}

.theme-badge.accent {
  background-color: rgba(217, 123, 58, 0.1);
  color: #D97B3A;
  border: 1px solid rgba(217, 123, 58, 0.2);
}

.theme-badge.light-accent {
  background-color: rgba(242, 166, 90, 0.1);
  color: #F2A65A;
  border: 1px solid rgba(242, 166, 90, 0.2);
}

/* Alert Styles */
.theme-alert {
  position: relative;
  padding: 1rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
  border-radius: 8px;
}

.theme-alert.primary {
  color: #2C4B2B;
  background-color: rgba(44, 75, 43, 0.1);
  border-color: rgba(44, 75, 43, 0.2);
  border-left: 4px solid #2C4B2B;
}

.theme-alert.secondary {
  color: #A1A43A;
  background-color: rgba(161, 164, 58, 0.1);
  border-color: rgba(161, 164, 58, 0.2);
  border-left: 4px solid #A1A43A;
}

.theme-alert.warning {
  color: #D97B3A;
  background-color: rgba(246, 178, 26, 0.1);
  border-color: rgba(246, 178, 26, 0.2);
  border-left: 4px solid #F6B21A;
}

.theme-alert.accent {
  color: #D97B3A;
  background-color: rgba(217, 123, 58, 0.1);
  border-color: rgba(217, 123, 58, 0.2);
  border-left: 4px solid #D97B3A;
}

/* Dashboard Stat Cards */
.theme-stat-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.theme-stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.theme-stat-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
}

.theme-stat-card.primary::after {
  background-color: #2C4B2B;
}

.theme-stat-card.secondary::after {
  background-color: #A1A43A;
}

.theme-stat-card.warning::after {
  background-color: #F6B21A;
}

.theme-stat-card.accent::after {
  background-color: #D97B3A;
}

.theme-stat-card.light-accent::after {
  background-color: #F2A65A;
}

.theme-stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.theme-stat-icon.primary {
  background-color: rgba(44, 75, 43, 0.1);
  color: #2C4B2B;
}

.theme-stat-icon.secondary {
  background-color: rgba(161, 164, 58, 0.1);
  color: #A1A43A;
}

.theme-stat-icon.warning {
  background-color: rgba(246, 178, 26, 0.1);
  color: #F6B21A;
}

.theme-stat-icon.accent {
  background-color: rgba(217, 123, 58, 0.1);
  color: #D97B3A;
}

.theme-stat-icon.light-accent {
  background-color: rgba(242, 166, 90, 0.1);
  color: #F2A65A;
}

.theme-stat-value {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: #333;
}

.theme-stat-label {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 1rem;
}

/* Progress Bars */
.theme-progress {
  height: 8px;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  overflow: hidden;
  margin-top: auto;
}

.theme-progress-bar {
  height: 100%;
  border-radius: 4px;
}

.theme-progress-bar.primary {
  background-color: #2C4B2B;
}

.theme-progress-bar.secondary {
  background-color: #A1A43A;
}

.theme-progress-bar.warning {
  background-color: #F6B21A;
}

.theme-progress-bar.accent {
  background-color: #D97B3A;
}

.theme-progress-bar.light-accent {
  background-color: #F2A65A;
}
