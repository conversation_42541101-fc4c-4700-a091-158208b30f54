import { useContext } from 'react';
import SystemSettingsContext from '../context/SystemSettingsContext';

/**
 * Custom hook to access system settings
 * @returns The system settings context
 */
const useSystemSettings = () => {
  const context = useContext(SystemSettingsContext);
  
  if (context === undefined) {
    throw new Error('useSystemSettings must be used within a SystemSettingsProvider');
  }
  
  return context;
};

export default useSystemSettings;
