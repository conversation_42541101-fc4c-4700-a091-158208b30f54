<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="Medical Records Management System"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <link
      href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
      integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />
    <title>MedApp | Medical Records System</title>
    <style>
      /* Dark mode styles for landing page */
      body.dark-mode,
      html.dark-mode,
      body.dark-mode #root,
      body.dark-mode .App,
      body.dark-mode .container,
      body.dark-mode .modern-landing {
        background-color: #121212 !important;
        color: #fff !important;
      }

      /* Fix for login button in dark mode */
      body.dark-mode .landing-btn.login-btn,
      html.dark-mode .landing-btn.login-btn,
      .dark-mode .landing-btn.login-btn,
      .dark-mode .modern-landing .landing-btn.login-btn,
      .dark-mode .landing-content .landing-btn.login-btn,
      .dark-mode .landing-buttons .landing-btn.login-btn {
        background-color: #D97B3A !important; /* Orange from logo */
        color: #fff !important;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3) !important;
      }

      body.dark-mode .landing-btn.login-btn:hover,
      html.dark-mode .landing-btn.login-btn:hover,
      .dark-mode .landing-btn.login-btn:hover,
      .dark-mode .modern-landing .landing-btn.login-btn:hover,
      .dark-mode .landing-content .landing-btn.login-btn:hover,
      .dark-mode .landing-buttons .landing-btn.login-btn:hover {
        background-color: #F2A65A !important; /* Light Orange from logo */
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4) !important;
      }

      /* Fix for the Sign In heading text color in login page */
      body.dark-mode .login-heading,
      html.dark-mode .login-heading,
      body.dark-mode .login-form-section .login-heading,
      html.dark-mode .login-form-section .login-heading,
      body.dark-mode .login-logo-container .login-heading,
      html.dark-mode .login-logo-container .login-heading {
        color: #ffffff !important;
        background-color: transparent !important;
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
