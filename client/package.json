{"name": "client", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fortawesome/fontawesome-free": "^6.7.2", "@mui/icons-material": "^6.4.6", "@mui/material": "^6.4.6", "@mui/x-date-pickers": "^8.2.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.126", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@types/react-router-dom": "^5.3.3", "@types/recharts": "^1.8.29", "axios": "^1.8.1", "chart.js": "^4.4.8", "dayjs": "^1.11.13", "leaflet": "^1.9.4", "react": "^19.0.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-leaflet": "^5.0.0", "react-quill": "^2.0.0", "react-router-dom": "^7.2.0", "react-scripts": "5.0.1", "recharts": "^2.15.3", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/leaflet": "^1.9.17"}}