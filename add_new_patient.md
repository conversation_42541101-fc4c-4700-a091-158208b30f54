# Add New Patient Feature Documentation

## Overview

The Add New Patient feature allows administrators and medical staff to create new patient records in the system. When a patient is created, the system automatically generates a unique patient ID and creates a corresponding user account for the patient.

## Frontend Implementation

The feature is implemented in `client/src/components/patients/PatientForm.tsx` with the following characteristics:

- Multi-step form with tabs for different sections of patient information
- Comprehensive data collection including:
  - Basic Information (name, DOB, gender)
  - Contact Details (phone, email, address)
  - Medical Information (medical history, allergies, medications)
  - Vital Signs (height, weight, blood pressure, etc.)
  - Lab Results (blood tests, etc.)
  - Mental Health assessments
  - Vaccinations
  - Health Status
- Form validation for required fields
- Auto-calculation of derived values (e.g., BMI from height and weight)
- Doctor assignment functionality

## Backend Implementation

The backend processing is handled in several files:

1. **API Endpoint**: `server/routes/patients.js`
   - POST `/api/patients` route for creating new patients
   - Handles the incoming request and calls the Patient model

2. **Patient Model**: `server/models/Patient.js`
   - `Patient.create()` method processes the data
   - Generates a unique patient ID (format: PXXXXX where X is alphanumeric)
   - Handles data validation and type conversion
   - Inserts the patient record into the database
   - Initiates user account creation for the patient

3. **User Account Creation**: `server/models/User.js`
   - Creates a user account with role 'patient'
   - Username is generated from patient's first and last name (lowercase, no spaces or special characters)
   - Default password is generated based on password policy
   - Links the user account to the patient record via patient_id

4. **Password Policy**: `server/models/PasswordPolicy.js`
   - Defines requirements for passwords (length, character types)
   - Generates default passwords that meet policy requirements
   - Default pattern is username plus additional characters

## Database Schema

Patient records are stored in the `patients` table with the following key fields:

- `patient_id`: Auto-incremented primary key
- `first_name`, `last_name`: Patient name
- `unique_id`: Generated unique identifier (PXXXXX format)
- `date_of_birth`: Patient's date of birth
- `gender`: Patient's gender
- Various health-related fields for vital signs, lab results, etc.

User accounts are stored in the `users` table with a reference to the patient record.

## Process Flow

1. User navigates to the Add New Patient form
2. User fills out the required information across multiple tabs
3. User submits the form
4. Backend validates the data
5. System generates a unique patient ID
6. Patient record is created in the database
7. System generates a username based on patient's name
8. System generates a default password based on policy
9. User account is created and linked to the patient
10. User is redirected to the patient details page
11. Success message is displayed with patient creation confirmation

## Potential Improvements

1. **Form Streamlining**:
   - Focus on essential fields first
   - Remove duplicate fields across tabs (e.g., medical history appears in both Basic Information and Medical Information tabs)

2. **User Account Creation**:
   - Improve error handling for user account creation
   - Enhance feedback to the user when a patient is created with their account details
   - Add option to customize username or initial password

3. **Validation and Data Quality**:
   - Add more comprehensive validation for medical data
   - Implement age-appropriate reference ranges for vital signs and lab results

4. **User Experience**:
   - Add progress saving for partially completed forms
   - Implement a simpler quick-add option for emergency situations
