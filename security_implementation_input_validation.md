# Input Validation & Output Encoding Implementation

This document outlines the implementation of consistent input validation, output encoding, and Content Security Policy (CSP) headers in the medical application system.

## 1. Input Validation

### Implementation Overview

Input validation is implemented using the `express-validator` library with a centralized validation schema approach. This ensures consistent validation rules across all routes and provides a standardized way to handle validation errors.

### Key Components

#### Validation Schemas

Centralized validation schemas are defined in `server/utils/validation.js`:

```javascript
const validationSchemas = {
  // User validation
  user: {
    create: [
      check('username', 'Username is required').not().isEmpty().trim().escape(),
      check('email', 'Please include a valid email').isEmail().normalizeEmail(),
      check('password', 'Password must be at least 8 characters').isLength({ min: 8 }),
      check('role').optional().isIn(['admin', 'doctor', 'staff', 'patient', 'kin', 'researcher', 'user']),
    ],
    // Other user validation schemas...
  },
  
  // Patient validation
  patient: {
    create: [
      check('first_name', 'First name is required').not().isEmpty().trim().escape(),
      check('last_name', 'Last name is required').not().isEmpty().trim().escape(),
      check('date_of_birth', 'Valid date of birth is required').isDate(),
      // Other patient fields...
    ],
    // Other patient validation schemas...
  },
  
  // Visit validation, Prescription validation, etc.
  // ...
};
```

#### Validation Middleware

A reusable validation middleware function that can be applied to any route:

```javascript
const validate = (validations) => {
  return async (req, res, next) => {
    await Promise.all(validations.map(validation => validation.run(req)));
    
    const errors = validationResult(req);
    if (errors.isEmpty()) {
      return next();
    }
    
    return res.status(400).json({ 
      errors: errors.array().map(err => ({
        param: err.param,
        msg: err.msg,
        location: err.location
      }))
    });
  };
};
```

### Usage in Routes

The validation middleware is applied to routes using the following pattern:

```javascript
router.post('/', [auth, validate(validationSchemas.patient.create)], async (req, res) => {
  // Route handler code...
});
```

For routes with URL parameters:

```javascript
router.get('/:id', [auth, validate(validationSchemas.idParam)], async (req, res) => {
  // Route handler code...
});
```

For routes with both URL parameters and request body:

```javascript
router.put('/:id', [
  auth, 
  validate([...validationSchemas.idParam, ...validationSchemas.patient.update])
], async (req, res) => {
  // Route handler code...
});
```

## 2. Output Encoding

### Implementation Overview

Output encoding is implemented using a middleware that intercepts all JSON responses and sanitizes the data before sending it to the client. This prevents XSS attacks by ensuring that any potentially malicious content is properly encoded.

### Key Components

#### Sanitization Function

A utility function that sanitizes content recursively:

```javascript
const sanitizeContent = (content) => {
  if (!content) return content;
  
  if (typeof content === 'string') {
    return sanitizeHtml(content, {
      allowedTags: ['b', 'i', 'em', 'strong', 'p', 'br'],
      allowedAttributes: {},
    });
  }
  
  if (typeof content === 'object') {
    const sanitized = {};
    for (const [key, value] of Object.entries(content)) {
      if (typeof value === 'string') {
        sanitized[key] = sanitizeHtml(value, {
          allowedTags: ['b', 'i', 'em', 'strong', 'p', 'br'],
          allowedAttributes: {},
        });
      } else if (typeof value === 'object' && value !== null) {
        sanitized[key] = sanitizeContent(value);
      } else {
        sanitized[key] = value;
      }
    }
    return sanitized;
  }
  
  return content;
};
```

#### Output Encoding Middleware

A middleware that intercepts all JSON responses:

```javascript
const outputEncoder = (req, res, next) => {
  // Store the original json method
  const originalJson = res.json;
  
  // Override the json method to sanitize the output
  res.json = function(data) {
    // Skip sanitization for certain endpoints that need raw data
    const skipEndpoints = [
      '/api/auth/token', // JWT tokens should not be sanitized
      '/api/settings/export', // Data exports should not be sanitized
    ];
    
    if (skipEndpoints.some(endpoint => req.path.startsWith(endpoint))) {
      return originalJson.call(this, data);
    }
    
    // Sanitize the data
    const sanitizedData = sanitizeContent(data);
    
    // Call the original json method with sanitized data
    return originalJson.call(this, sanitizedData);
  };
  
  next();
};
```

### Usage in Server

The output encoding middleware is applied globally in the server.js file:

```javascript
// Apply output encoding middleware
app.use(outputEncoder);
```

## 3. Content Security Policy (CSP)

### Implementation Overview

Content Security Policy headers are implemented using a middleware that sets various security headers for all responses. This helps prevent XSS attacks, clickjacking, and other security vulnerabilities.

### Key Components

#### Security Headers Middleware

A middleware that sets various security headers:

```javascript
const securityHeaders = (req, res, next) => {
  // Content Security Policy
  res.setHeader(
    'Content-Security-Policy',
    [
      "default-src 'self'",
      "script-src 'self' https://cdn.jsdelivr.net https://unpkg.com 'unsafe-inline'",
      "style-src 'self' https://fonts.googleapis.com https://cdn.jsdelivr.net 'unsafe-inline'",
      "font-src 'self' https://fonts.gstatic.com data:",
      "img-src 'self' data: blob:",
      "connect-src 'self'",
      "media-src 'self'",
      "object-src 'none'",
      "frame-src 'self'",
      "worker-src 'self' blob:",
      "form-action 'self'",
      "base-uri 'self'",
      "frame-ancestors 'self'",
      "upgrade-insecure-requests",
    ].join('; ')
  );
  
  // Other security headers
  res.setHeader('X-Content-Type-Options', 'nosniff');
  res.setHeader('X-Frame-Options', 'SAMEORIGIN');
  res.setHeader('X-XSS-Protection', '1; mode=block');
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  res.setHeader(
    'Permissions-Policy',
    'camera=(), microphone=(), geolocation=(self), payment=()'
  );
  
  // Cache control for sensitive pages
  if (req.path.includes('/api/auth/') || req.path.includes('/api/admin/')) {
    res.setHeader('Cache-Control', 'no-store, max-age=0');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
  }
  
  next();
};
```

### Usage in Server

The security headers middleware is applied globally in the server.js file:

```javascript
// Apply security headers middleware
app.use(securityHeaders);
```

## Testing the Implementation

### Input Validation Testing

1. Send invalid data to an endpoint and verify that it returns a 400 Bad Request with appropriate error messages.
2. Send valid data to an endpoint and verify that it processes the request correctly.

### Output Encoding Testing

1. Send data with potentially malicious content (e.g., HTML tags, JavaScript code) to an endpoint.
2. Verify that the response contains properly encoded content.

### CSP Testing

1. Use browser developer tools to inspect the security headers of responses.
2. Verify that the Content-Security-Policy header is present with the expected directives.
3. Try to inject inline scripts and verify that they are blocked by the CSP.

## Security Considerations

### Input Validation

- Always validate input on the server side, even if client-side validation is implemented.
- Use appropriate validation rules for each field based on its expected format and constraints.
- Consider using a combination of validation and sanitization for fields that accept rich text.

### Output Encoding

- Be careful not to over-sanitize data that legitimately contains HTML (e.g., rich text content).
- Consider context-specific encoding for different output contexts (HTML, JavaScript, CSS, etc.).
- Regularly review and update the list of allowed HTML tags and attributes.

### Content Security Policy

- Start with a strict policy and relax it only as needed.
- Regularly review and update the CSP directives as the application evolves.
- Consider using CSP reporting to monitor policy violations.
- Test the CSP thoroughly to ensure it doesn't break legitimate functionality.

## Conclusion

This implementation provides a comprehensive approach to input validation, output encoding, and Content Security Policy in the medical application system. By centralizing validation logic, automatically encoding output, and implementing strict security headers, the application is better protected against common web security vulnerabilities such as XSS, CSRF, and clickjacking.
