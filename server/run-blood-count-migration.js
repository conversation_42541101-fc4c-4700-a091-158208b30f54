const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

// Create a new pool using the individual connection parameters from .env
const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

async function runMigration() {
  const client = await pool.connect();
  
  try {
    console.log('Starting migration to ensure blood count fields...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'migrations', '033_ensure_blood_count_fields.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    await client.query(sql);
    
    console.log('✅ Successfully executed migration');
    console.log('All blood count fields have been verified and added if needed.');
  } catch (error) {
    console.error('❌ Error executing migration:', error);
  } finally {
    client.release();
    console.log('Database connection closed');
    pool.end();
  }
}

runMigration();
