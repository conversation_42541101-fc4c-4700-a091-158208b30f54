const { Pool } = require('pg');
require('dotenv').config();

// Configure PostgreSQL to use date/time without timezone adjustment
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'medapp',
  // Disable timezone conversion in the driver
  types: {
    getTypeParser: function(oid, format) {
      // Override the date/timestamp parsers to return strings instead of Date objects
      // This prevents any automatic timezone conversion
      if (oid === 1082 || oid === 1114 || oid === 1184) { // DATE, TIMESTAMP, TIMESTAMPTZ
        return value => value; // Return raw string value
      }
      // Use the default parser for other types
      return require('pg-types').getTypeParser(oid, format);
    }
  },
  // Add connection pool optimization
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // How long a client is allowed to remain idle before being closed
  connectionTimeoutMillis: 2000 // How long to wait for a connection to become available
});

// Set timezone to UTC for all connections
pool.on('connect', async (client) => {
  try {
    // Disable timezone conversion in the database session
    await client.query("SET timezone TO 'UTC'");
    console.log('Connected to database with timezone set to UTC (to prevent conversions)');
  } catch (err) {
    console.error('Error setting session parameters:', err);
  }
});

module.exports = pool;