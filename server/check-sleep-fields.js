/**
 * <PERSON><PERSON><PERSON> to check sleep-related fields in the database
 */

const { Pool } = require('pg');
require('dotenv').config();

// Create a connection to the database
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'medapp',
  password: process.env.DB_PASSWORD || '',
  port: process.env.DB_PORT || 5432,
});

async function checkSleepFields() {
  try {
    console.log('Checking sleep-related fields in the database...');

    // Check patients table
    const patientsQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'patients' 
      AND column_name LIKE '%sleep%'
    `;
    const patientsResult = await pool.query(patientsQuery);
    
    console.log('Sleep-related fields in patients table:');
    if (patientsResult.rows.length === 0) {
      console.log('  No sleep-related fields found');
    } else {
      patientsResult.rows.forEach(row => {
        console.log(`  ${row.column_name}`);
      });
    }

    // Check patient_visits table
    const visitsQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'patient_visits' 
      AND column_name LIKE '%sleep%'
    `;
    const visitsResult = await pool.query(visitsQuery);
    
    console.log('\nSleep-related fields in patient_visits table:');
    if (visitsResult.rows.length === 0) {
      console.log('  No sleep-related fields found');
    } else {
      visitsResult.rows.forEach(row => {
        console.log(`  ${row.column_name}`);
      });
    }

    // Check specifically for sleep_patterns field
    const sleepPatternsQuery = `
      SELECT 
        (SELECT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'patients' AND column_name = 'sleep_patterns')) as in_patients,
        (SELECT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'patient_visits' AND column_name = 'sleep_patterns')) as in_visits
    `;
    const sleepPatternsResult = await pool.query(sleepPatternsQuery);
    
    console.log('\nSpecific check for sleep_patterns field:');
    console.log(`  Exists in patients table: ${sleepPatternsResult.rows[0].in_patients}`);
    console.log(`  Exists in patient_visits table: ${sleepPatternsResult.rows[0].in_visits}`);

  } catch (err) {
    console.error('❌ Error checking sleep fields:', err);
  } finally {
    // Close the pool
    await pool.end();
    console.log('\nDatabase connection closed');
  }
}

// Run the function
checkSleepFields();
