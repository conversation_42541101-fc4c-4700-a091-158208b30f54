{"name": "server", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1", "set-nepal-timezone": "node scripts/set_nepal_timezone.js", "generate-certs": "node scripts/generate_certs.js"}, "keywords": [], "author": "", "license": "ISC", "description": "Medical App Backend API", "dependencies": {"@mui/x-date-pickers": "^8.1.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "date-fns": "^4.1.0", "dotenv": "^16.0.3", "express": "^4.18.2", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.0", "pg": "^8.10.0", "pg-types": "^4.0.2"}, "devDependencies": {"nodemon": "^2.0.22"}}