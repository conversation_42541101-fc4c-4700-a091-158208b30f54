/**
 * <PERSON><PERSON><PERSON> to run the migration for updating cognitive health fields
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Create a connection to the database
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'medapp',
  password: process.env.DB_PASSWORD || '',
  port: process.env.DB_PORT || 5432,
});

async function runMigration() {
  try {
    console.log('Starting migration to update cognitive health fields...');

    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'migrations', '018_update_cognitive_fields.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');

    // Execute the SQL
    const result = await pool.query(sql);

    console.log('✅ Successfully executed migration');
    console.log('Cognitive health fields have been updated: added cognitive_impairment_score and removed cognitive_status.');

    // Log any messages from the migration
    if (result && result.rows && result.rows.length > 0) {
      console.log('Migration results:');
      result.rows.forEach(row => {
        console.log(row);
      });
    }

  } catch (err) {
    console.error('❌ Error running migration:', err);
  } finally {
    // Close the pool
    await pool.end();
    console.log('Database connection closed');
  }
}

// Run the function
runMigration();
