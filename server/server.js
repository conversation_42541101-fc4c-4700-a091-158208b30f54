const express = require('express');
const cors = require('cors');
const db = require('./db');
const { check, validationResult } = require('express-validator');
const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Initialize Express
const app = express();

// Use UTC timezone to prevent any automatic conversions
process.env.TZ = 'UTC';
console.log('Application timezone set to:', process.env.TZ);

// Initialize database connection

(async () => {
  try {
    const client = await db.connect();
    console.log('Database connection established successfully');
    client.release();

    // Run necessary SQL scripts to ensure tables exist
    const runSqlScript = async (scriptPath) => {
      try {
        const fullPath = path.join(__dirname, scriptPath);
        if (fs.existsSync(fullPath)) {
          const sql = fs.readFileSync(fullPath, 'utf8');
          await db.query(sql);
          console.log(`Successfully executed SQL script: ${scriptPath}`);
        } else {
          console.warn(`SQL script not found: ${scriptPath}`);
        }
      } catch (error) {
        console.error(`Error executing SQL script ${scriptPath}:`, error);
      }
    };

    // Run scripts asynchronously
    await runSqlScript('sql/create_messages_table.sql');
    await runSqlScript('sql/create_system_logs_table.sql');

  } catch (err) {
    console.error('Error connecting to database:', err);
    process.exit(1); // Exit the process if database connection fails
  }
})();

// Middleware
// Configure CORS with more options
app.use(cors({
  origin: '*', // Allow all origins
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'], // Allow these HTTP methods
  allowedHeaders: ['Content-Type', 'x-auth-token', 'Authorization'] // Allow these headers
}));

// Handle preflight requests
app.options('*', cors());

app.use(express.json({ limit: '1mb' })); // For parsing JSON request bodies with size limit
app.use(express.urlencoded({ extended: true, limit: '1mb' })); // For parsing URL-encoded bodies

// Add request logging middleware with more details
app.use((req, res, next) => {
  const start = Date.now();
  res.on('finish', () => {
    const duration = Date.now() - start;
    console.log(
      `[${new Date().toISOString()}] ${req.method} ${req.url} ${res.statusCode} ${duration}ms`
    );
  });
  next();
});



// Import routes
const authRoutes = require('./routes/auth');
const usersRoutes = require('./routes/users');
const patientsRoutes = require('./routes/patients');
const recordsRoutes = require('./routes/records');
const doctorsRoutes = require('./routes/doctors');
const visitsRoutes = require('./routes/visits');
const logsRoutes = require('./routes/logs');
const adminRoutes = require('./routes/admin');
const appointmentsRoutes = require('./routes/appointments');
const debugRoutes = require('./routes/debug');
const beersCriteriaRoutes = require('./routes/beersCriteria');
const prescriptionsRoutes = require('./routes/prescriptions');
const settingsRoutes = require('./routes/settings');
const clinicalGuidanceRoutes = require('./routes/clinicalGuidance');
const mfaRoutes = require('./routes/mfa');
const integrationsRoutes = require('./routes/integrations');

const kinRoutes = require('./routes/kin');
const messagesRoutes = require('./routes/messages');

// Import middleware
const ipCheck = require('./middleware/ipCheck');
const securityHeaders = require('./middleware/securityHeaders');
const outputEncoder = require('./middleware/outputEncoder');
const { sanitizeBody } = require('./utils/validation');

// Routes
// Auth routes don't use IP check to prevent lockouts
app.use('/api/auth', authRoutes);

// Apply IP check middleware to all other routes
app.use('/api/users', ipCheck, usersRoutes);
app.use('/api/patients', ipCheck, patientsRoutes);
app.use('/api/records', ipCheck, recordsRoutes);
app.use('/api/doctors', ipCheck, doctorsRoutes);
app.use('/api/visits', ipCheck, visitsRoutes);
app.use('/api/logs', ipCheck, logsRoutes);
app.use('/api/admin', ipCheck, adminRoutes);
app.use('/api/appointments', ipCheck, appointmentsRoutes);
app.use('/api/debug', ipCheck, debugRoutes);
app.use('/api/prescriptions', ipCheck, prescriptionsRoutes);
app.use('/api/settings', ipCheck, settingsRoutes);
app.use('/api/mfa', ipCheck, mfaRoutes);

app.use('/api/kin', ipCheck, kinRoutes);
app.use('/api/messages', ipCheck, messagesRoutes);
app.use('/api/beers-criteria', ipCheck, beersCriteriaRoutes);
app.use('/api/clinical-guidance', ipCheck, clinicalGuidanceRoutes);
app.use('/api/integrations', ipCheck, integrationsRoutes);

// Default route
app.get('/', (req, res) => {
  res.send('Medical App API is running');
});

// Global error handling middleware (must be after routes)
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({
    error: 'Server error',
    message: process.env.NODE_ENV === 'development' ? err.message : 'An unexpected error occurred'
  });
});

// HTTPS Configuration
const httpsOptions = {
  key: fs.existsSync(path.join(__dirname, 'certs/key.pem'))
    ? fs.readFileSync(path.join(__dirname, 'certs/key.pem'))
    : null,
  cert: fs.existsSync(path.join(__dirname, 'certs/cert.pem'))
    ? fs.readFileSync(path.join(__dirname, 'certs/cert.pem'))
    : null
};

// Apply security headers middleware (includes HSTS)
app.use(securityHeaders);

// Apply input sanitization middleware
app.use(sanitizeBody);

// Apply output encoding middleware
app.use(outputEncoder);

// HTTP to HTTPS redirect middleware
const redirectToHttps = (req, res, next) => {
  if (!req.secure && process.env.NODE_ENV !== 'development') {
    return res.redirect(`https://${req.headers.host}${req.url}`);
  }
  next();
};

// Start servers
const HTTP_PORT = process.env.HTTP_PORT || 8080;
const HTTPS_PORT = process.env.HTTPS_PORT || 8443;
const FALLBACK_HTTP_PORT = 9090; // Fallback port if primary is in use
const FALLBACK_HTTPS_PORT = 9443; // Fallback port if primary is in use

// Determine if we can use HTTPS
const useHttps = httpsOptions.key && httpsOptions.cert;

let httpServer, httpsServer;
let actualHttpPort = HTTP_PORT;
let actualHttpsPort = HTTPS_PORT;

// Always create HTTP server
httpServer = http.createServer(app);

// Function to start HTTP server with fallback
const startHttpServer = (port, fallbackPort) => {
  return new Promise((resolve) => {
    httpServer.listen(port)
      .on('listening', () => {
        console.log(`HTTP Server running on port ${port}`);
        resolve(port);
      })
      .on('error', (err) => {
        if (err.code === 'EADDRINUSE') {
          console.log(`Port ${port} is already in use, trying fallback port ${fallbackPort}`);
          httpServer.listen(fallbackPort)
            .on('listening', () => {
              console.log(`HTTP Server running on fallback port ${fallbackPort}`);
              resolve(fallbackPort);
            })
            .on('error', (fallbackErr) => {
              console.error(`Failed to start HTTP server on both ports: ${port} and ${fallbackPort}`);
              console.error(fallbackErr);
              resolve(null);
            });
        } else {
          console.error('HTTP server error:', err);
          resolve(null);
        }
      });
  });
};

// Function to start HTTPS server with fallback
const startHttpsServer = (port, fallbackPort) => {
  return new Promise((resolve) => {
    if (!useHttps) {
      resolve(null);
      return;
    }

    httpsServer = https.createServer(httpsOptions, app);

    httpsServer.listen(port)
      .on('listening', () => {
        console.log(`HTTPS Server running on port ${port}`);
        resolve(port);
      })
      .on('error', (err) => {
        if (err.code === 'EADDRINUSE') {
          console.log(`Port ${port} is already in use, trying fallback port ${fallbackPort}`);
          httpsServer.listen(fallbackPort)
            .on('listening', () => {
              console.log(`HTTPS Server running on fallback port ${fallbackPort}`);
              resolve(fallbackPort);
            })
            .on('error', (fallbackErr) => {
              console.error(`Failed to start HTTPS server on both ports: ${port} and ${fallbackPort}`);
              console.error(fallbackErr);
              resolve(null);
            });
        } else {
          console.error('HTTPS server error:', err);
          resolve(null);
        }
      });
  });
};

// Start both servers with async/await
(async () => {
  // Start HTTPS server first if available
  if (useHttps) {
    actualHttpsPort = await startHttpsServer(HTTPS_PORT, FALLBACK_HTTPS_PORT);

    // In production, HTTP server just redirects to HTTPS
    if (process.env.NODE_ENV === 'production' && actualHttpsPort) {
      app.use((req, res, next) => {
        if (!req.secure) {
          return res.redirect(`https://${req.headers.host.split(':')[0]}:${actualHttpsPort}${req.url}`);
        }
        next();
      });
    }
  }

  // Then start HTTP server
  actualHttpPort = await startHttpServer(HTTP_PORT, FALLBACK_HTTP_PORT);

  // Log server status
  console.log(`Server time (UTC): ${new Date().toISOString()}`);

  if (useHttps) {
    if (actualHttpsPort) {
      console.log(`HTTPS server is available at https://localhost:${actualHttpsPort}`);
    } else {
      console.log('HTTPS server failed to start');
    }
  } else {
    console.log('HTTPS not configured - certificates not found');
    console.log('To enable HTTPS, create key.pem and cert.pem in the server/certs directory');
  }

  if (actualHttpPort) {
    console.log(`HTTP server is available at http://localhost:${actualHttpPort}`);
  } else {
    console.log('HTTP server failed to start');
  }
})();

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.error('Unhandled Promise Rejection:', err);
  // Close servers & exit process
  if (httpServer) httpServer.close();
  if (httpsServer) httpsServer.close();
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('Uncaught Exception:', err);
  // Close servers & exit process
  if (httpServer) httpServer.close();
  if (httpsServer) httpsServer.close();
  process.exit(1);
});