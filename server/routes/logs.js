const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const adminCheck = require('../middleware/adminCheck');
const PatientAccessLog = require('../models/PatientAccessLog');
const PatientEditLog = require('../models/PatientEditLog');
const LoginActivity = require('../models/LoginActivity');
const SystemLog = require('../models/SystemLog');

// @route   GET api/logs/access
// @desc    Get all access logs (admin only)
// @access  Private/Admin
router.get('/access', [auth, adminCheck], async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;

    const logsData = await PatientAccessLog.getAll(page, limit);

    res.json(logsData.logs);
  } catch (err) {
    console.error('Error fetching all access logs:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET api/logs/access/patient/:patientId
// @desc    Get access logs for a specific patient
// @access  Private
router.get('/access/patient/:patientId', auth, async (req, res) => {
  try {
    const logs = await PatientAccessLog.getByPatientId(req.params.patientId);
    res.json(logs);
  } catch (err) {
    console.error('Error fetching patient access logs:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET api/logs/access/user/:userId
// @desc    Get access logs by user (admin only)
// @access  Private/Admin
router.get('/access/user/:userId', [auth, adminCheck], async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;

    const logs = await PatientAccessLog.getByUserId(req.params.userId, page, limit);

    res.json(logs);
  } catch (err) {
    console.error('Error fetching user access logs:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET api/logs/edit
// @desc    Get all edit logs (admin only)
// @access  Private/Admin
router.get('/edit', [auth, adminCheck], async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;

    const logsData = await PatientEditLog.getAll(page, limit);

    res.json(logsData.logs);
  } catch (err) {
    console.error('Error fetching all edit logs:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET api/logs/edit/patient/:patientId
// @desc    Get edit logs for a specific patient
// @access  Private
router.get('/edit/patient/:patientId', auth, async (req, res) => {
  try {
    const logs = await PatientEditLog.getByPatientId(req.params.patientId);
    res.json(logs);
  } catch (err) {
    console.error('Error fetching patient edit logs:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET api/logs/edit/field/:fieldName
// @desc    Get edit logs by field name (admin only)
// @access  Private/Admin
router.get('/edit/field/:fieldName', [auth, adminCheck], async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;

    const logs = await PatientEditLog.getByField(req.params.fieldName, page, limit);

    res.json(logs);
  } catch (err) {
    console.error('Error fetching field edit logs:', err);
    res.status(500).json({ message: 'Server error' });
  }
});

// @route   GET api/logs/login-activity
// @desc    Get all login activity logs (admin only)
// @access  Private/Admin
router.get('/login-activity', [auth, adminCheck], async (req, res) => {
  try {
    const page = parseInt(req.query.page, 10) || 1; // Default to page 1 for consistency
    const limit = parseInt(req.query.limit, 10) || 10;

    console.log('GET /api/logs/login-activity called with page:', page, 'limit:', limit);

    const loginLogs = await LoginActivity.getAll(page, limit);
    console.log('Login logs retrieved:', loginLogs.logs.length, 'total:', loginLogs.total);

    res.json(loginLogs);
  } catch (err) {
    console.error('Error retrieving login activity logs:', err.message);
    res.status(500).json({ message: 'Server Error', error: err.message });
  }
});

// @route   GET api/logs/login-activity/user/:userId
// @desc    Get login activity logs for a specific user (admin only)
// @access  Private/Admin
router.get('/login-activity/user/:userId', [auth, adminCheck], async (req, res) => {
  try {
    const page = parseInt(req.query.page, 10) || 1; // Default to page 1 for consistency
    const limit = parseInt(req.query.limit, 10) || 10;

    console.log('GET /api/logs/login-activity/user/:userId called with userId:', req.params.userId, 'page:', page, 'limit:', limit);

    const loginLogs = await LoginActivity.getByUserId(req.params.userId, page, limit);
    console.log('User login logs retrieved:', loginLogs.logs.length, 'total:', loginLogs.total);

    res.json(loginLogs);
  } catch (err) {
    console.error('Error retrieving login activity logs for user:', err.message);
    res.status(500).json({ message: 'Server Error', error: err.message });
  }
});

// @route   GET api/logs/login-activity/stats
// @desc    Get login activity statistics (admin only)
// @access  Private/Admin
router.get('/login-activity/stats', [auth, adminCheck], async (req, res) => {
  try {
    console.log('GET /api/logs/login-activity/stats called');

    const stats = await LoginActivity.getStats();
    console.log('Login stats retrieved:', stats);

    res.json(stats);
  } catch (err) {
    console.error('Error retrieving login activity statistics:', err.message);
    res.status(500).json({ message: 'Server Error', error: err.message });
  }
});

// @route   GET api/logs/login-activity/geo-stats
// @desc    Get geographic statistics for login attempts (admin only)
// @access  Private/Admin
router.get('/login-activity/geo-stats', [auth, adminCheck], async (req, res) => {
  try {
    console.log('GET /api/logs/login-activity/geo-stats called');

    const geoStats = await LoginActivity.getGeoStats();
    console.log('Login geo stats retrieved:', {
      countries: geoStats.countries.length,
      locations: geoStats.locations.length
    });

    res.json(geoStats);
  } catch (err) {
    console.error('Error retrieving login geographic statistics:', err.message);
    res.status(500).json({ message: 'Server Error', error: err.message });
  }
});

// @route   GET api/logs/system
// @desc    Get all system logs (admin only)
// @access  Private/Admin
router.get('/system', [auth, adminCheck], async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;

    console.log('GET /api/logs/system called with page:', page, 'limit:', limit);

    const logsData = await SystemLog.getAll(page, limit);
    console.log('System logs retrieved:', logsData.logs.length, 'total:', logsData.totalLogs);

    res.json(logsData);
  } catch (err) {
    console.error('Error retrieving system logs:', err.message);
    res.status(500).json({ message: 'Server Error', error: err.message });
  }
});

// @route   GET api/logs/system/type/:logType
// @desc    Get system logs by type (admin only)
// @access  Private/Admin
router.get('/system/type/:logType', [auth, adminCheck], async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const logType = req.params.logType;

    console.log('GET /api/logs/system/type/:logType called with logType:', logType, 'page:', page, 'limit:', limit);

    const logsData = await SystemLog.getByType(logType, page, limit);
    console.log('System logs retrieved for type', logType, ':', logsData.logs.length, 'total:', logsData.totalLogs);

    res.json(logsData);
  } catch (err) {
    console.error('Error retrieving system logs by type:', err.message);
    res.status(500).json({ message: 'Server Error', error: err.message });
  }
});

// @route   GET api/logs/system/user/:userId
// @desc    Get system logs by user (admin only)
// @access  Private/Admin
router.get('/system/user/:userId', [auth, adminCheck], async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const userId = req.params.userId;

    console.log('GET /api/logs/system/user/:userId called with userId:', userId, 'page:', page, 'limit:', limit);

    const logsData = await SystemLog.getByUser(userId, page, limit);
    console.log('System logs retrieved for user', userId, ':', logsData.logs.length, 'total:', logsData.totalLogs);

    res.json(logsData);
  } catch (err) {
    console.error('Error retrieving system logs by user:', err.message);
    res.status(500).json({ message: 'Server Error', error: err.message });
  }
});

module.exports = router;