const express = require('express');
const router = express.Router();
const Doctor = require('../models/Doctor');
const auth = require('../middleware/auth');
const pool = require('../db');

// Get dashboard statistics for a doctor
router.get('/dashboard-stats/:doctorId', auth, async (req, res) => {
  try {
    const doctorId = req.params.doctorId;
    console.log('Fetching dashboard stats for doctor:', doctorId);
    console.log('Request user:', req.user);

    // Get total number of patients in the system
    const totalPatientsResult = await pool.query(
      'SELECT COUNT(DISTINCT patient_id) FROM patients'
    );
    const totalPatients = parseInt(totalPatientsResult.rows[0].count);
    console.log('Total patients query result:', totalPatientsResult.rows[0]);

    // Get number of patients assigned to this specific doctor
    const assignedPatientsResult = await pool.query(
      `SELECT COUNT(DISTINCT patient_id)
       FROM patients
       WHERE doctor_id = $1`,
      [doctorId]
    );
    const assignedPatients = parseInt(assignedPatientsResult.rows[0].count);
    console.log('Assigned patients query result:', assignedPatientsResult.rows[0]);

    console.log('Total patients in system:', totalPatients);
    console.log('Assigned patients to doctor:', assignedPatients);
    console.log('Final response:', { totalPatients, assignedPatients });

    res.json({
      totalPatients,
      assignedPatients
    });
  } catch (err) {
    console.error('Error in dashboard-stats:', err.message);
    console.error('Error details:', err);
    res.status(500).send('Server error');
  }
});

// Get upcoming appointments for the week
router.get('/upcoming-appointments/:doctorId', auth, async (req, res) => {
  try {
    const { doctorId } = req.params;
    console.log('Fetching appointments for doctor:', doctorId);

    const query = `
      SELECT
        v.visit_id as appointment_id,
        p.patient_id,
        CONCAT(p.first_name, ' ', p.last_name) as patient_name,
        v.visit_date::text as date,
        v.visit_date::time::text as start_time,
        v.end_time::text as end_time,
        v.visit_reason as reason,
        v.status,
        v.notes
      FROM patient_visits v
      JOIN patients p ON v.patient_id = p.patient_id
      WHERE v.doctor_id = $1
        AND v.visit_date::date >= CURRENT_DATE
        AND v.visit_date::date <= CURRENT_DATE + INTERVAL '7 days'
        AND (v.status = 'scheduled' OR v.status IS NULL)
      ORDER BY v.visit_date ASC
    `;

    console.log('Current date:', new Date().toISOString());

    console.log('Executing query:', query);
    console.log('With doctor_id:', doctorId);

    const result = await pool.query(query, [doctorId]);

    // Log each appointment with patient ID for debugging
    console.log(`Found ${result.rows.length} appointments for doctor ${doctorId}`);

    if (result.rows.length === 0) {
      console.log('No appointments found. This could be due to:');
      console.log('1. No appointments exist for this doctor');
      console.log('2. All appointments are in the past');
      console.log('3. All appointments are more than 7 days in the future');
      console.log('4. All appointments have a status other than "scheduled"');
    } else {
      result.rows.forEach((appointment, index) => {
        console.log(`Appointment ${index + 1} details:`, {
          appointment_id: appointment.appointment_id,
          patient_id: appointment.patient_id,
          patient_name: appointment.patient_name,
          date: appointment.date,
          start_time: appointment.start_time || (appointment.date ? new Date(appointment.date).toTimeString().split(' ')[0] : null),
          end_time: appointment.end_time,
          status: appointment.status,
          reason: appointment.reason,
          notes: appointment.notes ? (appointment.notes.length > 50 ? appointment.notes.substring(0, 50) + '...' : appointment.notes) : null
        });

        // Parse the date to check if it's valid
        try {
          const parsedDate = new Date(appointment.date);
          console.log(`  - Parsed date: ${parsedDate.toISOString()}`);
          console.log(`  - Is future date: ${parsedDate > new Date()}`);
          console.log(`  - Days from now: ${Math.round((parsedDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))}`);
        } catch (err) {
          console.log(`  - Error parsing date: ${err.message}`);
        }
      });
    }

    console.log('Raw appointment data:', result.rows);

    res.json(result.rows);
  } catch (err) {
    console.error('Error in upcoming-appointments:', err.message);
    res.status(500).send('Server error');
  }
});

// Get detailed dashboard statistics for a doctor
router.get('/detailed-dashboard-stats/:doctorId', auth, async (req, res) => {
  try {
    const doctorId = req.params.doctorId;
    console.log('Fetching detailed dashboard stats for doctor:', doctorId);

    // Get total number of patients in the system
    const totalPatientsResult = await pool.query(
      'SELECT COUNT(DISTINCT patient_id) FROM patients'
    );
    const totalPatients = parseInt(totalPatientsResult.rows[0].count);

    // Get number of patients officially assigned to this specific doctor
    const assignedPatientsResult = await pool.query(
      `SELECT COUNT(DISTINCT patient_id)
       FROM patients
       WHERE doctor_id = $1`,
      [doctorId]
    );
    const assignedPatients = parseInt(assignedPatientsResult.rows[0].count);

    // Get number of patients who have had visits with this doctor
    const patientsWithVisitsResult = await pool.query(
      `SELECT COUNT(DISTINCT patient_id)
       FROM patient_visits
       WHERE doctor_id = $1`,
      [doctorId]
    );
    const patientsWithVisits = parseInt(patientsWithVisitsResult.rows[0].count);

    // Get total number of visits conducted by this doctor
    const totalVisitsResult = await pool.query(
      `SELECT COUNT(*)
       FROM patient_visits
       WHERE doctor_id = $1`,
      [doctorId]
    );
    const totalVisits = parseInt(totalVisitsResult.rows[0].count);

    // Get appointments count for this doctor
    const appointmentsResult = await pool.query(
      `SELECT COUNT(*)
       FROM appointments
       WHERE doctor_id = $1`,
      [doctorId]
    );
    const appointmentsCount = parseInt(appointmentsResult.rows[0].count || 0);

    // Get upcoming appointments for this doctor
    const upcomingAppointmentsResult = await pool.query(
      `SELECT COUNT(*)
       FROM appointments
       WHERE doctor_id = $1 AND appointment_date >= CURRENT_DATE`,
      [doctorId]
    );
    const upcomingAppointments = parseInt(upcomingAppointmentsResult.rows[0].count || 0);

    const stats = {
      totalPatients,
      assignedPatients,
      patientsWithVisits,
      totalVisits,
      appointmentsCount,
      upcomingAppointments
    };

    console.log('Detailed stats:', stats);
    res.json(stats);
  } catch (err) {
    console.error('Error in detailed-dashboard-stats:', err.message);
    console.error('Error details:', err);
    res.status(500).send('Server error');
  }
});

// @route   GET api/doctors
// @desc    Get all doctors
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const doctors = await Doctor.getAll();
    res.json(doctors);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   POST api/doctors
// @desc    Create a new doctor
// @access  Private (Admin only)
router.post('/', auth, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ msg: 'Not authorized to add doctors' });
    }

    const doctor = await Doctor.create(req.body);
    res.status(201).json(doctor);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   GET api/doctors/:id/patients
// @desc    Get all patients for a doctor
// @access  Private
router.get('/:id/patients', auth, async (req, res) => {
  try {
    // Check if doctor exists
    const doctor = await Doctor.getById(req.params.id);
    if (!doctor) {
      return res.status(404).json({ msg: 'Doctor not found' });
    }

    const patients = await Doctor.getPatients(req.params.id);
    res.json(patients);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   GET api/doctors/:id
// @desc    Get doctor by ID
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const doctor = await Doctor.getById(req.params.id);

    if (!doctor) {
      return res.status(404).json({ msg: 'Doctor not found' });
    }

    res.json(doctor);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   PUT api/doctors/:id
// @desc    Update doctor
// @access  Private (Admin only)
router.put('/:id', auth, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ msg: 'Not authorized to update doctors' });
    }

    // Check if doctor exists
    let doctor = await Doctor.getById(req.params.id);
    if (!doctor) {
      return res.status(404).json({ msg: 'Doctor not found' });
    }

    // Update doctor
    doctor = await Doctor.update(req.params.id, req.body);
    res.json(doctor);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   DELETE api/doctors/:id
// @desc    Delete doctor
// @access  Private (Admin only)
router.delete('/:id', auth, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ msg: 'Not authorized to delete doctors' });
    }

    // Check if doctor exists
    const doctor = await Doctor.getById(req.params.id);
    if (!doctor) {
      return res.status(404).json({ msg: 'Doctor not found' });
    }

    // Delete doctor
    await Doctor.delete(req.params.id);
    res.json({ msg: 'Doctor removed' });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

module.exports = router;