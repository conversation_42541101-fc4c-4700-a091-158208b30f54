const express = require('express');
const router = express.Router();
const { check, validationResult } = require('express-validator');
const auth = require('../middleware/auth');
const adminCheck = require('../middleware/adminCheck');
const ApiKey = require('../models/ApiKey');
const Integration = require('../models/Integration');
const ExternalSystem = require('../models/ExternalSystem');
const IntegrationLog = require('../models/IntegrationLog');
const logger = require('../utils/logger');

// ===== API Keys Routes =====

/**
 * @route   GET api/integrations/api-keys
 * @desc    Get all API keys
 * @access  Public (temporarily for testing)
 */
router.get('/api-keys', async (req, res) => {
  try {
    const apiKeys = await ApiKey.getAll();

    // Don't return the actual key value for security
    const sanitizedKeys = apiKeys.map(key => {
      const { key_value, ...rest } = key;
      return {
        ...rest,
        key_value: key_value ? `${key_value.substring(0, 8)}...` : null
      };
    });

    res.json(sanitizedKeys);
  } catch (err) {
    logger.error('Error in GET /api/integrations/api-keys:', err.message);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

/**
 * @route   GET api/integrations/api-keys/:id
 * @desc    Get API key by ID
 * @access  Private/Admin
 */
router.get('/api-keys/:id', [auth, adminCheck], async (req, res) => {
  try {
    const apiKey = await ApiKey.getById(req.params.id);

    if (!apiKey) {
      return res.status(404).json({ message: 'API key not found' });
    }

    // Don't return the actual key value for security
    const { key_value, ...sanitizedKey } = apiKey;
    sanitizedKey.key_value = key_value ? `${key_value.substring(0, 8)}...` : null;

    res.json(sanitizedKey);
  } catch (err) {
    logger.error(`Error in GET /api/integrations/api-keys/${req.params.id}:`, err.message);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

/**
 * @route   POST api/integrations/api-keys
 * @desc    Create a new API key
 * @access  Private/Admin
 */
router.post('/api-keys', [
  auth,
  adminCheck,
  check('name', 'Name is required').not().isEmpty(),
  check('description', 'Description is required').not().isEmpty()
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const newApiKey = await ApiKey.create(req.body, req.user.id);

    // Return the full key value only when it's first created
    res.status(201).json(newApiKey);
  } catch (err) {
    logger.error('Error in POST /api/integrations/api-keys:', err.message);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

/**
 * @route   PUT api/integrations/api-keys/:id
 * @desc    Update an API key
 * @access  Private/Admin
 */
router.put('/api-keys/:id', [
  auth,
  adminCheck,
  check('name', 'Name is required').not().isEmpty()
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const apiKey = await ApiKey.getById(req.params.id);

    if (!apiKey) {
      return res.status(404).json({ message: 'API key not found' });
    }

    const updatedApiKey = await ApiKey.update(req.params.id, req.body, req.user.id);

    // Don't return the actual key value for security
    const { key_value, ...sanitizedKey } = updatedApiKey;
    sanitizedKey.key_value = key_value ? `${key_value.substring(0, 8)}...` : null;

    res.json(sanitizedKey);
  } catch (err) {
    logger.error(`Error in PUT /api/integrations/api-keys/${req.params.id}:`, err.message);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

/**
 * @route   DELETE api/integrations/api-keys/:id
 * @desc    Delete an API key
 * @access  Private/Admin
 */
router.delete('/api-keys/:id', [auth, adminCheck], async (req, res) => {
  try {
    const apiKey = await ApiKey.getById(req.params.id);

    if (!apiKey) {
      return res.status(404).json({ message: 'API key not found' });
    }

    await ApiKey.delete(req.params.id, req.user.id);

    res.json({ message: 'API key deleted successfully' });
  } catch (err) {
    logger.error(`Error in DELETE /api/integrations/api-keys/${req.params.id}:`, err.message);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

/**
 * @route   GET api/integrations/api-keys/:id/usage
 * @desc    Get API key usage statistics
 * @access  Private/Admin
 */
router.get('/api-keys/:id/usage', [auth, adminCheck], async (req, res) => {
  try {
    const apiKey = await ApiKey.getById(req.params.id);

    if (!apiKey) {
      return res.status(404).json({ message: 'API key not found' });
    }

    // Default to last 7 days if not specified
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - (req.query.days ? parseInt(req.query.days) : 7));

    const usageStats = await ApiKey.getUsageStats(req.params.id, startDate, endDate);

    res.json(usageStats);
  } catch (err) {
    logger.error(`Error in GET /api/integrations/api-keys/${req.params.id}/usage:`, err.message);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

// ===== Integrations Routes =====

/**
 * @route   GET api/integrations
 * @desc    Get all integrations
 * @access  Public (temporarily for testing)
 */
router.get('/', async (req, res) => {
  try {
    const integrations = await Integration.getAll();
    res.json(integrations);
  } catch (err) {
    logger.error('Error in GET /api/integrations:', err.message);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

/**
 * @route   GET api/integrations/:id
 * @desc    Get integration by ID
 * @access  Private/Admin
 */
router.get('/:id', [auth, adminCheck], async (req, res) => {
  try {
    const integration = await Integration.getById(req.params.id);

    if (!integration) {
      return res.status(404).json({ message: 'Integration not found' });
    }

    res.json(integration);
  } catch (err) {
    logger.error(`Error in GET /api/integrations/${req.params.id}:`, err.message);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

/**
 * @route   POST api/integrations
 * @desc    Create a new integration
 * @access  Private/Admin
 */
router.post('/', [
  auth,
  adminCheck,
  check('name', 'Name is required').not().isEmpty(),
  check('type', 'Type is required').not().isEmpty()
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const newIntegration = await Integration.create(req.body, req.user.id);
    res.status(201).json(newIntegration);
  } catch (err) {
    logger.error('Error in POST /api/integrations:', err.message);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

/**
 * @route   PUT api/integrations/:id
 * @desc    Update an integration
 * @access  Private/Admin
 */
router.put('/:id', [
  auth,
  adminCheck,
  check('name', 'Name is required').not().isEmpty()
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const integration = await Integration.getById(req.params.id);

    if (!integration) {
      return res.status(404).json({ message: 'Integration not found' });
    }

    const updatedIntegration = await Integration.update(req.params.id, req.body, req.user.id);
    res.json(updatedIntegration);
  } catch (err) {
    logger.error(`Error in PUT /api/integrations/${req.params.id}:`, err.message);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

/**
 * @route   DELETE api/integrations/:id
 * @desc    Delete an integration
 * @access  Private/Admin
 */
router.delete('/:id', [auth, adminCheck], async (req, res) => {
  try {
    const integration = await Integration.getById(req.params.id);

    if (!integration) {
      return res.status(404).json({ message: 'Integration not found' });
    }

    await Integration.delete(req.params.id, req.user.id);

    res.json({ message: 'Integration deleted successfully' });
  } catch (err) {
    logger.error(`Error in DELETE /api/integrations/${req.params.id}:`, err.message);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

/**
 * @route   GET api/integrations/:id/logs
 * @desc    Get logs for an integration
 * @access  Private/Admin
 */
router.get('/:id/logs', [auth, adminCheck], async (req, res) => {
  try {
    const integration = await Integration.getById(req.params.id);

    if (!integration) {
      return res.status(404).json({ message: 'Integration not found' });
    }

    const limit = parseInt(req.query.limit) || 100;
    const offset = parseInt(req.query.offset) || 0;

    const logs = await Integration.getLogs(req.params.id, limit, offset);
    res.json(logs);
  } catch (err) {
    logger.error(`Error in GET /api/integrations/${req.params.id}/logs:`, err.message);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

/**
 * @route   PUT api/integrations/:id/status
 * @desc    Update integration status
 * @access  Private/Admin
 */
router.put('/:id/status', [
  auth,
  adminCheck,
  check('status', 'Status is required').not().isEmpty(),
  check('health_status', 'Health status is required').not().isEmpty()
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const integration = await Integration.getById(req.params.id);

    if (!integration) {
      return res.status(404).json({ message: 'Integration not found' });
    }

    const { status, health_status } = req.body;

    const updatedIntegration = await Integration.updateStatus(
      req.params.id,
      status,
      health_status,
      req.user.id
    );

    // Log the status change
    await Integration.logActivity(
      req.params.id,
      null,
      'status_change',
      'success',
      `Integration status updated to ${status}`,
      { previous_status: integration.status, new_status: status }
    );

    res.json(updatedIntegration);
  } catch (err) {
    logger.error(`Error in PUT /api/integrations/${req.params.id}/status:`, err.message);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

// ===== External Systems Routes =====

/**
 * @route   GET api/integrations/external-systems
 * @desc    Get all external systems
 * @access  Public (temporarily for testing)
 */
router.get('/external-systems', async (req, res) => {
  try {
    const systems = await ExternalSystem.getAll();
    res.json(systems);
  } catch (err) {
    logger.error('Error in GET /api/integrations/external-systems:', err.message);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

/**
 * @route   GET api/integrations/external-systems/:id
 * @desc    Get external system by ID
 * @access  Private/Admin
 */
router.get('/external-systems/:id', [auth, adminCheck], async (req, res) => {
  try {
    const system = await ExternalSystem.getById(req.params.id);

    if (!system) {
      return res.status(404).json({ message: 'External system not found' });
    }

    res.json(system);
  } catch (err) {
    logger.error(`Error in GET /api/integrations/external-systems/${req.params.id}:`, err.message);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

/**
 * @route   POST api/integrations/external-systems
 * @desc    Create a new external system
 * @access  Private/Admin
 */
router.post('/external-systems', [
  auth,
  adminCheck,
  check('name', 'Name is required').not().isEmpty(),
  check('type', 'Type is required').not().isEmpty()
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const newSystem = await ExternalSystem.create(req.body, req.user.id);
    res.status(201).json(newSystem);
  } catch (err) {
    logger.error('Error in POST /api/integrations/external-systems:', err.message);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

/**
 * @route   PUT api/integrations/external-systems/:id
 * @desc    Update an external system
 * @access  Private/Admin
 */
router.put('/external-systems/:id', [
  auth,
  adminCheck,
  check('name', 'Name is required').not().isEmpty()
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const system = await ExternalSystem.getById(req.params.id);

    if (!system) {
      return res.status(404).json({ message: 'External system not found' });
    }

    const updatedSystem = await ExternalSystem.update(req.params.id, req.body, req.user.id);
    res.json(updatedSystem);
  } catch (err) {
    logger.error(`Error in PUT /api/integrations/external-systems/${req.params.id}:`, err.message);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

/**
 * @route   DELETE api/integrations/external-systems/:id
 * @desc    Delete an external system
 * @access  Private/Admin
 */
router.delete('/external-systems/:id', [auth, adminCheck], async (req, res) => {
  try {
    const system = await ExternalSystem.getById(req.params.id);

    if (!system) {
      return res.status(404).json({ message: 'External system not found' });
    }

    await ExternalSystem.delete(req.params.id, req.user.id);

    res.json({ message: 'External system deleted successfully' });
  } catch (err) {
    logger.error(`Error in DELETE /api/integrations/external-systems/${req.params.id}:`, err.message);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

/**
 * @route   POST api/integrations/external-systems/:id/test
 * @desc    Test connection to external system
 * @access  Private/Admin
 */
router.post('/external-systems/:id/test', [auth, adminCheck], async (req, res) => {
  try {
    const system = await ExternalSystem.getById(req.params.id);

    if (!system) {
      return res.status(404).json({ message: 'External system not found' });
    }

    const result = await ExternalSystem.testConnection(req.params.id);
    res.json(result);
  } catch (err) {
    logger.error(`Error in POST /api/integrations/external-systems/${req.params.id}/test:`, err.message);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

/**
 * @route   GET api/integrations/logs
 * @desc    Get all integration logs
 * @access  Private/Admin
 */
router.get('/logs', [auth, adminCheck], async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 100;
    const offset = parseInt(req.query.offset) || 0;

    const logs = await IntegrationLog.getAll(limit, offset);
    res.json(logs);
  } catch (err) {
    logger.error('Error in GET /api/integrations/logs:', err.message);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

/**
 * @route   GET api/integrations/logs/statistics
 * @desc    Get integration log statistics
 * @access  Private/Admin
 */
router.get('/logs/statistics', [auth, adminCheck], async (req, res) => {
  try {
    // Default to last 30 days if not specified
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - (req.query.days ? parseInt(req.query.days) : 30));

    const statistics = await IntegrationLog.getStatistics(startDate, endDate);
    res.json(statistics);
  } catch (err) {
    logger.error('Error in GET /api/integrations/logs/statistics:', err.message);
    res.status(500).json({ message: 'Server error', error: err.message });
  }
});

// Export the router
module.exports = router;
