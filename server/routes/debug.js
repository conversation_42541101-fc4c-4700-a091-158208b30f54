const express = require('express');
const router = express.Router();
const pool = require('../db');
const auth = require('../middleware/auth');
const adminCheck = require('../middleware/adminCheck');
const bcrypt = require('bcryptjs');
const { generateComprehensivePatientData } = require('../utils/patientDataGenerator');

/**
 * @route   GET api/debug/tables
 * @desc    Get all tables in the database
 * @access  Private/Admin
 */
router.get('/tables', [auth, adminCheck], async (req, res) => {
  try {
    console.log('Debug tables endpoint called by admin:', req.user.id);

    const result = await pool.query(`
      SELECT
        table_name,
        table_schema
      FROM
        information_schema.tables
      WHERE
        table_schema = 'public'
        AND table_type = 'BASE TABLE'
      ORDER BY
        table_name
    `);

    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching database tables:', err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET api/debug/schema/:table
 * @desc    Get schema details for a specific table
 * @access  Private/Admin
 */
router.get('/schema/:table', [auth, adminCheck], async (req, res) => {
  try {
    const { table } = req.params;
    console.log(`Debug schema endpoint called for table ${table} by admin:`, req.user.id);

    // Validate table name to prevent SQL injection
    const tableNameRegex = /^[a-zA-Z0-9_]+$/;
    if (!tableNameRegex.test(table)) {
      return res.status(400).json({ msg: 'Invalid table name' });
    }

    // Get column information
    const columnsResult = await pool.query(`
      SELECT
        column_name,
        data_type,
        character_maximum_length,
        column_default,
        is_nullable,
        udt_name
      FROM
        information_schema.columns
      WHERE
        table_schema = 'public'
        AND table_name = $1
      ORDER BY
        ordinal_position
    `, [table]);

    // Get primary key information
    const pkResult = await pool.query(`
      SELECT
        kcu.column_name
      FROM
        information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
      WHERE
        tc.constraint_type = 'PRIMARY KEY'
        AND tc.table_schema = 'public'
        AND tc.table_name = $1
    `, [table]);

    // Get foreign key information
    const fkResult = await pool.query(`
      SELECT
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
      FROM
        information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
      WHERE
        tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_schema = 'public'
        AND tc.table_name = $1
    `, [table]);

    // Get index information
    const indexResult = await pool.query(`
      SELECT
        indexname,
        indexdef
      FROM
        pg_indexes
      WHERE
        schemaname = 'public'
        AND tablename = $1
    `, [table]);

    // Get sample data (first 5 rows)
    const sampleDataResult = await pool.query(`
      SELECT * FROM "${table}" LIMIT 5
    `);

    // Get row count
    const countResult = await pool.query(`
      SELECT COUNT(*) FROM "${table}"
    `);

    res.json({
      table,
      columns: columnsResult.rows,
      primaryKeys: pkResult.rows.map(row => row.column_name),
      foreignKeys: fkResult.rows,
      indexes: indexResult.rows,
      rowCount: parseInt(countResult.rows[0].count),
      sampleData: sampleDataResult.rows
    });
  } catch (err) {
    console.error(`Error fetching schema for table ${req.params.table}:`, err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET api/debug/all-schema
 * @desc    Get schema details for all tables
 * @access  Private/Admin
 */
router.get('/all-schema', [auth, adminCheck], async (req, res) => {
  try {
    console.log('Debug all-schema endpoint called by admin:', req.user.id);

    // Get all tables
    const tablesResult = await pool.query(`
      SELECT
        table_name
      FROM
        information_schema.tables
      WHERE
        table_schema = 'public'
        AND table_type = 'BASE TABLE'
      ORDER BY
        table_name
    `);

    const tables = tablesResult.rows.map(row => row.table_name);
    const schemaData = {};

    // For each table, get its schema information
    for (const table of tables) {
      // Get column information
      const columnsResult = await pool.query(`
        SELECT
          column_name,
          data_type,
          character_maximum_length,
          column_default,
          is_nullable
        FROM
          information_schema.columns
        WHERE
          table_schema = 'public'
          AND table_name = $1
        ORDER BY
          ordinal_position
      `, [table]);

      // Get primary key information
      const pkResult = await pool.query(`
        SELECT
          kcu.column_name
        FROM
          information_schema.table_constraints tc
          JOIN information_schema.key_column_usage kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
        WHERE
          tc.constraint_type = 'PRIMARY KEY'
          AND tc.table_schema = 'public'
          AND tc.table_name = $1
      `, [table]);

      // Get foreign key information
      const fkResult = await pool.query(`
        SELECT
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name
        FROM
          information_schema.table_constraints tc
          JOIN information_schema.key_column_usage kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
          JOIN information_schema.constraint_column_usage ccu
            ON ccu.constraint_name = tc.constraint_name
            AND ccu.table_schema = tc.table_schema
        WHERE
          tc.constraint_type = 'FOREIGN KEY'
          AND tc.table_schema = 'public'
          AND tc.table_name = $1
      `, [table]);

      // Get row count
      const countResult = await pool.query(`
        SELECT COUNT(*) FROM "${table}"
      `);

      schemaData[table] = {
        columns: columnsResult.rows,
        primaryKeys: pkResult.rows.map(row => row.column_name),
        foreignKeys: fkResult.rows,
        rowCount: parseInt(countResult.rows[0].count)
      };
    }

    res.json(schemaData);
  } catch (err) {
    console.error('Error fetching all schema data:', err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   POST api/debug/query
 * @desc    Execute a custom SQL query (SELECT only)
 * @access  Private/Admin
 */
router.post('/query', [auth, adminCheck], async (req, res) => {
  try {
    const { query } = req.body;
    console.log(`Debug query endpoint called by admin ${req.user.id} with query: ${query}`);

    // Security check: only allow SELECT queries
    const normalizedQuery = query.trim().toLowerCase();
    if (!normalizedQuery.startsWith('select ')) {
      return res.status(403).json({
        msg: 'Only SELECT queries are allowed for security reasons'
      });
    }

    // Execute the query
    const result = await pool.query(query);

    res.json({
      rows: result.rows,
      rowCount: result.rowCount,
      fields: result.fields.map(field => ({
        name: field.name,
        dataTypeID: field.dataTypeID
      }))
    });
  } catch (err) {
    console.error('Error executing custom query:', err.message);
    res.status(500).json({
      error: 'Error executing query',
      message: err.message
    });
  }
});

/**
 * @route   POST api/debug/search-patient-data
 * @desc    Search for patient data across tables based on patient name
 * @access  Private/Admin
 */
router.post('/search-patient-data', [auth, adminCheck], async (req, res) => {
  try {
    const { searchTerm } = req.body;
    console.log(`Debug search-patient-data endpoint called by admin ${req.user.id} with search term: ${searchTerm}`);

    if (!searchTerm || searchTerm.trim() === '') {
      return res.status(400).json({ msg: 'Search term is required' });
    }

    // Sanitize the search term to prevent SQL injection
    const sanitizedSearchTerm = searchTerm.replace(/[^\w\s]/gi, '');
    const searchPattern = `%${sanitizedSearchTerm}%`;

    // First, find matching patients - return all fields
    const patientsResult = await pool.query(`
      SELECT *
      FROM patients
      WHERE first_name ILIKE $1 OR last_name ILIKE $1 OR unique_id ILIKE $1
      ORDER BY last_name, first_name
      LIMIT 20
    `, [searchPattern]);

    const patients = patientsResult.rows;
    const patientIds = patients.map(p => p.patient_id);

    // If no patients found, return empty results
    if (patientIds.length === 0) {
      return res.json({
        patients: [],
        visits: [],
        records: []
      });
    }

    // Find visits for these patients - return all fields
    const visitsResult = await pool.query(`
      SELECT
        v.*,
        p.first_name AS patient_first_name, p.last_name AS patient_last_name,
        d.first_name AS doctor_first_name, d.last_name AS doctor_last_name
      FROM patient_visits v
      JOIN patients p ON v.patient_id = p.patient_id
      LEFT JOIN doctors d ON v.doctor_id = d.doctor_id
      WHERE v.patient_id = ANY($1)
      ORDER BY v.visit_date DESC
    `, [patientIds]);

    // Find medical records for these patients - return all fields
    const recordsResult = await pool.query(`
      SELECT
        r.*,
        p.first_name AS patient_first_name, p.last_name AS patient_last_name
      FROM medical_records r
      JOIN patients p ON r.patient_id = p.patient_id
      WHERE r.patient_id = ANY($1)
      ORDER BY r.record_date DESC
    `, [patientIds]);

    // Return all the data
    res.json({
      patients: patients,
      visits: visitsResult.rows,
      records: recordsResult.rows
    });
  } catch (err) {
    console.error('Error searching patient data:', err.message);
    res.status(500).json({
      error: 'Error searching patient data',
      message: err.message
    });
  }
});

/**
 * @route   GET api/debug/datatypes
 * @desc    Get available PostgreSQL data types
 * @access  Private/Admin
 */
router.get('/datatypes', [auth, adminCheck], async (req, res) => {
  try {
    console.log('Debug datatypes endpoint called by admin:', req.user.id);

    // Common PostgreSQL data types that would be useful in this application
    const dataTypes = [
      { name: 'TEXT', description: 'Variable unlimited length text' },
      { name: 'VARCHAR', description: 'Variable-length character string', needsLength: true },
      { name: 'INTEGER', description: 'Signed four-byte integer' },
      { name: 'BIGINT', description: 'Signed eight-byte integer' },
      { name: 'DECIMAL', description: 'Exact numeric with selectable precision', needsPrecision: true },
      { name: 'NUMERIC', description: 'Exact numeric with selectable precision', needsPrecision: true },
      { name: 'BOOLEAN', description: 'Logical Boolean (true/false)' },
      { name: 'DATE', description: 'Calendar date (year, month, day)' },
      { name: 'TIME', description: 'Time of day (no time zone)' },
      { name: 'TIMESTAMP', description: 'Date and time (no time zone)' },
      { name: 'FLOAT', description: 'Floating-point number' },
      { name: 'DOUBLE PRECISION', description: 'Double precision floating-point number' },
      { name: 'SERIAL', description: 'Autoincrementing four-byte integer' },
      { name: 'BIGSERIAL', description: 'Autoincrementing eight-byte integer' },
      { name: 'JSON', description: 'JSON data' },
      { name: 'JSONB', description: 'Binary JSON data, decomposed' }
    ];

    res.json(dataTypes);
  } catch (err) {
    console.error('Error fetching data types:', err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   POST api/debug/add-column
 * @desc    Add a new column to a table
 * @access  Private/Admin
 */
router.post('/add-column', [auth, adminCheck], async (req, res) => {
  try {
    const { table, columnName, dataType, length, precision, scale, nullable, defaultValue } = req.body;
    console.log(`Debug add-column endpoint called by admin ${req.user.id} for table ${table}`);

    // Validate inputs
    const tableNameRegex = /^[a-zA-Z0-9_]+$/;
    const columnNameRegex = /^[a-zA-Z0-9_]+$/;

    if (!tableNameRegex.test(table)) {
      return res.status(400).json({ msg: 'Invalid table name' });
    }

    if (!columnNameRegex.test(columnName)) {
      return res.status(400).json({ msg: 'Invalid column name' });
    }

    // Check if column already exists
    const columnCheck = await pool.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_schema = 'public'
        AND table_name = $1
        AND column_name = $2
    `, [table, columnName]);

    if (columnCheck.rows.length > 0) {
      return res.status(400).json({ msg: 'Column already exists in this table' });
    }

    // Build the ALTER TABLE statement
    let fullDataType = dataType;

    // Add length for VARCHAR
    if (dataType === 'VARCHAR' && length) {
      fullDataType = `VARCHAR(${length})`;
    }

    // Add precision and scale for DECIMAL/NUMERIC
    if ((dataType === 'DECIMAL' || dataType === 'NUMERIC') && precision) {
      if (scale !== undefined) {
        fullDataType = `${dataType}(${precision},${scale})`;
      } else {
        fullDataType = `${dataType}(${precision})`;
      }
    }

    // Build the full ALTER TABLE statement
    let query = `ALTER TABLE "${table}" ADD COLUMN "${columnName}" ${fullDataType}`;

    // Add NOT NULL constraint if specified
    if (nullable === false) {
      query += ' NOT NULL';
    }

    // Add DEFAULT value if specified
    if (defaultValue !== undefined && defaultValue !== null) {
      query += ` DEFAULT ${defaultValue}`;
    }

    // Execute the query
    await pool.query(query);

    // Log the successful operation
    console.log(`Added column ${columnName} to table ${table}`);

    res.json({
      success: true,
      msg: `Column ${columnName} added to table ${table}`,
      columnDetails: {
        column_name: columnName,
        data_type: dataType,
        character_maximum_length: length || null,
        is_nullable: nullable === false ? 'NO' : 'YES',
        column_default: defaultValue || null
      }
    });
  } catch (err) {
    console.error('Error adding column:', err.message);
    res.status(500).json({
      error: 'Error adding column',
      message: err.message
    });
  }
});

/**
 * @route   POST api/debug/remove-column
 * @desc    Remove a column from a table
 * @access  Private/Admin
 */
router.post('/remove-column', [auth, adminCheck], async (req, res) => {
  try {
    const { table, columnName } = req.body;
    console.log(`Debug remove-column endpoint called by admin ${req.user.id} for table ${table}`);

    // Validate inputs
    const tableNameRegex = /^[a-zA-Z0-9_]+$/;
    const columnNameRegex = /^[a-zA-Z0-9_]+$/;

    if (!tableNameRegex.test(table)) {
      return res.status(400).json({ msg: 'Invalid table name' });
    }

    if (!columnNameRegex.test(columnName)) {
      return res.status(400).json({ msg: 'Invalid column name' });
    }

    // Check if column exists
    const columnCheck = await pool.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_schema = 'public'
        AND table_name = $1
        AND column_name = $2
    `, [table, columnName]);

    if (columnCheck.rows.length === 0) {
      return res.status(400).json({ msg: 'Column does not exist in this table' });
    }

    // Check if column is a primary key
    const pkCheck = await pool.query(`
      SELECT kcu.column_name
      FROM information_schema.table_constraints tc
      JOIN information_schema.key_column_usage kcu
        ON tc.constraint_name = kcu.constraint_name
        AND tc.table_schema = kcu.table_schema
      WHERE tc.constraint_type = 'PRIMARY KEY'
        AND tc.table_schema = 'public'
        AND tc.table_name = $1
        AND kcu.column_name = $2
    `, [table, columnName]);

    if (pkCheck.rows.length > 0) {
      return res.status(400).json({
        msg: 'Cannot remove a primary key column',
        isPrimaryKey: true
      });
    }

    // Check if column is referenced by foreign keys
    const fkCheck = await pool.query(`
      SELECT ccu.table_name, ccu.column_name
      FROM information_schema.table_constraints tc
      JOIN information_schema.constraint_column_usage ccu
        ON tc.constraint_name = ccu.constraint_name
      WHERE tc.constraint_type = 'FOREIGN KEY'
        AND ccu.table_schema = 'public'
        AND ccu.table_name = $1
        AND ccu.column_name = $2
    `, [table, columnName]);

    if (fkCheck.rows.length > 0) {
      return res.status(400).json({
        msg: 'Cannot remove a column referenced by foreign keys',
        isForeignKeyTarget: true,
        referencedBy: fkCheck.rows
      });
    }

    // Execute the query to remove the column
    await pool.query(`ALTER TABLE "${table}" DROP COLUMN "${columnName}"`);

    // Log the successful operation
    console.log(`Removed column ${columnName} from table ${table}`);

    res.json({
      success: true,
      msg: `Column ${columnName} removed from table ${table}`
    });
  } catch (err) {
    console.error('Error removing column:', err.message);
    res.status(500).json({
      error: 'Error removing column',
      message: err.message
    });
  }
});

/**
 * @route   POST api/debug/alter-column-type
 * @desc    Alter the data type of a column in a table
 * @access  Private/Admin
 */
router.post('/alter-column-type', [auth, adminCheck], async (req, res) => {
  try {
    const { table, columnName, newDataType, length, precision, scale } = req.body;
    console.log(`Debug alter-column-type endpoint called by admin ${req.user.id} for table ${table}, column ${columnName}`);

    // Validate inputs
    const tableNameRegex = /^[a-zA-Z0-9_]+$/;
    const columnNameRegex = /^[a-zA-Z0-9_]+$/;

    if (!tableNameRegex.test(table)) {
      return res.status(400).json({ msg: 'Invalid table name' });
    }

    if (!columnNameRegex.test(columnName)) {
      return res.status(400).json({ msg: 'Invalid column name' });
    }

    // Check if column exists
    const columnCheck = await pool.query(`
      SELECT column_name, data_type
      FROM information_schema.columns
      WHERE table_schema = 'public'
        AND table_name = $1
        AND column_name = $2
    `, [table, columnName]);

    if (columnCheck.rows.length === 0) {
      return res.status(400).json({ msg: 'Column does not exist in this table' });
    }

    const currentDataType = columnCheck.rows[0].data_type;

    // Build the full data type string
    let fullDataType = newDataType;

    // Add length for VARCHAR
    if (newDataType === 'VARCHAR' && length) {
      fullDataType = `VARCHAR(${length})`;
    }

    // Add precision and scale for DECIMAL/NUMERIC
    if ((newDataType === 'DECIMAL' || newDataType === 'NUMERIC') && precision) {
      if (scale !== undefined) {
        fullDataType = `${newDataType}(${precision},${scale})`;
      } else {
        fullDataType = `${newDataType}(${precision})`;
      }
    }

    // Build the ALTER TABLE statement
    // Use USING clause to handle type conversion
    const query = `ALTER TABLE "${table}" ALTER COLUMN "${columnName}" TYPE ${fullDataType} USING "${columnName}"::${fullDataType}`;

    // Execute the query
    await pool.query(query);

    // Log the successful operation
    console.log(`Altered column ${columnName} in table ${table} from ${currentDataType} to ${newDataType}`);

    res.json({
      success: true,
      msg: `Column ${columnName} data type changed from ${currentDataType} to ${newDataType}`,
      columnDetails: {
        column_name: columnName,
        old_data_type: currentDataType,
        new_data_type: newDataType
      }
    });
  } catch (err) {
    console.error('Error altering column data type:', err.message);
    res.status(500).json({
      error: 'Error altering column data type',
      message: err.message
    });
  }
});

/**
 * @route   DELETE api/debug/sample-data/:table/:id
 * @desc    Delete a single sample data record from a table
 * @access  Private/Admin
 */
router.delete('/sample-data/:table/:id', [auth, adminCheck], async (req, res) => {
  try {
    const { table, id } = req.params;
    console.log(`Debug delete sample data endpoint called for table ${table}, ID ${id} by admin:`, req.user.id);

    // Validate table name to prevent SQL injection
    const tableNameRegex = /^[a-zA-Z0-9_]+$/;
    if (!tableNameRegex.test(table)) {
      return res.status(400).json({ msg: 'Invalid table name' });
    }

    // Get primary key column name
    const pkResult = await pool.query(`
      SELECT
        kcu.column_name
      FROM
        information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
      WHERE
        tc.constraint_type = 'PRIMARY KEY'
        AND tc.table_schema = 'public'
        AND tc.table_name = $1
    `, [table]);

    if (pkResult.rows.length === 0) {
      return res.status(400).json({ msg: 'Table does not have a primary key' });
    }

    const primaryKeyColumn = pkResult.rows[0].column_name;

    // Delete the record
    const deleteResult = await pool.query(`
      DELETE FROM "${table}"
      WHERE "${primaryKeyColumn}" = $1
      RETURNING *
    `, [id]);

    if (deleteResult.rows.length === 0) {
      return res.status(404).json({ msg: 'Record not found' });
    }

    res.json({
      success: true,
      msg: `Record with ID ${id} deleted from ${table}`,
      deletedRecord: deleteResult.rows[0]
    });
  } catch (err) {
    console.error(`Error deleting sample data from ${req.params.table}:`, err.message);
    res.status(500).json({
      error: 'Error deleting sample data',
      message: err.message
    });
  }
});

/**
 * @route   DELETE api/debug/all-sample-data/:table
 * @desc    Delete all sample data from a table
 * @access  Private/Admin
 */
router.delete('/all-sample-data/:table', [auth, adminCheck], async (req, res) => {
  try {
    const { table } = req.params;
    console.log(`Debug delete all sample data endpoint called for table ${table} by admin:`, req.user.id);

    // Validate table name to prevent SQL injection
    const tableNameRegex = /^[a-zA-Z0-9_]+$/;
    if (!tableNameRegex.test(table)) {
      return res.status(400).json({ msg: 'Invalid table name' });
    }

    // Get row count before deletion
    const countResult = await pool.query(`
      SELECT COUNT(*) FROM "${table}"
    `);

    const rowCount = parseInt(countResult.rows[0].count);

    // Delete all records
    await pool.query(`
      DELETE FROM "${table}"
    `);

    res.json({
      success: true,
      msg: `All ${rowCount} records deleted from ${table}`,
      deletedCount: rowCount
    });
  } catch (err) {
    console.error(`Error deleting all sample data from ${req.params.table}:`, err.message);
    res.status(500).json({
      error: 'Error deleting all sample data',
      message: err.message
    });
  }
});

/**
 * @route   POST api/debug/generate-simple-patient
 * @desc    Generate a patient with provided name and doctor, and random data for other fields
 * @access  Private/Admin
 */
router.post('/generate-simple-patient', [auth, adminCheck], async (req, res) => {
  try {
    const { firstName, lastName, doctorId, gender, birthDate, createdAt } = req.body;
    console.log(`Debug generate sample patient endpoint called by admin:`, req.user.id);
    console.log('Request body:', JSON.stringify(req.body));

    if (!firstName || !lastName || !doctorId || !gender) {
      return res.status(400).json({ msg: 'First name, last name, doctor ID, and gender are required' });
    }

    // Verify the doctor exists
    const doctorResult = await pool.query('SELECT doctor_id FROM doctors WHERE doctor_id = $1', [doctorId]);

    if (doctorResult.rows.length === 0) {
      return res.status(400).json({ msg: 'Doctor not found' });
    }

    // Get admin user for last_edited_by
    const userResult = await pool.query('SELECT user_id FROM users WHERE role = \'admin\' LIMIT 1');
    const adminId = userResult.rows.length > 0 ? userResult.rows[0].user_id : null;

    // Use provided birth date or generate a random one
    let dob;
    if (birthDate) {
      dob = new Date(birthDate);
    } else {
      // Random date of birth (65-95 years old)
      dob = new Date();
      const age = 65 + Math.floor(Math.random() * 30);
      dob.setFullYear(dob.getFullYear() - age);
      dob.setMonth(Math.floor(Math.random() * 12));
      dob.setDate(1 + Math.floor(Math.random() * 28));
    }

    // Use the provided gender from the request

    // Random contact info
    const phone = `555-${Math.floor(100 + Math.random() * 900)}-${Math.floor(1000 + Math.random() * 9000)}`;
    const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}${Math.floor(Math.random() * 100)}@example.com`;

    // Random address
    const streetNumber = Math.floor(100 + Math.random() * 9900);
    const streets = ['Main St', 'Oak Ave', 'Maple Rd', 'Washington Blvd', 'Park Lane', 'Cedar Dr', 'Pine St', 'Elm Rd'];
    const cities = ['Springfield', 'Riverdale', 'Lakeside', 'Maplewood', 'Oakville', 'Brookfield', 'Fairview', 'Greenwood'];
    const states = ['CA', 'NY', 'TX', 'FL', 'IL', 'PA', 'OH', 'GA', 'NC', 'MI'];
    const street = streets[Math.floor(Math.random() * streets.length)];
    const city = cities[Math.floor(Math.random() * cities.length)];
    const state = states[Math.floor(Math.random() * states.length)];
    const zipCode = Math.floor(10000 + Math.random() * 90000);
    const address = `${streetNumber} ${street}, ${city}, ${state} ${zipCode}`;

    // Generate comprehensive patient data
    const patientData = generateComprehensivePatientData();

    // Generate a unique ID for the patient
    const uniqueId = `P${Math.floor(10000 + Math.random() * 90000)}`;

    // Check if a custom creation date was provided
    let patientCreatedAt = null;
    if (createdAt) {
      const parsedCreatedAt = new Date(createdAt);
      if (!isNaN(parsedCreatedAt.getTime())) {
        patientCreatedAt = parsedCreatedAt;
        console.log(`Setting custom creation date for patient: ${parsedCreatedAt.toISOString()}`);
      } else {
        console.warn(`Invalid creation date provided: ${createdAt}, using current timestamp for patient`);
      }
    }

    // Get all column names from the patients table
    const columnsResult = await pool.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_schema = 'public'
        AND table_name = 'patients'
        AND column_name != 'patient_id'
        AND column_name != 'created_at'
        AND column_name != 'last_edited_at'
      ORDER BY ordinal_position
    `);

    // Extract column names
    const columnNames = columnsResult.rows.map(row => row.column_name);

    // Build the SQL query based on whether we have a custom creation date
    let insertQuery = `
      INSERT INTO patients (
        ${columnNames.join(', ')}`;

    if (patientCreatedAt) {
      insertQuery += `, created_at`;
    }

    // Create parameter placeholders ($1, $2, etc.) based on the number of columns
    const placeholders = columnNames.map((_, index) => `$${index + 1}`);

    insertQuery += `
      ) VALUES (
        ${placeholders.join(', ')}`;

    // Add placeholder for created_at if needed
    if (patientCreatedAt) {
      insertQuery += `, $${columnNames.length + 1}`;
    }

    insertQuery += `) RETURNING *`;

    // Create a mapping of column names to values
    const columnValues = {
      // Basic patient info
      first_name: firstName,
      last_name: lastName,
      unique_id: uniqueId,
      date_of_birth: dob.toISOString().split('T')[0],
      gender: gender,
      phone: phone,
      email: email,
      address: address,
      doctor_id: doctorId,

      // Vital signs
      lying_bp_systolic: patientData.lyingBpSystolic,
      lying_bp_diastolic: patientData.lyingBpDiastolic,
      standing_bp_systolic: patientData.standingBpSystolic,
      standing_bp_diastolic: patientData.standingBpDiastolic,
      sitting_bp_systolic: patientData.sittingBpSystolic,
      sitting_bp_diastolic: patientData.sittingBpDiastolic,
      heart_rate: patientData.heartRate,
      lying_heart_rate: patientData.lyingHeartRate,
      standing_heart_rate: patientData.standingHeartRate,
      heart_rhythm: patientData.heartRhythm,
      temperature: patientData.temperature,
      body_temperature: patientData.bodyTemperature,
      respiratory_rate: patientData.respiratoryRate,
      pulse_oximetry: patientData.pulseOximetry,
      oxygen_saturation: patientData.oxygenSaturation,

      // Lab values
      blood_glucose: patientData.bloodGlucose,
      hba1c: patientData.hba1c,
      cholesterol_total: patientData.cholesterolTotal,
      hdl_cholesterol: patientData.hdlCholesterol,
      ldl_cholesterol: patientData.ldlCholesterol,
      triglycerides: patientData.triglycerides,
      creatinine: patientData.creatinine,
      egfr: patientData.egfr,
      blood_urea_nitrogen: patientData.bloodUreaNitrogen,
      sodium: patientData.sodium,
      potassium: patientData.potassium,
      calcium: patientData.calcium,
      magnesium: patientData.magnesium,
      phosphorus: patientData.phosphorus,
      alt: patientData.alt,
      ast: patientData.ast,
      alp: patientData.alp,
      bilirubin: patientData.bilirubin,
      albumin: patientData.albumin,
      tsh: patientData.tsh,
      t4: patientData.t4,
      t3: patientData.t3,
      crp: patientData.crp,
      esr: patientData.esr,
      hemoglobin: patientData.hemoglobin,
      hematocrit: patientData.hematocrit,
      ferritin: patientData.ferritin,
      vitamin_d: patientData.vitaminD,
      vitamin_b12: patientData.vitaminB12,
      folate: patientData.folate,
      psa: patientData.psa,

      // Physical measurements
      height: patientData.height,
      weight: patientData.weight,
      bmi: patientData.bmi,
      calf_circumference: patientData.calfCircumference,

      // Health status
      activity_level: patientData.activityLevel,
      exercise_frequency: patientData.exerciseFrequency,
      mobility_status: patientData.mobilityStatus,
      nutritional_status: patientData.nutritionalStatus,
      hydration_status: patientData.hydrationStatus,

      // Sleep data
      sleep_quality: patientData.sleepQuality,
      sleep_disturbances: patientData.sleepDisturbance,
      sleep_quality_duration: patientData.sleepQualityDuration,
      sleep_duration: patientData.sleepDuration,
      sleep_initiation_difficulties: patientData.sleepInitiationDifficulty,

      // Pain assessment
      pain_level: patientData.painLevel,
      pain_location: patientData.painLocation,
      pain_character: patientData.painCharacter,
      safe_pain_medications: patientData.safePainMedications,

      // Social and living situation
      living_situation: patientData.livingSituation,
      social_support: patientData.socialSupport,
      social_support_network: patientData.socialSupportNetwork,
      social_interaction_levels: patientData.socialInteractionLevel,
      living_conditions: patientData.livingConditions,
      environmental_risks: patientData.environmentalRisk,
      age_friendly_environment: patientData.ageFriendlyEnvironment,
      transportation_access: patientData.transportationAccess,
      financial_concern: patientData.financialConcern,

      // Emergency contacts
      emergency_contact_name: patientData.emergencyContactName,
      emergency_contact_phone: patientData.emergencyContactPhone,
      emergency_contact_updated: patientData.emergencyContactUpdated,
      sos_alerts: patientData.sosAlerts,

      // Medical history and conditions
      medical_history: patientData.medicalHistory,
      allergies: patientData.allergies,
      medication_allergies: patientData.medicationAllergies,
      blood_type: patientData.bloodType,
      current_medications: patientData.currentMedications,
      pill_burden: patientData.pillBurden,
      medication_adherence: patientData.medicationAdherence,
      medication_side_effects: patientData.medicationSideEffects,

      // Cognitive and mental health
      cognitive_test_results: patientData.cognitiveTestResults,
      cognitive_impairment_score: patientData.cognitiveImpairmentScore,
      mental_health_assessment: patientData.mentalHealthAssessment,
      depression_screening: patientData.depressionScreening,
      anxiety_screening: patientData.anxietyScreening,
      depression_score: patientData.depressionScore,
      anxiety_score: patientData.anxietyScore,

      // Mobility and fall risk
      fall_risk: patientData.fallRisk,
      fall_detection_incidents: patientData.fallDetectionIncidents,
      mobility_aids_used: patientData.mobilityAidsUsed,
      assistive_devices_used: patientData.assistiveDevicesUsed,

      // Vision and hearing
      vision_status: patientData.visionStatus,
      hearing_status: patientData.hearingStatus,
      use_of_aids: patientData.useOfAids,

      // Nutrition and hydration
      dietary_intake_quality: patientData.dietaryIntakeQuality,
      vitamin_mineral_levels: patientData.vitaminMineralLevels,

      // Urinary and bowel function
      urinary_bowel_issues: patientData.urinaryBowelIssues,
      substance_abuse: patientData.substanceAbuse,
      substance_abuse_details: patientData.substanceAbuseDetails,

      // Preventive care
      health_checkup_adherence: patientData.healthCheckupAdherence,
      preventive_care_adherence: patientData.preventiveCareAdherence,
      vaccination_updated: patientData.vaccinationUpdated,

      // Vaccination dates
      influenza_vaccination_date: patientData.influenzaVaccinationDate,
      pneumococcal_vaccination_date: patientData.pneumococcalVaccinationDate,
      zoster_vaccination_date: patientData.zosterVaccinationDate,
      tdap_vaccination_date: patientData.tdapVaccinationDate,
      covid19_vaccination_date: patientData.covid19VaccinationDate,
      covid19_booster_date: patientData.covid19BoosterDate,
      hepatitis_a_vaccination_date: patientData.hepatitisAVaccinationDate,
      hepatitis_b_vaccination_date: patientData.hepatitisBVaccinationDate,
      mmr_vaccination_date: patientData.mmrVaccinationDate,
      varicella_vaccination_date: patientData.varicellaVaccinationDate,
      pneumococcal_booster_vaccination_date: patientData.pneumococcalBoosterVaccinationDate,
      covid19_booster_vaccination_date: patientData.covid19BoosterVaccinationDate,
      other_vaccinations: patientData.otherVaccinations,

      // Additional health concerns
      additional_health_concerns: patientData.additionalHealthConcerns,
      cancer_screening_results: patientData.cancerScreeningResults,

      // Metadata
      last_edited_by: adminId,
      user_id: null // Will be updated after user creation
    };

    // Get column data types from the database
    const columnTypesResult = await pool.query(`
      SELECT column_name, data_type, udt_name
      FROM information_schema.columns
      WHERE table_name = 'patients'
      AND table_schema = 'public'
    `);

    // Create a map of column names to their data types
    const columnTypes = {};
    columnTypesResult.rows.forEach(row => {
      columnTypes[row.column_name] = {
        data_type: row.data_type,
        udt_name: row.udt_name
      };
    });

    // Helper function to determine if a column is integer
    const isInteger = (columnName) => {
      if (!columnTypes[columnName]) return false;

      const type = columnTypes[columnName];
      return (
        type.data_type === 'integer' ||
        type.data_type === 'bigint' ||
        type.data_type === 'smallint' ||
        type.udt_name === 'int4' ||
        type.udt_name === 'int8' ||
        type.udt_name === 'int2'
      );
    };

    // Create the values array based on the column names
    const insertValues = columnNames.map(column => {
      const value = columnValues[column];

      // Handle undefined values
      if (value === undefined || value === null) return null;

      // Convert to integer for integer columns
      if (isInteger(column)) {
        // If it's already a number, convert to integer
        if (typeof value === 'number') {
          return Math.floor(value);
        }

        // If it's a string with a decimal, parse as integer
        if (typeof value === 'string' && value.includes('.')) {
          return parseInt(value, 10);
        }
      }

      return value;
    });

    if (patientCreatedAt) {
      insertValues.push(patientCreatedAt);
    }

    // Insert the patient
    const insertResult = await pool.query(insertQuery, insertValues);

    // Create a user account for the patient
    try {
      console.log('Starting user account creation process...');

      // Create a username from the patient's name (remove spaces and special characters)
      const cleanFirstName = firstName.toLowerCase().replace(/[^a-z0-9]/g, '');
      const cleanLastName = lastName.toLowerCase().replace(/[^a-z0-9]/g, '');
      const username = `${cleanFirstName}_${cleanLastName}`;
      console.log(`Generated username: ${username}`);

      // Check if username already exists
      console.log('Checking if username already exists...');
      const usernameCheck = await pool.query('SELECT user_id FROM users WHERE username = $1', [username]);
      console.log(`Username check result: ${usernameCheck.rows.length} rows found`);

      let finalUsername = username;
      // If username exists, add a random number to make it unique
      if (usernameCheck.rows.length > 0) {
        finalUsername = `${username}${Math.floor(100 + Math.random() * 900)}`;
        console.log(`Username already exists, generated new username: ${finalUsername}`);
      }

      // Create a default password using the User model's createWithDefaultPassword method
      // This will use the password policy to generate a default password
      console.log('Loading User model...');
      const User = require('../models/User');

      // Create the user with patient role
      const userData = {
        username: finalUsername,
        email: email,
        role: 'patient',
        patient_id: insertResult.rows[0].patient_id // Add patient_id to link the user to the patient
      };

      console.log(`Preparing user data: ${JSON.stringify(userData)}`);

      // Create the user with default password
      let result;

      if (createdAt) {
        // Parse the creation date
        const parsedCreatedAt = new Date(createdAt);
        if (!isNaN(parsedCreatedAt.getTime())) {
          // If valid date, set it as created_at
          userData.created_at = parsedCreatedAt;
          console.log(`Setting custom creation date for user: ${parsedCreatedAt.toISOString()}`);
        } else {
          console.warn(`Invalid creation date provided: ${createdAt}, using current timestamp instead`);
        }
      }

      // Create the user with default password
      console.log('Calling User.createWithDefaultPassword...');
      result = await User.createWithDefaultPassword(userData);
      console.log('User created successfully with default password');

      // Extract user and default password
      const userResult = { rows: [result.user] };
      const defaultPassword = result.defaultPassword;

      const userId = userResult.rows[0].user_id;
      console.log(`Successfully created user with ID: ${userId}`);

      // Add user_id column to patients table if it doesn't exist
      try {
        console.log('Checking if user_id column exists in patients table...');
        // Check if user_id column exists in patients table
        const columnCheck = await pool.query(`
          SELECT column_name
          FROM information_schema.columns
          WHERE table_name = 'patients' AND column_name = 'user_id'
        `);
        console.log(`Column check result: ${columnCheck.rows.length} rows found`);

        // If user_id column doesn't exist, add it
        if (columnCheck.rows.length === 0) {
          console.log('Adding user_id column to patients table');
          await pool.query('ALTER TABLE patients ADD COLUMN user_id INTEGER REFERENCES users(user_id)');
          console.log('Added user_id column to patients table');
        } else {
          console.log('user_id column already exists in patients table');
        }

        // Link the patient to the user
        console.log(`Linking patient ${insertResult.rows[0].patient_id} to user ${userId}`);
        await pool.query('UPDATE patients SET user_id = $1 WHERE patient_id = $2',
          [userId, insertResult.rows[0].patient_id]
        );
        console.log('Successfully linked patient to user');
      } catch (columnErr) {
        console.error('Error with patient-user linking:', columnErr);
        // Continue even if linking fails
      }

      console.log(`Created user account for patient: ${finalUsername} (ID: ${userId})`);

      // Return the created patient with user info
      res.json({
        success: true,
        msg: `Generated patient: ${firstName} ${lastName} with user account`,
        patient: insertResult.rows[0],
        user: {
          username: finalUsername,
          email: email,
          password: defaultPassword, // Only sending this because it's a development tool
          user_id: userId,
          is_first_login: userResult.rows[0].is_first_login,
          default_password: userResult.rows[0].default_password
        }
      });
    } catch (userErr) {
      console.error('Error creating user account for patient:', userErr);
      console.error('Error details:', userErr.stack);
      // Still return the patient even if user creation fails
      res.json({
        success: true,
        msg: `Generated patient: ${firstName} ${lastName} (but failed to create user account)`,
        patient: insertResult.rows[0],
        userError: userErr.message
      });
    }
  } catch (err) {
    console.error(`Error generating simple patient:`, err.message);
    res.status(500).json({
      error: 'Error generating patient',
      message: err.message
    });
  }
});

/**
 * @route   POST api/debug/generate-visit-for-patient
 * @desc    Generate a visit for a specific patient with a specified date
 * @access  Private/Admin
 */
router.post('/generate-visit-for-patient', [auth, adminCheck], async (req, res) => {
  try {
    const { patientId, visitDate } = req.body;
    console.log(`Debug generate visit for patient ${patientId} endpoint called by admin:`, req.user.id);

    if (!patientId) {
      return res.status(400).json({ msg: 'Patient ID is required' });
    }

    // Verify the patient exists and get their doctor and contact information
    const patientResult = await pool.query('SELECT patient_id, doctor_id, phone, email, address, emergency_contact_name, emergency_contact_phone, emergency_contact_relationship FROM patients WHERE patient_id = $1', [patientId]);

    if (patientResult.rows.length === 0) {
      return res.status(400).json({ msg: 'Patient not found' });
    }

    const doctorId = patientResult.rows[0].doctor_id;
    const patientPhone = patientResult.rows[0].phone;
    const patientEmail = patientResult.rows[0].email;
    const patientAddress = patientResult.rows[0].address;
    const emergencyContactName = patientResult.rows[0].emergency_contact_name;
    const emergencyContactPhone = patientResult.rows[0].emergency_contact_phone;
    const emergencyContactRelationship = patientResult.rows[0].emergency_contact_relationship;

    // Get admin user for created_by
    const userResult = await pool.query('SELECT user_id FROM users WHERE role = \'admin\' LIMIT 1');
    const adminId = userResult.rows.length > 0 ? userResult.rows[0].user_id : null;

    // Get column data types from the database
    const columnTypesResult = await pool.query(`
      SELECT column_name, data_type, udt_name
      FROM information_schema.columns
      WHERE table_name = 'patient_visits'
      AND table_schema = 'public'
    `);

    // Create a map of column names to their data types
    const columnTypes = {};
    columnTypesResult.rows.forEach(row => {
      columnTypes[row.column_name] = {
        data_type: row.data_type,
        udt_name: row.udt_name
      };
    });

    // Helper function to determine if a column is numeric with decimals
    const isNumericWithDecimals = (columnName) => {
      if (!columnTypes[columnName]) return false;

      const type = columnTypes[columnName];
      return (
        type.data_type === 'numeric' ||
        type.data_type === 'real' ||
        type.data_type === 'double precision' ||
        type.udt_name === 'numeric' ||
        type.udt_name === 'float4' ||
        type.udt_name === 'float8'
      );
    };

    // Helper function to determine if a column is integer
    const isInteger = (columnName) => {
      if (!columnTypes[columnName]) return false;

      const type = columnTypes[columnName];
      return (
        type.data_type === 'integer' ||
        type.data_type === 'bigint' ||
        type.data_type === 'smallint' ||
        type.udt_name === 'int4' ||
        type.udt_name === 'int8' ||
        type.udt_name === 'int2'
      );
    };

    // Use provided visit date or create a random date
    let visitDateTime;
    if (visitDate) {
      visitDateTime = new Date(visitDate);
      // Set a random time between 8 AM and 5 PM
      const hours = 8 + Math.floor(Math.random() * 9);
      const minutes = Math.floor(Math.random() * 4) * 15; // 0, 15, 30, or 45 minutes
      visitDateTime.setHours(hours, minutes, 0, 0);
    } else {
      // Create a random date within the last year
      visitDateTime = new Date();
      visitDateTime.setDate(visitDateTime.getDate() - Math.floor(Math.random() * 365));
      // Random time between 8 AM and 5 PM
      const hours = 8 + Math.floor(Math.random() * 9);
      const minutes = Math.floor(Math.random() * 4) * 15; // 0, 15, 30, or 45 minutes
      visitDateTime.setHours(hours, minutes, 0, 0);
    }

    // End time is 30 minutes after start time
    const endTime = new Date(visitDateTime);
    endTime.setMinutes(endTime.getMinutes() + 30);

    // Random vital signs
    const systolic = 110 + Math.floor(Math.random() * 40); // 110-150
    const diastolic = 70 + Math.floor(Math.random() * 20); // 70-90
    const heartRate = 60 + Math.floor(Math.random() * 40); // 60-100
    const lyingHeartRate = heartRate - Math.floor(Math.random() * 5); // Slightly lower when lying
    const standingHeartRate = heartRate + Math.floor(Math.random() * 10); // Slightly higher when standing
    // Temperature in Celsius (normal range: 36.1-37.2°C)
    const temperature = 36.1 + (Math.random() * 1.1);
    const weight = 50 + Math.floor(Math.random() * 50); // 50-100 kg
    const height = 150 + Math.floor(Math.random() * 50); // 150-200 cm
    const bmi = weight / ((height / 100) * (height / 100)); // Calculate BMI
    const calfCircumference = parseFloat((30 + Math.random() * 10).toFixed(1)); // 30-40 cm with one decimal place

    // Heart rhythms
    const heartRhythms = ['Regular', 'Irregular', 'Bradycardia', 'Tachycardia', 'Arrhythmia'];
    const sittingHeartRate = heartRate - Math.floor(Math.random() * 3); // Slightly lower than standing, higher than lying

    // Random lab values
    const bloodGlucose = 70 + Math.floor(Math.random() * 130); // 70-200
    const cholesterolTotal = 150 + Math.floor(Math.random() * 100); // 150-250

    // Visit reasons
    const visitReasons = [
      'Annual checkup',
      'Follow-up visit',
      'Blood pressure check',
      'Diabetes monitoring',
      'Joint pain',
      'Respiratory symptoms',
      'Medication review',
      'Preventive care',
      'Chronic disease management',
      'General consultation'
    ];

    // Activity levels
    const activityLevels = [
      'Sedentary',
      'Light activity',
      'Moderate activity',
      'Active',
      'Very active'
    ];

    // Nutritional status
    const nutritionalStatuses = [
      'Poor',
      'Fair',
      'Good',
      'Excellent'
    ];

    // Hydration status
    const hydrationStatuses = [
      'Dehydrated',
      'Slightly dehydrated',
      'Well hydrated',
      'Optimal hydration'
    ];

    // Format the end time as a time string (HH:MM:SS)
    const endTimeStr = `${endTime.getHours().toString().padStart(2, '0')}:${endTime.getMinutes().toString().padStart(2, '0')}:00`;

    // Generate comprehensive lab values for all possible lab tests
    const generateLabValues = () => {
      // Helper function to generate a random value within a range based on column type
      // with a chance of generating abnormal values (high or low)
      const randomInRange = (columnName, min, max, defaultDecimals = 0, abnormalChance = 0.3) => {
        // Determine if this value should be abnormal
        const isAbnormal = Math.random() < abnormalChance;

        let value;

        if (isAbnormal) {
          // Generate an abnormal value (either high or low)
          const isHigh = Math.random() < 0.5;

          if (isHigh) {
            // Generate a high abnormal value (20-50% above max)
            const abnormalFactor = 1.2 + (Math.random() * 0.3);
            value = max * abnormalFactor;
          } else {
            // Generate a low abnormal value (20-50% below min)
            const abnormalFactor = 0.5 + (Math.random() * 0.3);
            value = min * abnormalFactor;
          }
        } else {
          // Generate a normal value
          value = min + (Math.random() * (max - min));
        }

        // Check if the column exists in the database
        if (!columnTypes[columnName]) {
          // If column doesn't exist, use the default decimals
          return defaultDecimals > 0 ? parseFloat(value.toFixed(defaultDecimals)) : Math.floor(value);
        }

        // If it's an integer column, return an integer
        if (isInteger(columnName)) {
          return Math.floor(value);
        }

        // If it's a numeric column with decimals, return a numeric value with appropriate precision
        if (isNumericWithDecimals(columnName)) {
          return parseFloat(value.toFixed(defaultDecimals > 0 ? defaultDecimals : 1));
        }

        // Default case: return integer
        return Math.floor(value);
      };

      return {
        // Complete Blood Count (CBC)
        wbc: randomInRange('wbc', 4, 11, 1),                    // White blood cells (4-11 K/uL)
        rbc: randomInRange('rbc', 4.5, 5.9, 2),                 // Red blood cells (4.5-5.9 million cells/μL for males)
        hemoglobin: randomInRange('hemoglobin', 12.0, 17.0, 1), // Hemoglobin (12.0-17.0 g/dL)
        hematocrit: randomInRange('hematocrit', 35.0, 50.0, 1), // Hematocrit (35.0-50.0%)
        mcv: randomInRange('mcv', 80, 100, 1),                  // Mean corpuscular volume (80-100 fL)
        mch: randomInRange('mch', 27, 33, 1),                   // Mean corpuscular hemoglobin (27-33 pg)
        mchc: randomInRange('mchc', 32, 36, 1),                 // Mean corpuscular hemoglobin concentration (32-36 g/dL)
        rdw: randomInRange('rdw', 11.5, 14.5, 1),               // Red cell distribution width (11.5-14.5%)
        platelets: randomInRange('platelets', 150, 450, 0),     // Platelets (150-450 thousand/μL)
        mpv: randomInRange('mpv', 7, 11, 0),                    // Mean platelet volume (7-11 fL)
        neutrophils: randomInRange('neutrophils', 40, 60, 0),   // Neutrophils (40-60%)
        lymphocytes: randomInRange('lymphocytes', 20, 40, 0),   // Lymphocytes (20-40%)
        monocytes: randomInRange('monocytes', 2, 8, 0),         // Monocytes (2-8%)
        eosinophils: randomInRange('eosinophils', 1, 4, 0),     // Eosinophils (1-4%)
        basophils: randomInRange('basophils', 0.5, 1, 1),       // Basophils (0.5-1%)

        // Comprehensive Metabolic Panel (CMP)
        glucose: randomInRange('blood_glucose', 70, 110, 0),     // Glucose (70-110 mg/dL)
        bun: randomInRange('bun', 7, 20, 0),                     // Blood urea nitrogen (7-20 mg/dL)
        creatinine: randomInRange('creatinine', 0.6, 1.2, 1),    // Creatinine (0.6-1.2 mg/dL)
        egfr: randomInRange('egfr', 60, 120, 0),                 // eGFR (>60 mL/min/1.73m²)
        sodium: randomInRange('sodium', 135, 145, 0),            // Sodium (135-145 mEq/L)
        potassium: randomInRange('potassium', 3.5, 5.0, 1),      // Potassium (3.5-5.0 mEq/L)
        chloride: randomInRange('chloride', 98, 107, 0),         // Chloride (98-107 mEq/L)
        co2: randomInRange('co2', 22, 29, 0),                    // Carbon dioxide (22-29 mEq/L)
        calcium: randomInRange('calcium', 8.5, 10.2, 1),         // Calcium (8.5-10.2 mg/dL)
        total_protein: randomInRange('total_protein', 6.0, 8.3, 1), // Total protein (6.0-8.3 g/dL)
        albumin: randomInRange('albumin', 3.5, 5.0, 1),          // Albumin (3.5-5.0 g/dL)
        globulin: randomInRange('globulin', 2.0, 3.5, 1),        // Globulin (2.0-3.5 g/dL)
        ag_ratio: randomInRange('ag_ratio', 1.0, 2.0, 1),        // A/G ratio (1.0-2.0)
        bilirubin_t: randomInRange('bilirubin_t', 0.1, 1.2, 2),  // Total bilirubin (0.1-1.2 mg/dL)
        bilirubin_d: randomInRange('bilirubin_d', 0.0, 0.3, 2),  // Direct bilirubin (0.0-0.3 mg/dL)
        alt: randomInRange('alt', 7, 56, 0),                     // ALT (7-56 U/L)
        ast: randomInRange('ast', 10, 40, 0),                    // AST (10-40 U/L)
        alp: randomInRange('alp', 44, 147, 0),                   // Alkaline phosphatase (44-147 U/L)
        ggt: randomInRange('ggt', 8, 61, 0),                     // GGT (8-61 U/L for males, 5-36 U/L for females)

        // Lipid Panel
        cholesterol_total: randomInRange('cholesterol_total', 125, 200, 0), // Total cholesterol (<200 mg/dL)
        triglycerides: randomInRange('triglycerides', 40, 150, 0),          // Triglycerides (<150 mg/dL)
        hdl_cholesterol: randomInRange('hdl_cholesterol', 40, 60, 0),       // HDL (>40 mg/dL)
        ldl_cholesterol: randomInRange('ldl_cholesterol', 70, 130, 0),      // LDL (<130 mg/dL)
        vldl: randomInRange('vldl', 5, 40, 0),                              // VLDL (5-40 mg/dL)
        cholesterol_hdl_ratio: randomInRange('cholesterol_hdl_ratio', 3.0, 5.0, 1), // Cholesterol/HDL ratio (<5.0)
        non_hdl_cholesterol: randomInRange('non_hdl_cholesterol', 100, 160, 0), // Non-HDL cholesterol (<160 mg/dL)

        // Thyroid Panel
        tsh: randomInRange('tsh', 0.4, 4.0, 2),                 // TSH (0.4-4.0 mIU/L)
        t3_total: randomInRange('t3_total', 80, 200, 0),        // Total T3 (80-200 ng/dL)
        free_t3: randomInRange('free_t3', 2.3, 4.2, 1),         // Free T3 (2.3-4.2 pg/mL)
        t4_total: randomInRange('t4_total', 5.0, 12.0, 1),      // Total T4 (5.0-12.0 ug/dL)
        free_t4: randomInRange('free_t4', 0.8, 1.8, 1),         // Free T4 (0.8-1.8 ng/dL)

        // Diabetes Tests
        hba1c: randomInRange('hba1c', 4.0, 6.0, 1),               // HbA1c (4.0-5.6%)
        insulin: randomInRange('insulin', 2.6, 24.9, 1),          // Insulin (2.6-24.9 uIU/mL)
        c_peptide: randomInRange('c_peptide', 0.8, 3.9, 1),       // C-peptide (0.8-3.9 ng/mL)

        // Inflammation and Immune Markers
        crp: randomInRange('crp', 0.0, 3.0, 1),                   // C-reactive protein (0.0-3.0 mg/L)
        esr: randomInRange('esr', 0, 20, 0),                      // Erythrocyte sedimentation rate (0-20 mm/hr)
        ana: Math.random() < 0.9 ? 'Negative' : 'Positive',       // Antinuclear antibody (Negative)
        rf: randomInRange('rf', 0, 14, 0),                        // Rheumatoid factor (0-14 IU/mL)

        // Vitamin Levels
        vitamin_b12: randomInRange('vitamin_b12', 200, 900, 0),   // Vitamin B12 (200-900 pg/mL)
        folate: randomInRange('folate', 2.7, 17.0, 1),            // Folate (2.7-17.0 ng/mL)
        vitamin_d: randomInRange('vitamin_d', 20, 50, 0),         // Vitamin D (20-50 ng/mL)

        // Bone Health
        phosphorus: randomInRange('phosphorus', 2.5, 4.5, 1),     // Phosphorus (2.5-4.5 mg/dL)
        magnesium: randomInRange('magnesium', 1.7, 2.2, 1),       // Magnesium (1.7-2.2 mg/dL)

        // Iron Studies
        iron: randomInRange('iron', 50, 170, 0),                  // Iron (50-170 ug/dL)
        tibc: randomInRange('tibc', 250, 450, 0),                 // Total iron binding capacity (250-450 ug/dL)
        transferrin: randomInRange('transferrin', 200, 360, 0),   // Transferrin (200-360 mg/dL)
        transferrin_saturation: randomInRange('transferrin_saturation', 15, 50, 0), // Transferrin saturation (15-50%)
        ferritin: randomInRange('ferritin', 30, 400, 0),          // Ferritin (30-400 ng/mL)

        // Kidney Function (additional)
        uric_acid: randomInRange('uric_acid', 3.5, 7.2, 1),       // Uric acid (3.5-7.2 mg/dL for males, 2.5-6.0 mg/dL for females)
        cystatin_c: randomInRange('cystatin_c', 0.5, 1.0, 1),     // Cystatin C (0.5-1.0 mg/L)

        // Liver Function (additional)
        ggt: randomInRange('ggt', 8, 61, 0),                      // Gamma-glutamyl transferase (8-61 U/L)
        ldh: randomInRange('ldh', 140, 280, 0),                   // Lactate dehydrogenase (140-280 U/L)
        alkaline_phosphatase_bone: randomInRange('alkaline_phosphatase_bone', 50, 150, 0), // Bone-specific alkaline phosphatase (50-150 U/L)

        // Pancreatic Function
        amylase: randomInRange('amylase', 30, 110, 0),            // Amylase (30-110 U/L)
        lipase: randomInRange('lipase', 10, 60, 0),               // Lipase (10-60 U/L)

        // Cardiac Markers
        bnp: randomInRange('bnp', 0, 100, 0),                     // B-type natriuretic peptide (0-100 pg/mL)
        troponin_i: randomInRange('troponin_i', 0.0, 0.04, 2),    // Troponin I (0.00-0.04 ng/mL)
        ck_mb: randomInRange('ck_mb', 0, 3.6, 1),                 // CK-MB (0-3.6 ng/mL)
        homocysteine: randomInRange('homocysteine', 5, 15, 0),    // Homocysteine (5-15 umol/L)

        // Coagulation Studies
        pt: randomInRange('pt', 11.0, 13.5, 1),                   // Prothrombin time (11.0-13.5 sec)
        inr: randomInRange('inr', 0.8, 1.2, 1),                   // International normalized ratio (0.8-1.2)
        ptt: randomInRange('ptt', 25, 35, 0),                     // Partial thromboplastin time (25-35 sec)

        // Electrolytes (additional)
        phosphate: randomInRange('phosphate', 2.5, 4.5, 1),       // Phosphate (2.5-4.5 mg/dL)

        // Hormone Levels
        cortisol: randomInRange('cortisol', 5, 25, 0),                   // Cortisol (5-25 ug/dL)
        dhea_s: randomInRange('dhea_s', 80, 560, 0),                     // DHEA-S (80-560 ug/dL)
        estradiol: randomInRange('estradiol', 10, 50, 0),                // Estradiol (10-50 pg/mL)
        fsh: randomInRange('fsh', 1.5, 12.4, 1),                         // FSH (1.5-12.4 mIU/mL)
        lh: randomInRange('lh', 1.7, 8.6, 1),                            // LH (1.7-8.6 mIU/mL)
        progesterone: randomInRange('progesterone', 0.1, 1.5, 1),        // Progesterone (0.1-1.5 ng/mL)
        testosterone_total: randomInRange('testosterone_total', 280, 1100, 0), // Total testosterone (280-1100 ng/dL)
        testosterone_free: randomInRange('testosterone_free', 5.0, 21.0, 1),  // Free testosterone (5.0-21.0 pg/mL)
        parathyroid_hormone: randomInRange('parathyroid_hormone', 10, 65, 0),  // Parathyroid hormone (10-65 pg/mL)

        // Tumor Markers
        afp: randomInRange('afp', 0, 8.1, 1),                    // Alpha-fetoprotein (0-8.1 ng/mL)
        cea: randomInRange('cea', 0, 3.0, 1),                    // Carcinoembryonic antigen (0-3.0 ng/mL)
        psa_total: randomInRange('psa_total', 0, 4.0, 1),        // Prostate-specific antigen (0-4.0 ng/mL)
        ca125: randomInRange('ca125', 0, 35, 0),                 // Cancer antigen 125 (0-35 U/mL)
        ca_19_9: randomInRange('ca_19_9', 0, 37, 0),             // Cancer antigen 19-9 (0-37 U/mL)

        // Infectious Disease Markers
        hiv: 'Negative',                                         // HIV (Negative)
        hep_b_surface_antigen: 'Negative',                       // Hepatitis B surface antigen (Negative)
        hep_c_antibody: 'Negative',                              // Hepatitis C antibody (Negative)

        // Urinalysis
        urineColor: ['Pale Yellow', 'Yellow', 'Dark Yellow', 'Amber', 'Orange', 'Red', 'Brown'][Math.floor(Math.random() * 7)],
        urineTransparency: ['Clear', 'Slightly Cloudy', 'Cloudy', 'Turbid'][Math.floor(Math.random() * 4)],
        urine_specific_gravity: randomInRange('urine_specific_gravity', 1.005, 1.030, 3), // Specific gravity (1.005-1.030)
        urinePh: randomInRange('urine_ph', 4.5, 8.0, 1),        // pH (4.5-8.0)
        urineProtein: ['Negative', 'Trace', '1+', '2+', '3+', '4+'][Math.floor(Math.random() * 6)], // Protein
        urineSugar: ['Negative', 'Trace', '1+', '2+', '3+', '4+'][Math.floor(Math.random() * 6)], // Glucose/Sugar
        urine_ketones: ['Negative', 'Trace', '1+'][Math.floor(Math.random() * 3)], // Ketones
        urine_blood: ['Negative', 'Trace', '1+'][Math.floor(Math.random() * 3)], // Blood
        urine_nitrite: ['Negative', 'Positive'][Math.floor(Math.random() * 2)], // Nitrite
        urine_leukocyte_esterase: ['Negative', 'Trace', '1+', '2+'][Math.floor(Math.random() * 4)], // Leukocyte esterase
        urineRbcs: ['0-2/HPF', '3-5/HPF', '6-10/HPF', '>10/HPF'][Math.floor(Math.random() * 4)], // RBCs
        urinePusCells: ['0-5/HPF', '6-10/HPF', '11-20/HPF', '>20/HPF'][Math.floor(Math.random() * 4)], // Pus cells
        urineCrystals: ['None', 'Calcium Oxalate', 'Uric Acid', 'Triple Phosphate', 'Amorphous'][Math.floor(Math.random() * 5)], // Crystals
        urineCasts: ['None', 'Hyaline', 'Granular', 'Waxy', 'RBC', 'WBC'][Math.floor(Math.random() * 6)], // Casts

        // Additional Specialized Tests
        vitamin_a: randomInRange('vitamin_a', 20, 60, 0),         // Vitamin A (20-60 ug/dL)
        vitamin_e: randomInRange('vitamin_e', 5.5, 17.0, 1),      // Vitamin E (5.5-17.0 mg/L)
        vitamin_k: randomInRange('vitamin_k', 0.1, 2.2, 1),       // Vitamin K (0.1-2.2 ng/mL)
        zinc: randomInRange('zinc', 70, 120, 0),                  // Zinc (70-120 ug/dL)
        copper: randomInRange('copper', 70, 140, 0),              // Copper (70-140 ug/dL)
        selenium: randomInRange('selenium', 70, 150, 0),          // Selenium (70-150 ug/L)

        // Allergy and Immunology
        ige_total: randomInRange('ige_total', 0, 100, 0),         // Total IgE (0-100 IU/mL)

        // Toxicology
        alcohol: '0.00',                                          // Blood alcohol (0.00%)

        // Genetic Markers
        apoe: ['E3/E3', 'E3/E4', 'E2/E3'][Math.floor(Math.random() * 3)], // APOE genotype

        // Nutritional Markers
        prealbumin: randomInRange('prealbumin', 20, 40, 0),       // Prealbumin (20-40 mg/dL)

        // Miscellaneous
        osmolality: randomInRange('osmolality', 275, 295, 0)      // Osmolality (275-295 mOsm/kg)
      };
    };

    // Generate health assessment data
    const generateHealthAssessment = () => {
      // Pain assessment - using numeric values (0-3) since pain_level is an integer in the database
      const painLevels = [0, 1, 2, 3]; // 0=None, 1=Mild, 2=Moderate, 3=Severe
      const painLocations = ['None', 'Back', 'Joints', 'Head', 'Chest', 'Abdomen', 'Extremities'];
      const painCharacteristics = ['Sharp', 'Dull', 'Burning', 'Throbbing', 'Stabbing', 'Aching', 'None'];
      const painDurations = ['Acute', 'Chronic', 'Intermittent', 'Constant', 'None'];
      const painAggravatingFactors = ['Movement', 'Standing', 'Sitting', 'Stress', 'Cold', 'Heat', 'None'];
      const painRelievingFactors = ['Rest', 'Medication', 'Heat', 'Cold', 'Position change', 'None'];

      // Cognitive assessment
      const cognitiveScores = [28, 29, 30, 27, 26, 25, 24, 23]; // Mini-Cog scores
      const orientationStatuses = ['Fully oriented', 'Oriented to person and place', 'Disoriented to time', 'Mild confusion'];
      const memoryStatuses = ['Intact', 'Mild impairment', 'Moderate impairment', 'No apparent issues'];
      const attentionStatuses = ['Normal', 'Easily distracted', 'Difficulty concentrating', 'No apparent issues'];

      // Mental health
      const depressionScores = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]; // PHQ-9 scores
      const anxietyScores = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10]; // GAD-7 scores
      const moodStatuses = ['Euthymic', 'Depressed', 'Anxious', 'Irritable', 'Elevated'];
      const stressLevels = ['Low', 'Moderate', 'High', 'Severe', 'None reported'];

      // Sleep quality
      const sleepQualities = ['Poor', 'Fair', 'Good', 'Excellent'];
      // Sleep duration in hours (numeric values)
      const sleepDurations = [4.5, 5.0, 5.5, 6.0, 6.5, 7.0, 7.5, 8.0, 8.5, 9.0];
      const sleepDurationTexts = ['Less than 5 hours', '5-6 hours', '7-8 hours', 'More than 8 hours'];
      const sleepDisturbances = ['None', 'Insomnia', 'Sleep apnea', 'Frequent waking', 'Nightmares', 'Restless legs'];

      // Medication adherence
      const medicationAdherences = ['High', 'Medium', 'Low', 'Not applicable'];
      const medicationSideEffects = ['None reported', 'Mild', 'Moderate', 'Severe', 'Not applicable'];

      // Assistive devices
      const assistiveDevices = ['None', 'Cane', 'Walker', 'Wheelchair', 'Hearing aid', 'Glasses', 'Dentures', 'CPAP'];

      // Functional status
      const adlStatuses = ['Independent', 'Needs minimal assistance', 'Needs moderate assistance', 'Dependent'];
      const mobilityStatuses = ['Independent', 'Independent with device', 'Needs assistance', 'Severely limited'];
      const balanceStatuses = ['Good', 'Fair', 'Poor', 'At risk for falls'];
      const gaitStatuses = ['Normal', 'Antalgic', 'Shuffling', 'Unsteady', 'Requires assistance'];

      // Skin assessment
      const skinIntegrities = ['Intact', 'Dry', 'Fragile', 'Breakdown present', 'Wound present'];
      const skinColors = ['Normal', 'Pale', 'Flushed', 'Jaundiced', 'Cyanotic'];
      const edemaStatuses = ['None', 'Mild', 'Moderate', 'Severe', '+1', '+2', '+3', '+4'];

      // Sensory assessment
      const visionStatuses = ['Normal', 'Impaired - corrected', 'Impaired - uncorrected', 'Legally blind'];
      const hearingStatuses = ['Normal', 'Mild loss', 'Moderate loss', 'Severe loss', 'Deaf'];

      // Respiratory assessment
      const respiratoryEfforts = ['Normal', 'Labored', 'Shallow', 'Deep', 'Irregular'];
      const oxygenUses = ['None', 'PRN', 'Continuous low flow', 'Continuous high flow'];
      const coughStatuses = ['None', 'Productive', 'Non-productive', 'Occasional', 'Frequent'];

      // Cardiovascular assessment
      const peripheralPulseStatuses = ['Strong', 'Weak', 'Irregular', 'Absent'];
      const edemaLocations = ['None', 'Pedal', 'Ankle', 'Pretibial', 'Sacral', 'Generalized'];

      // Gastrointestinal assessment
      const appetiteStatuses = ['Good', 'Fair', 'Poor', 'Anorexic'];
      const nauseaStatuses = ['None', 'Mild', 'Moderate', 'Severe'];
      const bowelSoundStatuses = ['Normal', 'Hyperactive', 'Hypoactive', 'Absent'];
      const bowelMovementPatterns = ['Regular', 'Constipation', 'Diarrhea', 'Irregular', 'Incontinent'];

      // Genitourinary assessment
      const urinaryPatterns = ['Normal', 'Frequency', 'Urgency', 'Hesitancy', 'Retention', 'Incontinent'];
      const urinaryDevices = ['None', 'Foley catheter', 'Suprapubic catheter', 'Intermittent catheterization'];

      return {
        // Pain assessment
        pain_level: painLevels[Math.floor(Math.random() * painLevels.length)], // Integer value
        pain_location: painLocations[Math.floor(Math.random() * painLocations.length)],
        pain_characteristics: painCharacteristics[Math.floor(Math.random() * painCharacteristics.length)],
        pain_duration: painDurations[Math.floor(Math.random() * painDurations.length)],
        pain_aggravating_factors: painAggravatingFactors[Math.floor(Math.random() * painAggravatingFactors.length)],
        pain_relieving_factors: painRelievingFactors[Math.floor(Math.random() * painRelievingFactors.length)],

        // Cognitive assessment
        cognitive_impairment_score: cognitiveScores[Math.floor(Math.random() * cognitiveScores.length)],
        cognitive_test_results: 'Patient performed well on orientation and recall tasks',
        orientation_status: orientationStatuses[Math.floor(Math.random() * orientationStatuses.length)],
        memory_status: memoryStatuses[Math.floor(Math.random() * memoryStatuses.length)],
        attention_status: attentionStatuses[Math.floor(Math.random() * attentionStatuses.length)],

        // Mental health
        depression_score: depressionScores[Math.floor(Math.random() * depressionScores.length)],
        anxiety_score: anxietyScores[Math.floor(Math.random() * anxietyScores.length)],
        mood_status: moodStatuses[Math.floor(Math.random() * moodStatuses.length)],
        stress_level: stressLevels[Math.floor(Math.random() * stressLevels.length)],

        // Sleep assessment
        sleep_quality: sleepQualities[Math.floor(Math.random() * sleepQualities.length)],
        sleep_duration: sleepDurations[Math.floor(Math.random() * sleepDurations.length)],
        sleep_duration_text: sleepDurationTexts[Math.floor(Math.random() * sleepDurationTexts.length)],
        sleep_disturbances: sleepDisturbances[Math.floor(Math.random() * sleepDisturbances.length)],

        // Medication assessment
        medication_adherence: medicationAdherences[Math.floor(Math.random() * medicationAdherences.length)],
        medication_side_effects: medicationSideEffects[Math.floor(Math.random() * medicationSideEffects.length)],

        // Assistive devices
        assistive_devices_used: assistiveDevices[Math.floor(Math.random() * assistiveDevices.length)],

        // Functional status
        adl_status: adlStatuses[Math.floor(Math.random() * adlStatuses.length)],
        mobility_status: mobilityStatuses[Math.floor(Math.random() * mobilityStatuses.length)],
        balance_status: balanceStatuses[Math.floor(Math.random() * balanceStatuses.length)],
        gait_status: gaitStatuses[Math.floor(Math.random() * gaitStatuses.length)],

        // Skin assessment
        skin_integrity: skinIntegrities[Math.floor(Math.random() * skinIntegrities.length)],
        skin_color: skinColors[Math.floor(Math.random() * skinColors.length)],
        edema_status: edemaStatuses[Math.floor(Math.random() * edemaStatuses.length)],

        // Sensory assessment
        vision_status: visionStatuses[Math.floor(Math.random() * visionStatuses.length)],
        hearing_status: hearingStatuses[Math.floor(Math.random() * hearingStatuses.length)],

        // Respiratory assessment
        respiratory_effort: respiratoryEfforts[Math.floor(Math.random() * respiratoryEfforts.length)],
        oxygen_use: oxygenUses[Math.floor(Math.random() * oxygenUses.length)],
        cough_status: coughStatuses[Math.floor(Math.random() * coughStatuses.length)],

        // Cardiovascular assessment
        peripheral_pulse_status: peripheralPulseStatuses[Math.floor(Math.random() * peripheralPulseStatuses.length)],
        edema_location: edemaLocations[Math.floor(Math.random() * edemaLocations.length)],

        // Gastrointestinal assessment
        appetite_status: appetiteStatuses[Math.floor(Math.random() * appetiteStatuses.length)],
        nausea_status: nauseaStatuses[Math.floor(Math.random() * nauseaStatuses.length)],
        bowel_sound_status: bowelSoundStatuses[Math.floor(Math.random() * bowelSoundStatuses.length)],
        bowel_movement_pattern: bowelMovementPatterns[Math.floor(Math.random() * bowelMovementPatterns.length)],

        // Genitourinary assessment
        urinary_pattern: urinaryPatterns[Math.floor(Math.random() * urinaryPatterns.length)],
        urinary_device: urinaryDevices[Math.floor(Math.random() * urinaryDevices.length)]
      };
    };

    // Generate social determinants data
    const generateSocialDeterminants = () => {
      const transportationAccess = ['Good', 'Limited', 'None'];
      const financialConcern = ['None', 'Low', 'Moderate', 'High'];
      const socialSupport = ['Strong', 'Moderate', 'Limited', 'None'];
      const housingStatuses = ['Stable', 'Temporary', 'Unstable', 'Homeless'];
      const foodSecurityStatuses = ['Secure', 'Insecure', 'Very insecure'];
      const employmentStatuses = ['Employed full-time', 'Employed part-time', 'Unemployed', 'Retired', 'Disabled'];
      const educationLevels = ['Less than high school', 'High school', 'Some college', 'Associate degree', 'Bachelor degree', 'Graduate degree'];
      const insuranceStatuses = ['Private', 'Medicare', 'Medicaid', 'Dual eligible', 'Uninsured', 'Other'];
      const caregiverStatuses = ['None needed', 'Family caregiver', 'Professional caregiver', 'Needs caregiver but none available'];
      const communityEngagements = ['Active', 'Moderate', 'Limited', 'None'];
      const religiousInvolvements = ['Active', 'Occasional', 'None', 'Declined to state'];
      const safetyRisks = ['None identified', 'Fall risk', 'Elder abuse risk', 'Self-neglect risk', 'Environmental hazards'];
      const technologyAccess = ['Full access', 'Limited access', 'No access'];

      return {
        transportation_access: transportationAccess[Math.floor(Math.random() * transportationAccess.length)],
        financial_concern: financialConcern[Math.floor(Math.random() * financialConcern.length)],
        social_support_network: socialSupport[Math.floor(Math.random() * socialSupport.length)],
        housing_status: housingStatuses[Math.floor(Math.random() * housingStatuses.length)],
        food_security_status: foodSecurityStatuses[Math.floor(Math.random() * foodSecurityStatuses.length)],
        employment_status: employmentStatuses[Math.floor(Math.random() * employmentStatuses.length)],
        education_level: educationLevels[Math.floor(Math.random() * educationLevels.length)],
        insurance_status: insuranceStatuses[Math.floor(Math.random() * insuranceStatuses.length)],
        caregiver_status: caregiverStatuses[Math.floor(Math.random() * caregiverStatuses.length)],
        community_engagement: communityEngagements[Math.floor(Math.random() * communityEngagements.length)],
        religious_involvement: religiousInvolvements[Math.floor(Math.random() * religiousInvolvements.length)],
        safety_risks: safetyRisks[Math.floor(Math.random() * safetyRisks.length)],
        technology_access: technologyAccess[Math.floor(Math.random() * technologyAccess.length)]
      };
    };

    // Generate medical conditions and allergies
    const generateMedicalInfo = () => {
      const conditions = [
        'Hypertension',
        'Type 2 Diabetes',
        'Osteoarthritis',
        'Hyperlipidemia',
        'COPD',
        'Atrial Fibrillation',
        'Osteoporosis',
        'Hypothyroidism',
        'Chronic Kidney Disease',
        'Depression',
        'Congestive Heart Failure',
        'Coronary Artery Disease',
        'Asthma',
        'Gastroesophageal Reflux Disease',
        'Alzheimer\'s Disease',
        'Parkinson\'s Disease',
        'Rheumatoid Arthritis',
        'Chronic Pain Syndrome',
        'Obesity',
        'Anxiety Disorder'
      ];

      const allergies = [
        'No known allergies',
        'Penicillin',
        'Sulfa drugs',
        'NSAIDs',
        'Shellfish',
        'Peanuts',
        'Latex',
        'Contrast dye',
        'Eggs',
        'Dairy',
        'Wheat',
        'Soy',
        'Tree nuts',
        'Codeine',
        'Morphine',
        'Tetracycline',
        'Aspirin',
        'Iodine'
      ];

      const surgeries = [
        'None',
        'Appendectomy',
        'Cholecystectomy',
        'Hip replacement',
        'Knee replacement',
        'Coronary artery bypass',
        'Cataract surgery',
        'Hysterectomy',
        'Prostatectomy',
        'Tonsillectomy',
        'Hernia repair',
        'Spinal fusion',
        'Thyroidectomy'
      ];

      const familyHistories = [
        'Cardiovascular disease',
        'Diabetes',
        'Cancer',
        'Hypertension',
        'Stroke',
        'Alzheimer\'s disease',
        'Osteoporosis',
        'Rheumatoid arthritis',
        'Mental illness',
        'Substance abuse',
        'None significant'
      ];

      const immunizations = [
        'Influenza (current season)',
        'Pneumococcal',
        'Tetanus/Diphtheria/Pertussis',
        'Shingles',
        'COVID-19',
        'Hepatitis B',
        'None recent'
      ];

      const tobaccoUseStatuses = [
        'Never smoker',
        'Former smoker - quit over 10 years ago',
        'Former smoker - quit 1-10 years ago',
        'Former smoker - quit within last year',
        'Current smoker - light (less than 10 cigarettes/day)',
        'Current smoker - moderate (10-20 cigarettes/day)',
        'Current smoker - heavy (more than 20 cigarettes/day)'
      ];

      const alcoholUseStatuses = [
        'Non-drinker',
        'Occasional (less than 1 drink per week)',
        'Light (1-3 drinks per week)',
        'Moderate (4-7 drinks per week)',
        'Heavy (more than 7 drinks per week)',
        'History of alcohol use disorder, currently abstinent'
      ];

      const substanceUseStatuses = [
        'None',
        'Marijuana - occasional',
        'Marijuana - regular',
        'History of prescription drug misuse',
        'History of illicit drug use, currently abstinent',
        'Current illicit drug use'
      ];

      const dietaryRestrictions = [
        'None',
        'Vegetarian',
        'Vegan',
        'Gluten-free',
        'Dairy-free',
        'Low sodium',
        'Low fat',
        'Diabetic',
        'Renal',
        'Kosher',
        'Halal'
      ];

      const exerciseFrequencies = [
        'None',
        'Rarely (less than once per week)',
        'Occasional (1-2 times per week)',
        'Regular (3-5 times per week)',
        'Daily'
      ];

      const exerciseTypes = [
        'Walking',
        'Swimming',
        'Cycling',
        'Strength training',
        'Yoga',
        'Tai Chi',
        'Gardening',
        'None'
      ];

      // Randomly select 0-3 conditions
      const numConditions = Math.floor(Math.random() * 4);
      const selectedConditions = [];
      for (let i = 0; i < numConditions; i++) {
        const condition = conditions[Math.floor(Math.random() * conditions.length)];
        if (!selectedConditions.includes(condition)) {
          selectedConditions.push(condition);
        }
      }

      // Randomly select 0-2 allergies
      const numAllergies = Math.floor(Math.random() * 3);
      const selectedAllergies = [];
      for (let i = 0; i < numAllergies; i++) {
        const allergy = allergies[Math.floor(Math.random() * allergies.length)];
        if (!selectedAllergies.includes(allergy)) {
          selectedAllergies.push(allergy);
        }
      }

      // Randomly select 0-2 surgeries
      const numSurgeries = Math.floor(Math.random() * 3);
      const selectedSurgeries = [];
      for (let i = 0; i < numSurgeries; i++) {
        const surgery = surgeries[Math.floor(Math.random() * surgeries.length)];
        if (!selectedSurgeries.includes(surgery) && surgery !== 'None') {
          selectedSurgeries.push(surgery);
        }
      }

      // Randomly select 0-3 family history items
      const numFamilyHistory = Math.floor(Math.random() * 4);
      const selectedFamilyHistory = [];
      for (let i = 0; i < numFamilyHistory; i++) {
        const history = familyHistories[Math.floor(Math.random() * familyHistories.length)];
        if (!selectedFamilyHistory.includes(history) && history !== 'None significant') {
          selectedFamilyHistory.push(history);
        }
      }

      // Randomly select 0-3 immunizations
      const numImmunizations = Math.floor(Math.random() * 4);
      const selectedImmunizations = [];
      for (let i = 0; i < numImmunizations; i++) {
        const immunization = immunizations[Math.floor(Math.random() * immunizations.length)];
        if (!selectedImmunizations.includes(immunization) && immunization !== 'None recent') {
          selectedImmunizations.push(immunization);
        }
      }

      // Randomly select 0-2 exercise types
      const numExerciseTypes = Math.floor(Math.random() * 3);
      const selectedExerciseTypes = [];
      for (let i = 0; i < numExerciseTypes; i++) {
        const exerciseType = exerciseTypes[Math.floor(Math.random() * exerciseTypes.length)];
        if (!selectedExerciseTypes.includes(exerciseType) && exerciseType !== 'None') {
          selectedExerciseTypes.push(exerciseType);
        }
      }

      // Randomly select 0-2 dietary restrictions
      const numDietaryRestrictions = Math.floor(Math.random() * 3);
      const selectedDietaryRestrictions = [];
      for (let i = 0; i < numDietaryRestrictions; i++) {
        const restriction = dietaryRestrictions[Math.floor(Math.random() * dietaryRestrictions.length)];
        if (!selectedDietaryRestrictions.includes(restriction) && restriction !== 'None') {
          selectedDietaryRestrictions.push(restriction);
        }
      }

      return {
        current_medical_conditions: selectedConditions.length > 0 ? selectedConditions.join(', ') : 'None',
        allergies: selectedAllergies.length > 0 ? selectedAllergies.join(', ') : 'No known allergies',
        past_surgeries: selectedSurgeries.length > 0 ? selectedSurgeries.join(', ') : 'None',
        family_history: selectedFamilyHistory.length > 0 ? selectedFamilyHistory.join(', ') : 'None significant',
        immunizations: selectedImmunizations.length > 0 ? selectedImmunizations.join(', ') : 'None recent',
        tobacco_use: tobaccoUseStatuses[Math.floor(Math.random() * tobaccoUseStatuses.length)],
        alcohol_use: alcoholUseStatuses[Math.floor(Math.random() * alcoholUseStatuses.length)],
        substance_use: substanceUseStatuses[Math.floor(Math.random() * substanceUseStatuses.length)],
        dietary_restrictions: selectedDietaryRestrictions.length > 0 ? selectedDietaryRestrictions.join(', ') : 'None',
        exercise_frequency: exerciseFrequencies[Math.floor(Math.random() * exerciseFrequencies.length)],
        exercise_type: selectedExerciseTypes.length > 0 ? selectedExerciseTypes.join(', ') : 'None'
      };
    };

    // Generate comprehensive visit notes
    const generateVisitNotes = (visitReason) => {
      const templates = {
        'Annual checkup': 'Patient presents for annual wellness visit. Reports feeling generally well. No acute complaints. Preventive screenings reviewed and updated.',
        'Follow-up visit': 'Patient returns for follow-up. Reports improvement in previously reported symptoms. Medication regimen appears effective with no significant side effects.',
        'Blood pressure check': 'Patient presents for blood pressure monitoring. Has been taking medications as prescribed. Reports occasional dizziness when standing quickly.',
        'Diabetes monitoring': 'Patient presents for diabetes management. Blood glucose logs reviewed. Patient reports adherence to diet and medication regimen with occasional lapses.',
        'Joint pain': 'Patient reports ongoing joint pain, primarily in knees and hips. Pain is worse with activity and improves with rest. Currently managing with OTC pain relievers.',
        'Respiratory symptoms': 'Patient presents with respiratory concerns including occasional shortness of breath and productive cough. No fever reported. Symptoms worse in the morning.',
        'Medication review': 'Comprehensive medication review performed. Patient reports taking all medications as prescribed. No significant side effects reported.',
        'Preventive care': 'Patient presents for preventive care visit. Immunizations updated. Cancer screenings reviewed and ordered as appropriate for age and risk factors.',
        'Chronic disease management': 'Patient presents for ongoing management of chronic conditions. Current treatment plan reviewed and appears effective. Minor adjustments made to medication regimen.',
        'General consultation': 'Patient presents with multiple concerns including sleep disturbances, occasional dizziness, and mild fatigue. Comprehensive evaluation performed.'
      };

      const baseNote = templates[visitReason] || 'Patient presents for scheduled visit. Comprehensive evaluation performed.';

      return baseNote + ' Vital signs stable. Labs reviewed with patient. Preventive care measures discussed. Follow-up scheduled as appropriate.';
    };

    // Get lab values
    const labValues = generateLabValues();

    // Get health assessment data
    const healthAssessment = generateHealthAssessment();

    // Get social determinants data
    const socialDeterminants = generateSocialDeterminants();

    // Get medical conditions and allergies
    const medicalInfo = generateMedicalInfo();

    // Select a visit reason
    const visitReason = visitReasons[Math.floor(Math.random() * visitReasons.length)];

    // Generate visit notes
    const visitNotes = generateVisitNotes(visitReason);

    // Random visit data
    const visitData = {
      patient_id: patientId,
      doctor_id: doctorId,
      visit_date: visitDateTime, // This is a timestamp in PostgreSQL
      end_time: endTimeStr,
      visit_reason: visitReason,
      status: 'completed',

      // Contact information from patient record
      phone: patientPhone,
      email: patientEmail,
      address: patientAddress,
      emergency_contact_name: emergencyContactName,
      emergency_contact_phone: emergencyContactPhone,
      emergency_contact_relationship: emergencyContactRelationship,

      // Vital Signs
      lying_bp_systolic: systolic,
      lying_bp_diastolic: diastolic,
      sitting_bp_systolic: systolic - Math.floor(Math.random() * 5),
      sitting_bp_diastolic: diastolic - Math.floor(Math.random() * 5),
      standing_bp_systolic: systolic + Math.floor(Math.random() * 10) - 5,
      standing_bp_diastolic: diastolic + Math.floor(Math.random() * 10) - 5,
      lying_heart_rate: lyingHeartRate,
      standing_heart_rate: standingHeartRate,
      sitting_heart_rate: sittingHeartRate,
      heart_rate: heartRate,
      heart_rhythm: heartRhythms[Math.floor(Math.random() * heartRhythms.length)],
      temperature: parseFloat(temperature.toFixed(1)), // Temperature in Celsius
      respiratory_rate: 12 + Math.floor(Math.random() * 8), // 12-20
      pulse_oximetry: 95 + Math.floor(Math.random() * 5), // 95-100

      // Physical measurements
      weight: weight.toFixed(1),
      height: height.toFixed(1),
      bmi: bmi.toFixed(1),
      calf_circumference: calfCircumference,

      // Health Status
      activity_level: activityLevels[Math.floor(Math.random() * activityLevels.length)],
      nutritional_status: nutritionalStatuses[Math.floor(Math.random() * nutritionalStatuses.length)],
      hydration_status: hydrationStatuses[Math.floor(Math.random() * hydrationStatuses.length)],

      // Lab Results - Basic metabolic panel
      blood_glucose: labValues.glucose,
      sodium: labValues.sodium,
      potassium: labValues.potassium,
      chloride: labValues.chloride,
      co2: labValues.co2,
      creatinine: labValues.creatinine,
      blood_urea_nitrogen: labValues.bun, // Use blood_urea_nitrogen instead of bun
      calcium: labValues.calcium,
      magnesium: labValues.magnesium,
      phosphate: labValues.phosphate,
      uric_acid: labValues.uric_acid,

      // Lipid panel
      cholesterol_total: labValues.cholesterol_total,
      hdl_cholesterol: labValues.hdl_cholesterol,
      ldl_cholesterol: labValues.ldl_cholesterol,
      triglycerides: labValues.triglycerides,
      vldl: labValues.vldl,

      // Liver function tests
      alt: labValues.alt,
      ast: labValues.ast,
      alp: labValues.alp,
      bilirubin_t: labValues.bilirubin_t,
      bilirubin_d: labValues.bilirubin_d,
      total_protein: labValues.total_protein,
      albumin: labValues.albumin,
      ggt: labValues.ggt,

      // Complete blood count
      rbc: labValues.rbc,
      wbc: labValues.wbc,
      hemoglobin: labValues.hemoglobin,
      hematocrit: labValues.hematocrit,
      platelets: labValues.platelets,

      // Red blood cell indices
      mcv: labValues.mcv,
      mch: labValues.mch,
      mchc: labValues.mchc,
      rdw: labValues.rdw,

      // White blood cell differential
      neutrophils: labValues.neutrophils,
      lymphocytes: labValues.lymphocytes,
      monocytes: labValues.monocytes,
      eosinophils: labValues.eosinophils,
      basophils: labValues.basophils,

      // Iron studies
      iron: labValues.iron,
      ferritin: labValues.ferritin,

      // Cancer markers
      ca125: labValues.ca125,

      // Urinalysis
      urineColor: labValues.urineColor,
      urineTransparency: labValues.urineTransparency,
      urinePh: labValues.urinePh,
      urineProtein: labValues.urineProtein,
      urineSugar: labValues.urineSugar,
      urineRbcs: labValues.urineRbcs,
      urinePusCells: labValues.urinePusCells,
      urineCrystals: labValues.urineCrystals,
      urineCasts: labValues.urineCasts,

      // Lab Results - Bone health
      vitamin_d: labValues.vitamin_d,
      calcium: labValues.calcium,
      phosphorus: labValues.phosphorus,

      // Lab Results - Thyroid function
      tsh: labValues.tsh,
      t3_total: labValues.t3_total,
      free_t3: labValues.free_t3,
      t4_total: labValues.t4_total,
      free_t4: labValues.free_t4,

      // Lab Results - Inflammation markers
      crp: labValues.crp,
      esr: labValues.esr,

      // Lab Results - Diabetes markers
      hba1c: labValues.hba1c,
      insulin: labValues.insulin,
      c_peptide: labValues.c_peptide,

      // Lab Results - Kidney function
      egfr: labValues.egfr,
      cystatin_c: labValues.cystatin_c,

      // Lab Results - Cardiac markers
      bnp: labValues.bnp,
      troponin_i: labValues.troponin_i,
      ck_mb: labValues.ck_mb,
      homocysteine: labValues.homocysteine,

      // Lab Results - Coagulation studies
      pt: labValues.pt,
      inr: labValues.inr,
      ptt: labValues.ptt,

      // Lab Results - Iron studies
      iron: labValues.iron,
      tibc: labValues.tibc,
      transferrin: labValues.transferrin,
      transferrin_saturation: labValues.transferrin_saturation,
      ferritin: labValues.ferritin,

      // Lab Results - Vitamin levels
      vitamin_b12: labValues.vitamin_b12,
      folate: labValues.folate,
      vitamin_a: labValues.vitamin_a,
      vitamin_e: labValues.vitamin_e,
      vitamin_k: labValues.vitamin_k,

      // Lab Results - Minerals
      zinc: labValues.zinc,
      copper: labValues.copper,
      selenium: labValues.selenium,

      // Lab Results - Pancreatic function
      amylase: labValues.amylase,
      lipase: labValues.lipase,

      // Lab Results - Hormone levels
      cortisol: labValues.cortisol,
      dhea_s: labValues.dhea_s,
      estradiol: labValues.estradiol,
      fsh: labValues.fsh,
      lh: labValues.lh,
      progesterone: labValues.progesterone,
      testosterone_total: labValues.testosterone_total,
      testosterone_free: labValues.testosterone_free,
      parathyroid_hormone: labValues.parathyroid_hormone,

      // Lab Results - Tumor markers
      afp: labValues.afp,
      cea: labValues.cea,
      psa_total: labValues.psa_total,
      ca_19_9: labValues.ca_19_9,

      // Lab Results - Additional Urinalysis
      urine_specific_gravity: labValues.urine_specific_gravity,
      urine_ketones: labValues.urine_ketones,
      urine_blood: labValues.urine_blood,
      urine_nitrite: labValues.urine_nitrite,
      urine_leukocyte_esterase: labValues.urine_leukocyte_esterase,

      // Lab Results - Miscellaneous
      osmolality: labValues.osmolality,
      prealbumin: labValues.prealbumin,

      // Health Assessment - Pain
      pain_level: healthAssessment.pain_level,
      pain_location: healthAssessment.pain_location,
      pain_characteristics: healthAssessment.pain_characteristics,
      pain_duration: healthAssessment.pain_duration,
      pain_aggravating_factors: healthAssessment.pain_aggravating_factors,
      pain_relieving_factors: healthAssessment.pain_relieving_factors,

      // Health Assessment - Cognitive
      cognitive_impairment_score: healthAssessment.cognitive_impairment_score,
      cognitive_test_results: healthAssessment.cognitive_test_results,
      orientation_status: healthAssessment.orientation_status,
      memory_status: healthAssessment.memory_status,
      attention_status: healthAssessment.attention_status,

      // Health Assessment - Mental health
      depression_score: healthAssessment.depression_score,
      anxiety_score: healthAssessment.anxiety_score,
      mood_status: healthAssessment.mood_status,
      stress_level: healthAssessment.stress_level,

      // Health Assessment - Sleep
      sleep_quality: healthAssessment.sleep_quality,
      sleep_duration: healthAssessment.sleep_duration,
      sleep_disturbances: healthAssessment.sleep_disturbances,

      // Health Assessment - Medication
      medication_adherence: healthAssessment.medication_adherence,
      medication_side_effects: healthAssessment.medication_side_effects,

      // Health Assessment - Assistive devices
      assistive_devices_used: healthAssessment.assistive_devices_used,

      // Health Assessment - Functional status
      adl_status: healthAssessment.adl_status,
      mobility_status: healthAssessment.mobility_status,
      balance_status: healthAssessment.balance_status,
      gait_status: healthAssessment.gait_status,

      // Health Assessment - Skin
      skin_integrity: healthAssessment.skin_integrity,
      skin_color: healthAssessment.skin_color,
      edema_status: healthAssessment.edema_status,

      // Health Assessment - Sensory
      vision_status: healthAssessment.vision_status,
      hearing_status: healthAssessment.hearing_status,

      // Health Assessment - Respiratory
      respiratory_effort: healthAssessment.respiratory_effort,
      oxygen_use: healthAssessment.oxygen_use,
      cough_status: healthAssessment.cough_status,

      // Health Assessment - Cardiovascular
      peripheral_pulse_status: healthAssessment.peripheral_pulse_status,
      edema_location: healthAssessment.edema_location,

      // Health Assessment - Gastrointestinal
      appetite_status: healthAssessment.appetite_status,
      nausea_status: healthAssessment.nausea_status,
      bowel_sound_status: healthAssessment.bowel_sound_status,
      bowel_movement_pattern: healthAssessment.bowel_movement_pattern,

      // Health Assessment - Genitourinary
      urinary_pattern: healthAssessment.urinary_pattern,
      urinary_device: healthAssessment.urinary_device,

      // Social Determinants
      transportation_access: socialDeterminants.transportation_access,
      financial_concern: socialDeterminants.financial_concern,
      social_support_network: socialDeterminants.social_support_network,
      housing_status: socialDeterminants.housing_status,
      food_security_status: socialDeterminants.food_security_status,
      employment_status: socialDeterminants.employment_status,
      education_level: socialDeterminants.education_level,
      insurance_status: socialDeterminants.insurance_status,
      caregiver_status: socialDeterminants.caregiver_status,
      community_engagement: socialDeterminants.community_engagement,
      religious_involvement: socialDeterminants.religious_involvement,
      safety_risks: socialDeterminants.safety_risks,
      technology_access: socialDeterminants.technology_access,

      // Medical Information
      current_medical_conditions: medicalInfo.current_medical_conditions,
      allergies: medicalInfo.allergies,
      past_surgeries: medicalInfo.past_surgeries,
      family_history: medicalInfo.family_history,
      immunizations: medicalInfo.immunizations,
      tobacco_use: medicalInfo.tobacco_use,
      alcohol_use: medicalInfo.alcohol_use,
      substance_use: medicalInfo.substance_use,
      dietary_restrictions: medicalInfo.dietary_restrictions,
      exercise_frequency: medicalInfo.exercise_frequency,
      exercise_type: medicalInfo.exercise_type,

      // Notes
      notes: visitNotes,
      created_by: adminId
    };

    // First, check which columns exist in the patient_visits table
    const columnsResult = await pool.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'patient_visits'
      AND table_schema = 'public'
    `);

    const existingColumns = columnsResult.rows.map(row => row.column_name);

    // Build dynamic query based on existing columns
    let columns = [];
    let values = [];
    let placeholders = [];
    let paramIndex = 1;

    // Helper function to add a field if it exists in the database
    const addFieldIfExists = (fieldName, value) => {
      if (existingColumns.includes(fieldName)) {
        columns.push(fieldName);
        values.push(value);
        placeholders.push(`$${paramIndex++}`);
      }
    };

    // Define field mappings for columns that might have different names
    const fieldMappings = {
      // Add mappings for fields that might have different names in the database
      'lying_heart_rate': ['lying_heart_rate', 'resting_heart_rate', 'heart_rate_lying'],
      'standing_heart_rate': ['standing_heart_rate', 'heart_rate_standing'],
      'sitting_heart_rate': ['sitting_heart_rate', 'heart_rate_sitting'],
      'ldl_cholesterol': ['ldl', 'ldl_cholesterol'],
      'hdl_cholesterol': ['hdl', 'hdl_cholesterol'],
      'bilirubin_t': ['bilirubin_t', 'bilirubin_total', 'total_bilirubin'],
      'bilirubin_d': ['bilirubin_d', 'bilirubin_direct', 'direct_bilirubin'],
      'total_protein': ['total_protein', 'protein_total'],
      'magnesium': ['magnesium', 'mg'],
      'albumin': ['albumin', 'alb'],
      'vitamin_b12': ['vitamin_b12', 'b12'],
      'folate': ['folate', 'folic_acid'],
      'ferritin': ['ferritin', 'fer'],
      'parathyroid_hormone': ['parathyroid_hormone', 'pth'],
      't3_total': ['t3_total', 't3', 'total_t3'],
      'free_t3': ['free_t3', 't3_free'],
      't4_total': ['t4_total', 't4', 'total_t4'],
      'free_t4': ['free_t4', 't4_free'],
      'calf_circumference': ['calf_circumference'],
      'alp': ['alp', 'alkaline_phosphatase', 'alkaline_phosphatase_bone'],
      'hemoglobin': ['hemoglobin', 'hgb', 'hb'],
      'platelets': ['platelets', 'platelet_count'],
      'urineColor': ['urineColor', 'urine_color'],
      'urineTransparency': ['urineTransparency', 'urine_transparency', 'urine_clarity'],
      'urinePh': ['urinePh', 'urine_ph'],
      'urineProtein': ['urineProtein', 'urine_protein'],
      'urineSugar': ['urineSugar', 'urine_sugar', 'urine_glucose'],
      'urineRbcs': ['urineRbcs', 'urine_rbcs'],
      'urinePusCells': ['urinePusCells', 'urine_pus_cells'],
      'urineCrystals': ['urineCrystals', 'urine_crystals'],
      'urineCasts': ['urineCasts', 'urine_casts'],
      'ca125': ['ca125', 'ca_125']
    };

    // Add all fields from visitData if they exist in the database
    Object.entries(visitData).forEach(([key, value]) => {
      // Skip null or undefined values
      if (value === null || value === undefined) {
        return;
      }

      // First try the direct field name
      if (existingColumns.includes(key)) {
        columns.push(key);
        values.push(value);
        placeholders.push(`$${paramIndex++}`);
      }
      // If the field has mappings, try those alternative names
      else if (fieldMappings[key]) {
        const alternativeNames = fieldMappings[key];
        for (const altName of alternativeNames) {
          if (existingColumns.includes(altName)) {
            columns.push(altName);
            values.push(value);
            placeholders.push(`$${paramIndex++}`);
            console.log(`Using alternative column name ${altName} for ${key}`);
            break;
          }
        }
      }
    });

    // Build and execute the query
    const query = `
      INSERT INTO patient_visits (${columns.join(', ')})
      VALUES (${placeholders.join(', ')})
      RETURNING *
    `;

    console.log(`Inserting visit with ${columns.length} fields`);

    // Log the fields that are being inserted
    console.log('Fields being inserted:');
    columns.forEach((col, index) => {
      console.log(`${col}: ${values[index]}`);
    });

    // Log fields that were in visitData but not found in the database
    console.log('Fields not found in database:');
    Object.keys(visitData).forEach(key => {
      if (!columns.includes(key) && !columns.some(col => fieldMappings[key] && fieldMappings[key].includes(col))) {
        console.log(`- ${key}`);
      }
    });

    const insertResult = await pool.query(query, values);

    // Return the created visit
    res.json({
      success: true,
      msg: `Generated visit for patient ID: ${patientId}`,
      visit: insertResult.rows[0]
    });
  } catch (err) {
    console.error(`Error generating visit for patient:`, err.message);
    res.status(500).json({
      error: 'Error generating visit',
      message: err.message
    });
  }
});

/**
 * @route   POST api/debug/generate-sample-data/:table
 * @desc    Generate random sample data for a table
 * @access  Private/Admin
 */
router.post('/generate-sample-data/:table', [auth, adminCheck], async (req, res) => {
  try {
    const { table } = req.params;
    const { count = 5 } = req.body;
    console.log(`Debug generate sample data endpoint called for table ${table} by admin:`, req.user.id);

    // Validate table name to prevent SQL injection
    const tableNameRegex = /^[a-zA-Z0-9_]+$/;
    if (!tableNameRegex.test(table)) {
      return res.status(400).json({ msg: 'Invalid table name' });
    }

    // Limit the number of records that can be generated at once
    const recordCount = Math.min(parseInt(count), 50);

    // Get column information
    const columnsResult = await pool.query(`
      SELECT
        column_name,
        data_type,
        is_nullable,
        column_default
      FROM
        information_schema.columns
      WHERE
        table_schema = 'public'
        AND table_name = $1
      ORDER BY
        ordinal_position
    `, [table]);

    // Get primary key information
    const pkResult = await pool.query(`
      SELECT
        kcu.column_name
      FROM
        information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
      WHERE
        tc.constraint_type = 'PRIMARY KEY'
        AND tc.table_schema = 'public'
        AND tc.table_name = $1
    `, [table]);

    const primaryKeys = pkResult.rows.map(row => row.column_name);

    // Generate sample data based on the table
    let generatedRecords = [];

    if (table === 'patient_visits') {
      // For patient visits, we need to get valid patient and doctor IDs, and patient contact information
      const patientResult = await pool.query('SELECT patient_id, phone, email, address, emergency_contact_name, emergency_contact_phone, emergency_contact_relationship FROM patients LIMIT 10');
      const doctorResult = await pool.query('SELECT doctor_id FROM doctors LIMIT 10');
      const userResult = await pool.query('SELECT user_id FROM users WHERE role = \'admin\' LIMIT 1');

      if (patientResult.rows.length === 0 || doctorResult.rows.length === 0 || userResult.rows.length === 0) {
        return res.status(400).json({ msg: 'Cannot generate sample visits: missing patients, doctors, or admin users' });
      }

      const patients = patientResult.rows;
      const doctorIds = doctorResult.rows.map(row => row.doctor_id);
      const adminId = userResult.rows[0].user_id;

      // Generate random visit data
      for (let i = 0; i < recordCount; i++) {
        // Select a random patient
        const randomPatient = patients[Math.floor(Math.random() * patients.length)];

        // Create a random date within the last year
        const visitDate = new Date();
        visitDate.setDate(visitDate.getDate() - Math.floor(Math.random() * 365));

        // Random time between 8 AM and 5 PM
        const hours = 8 + Math.floor(Math.random() * 9);
        const minutes = Math.floor(Math.random() * 4) * 15; // 0, 15, 30, or 45 minutes
        visitDate.setHours(hours, minutes, 0, 0);

        // End time is 30 minutes after start time
        const endTime = new Date(visitDate);
        endTime.setMinutes(endTime.getMinutes() + 30);

        // Random vital signs
        const systolic = 110 + Math.floor(Math.random() * 40); // 110-150
        const diastolic = 70 + Math.floor(Math.random() * 20); // 70-90
        const heartRate = 60 + Math.floor(Math.random() * 40); // 60-100
        const lyingHeartRate = heartRate - Math.floor(Math.random() * 5); // Slightly lower when lying
        const standingHeartRate = heartRate + Math.floor(Math.random() * 10); // Slightly higher when standing
        const temperature = 36.1 + (Math.random() * 1.1); // 36.1-37.2°C
        const weight = 50 + Math.floor(Math.random() * 50); // 50-100 kg
        const height = 150 + Math.floor(Math.random() * 50); // 150-200 cm
        const bmi = weight / ((height / 100) * (height / 100)); // Calculate BMI

        // Random lab values
        const bloodGlucose = 70 + Math.floor(Math.random() * 130); // 70-200
        const cholesterolTotal = 150 + Math.floor(Math.random() * 100); // 150-250

        // Visit reasons
        const visitReasons = [
          'Annual checkup',
          'Follow-up visit',
          'Blood pressure check',
          'Diabetes monitoring',
          'Joint pain',
          'Respiratory symptoms',
          'Medication review',
          'Preventive care',
          'Chronic disease management',
          'General consultation'
        ];

        // Activity levels
        const activityLevels = [
          'Sedentary',
          'Light activity',
          'Moderate activity',
          'Active',
          'Very active'
        ];

        // Nutritional status
        const nutritionalStatuses = [
          'Poor',
          'Fair',
          'Good',
          'Excellent'
        ];

        // Hydration status
        const hydrationStatuses = [
          'Dehydrated',
          'Slightly dehydrated',
          'Well hydrated',
          'Optimal hydration'
        ];

        // Random visit data
        const visitData = {
          patient_id: randomPatient.patient_id,
          doctor_id: doctorIds[Math.floor(Math.random() * doctorIds.length)],
          visit_date: visitDate.toISOString(),
          visit_time: `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:00`,
          end_time: `${endTime.getHours().toString().padStart(2, '0')}:${endTime.getMinutes().toString().padStart(2, '0')}:00`,
          visit_reason: visitReasons[Math.floor(Math.random() * visitReasons.length)],
          status: 'completed',

          // Contact information from patient record
          phone: randomPatient.phone,
          email: randomPatient.email,
          address: randomPatient.address,
          emergency_contact_name: randomPatient.emergency_contact_name,
          emergency_contact_phone: randomPatient.emergency_contact_phone,
          emergency_contact_relationship: randomPatient.emergency_contact_relationship,
          lying_bp_systolic: systolic,
          lying_bp_diastolic: diastolic,
          standing_bp_systolic: systolic + Math.floor(Math.random() * 10) - 5,
          standing_bp_diastolic: diastolic + Math.floor(Math.random() * 10) - 5,
          lying_heart_rate: lyingHeartRate,
          standing_heart_rate: standingHeartRate,
          heart_rate: heartRate,
          temperature: temperature.toFixed(1),
          respiratory_rate: 12 + Math.floor(Math.random() * 8), // 12-20
          pulse_oximetry: 95 + Math.floor(Math.random() * 5), // 95-100
          blood_glucose: bloodGlucose,
          cholesterol_total: cholesterolTotal,
          ldl_cholesterol: 70 + Math.floor(Math.random() * 60), // 70-130 mg/dL
          hdl_cholesterol: 40 + Math.floor(Math.random() * 20), // 40-60 mg/dL
          bilirubin_total: (0.1 + Math.random() * 1.1).toFixed(1), // 0.1-1.2 mg/dL
          magnesium: (1.7 + Math.random() * 0.5).toFixed(1), // 1.7-2.2 mg/dL
          albumin: (3.5 + Math.random() * 1.5).toFixed(1), // 3.5-5.0 g/dL
          parathyroid_hormone: 10 + Math.floor(Math.random() * 55), // 10-65 pg/mL
          alkaline_phosphatase_bone: 50 + Math.floor(Math.random() * 100), // 50-150 U/L
          free_t4: (0.8 + Math.random() * 1.0).toFixed(1), // 0.8-1.8 ng/dL
          free_t3: (2.3 + Math.random() * 1.9).toFixed(1), // 2.3-4.2 pg/mL
          hemoglobin: (12.0 + Math.random() * 5.0).toFixed(1), // 12.0-17.0 g/dL
          ferritin: 30 + Math.floor(Math.random() * 370), // 30-400 ng/mL
          vitamin_b12: 200 + Math.floor(Math.random() * 700), // 200-900 pg/mL
          folate: (2.7 + Math.random() * 14.3).toFixed(1), // 2.7-17.0 ng/mL
          weight: weight.toFixed(1),
          height: height.toFixed(1),
          bmi: bmi.toFixed(1),
          activity_level: activityLevels[Math.floor(Math.random() * activityLevels.length)],
          nutritional_status: nutritionalStatuses[Math.floor(Math.random() * nutritionalStatuses.length)],
          hydration_status: hydrationStatuses[Math.floor(Math.random() * hydrationStatuses.length)],
          notes: 'Sample generated visit data',
          created_by: adminId
        };

        // Use the same dynamic column approach as in the generate-visit-for-patient endpoint
        // First, check which columns exist in the patient_visits table
        const columnsResult = await pool.query(`
          SELECT column_name
          FROM information_schema.columns
          WHERE table_name = 'patient_visits'
          AND table_schema = 'public'
        `);

        const existingColumns = columnsResult.rows.map(row => row.column_name);

        // Build dynamic query based on existing columns
        let columns = [];
        let values = [];
        let placeholders = [];
        let paramIndex = 1;

        // Define field mappings for columns that might have different names
        const fieldMappings = {
          // Add mappings for fields that might have different names in the database
          'lying_heart_rate': ['lying_heart_rate', 'resting_heart_rate', 'heart_rate_lying'],
          'standing_heart_rate': ['standing_heart_rate', 'heart_rate_standing'],
          'ldl': ['ldl', 'ldl_cholesterol'],
          'hdl': ['hdl', 'hdl_cholesterol'],
          'bilirubin_total': ['bilirubin_total', 'total_bilirubin'],
          'magnesium': ['magnesium', 'mg'],
          'albumin': ['albumin', 'alb'],
          'vitamin_b12': ['vitamin_b12', 'b12'],
          'folate': ['folate', 'folic_acid'],
          'ferritin': ['ferritin', 'fer'],
          'parathyroid_hormone': ['parathyroid_hormone', 'pth'],
          't3_total': ['t3_total', 't3', 'total_t3'],
          'free_t3': ['free_t3', 't3_free'],
          't4_total': ['t4_total', 't4', 'total_t4'],
          'free_t4': ['free_t4', 't4_free'],
          'calf_circumference': ['calf_circumference'],
          'alp': ['alp', 'alkaline_phosphatase', 'alkaline_phosphatase_bone'],
          'hemoglobin': ['hemoglobin', 'hgb', 'hb']
        };

        // Add all fields from visitData if they exist in the database
        Object.entries(visitData).forEach(([key, value]) => {
          // Skip null or undefined values
          if (value === null || value === undefined) {
            return;
          }

          // First try the direct field name
          if (existingColumns.includes(key)) {
            columns.push(key);
            values.push(value);
            placeholders.push(`$${paramIndex++}`);
          }
          // If the field has mappings, try those alternative names
          else if (fieldMappings[key]) {
            const alternativeNames = fieldMappings[key];
            for (const altName of alternativeNames) {
              if (existingColumns.includes(altName)) {
                columns.push(altName);
                values.push(value);
                placeholders.push(`$${paramIndex++}`);
                console.log(`Using alternative column name ${altName} for ${key}`);
                break;
              }
            }
          }
        });

        // Build and execute the query
        const query = `
          INSERT INTO patient_visits (${columns.join(', ')})
          VALUES (${placeholders.join(', ')})
          RETURNING *
        `;

        console.log(`Inserting visit with ${columns.length} fields`);

        // Insert the visit
        const insertResult = await pool.query(query, values);

        generatedRecords.push(insertResult.rows[0]);
      }
    } else {
      return res.status(400).json({
        msg: `Sample data generation for table '${table}' is not supported yet.`,
        supportedTables: ['patient_visits']
      });
    }

    res.json({
      success: true,
      msg: `Generated ${generatedRecords.length} sample records for ${table}`,
      generatedCount: generatedRecords.length,
      sampleRecords: generatedRecords.slice(0, 2) // Return just a couple of samples to avoid large response
    });
  } catch (err) {
    console.error(`Error generating sample data for ${req.params.table}:`, err.message);
    res.status(500).json({
      error: 'Error generating sample data',
      message: err.message
    });
  }
});

/**
 * @route   GET api/debug/download-schema/:table
 * @desc    Get schema details for a specific table in a format suitable for download
 * @access  Private/Admin
 */
router.get('/download-schema/:table', [auth, adminCheck], async (req, res) => {
  try {
    const { table } = req.params;
    console.log(`Debug download schema endpoint called for table ${table} by admin:`, req.user.id);

    // Validate table name to prevent SQL injection
    const tableNameRegex = /^[a-zA-Z0-9_]+$/;
    if (!tableNameRegex.test(table)) {
      return res.status(400).json({ msg: 'Invalid table name' });
    }

    // Get column information
    const columnsResult = await pool.query(`
      SELECT
        column_name,
        data_type,
        character_maximum_length,
        column_default,
        is_nullable,
        udt_name
      FROM
        information_schema.columns
      WHERE
        table_schema = 'public'
        AND table_name = $1
      ORDER BY
        ordinal_position
    `, [table]);

    // Get primary key information
    const pkResult = await pool.query(`
      SELECT
        kcu.column_name
      FROM
        information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
      WHERE
        tc.constraint_type = 'PRIMARY KEY'
        AND tc.table_schema = 'public'
        AND tc.table_name = $1
    `, [table]);

    // Get foreign key information
    const fkResult = await pool.query(`
      SELECT
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
      FROM
        information_schema.table_constraints tc
        JOIN information_schema.key_column_usage kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
      WHERE
        tc.constraint_type = 'FOREIGN KEY'
        AND tc.table_schema = 'public'
        AND tc.table_name = $1
    `, [table]);

    // Format the data for download
    const primaryKeys = pkResult.rows.map(row => row.column_name);
    const columns = columnsResult.rows.map(column => {
      const isPrimaryKey = primaryKeys.includes(column.column_name);
      const foreignKey = fkResult.rows.find(fk => fk.column_name === column.column_name);

      return {
        column_name: column.column_name,
        data_type: column.data_type,
        character_maximum_length: column.character_maximum_length,
        is_nullable: column.is_nullable,
        column_default: column.column_default,
        is_primary_key: isPrimaryKey,
        foreign_key: foreignKey ? {
          references_table: foreignKey.foreign_table_name,
          references_column: foreignKey.foreign_column_name
        } : null
      };
    });

    res.json({
      table_name: table,
      columns: columns
    });
  } catch (err) {
    console.error(`Error fetching download schema for table ${req.params.table}:`, err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   GET api/debug/download-all-schema
 * @desc    Get schema details for all tables in a format suitable for download
 * @access  Private/Admin
 */
router.get('/download-all-schema', [auth, adminCheck], async (req, res) => {
  try {
    console.log('Debug download all schema endpoint called by admin:', req.user.id);

    // Get all tables
    const tablesResult = await pool.query(`
      SELECT
        table_name
      FROM
        information_schema.tables
      WHERE
        table_schema = 'public'
        AND table_type = 'BASE TABLE'
      ORDER BY
        table_name
    `);

    const tables = tablesResult.rows.map(row => row.table_name);
    const schemaData = {};

    // For each table, get its schema information
    for (const table of tables) {
      // Get column information
      const columnsResult = await pool.query(`
        SELECT
          column_name,
          data_type,
          character_maximum_length,
          column_default,
          is_nullable
        FROM
          information_schema.columns
        WHERE
          table_schema = 'public'
          AND table_name = $1
        ORDER BY
          ordinal_position
      `, [table]);

      // Get primary key information
      const pkResult = await pool.query(`
        SELECT
          kcu.column_name
        FROM
          information_schema.table_constraints tc
          JOIN information_schema.key_column_usage kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
        WHERE
          tc.constraint_type = 'PRIMARY KEY'
          AND tc.table_schema = 'public'
          AND tc.table_name = $1
      `, [table]);

      // Get foreign key information
      const fkResult = await pool.query(`
        SELECT
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name
        FROM
          information_schema.table_constraints tc
          JOIN information_schema.key_column_usage kcu
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
          JOIN information_schema.constraint_column_usage ccu
            ON ccu.constraint_name = tc.constraint_name
            AND ccu.table_schema = tc.table_schema
        WHERE
          tc.constraint_type = 'FOREIGN KEY'
          AND tc.table_schema = 'public'
          AND tc.table_name = $1
      `, [table]);

      // Format the data for download
      const primaryKeys = pkResult.rows.map(row => row.column_name);
      const columns = columnsResult.rows.map(column => {
        const isPrimaryKey = primaryKeys.includes(column.column_name);
        const foreignKey = fkResult.rows.find(fk => fk.column_name === column.column_name);

        return {
          column_name: column.column_name,
          data_type: column.data_type,
          character_maximum_length: column.character_maximum_length,
          is_nullable: column.is_nullable,
          column_default: column.column_default,
          is_primary_key: isPrimaryKey,
          foreign_key: foreignKey ? {
            references_table: foreignKey.foreign_table_name,
            references_column: foreignKey.foreign_column_name
          } : null
        };
      });

      schemaData[table] = {
        table_name: table,
        columns: columns
      };
    }

    res.json(schemaData);
  } catch (err) {
    console.error('Error fetching all schema data for download:', err.message);
    res.status(500).send('Server Error');
  }
});

/**
 * @route   POST api/debug/delete-table-data
 * @desc    Delete all data from a table
 * @access  Private/Admin
 */
router.post('/delete-table-data', [auth, adminCheck], async (req, res) => {
  try {
    const { table } = req.body;
    console.log(`Debug delete-table-data endpoint called by admin ${req.user.id} for table ${table}`);

    // Validate inputs
    const tableNameRegex = /^[a-zA-Z0-9_]+$/;

    if (!tableNameRegex.test(table)) {
      return res.status(400).json({ msg: 'Invalid table name' });
    }

    // Check if table exists
    const tableCheck = await pool.query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
        AND table_name = $1
    `, [table]);

    if (tableCheck.rows.length === 0) {
      return res.status(400).json({ msg: 'Table does not exist' });
    }

    // Get the count of rows before deletion
    const countQuery = await pool.query(`SELECT COUNT(*) FROM "${table}"`);
    const rowCount = parseInt(countQuery.rows[0].count);

    // Delete all data from the table
    await pool.query(`DELETE FROM "${table}"`);

    // Log the successful operation
    console.log(`Deleted all data (${rowCount} rows) from table ${table}`);

    res.json({
      success: true,
      msg: `Deleted all data (${rowCount} rows) from table ${table}`,
      rowCount
    });
  } catch (err) {
    console.error('Error deleting table data:', err.message);
    res.status(500).json({
      error: 'Error deleting table data',
      message: err.message
    });
  }
});

module.exports = router;
