const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const User = require('../models/User');

// @route   GET api/users
// @desc    Get all users (admin only)
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ msg: 'Not authorized to access user management' });
    }
    
    const users = await User.getAllUsers();
    res.json(users);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   PUT api/users/:id/lock
// @desc    Lock a user account (admin only)
// @access  Private
router.put('/:id/lock', auth, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ msg: 'Not authorized to lock user accounts' });
    }
    
    // Prevent admins from locking their own account
    if (req.user.id === parseInt(req.params.id)) {
      return res.status(400).json({ msg: 'Cannot lock your own account' });
    }
    
    const user = await User.lockAccount(req.params.id);
    res.json({ msg: 'User account locked successfully', user });
  } catch (err) {
    console.error(err.message);
    if (err.message === 'User not found') {
      return res.status(404).json({ msg: 'User not found' });
    }
    res.status(500).send('Server error');
  }
});

// @route   PUT api/users/:id/unlock
// @desc    Unlock a user account (admin only)
// @access  Private
router.put('/:id/unlock', auth, async (req, res) => {
  try {
    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ msg: 'Not authorized to unlock user accounts' });
    }
    
    const user = await User.unlockAccount(req.params.id);
    res.json({ msg: 'User account unlocked successfully', user });
  } catch (err) {
    console.error(err.message);
    if (err.message === 'User not found') {
      return res.status(404).json({ msg: 'User not found' });
    }
    res.status(500).send('Server error');
  }
});

module.exports = router; 