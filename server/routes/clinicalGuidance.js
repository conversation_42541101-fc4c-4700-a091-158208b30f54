const express = require('express');
const router = express.Router();
const { check, validationResult } = require('express-validator');
const auth = require('../middleware/auth');
const adminCheck = require('../middleware/adminCheck');
const ClinicalGuidance = require('../models/ClinicalGuidance');
const GuidanceCategory = require('../models/GuidanceCategory');

// @route   GET api/clinical-guidance
// @desc    Get all clinical guidance entries
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    const { 
      search, 
      categoryId, 
      contextKey, 
      tags,
      publishedOnly = 'true'
    } = req.query;

    // Parse tags if provided
    const parsedTags = tags ? tags.split(',') : [];

    // Convert publishedOnly to boolean
    const publishedOnlyBool = publishedOnly === 'true';

    const options = {
      search,
      categoryId: categoryId ? parseInt(categoryId) : undefined,
      contextKey,
      tags: parsedTags,
      publishedOnly: publishedOnlyBool,
      role: req.user.role
    };

    const guidance = await ClinicalGuidance.getAll(options);
    res.json(guidance);
  } catch (err) {
    console.error('Error in GET /api/clinical-guidance:', err.message);
    res.status(500).send('Server Error');
  }
});

// @route   GET api/clinical-guidance/:id
// @desc    Get a clinical guidance entry by ID
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const guidance = await ClinicalGuidance.getById(req.params.id, req.user.role);
    
    if (!guidance) {
      return res.status(404).json({ msg: 'Guidance not found' });
    }
    
    res.json(guidance);
  } catch (err) {
    console.error(`Error in GET /api/clinical-guidance/${req.params.id}:`, err.message);
    res.status(500).send('Server Error');
  }
});

// @route   POST api/clinical-guidance
// @desc    Create a new clinical guidance entry
// @access  Private/Admin
router.post('/', [
  auth,
  adminCheck,
  [
    check('title', 'Title is required').not().isEmpty(),
    check('content', 'Content is required').not().isEmpty()
  ]
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const {
      title,
      content,
      summary,
      categoryId,
      contextKey,
      isPublished,
      tags
    } = req.body;

    const guidanceData = {
      title,
      content,
      summary,
      categoryId,
      contextKey,
      isPublished,
      tags
    };

    const guidance = await ClinicalGuidance.create(guidanceData, req.user.id);
    res.json(guidance);
  } catch (err) {
    console.error('Error in POST /api/clinical-guidance:', err.message);
    res.status(500).send('Server Error');
  }
});

// @route   PUT api/clinical-guidance/:id
// @desc    Update a clinical guidance entry
// @access  Private/Admin
router.put('/:id', [
  auth,
  adminCheck,
  [
    check('title', 'Title is required').optional().not().isEmpty(),
    check('content', 'Content is required').optional().not().isEmpty()
  ]
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const {
      title,
      content,
      summary,
      categoryId,
      contextKey,
      isPublished,
      tags
    } = req.body;

    const guidanceData = {
      title,
      content,
      summary,
      categoryId,
      contextKey,
      isPublished,
      tags
    };

    const guidance = await ClinicalGuidance.update(req.params.id, guidanceData, req.user.id);
    res.json(guidance);
  } catch (err) {
    console.error(`Error in PUT /api/clinical-guidance/${req.params.id}:`, err.message);
    
    if (err.message.includes('not found')) {
      return res.status(404).json({ msg: 'Guidance not found' });
    }
    
    res.status(500).send('Server Error');
  }
});

// @route   DELETE api/clinical-guidance/:id
// @desc    Delete a clinical guidance entry
// @access  Private/Admin
router.delete('/:id', [auth, adminCheck], async (req, res) => {
  try {
    const success = await ClinicalGuidance.delete(req.params.id, req.user.id);
    
    if (!success) {
      return res.status(404).json({ msg: 'Guidance not found' });
    }
    
    res.json({ msg: 'Guidance removed' });
  } catch (err) {
    console.error(`Error in DELETE /api/clinical-guidance/${req.params.id}:`, err.message);
    res.status(500).send('Server Error');
  }
});

// @route   GET api/clinical-guidance/:id/versions
// @desc    Get version history for a guidance entry
// @access  Private/Admin
router.get('/:id/versions', [auth, adminCheck], async (req, res) => {
  try {
    const versions = await ClinicalGuidance.getVersionHistory(req.params.id);
    res.json(versions);
  } catch (err) {
    console.error(`Error in GET /api/clinical-guidance/${req.params.id}/versions:`, err.message);
    res.status(500).send('Server Error');
  }
});

// @route   GET api/clinical-guidance/:id/audit
// @desc    Get audit trail for a guidance entry
// @access  Private/Admin
router.get('/:id/audit', [auth, adminCheck], async (req, res) => {
  try {
    const audit = await ClinicalGuidance.getAuditTrail(req.params.id);
    res.json(audit);
  } catch (err) {
    console.error(`Error in GET /api/clinical-guidance/${req.params.id}/audit:`, err.message);
    res.status(500).send('Server Error');
  }
});

// @route   GET api/clinical-guidance/:id/access
// @desc    Get access control for a guidance entry
// @access  Private/Admin
router.get('/:id/access', [auth, adminCheck], async (req, res) => {
  try {
    const access = await ClinicalGuidance.getAccess(req.params.id);
    res.json(access);
  } catch (err) {
    console.error(`Error in GET /api/clinical-guidance/${req.params.id}/access:`, err.message);
    res.status(500).send('Server Error');
  }
});

// @route   PUT api/clinical-guidance/:id/access
// @desc    Update access control for a guidance entry
// @access  Private/Admin
router.put('/:id/access', [
  auth,
  adminCheck,
  [
    check('accessList', 'Access list is required').isArray()
  ]
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const { accessList } = req.body;
    const success = await ClinicalGuidance.updateAccess(req.params.id, accessList, req.user.id);
    
    if (!success) {
      return res.status(404).json({ msg: 'Guidance not found' });
    }
    
    res.json({ msg: 'Access updated' });
  } catch (err) {
    console.error(`Error in PUT /api/clinical-guidance/${req.params.id}/access:`, err.message);
    res.status(500).send('Server Error');
  }
});

// @route   GET api/clinical-guidance/context/:key
// @desc    Get guidance by context key
// @access  Private
router.get('/context/:key', auth, async (req, res) => {
  try {
    const options = {
      contextKey: req.params.key,
      publishedOnly: true,
      role: req.user.role
    };

    const guidance = await ClinicalGuidance.getAll(options);
    res.json(guidance);
  } catch (err) {
    console.error(`Error in GET /api/clinical-guidance/context/${req.params.key}:`, err.message);
    res.status(500).send('Server Error');
  }
});

// @route   GET api/clinical-guidance/categories
// @desc    Get all guidance categories
// @access  Private
router.get('/categories/all', auth, async (req, res) => {
  try {
    const { hierarchy } = req.query;
    const includeHierarchy = hierarchy === 'true';
    
    const categories = await GuidanceCategory.getAll(includeHierarchy);
    res.json(categories);
  } catch (err) {
    console.error('Error in GET /api/clinical-guidance/categories:', err.message);
    res.status(500).send('Server Error');
  }
});

// @route   GET api/clinical-guidance/categories/:id
// @desc    Get a guidance category by ID
// @access  Private
router.get('/categories/:id', auth, async (req, res) => {
  try {
    const category = await GuidanceCategory.getById(req.params.id);
    
    if (!category) {
      return res.status(404).json({ msg: 'Category not found' });
    }
    
    res.json(category);
  } catch (err) {
    console.error(`Error in GET /api/clinical-guidance/categories/${req.params.id}:`, err.message);
    res.status(500).send('Server Error');
  }
});

// @route   POST api/clinical-guidance/categories
// @desc    Create a new guidance category
// @access  Private/Admin
router.post('/categories', [
  auth,
  adminCheck,
  [
    check('name', 'Name is required').not().isEmpty()
  ]
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const { name, description, parentId } = req.body;
    
    const categoryData = {
      name,
      description,
      parentId
    };
    
    const category = await GuidanceCategory.create(categoryData);
    res.json(category);
  } catch (err) {
    console.error('Error in POST /api/clinical-guidance/categories:', err.message);
    res.status(500).send('Server Error');
  }
});

// @route   PUT api/clinical-guidance/categories/:id
// @desc    Update a guidance category
// @access  Private/Admin
router.put('/categories/:id', [
  auth,
  adminCheck,
  [
    check('name', 'Name is required').not().isEmpty()
  ]
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const { name, description, parentId } = req.body;
    
    const categoryData = {
      name,
      description,
      parentId
    };
    
    const category = await GuidanceCategory.update(req.params.id, categoryData);
    res.json(category);
  } catch (err) {
    console.error(`Error in PUT /api/clinical-guidance/categories/${req.params.id}:`, err.message);
    
    if (err.message.includes('Circular reference')) {
      return res.status(400).json({ msg: err.message });
    }
    
    if (err.message.includes('not found')) {
      return res.status(404).json({ msg: 'Category not found' });
    }
    
    res.status(500).send('Server Error');
  }
});

// @route   DELETE api/clinical-guidance/categories/:id
// @desc    Delete a guidance category
// @access  Private/Admin
router.delete('/categories/:id', [auth, adminCheck], async (req, res) => {
  try {
    const success = await GuidanceCategory.delete(req.params.id);
    
    if (!success) {
      return res.status(404).json({ msg: 'Category not found' });
    }
    
    res.json({ msg: 'Category removed' });
  } catch (err) {
    console.error(`Error in DELETE /api/clinical-guidance/categories/${req.params.id}:`, err.message);
    res.status(500).send('Server Error');
  }
});

// @route   GET api/clinical-guidance/tags
// @desc    Get all guidance tags
// @access  Private
router.get('/tags/all', auth, async (req, res) => {
  try {
    const tags = await GuidanceCategory.getAllTags();
    res.json(tags);
  } catch (err) {
    console.error('Error in GET /api/clinical-guidance/tags:', err.message);
    res.status(500).send('Server Error');
  }
});

module.exports = router;
