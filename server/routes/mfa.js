const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const adminCheck = require('../middleware/adminCheck');
const MfaSettings = require('../models/MfaSettings');
const UserMfa = require('../models/UserMfa');

// @route   GET api/mfa/settings
// @desc    Get all MFA settings
// @access  Private/Admin
router.get('/settings', [auth, adminCheck], async (req, res) => {
  try {
    const settings = await MfaSettings.getAll();
    res.json(settings);
  } catch (err) {
    console.error('Error fetching MFA settings:', err.message);
    res.status(500).json({ message: 'Server Error', error: err.message });
  }
});

// @route   PUT api/mfa/settings
// @desc    Update MFA settings
// @access  Private/Admin
router.put('/settings', [auth, adminCheck], async (req, res) => {
  try {
    const updatedSettings = await MfaSettings.update(req.body);
    res.json(updatedSettings);
  } catch (err) {
    console.error('Error updating MFA settings:', err.message);
    res.status(500).json({ message: 'Server Error', error: err.message });
  }
});

// @route   GET api/mfa/users
// @desc    Get all users with MFA status
// @access  Private/Admin
router.get('/users', [auth, adminCheck], async (req, res) => {
  try {
    const users = await UserMfa.getAllUsersWithMfaStatus();
    res.json(users);
  } catch (err) {
    console.error('Error fetching users with MFA status:', err.message);
    res.status(500).json({ message: 'Server Error', error: err.message });
  }
});

// @route   GET api/mfa/user/:userId
// @desc    Get MFA status for a specific user
// @access  Private/Admin
router.get('/user/:userId', [auth, adminCheck], async (req, res) => {
  try {
    const userId = req.params.userId;
    const mfaStatus = await UserMfa.getForUser(userId);
    res.json(mfaStatus);
  } catch (err) {
    console.error(`Error fetching MFA status for user ${req.params.userId}:`, err.message);
    res.status(500).json({ message: 'Server Error', error: err.message });
  }
});

// @route   PUT api/mfa/user/:userId/enable
// @desc    Enable MFA for a user
// @access  Private/Admin
router.put('/user/:userId/enable', [auth, adminCheck], async (req, res) => {
  try {
    const userId = req.params.userId;
    const { method } = req.body;
    
    if (!method || !['email', 'app'].includes(method)) {
      return res.status(400).json({ message: 'Invalid MFA method' });
    }
    
    const result = await UserMfa.enable(userId, method);
    res.json(result);
  } catch (err) {
    console.error(`Error enabling MFA for user ${req.params.userId}:`, err.message);
    res.status(500).json({ message: 'Server Error', error: err.message });
  }
});

// @route   PUT api/mfa/user/:userId/disable
// @desc    Disable MFA for a user
// @access  Private/Admin
router.put('/user/:userId/disable', [auth, adminCheck], async (req, res) => {
  try {
    const userId = req.params.userId;
    const result = await UserMfa.disable(userId);
    res.json(result);
  } catch (err) {
    console.error(`Error disabling MFA for user ${req.params.userId}:`, err.message);
    res.status(500).json({ message: 'Server Error', error: err.message });
  }
});

// @route   POST api/mfa/verify
// @desc    Verify MFA code
// @access  Private
router.post('/verify', auth, async (req, res) => {
  try {
    const { code, method } = req.body;
    const userId = req.user.id;
    
    if (!code) {
      return res.status(400).json({ message: 'MFA code is required' });
    }
    
    const isValid = await UserMfa.verify(userId, code, method);
    
    if (!isValid) {
      return res.status(401).json({ message: 'Invalid MFA code' });
    }
    
    res.json({ message: 'MFA verification successful' });
  } catch (err) {
    console.error(`Error verifying MFA for user ${req.user.id}:`, err.message);
    res.status(500).json({ message: 'Server Error', error: err.message });
  }
});

// @route   GET api/mfa/status
// @desc    Get MFA status for the current user
// @access  Private
router.get('/status', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const mfaStatus = await UserMfa.getForUser(userId);
    res.json(mfaStatus);
  } catch (err) {
    console.error(`Error fetching MFA status for user ${req.user.id}:`, err.message);
    res.status(500).json({ message: 'Server Error', error: err.message });
  }
});

module.exports = router;
