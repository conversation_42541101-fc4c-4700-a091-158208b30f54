const express = require('express');
const router = express.Router();
const Visit = require('../models/Visit');
const auth = require('../middleware/auth');
const pool = require('../db');

/**
 * @route   POST /api/visits
 * @desc    Create a new patient visit
 * @access  Private (Staff, Doctors, Admin)
 */
router.post('/', auth, async (req, res) => {
  try {
    // Add the user ID who created this visit
    req.body.created_by = req.user.id;

    const visit = await Visit.create(req.body);
    res.status(201).json(visit);
  } catch (error) {
    console.error('Error creating visit:', error.message);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route   GET /api/visits/patient/:patientId
 * @desc    Get all visits for a specific patient
 * @access  Private (Staff, Doctors, Admin)
 */
router.get('/patient/:patientId', auth, async (req, res) => {
  try {
    const patientId = req.params.patientId;
    const visits = await Visit.getByPatientId(patientId);
    res.json(visits);
  } catch (error) {
    console.error('Error getting patient visits:', error.message);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route   GET /api/visits/doctor/:doctorId
 * @desc    Get all visits for a specific doctor
 * @access  Private (Staff, Doctors, Admin)
 */
router.get('/doctor/:doctorId', auth, async (req, res) => {
  try {
    const doctorId = req.params.doctorId;
    const visits = await Visit.getByDoctorId(doctorId);
    res.json(visits);
  } catch (error) {
    console.error('Error getting doctor visits:', error.message);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route   GET /api/visits/recent
 * @desc    Get recent visits
 * @access  Private (Staff, Doctors, Admin)
 */
router.get('/recent', auth, async (req, res) => {
  try {
    const limit = req.query.limit ? parseInt(req.query.limit) : 10;
    const visits = await Visit.getRecent(limit);
    res.json(visits);
  } catch (error) {
    console.error('Error getting recent visits:', error.message);
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route   GET /api/visits/:id
 * @desc    Get a single visit by ID
 * @access  Private (Staff, Doctors, Admin)
 */
router.get('/:id', auth, async (req, res) => {
  try {
    const visitId = req.params.id;
    const visit = await Visit.getById(visitId);
    res.json(visit);
  } catch (error) {
    console.error('Error getting visit:', error.message);
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route   PUT /api/visits/:id
 * @desc    Update a visit
 * @access  Private (Staff, Doctors, Admin)
 */
router.put('/:id', auth, async (req, res) => {
  try {
    console.log('Updating visit with ID:', req.params.id);
    console.log('Update data:', JSON.stringify(req.body, null, 2));

    // Log specific categories of fields for debugging
    console.log('Activity & Nutrition fields:', {
      activity_level: req.body.activity_level,
      nutritional_status: req.body.nutritional_status,
      hydration_status: req.body.hydration_status
    });

    console.log('Social & Environment fields:', {
      social_interaction_levels: req.body.social_interaction_levels,
      living_conditions: req.body.living_conditions,
      age_friendly_environment: req.body.age_friendly_environment,
      type_age_friendly: typeof req.body.age_friendly_environment,
      social_support_network: req.body.social_support_network,
      transportation_access: req.body.transportation_access,
      financial_concerns: req.body.financial_concerns
    });

    console.log('Safety & Emergency fields:', {
      fall_risk_assessment: req.body.fall_risk_assessment,
      home_safety_evaluation: req.body.home_safety_evaluation,
      assistive_devices_used: req.body.assistive_devices_used
    });

    console.log('Vaccination Status fields:', {
      influenza: req.body.influenza_vaccination_date,
      pneumococcal: req.body.pneumococcal_vaccination_date,
      zoster: req.body.zoster_vaccination_date,
      tdap: req.body.tdap_vaccination_date,
      covid19: req.body.covid19_vaccination_date,
      covid19_booster: req.body.covid19_booster_date
    });

    console.log('Treatment fields:', {
      referrals: req.body.referrals,
      medication_changes: req.body.medication_changes,
      referrals_type: typeof req.body.referrals,
      medication_changes_type: typeof req.body.medication_changes
    });

    const visitId = req.params.id;
    const visitData = req.body;
    const userId = req.user.id;

    const updatedVisit = await Visit.update(visitId, visitData, userId);
    console.log('Visit updated successfully:', updatedVisit.visit_id, 'by user:', userId);

    res.json(updatedVisit);
  } catch (error) {
    // Log detailed error for debugging
    console.error('Error updating visit:', error);
    console.error('Error stack:', error.stack);
    console.error('Visit data that caused the error:', req.body);

    if (error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }
    res.status(500).json({ error: 'Error saving visit. Please try again. ' + error.message });
  }
});

/**
 * @route   DELETE /api/visits/:id
 * @desc    Delete a visit
 * @access  Private (Staff, Doctors, Admin)
 */
router.delete('/:id', auth, async (req, res) => {
  try {
    const visitId = req.params.id;
    const userId = req.user.id;

    // Pass the user ID for audit logging
    const result = await Visit.delete(visitId, userId);
    console.log(`Visit ${visitId} deleted by user ID: ${userId}`);

    res.json({
      message: 'Visit deleted successfully',
      visitId: result.visitId,
      patientName: result.patientName
    });
  } catch (error) {
    console.error('Error deleting visit:', error.message);
    if (error.message.includes('not found')) {
      return res.status(404).json({ error: error.message });
    }
    res.status(500).json({ error: error.message });
  }
});

/**
 * @route   GET /api/visits/health-metrics/:patientId
 * @desc    Get combined health metrics data from both patients and patient_visits tables
 * @access  Private (Staff, Doctors, Admin, Patient)
 */
router.get('/health-metrics/:patientId', auth, async (req, res) => {
  try {
    const patientId = req.params.patientId;

    // Security check: Ensure user is either the patient themselves, a kin related to the patient, or has appropriate role
    if (
      req.user.patient_id !== parseInt(patientId) &&
      !['admin', 'doctor', 'nurse', 'kin'].includes(req.user.role)
    ) {
      return res.status(403).json({ msg: 'Not authorized to access this patient data' });
    }

    // First, get patient data (baseline metrics)
    const patientQuery = `
      SELECT
        patient_id,
        created_at,
        'baseline' as visit_type,
        created_at as visit_date,

        -- Vital Signs
        lying_bp_systolic, lying_bp_diastolic,
        sitting_bp_systolic, sitting_bp_diastolic,
        standing_bp_systolic, standing_bp_diastolic,
        lying_heart_rate, standing_heart_rate,
        heart_rhythm,
        temperature,
        respiratory_rate,
        pulse_oximetry,
        weight, height, bmi,

        -- Blood Glucose
        blood_glucose,
        hba1c,

        -- Cholesterol
        cholesterol_total,
        hdl_cholesterol,
        ldl_cholesterol,
        triglycerides,

        -- Kidney Function
        creatinine,
        blood_urea_nitrogen,
        egfr,

        -- Liver Function
        alt, ast, alp,
        bilirubin_t,
        bilirubin_d,
        albumin,

        -- Complete Blood Count
        wbc,
        rbc,
        hemoglobin,
        hematocrit,
        platelets,

        -- Thyroid Function
        tsh, t3, t4,

        -- Inflammatory Markers
        crp, esr,

        -- Electrolytes
        sodium, potassium, calcium, magnesium,

        -- Vitamins & Iron
        vitamin_d, vitamin_b12, folate, ferritin, iron
      FROM patients
      WHERE patient_id = $1
    `;

    // Then, get all visits data
    const visitsQuery = `
      SELECT
        patient_id,
        visit_id,
        created_at,
        'visit' as visit_type,
        visit_date,

        -- Vital Signs
        lying_bp_systolic, lying_bp_diastolic,
        sitting_bp_systolic, sitting_bp_diastolic,
        standing_bp_systolic, standing_bp_diastolic,
        lying_heart_rate, standing_heart_rate,
        heart_rhythm,
        temperature,
        respiratory_rate,
        pulse_oximetry,
        weight, height, bmi,

        -- Blood Glucose
        blood_glucose,
        hba1c,

        -- Cholesterol
        cholesterol_total,
        hdl_cholesterol,
        ldl_cholesterol,
        triglycerides,

        -- Kidney Function
        creatinine,
        blood_urea_nitrogen,
        egfr,

        -- Liver Function
        alt, ast, alp,
        bilirubin_t,
        bilirubin_d,
        albumin,

        -- Complete Blood Count
        wbc,
        rbc,
        hemoglobin,
        hematocrit,
        platelets,

        -- Thyroid Function
        tsh, t3, t4,

        -- Inflammatory Markers
        crp, esr,

        -- Electrolytes
        sodium, potassium, calcium, magnesium,

        -- Vitamins & Iron
        vitamin_d, vitamin_b12, folate, ferritin, iron
      FROM patient_visits
      WHERE patient_id = $1
      ORDER BY visit_date ASC
    `;

    // Execute both queries in parallel
    const [patientResult, visitsResult] = await Promise.all([
      pool.query(patientQuery, [patientId]),
      pool.query(visitsQuery, [patientId])
    ]);

    // Process patient data (baseline)
    let baselineData = null;
    if (patientResult.rows.length > 0) {
      baselineData = patientResult.rows[0];

      // Check if baseline data has any health metrics
      const hasHealthData = Object.entries(baselineData).some(([key, value]) => {
        // Only check health metric fields, not metadata fields
        const metadataFields = ['patient_id', 'created_at', 'visit_type', 'visit_date'];
        return !metadataFields.includes(key) && value !== null && value !== undefined;
      });

      if (!hasHealthData) {
        baselineData = null;
      }
    }

    // Process visits data
    const visitsData = visitsResult.rows;

    // Combine data (if baseline data exists and has health metrics)
    const combinedData = baselineData
      ? [baselineData, ...visitsData]
      : visitsData;

    // Sort by date (just to be sure)
    combinedData.sort((a, b) => new Date(a.visit_date) - new Date(b.visit_date));

    res.json({
      success: true,
      data: combinedData,
      baselineIncluded: !!baselineData
    });
  } catch (error) {
    console.error('Error fetching combined health metrics:', error);
    res.status(500).json({
      error: 'Error fetching health metrics data',
      message: error.message
    });
  }
});

module.exports = router;