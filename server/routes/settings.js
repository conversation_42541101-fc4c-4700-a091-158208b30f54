const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const SystemSettings = require('../models/SystemSettings');

// @route   GET api/settings
// @desc    Get system settings (public fields only)
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    console.log('Settings endpoint called by user:', req.user.id);

    // Get settings from database
    const settings = await SystemSettings.getCurrent();

    // Return only the public fields that all users need
    const publicSettings = {
      system_name: settings.system_name,
      timezone: settings.timezone,
      date_format: settings.date_format,
      language: settings.language,
      currency: settings.currency
    };

    res.json(publicSettings);
  } catch (err) {
    console.error('Error fetching system settings:', err.message);
    res.status(500).send('Server Error');
  }
});

module.exports = router;
