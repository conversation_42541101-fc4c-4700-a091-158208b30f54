const express = require('express');
const router = express.Router();
const db = require('../db');
const auth = require('../middleware/auth');
const { check, validationResult } = require('express-validator');

// @route    GET api/appointments
// @desc     Get all appointments
// @access   Private
router.get('/', auth, async (req, res) => {
  try {
    console.log('Fetching appointments for', req.user.role === 'doctor' ? `doctor: ${req.user.doctor_id}` : 'all users');

    // If the user is a doctor, only show their appointments
    if (req.user.role === 'doctor' && req.user.doctor_id) {
      const query = `
      SELECT
        v.visit_id as appointment_id,
        p.patient_id,
        CONCAT(p.first_name, ' ', p.last_name) as patient_name,
        v.visit_date::text as date,
        v.visit_date::time::text as start_time,
        v.end_time::text as end_time,
        v.visit_reason as reason,
        v.status
      FROM patient_visits v
      JOIN patients p ON v.patient_id = p.patient_id
      WHERE v.doctor_id = $1
        AND v.visit_date >= CURRENT_DATE
        AND v.visit_date <= CURRENT_DATE + INTERVAL '7 days'
      ORDER BY v.visit_date ASC
    `;
      console.log('Executing query: \n', query);
      console.log('With doctor_id:', req.user.doctor_id);

      const result = await db.query(query, [req.user.doctor_id]);
      console.log('Found appointments:', result.rows);

      return res.json(result.rows);
    }

    // For assistants and other staff, show all appointments
    const query = `
      SELECT
        v.visit_id as appointment_id,
        p.patient_id,
        CONCAT(p.first_name, ' ', p.last_name) as patient_name,
        v.visit_date::text as date,
        v.visit_date::time::text as start_time,
        v.end_time::text as end_time,
        v.visit_reason as reason,
        v.status,
        d.doctor_id,
        CONCAT(d.first_name, ' ', d.last_name) as doctor_name
      FROM patient_visits v
      JOIN patients p ON v.patient_id = p.patient_id
      JOIN doctors d ON v.doctor_id = d.doctor_id
      WHERE v.visit_date >= CURRENT_DATE
        AND v.visit_date <= CURRENT_DATE + INTERVAL '7 days'
      ORDER BY v.visit_date ASC
    `;
    console.log('Executing query for all appointments: \n', query);

    const result = await db.query(query);
    console.log('Found appointments:', result.rows.length);

    res.json(result.rows);
  } catch (err) {
    console.error('Error in GET /appointments:', err);
    res.status(500).send('Server Error');
  }
});

// @route    GET api/appointments/:id
// @desc     Get appointment by ID
// @access   Private
router.get('/:id', auth, async (req, res) => {
  try {
    const result = await db.query(
      `SELECT
        v.visit_id as appointment_id,
        p.patient_id,
        CONCAT(p.first_name, ' ', p.last_name) as patient_name,
        v.visit_date::text as date,
        v.visit_date::time::text as start_time,
        v.end_time::text as end_time,
        v.visit_reason as reason,
        v.status,
        v.notes,
        d.doctor_id,
        CONCAT(d.first_name, ' ', d.last_name) as doctor_name
      FROM patient_visits v
      JOIN patients p ON v.patient_id = p.patient_id
      JOIN doctors d ON v.doctor_id = d.doctor_id
      WHERE v.visit_id = $1`,
      [req.params.id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ msg: 'Appointment not found' });
    }

    res.json(result.rows[0]);
  } catch (err) {
    console.error('Error in GET /appointments/:id:', err);
    res.status(500).send('Server Error');
  }
});

// @route    POST api/appointments
// @desc     Create a new appointment
// @access   Private
router.post('/', [
  auth,
  [
    check('patient_id', 'Patient is required').not().isEmpty(),
    check('doctor_id', 'Doctor is required').not().isEmpty(),
    check('date', 'Date is required').not().isEmpty(),
    // Make reason optional if title is provided
    check('reason', 'Reason or title is required').custom((value, { req }) => {
      return value || req.body.title;
    }),
    // Make sure time or start_time is provided
    check('time', 'Time is required').custom((value, { req }) => {
      return value || req.body.start_time;
    })
  ]
], async (req, res) => {
  console.log("POST /api/appointments - Request received");
  console.log("Request body:", req.body);
  console.log("User making request:", req.user);
  console.log("Request headers:", req.headers);

  // Validate request
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    console.log("Validation errors:", errors.array());
    return res.status(400).json({ errors: errors.array() });
  }

  // Extract data from request body
  const {
    patient_id,
    doctor_id,
    date,
    // Accept either time or start_time
    time = req.body.start_time || '',
    // Get end_time
    end_time = req.body.end_time || '',
    // Accept either reason or title
    reason = req.body.title || '',
    status = 'scheduled',
    notes = '',
    type = 'checkup'
  } = req.body;

  // Make sure patient_id and doctor_id are numbers
  const patientId = parseInt(patient_id);
  const doctorId = parseInt(doctor_id);

  console.log('Creating appointment with parsed data:', {
    patientId,
    doctorId,
    date,
    time,
    end_time,
    reason,
    status,
    notes,
    type
  });

  try {
    // Verify patient exists
    const patientCheck = await db.query(
      'SELECT * FROM patients WHERE patient_id = $1',
      [patientId]
    );

    if (patientCheck.rows.length === 0) {
      console.log(`Patient with ID ${patientId} not found`);
      return res.status(400).json({ msg: 'Patient not found' });
    }

    // Verify doctor exists
    const doctorCheck = await db.query(
      'SELECT * FROM doctors WHERE doctor_id = $1',
      [doctorId]
    );

    if (doctorCheck.rows.length === 0) {
      console.log(`Doctor with ID ${doctorId} not found`);
      return res.status(400).json({ msg: 'Doctor not found' });
    }

    // Check if the doctor is available at this time
    // We'll use the availability check logic below instead of creating visitDateTime here

    // For time-specific appointments, check for overlaps
    let availabilityQuery;
    let availabilityParams;

    if (time && time.trim() !== '') {
      // If time is provided, check for overlapping appointments
      availabilityQuery = `
        SELECT * FROM patient_visits
        WHERE doctor_id = $1
        AND visit_date::date = $2::date
        AND status = 'scheduled'
        AND (
          -- Check if the new appointment time overlaps with existing appointments
          -- Assuming each appointment is 1 hour long
          (visit_date::time <= $3::time AND (visit_date::time + INTERVAL '1 hour') > $3::time)
        )
      `;
      availabilityParams = [doctorId, date, time];
    } else {
      // If only date is provided, just check for any appointments on that day
      availabilityQuery = `
        SELECT * FROM patient_visits
        WHERE doctor_id = $1
        AND visit_date::date = $2::date
        AND status = 'scheduled'
      `;
      availabilityParams = [doctorId, date];
    }

    console.log('Checking availability with query:', availabilityQuery);
    console.log('Availability params:', availabilityParams);

    const availability = await db.query(availabilityQuery, availabilityParams);

    if (availability.rows.length > 0) {
      console.log(`Time slot ${date} ${time} already booked for doctor ${doctorId}`);
      return res.status(400).json({
        msg: 'This time slot is already booked for this doctor'
      });
    }

    console.log('Inserting new appointment into database...');

    // Insert the appointment
    // Combine date and time into a proper timestamp if time is provided
    const visitDateTime = time && time.trim() !== '' && date.length === 10
      ? `${date} ${time}`
      : date;

    console.log('Creating appointment with datetime:', visitDateTime);

    // Process end time
    let endTimeValue = null;
    if (end_time && end_time.trim() !== '') {
      endTimeValue = end_time;
    } else if (time && time.trim() !== '') {
      // Default to 1 hour after start time if not provided
      const startTimeParts = time.split(':').map(Number);
      let endHour = startTimeParts[0] + 1;
      if (endHour > 23) endHour = 23;
      endTimeValue = `${endHour.toString().padStart(2, '0')}:${startTimeParts[1].toString().padStart(2, '0')}:00`;
    }

    console.log('Using end time:', endTimeValue);

    const result = await db.query(
      `INSERT INTO patient_visits (
        patient_id,
        doctor_id,
        visit_date,
        visit_reason,
        status,
        notes,
        end_time,
        created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())
      RETURNING visit_id`,
      [
        patientId,
        doctorId,
        visitDateTime,
        reason,
        status,
        notes,
        endTimeValue
      ]
    );

    const visit_id = result.rows[0].visit_id;
    console.log(`New appointment created with ID: ${visit_id}`);

    // Get the full appointment data
    const appointmentResult = await db.query(
      `SELECT
        v.visit_id as appointment_id,
        p.patient_id,
        CONCAT(p.first_name, ' ', p.last_name) as patient_name,
        v.visit_date::text as date,
        v.visit_date::time::text as start_time,
        v.end_time::text as end_time,
        v.visit_reason as reason,
        v.status,
        v.notes,
        d.doctor_id,
        CONCAT(d.first_name, ' ', d.last_name) as doctor_name,
        d.specialty as doctor_specialty
      FROM patient_visits v
      JOIN patients p ON v.patient_id = p.patient_id
      JOIN doctors d ON v.doctor_id = d.doctor_id
      WHERE v.visit_id = $1`,
      [visit_id]
    );

    console.log('Appointment created successfully, returning data:', appointmentResult.rows[0]);

    res.json(appointmentResult.rows[0]);
  } catch (err) {
    console.error('Error in POST /appointments:', err);
    res.status(500).json({ msg: 'Server Error', error: err.message });
  }
});

// @route    PUT api/appointments/:id
// @desc     Update an appointment
// @access   Private
router.put('/:id', auth, async (req, res) => {
  try {
    const {
      patient_id,
      doctor_id,
      date,
      time,
      end_time,
      reason = req.body.title || '',
      status = 'scheduled',
      notes = ''
    } = req.body;

    console.log(`Updating appointment ${req.params.id} with data:`, {
      patient_id,
      doctor_id,
      date,
      time,
      end_time,
      reason,
      status,
      notes
    });

    // Check if the appointment exists
    const appointmentCheck = await db.query(
      'SELECT * FROM patient_visits WHERE visit_id = $1',
      [req.params.id]
    );

    if (appointmentCheck.rows.length === 0) {
      return res.status(404).json({ msg: 'Appointment not found' });
    }

    // Check if updating the time slot would conflict with another appointment
    if (date) {
      // For time-specific appointments, check for overlaps
      let availabilityQuery;
      let availabilityParams;

      if (time && time.trim() !== '') {
        // If time is provided, check for overlapping appointments
        availabilityQuery = `
          SELECT * FROM patient_visits
          WHERE doctor_id = $1
          AND visit_date::date = $2::date
          AND visit_id != $3
          AND status = 'scheduled'
          AND (
            -- Check if the new appointment time overlaps with existing appointments
            -- Assuming each appointment is 1 hour long
            (visit_date::time <= $4::time AND (visit_date::time + INTERVAL '1 hour') > $4::time)
          )
        `;
        availabilityParams = [doctor_id, date, req.params.id, time];
      } else {
        // If only date is provided, just check for any appointments on that day
        availabilityQuery = `
          SELECT * FROM patient_visits
          WHERE doctor_id = $1
          AND visit_date::date = $2::date
          AND visit_id != $3
          AND status = 'scheduled'
        `;
        availabilityParams = [doctor_id, date, req.params.id];
      }

      console.log('Checking update availability with query:', availabilityQuery);
      console.log('Update availability params:', availabilityParams);

      const availability = await db.query(availabilityQuery, availabilityParams);

      if (availability.rows.length > 0) {
        return res.status(400).json({
          msg: 'This time slot is already booked for this doctor'
        });
      }
    }

    // Combine date and time into a proper timestamp if time is provided
    const visitDateTime = time && time.trim() !== '' && date.length === 10
      ? `${date} ${time}`
      : date;

    console.log('Updating appointment with datetime:', visitDateTime);

    // Process end time
    let endTimeValue = null;
    if (end_time && end_time.trim() !== '') {
      endTimeValue = end_time;
    } else if (time && time.trim() !== '') {
      // Default to 1 hour after start time if not provided
      const startTimeParts = time.split(':').map(Number);
      let endHour = startTimeParts[0] + 1;
      if (endHour > 23) endHour = 23;
      endTimeValue = `${endHour.toString().padStart(2, '0')}:${startTimeParts[1].toString().padStart(2, '0')}:00`;
    }

    console.log('Using end time for update:', endTimeValue);

    // Update the appointment
    await db.query(
      `UPDATE patient_visits
       SET patient_id = $1,
           doctor_id = $2,
           visit_date = $3,
           visit_reason = $4,
           status = $5,
           notes = $6,
           end_time = $7
       WHERE visit_id = $8`,
      [
        patient_id,
        doctor_id,
        visitDateTime,
        reason,
        status,
        notes,
        endTimeValue,
        req.params.id
      ]
    );

    // Get the updated appointment data
    const updatedAppointment = await db.query(
      `SELECT
        v.visit_id as appointment_id,
        p.patient_id,
        CONCAT(p.first_name, ' ', p.last_name) as patient_name,
        v.visit_date::text as date,
        v.visit_date::time::text as start_time,
        v.end_time::text as end_time,
        v.visit_reason as reason,
        v.status,
        v.notes,
        d.doctor_id,
        CONCAT(d.first_name, ' ', d.last_name) as doctor_name
      FROM patient_visits v
      JOIN patients p ON v.patient_id = p.patient_id
      JOIN doctors d ON v.doctor_id = d.doctor_id
      WHERE v.visit_id = $1`,
      [req.params.id]
    );

    res.json(updatedAppointment.rows[0]);
  } catch (err) {
    console.error('Error in PUT /appointments/:id:', err);
    res.status(500).send('Server Error');
  }
});

// @route    DELETE api/appointments/:id
// @desc     Delete an appointment
// @access   Private
router.delete('/:id', auth, async (req, res) => {
  try {
    // Check if appointment exists
    const appointment = await db.query(
      'SELECT * FROM patient_visits WHERE visit_id = $1',
      [req.params.id]
    );

    if (appointment.rows.length === 0) {
      return res.status(404).json({ msg: 'Appointment not found' });
    }

    // Delete the appointment
    await db.query(
      'DELETE FROM patient_visits WHERE visit_id = $1',
      [req.params.id]
    );

    res.json({ msg: 'Appointment removed' });
  } catch (err) {
    console.error('Error in DELETE /appointments/:id:', err);
    res.status(500).send('Server Error');
  }
});

module.exports = router;