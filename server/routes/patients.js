const express = require('express');
const router = express.Router();
const Patient = require('../models/Patient');
const auth = require('../middleware/auth');
const logPatientAccess = require('../middleware/logPatientAccess');
const pool = require('../db');
const { check, validationResult } = require('express-validator');
const { validationSchemas, validate } = require('../utils/validation');

// @route   POST api/patients
// @desc    Create a new patient
// @access  Private
router.post('/', [auth, validate(validationSchemas.patient.create)], async (req, res) => {
  try {
    // Add doctor_id from request body or use user_id as default if user is a doctor
    const patientData = {
      ...req.body,
      doctor_id: req.body.doctor_id || (req.user.role === 'doctor' ? req.user.id : null)
    };

    const patient = await Pat<PERSON>.create(patientData);

    // Log the creation
    // This would typically be handled in the Patient.create method

    // Check if a user account was created
    if (patient.user) {
      console.log(`Patient created with user account: ${patient.user.username}`);

      // Return patient with user information
      res.status(201).json({
        patient,
        user: {
          username: patient.user.username,
          default_password: patient.user.default_password
        },
        message: 'Patient created successfully with user account'
      });
    } else {
      // If user creation failed, return the error
      res.status(201).json({
        patient,
        userError: patient.userError || 'Failed to create user account',
        message: 'Patient created successfully but user account creation failed'
      });
    }
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   GET api/patients
// @desc    Get all patients
// @access  Private
router.get('/', auth, async (req, res) => {
  try {
    // Check if search query exists
    if (req.query.search) {
      const patients = await Patient.search(req.query.search);
      return res.json(patients);
    }

    const patients = await Patient.getAll();
    res.json(patients);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   GET api/patients/:id
// @desc    Get patient by ID
// @access  Private
router.get('/:id', [auth, logPatientAccess('view'), validate(validationSchemas.idParam)], async (req, res) => {
  try {
    const patient = await Patient.getById(req.params.id);

    if (!patient) {
      return res.status(404).json({ msg: 'Patient not found' });
    }

    res.json(patient);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   PUT api/patients/:id
// @desc    Update patient
// @access  Private
router.put('/:id', [auth, logPatientAccess('edit'), validate([...validationSchemas.idParam, ...validationSchemas.patient.update])], async (req, res) => {
  try {
    console.log(`Received request to update patient ID: ${req.params.id}`);
    console.log(`Request body contains ${Object.keys(req.body).length} fields`);

    // Check if patient exists
    let patient = await Patient.getById(req.params.id);
    if (!patient) {
      console.log(`Patient with ID ${req.params.id} not found`);
      return res.status(404).json({ msg: 'Patient not found' });
    }

    // Log a few key fields for debugging
    console.log(`Updating patient: ${patient.first_name} ${patient.last_name} (ID: ${patient.patient_id})`);
    console.log(`User making the change: ${req.user.username} (ID: ${req.user.id})`);

    // Update patient with userId for tracking who made the change
    try {
      // Log the request body for debugging
      console.log('Request body sample:');
      const sampleKeys = Object.keys(req.body).slice(0, 10);
      sampleKeys.forEach(key => {
        console.log(`- ${key}: ${req.body[key]} (type: ${typeof req.body[key]})`);
      });

      // Check specifically for treatment plan fields
      console.log('Treatment Plan Fields:');
      console.log(`- treatment_plan: ${req.body.treatment_plan} (type: ${typeof req.body.treatment_plan})`);
      console.log(`- follow_up_instructions: ${req.body.follow_up_instructions} (type: ${typeof req.body.follow_up_instructions})`);
      console.log(`- referrals: ${req.body.referrals} (type: ${typeof req.body.referrals})`);

      // Check for empty strings in numeric fields
      const numericFields = [
        'lying_bp_systolic', 'lying_bp_diastolic', 'sitting_bp_systolic', 'sitting_bp_diastolic',
        'standing_bp_systolic', 'standing_bp_diastolic', 'heart_rate', 'respiratory_rate',
        'lying_heart_rate', 'sitting_heart_rate', 'standing_heart_rate',
        'blood_glucose', 'hba1c', 'cholesterol_total', 'hdl_cholesterol', 'ldl_cholesterol',
        'triglycerides', 'sodium', 'potassium', 'calcium', 'magnesium', 'creatinine',
        'weight', 'height', 'bmi', 'doctor_id'
      ];

      numericFields.forEach(field => {
        if (field in req.body && req.body[field] === '') {
          console.log(`Warning: Empty string in numeric field ${field}`);
        }
      });

      patient = await Patient.update(req.params.id, req.body, req.user.id);
      console.log(`Patient update successful for ID: ${req.params.id}`);
      res.json(patient);
    } catch (updateErr) {
      console.error('Error in Patient.update():', updateErr);

      // Add more detailed error information
      let errorDetails = updateErr.message;
      if (updateErr.code) {
        errorDetails += ` (Code: ${updateErr.code})`;
      }
      if (updateErr.column) {
        errorDetails += ` - Column: ${updateErr.column}`;
      }

      return res.status(500).json({
        msg: 'Failed to update patient data',
        error: errorDetails
      });
    }
  } catch (err) {
    console.error('Error in patient update route:', err);
    if (err.message.includes('Patient with ID')) {
      return res.status(404).json({ msg: 'Patient not found' });
    }
    res.status(500).json({
      msg: 'Server error while updating patient',
      error: err.message
    });
  }
});

// @route   DELETE api/patients/:id
// @desc    Delete patient
// @access  Private
router.delete('/:id', [auth, logPatientAccess('delete'), validate(validationSchemas.idParam)], async (req, res) => {
  try {
    // Check if patient exists
    const patient = await Patient.getById(req.params.id);
    if (!patient) {
      return res.status(404).json({ msg: 'Patient not found' });
    }

    // Delete patient and pass the user ID for audit logging
    const result = await Patient.delete(req.params.id, req.user.id);
    console.log(`Patient ${result.patientName} (ID: ${result.patientId}) deleted by user ID: ${req.user.id}`);

    res.json({ msg: 'Patient removed', patientName: result.patientName });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   GET api/patients/dashboard/:id
// @desc    Get patient dashboard data including patient info, recent visits, medications, and latest vitals
// @access  Private (patient or authorized staff)
router.get('/dashboard/:id', [auth, validate(validationSchemas.idParam)], async (req, res) => {
  try {
    const patientId = req.params.id;

    // Security check: Ensure user is either the patient themselves, a kin related to the patient, or has appropriate role
    if (
      req.user.patient_id !== parseInt(patientId) &&
      !['admin', 'doctor', 'nurse', 'kin'].includes(req.user.role)
    ) {
      return res.status(403).json({ msg: 'Not authorized to access this patient data' });
    }

    console.log(`Fetching dashboard data for patient ID: ${patientId}`);

    // Get patient basic information with adjusted columns that match the actual schema
    const patientInfoQuery = `
      SELECT p.patient_id, p.first_name, p.last_name, p.unique_id,
             p.date_of_birth, p.gender, p.email, p.phone,
             p.medication_allergies as allergies, p.doctor_id,
             CONCAT(d.first_name, ' ', d.last_name) as doctor_name,
             p.blood_type,
             p.emergency_contact_name, p.emergency_contact_phone, p.emergency_contact_relationship,
             p.emergency_contact_updated
      FROM patients p
      LEFT JOIN doctors d ON p.doctor_id = d.doctor_id
      WHERE p.patient_id = $1
    `;
    const patientResult = await pool.query(patientInfoQuery, [patientId]);

    if (patientResult.rows.length === 0) {
      return res.status(404).json({ msg: 'Patient not found' });
    }

    const patientInfo = patientResult.rows[0];

    // Get recent visits with doctor information (last 5)
    // Using patient_visits instead of visits table
    const visitsQuery = `
      SELECT
        v.visit_id,
        v.visit_date,
        v.visit_reason,
        v.diagnosis,
        jsonb_build_object(
          'blood_pressure', CONCAT(v.lying_bp_systolic, '/', v.lying_bp_diastolic),
          'heart_rate', v.lying_heart_rate,
          'temperature', COALESCE(v.temperature, v.body_temperature),
          'oxygen_level', COALESCE(v.pulse_oximetry, v.oxygen_saturation),
          'weight', v.weight,
          'height', v.height,
          'bmi', v.bmi,
          'lying_bp_systolic', v.lying_bp_systolic,
          'lying_bp_diastolic', v.lying_bp_diastolic,
          'sitting_bp_systolic', v.sitting_bp_systolic,
          'sitting_bp_diastolic', v.sitting_bp_diastolic,
          'standing_bp_systolic', v.standing_bp_systolic,
          'standing_bp_diastolic', v.standing_bp_diastolic,
          'lying_heart_rate', v.lying_heart_rate,
          'sitting_heart_rate', v.sitting_heart_rate,
          'standing_heart_rate', v.standing_heart_rate
        ) as vital_signs,
        v.notes,
        v.doctor_id,
        d.first_name as doctor_first_name,
        d.last_name as doctor_last_name
      FROM patient_visits v
      LEFT JOIN doctors d ON v.doctor_id = d.doctor_id
      WHERE v.patient_id = $1
      ORDER BY v.visit_date DESC
      LIMIT 5
    `;

    let visitsResult;
    try {
      visitsResult = await pool.query(visitsQuery, [patientId]);
    } catch (err) {
      console.error('Error fetching patient visits:', err);
      // Fallback to empty result if query fails
      visitsResult = { rows: [] };
    }

    // Get current medications from prescriptions table
    const medicationsQuery = `
      SELECT
        p.medication as medication_name,
        p.dosage,
        COALESCE(p.frequency, 'As directed') as instructions,
        COALESCE(p.created_at, now()) as prescribed_date
      FROM prescriptions p
      WHERE p.patient_id = $1
      ORDER BY COALESCE(p.created_at, now()) DESC
    `;

    let medicationsResult;
    try {
      medicationsResult = await pool.query(medicationsQuery, [patientId]);
    } catch (err) {
      console.error('Error fetching medications:', err);
      // If medications table doesn't exist or other error, return empty array
      medicationsResult = { rows: [] };

      // Try alternative query using current_medications text field
      try {
        const altMedQuery = `
          SELECT
            unnest(string_to_array(current_medications, ',')) as medication_name,
            'As prescribed' as dosage,
            'Take as directed' as instructions,
            now() as prescribed_date
          FROM patients
          WHERE patient_id = $1 AND current_medications IS NOT NULL AND current_medications <> ''
        `;

        const altResult = await pool.query(altMedQuery, [patientId]);
        if (altResult.rows.length > 0) {
          medicationsResult = altResult;
        }
      } catch (altErr) {
        console.error('Error with alternative medications query:', altErr);
      }
    }

    // Get latest vital signs from the most recent visit
    let latestVitals = null;

    // If we have visits, use the vital signs from the most recent visit
    if (visitsResult.rows.length > 0) {
      const mostRecentVisit = visitsResult.rows[0];
      const vs = mostRecentVisit.vital_signs;

      latestVitals = {
        blood_pressure: vs.blood_pressure || 'Not recorded',
        heart_rate: vs.heart_rate || null,
        temperature: vs.temperature || null,
        oxygen_level: vs.oxygen_level || null,
        weight: vs.weight || null,
        height: vs.height || null,
        lying_bp_systolic: vs.lying_bp_systolic || null,
        lying_bp_diastolic: vs.lying_bp_diastolic || null,
        sitting_bp_systolic: vs.sitting_bp_systolic || null,
        sitting_bp_diastolic: vs.sitting_bp_diastolic || null,
        standing_bp_systolic: vs.standing_bp_systolic || null,
        standing_bp_diastolic: vs.standing_bp_diastolic || null,
        lying_heart_rate: vs.lying_heart_rate || null,
        sitting_heart_rate: vs.sitting_heart_rate || null,
        standing_heart_rate: vs.standing_heart_rate || null
      };

      // Calculate BMI if weight and height are available but BMI is not
      if (vs.weight && vs.height && !vs.bmi) {
        // BMI formula: weight (kg) / height (m)^2
        // Convert weight from lbs to kg: weight * 0.453592
        // Convert height from inches to meters: height * 0.0254
        const weightInKg = vs.weight * 0.453592;
        const heightInM = vs.height * 0.0254;
        latestVitals.bmi = parseFloat((weightInKg / (heightInM * heightInM)).toFixed(1));
      } else {
        latestVitals.bmi = vs.bmi || null;
      }

      console.log('Latest vitals from most recent visit:', latestVitals);
    }

    // If no visits or no vital signs in visits, fall back to patient record
    if (!latestVitals) {
      const patient = await pool.query(
        `SELECT
          sitting_bp_systolic, sitting_bp_diastolic,
          lying_bp_systolic, lying_bp_diastolic,
          standing_bp_systolic, standing_bp_diastolic,
          lying_heart_rate, sitting_heart_rate, standing_heart_rate,
          COALESCE(temperature, body_temperature) as temperature,
          COALESCE(pulse_oximetry, oxygen_saturation) as pulse_oximetry,
          weight, height, bmi
        FROM patients
        WHERE patient_id = $1`,
        [patientId]
      );

      if (patient.rows.length > 0) {
        const p = patient.rows[0];

        // Prefer lying BP if available, otherwise use sitting BP
        const systolic = p.lying_bp_systolic || p.sitting_bp_systolic;
        const diastolic = p.lying_bp_diastolic || p.sitting_bp_diastolic;

        latestVitals = {
          blood_pressure: systolic && diastolic ?
            `${systolic}/${diastolic}` : 'Not recorded',
          heart_rate: p.lying_heart_rate || p.sitting_heart_rate || null,
          temperature: p.temperature || null,
          oxygen_level: p.pulse_oximetry || null,
          weight: p.weight || null,
          height: p.height || null,
          lying_bp_systolic: p.lying_bp_systolic || null,
          lying_bp_diastolic: p.lying_bp_diastolic || null,
          sitting_bp_systolic: p.sitting_bp_systolic || null,
          sitting_bp_diastolic: p.sitting_bp_diastolic || null,
          standing_bp_systolic: p.standing_bp_systolic || null,
          standing_bp_diastolic: p.standing_bp_diastolic || null,
          lying_heart_rate: p.lying_heart_rate || null,
          sitting_heart_rate: p.sitting_heart_rate || null,
          standing_heart_rate: p.standing_heart_rate || null
        };

        // Calculate BMI if weight and height are available but BMI is not
        if (p.weight && p.height && !p.bmi) {
          // BMI formula: weight (kg) / height (m)^2
          // Convert weight from lbs to kg: weight * 0.453592
          // Convert height from inches to meters: height * 0.0254
          const weightInKg = p.weight * 0.453592;
          const heightInM = p.height * 0.0254;
          latestVitals.bmi = parseFloat((weightInKg / (heightInM * heightInM)).toFixed(1));
        } else {
          latestVitals.bmi = p.bmi || null;
        }

        console.log('Latest vitals from patient record:', latestVitals);
      }
    }

    console.log('Successfully fetched dashboard data');

    // Determine data source for the dashboard
    const dataSource = visitsResult.rows.length > 0 ? 'visit' : 'patient';
    console.log(`Data source for dashboard: ${dataSource}`);

    // Return complete dashboard data with source indicator
    res.json({
      patientInfo,
      visits: visitsResult.rows,
      medications: medicationsResult.rows,
      latestVitals,
      dataSource
    });

  } catch (err) {
    console.error('Error fetching patient dashboard data:', err);

    // Log detailed error information for debugging
    if (err.code) {
      console.error('Error code:', err.code);
    }
    if (err.column) {
      console.error('Error column:', err.column);
    }
    if (err.detail) {
      console.error('Error detail:', err.detail);
    }
    if (err.table) {
      console.error('Error table:', err.table);
    }
    if (err.constraint) {
      console.error('Error constraint:', err.constraint);
    }

    // Return a more informative error message to the client
    res.status(500).json({
      error: 'Server error',
      message: 'Failed to fetch dashboard data. Please try again later.',
      details: process.env.NODE_ENV === 'development' ? err.message : undefined
    });
  }
});

// @route   GET api/patients/my-dashboard
// @desc    Get logged in patient's dashboard data
// @access  Private (patient only)
router.get('/my-dashboard', auth, async (req, res) => {
  try {
    // Only allow patients to access this endpoint
    if (req.user.role !== 'patient' || !req.user.patient_id) {
      return res.status(403).json({ msg: 'This endpoint is only for patient users' });
    }

    // Redirect to the patient dashboard endpoint
    res.redirect(`/api/patients/dashboard/${req.user.patient_id}`);
  } catch (err) {
    console.error('Error redirecting to patient dashboard:', err.message);
    res.status(500).send('Server error');
  }
});

module.exports = router;