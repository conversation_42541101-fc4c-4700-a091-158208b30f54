const express = require('express');
const router = express.Router();
const BeersCriteria = require('../models/BeersCriteria');
const Patient = require('../models/Patient');
const Visit = require('../models/Visit');
const pool = require('../db');
const auth = require('../middleware/auth');
const { check, validationResult } = require('express-validator');
const logger = require('../utils/logger');

/**
 * @route   GET api/beers-criteria
 * @desc    Get all BEERS criteria
 * @access  Private
 */
router.get('/', auth, async (req, res) => {
  try {
    const criteria = await BeersCriteria.getAll();
    res.json(criteria);
  } catch (err) {
    logger.error('Error in GET /api/beers-criteria:', err);
    res.status(500).send('Server error');
  }
});

/**
 * @route   GET api/beers-criteria/:id
 * @desc    Get BEERS criteria by ID
 * @access  Private
 */
router.get('/:id', auth, async (req, res) => {
  try {
    const criteria = await BeersCriteria.getById(req.params.id);

    if (!criteria) {
      return res.status(404).json({ msg: 'BEERS criteria not found' });
    }

    res.json(criteria);
  } catch (err) {
    logger.error(`Error in GET /api/beers-criteria/${req.params.id}:`, err);
    res.status(500).send('Server error');
  }
});

/**
 * @route   GET api/beers-criteria/medication/:name
 * @desc    Get BEERS criteria by medication name
 * @access  Private
 */
router.get('/medication/:name', auth, async (req, res) => {
  try {
    const criteria = await BeersCriteria.getByMedicationName(req.params.name);
    res.json(criteria);
  } catch (err) {
    logger.error(`Error in GET /api/beers-criteria/medication/${req.params.name}:`, err);
    res.status(500).send('Server error');
  }
});

/**
 * @route   GET api/beers-criteria/category/:category
 * @desc    Get BEERS criteria by category
 * @access  Private
 */
router.get('/category/:category', auth, async (req, res) => {
  try {
    const criteria = await BeersCriteria.getByCategory(req.params.category);
    res.json(criteria);
  } catch (err) {
    logger.error(`Error in GET /api/beers-criteria/category/${req.params.category}:`, err);
    res.status(500).send('Server error');
  }
});

/**
 * @route   GET api/beers-criteria/condition/:condition
 * @desc    Get BEERS criteria by condition
 * @access  Private
 */
router.get('/condition/:condition', auth, async (req, res) => {
  try {
    const criteria = await BeersCriteria.getByCondition(req.params.condition);
    res.json(criteria);
  } catch (err) {
    logger.error(`Error in GET /api/beers-criteria/condition/${req.params.condition}:`, err);
    res.status(500).send('Server error');
  }
});

/**
 * @route   POST api/beers-criteria
 * @desc    Create a new BEERS criteria entry
 * @access  Private (Admin only)
 */
router.post(
  '/',
  [
    auth,
    check('medication_name', 'Medication name is required').not().isEmpty(),
    check('category', 'Category is required').not().isEmpty(),
    check('recommendation', 'Recommendation is required').not().isEmpty(),
    check('rationale', 'Rationale is required').not().isEmpty()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ msg: 'Not authorized to create BEERS criteria' });
    }

    try {
      const criteria = await BeersCriteria.create(req.body);
      res.status(201).json(criteria);
    } catch (err) {
      logger.error('Error in POST /api/beers-criteria:', err);
      res.status(500).send('Server error');
    }
  }
);

/**
 * @route   PUT api/beers-criteria/:id
 * @desc    Update a BEERS criteria entry
 * @access  Private (Admin only)
 */
router.put(
  '/:id',
  [
    auth,
    check('medication_name', 'Medication name is required').not().isEmpty(),
    check('category', 'Category is required').not().isEmpty(),
    check('recommendation', 'Recommendation is required').not().isEmpty(),
    check('rationale', 'Rationale is required').not().isEmpty()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    // Check if user is admin
    if (req.user.role !== 'admin') {
      return res.status(403).json({ msg: 'Not authorized to update BEERS criteria' });
    }

    try {
      const criteria = await BeersCriteria.update(req.params.id, req.body);

      if (!criteria) {
        return res.status(404).json({ msg: 'BEERS criteria not found' });
      }

      res.json(criteria);
    } catch (err) {
      logger.error(`Error in PUT /api/beers-criteria/${req.params.id}:`, err);
      res.status(500).send('Server error');
    }
  }
);

/**
 * @route   DELETE api/beers-criteria/:id
 * @desc    Delete a BEERS criteria entry
 * @access  Private (Admin only)
 */
router.delete('/:id', auth, async (req, res) => {
  // Check if user is admin
  if (req.user.role !== 'admin') {
    return res.status(403).json({ msg: 'Not authorized to delete BEERS criteria' });
  }

  try {
    const success = await BeersCriteria.delete(req.params.id);

    if (!success) {
      return res.status(404).json({ msg: 'BEERS criteria not found' });
    }

    res.json({ msg: 'BEERS criteria removed' });
  } catch (err) {
    logger.error(`Error in DELETE /api/beers-criteria/${req.params.id}:`, err);
    res.status(500).send('Server error');
  }
});

/**
 * @route   POST api/beers-criteria/check
 * @desc    Check medications against BEERS criteria
 * @access  Private
 */
router.post(
  '/check',
  [
    auth,
    check('medications', 'Medications are required').isArray(),
    check('patientId', 'Patient ID is required').isInt()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { medications, patientId, visitId } = req.body;

      // Get patient data
      const patient = await Patient.getById(patientId);

      if (!patient) {
        return res.status(404).json({ msg: 'Patient not found' });
      }

      // Calculate age from date of birth
      const dob = new Date(patient.date_of_birth);
      const today = new Date();
      let age = today.getFullYear() - dob.getFullYear();
      const monthDiff = today.getMonth() - dob.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
        age--;
      }

      // Get visit data if visitId is provided
      let visitData = {};
      if (visitId) {
        try {
          const visit = await Visit.getById(visitId);
          if (visit) {
            visitData = visit;
          }
        } catch (visitErr) {
          logger.error(`Error fetching visit data for BEERS criteria check: ${visitErr.message}`);
        }
      }

      // Get current medications from prescriptions
      let currentMedications = '';
      try {
        // Get medications directly from the prescriptions table
        const prescriptionsQuery = `
          SELECT medication
          FROM prescriptions
          WHERE patient_id = $1
        `;
        const prescriptionsResult = await pool.query(prescriptionsQuery, [patientId]);

        if (prescriptionsResult.rows.length > 0) {
          currentMedications = prescriptionsResult.rows.map(row => row.medication).join(', ');
          logger.debug(`Found ${prescriptionsResult.rows.length} medications from prescriptions table`);
        } else if (patient.current_medications) {
          currentMedications = patient.current_medications;
          logger.debug('Using current_medications from patient record');
        }
      } catch (medsErr) {
        logger.error(`Error fetching current medications for BEERS criteria check: ${medsErr.message}`);
        // Fall back to patient.current_medications if available
        if (patient.current_medications) {
          currentMedications = patient.current_medications;
          logger.debug('Falling back to current_medications from patient record after error');
        }
      }

      // Combine patient data, visit data, and additional data
      const patientData = {
        ...patient,
        ...visitData,
        age,
        current_medications: currentMedications
      };

      // Log the data being used for BEERS criteria check at debug level only
      logger.debug('Patient data for BEERS criteria check:', {
        age: patientData.age,
        medical_history: patientData.medical_history || 'Not available',
        diagnosis: patientData.diagnosis || 'Not available',
        current_medications: patientData.current_medications || 'Not available',
        egfr: patientData.egfr || 'Not available',
        creatinine: patientData.creatinine || 'Not available'
      });

      // Check medications against BEERS criteria
      const alerts = await BeersCriteria.checkMedications(medications, patientData);

      res.json(alerts);
    } catch (err) {
      logger.error('Error in POST /api/beers-criteria/check:', err);
      logger.error('Error stack:', err.stack);

      // Send a more detailed error message to help with debugging
      res.status(500).json({
        error: 'Server error processing BEERS criteria check',
        message: err.message,
        details: 'Check server logs for more information'
      });
    }
  }
);

/**
 * @route   POST api/beers-criteria/visit/:visitId/alerts
 * @desc    Save BEERS criteria alerts for a patient visit
 * @access  Private
 */
router.post(
  '/visit/:visitId/alerts',
  [
    auth,
    check('alerts', 'Alerts are required').isArray()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { visitId } = req.params;
      const { alerts } = req.body;

      // Check if visit exists
      const visit = await Visit.getById(visitId);

      if (!visit) {
        return res.status(404).json({ msg: 'Visit not found' });
      }

      // Save alerts
      const success = await BeersCriteria.saveAlerts(visitId, alerts);

      if (!success) {
        return res.status(500).json({ msg: 'Failed to save alerts' });
      }

      res.json({ msg: 'Alerts saved successfully' });
    } catch (err) {
      logger.error(`Error in POST /api/beers-criteria/visit/${req.params.visitId}/alerts:`, err);
      res.status(500).send('Server error');
    }
  }
);

/**
 * @route   POST api/beers-criteria/visit/:visitId/overrides
 * @desc    Save BEERS criteria overrides for a patient visit
 * @access  Private
 */
router.post(
  '/visit/:visitId/overrides',
  [
    auth,
    check('overrides', 'Overrides are required').isArray()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { visitId } = req.params;
      const { overrides } = req.body;

      // Check if visit exists
      const visit = await Visit.getById(visitId);

      if (!visit) {
        return res.status(404).json({ msg: 'Visit not found' });
      }

      // Save overrides
      const success = await BeersCriteria.saveOverrides(visitId, overrides);

      if (!success) {
        return res.status(500).json({ msg: 'Failed to save overrides' });
      }

      res.json({ msg: 'Overrides saved successfully' });
    } catch (err) {
      logger.error(`Error in POST /api/beers-criteria/visit/${req.params.visitId}/overrides:`, err);
      res.status(500).send('Server error');
    }
  }
);

/**
 * @route   GET api/beers-criteria/visit/:visitId/alerts
 * @desc    Get BEERS criteria alerts for a patient visit
 * @access  Private
 */
router.get('/visit/:visitId/alerts', auth, async (req, res) => {
  try {
    const { visitId } = req.params;

    // Check if visit exists
    const visit = await Visit.getById(visitId);

    if (!visit) {
      return res.status(404).json({ msg: 'Visit not found' });
    }

    // Get alerts
    const alerts = await BeersCriteria.getAlerts(visitId);

    res.json(alerts);
  } catch (err) {
    logger.error(`Error in GET /api/beers-criteria/visit/${req.params.visitId}/alerts:`, err);
    res.status(500).send('Server error');
  }
});

/**
 * @route   GET api/beers-criteria/visit/:visitId/overrides
 * @desc    Get BEERS criteria overrides for a patient visit
 * @access  Private
 */
router.get('/visit/:visitId/overrides', auth, async (req, res) => {
  try {
    const { visitId } = req.params;

    // Check if visit exists
    const visit = await Visit.getById(visitId);

    if (!visit) {
      return res.status(404).json({ msg: 'Visit not found' });
    }

    // Get overrides
    const overrides = await BeersCriteria.getOverrides(visitId);

    res.json(overrides);
  } catch (err) {
    logger.error(`Error in GET /api/beers-criteria/visit/${req.params.visitId}/overrides:`, err);
    res.status(500).send('Server error');
  }
});

module.exports = router;
