const express = require('express');
const router = express.Router();
const { check, validationResult } = require('express-validator');
const auth = require('../middleware/auth');
const Message = require('../models/Message');

// Try to import the securityLogger, but don't fail if it's not available
let securityLogger;
try {
  securityLogger = require('../utils/securityLogger');
} catch (err) {
  console.warn('Security logger not available in messages route, security events will not be logged');
  // Create a dummy logger that does nothing
  securityLogger = {
    logSecurityEvent: () => Promise.resolve(null)
  };
}

/**
 * @route   POST /api/messages
 * @desc    Send a new message
 * @access  Private
 */
router.post(
  '/',
  [
    auth,
    check('content', 'Message content is required').not().isEmpty(),
    check('recipient_id', 'Recipient ID is required').isNumeric(),
    check('patient_id', 'Patient ID is required').isNumeric()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    try {
      const { content, recipient_id, patient_id } = req.body;
      const sender_id = req.user.id;

      // Create the message
      const message = await Message.create({
        content,
        sender_id,
        recipient_id,
        patient_id
      });

      // Log the security event
      await securityLogger.logSecurityEvent({
        event_type: 'MESSAGE_SEND',
        user_id: sender_id,
        details: `Sent message to user ID: ${recipient_id} regarding patient ID: ${patient_id}`,
        ip_address: req.ip,
        resource_type: 'messages',
        resource_id: message.id
      });

      res.status(201).json(message);
    } catch (error) {
      console.error('Error sending message:', error);
      res.status(500).json({ msg: 'Server error' });
    }
  }
);

/**
 * @route   GET /api/messages
 * @desc    Get all messages for the authenticated user
 * @access  Private
 */
router.get('/', auth, async (req, res) => {
  try {
    const messages = await Message.getForUser(req.user.id);
    res.json(messages);
  } catch (error) {
    console.error('Error fetching messages:', error);
    res.status(500).json({ msg: 'Server error' });
  }
});

/**
 * @route   GET /api/messages/patient/:patientId
 * @desc    Get all messages for a specific patient
 * @access  Private
 */
router.get('/patient/:patientId', auth, async (req, res) => {
  try {
    const messages = await Message.getForPatient(req.params.patientId);
    res.json(messages);
  } catch (error) {
    console.error('Error fetching patient messages:', error);
    res.status(500).json({ msg: 'Server error' });
  }
});

/**
 * @route   PUT /api/messages/:id/read
 * @desc    Mark a message as read
 * @access  Private
 */
router.put('/:id/read', auth, async (req, res) => {
  try {
    const message = await Message.markAsRead(req.params.id);

    if (!message) {
      return res.status(404).json({ msg: 'Message not found' });
    }

    // Only the recipient can mark a message as read
    if (message.recipient_id !== req.user.id) {
      return res.status(403).json({ msg: 'Not authorized to mark this message as read' });
    }

    res.json(message);
  } catch (error) {
    console.error('Error marking message as read:', error);
    res.status(500).json({ msg: 'Server error' });
  }
});

/**
 * @route   DELETE /api/messages/:id
 * @desc    Delete a message
 * @access  Private
 */
router.delete('/:id', auth, async (req, res) => {
  try {
    const success = await Message.delete(req.params.id, req.user.id);

    if (!success) {
      return res.status(404).json({ msg: 'Message not found' });
    }

    res.json({ msg: 'Message deleted' });
  } catch (error) {
    console.error('Error deleting message:', error);
    res.status(500).json({ msg: 'Server error' });
  }
});

module.exports = router;
