const express = require('express');
const router = express.Router();
const pool = require('../db');
const auth = require('../middleware/auth');
const { check, validationResult } = require('express-validator');

// Helper function to check if security_logs table exists
const logSecurityEvent = async (userId, action, details, ipAddress) => {
  try {
    await pool.query(
      `INSERT INTO security_logs
       (user_id, action, details, ip_address)
       VALUES ($1, $2, $3, $4)`,
      [userId, action, details, ipAddress]
    );
  } catch (err) {
    console.error('Error logging security event:', err);
    // Continue execution even if logging fails
  }
};

// @route   GET api/kin/patients
// @desc    Get all patients related to the authenticated kin user
// @access  Private (kin only)
router.get('/patients', auth, async (req, res) => {
  try {
    // Check if the user is a kin
    if (req.user.role !== 'kin') {
      return res.status(403).json({ msg: 'Not authorized to access this resource' });
    }

    // Get all patients related to this kin user
    const result = await pool.query(`
      SELECT p.patient_id, p.first_name, p.last_name, p.date_of_birth, p.gender,
             kpr.relationship_type, kpr.is_primary, kpr.relationship_id
      FROM kin_patient_relationships kpr
      JOIN patients p ON kpr.patient_id = p.patient_id
      WHERE kpr.kin_user_id = $1
      ORDER BY kpr.is_primary DESC, p.last_name, p.first_name
    `, [req.user.id]);

    if (result.rows.length === 0) {
      return res.status(404).json({ msg: 'No related patients found for this kin user' });
    }

    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching kin-related patients:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

// @route   POST api/kin/patients
// @desc    Add a new patient relationship for the authenticated kin user
// @access  Private (admin only)
router.post(
  '/patients',
  [
    auth,
    check('patient_id', 'Patient ID is required').not().isEmpty(),
    check('relationship_type', 'Relationship type is required').not().isEmpty(),
    check('is_primary', 'Primary relationship flag is required').isBoolean()
  ],
  async (req, res) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ errors: errors.array() });
    }

    // Only admins can add relationships
    if (req.user.role !== 'admin') {
      return res.status(403).json({ msg: 'Not authorized to add patient relationships' });
    }

    const { kin_user_id, patient_id, relationship_type, is_primary } = req.body;

    try {
      // Check if the kin user exists
      const kinUserResult = await pool.query(
        'SELECT * FROM users WHERE user_id = $1 AND role = $2',
        [kin_user_id, 'kin']
      );

      if (kinUserResult.rows.length === 0) {
        return res.status(404).json({ msg: 'Kin user not found' });
      }

      // Check if the patient exists
      const patientResult = await pool.query(
        'SELECT * FROM patients WHERE patient_id = $1',
        [patient_id]
      );

      if (patientResult.rows.length === 0) {
        return res.status(404).json({ msg: 'Patient not found' });
      }

      // If this is set as primary, update any existing primary relationships to false
      if (is_primary) {
        await pool.query(
          'UPDATE kin_patient_relationships SET is_primary = false WHERE kin_user_id = $1',
          [kin_user_id]
        );
      }

      // Add the relationship
      const result = await pool.query(
        `INSERT INTO kin_patient_relationships
         (kin_user_id, patient_id, relationship_type, is_primary)
         VALUES ($1, $2, $3, $4)
         ON CONFLICT (kin_user_id, patient_id)
         DO UPDATE SET relationship_type = $3, is_primary = $4
         RETURNING *`,
        [kin_user_id, patient_id, relationship_type, is_primary]
      );

      // Log the action (if security_logs table exists)
      try {
        await logSecurityEvent(
          req.user.id,
          'ADD_KIN_PATIENT_RELATIONSHIP',
          `Added relationship between kin user ${kin_user_id} and patient ${patient_id}`,
          req.ip
        );
      } catch (logErr) {
        console.error('Error logging security event:', logErr);
        // Continue execution even if logging fails
      }

      res.json(result.rows[0]);
    } catch (err) {
      console.error('Error adding kin-patient relationship:', err);
      res.status(500).json({ msg: 'Server error' });
    }
  }
);

// @route   DELETE api/kin/patients/:relationship_id
// @desc    Remove a patient relationship for a kin user
// @access  Private (admin only)
router.delete('/patients/:relationship_id', auth, async (req, res) => {
  try {
    // Only admins can remove relationships
    if (req.user.role !== 'admin') {
      return res.status(403).json({ msg: 'Not authorized to remove patient relationships' });
    }

    const { relationship_id } = req.params;

    // Get the relationship details before deleting (for logging)
    const relationshipResult = await pool.query(
      'SELECT * FROM kin_patient_relationships WHERE relationship_id = $1',
      [relationship_id]
    );

    if (relationshipResult.rows.length === 0) {
      return res.status(404).json({ msg: 'Relationship not found' });
    }

    const relationship = relationshipResult.rows[0];

    // Delete the relationship
    await pool.query(
      'DELETE FROM kin_patient_relationships WHERE relationship_id = $1',
      [relationship_id]
    );

    // Log the action
    await logSecurityEvent(
      req.user.id,
      'REMOVE_KIN_PATIENT_RELATIONSHIP',
      `Removed relationship between kin user ${relationship.kin_user_id} and patient ${relationship.patient_id}`,
      req.ip
    );

    res.json({ msg: 'Relationship removed successfully' });
  } catch (err) {
    console.error('Error removing kin-patient relationship:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

// @route   GET api/kin/relationships/:kin_user_id
// @desc    Get all patient relationships for a specific kin user (for admin use)
// @access  Private (admin only)
router.get('/relationships/:kin_user_id', auth, async (req, res) => {
  try {
    // Only admins can view all relationships for a kin user
    if (req.user.role !== 'admin') {
      return res.status(403).json({ msg: 'Not authorized to view kin relationships' });
    }

    const { kin_user_id } = req.params;

    // Check if the kin user exists
    const kinUserResult = await pool.query(
      'SELECT * FROM users WHERE user_id = $1 AND role = $2',
      [kin_user_id, 'kin']
    );

    if (kinUserResult.rows.length === 0) {
      return res.status(404).json({ msg: 'Kin user not found' });
    }

    // Get all relationships for this kin user
    const result = await pool.query(`
      SELECT kpr.relationship_id, kpr.patient_id, p.first_name, p.last_name,
             p.date_of_birth, p.gender, kpr.relationship_type, kpr.is_primary
      FROM kin_patient_relationships kpr
      JOIN patients p ON kpr.patient_id = p.patient_id
      WHERE kpr.kin_user_id = $1
      ORDER BY kpr.is_primary DESC, p.last_name, p.first_name
    `, [kin_user_id]);

    res.json(result.rows);
  } catch (err) {
    console.error('Error fetching kin relationships:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

// @route   PUT api/kin/patients/:relationship_id/primary
// @desc    Set a relationship as primary for a kin user
// @access  Private (admin only)
router.put('/patients/:relationship_id/primary', auth, async (req, res) => {
  try {
    // Only admins can set primary relationships
    if (req.user.role !== 'admin') {
      return res.status(403).json({ msg: 'Not authorized to set primary relationships' });
    }

    const { relationship_id } = req.params;

    // Get the relationship details
    const relationshipResult = await pool.query(
      'SELECT * FROM kin_patient_relationships WHERE relationship_id = $1',
      [relationship_id]
    );

    if (relationshipResult.rows.length === 0) {
      return res.status(404).json({ msg: 'Relationship not found' });
    }

    const relationship = relationshipResult.rows[0];

    // Update all relationships for this kin user to not be primary
    await pool.query(
      'UPDATE kin_patient_relationships SET is_primary = false WHERE kin_user_id = $1',
      [relationship.kin_user_id]
    );

    // Set this relationship as primary
    await pool.query(
      'UPDATE kin_patient_relationships SET is_primary = true WHERE relationship_id = $1',
      [relationship_id]
    );

    // Log the action
    await logSecurityEvent(
      req.user.id,
      'SET_PRIMARY_KIN_RELATIONSHIP',
      `Set primary relationship between kin user ${relationship.kin_user_id} and patient ${relationship.patient_id}`,
      req.ip
    );

    res.json({ msg: 'Primary relationship set successfully' });
  } catch (err) {
    console.error('Error setting primary relationship:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

module.exports = router;
