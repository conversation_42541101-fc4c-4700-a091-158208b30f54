const express = require('express');
const router = express.Router();
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const Doctor = require('../models/Doctor');
const Patient = require('../models/Patient');
const LoginActivity = require('../models/LoginActivity');
const UserActivity = require('../models/UserActivity');
const PasswordPolicy = require('../models/PasswordPolicy');
const auth = require('../middleware/auth');
const bcrypt = require('bcryptjs');
const pool = require('../db');
require('dotenv').config();

// Public registration has been removed
// Users can now only be created by administrators

// @route   POST api/auth/login
// @desc    Authenticate user & get token
// @access  Public
router.post('/login', async (req, res) => {
  const { email, password } = req.body;

  console.log('Login attempt received for:', { email });
  const ipAddress = req.ip || req.connection.remoteAddress;
  const userAgent = req.headers['user-agent'];

  try {
    // Check if email contains @ symbol - if not, treat as username
    let user;
    if (email && email.includes('@')) {
      // Find user by email
      user = await User.findByEmail(email);
      console.log('Looking up by email:', email);
    } else {
      // Find user by username
      user = await User.findByUsername(email);
      console.log('Looking up by username:', email);
    }

    console.log('User found:', user ? { user_id: user.user_id, username: user.username, role: user.role } : 'No user found');

    if (!user) {
      console.log('Login failed: User not found');

      // Log failed login attempt for non-existent user
      try {
        await LoginActivity.create(
          null,
          email,
          ipAddress,
          'failed',
          'Invalid credentials - user not found',
          userAgent
        );
      } catch (logErr) {
        console.error('Failed to log login activity, but continuing:', logErr.message);
      }

      return res.status(400).json({ msg: 'Invalid credentials' });
    }

    // Check if account is locked
    if (user.is_locked) {
      console.log(`Login failed: Account is locked for user: ${user.username} (ID: ${user.user_id})`);

      // Get the lockout message
      const lockoutMessage = 'Account is locked. Please contact an administrator to unlock your account.';
      const lockoutDetails = 'Account locked - admin unlock required';

      // Log failed login attempt for locked account
      try {
        await LoginActivity.create(
          user.user_id,
          user.username,
          ipAddress,
          'failed',
          lockoutDetails,
          userAgent
        );
      } catch (logErr) {
        console.error('Failed to log login activity, but continuing:', logErr.message);
      }

      // Get current failed attempts and max attempts for the response
      const policy = await PasswordPolicy.getCurrent();
      const maxAttempts = policy.max_failed_attempts;

      return res.status(401).json({
        msg: lockoutMessage,
        locked: true,
        maxAttempts: maxAttempts
      });
    }

    // Check password
    const isMatch = await User.comparePassword(password, user.password);
    console.log('Password match:', isMatch);

    if (!isMatch) {
      console.log(`Password does not match for user: ${user.username} (ID: ${user.user_id})`);

      // Increment failed login attempts and get policy info
      const result = await User.incrementLoginAttempts(user.user_id);
      const { failedAttempts, maxAttempts, isLocked } = result;
      console.log('Failed login attempt. Total attempts:', failedAttempts, 'of', maxAttempts, 'Account locked:', isLocked);

      // Log failed login attempt
      try {
        await LoginActivity.create(
          user.user_id,
          user.username,
          ipAddress,
          'failed',
          `Invalid password - attempt ${failedAttempts} of ${maxAttempts}`,
          userAgent
        );
      } catch (logErr) {
        console.error('Failed to log login activity, but continuing:', logErr.message);
      }

      // If account is now locked due to too many attempts
      if (isLocked) {
        console.log('Account locked due to too many failed attempts');

        // Log account locking
        try {
          await LoginActivity.create(
            user.user_id,
            user.username,
            ipAddress,
            'locked',
            `Account locked after ${maxAttempts} failed attempts`,
            userAgent
          );
        } catch (logErr) {
          console.error('Failed to log login activity, but continuing:', logErr.message);
        }

        return res.status(401).json({
          msg: `Too many failed login attempts. Your account has been locked. Please contact an administrator.`,
          locked: true
        });
      }

      // Account not locked yet, return appropriate message
      return res.status(400).json({
        msg: `Invalid credentials (Attempt ${failedAttempts} of ${maxAttempts})`,
        remainingAttempts: maxAttempts - failedAttempts,
        attemptsUsed: failedAttempts,
        maxAttempts: maxAttempts
      });
    }

    // Reset login attempts on successful login
    await User.resetLoginAttempts(user.user_id);
    console.log('Login successful, attempts reset');

    // Check if user needs to change password
    const passwordChangeCheck = await User.needsPasswordChange(user.user_id);
    console.log('Password change check:', passwordChangeCheck);

    // Log successful login
    try {
      await LoginActivity.create(
        user.user_id,
        user.username,
        ipAddress,
        'success',
        `Successful login as ${user.role}${passwordChangeCheck.required ? ' - password change required' : ''}`,
        userAgent
      );

      // Also log in user_activities for the activity dashboard
      await UserActivity.create({
        user_id: user.user_id,
        activity_type: 'login',
        activity_details: `User logged in as ${user.role}`,
        ip_address: ipAddress
      });
    } catch (logErr) {
      console.error('Failed to log login activity, but continuing:', logErr.message);
      // Continue with login process even if logging fails
    }

    // Create JWT payload
    const payload = {
      user: {
        id: user.user_id,
        username: user.username,
        role: user.role,
        passwordChange: passwordChangeCheck
      }
    };

    // Add doctor_id if user is a doctor
    if (user.role === 'doctor') {
      try {
        const doctorResult = await pool.query(
          'SELECT doctor_id FROM doctors WHERE user_id = $1 OR LOWER(email) = LOWER($2)',
          [user.user_id, user.email]
        );

        console.log('Doctor search result:', doctorResult.rows);

        if (doctorResult.rows.length > 0) {
          payload.user.doctor_id = doctorResult.rows[0].doctor_id;
          console.log('Added doctor_id to payload:', payload.user.doctor_id);
        } else {
          console.log('No doctor record found for user_id:', user.user_id);
        }
      } catch (err) {
        console.error('Error fetching doctor info:', err.message);
      }
    }

    // Add patient_id if user is a patient
    if (user.role === 'patient') {
      try {
        // Use direct SQL query since we added the user_id column manually
        const patientResult = await pool.query(
          'SELECT patient_id FROM patients WHERE user_id = $1',
          [user.user_id]
        );

        console.log('Patient query result:', patientResult.rows);

        if (patientResult.rows.length > 0) {
          payload.user.patient_id = patientResult.rows[0].patient_id;
          console.log('Added patient_id to payload:', payload.user.patient_id);
        } else {
          console.log('No patient record found for user_id:', user.user_id);
        }
      } catch (err) {
        console.error('Error fetching patient info:', err.message);
      }
    }

    console.log('Final JWT payload:', payload);

    // Sign token
    jwt.sign(
      payload,
      process.env.JWT_SECRET,
      { expiresIn: '12h' },
      (err, token) => {
        if (err) {
          console.error('JWT signing error:', err);
          throw err;
        }
        console.log('Token generated successfully');
        res.json({ token });
      }
    );
  } catch (err) {
    console.error('Server error during login:', err.message);
    res.status(500).send('Server error');
  }
});

// @route   GET api/auth/user
// @desc    Get user data
// @access  Private
router.get('/user', auth, async (req, res) => {
  try {
    const user = await User.findById(req.user.id);
    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    // Remove password from response
    const { password, password_history, ...userData } = user;

    // Check if user needs to change password
    const passwordChangeCheck = await User.needsPasswordChange(user.user_id);
    userData.passwordChange = passwordChangeCheck;

    // If user is a doctor, get their doctor_id
    if (user.role === 'doctor') {
      try {
        const doctorResult = await pool.query(
          'SELECT d.doctor_id FROM doctors d WHERE LOWER(d.email) = LOWER($1) OR d.user_id = $2',
          [user.email, user.user_id]
        );

        if (doctorResult.rows.length > 0) {
          userData.doctor_id = doctorResult.rows[0].doctor_id;
        }
      } catch (err) {
        console.error('Error fetching doctor_id:', err);
      }
    }

    // If user is a patient, get their patient_id
    if (user.role === 'patient') {
      try {
        const patientResult = await pool.query(
          'SELECT patient_id FROM patients WHERE user_id = $1',
          [user.user_id]
        );

        if (patientResult.rows.length > 0) {
          userData.patient_id = patientResult.rows[0].patient_id;
        }
      } catch (err) {
        console.error('Error fetching patient_id:', err);
      }
    }

    res.json(userData);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   POST api/auth/reset-password
// @desc    Reset user password using their username (admin function)
// @access  Public
router.post('/reset-password', async (req, res) => {
  const { username, newPassword } = req.body;

  try {
    // Check if user exists
    const user = await User.findByUsername(username);
    if (!user) {
      return res.status(400).json({ msg: 'User not found' });
    }

    // Validate password against policy
    const validation = await PasswordPolicy.validatePassword(newPassword, user.user_id, user.username);
    if (!validation.isValid) {
      return res.status(400).json({
        msg: 'Password does not meet requirements',
        errors: validation.errors
      });
    }

    // Hash new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);

    // Update user's password
    await User.update(user.user_id, {
      password: hashedPassword,
      is_first_login: true,
      default_password: true,
      password_last_changed: new Date()
    });

    res.json({ msg: 'Password reset successfully. User will be required to change password on next login.' });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   POST api/auth/change-password
// @desc    Change user password (requires authentication)
// @access  Private
router.post('/change-password', auth, async (req, res) => {
  const { currentPassword, newPassword } = req.body;
  const userId = req.user.id;

  try {
    // Get user
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    // If current password is provided (not first login), verify it
    if (currentPassword) {
      const isMatch = await User.comparePassword(currentPassword, user.password);
      if (!isMatch) {
        return res.status(400).json({ msg: 'Current password is incorrect' });
      }
    }

    // Validate new password against policy
    const validation = await PasswordPolicy.validatePassword(newPassword, userId, user.username);
    if (!validation.isValid) {
      return res.status(400).json({
        msg: 'Password does not meet requirements',
        errors: validation.errors
      });
    }

    // Change password
    await User.changePassword(userId, newPassword);

    // Log password change
    try {
      const ipAddress = req.ip || req.connection.remoteAddress;
      const userAgent = req.headers['user-agent'];
      await LoginActivity.create(
        userId,
        user.username,
        ipAddress,
        'password_change',
        'User changed password',
        userAgent
      );
    } catch (logErr) {
      console.error('Failed to log password change activity, but continuing:', logErr.message);
    }

    res.json({
      msg: 'Password changed successfully',
      requiresRelogin: true
    });
  } catch (err) {
    console.error('Error changing password:', err.message);
    res.status(500).send('Server error');
  }
});

// @route   GET api/auth/password-policy
// @desc    Get current password policy
// @access  Public
router.get('/password-policy', async (req, res) => {
  try {
    const policy = await PasswordPolicy.getCurrent();

    // Remove sensitive fields
    const { policy_id, created_at, updated_at, ...publicPolicy } = policy;

    res.json(publicPolicy);
  } catch (err) {
    console.error('Error fetching password policy:', err.message);
    res.status(500).send('Server error');
  }
});

// @route   POST api/auth/logout
// @desc    Log user logout
// @access  Private
router.post('/logout', auth, async (req, res) => {
  try {
    const userId = req.user.id;
    const user = await User.findById(userId);

    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    const ipAddress = req.ip || req.connection.remoteAddress;
    const userAgent = req.headers['user-agent'];

    // Log logout in login_activities
    await LoginActivity.create(
      userId,
      user.username,
      ipAddress,
      'logout',
      'User logged out',
      userAgent
    );

    // Also log in user_activities for the activity dashboard
    await UserActivity.create({
      user_id: userId,
      activity_type: 'logout',
      activity_details: 'User logged out',
      ip_address: ipAddress
    });

    res.json({ msg: 'Logout successful' });
  } catch (err) {
    console.error('Error logging out:', err.message);
    res.status(500).send('Server error');
  }
});

// @route   POST api/auth/validate-password
// @desc    Validate a password against the current policy
// @access  Private
router.post('/validate-password', auth, async (req, res) => {
  const { password } = req.body;
  const userId = req.user.id;

  try {
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    const validation = await PasswordPolicy.validatePassword(password, userId, user.username);
    res.json(validation);
  } catch (err) {
    console.error('Error validating password:', err.message);
    res.status(500).send('Server error');
  }
});

module.exports = router;