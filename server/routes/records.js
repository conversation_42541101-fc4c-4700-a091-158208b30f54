const express = require('express');
const router = express.Router();
const MedicalRecord = require('../models/MedicalRecord');
const Patient = require('../models/Patient');
const auth = require('../middleware/auth');

// @route   POST api/records
// @desc    Create a new medical record
// @access  Private
router.post('/', auth, async (req, res) => {
  try {
    // Check if patient exists
    const patient = await Patient.getById(req.body.patient_id);
    if (!patient) {
      return res.status(404).json({ msg: 'Patient not found' });
    }
    
    // Add user ID to record
    const recordData = {
      ...req.body,
      created_by: req.user.id
    };
    
    const record = await MedicalRecord.create(recordData);
    res.status(201).json(record);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   GET api/records/patient/:patientId
// @desc    Get all records for a patient
// @access  Private
router.get('/patient/:patientId', auth, async (req, res) => {
  try {
    // Check if patient exists
    const patient = await Patient.getById(req.params.patientId);
    if (!patient) {
      return res.status(404).json({ msg: 'Patient not found' });
    }
    
    const records = await MedicalRecord.getByPatientId(req.params.patientId);
    res.json(records);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   GET api/records/:id
// @desc    Get record by ID
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const record = await MedicalRecord.getById(req.params.id);
    
    if (!record) {
      return res.status(404).json({ msg: 'Record not found' });
    }
    
    res.json(record);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   PUT api/records/:id
// @desc    Update record
// @access  Private
router.put('/:id', auth, async (req, res) => {
  try {
    // Check if record exists
    let record = await MedicalRecord.getById(req.params.id);
    if (!record) {
      return res.status(404).json({ msg: 'Record not found' });
    }
    
    // Update record
    record = await MedicalRecord.update(req.params.id, req.body);
    res.json(record);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   DELETE api/records/:id
// @desc    Delete record
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    // Check if record exists
    const record = await MedicalRecord.getById(req.params.id);
    if (!record) {
      return res.status(404).json({ msg: 'Record not found' });
    }
    
    // Delete record
    await MedicalRecord.delete(req.params.id);
    res.json({ msg: 'Record removed' });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

// @route   GET api/records/recent
// @desc    Get recent records
// @access  Private
router.get('/recent/all', auth, async (req, res) => {
  try {
    const limit = req.query.limit ? parseInt(req.query.limit) : 10;
    const records = await MedicalRecord.getRecent(limit);
    res.json(records);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server error');
  }
});

module.exports = router; 