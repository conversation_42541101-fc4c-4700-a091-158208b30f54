const express = require('express');
const router = express.Router();
const Prescription = require('../models/Prescription');
const Patient = require('../models/Patient');
const auth = require('../middleware/auth');
const pool = require('../db');
const { check, validationResult } = require('express-validator');

// @route   POST api/prescriptions
// @desc    Create a new prescription
// @access  Private
router.post('/', [
  auth,
  [
    check('medication', 'Medication name is required').not().isEmpty(),
    check('dosage', 'Dosage is required').not().isEmpty(),
    check('frequency', 'Frequency is required').not().isEmpty(),
    check('duration', 'Duration is required').not().isEmpty()
  ]
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    // Validate that at least one ID is provided
    if (!req.body.patient_id && !req.body.visit_id) {
      return res.status(400).json({ msg: 'Either patient_id or visit_id must be provided' });
    }

    // If patient_id is provided
    if (req.body.patient_id) {
      // Check if patient exists
      const patient = await Patient.getById(req.body.patient_id);
      if (!patient) {
        return res.status(404).json({ msg: 'Patient not found' });
      }
    }

    // If visit_id is provided
    if (req.body.visit_id) {
      // Check if visit exists
      try {
        const visitQuery = 'SELECT * FROM patient_visits WHERE visit_id = $1';
        const visitResult = await pool.query(visitQuery, [req.body.visit_id]);
        if (visitResult.rows.length === 0) {
          return res.status(404).json({ msg: 'Visit not found' });
        }
      } catch (err) {
        console.error('Error checking visit:', err);
        return res.status(500).send('Server error');
      }
    }

    const prescription = await Prescription.create(req.body);
    res.status(201).json(prescription);
  } catch (err) {
    console.error('Error creating prescription:', err.message);
    res.status(500).send('Server error');
  }
});

// @route   POST api/prescriptions/batch
// @desc    Create multiple prescriptions for a patient or visit
// @access  Private
router.post('/batch', [
  auth,
  [
    check('prescriptions', 'Prescriptions array is required').isArray(),
    check('prescriptions.*.medication', 'Medication name is required for all prescriptions').not().isEmpty(),
    check('prescriptions.*.dosage', 'Dosage is required for all prescriptions').not().isEmpty(),
    check('prescriptions.*.frequency', 'Frequency is required for all prescriptions').not().isEmpty(),
    check('prescriptions.*.duration', 'Duration is required for all prescriptions').not().isEmpty()
  ]
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    // Log the authenticated user
    console.log('Authenticated user for prescription batch:', {
      user_id: req.user?.id,
      username: req.user?.username,
      role: req.user?.role
    });

    // Add the user ID to the request body for use in BEERS criteria overrides
    req.body.user_id = req.user?.id;

    // Debug log the request body
    console.log('Prescription batch request received:', {
      patient_id: req.body.patient_id,
      visit_id: req.body.visit_id,
      prescription_count: req.body.prescriptions?.length || 0,
      first_prescription: req.body.prescriptions?.[0] || null
    });

    // Detailed debug logging
    console.log('DETAILED DEBUG: Full prescriptions request body:', JSON.stringify(req.body, null, 2));

    // Validate that at least one ID is provided
    if (!req.body.patient_id && !req.body.visit_id) {
      return res.status(400).json({ msg: 'Either patient_id or visit_id must be provided' });
    }

    // Create options object for createMany
    const options = {
      userId: req.user?.id // Add the user ID to the options
    };

    // If patient_id is provided
    if (req.body.patient_id) {
      // Check if patient exists
      const patient = await Patient.getById(req.body.patient_id);
      if (!patient) {
        return res.status(404).json({ msg: 'Patient not found' });
      }
      options.patientId = req.body.patient_id;
      options.visitType = req.body.visit_type || 'initial';
      console.log('Using patient_id in options:', options.patientId);
    }

    // If visit_id is provided
    if (req.body.visit_id) {
      // Check if visit exists
      try {
        const visitQuery = 'SELECT * FROM patient_visits WHERE visit_id = $1';
        const visitResult = await pool.query(visitQuery, [req.body.visit_id]);
        if (visitResult.rows.length === 0) {
          return res.status(404).json({ msg: 'Visit not found' });
        }

        // If patient_id is not provided, get it from the visit
        if (!req.body.patient_id && visitResult.rows[0].patient_id) {
          options.patientId = visitResult.rows[0].patient_id;
          console.log('Retrieved patient_id from visit:', options.patientId);
        }
      } catch (err) {
        console.error('Error checking visit:', err);
        return res.status(500).send('Server error');
      }
      options.visitId = req.body.visit_id;
      options.visitType = 'follow-up';
      console.log('Using visit_id in options:', options.visitId);
    }

    // Process each prescription to ensure it has the necessary fields
    const processedPrescriptions = req.body.prescriptions.map(prescription => {
      // Log the original prescription data for debugging
      console.log(`Processing prescription for ${prescription.medication}:`, {
        patient_id: prescription.patient_id,
        beers_criteria_id: prescription.beers_criteria_id,
        beers_override_reason: prescription.beers_override_reason,
        beers_overridden_by: prescription.beers_overridden_by,
        beers_overridden_at: prescription.beers_overridden_at
      });

      // Create a copy of the prescription to avoid modifying the original
      const processedPrescription = { ...prescription };

      // If the prescription has its own patient_id, use it
      if (processedPrescription.patient_id) {
        console.log(`Prescription for ${processedPrescription.medication} has its own patient_id: ${processedPrescription.patient_id}`);
      }
      // Otherwise, use the patient_id from options
      else if (options.patientId) {
        console.log(`Adding patient_id ${options.patientId} to prescription for ${processedPrescription.medication}`);
        processedPrescription.patient_id = options.patientId;
      }
      // If we have a visit_id but no patient_id, log a warning
      else if (options.visitId && !options.patientId) {
        console.warn(`Warning: Creating prescription for ${processedPrescription.medication} with visit_id ${options.visitId} but no patient_id. The model will attempt to retrieve the patient_id from the visit.`);
      }

      // Ensure BEERS criteria fields are preserved
      if (prescription.beers_criteria_id || prescription.beers_override_reason) {
        // If we have BEERS override data, make sure we have the user ID
        if (prescription.beers_override_reason) {
          // Use the user ID from the auth token if available
          if (req.user) {
            // The user ID could be in req.user.id (from JWT) or in the prescription object
            const userId = req.user.id;

            if (userId) {
              processedPrescription.beers_overridden_by = userId;
              console.log(`Setting beers_overridden_by to authenticated user ID: ${userId}`);
            } else {
              console.log('User ID not found in auth token, full req.user object:', req.user);

              // Keep the existing value if provided
              if (prescription.beers_overridden_by) {
                processedPrescription.beers_overridden_by = prescription.beers_overridden_by;
                console.log(`Using provided beers_overridden_by: ${prescription.beers_overridden_by}`);
              } else {
                console.log('No beers_overridden_by value available');
              }
            }
          } else {
            console.log('No user object in request');

            // Keep the existing value if provided
            if (prescription.beers_overridden_by) {
              processedPrescription.beers_overridden_by = prescription.beers_overridden_by;
              console.log(`Using provided beers_overridden_by: ${prescription.beers_overridden_by}`);
            } else {
              console.log('No beers_overridden_by value available');
            }
          }
        }

        console.log(`Preserving BEERS criteria data for ${processedPrescription.medication}:`, {
          beers_criteria_id: prescription.beers_criteria_id,
          beers_override_reason: prescription.beers_override_reason,
          beers_overridden_by: processedPrescription.beers_overridden_by,
          beers_overridden_at: prescription.beers_overridden_at
        });
      }

      return processedPrescription;
    });

    // Log the final processed prescriptions before creating them
    console.log('Final processed prescriptions:', processedPrescriptions.map(p => ({
      medication: p.medication,
      patient_id: p.patient_id,
      visit_id: options.visitId,
      beers_criteria_id: p.beers_criteria_id,
      beers_override_reason: p.beers_override_reason,
      beers_overridden_by: p.beers_overridden_by,
      beers_overridden_at: p.beers_overridden_at
    })));

    const prescriptions = await Prescription.createMany(processedPrescriptions, options);

    // Log the created prescriptions
    console.log(`Created ${prescriptions.length} prescriptions successfully:`,
      prescriptions.map(p => ({
        prescription_id: p.prescription_id,
        medication: p.medication,
        patient_id: p.patient_id,
        visit_id: p.visit_id,
        beers_criteria_id: p.beers_criteria_id,
        beers_override_reason: p.beers_override_reason,
        beers_overridden_by: p.beers_overridden_by,
        beers_overridden_at: p.beers_overridden_at
      }))
    );

    res.status(201).json(prescriptions);
  } catch (err) {
    console.error('Error creating prescriptions:', err.message);
    res.status(500).send('Server error');
  }
});



// @route   GET api/prescriptions/patient/:patientId
// @desc    Get prescriptions for a patient that are not associated with a visit
// @access  Private
router.get('/patient/:patientId', auth, async (req, res) => {
  try {
    // Check if patient exists
    const patient = await Patient.getById(req.params.patientId);
    if (!patient) {
      return res.status(404).json({ msg: 'Patient not found' });
    }

    // Get prescriptions that are not associated with a visit
    const prescriptions = await Prescription.getByPatientId(req.params.patientId);
    console.log(`Returning ${prescriptions.length} prescriptions for patient ${req.params.patientId} (without visit_id)`);
    res.json(prescriptions);
  } catch (err) {
    console.error('Error getting patient prescriptions:', err.message);
    res.status(500).send('Server error');
  }
});

// @route   GET api/prescriptions/patient/:patientId/all
// @desc    Get all prescriptions for a patient, including those associated with visits
// @access  Private
router.get('/patient/:patientId/all', auth, async (req, res) => {
  try {
    // Check if patient exists
    const patient = await Patient.getById(req.params.patientId);
    if (!patient) {
      return res.status(404).json({ msg: 'Patient not found' });
    }

    // Get all prescriptions for the patient, including those associated with visits
    const prescriptions = await Prescription.getAllByPatientId(req.params.patientId);
    console.log(`Returning ${prescriptions.length} total prescriptions for patient ${req.params.patientId} (including visit prescriptions)`);
    res.json(prescriptions);
  } catch (err) {
    console.error('Error getting all patient prescriptions:', err.message);
    res.status(500).send('Server error');
  }
});

// @route   GET api/prescriptions/visit/:visitId
// @desc    Get all prescriptions for a visit
// @access  Private
router.get('/visit/:visitId', auth, async (req, res) => {
  try {
    // Check if visit exists
    const visitQuery = 'SELECT * FROM patient_visits WHERE visit_id = $1';
    const visitResult = await pool.query(visitQuery, [req.params.visitId]);

    if (visitResult.rows.length === 0) {
      return res.status(404).json({ msg: 'Visit not found' });
    }

    const prescriptions = await Prescription.getByVisitId(req.params.visitId);
    res.json(prescriptions);
  } catch (err) {
    console.error('Error getting visit prescriptions:', err.message);
    res.status(500).send('Server error');
  }
});

// @route   GET api/prescriptions/:id
// @desc    Get prescription by ID
// @access  Private
router.get('/:id', auth, async (req, res) => {
  try {
    const prescription = await Prescription.getById(req.params.id);

    if (!prescription) {
      return res.status(404).json({ msg: 'Prescription not found' });
    }

    res.json(prescription);
  } catch (err) {
    console.error('Error getting prescription:', err.message);
    res.status(500).send('Server error');
  }
});

// @route   PUT api/prescriptions/:id
// @desc    Update prescription
// @access  Private
router.put('/:id', [
  auth,
  [
    check('medication', 'Medication name is required').not().isEmpty(),
    check('dosage', 'Dosage is required').not().isEmpty(),
    check('frequency', 'Frequency is required').not().isEmpty()
  ]
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    // Check if prescription exists
    let prescription = await Prescription.getById(req.params.id);
    if (!prescription) {
      return res.status(404).json({ msg: 'Prescription not found' });
    }

    // Process the prescription data to ensure BEERS criteria fields are preserved
    const processedPrescription = { ...req.body };

    // Ensure BEERS criteria fields are preserved
    if (req.body.beers_override_reason) {
      // Use the user ID from the auth token if available
      if (req.user && req.user.id) {
        processedPrescription.beers_overridden_by = req.user.id;
        console.log(`Setting beers_overridden_by to authenticated user ID: ${req.user.id}`);
      } else {
        console.log('User ID not found in auth token, checking for user.id:', req.user?.id);
        console.log('Full req.user object:', req.user);

        // Keep the existing value if provided
        if (req.body.beers_overridden_by) {
          processedPrescription.beers_overridden_by = req.body.beers_overridden_by;
          console.log(`Using provided beers_overridden_by: ${req.body.beers_overridden_by}`);
        } else {
          console.log('No beers_overridden_by value available');
        }
      }
    }

    // Update prescription
    prescription = await Prescription.update(req.params.id, processedPrescription);
    res.json(prescription);
  } catch (err) {
    console.error('Error updating prescription:', err.message);
    res.status(500).send('Server error');
  }
});

// @route   DELETE api/prescriptions/:id
// @desc    Delete prescription
// @access  Private
router.delete('/:id', auth, async (req, res) => {
  try {
    // Check if prescription exists
    const prescription = await Prescription.getById(req.params.id);
    if (!prescription) {
      return res.status(404).json({ msg: 'Prescription not found' });
    }

    // Delete prescription
    await Prescription.delete(req.params.id);
    res.json({ msg: 'Prescription removed' });
  } catch (err) {
    console.error('Error deleting prescription:', err.message);
    res.status(500).send('Server error');
  }
});

// @route   POST api/prescriptions/:id/override-beers
// @desc    Override BEERS Criteria alert for a prescription
// @access  Private
router.post('/:id/override-beers', [
  auth,
  [
    check('criteriaId', 'BEERS Criteria ID is required').isNumeric(),
    check('reason', 'Override reason is required').not().isEmpty()
  ]
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    // Check if prescription exists
    const prescription = await Prescription.getById(req.params.id);
    if (!prescription) {
      return res.status(404).json({ msg: 'Prescription not found' });
    }

    // Create override data
    const overrideData = {
      criteriaId: req.body.criteriaId,
      reason: req.body.reason,
      userId: req.user.id // From auth middleware
    };

    // Log the user ID for debugging
    console.log('User ID for BEERS override:', req.user.id);

    // Override BEERS Criteria alert
    const updatedPrescription = await Prescription.overrideBeersCriteria(req.params.id, overrideData);
    res.json(updatedPrescription);
  } catch (err) {
    console.error('Error overriding BEERS Criteria alert:', err.message);
    res.status(500).send('Server error');
  }
});

module.exports = router;
