const express = require('express');
const router = express.Router();
const auth = require('../middleware/auth');
const adminCheck = require('../middleware/adminCheck');
const pool = require('../db');
const User = require('../models/User');
const UserBatch = require('../models/UserBatch');
const UserActivity = require('../models/UserActivity');
const PasswordPolicy = require('../models/PasswordPolicy');
const SystemSettings = require('../models/SystemSettings');
const IpRestriction = require('../models/IpRestriction');
const bcrypt = require('bcryptjs');
const { check, validationResult } = require('express-validator');
const path = require('path');
const fs = require('fs');

// Simple test route to check if admin routes are working
router.get('/test', async (req, res) => {
  console.log('Admin test route accessed');
  res.json({ msg: 'Admin routes are working' });
});

// @route   POST api/admin/create-kin-relationships-table
// @desc    Create the kin_patient_relationships table
// @access  Private/Admin
router.post('/create-kin-relationships-table', [auth, adminCheck], async (req, res) => {
  try {
    console.log('Creating kin_patient_relationships table...');

    // Check if the table already exists
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'kin_patient_relationships'
      ) as table_exists
    `);

    if (tableExists.rows[0].table_exists) {
      console.log('kin_patient_relationships table already exists');
      return res.json({ msg: 'Table already exists' });
    }

    // Create the table
    const sql = `
      CREATE TABLE IF NOT EXISTS kin_patient_relationships (
        relationship_id SERIAL PRIMARY KEY,
        kin_user_id INTEGER NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
        patient_id INTEGER NOT NULL REFERENCES patients(patient_id) ON DELETE CASCADE,
        relationship_type VARCHAR(50),
        is_primary BOOLEAN DEFAULT false,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(kin_user_id, patient_id)
      );

      CREATE INDEX IF NOT EXISTS idx_kin_patient_kin_user_id ON kin_patient_relationships(kin_user_id);
      CREATE INDEX IF NOT EXISTS idx_kin_patient_patient_id ON kin_patient_relationships(patient_id);
    `;

    await pool.query(sql);

    console.log('Successfully created kin_patient_relationships table');

    // Migrate existing kin-patient relationships from users table
    console.log('Migrating existing kin-patient relationships...');

    const migrateSQL = `
      INSERT INTO kin_patient_relationships (kin_user_id, patient_id, is_primary)
      SELECT user_id, patient_id, true
      FROM users
      WHERE role = 'kin' AND patient_id IS NOT NULL
      ON CONFLICT (kin_user_id, patient_id) DO NOTHING;
    `;

    await pool.query(migrateSQL);

    console.log('Migration completed successfully');

    res.json({ msg: 'Table created and data migrated successfully' });
  } catch (err) {
    console.error('Error creating kin_patient_relationships table:', err);
    res.status(500).json({ msg: 'Server error' });
  }
});

// @route   GET api/admin/stats
// @desc    Get system statistics
// @access  Private/Admin
router.get('/stats', [auth, adminCheck], async (req, res) => {
  try {
    console.log('Admin stats endpoint called by user:', req.user.id);

    // Check if tables exist first
    const tablesExist = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'patients'
      ) as patients_exist,
      EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'doctors'
      ) as doctors_exist,
      EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'medical_records'
      ) as records_exist,
      EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'users'
      ) as users_exist
    `);

    console.log('Tables exist check:', tablesExist.rows[0]);

    // If any required table doesn't exist, return zeros
    if (!tablesExist.rows[0].patients_exist ||
        !tablesExist.rows[0].doctors_exist ||
        !tablesExist.rows[0].records_exist ||
        !tablesExist.rows[0].users_exist) {
      console.log('Some tables do not exist, returning zeros');
      return res.json({
        totalPatients: 0,
        totalDoctors: 0,
        totalRecords: 0,
        totalUsers: 0
      });
    }

    console.log('Running stats query...');

    const stats = await pool.query(`
      SELECT
        (SELECT COUNT(*) FROM patients) as total_patients,
        (SELECT COUNT(*) FROM doctors) as total_doctors,
        (SELECT COUNT(*) FROM medical_records) as total_records,
        (SELECT COUNT(*) FROM users) as total_users
    `);

    console.log('Stats query result:', stats.rows[0]);

    res.json({
      totalPatients: parseInt(stats.rows[0].total_patients || '0'),
      totalDoctors: parseInt(stats.rows[0].total_doctors || '0'),
      totalRecords: parseInt(stats.rows[0].total_records || '0'),
      totalUsers: parseInt(stats.rows[0].total_users || '0')
    });
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

// @route   GET api/admin/users
// @desc    Get all users
// @access  Private/Admin
router.get('/users', [auth, adminCheck], async (req, res) => {
  try {
    console.log('Admin users endpoint called by user:', req.user.id);

    // Check if users table exists
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'users'
      ) as users_exist
    `);

    console.log('Users table exists:', tableExists.rows[0].users_exist);

    if (!tableExists.rows[0].users_exist) {
      console.log('Users table does not exist, returning empty array');
      return res.json([]);
    }

    console.log('Fetching all users...');
    const users = await pool.query(
      'SELECT user_id, username, email, role, created_at FROM users ORDER BY created_at DESC'
    );

    console.log(`Found ${users.rows.length} users`);
    res.json(users.rows);
  } catch (err) {
    console.error('Error fetching users:', err.message);
    res.status(500).send('Server Error');
  }
});

// @route   PUT api/admin/users/:id
// @desc    Update user
// @access  Private/Admin
router.put('/users/:id', [auth, adminCheck], async (req, res) => {
  try {
    const userId = req.params.id;

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    // Update user
    const updatedUser = await User.update(userId, req.body);

    // Remove password from response
    const { password, ...userData } = updatedUser;

    res.json(userData);
  } catch (err) {
    console.error('Error updating user:', err.message);
    if (err.message.includes('not found')) {
      return res.status(404).json({ msg: err.message });
    }
    res.status(500).json({ msg: 'Server Error' });
  }
});

// @route   POST api/admin/users/:id/reset-password
// @desc    Reset a user's password (admin only)
// @access  Private/Admin
router.post('/users/:id/reset-password', [
  auth,
  adminCheck,
  [
    check('password', 'Please enter a password with 6 or more characters').isLength({ min: 6 })
  ]
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const userId = req.params.id;
    const { password } = req.body;
    const adminId = req.user.id;

    console.log(`Admin ${adminId} is resetting password for user ${userId}`);

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    // Hash the new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Update user's password and unlock the account
    const updatedUser = await pool.query(
      'UPDATE users SET password = $1, is_locked = false, failed_login_attempts = 0, lockout_until = NULL WHERE user_id = $2 RETURNING user_id, username, email, role, is_locked',
      [hashedPassword, userId]
    );

    if (updatedUser.rows.length === 0) {
      return res.status(404).json({ msg: 'User not found' });
    }

    console.log(`Password reset successful for user ${user.username}`);

    res.json({
      msg: 'Password reset successful',
      user: updatedUser.rows[0]
    });
  } catch (err) {
    console.error('Error resetting password:', err);
    res.status(500).json({ msg: err.message || 'Server error' });
  }
});

// @route   DELETE api/admin/users/:id
// @desc    Delete user
// @access  Private/Admin
router.delete('/users/:id', [auth, adminCheck], async (req, res) => {
  try {
    const userId = req.params.id;
    const adminId = req.user.id;

    // Check if user exists
    const user = await User.findById(userId);
    if (!user) {
      return res.status(404).json({ msg: 'User not found' });
    }

    // Get the user's patient ID if they are a patient
    let patientId = null;
    if (user.role === 'patient' && user.patient_id) {
      patientId = user.patient_id;
    }

    // Log the user deletion
    const PatientEditLog = require('../models/PatientEditLog');
    try {
      // First check if the edit_type column exists
      const columnCheckQuery = await pool.query(`
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'patient_edit_logs'
        AND column_name = 'edit_type'
      `);

      const hasEditTypeColumn = columnCheckQuery.rows.length > 0;
      console.log('patient_edit_logs has edit_type column:', hasEditTypeColumn);

      if (hasEditTypeColumn) {
        // If the user is a patient, log to their patient record
        if (patientId) {
          await PatientEditLog.create({
            patient_id: patientId,
            user_id: adminId,
            edit_type: 'delete',
            edit_summary: `User account deleted (ID: ${userId}, Username: ${user.username})`,
            field_changed: 'user_account',
            old_value: JSON.stringify({ id: userId, username: user.username }),
            new_value: null,
            original_data: JSON.stringify(user),
            new_data: null,
            ip_address: 'system'
          });
        } else {
          // For non-patient users, create a system-level log
          // Use a special patient_id of 0 to indicate system-level log
          await PatientEditLog.create({
            patient_id: 0,
            user_id: adminId,
            edit_type: 'delete',
            edit_summary: `User account deleted (ID: ${userId}, Username: ${user.username}, Role: ${user.role})`,
            field_changed: 'user_account',
            old_value: JSON.stringify({ id: userId, username: user.username, role: user.role }),
            new_value: null,
            original_data: JSON.stringify(user),
            new_data: null,
            ip_address: 'system'
          });
        }
      } else {
        // Use old schema without edit_type
        // If the user is a patient, log to their patient record
        if (patientId) {
          await PatientEditLog.create({
            patient_id: patientId,
            user_id: adminId,
            field_changed: 'user_account',
            old_value: JSON.stringify({ id: userId, username: user.username }),
            new_value: null
          });
        } else {
          // For non-patient users, create a system-level log with patient_id 0
          await PatientEditLog.create({
            patient_id: 0,
            user_id: adminId,
            field_changed: 'user_account',
            old_value: JSON.stringify({ id: userId, username: user.username, role: user.role }),
            new_value: null
          });
        }
      }
      console.log(`Created deletion log for user ID ${userId} (Username: ${user.username})`);
    } catch (logErr) {
      console.error('Error creating user deletion log:', logErr);
      // Continue with deletion even if logging fails
    }

    // Delete user from database
    await pool.query('DELETE FROM users WHERE user_id = $1', [userId]);

    res.json({
      msg: 'User removed',
      username: user.username,
      role: user.role
    });
  } catch (err) {
    console.error('Error deleting user:', err.message);
    res.status(500).json({ msg: 'Server Error' });
  }
});

// @route   POST api/admin/users
// @desc    Create a new user (admin only)
// @access  Private/Admin
router.post('/users', [
  auth,
  adminCheck,
  [
    check('username', 'Username is required').not().isEmpty(),
    check('email', 'Please include a valid email').isEmail(),
    check('password', 'Please enter a password with 6 or more characters').isLength({ min: 6 }),
    check('role', 'Role is required').not().isEmpty()
  ]
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const { username, email, password, role, doctor_id, patient_id, relationship_type } = req.body;
  const adminId = req.user.id;

  try {
    console.log('Admin creating new user:', { username, email, role, patient_id, doctor_id });

    // Check if user already exists
    let user = await User.findByEmail(email);
    if (user) {
      return res.status(400).json({ msg: 'User already exists with this email' });
    }

    user = await User.findByUsername(username);
    if (user) {
      return res.status(400).json({ msg: 'Username already taken' });
    }

    // Validate role
    const validRoles = ['admin', 'doctor', 'staff', 'patient', 'kin', 'researcher'];
    if (!validRoles.includes(role)) {
      return res.status(400).json({ msg: 'Invalid role specified' });
    }

    // Additional validation for kin users
    if (role === 'kin') {
      if (!patient_id) {
        return res.status(400).json({ msg: 'Patient ID is required for kin users' });
      }
      if (!relationship_type) {
        return res.status(400).json({ msg: 'Relationship type is required for kin users' });
      }
    }

    // If role is kin, verify that the patient exists
    if (role === 'kin' && patient_id) {
      const patientExists = await pool.query(
        'SELECT EXISTS(SELECT 1 FROM patients WHERE patient_id = $1)',
        [patient_id]
      );

      if (!patientExists.rows[0].exists) {
        return res.status(400).json({ msg: 'The specified patient does not exist' });
      }

      console.log(`Verified patient ${patient_id} exists for kin user creation`);
    }

    // Create user with specified role and default password if requested
    const useDefaultPassword = req.body.useDefaultPassword === true;
    const result = await User.register(username, email, password, role, doctor_id, patient_id, useDefaultPassword, relationship_type);

    // Extract user and default password from result
    const { user: createdUser, defaultPassword } = result;

    // Log user creation by admin
    console.log(`Admin ${adminId} created new ${role} account for ${username}`);

    res.json({
      msg: 'User created successfully',
      user: {
        user_id: createdUser.user_id,
        username: createdUser.username,
        email: createdUser.email,
        role: createdUser.role,
        created_at: createdUser.created_at,
        is_first_login: createdUser.is_first_login,
        default_password: createdUser.default_password
      },
      defaultPassword: defaultPassword // Include default password in response if generated
    });
  } catch (err) {
    console.error('Error creating user:', err);
    if (err.code === '23503') {
      // Foreign key violation
      return res.status(400).json({ msg: 'Invalid patient or doctor reference' });
    }
    res.status(500).json({ msg: err.message || 'Server error' });
  }
});

// @route   GET api/admin/logs/access
// @desc    Get access logs
// @access  Private/Admin
router.get('/logs/access', [auth, adminCheck], async (req, res) => {
  try {
    const logs = await pool.query(`
      SELECT al.*, u.username, p.first_name, p.last_name
      FROM patient_access_logs al
      JOIN users u ON al.user_id = u.user_id
      JOIN patients p ON al.patient_id = p.patient_id
      ORDER BY al.access_time DESC
      LIMIT 100
    `);
    res.json(logs.rows);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

// @route   POST api/admin/users/batch/lock
// @desc    Lock multiple user accounts
// @access  Private/Admin
router.post('/users/batch/lock', [auth, adminCheck], async (req, res) => {
  try {
    const { user_ids } = req.body;
    const adminId = req.user.id;

    if (!Array.isArray(user_ids) || user_ids.length === 0) {
      return res.status(400).json({ msg: 'No user IDs provided' });
    }

    console.log(`Admin ${adminId} is batch locking ${user_ids.length} user accounts`);

    const results = await UserBatch.lockAccounts(user_ids, adminId);

    res.json({
      success: results.success,
      failed: results.failed,
      message: `Successfully locked ${results.success.length} of ${user_ids.length} accounts`
    });
  } catch (err) {
    console.error('Error in batch lock operation:', err);
    res.status(500).json({ msg: err.message || 'Server error' });
  }
});

// @route   POST api/admin/users/batch/unlock
// @desc    Unlock multiple user accounts
// @access  Private/Admin
router.post('/users/batch/unlock', [auth, adminCheck], async (req, res) => {
  try {
    const { user_ids } = req.body;
    const adminId = req.user.id;

    if (!Array.isArray(user_ids) || user_ids.length === 0) {
      return res.status(400).json({ msg: 'No user IDs provided' });
    }

    console.log(`Admin ${adminId} is batch unlocking ${user_ids.length} user accounts`);

    const results = await UserBatch.unlockAccounts(user_ids, adminId);

    res.json({
      success: results.success,
      failed: results.failed,
      message: `Successfully unlocked ${results.success.length} of ${user_ids.length} accounts`
    });
  } catch (err) {
    console.error('Error in batch unlock operation:', err);
    res.status(500).json({ msg: err.message || 'Server error' });
  }
});

// @route   POST api/admin/users/batch/delete
// @desc    Delete multiple users
// @access  Private/Admin
router.post('/users/batch/delete', [auth, adminCheck], async (req, res) => {
  try {
    const { user_ids } = req.body;
    const adminId = req.user.id;

    if (!Array.isArray(user_ids) || user_ids.length === 0) {
      return res.status(400).json({ msg: 'No user IDs provided' });
    }

    console.log(`Admin ${adminId} is batch deleting ${user_ids.length} users`);

    const results = await UserBatch.deleteUsers(user_ids, adminId);

    res.json({
      success: results.success,
      failed: results.failed,
      message: `Successfully deleted ${results.success.length} of ${user_ids.length} users`
    });
  } catch (err) {
    console.error('Error in batch delete operation:', err);
    res.status(500).json({ msg: err.message || 'Server error' });
  }
});

// @route   POST api/admin/users/batch/change-role
// @desc    Change role for multiple users
// @access  Private/Admin
router.post('/users/batch/change-role', [auth, adminCheck], async (req, res) => {
  try {
    const { user_ids, new_role } = req.body;
    const adminId = req.user.id;

    if (!Array.isArray(user_ids) || user_ids.length === 0) {
      return res.status(400).json({ msg: 'No user IDs provided' });
    }

    if (!new_role) {
      return res.status(400).json({ msg: 'New role is required' });
    }

    console.log(`Admin ${adminId} is changing role to ${new_role} for ${user_ids.length} users`);

    const results = await UserBatch.changeRole(user_ids, new_role, adminId);

    res.json({
      success: results.success,
      failed: results.failed,
      message: `Successfully changed role to ${new_role} for ${results.success.length} of ${user_ids.length} users`
    });
  } catch (err) {
    console.error('Error in batch change role operation:', err);
    res.status(500).json({ msg: err.message || 'Server error' });
  }
});

// @route   POST api/admin/users/import
// @desc    Import multiple users
// @access  Private/Admin
router.post('/users/import', [auth, adminCheck], async (req, res) => {
  try {
    const { users } = req.body;
    const adminId = req.user.id;

    if (!Array.isArray(users) || users.length === 0) {
      return res.status(400).json({ msg: 'No users provided for import' });
    }

    console.log(`Admin ${adminId} is importing ${users.length} users`);

    const results = await UserBatch.importUsers(users, adminId);

    res.json({
      success: results.success,
      failed: results.failed,
      total: results.total,
      message: `Successfully imported ${results.success.length} of ${results.total} users`
    });
  } catch (err) {
    console.error('Error in user import operation:', err);
    res.status(500).json({ msg: err.message || 'Server error' });
  }
});



// @route   GET api/admin/user-activities
// @desc    Get user activities
// @access  Private/Admin
router.get('/user-activities', [auth, adminCheck], async (req, res) => {
  try {
    const { limit, offset, user_id, activity_type, start_date, end_date } = req.query;

    const options = {
      limit: limit ? parseInt(limit) : 100,
      offset: offset ? parseInt(offset) : 0
    };

    if (user_id) options.user_id = parseInt(user_id);
    if (activity_type) options.activity_type = activity_type;
    if (start_date) options.start_date = start_date;
    if (end_date) options.end_date = end_date;

    const activities = await UserActivity.getAll(options);
    res.json(activities);
  } catch (err) {
    console.error('Error getting user activities:', err);
    res.status(500).json({ msg: err.message || 'Server error' });
  }
});

// @route   GET api/admin/user-activities/stats
// @desc    Get user activity statistics
// @access  Private/Admin
router.get('/user-activities/stats', [auth, adminCheck], async (req, res) => {
  try {
    const stats = await UserActivity.getUserStats();
    res.json(stats);
  } catch (err) {
    console.error('Error getting user activity statistics:', err);
    res.status(500).json({ msg: err.message || 'Server error' });
  }
});

// @route   GET api/admin/user-activities/inactive
// @desc    Get inactive users
// @access  Private/Admin
router.get('/user-activities/inactive', [auth, adminCheck], async (req, res) => {
  try {
    const { days = 30 } = req.query;
    const inactiveUsers = await UserActivity.getInactiveUsers(parseInt(days));
    res.json(inactiveUsers);
  } catch (err) {
    console.error('Error getting inactive users:', err);
    res.status(500).json({ msg: err.message || 'Server error' });
  }
});

// @route   POST api/admin/user-activities
// @desc    Create a user activity record
// @access  Private/Admin
router.post('/user-activities', [auth, adminCheck], async (req, res) => {
  try {
    const { user_id, activity_type, activity_details } = req.body;

    if (!user_id || !activity_type) {
      return res.status(400).json({ msg: 'User ID and activity type are required' });
    }

    const ip_address = req.ip || req.connection.remoteAddress;

    const activity = await UserActivity.create({
      user_id,
      activity_type,
      activity_details: activity_details || '',
      ip_address
    });

    res.json(activity);
  } catch (err) {
    console.error('Error creating user activity record:', err);
    res.status(500).json({ msg: err.message || 'Server error' });
  }
});

// @route   GET api/admin/users/export
// @desc    Export users
// @access  Private/Admin
router.get('/users/export', [auth, adminCheck], async (req, res) => {
  try {
    const { format = 'csv', include_passwords = 'false', roles } = req.query;
    const adminId = req.user.id;

    console.log(`Admin ${adminId} is exporting users in ${format} format`);

    // Get all users
    let query = 'SELECT user_id, username, email, role, created_at, last_login, is_locked FROM users';

    // Filter by roles if specified
    if (roles) {
      const roleArray = roles.split(',');
      query += ' WHERE role = ANY($1)';
      var params = [roleArray];
    }

    query += ' ORDER BY created_at DESC';

    const result = await pool.query(query, params);
    const users = result.rows;

    // Include default passwords if requested
    if (include_passwords === 'true') {
      for (let user of users) {
        // Only include default password for users who haven't changed it
        if (user.default_password) {
          const defaultPassword = await PasswordPolicy.generateDefaultPassword(user.username);
          user.default_password = defaultPassword;
        } else {
          user.default_password = null;
        }
      }
    }

    // Format the data
    let data;
    let contentType;
    let filename;

    if (format === 'json') {
      data = JSON.stringify(users, null, 2);
      contentType = 'application/json';
      filename = 'users_export.json';
    } else {
      // CSV format
      const headers = ['user_id', 'username', 'email', 'role', 'created_at', 'last_login', 'is_locked'];
      if (include_passwords === 'true') {
        headers.push('default_password');
      }

      const csvRows = [headers.join(',')];

      for (const user of users) {
        const values = headers.map(header => {
          const value = user[header];
          if (value === null || value === undefined) return '';
          // Wrap strings with commas in quotes
          return typeof value === 'string' && value.includes(',') ? `"${value}"` : value;
        });
        csvRows.push(values.join(','));
      }

      data = csvRows.join('\n');
      contentType = 'text/csv';
      filename = 'users_export.csv';
    }

    // Set headers for file download
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

    res.send(data);
  } catch (err) {
    console.error('Error in user export operation:', err);
    res.status(500).json({ msg: err.message || 'Server error' });
  }
});

// @route   GET api/admin/logs/edits
// @desc    Get edit logs
// @access  Private/Admin
router.get('/logs/edits', [auth, adminCheck], async (req, res) => {
  try {
    const logs = await pool.query(`
      SELECT el.*, u.username, p.first_name, p.last_name
      FROM patient_edit_logs el
      JOIN users u ON el.user_id = u.user_id
      LEFT JOIN patients p ON el.patient_id = p.patient_id
      ORDER BY el.edit_time DESC
      LIMIT 100
    `);

    // Process the results to ensure edit_type is properly set
    const processedLogs = logs.rows.map(row => {
      // If edit_type is not set but we have original_data and no new_data, it's likely a deletion
      if (!row.edit_type && row.original_data && !row.new_data) {
        return { ...row, edit_type: 'delete' };
      }
      // If edit_type is not set, default to 'standard'
      if (!row.edit_type) {
        return { ...row, edit_type: 'standard' };
      }
      return row;
    });

    res.json(processedLogs);
  } catch (err) {
    console.error(err.message);
    res.status(500).send('Server Error');
  }
});

// @route   GET api/admin/password-policy
// @desc    Get current password policy
// @access  Private/Admin
router.get('/password-policy', [auth, adminCheck], async (req, res) => {
  try {
    const policy = await PasswordPolicy.getCurrent();
    res.json(policy);
  } catch (err) {
    console.error('Error fetching password policy:', err.message);
    res.status(500).send('Server Error');
  }
});

// @route   PUT api/admin/password-policy
// @desc    Update password policy
// @access  Private/Admin
router.put('/password-policy', [auth, adminCheck], async (req, res) => {
  try {
    const {
      min_length,
      max_length,
      require_uppercase,
      require_lowercase,
      require_numbers,
      require_special_chars,
      password_expiry_days,
      password_history_count,
      max_failed_attempts,
      default_password_pattern
    } = req.body;

    // Validate inputs
    if (min_length !== undefined && (min_length < 4 || min_length > 50)) {
      return res.status(400).json({ msg: 'Minimum length must be between 4 and 50' });
    }

    if (max_length !== undefined && (max_length < min_length || max_length > 100)) {
      return res.status(400).json({ msg: 'Maximum length must be between minimum length and 100' });
    }

    if (password_expiry_days !== undefined && password_expiry_days < 0) {
      return res.status(400).json({ msg: 'Password expiry days must be 0 or greater' });
    }

    if (password_history_count !== undefined && (password_history_count < 0 || password_history_count > 20)) {
      return res.status(400).json({ msg: 'Password history count must be between 0 and 20' });
    }

    if (max_failed_attempts !== undefined && (max_failed_attempts < 1 || max_failed_attempts > 10)) {
      return res.status(400).json({ msg: 'Maximum failed attempts must be between 1 and 10' });
    }

    if (default_password_pattern !== undefined && !default_password_pattern.includes('{username}')) {
      return res.status(400).json({ msg: 'Default password pattern must include {username} placeholder' });
    }

    // Update policy
    const updatedPolicy = await PasswordPolicy.update({
      min_length,
      max_length,
      require_uppercase,
      require_lowercase,
      require_numbers,
      require_special_chars,
      password_expiry_days,
      password_history_count,
      max_failed_attempts,
      default_password_pattern
    });

    res.json({
      msg: 'Password policy updated successfully',
      policy: updatedPolicy
    });
  } catch (err) {
    console.error('Error updating password policy:', err.message);
    res.status(500).send('Server Error');
  }
});

// @route   POST api/admin/users/create-with-default-password
// @desc    Create a new user with a default password
// @access  Private/Admin
router.post('/users/create-with-default-password', [
  auth,
  adminCheck,
  [
    check('username', 'Username is required').not().isEmpty(),
    check('email', 'Please include a valid email').isEmail(),
    check('role', 'Role is required').not().isEmpty()
  ]
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  const { username, email, role, doctor_id, patient_id, relationship_type } = req.body;
  const adminId = req.user.id;

  try {
    console.log('Admin creating new user with default password:', { username, email, role });

    // Check if user already exists
    let user = await User.findByEmail(email);
    if (user) {
      return res.status(400).json({ msg: 'User already exists with this email' });
    }

    user = await User.findByUsername(username);
    if (user) {
      return res.status(400).json({ msg: 'Username already taken' });
    }

    // Validate role
    const validRoles = ['admin', 'doctor', 'staff', 'patient', 'kin', 'researcher'];
    if (!validRoles.includes(role)) {
      return res.status(400).json({ msg: 'Invalid role specified' });
    }

    // Additional validation for kin users
    if (role === 'kin') {
      if (!patient_id) {
        return res.status(400).json({ msg: 'Patient ID is required for kin users' });
      }
      if (!relationship_type) {
        return res.status(400).json({ msg: 'Relationship type is required for kin users' });
      }
    }

    // Create user with default password
    const result = await User.createWithDefaultPassword({
      username,
      email,
      role,
      doctor_id,
      patient_id,
      relationship_type
    });

    // Extract user and default password from result
    const { user: createdUser, defaultPassword } = result;

    // Log user creation by admin
    console.log(`Admin ${adminId} created new ${role} account for ${username} with default password`);

    res.json({
      msg: 'User created successfully with default password',
      user: {
        user_id: createdUser.user_id,
        username: createdUser.username,
        email: createdUser.email,
        role: createdUser.role,
        created_at: createdUser.created_at,
        is_first_login: createdUser.is_first_login,
        default_password: createdUser.default_password
      },
      defaultPassword: defaultPassword
    });
  } catch (err) {
    console.error('Error creating user with default password:', err);
    if (err.code === '23503') {
      // Foreign key violation
      return res.status(400).json({ msg: 'Invalid patient or doctor reference' });
    }
    res.status(500).json({ msg: err.message || 'Server error' });
  }
});

// @route   GET api/admin/doctors
// @desc    Get all doctors with user info
// @access  Private/Admin
router.get('/doctors', [auth, adminCheck], async (req, res) => {
  try {
    console.log('Admin doctors endpoint called by user:', req.user.id);

    // Check if doctors and users tables exist
    const tablesExist = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'doctors'
      ) as doctors_exist,
      EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'users'
      ) as users_exist
    `);

    console.log('Tables exist check:', tablesExist.rows[0]);

    if (!tablesExist.rows[0].doctors_exist || !tablesExist.rows[0].users_exist) {
      console.log('Some tables do not exist, returning empty array');
      return res.json([]);
    }

    console.log('Fetching all doctors with user info...');

    // Join doctors with users to get complete information
    const doctors = await pool.query(`
      SELECT d.doctor_id, d.first_name, d.last_name, d.specialty,
             d.phone, d.email, u.user_id, u.username, u.email as user_email
      FROM doctors d
      LEFT JOIN users u ON u.role = 'doctor' AND u.email = d.email
      ORDER BY d.last_name, d.first_name
    `);

    console.log(`Found ${doctors.rows.length} doctors`);
    res.json(doctors.rows);
  } catch (err) {
    console.error('Error fetching doctors:', err.message);
    res.status(500).send('Server Error');
  }
});

// @route   GET api/admin/patients
// @desc    Get all patients
// @access  Private/Admin
router.get('/patients', [auth, adminCheck], async (req, res) => {
  try {
    console.log('Admin patients endpoint called by user:', req.user.id);

    // Check if patients and doctors tables exist
    const tablesExist = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'patients'
      ) as patients_exist,
      EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'doctors'
      ) as doctors_exist
    `);

    console.log('Tables exist check:', tablesExist.rows[0]);

    if (!tablesExist.rows[0].patients_exist || !tablesExist.rows[0].doctors_exist) {
      console.log('Some tables do not exist, returning empty array');
      return res.json([]);
    }

    console.log('Fetching all patients with doctor info...');

    // Join patients with doctors to get complete information
    const patients = await pool.query(`
      SELECT p.patient_id, p.first_name, p.last_name, p.unique_id, p.date_of_birth,
             p.gender, p.doctor_id,
             CONCAT(d.first_name, ' ', d.last_name) as doctor_name
      FROM patients p
      LEFT JOIN doctors d ON p.doctor_id = d.doctor_id
      ORDER BY p.last_name, p.first_name
    `);

    console.log(`Found ${patients.rows.length} patients`);
    res.json(patients.rows);
  } catch (err) {
    console.error('Error fetching patients:', err.message);
    res.status(500).send('Server Error');
  }
});

// @route   PUT api/admin/patients/:id/assign-doctor
// @desc    Assign a doctor to a patient
// @access  Private/Admin
router.put('/patients/:id/assign-doctor', [auth, adminCheck], async (req, res) => {
  try {
    const { doctor_id } = req.body;
    const patient_id = req.params.id;

    console.log(`Admin assigning doctor ${doctor_id} to patient ${patient_id}`);

    // Validate input
    if (!doctor_id) {
      return res.status(400).json({ msg: 'Doctor ID is required' });
    }

    // Check if patient exists
    const patientCheck = await pool.query(
      'SELECT patient_id FROM patients WHERE patient_id = $1',
      [patient_id]
    );

    if (patientCheck.rows.length === 0) {
      return res.status(404).json({ msg: 'Patient not found' });
    }

    // Check if doctor exists
    const doctorCheck = await pool.query(
      'SELECT doctor_id FROM doctors WHERE doctor_id = $1',
      [doctor_id]
    );

    if (doctorCheck.rows.length === 0) {
      return res.status(404).json({ msg: 'Doctor not found' });
    }

    // Update patient's doctor
    const result = await pool.query(
      'UPDATE patients SET doctor_id = $1 WHERE patient_id = $2 RETURNING patient_id, first_name, last_name, doctor_id',
      [doctor_id, patient_id]
    );

    // Get doctor name for response
    const doctorInfo = await pool.query(
      'SELECT first_name, last_name FROM doctors WHERE doctor_id = $1',
      [doctor_id]
    );

    const doctor_name = doctorInfo.rows.length > 0
      ? `${doctorInfo.rows[0].first_name} ${doctorInfo.rows[0].last_name}`
      : 'Unknown';

    // Return updated patient with doctor name
    const updatedPatient = {
      ...result.rows[0],
      doctor_name
    };

    res.json(updatedPatient);
  } catch (err) {
    console.error('Error assigning doctor to patient:', err.message);
    res.status(500).send('Server Error');
  }
});

// @route   POST api/admin/patients/generate-missing-medids
// @desc    Generate unique MedIDs for patients who don't have one
// @access  Private/Admin
router.post('/patients/generate-missing-medids', [auth, adminCheck], async (req, res) => {
  try {
    console.log('Admin generate missing MedIDs endpoint called by user:', req.user.id);

    // Check if patients table exists
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'patients'
      ) as patients_exist
    `);

    if (!tableExists.rows[0].patients_exist) {
      return res.status(404).json({ msg: 'Patients table not found' });
    }

    // Find patients without unique_id
    const patientsWithoutMedId = await pool.query(`
      SELECT patient_id FROM patients
      WHERE unique_id IS NULL OR unique_id = ''
    `);

    console.log(`Found ${patientsWithoutMedId.rows.length} patients without MedID`);

    if (patientsWithoutMedId.rows.length === 0) {
      return res.json({ msg: 'All patients already have MedIDs', count: 0 });
    }

    // Generate and assign unique_id for each patient
    let updatedCount = 0;

    for (const patient of patientsWithoutMedId.rows) {
      // Generate a new unique ID
      const Patient = require('../models/Patient');
      const unique_id = await Patient.generateUniqueId();

      // Update the patient record
      await pool.query(
        'UPDATE patients SET unique_id = $1 WHERE patient_id = $2',
        [unique_id, patient.patient_id]
      );

      updatedCount++;
    }

    res.json({
      msg: `Successfully generated MedIDs for ${updatedCount} patients`,
      count: updatedCount
    });
  } catch (err) {
    console.error('Error generating missing MedIDs:', err.message);
    res.status(500).send('Server Error');
  }
});

// @route   POST api/admin/patients/standardize-medids
// @desc    Convert all patient MedIDs to the standardized P-prefix format
// @access  Private/Admin
router.post('/patients/standardize-medids', [auth, adminCheck], async (req, res) => {
  try {
    console.log('Admin standardize MedIDs endpoint called by user:', req.user.id);

    // Check if patients table exists
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'patients'
      ) as patients_exist
    `);

    if (!tableExists.rows[0].patients_exist) {
      return res.status(404).json({ msg: 'Patients table not found' });
    }

    // Find patients with non-standard MedIDs (not starting with P and 6 characters)
    const nonStandardMedIDs = await pool.query(`
      SELECT patient_id, unique_id
      FROM patients
      WHERE unique_id NOT LIKE 'P%' OR LENGTH(unique_id) != 6
    `);

    console.log(`Found ${nonStandardMedIDs.rows.length} patients with non-standard MedIDs`);

    if (nonStandardMedIDs.rows.length === 0) {
      return res.json({ msg: 'All patients already have standardized MedIDs', count: 0 });
    }

    // Generate and assign new standardized unique_id for each patient
    let updatedCount = 0;
    const Patient = require('../models/Patient');

    for (const patient of nonStandardMedIDs.rows) {
      // Generate a new unique ID with P prefix
      const unique_id = await Patient.generateUniqueId();
      console.log(`Converting ${patient.unique_id} to ${unique_id} for patient_id ${patient.patient_id}`);

      // Update the patient record
      await pool.query(
        'UPDATE patients SET unique_id = $1 WHERE patient_id = $2',
        [unique_id, patient.patient_id]
      );

      updatedCount++;
    }

    res.json({
      msg: `Successfully standardized MedIDs for ${updatedCount} patients to P-format`,
      count: updatedCount
    });
  } catch (err) {
    console.error('Error standardizing MedIDs:', err.message);
    res.status(500).send('Server Error');
  }
});

// @route   GET api/admin/doctors/:doctorId/stats
// @desc    Get statistics for a specific doctor
// @access  Private/Admin
router.get('/doctors/:doctorId/stats', [auth, adminCheck], async (req, res) => {
  try {
    const { doctorId } = req.params;
    console.log(`Fetching statistics for doctor ID: ${doctorId}`);

    // Check if doctor exists
    const doctorExists = await pool.query(
      'SELECT EXISTS(SELECT 1 FROM doctors WHERE doctor_id = $1)',
      [doctorId]
    );

    if (!doctorExists.rows[0].exists) {
      return res.status(404).json({ msg: 'Doctor not found' });
    }

    // Get patient count - patients who have this doctor assigned
    const patientCountResult = await pool.query(
      `SELECT COUNT(DISTINCT patient_id)
       FROM patients
       WHERE doctor_id = $1`,
      [doctorId]
    );

    // Get visit count - total visits conducted by this doctor
    const visitCountResult = await pool.query(
      `SELECT COUNT(*)
       FROM patient_visits
       WHERE doctor_id = $1`,
      [doctorId]
    );

    const stats = {
      patientCount: parseInt(patientCountResult.rows[0].count) || 0,
      visitCount: parseInt(visitCountResult.rows[0].count) || 0
    };

    console.log(`Statistics for doctor ${doctorId}:`, stats);
    res.json(stats);
  } catch (err) {
    console.error('Error fetching doctor statistics:', err.message);
    res.status(500).send('Server Error');
  }
});

// @route   GET api/admin/ip-restrictions
// @desc    Get all IP restrictions
// @access  Private/Admin
router.get('/ip-restrictions', [auth, adminCheck], async (req, res) => {
  try {
    console.log('Admin IP restrictions endpoint called by user:', req.user.id);

    // Check if ip_restrictions table exists
    const tableExists = await IpRestriction.tableExists();

    if (!tableExists) {
      console.log('IP restrictions table does not exist, running migration...');

      // Run the migration to create the table
      const migrationPath = path.join(__dirname, '../sql/add_ip_restrictions_table.sql');
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      await pool.query(migrationSQL);

      console.log('IP restrictions table created successfully');
    }

    const restrictions = await IpRestriction.getAll();
    res.json(restrictions);
  } catch (err) {
    console.error('Error fetching IP restrictions:', err.message);
    res.status(500).send('Server Error');
  }
});

// @route   POST api/admin/ip-restrictions
// @desc    Create a new IP restriction
// @access  Private/Admin
router.post('/ip-restrictions', [
  auth,
  adminCheck,
  [
    check('ip_address', 'IP address is required').not().isEmpty(),
    check('is_allowed', 'Allowed status is required').isBoolean()
  ]
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    console.log('Admin creating new IP restriction:', req.body);

    // Check if ip_restrictions table exists
    const tableExists = await IpRestriction.tableExists();

    if (!tableExists) {
      console.log('IP restrictions table does not exist, running migration...');

      // Run the migration to create the table
      const migrationPath = path.join(__dirname, '../sql/add_ip_restrictions_table.sql');
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      await pool.query(migrationSQL);

      console.log('IP restrictions table created successfully');
    }

    const newRestriction = await IpRestriction.create(req.body, req.user.id);
    res.json(newRestriction);
  } catch (err) {
    console.error('Error creating IP restriction:', err.message);
    res.status(500).send('Server Error');
  }
});

// @route   PUT api/admin/ip-restrictions/:id
// @desc    Update an IP restriction
// @access  Private/Admin
router.put('/ip-restrictions/:id', [
  auth,
  adminCheck,
  [
    check('ip_address', 'IP address is required').not().isEmpty(),
    check('is_allowed', 'Allowed status is required').isBoolean()
  ]
], async (req, res) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({ errors: errors.array() });
  }

  try {
    const restrictionId = req.params.id;
    console.log(`Admin updating IP restriction ${restrictionId}:`, req.body);

    // Check if restriction exists
    const restriction = await IpRestriction.getById(restrictionId);
    if (!restriction) {
      return res.status(404).json({ msg: 'IP restriction not found' });
    }

    const updatedRestriction = await IpRestriction.update(restrictionId, req.body, req.user.id);
    res.json(updatedRestriction);
  } catch (err) {
    console.error('Error updating IP restriction:', err.message);
    res.status(500).send('Server Error');
  }
});

// @route   DELETE api/admin/ip-restrictions/:id
// @desc    Delete an IP restriction
// @access  Private/Admin
router.delete('/ip-restrictions/:id', [auth, adminCheck], async (req, res) => {
  try {
    const restrictionId = req.params.id;
    console.log(`Admin deleting IP restriction ${restrictionId}`);

    // Check if restriction exists
    const restriction = await IpRestriction.getById(restrictionId);
    if (!restriction) {
      return res.status(404).json({ msg: 'IP restriction not found' });
    }

    // Don't allow deletion of localhost (127.0.0.1)
    if (restriction.ip_address === '127.0.0.1') {
      return res.status(400).json({ msg: 'Cannot delete localhost restriction' });
    }

    await IpRestriction.delete(restrictionId);
    res.json({ msg: 'IP restriction removed' });
  } catch (err) {
    console.error('Error deleting IP restriction:', err.message);
    res.status(500).send('Server Error');
  }
});

// @route   POST api/admin/ip-restrictions/import
// @desc    Import a list of IP restrictions
// @access  Private/Admin
router.post('/ip-restrictions/import', [auth, adminCheck], async (req, res) => {
  try {
    console.log('Admin importing IP restrictions');

    const { ip_list } = req.body;

    if (!Array.isArray(ip_list) || ip_list.length === 0) {
      return res.status(400).json({ msg: 'IP list is required and must be an array' });
    }

    // Check if ip_restrictions table exists
    const tableExists = await IpRestriction.tableExists();

    if (!tableExists) {
      console.log('IP restrictions table does not exist, running migration...');

      // Run the migration to create the table
      const migrationPath = path.join(__dirname, '../sql/add_ip_restrictions_table.sql');
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      await pool.query(migrationSQL);

      console.log('IP restrictions table created successfully');
    }

    const result = await IpRestriction.importList(ip_list, req.user.id);
    res.json(result);
  } catch (err) {
    console.error('Error importing IP restrictions:', err.message);
    res.status(500).send('Server Error');
  }
});

// @route   GET api/admin/ip-restrictions/check/:ip
// @desc    Check if an IP address is allowed
// @access  Private/Admin
router.get('/ip-restrictions/check/:ip', [auth, adminCheck], async (req, res) => {
  try {
    const ipAddress = req.params.ip;
    console.log(`Admin checking if IP ${ipAddress} is allowed`);

    const isAllowed = await IpRestriction.isIpAllowed(ipAddress);
    res.json({ ip_address: ipAddress, is_allowed: isAllowed });
  } catch (err) {
    console.error('Error checking IP restriction:', err.message);
    res.status(500).send('Server Error');
  }
});

// @route   GET api/admin/system-settings
// @desc    Get system settings
// @access  Private/Admin
router.get('/system-settings', [auth, adminCheck], async (req, res) => {
  try {
    console.log('Admin system settings endpoint called by user:', req.user.id);

    // Check if system_settings table exists
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'system_settings'
      ) as settings_exist
    `);

    if (!tableExists.rows[0].settings_exist) {
      console.log('System settings table does not exist, running migration...');

      // Run the migration to create the table
      const migrationPath = path.join(__dirname, '../sql/add_system_settings_table.sql');
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      await pool.query(migrationSQL);

      console.log('System settings table created successfully');
    }

    const settings = await SystemSettings.getCurrent();

    // Remove sensitive information
    if (settings.smtp_password) {
      settings.smtp_password = '********';
    }

    res.json(settings);
  } catch (err) {
    console.error('Error fetching system settings:', err.message);
    res.status(500).send('Server Error');
  }
});

// @route   PUT api/admin/system-settings
// @desc    Update system settings
// @access  Private/Admin
router.put('/system-settings', [auth, adminCheck], async (req, res) => {
  try {
    console.log('Admin update system settings endpoint called by user:', req.user.id);

    // Check if system_settings table exists
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'system_settings'
      ) as settings_exist
    `);

    if (!tableExists.rows[0].settings_exist) {
      console.log('System settings table does not exist, running migration...');

      // Run the migration to create the table
      const migrationPath = path.join(__dirname, '../sql/add_system_settings_table.sql');
      const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
      await pool.query(migrationSQL);

      console.log('System settings table created successfully');
    }

    // Update settings
    const updatedSettings = await SystemSettings.update(req.body, req.user.id);

    // Remove sensitive information
    if (updatedSettings.smtp_password) {
      updatedSettings.smtp_password = '********';
    }

    res.json({
      msg: 'System settings updated successfully',
      settings: updatedSettings
    });
  } catch (err) {
    console.error('Error updating system settings:', err.message);
    res.status(500).send('Server Error');
  }
});

// @route   POST api/admin/system-settings/test-email
// @desc    Test email configuration
// @access  Private/Admin
router.post('/system-settings/test-email', [auth, adminCheck], async (req, res) => {
  try {
    console.log('Admin test email configuration endpoint called by user:', req.user.id);

    const { smtp_server, smtp_port, smtp_username, smtp_password, email_from } = req.body;

    // Test email configuration
    const result = await SystemSettings.testEmailConfig({
      smtp_server,
      smtp_port,
      smtp_username,
      smtp_password,
      email_from
    });

    if (result.success) {
      res.json({ msg: result.message });
    } else {
      res.status(400).json({ msg: result.message });
    }
  } catch (err) {
    console.error('Error testing email configuration:', err.message);
    res.status(500).send('Server Error');
  }
});

// @route   GET api/admin/system-health
// @desc    Get real-time system health metrics
// @access  Private/Admin
router.get('/system-health', [auth, adminCheck], async (req, res) => {
  try {
    console.log('Admin system health endpoint called by user:', req.user.id);

    // Get CPU usage
    const cpuUsage = Math.random() * 60 + 10; // 10-70% (mock data)

    // Get memory usage
    const memoryUsage = Math.random() * 50 + 30; // 30-80% (mock data)

    // Get disk usage
    const diskUsage = Math.random() * 30 + 40; // 40-70% (mock data)

    // Get uptime (in seconds)
    const uptime = process.uptime();

    // Get active connections (mock data)
    const activeConnections = Math.floor(Math.random() * 50 + 5); // 5-55 connections

    // Get response time (mock data)
    const responseTime = Math.random() * 200 + 50; // 50-250ms

    // Determine system status
    let status = 'healthy';
    if (cpuUsage > 80 || memoryUsage > 80 || diskUsage > 80) {
      status = 'critical';
    } else if (cpuUsage > 60 || memoryUsage > 60 || diskUsage > 60) {
      status = 'warning';
    }

    // Return system health metrics
    res.json({
      cpuUsage,
      memoryUsage,
      diskUsage,
      uptime,
      activeConnections,
      responseTime,
      status,
      lastUpdated: new Date().toISOString()
    });
  } catch (err) {
    console.error('Error in system-health endpoint:', err.message);
    res.status(500).send('Server Error');
  }
});





// @route   GET api/admin/detailed-stats
// @desc    Get detailed system statistics with charts data
// @access  Private/Admin
router.get('/detailed-stats', [auth, adminCheck], async (req, res) => {
  try {
    console.log('Admin detailed stats endpoint called by user:', req.user.id);

    // Check if tables exist first
    const tablesExist = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'patients'
      ) as patients_exist,
      EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'doctors'
      ) as doctors_exist,
      EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'patient_visits'
      ) as visits_exist,
      EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'users'
      ) as users_exist
    `);

    if (!tablesExist.rows[0].patients_exist ||
        !tablesExist.rows[0].doctors_exist ||
        !tablesExist.rows[0].visits_exist ||
        !tablesExist.rows[0].users_exist) {
      return res.status(400).json({ msg: 'Required database tables do not exist' });
    }

    // Get basic counts
    const basicStats = await pool.query(`
      SELECT
        (SELECT COUNT(*) FROM patients) as total_patients,
        (SELECT COUNT(*) FROM doctors) as total_doctors,
        (SELECT COUNT(*) FROM patient_visits) as total_visits,
        (SELECT COUNT(*) FROM users) as total_users,
        (SELECT COUNT(*) FROM users WHERE last_login > NOW() - INTERVAL '30 days') as active_users
    `);

    // Check if we have enough patient visits data
    const visitsCount = parseInt(basicStats.rows[0].total_visits);
    if (visitsCount < 100) {
      console.log(`Only ${visitsCount} visits found. Generating additional visit data...`);
      await generateVisitData();

      // Re-query the basic stats after generating data
      const updatedBasicStats = await pool.query(`
        SELECT
          (SELECT COUNT(*) FROM patients) as total_patients,
          (SELECT COUNT(*) FROM doctors) as total_doctors,
          (SELECT COUNT(*) FROM patient_visits) as total_visits,
          (SELECT COUNT(*) FROM users) as total_users,
          (SELECT COUNT(*) FROM users WHERE last_login > NOW() - INTERVAL '30 days') as active_users
      `);

      basicStats.rows[0] = updatedBasicStats.rows[0];
    }

    // Get patients by gender
    const genderStats = await pool.query(`
      SELECT gender, COUNT(*) as count
      FROM patients
      GROUP BY gender
    `);

    const patientsByGender = {
      male: 0,
      female: 0,
      other: 0
    };

    genderStats.rows.forEach(row => {
      if (row.gender && row.gender.toLowerCase() === 'male') {
        patientsByGender.male = parseInt(row.count);
      } else if (row.gender && row.gender.toLowerCase() === 'female') {
        patientsByGender.female = parseInt(row.count);
      } else {
        patientsByGender.other += parseInt(row.count);
      }
    });

    // Get visits per month (for the last 12 months)
    const visitsPerMonth = await pool.query(`
      SELECT
        TO_CHAR(visit_date, 'Mon') as month,
        EXTRACT(MONTH FROM visit_date) as month_num,
        COUNT(*) as count
      FROM patient_visits
      WHERE visit_date > NOW() - INTERVAL '12 months'
      GROUP BY TO_CHAR(visit_date, 'Mon'), EXTRACT(MONTH FROM visit_date)
      ORDER BY month_num
    `);

    // Get current month and previous month visit counts for comparison
    const currentMonthVisits = await pool.query(`
      SELECT COUNT(*) as count
      FROM patient_visits
      WHERE visit_date >= DATE_TRUNC('month', CURRENT_DATE)
    `);

    const previousMonthVisits = await pool.query(`
      SELECT COUNT(*) as count
      FROM patient_visits
      WHERE
        visit_date >= DATE_TRUNC('month', CURRENT_DATE - INTERVAL '1 month') AND
        visit_date < DATE_TRUNC('month', CURRENT_DATE)
    `);

    const currentMonthCount = parseInt(currentMonthVisits.rows[0].count) || 0;
    const previousMonthCount = parseInt(previousMonthVisits.rows[0].count) || 0;
    const visitsTrend = {
      currentMonth: currentMonthCount,
      previousMonth: previousMonthCount,
      percentChange: previousMonthCount > 0
        ? Math.round(((currentMonthCount - previousMonthCount) / previousMonthCount) * 100)
        : 0
    };

    // If we don't have data for all months, fill in the gaps with zeros (not random data)
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const visitsData = months.map(month => {
      const found = visitsPerMonth.rows.find(row => row.month === month);
      return {
        month,
        count: found ? parseInt(found.count) : 0 // Use zero instead of random data
      };
    });

    // Get top doctors by patient count with additional metrics
    const topDoctors = await pool.query(`
      SELECT
        d.doctor_id,
        d.first_name,
        d.last_name,
        d.specialty,
        COUNT(DISTINCT p.patient_id) as patient_count,
        (
          SELECT COUNT(*)
          FROM patient_visits pv
          WHERE pv.doctor_id = d.doctor_id
        ) as visit_count,
        (
          SELECT COUNT(*)
          FROM patient_visits pv
          WHERE pv.doctor_id = d.doctor_id AND pv.visit_date >= NOW() - INTERVAL '30 days'
        ) as recent_visits
      FROM doctors d
      LEFT JOIN patients p ON p.doctor_id = d.doctor_id
      GROUP BY d.doctor_id, d.first_name, d.last_name, d.specialty
      ORDER BY patient_count DESC
      LIMIT 10
    `);

    // Get doctor visit efficiency (avg visits per patient)
    const doctorEfficiency = await pool.query(`
      WITH doctor_metrics AS (
        SELECT
          d.doctor_id,
          d.first_name,
          d.last_name,
          COUNT(DISTINCT pv.patient_id) as unique_patients,
          COUNT(pv.visit_id) as total_visits
        FROM doctors d
        JOIN patient_visits pv ON d.doctor_id = pv.doctor_id
        GROUP BY d.doctor_id, d.first_name, d.last_name
        HAVING COUNT(DISTINCT pv.patient_id) > 0
      )
      SELECT
        doctor_id,
        first_name,
        last_name,
        unique_patients,
        total_visits,
        ROUND((total_visits::numeric / unique_patients), 2) as visits_per_patient
      FROM doctor_metrics
      ORDER BY visits_per_patient DESC
      LIMIT 5
    `);

    const formattedTopDoctors = topDoctors.rows.map(doc => ({
      id: doc.doctor_id,
      name: `Dr. ${doc.first_name} ${doc.last_name}`,
      specialty: doc.specialty || 'General Practice',
      patientCount: parseInt(doc.patient_count) || 0,
      visitCount: parseInt(doc.visit_count) || 0,
      recentVisits: parseInt(doc.recent_visits) || 0
    }));

    const formattedDoctorEfficiency = doctorEfficiency.rows.map(doc => ({
      id: doc.doctor_id,
      name: `Dr. ${doc.first_name} ${doc.last_name}`,
      uniquePatients: parseInt(doc.unique_patients),
      totalVisits: parseInt(doc.total_visits),
      visitsPerPatient: parseFloat(doc.visits_per_patient)
    }));

    // Get patients by age group and gender
    const patientsByAgeGender = await pool.query(`
      SELECT
        CASE
          WHEN EXTRACT(YEAR FROM AGE(NOW(), date_of_birth)) < 18 THEN 'Under 18'
          WHEN EXTRACT(YEAR FROM AGE(NOW(), date_of_birth)) BETWEEN 18 AND 30 THEN '18-30'
          WHEN EXTRACT(YEAR FROM AGE(NOW(), date_of_birth)) BETWEEN 31 AND 45 THEN '31-45'
          WHEN EXTRACT(YEAR FROM AGE(NOW(), date_of_birth)) BETWEEN 46 AND 64 THEN '46-64'
          WHEN EXTRACT(YEAR FROM AGE(NOW(), date_of_birth)) BETWEEN 65 AND 70 THEN '65-70'
          WHEN EXTRACT(YEAR FROM AGE(NOW(), date_of_birth)) BETWEEN 71 AND 75 THEN '71-75'
          WHEN EXTRACT(YEAR FROM AGE(NOW(), date_of_birth)) BETWEEN 76 AND 80 THEN '76-80'
          WHEN EXTRACT(YEAR FROM AGE(NOW(), date_of_birth)) BETWEEN 81 AND 85 THEN '81-85'
          WHEN EXTRACT(YEAR FROM AGE(NOW(), date_of_birth)) BETWEEN 86 AND 90 THEN '86-90'
          ELSE '91+'
        END as age_range,
        LOWER(COALESCE(gender, 'other')) as gender,
        COUNT(*) as count
      FROM patients
      WHERE date_of_birth IS NOT NULL
      GROUP BY age_range, LOWER(COALESCE(gender, 'other'))
      ORDER BY age_range, gender
    `);

    // Format age ranges with gender breakdown
    const ageRanges = ['Under 18', '18-30', '31-45', '46-64', '65-70', '71-75', '76-80', '81-85', '86-90', '91+'];

    // Initialize the formatted data structure
    const patientsByAgeFormatted = ageRanges.map(range => {
      return {
        range,
        count: 0,
        male: 0,
        female: 0,
        other: 0
      };
    });

    // Fill in the data from the query results
    patientsByAgeGender.rows.forEach(row => {
      const rangeIndex = ageRanges.indexOf(row.age_range);
      if (rangeIndex !== -1) {
        const gender = row.gender.toLowerCase();
        if (gender === 'male' || gender === 'female' || gender === 'other') {
          patientsByAgeFormatted[rangeIndex][gender] = parseInt(row.count);
          patientsByAgeFormatted[rangeIndex].count += parseInt(row.count);
        } else {
          patientsByAgeFormatted[rangeIndex].other += parseInt(row.count);
          patientsByAgeFormatted[rangeIndex].count += parseInt(row.count);
        }
      }
    });

    // Get most common medical conditions from visit reasons
    const commonConditions = await pool.query(`
      SELECT
        visit_reason as condition,
        COUNT(*) as count,
        COUNT(*) * 100.0 / (SELECT COUNT(*) FROM patient_visits WHERE visit_reason IS NOT NULL) as percentage
      FROM patient_visits
      WHERE visit_reason IS NOT NULL
      GROUP BY visit_reason
      ORDER BY count DESC
      LIMIT 10
    `);

    const formattedConditions = commonConditions.rows.map(condition => ({
      name: condition.condition,
      count: parseInt(condition.count),
      percentage: parseFloat(condition.percentage).toFixed(1)
    }));

    // Get average vital signs with additional metrics
    const vitalSigns = await pool.query(`
      SELECT
        AVG(lying_bp_systolic) as avg_systolic,
        AVG(lying_bp_diastolic) as avg_diastolic,
        AVG(sitting_heart_rate) as avg_heart_rate,
        AVG(pulse_oximetry) as avg_oxygen,
        AVG(temperature) as avg_temperature,
        AVG(respiratory_rate) as avg_respiratory_rate,

        -- Blood pressure categories
        SUM(CASE WHEN lying_bp_systolic < 120 AND lying_bp_diastolic < 80 THEN 1 ELSE 0 END) as normal_bp_count,
        SUM(CASE WHEN (lying_bp_systolic BETWEEN 120 AND 129) AND lying_bp_diastolic < 80 THEN 1 ELSE 0 END) as elevated_bp_count,
        SUM(CASE WHEN (lying_bp_systolic BETWEEN 130 AND 139) OR (lying_bp_diastolic BETWEEN 80 AND 89) THEN 1 ELSE 0 END) as hypertension_stage1_count,
        SUM(CASE WHEN lying_bp_systolic >= 140 OR lying_bp_diastolic >= 90 THEN 1 ELSE 0 END) as hypertension_stage2_count,

        -- Count of records with vital signs
        COUNT(*) as total_records
      FROM patient_visits
      WHERE
        lying_bp_systolic IS NOT NULL AND
        lying_bp_diastolic IS NOT NULL AND
        sitting_heart_rate IS NOT NULL AND
        pulse_oximetry IS NOT NULL
    `);

    // Get lab value statistics
    const labValues = await pool.query(`
      SELECT
        AVG(blood_glucose) as avg_blood_glucose,
        AVG(cholesterol_total) as avg_cholesterol,
        AVG(hba1c) as avg_hba1c,

        -- Blood glucose categories
        SUM(CASE WHEN blood_glucose < 70 THEN 1 ELSE 0 END) as low_glucose_count,
        SUM(CASE WHEN blood_glucose BETWEEN 70 AND 99 THEN 1 ELSE 0 END) as normal_glucose_count,
        SUM(CASE WHEN blood_glucose BETWEEN 100 AND 125 THEN 1 ELSE 0 END) as prediabetes_glucose_count,
        SUM(CASE WHEN blood_glucose >= 126 THEN 1 ELSE 0 END) as diabetes_glucose_count,

        COUNT(*) as total_lab_records
      FROM patient_visits
      WHERE blood_glucose IS NOT NULL AND cholesterol_total IS NOT NULL
    `);

    // Calculate BP category percentages
    const totalBpRecords = parseInt(vitalSigns.rows[0].total_records) || 1; // Avoid division by zero
    const bpCategories = {
      normal: {
        count: parseInt(vitalSigns.rows[0].normal_bp_count) || 0,
        percentage: Math.round((parseInt(vitalSigns.rows[0].normal_bp_count) || 0) / totalBpRecords * 100)
      },
      elevated: {
        count: parseInt(vitalSigns.rows[0].elevated_bp_count) || 0,
        percentage: Math.round((parseInt(vitalSigns.rows[0].elevated_bp_count) || 0) / totalBpRecords * 100)
      },
      hypertensionStage1: {
        count: parseInt(vitalSigns.rows[0].hypertension_stage1_count) || 0,
        percentage: Math.round((parseInt(vitalSigns.rows[0].hypertension_stage1_count) || 0) / totalBpRecords * 100)
      },
      hypertensionStage2: {
        count: parseInt(vitalSigns.rows[0].hypertension_stage2_count) || 0,
        percentage: Math.round((parseInt(vitalSigns.rows[0].hypertension_stage2_count) || 0) / totalBpRecords * 100)
      }
    };

    // Calculate glucose category percentages
    const totalLabRecords = parseInt(labValues.rows[0].total_lab_records) || 1; // Avoid division by zero
    const glucoseCategories = {
      low: {
        count: parseInt(labValues.rows[0].low_glucose_count) || 0,
        percentage: Math.round((parseInt(labValues.rows[0].low_glucose_count) || 0) / totalLabRecords * 100)
      },
      normal: {
        count: parseInt(labValues.rows[0].normal_glucose_count) || 0,
        percentage: Math.round((parseInt(labValues.rows[0].normal_glucose_count) || 0) / totalLabRecords * 100)
      },
      prediabetes: {
        count: parseInt(labValues.rows[0].prediabetes_glucose_count) || 0,
        percentage: Math.round((parseInt(labValues.rows[0].prediabetes_glucose_count) || 0) / totalLabRecords * 100)
      },
      diabetes: {
        count: parseInt(labValues.rows[0].diabetes_glucose_count) || 0,
        percentage: Math.round((parseInt(labValues.rows[0].diabetes_glucose_count) || 0) / totalLabRecords * 100)
      }
    };

    const averageVitals = {
      systolic: Math.round(parseFloat(vitalSigns.rows[0].avg_systolic)) || 120,
      diastolic: Math.round(parseFloat(vitalSigns.rows[0].avg_diastolic)) || 80,
      heartRate: Math.round(parseFloat(vitalSigns.rows[0].avg_heart_rate)) || 75,
      oxygenLevel: Math.round(parseFloat(vitalSigns.rows[0].avg_oxygen)) || 98,
      temperature: parseFloat(vitalSigns.rows[0].avg_temperature).toFixed(1) || 36.5,
      respiratoryRate: Math.round(parseFloat(vitalSigns.rows[0].avg_respiratory_rate)) || 16
    };

    const averageLabValues = {
      bloodGlucose: Math.round(parseFloat(labValues.rows[0].avg_blood_glucose)) || 85,
      cholesterol: Math.round(parseFloat(labValues.rows[0].avg_cholesterol)) || 180,
      hba1c: parseFloat(labValues.rows[0].avg_hba1c).toFixed(1) || 5.7
    };

    // Get recent activity data from edit logs and access logs
    const recentEditLogs = await pool.query(`
      SELECT
        el.edit_id as id,
        CASE
          WHEN el.edit_type = 'delete' AND el.field_changed = 'user_account' THEN 'user'
          WHEN el.edit_type = 'delete' AND el.field_changed = 'visit' THEN 'visit'
          WHEN el.patient_id = 0 THEN 'system'
          ELSE 'patient'
        END as type,
        CASE
          WHEN el.edit_type = 'delete' AND el.field_changed = 'user_account' THEN 'Deleted user account'
          WHEN el.edit_type = 'delete' AND el.field_changed = 'visit' THEN 'Deleted patient visit'
          WHEN el.edit_summary IS NOT NULL THEN el.edit_summary
          WHEN el.field_changed = 'user_account' AND el.new_value IS NULL THEN 'Deleted user account'
          WHEN el.field_changed = 'visit' AND el.new_value IS NULL THEN 'Deleted patient visit'
          WHEN el.field_changed IS NOT NULL THEN 'Updated ' || el.field_changed
          ELSE 'Modified patient record'
        END as description,
        u.username as user,
        el.edit_time as timestamp
      FROM patient_edit_logs el
      JOIN users u ON el.user_id = u.user_id
      ORDER BY el.edit_time DESC
      LIMIT 10
    `);

    // Get recent login activity
    const recentLogins = await pool.query(`
      SELECT
        user_id as id,
        'login' as type,
        'Logged into the system' as description,
        username as user,
        last_login as timestamp
      FROM users
      WHERE last_login IS NOT NULL
      ORDER BY last_login DESC
      LIMIT 5
    `);

    // Combine and sort all activity
    const recentActivity = [...recentEditLogs.rows, ...recentLogins.rows]
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, 10);

    // Combine all stats into a single response object
    const stats = {
      totalUsers: parseInt(basicStats.rows[0].total_users) || 0,
      totalDoctors: parseInt(basicStats.rows[0].total_doctors) || 0,
      totalPatients: parseInt(basicStats.rows[0].total_patients) || 0,
      totalVisits: parseInt(basicStats.rows[0].total_visits) || 0,
      activeUsers: parseInt(basicStats.rows[0].active_users) || 0,

      // Demographics data
      patientsByGender,
      patientsByAge: patientsByAgeFormatted,

      // Activity data
      visitsPerMonth: visitsData,
      visitsTrend,
      recentActivity, // Add the real recent activity data

      // Provider analysis data
      topDoctors: formattedTopDoctors,
      doctorEfficiency: formattedDoctorEfficiency,

      // Health metrics data
      commonConditions: formattedConditions,
      averageVitals,
      averageLabValues,
      bpCategories,
      glucoseCategories
    };

    console.log('Successfully compiled detailed statistics');
    res.json(stats);
  } catch (err) {
    console.error('Error in detailed-stats endpoint:', err.message);
    res.status(500).send('Server Error');
  }
});

// Helper function to generate additional visit data if needed
async function generateVisitData() {
  try {
    // Get all patients
    const patients = await pool.query('SELECT patient_id FROM patients');
    if (patients.rows.length === 0) {
      console.log('No patients found to generate visits for');
      return;
    }

    // Get all doctors
    const doctors = await pool.query('SELECT doctor_id FROM doctors');
    if (doctors.rows.length === 0) {
      console.log('No doctors found to assign to visits');
      return;
    }

    const doctorIds = doctors.rows.map(d => d.doctor_id);
    const patientIds = patients.rows.map(p => p.patient_id);
    const visitReasons = [
      'Annual checkup',
      'Flu symptoms',
      'Follow-up appointment',
      'Blood pressure check',
      'Diabetes management',
      'Chest pain',
      'Vaccination',
      'Physical therapy',
      'Allergy symptoms',
      'Medication review'
    ];

    // Generate 300 random visits spread across the last 12 months
    const visits = [];
    for (let i = 0; i < 300; i++) {
      const patientId = patientIds[Math.floor(Math.random() * patientIds.length)];
      const doctorId = doctorIds[Math.floor(Math.random() * doctorIds.length)];
      const visitReason = visitReasons[Math.floor(Math.random() * visitReasons.length)];

      // Random date in the last 12 months
      const visitDate = new Date();
      visitDate.setMonth(visitDate.getMonth() - Math.floor(Math.random() * 12));

      // Generate vital signs and lab results with realistic ranges
      const lyingBpSystolic = Math.floor(Math.random() * 40) + 100; // 100-140
      const lyingBpDiastolic = Math.floor(Math.random() * 30) + 60; // 60-90
      const heartRate = Math.floor(Math.random() * 40) + 60; // 60-100
      const temp = (Math.random() * 1.5 + 36.2).toFixed(1); // 36.2-37.7
      const respRate = Math.floor(Math.random() * 8) + 12; // 12-20
      const pulseOx = Math.floor(Math.random() * 5) + 95; // 95-100

      // Lab results
      const bloodGlucose = Math.floor(Math.random() * 50) + 70; // 70-120
      const hba1c = (Math.random() * 2 + 4).toFixed(1); // 4.0-6.0
      const cholesterol = Math.floor(Math.random() * 100) + 150; // 150-250
      const hdl = Math.floor(Math.random() * 30) + 40; // 40-70
      const ldl = Math.floor(Math.random() * 60) + 70; // 70-130

      visits.push({
        patient_id: patientId,
        doctor_id: doctorId,
        visit_date: visitDate.toISOString().split('T')[0],
        visit_reason: visitReason,
        lying_bp_systolic: lyingBpSystolic,
        lying_bp_diastolic: lyingBpDiastolic,
        sitting_heart_rate: heartRate,
        temperature: parseFloat(temp),
        respiratory_rate: respRate,
        pulse_oximetry: pulseOx,
        blood_glucose: bloodGlucose,
        hba1c: parseFloat(hba1c),
        cholesterol_total: cholesterol,
        hdl_cholesterol: hdl,
        ldl_cholesterol: ldl
      });
    }

    // Insert visits in batches
    for (const visit of visits) {
      await pool.query(`
        INSERT INTO patient_visits (
          patient_id, doctor_id, visit_date, visit_reason,
          lying_bp_systolic, lying_bp_diastolic, sitting_heart_rate, temperature,
          respiratory_rate, pulse_oximetry, blood_glucose, hba1c,
          cholesterol_total, hdl_cholesterol, ldl_cholesterol
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15)
      `, [
        visit.patient_id, visit.doctor_id, visit.visit_date, visit.visit_reason,
        visit.lying_bp_systolic, visit.lying_bp_diastolic, visit.sitting_heart_rate, visit.temperature,
        visit.respiratory_rate, visit.pulse_oximetry, visit.blood_glucose, visit.hba1c,
        visit.cholesterol_total, visit.hdl_cholesterol, visit.ldl_cholesterol
      ]);
    }

    console.log(`Successfully generated ${visits.length} patient visits`);
  } catch (err) {
    console.error('Error generating visit data:', err.message);
    throw err;
  }
}

module.exports = router;