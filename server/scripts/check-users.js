const pool = require('../db');

async function checkUsers() {
  try {
    console.log('Checking users in the database...');
    
    const result = await pool.query('SELECT user_id, username, email, role, is_locked, failed_login_attempts FROM users');
    
    if (result.rows.length === 0) {
      console.log('No users found in the database.');
    } else {
      console.log(`Found ${result.rows.length} users:`);
      result.rows.forEach(user => {
        console.log(`ID: ${user.user_id}, Username: ${user.username}, Email: ${user.email}, Role: ${user.role}, Locked: ${user.is_locked}, Failed Attempts: ${user.failed_login_attempts}`);
      });
    }
  } catch (err) {
    console.error('Error checking users:', err);
  } finally {
    pool.end();
  }
}

checkUsers(); 