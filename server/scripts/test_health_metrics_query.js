/**
 * <PERSON><PERSON><PERSON> to test the combined health metrics query directly
 */

require('dotenv').config();
const pool = require('../db');

// Function to test the combined health metrics query
const testHealthMetricsQuery = async () => {
  try {
    // Get a patient ID
    const patientResult = await pool.query('SELECT patient_id FROM patients LIMIT 1');

    if (patientResult.rows.length === 0) {
      console.error('No patients found');
      return;
    }

    const patientId = patientResult.rows[0].patient_id;
    console.log(`Testing combined health metrics query for patient ID: ${patientId}`);

    // First, get patient data (baseline metrics)
    const patientQuery = `
      SELECT
        patient_id,
        created_at,
        'baseline' as visit_type,
        created_at as visit_date,

        -- Vital Signs
        lying_bp_systolic, lying_bp_diastolic,
        weight, height, bmi
      FROM patients
      WHERE patient_id = $1
    `;

    // Then, get all visits data
    const visitsQuery = `
      SELECT
        patient_id,
        visit_id,
        created_at,
        'visit' as visit_type,
        visit_date,

        -- <PERSON>l Signs
        lying_bp_systolic, lying_bp_diastolic,
        weight, height, bmi
      FROM patient_visits
      WHERE patient_id = $1
      ORDER BY visit_date ASC
    `;

    // Execute both queries in parallel
    const [patientDataResult, visitsDataResult] = await Promise.all([
      pool.query(patientQuery, [patientId]),
      pool.query(visitsQuery, [patientId])
    ]);

    // Process patient data (baseline)
    let baselineData = null;
    if (patientDataResult.rows.length > 0) {
      baselineData = patientDataResult.rows[0];

      // Check if baseline data has any health metrics
      const hasHealthData = Object.entries(baselineData).some(([key, value]) => {
        // Only check health metric fields, not metadata fields
        const metadataFields = ['patient_id', 'created_at', 'visit_type', 'visit_date'];
        return !metadataFields.includes(key) && value !== null && value !== undefined;
      });

      if (!hasHealthData) {
        baselineData = null;
        console.log('Baseline data has no health metrics, excluding from results');
      } else {
        console.log('Baseline data has health metrics, including in results');
      }
    }

    // Process visits data
    const visitsData = visitsDataResult.rows;
    console.log(`Found ${visitsData.length} visits for patient ID: ${patientId}`);

    // Combine data (if baseline data exists and has health metrics)
    const combinedData = baselineData
      ? [baselineData, ...visitsData]
      : visitsData;

    // Sort by date (just to be sure)
    combinedData.sort((a, b) => new Date(a.visit_date) - new Date(b.visit_date));

    console.log(`Combined data has ${combinedData.length} data points`);

    // Print the first data point
    if (combinedData.length > 0) {
      console.log('\nFirst data point:');
      console.log(`- Type: ${combinedData[0].visit_type}`);
      console.log(`- Date: ${combinedData[0].visit_date}`);

      // Print some health metrics
      console.log('\nVital Signs:');
      console.log(`- Systolic BP: ${combinedData[0].lying_bp_systolic}`);
      console.log(`- Diastolic BP: ${combinedData[0].lying_bp_diastolic}`);
      console.log(`- Weight: ${combinedData[0].weight}`);
      console.log(`- Height: ${combinedData[0].height}`);
      console.log(`- BMI: ${combinedData[0].bmi}`);
    }
  } catch (error) {
    console.error('Error testing combined health metrics query:', error.message);
  } finally {
    // Close the database connection
    await pool.end();
  }
};

// Run the test
testHealthMetricsQuery();
