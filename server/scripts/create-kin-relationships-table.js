const pool = require('../db');

async function createKinPatientRelationshipsTable() {
  try {
    console.log('Creating kin_patient_relationships table...');
    
    const sql = `
      CREATE TABLE IF NOT EXISTS kin_patient_relationships (
        relationship_id SERIAL PRIMARY KEY,
        kin_user_id INTEGER NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
        patient_id INTEGER NOT NULL REFERENCES patients(patient_id) ON DELETE CASCADE,
        relationship_type VARCHAR(50),
        is_primary BOOLEAN DEFAULT false,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE(kin_user_id, patient_id)
      );
      
      CREATE INDEX IF NOT EXISTS idx_kin_patient_kin_user_id ON kin_patient_relationships(kin_user_id);
      CREATE INDEX IF NOT EXISTS idx_kin_patient_patient_id ON kin_patient_relationships(patient_id);
    `;
    
    await pool.query(sql);
    
    console.log('Successfully created kin_patient_relationships table.');
    
    // Migrate existing kin-patient relationships from users table
    console.log('Migrating existing kin-patient relationships...');
    
    const migrateSQL = `
      INSERT INTO kin_patient_relationships (kin_user_id, patient_id, is_primary)
      SELECT user_id, patient_id, true
      FROM users
      WHERE role = 'kin' AND patient_id IS NOT NULL
      ON CONFLICT (kin_user_id, patient_id) DO NOTHING;
    `;
    
    await pool.query(migrateSQL);
    
    console.log('Migration completed successfully.');
    
    // Check the migrated relationships
    const result = await pool.query(`
      SELECT kpr.relationship_id, kpr.kin_user_id, u.username, kpr.patient_id, 
             p.first_name, p.last_name, kpr.is_primary
      FROM kin_patient_relationships kpr
      JOIN users u ON kpr.kin_user_id = u.user_id
      JOIN patients p ON kpr.patient_id = p.patient_id
    `);
    
    if (result.rows.length === 0) {
      console.log('No kin-patient relationships found after migration.');
    } else {
      console.log(`Found ${result.rows.length} kin-patient relationships:`);
      result.rows.forEach(rel => {
        console.log(`Relationship ID: ${rel.relationship_id}, Kin User: ${rel.username} (ID: ${rel.kin_user_id}), Patient: ${rel.first_name} ${rel.last_name} (ID: ${rel.patient_id}), Primary: ${rel.is_primary}`);
      });
    }
  } catch (err) {
    console.error('Error creating kin_patient_relationships table:', err);
  } finally {
    pool.end();
  }
}

createKinPatientRelationshipsTable();
