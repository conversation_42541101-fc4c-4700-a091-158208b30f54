const pool = require('../db');

async function addLockoutUntilColumn() {
  try {
    console.log('Checking if lockout_until column exists...');
    
    // Check if column exists
    const checkResult = await pool.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'users' AND column_name = 'lockout_until'
    `);
    
    if (checkResult.rows.length === 0) {
      console.log('lockout_until column does not exist. Adding it...');
      
      // Add the column
      await pool.query(`
        ALTER TABLE users 
        ADD COLUMN lockout_until TIMESTAMP
      `);
      
      console.log('lockout_until column added successfully.');
    } else {
      console.log('lockout_until column already exists.');
    }
    
    // Verify the users table structure
    const tableResult = await pool.query(`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'users'
    `);
    
    console.log('Current users table structure:');
    console.table(tableResult.rows);
    
    process.exit(0);
  } catch (err) {
    console.error('Error:', err);
    process.exit(1);
  }
}

addLockoutUntilColumn();
