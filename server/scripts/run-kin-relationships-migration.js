const fs = require('fs');
const path = require('path');
const pool = require('../db');

async function runKinRelationshipsMigration() {
  try {
    console.log('Running kin_patient_relationships migration...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../sql/add_kin_patient_relationships.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    await pool.query(sql);
    
    console.log('Successfully added kin_patient_relationships table and migrated existing relationships.');
    
    // Check the migrated relationships
    const result = await pool.query(`
      SELECT kpr.relationship_id, kpr.kin_user_id, u.username, kpr.patient_id, 
             p.first_name, p.last_name, kpr.is_primary
      FROM kin_patient_relationships kpr
      JOIN users u ON kpr.kin_user_id = u.user_id
      JOIN patients p ON kpr.patient_id = p.patient_id
    `);
    
    if (result.rows.length === 0) {
      console.log('No kin-patient relationships found after migration.');
    } else {
      console.log(`Found ${result.rows.length} kin-patient relationships:`);
      result.rows.forEach(rel => {
        console.log(`Relationship ID: ${rel.relationship_id}, Kin User: ${rel.username} (ID: ${rel.kin_user_id}), Patient: ${rel.first_name} ${rel.last_name} (ID: ${rel.patient_id}), Primary: ${rel.is_primary}`);
      });
    }
  } catch (err) {
    console.error('Error running kin_patient_relationships migration:', err);
  } finally {
    pool.end();
  }
}

runKinRelationshipsMigration();
