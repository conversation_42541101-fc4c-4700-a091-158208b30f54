const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'medapp'
});

async function runMigration() {
  const client = await pool.connect();
  try {
    console.log('Setting database timezone to Nepal timezone (Asia/Kathmandu)...');
    
    // Read the migration SQL file
    const migrationPath = path.join(__dirname, '../migrations/008_set_nepal_timezone.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Run the migration
    const result = await client.query(migrationSQL);
    
    console.log('Migration completed successfully!');
    console.log('Current database timezone:', result[1]?.rows[0]?.current_timezone || 'Asia/Kathmandu');
    console.log('Message:', result[result.length - 1]?.rows[0]?.message);
    
  } catch (error) {
    console.error('Error running Nepal timezone migration:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the migration
runMigration(); 