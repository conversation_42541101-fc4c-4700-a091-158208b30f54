/**
 * <PERSON><PERSON><PERSON> to populate the clinical guidance database with additional lab results guidance
 */

const db = require('../db');

// Guidance content organized by context
const guidanceContent = [
  // Vitamin Status
  {
    title: "Vitamin Status",
    content: `<p><strong>Vitamin B12:</strong> Normal range is 200-900 pg/mL. Low values may indicate pernicious anemia, malabsorption, or strict vegetarian diet. High values may indicate supplementation or liver disease.</p>
<p><strong>Vitamin D (25-OH):</strong> Normal range is 30-80 ng/mL. Values below 20 ng/mL indicate deficiency, 20-30 ng/mL indicate insufficiency. Low values are associated with osteoporosis, muscle weakness, and increased fall risk in older adults.</p>
<p><strong>Folate:</strong> Normal range is 2.7-17.0 ng/mL. Low values may indicate malnutrition, malabsorption, or increased requirements (pregnancy). Deficiency can lead to megaloblastic anemia and elevated homocysteine levels.</p>`,
    summary: "Guidance on interpreting vitamin status in older adults",
    category_id: 2, // Lab Results
    context_key: "lab_results_vitamins",
    is_published: true,
    tags: ["vitamins", "vitamin B12", "vitamin D", "folate", "deficiency"]
  },
  
  // Thyroid Function Tests
  {
    title: "Thyroid Function Tests",
    content: `<p><strong>TSH (Thyroid Stimulating Hormone):</strong> Normal range is 0.4-4.0 mIU/L. Elevated values suggest hypothyroidism, while low values suggest hyperthyroidism. TSH is the most sensitive indicator of thyroid dysfunction.</p>
<p><strong>T4 (Total Thyroxine):</strong> Normal range is 4.5-12.0 μg/dL. Low values may indicate hypothyroidism, while high values may indicate hyperthyroidism. Measures both bound and free T4.</p>
<p><strong>T3 (Total Triiodothyronine):</strong> Normal range is 80-200 ng/dL. Low values may indicate hypothyroidism, while high values may indicate hyperthyroidism. Measures both bound and free T3.</p>
<p><strong>Free T4:</strong> Normal range is 0.8-1.8 ng/dL. More accurate than total T4 for assessing thyroid function as it measures the metabolically active hormone.</p>
<p><strong>Free T3:</strong> Normal range is 2.3-4.2 pg/mL. More accurate than total T3 for assessing thyroid function as it measures the metabolically active hormone.</p>
<p><strong>Thyroid Antibodies:</strong> Anti-TPO and anti-thyroglobulin antibodies may be elevated in autoimmune thyroid diseases like Hashimoto's thyroiditis.</p>
<p><strong>Considerations for Older Adults:</strong> Subclinical thyroid dysfunction is common in older adults. Treatment decisions should consider symptoms, comorbidities, and potential medication interactions.</p>`,
    summary: "Guidance on interpreting thyroid function test results in older adults",
    category_id: 2, // Lab Results
    context_key: "lab_results_thyroid",
    is_published: true,
    tags: ["thyroid", "TSH", "T4", "T3", "hypothyroidism", "hyperthyroidism"]
  },
  
  // Inflammatory Markers
  {
    title: "Inflammatory Markers",
    content: `<p><strong>CRP (C-Reactive Protein):</strong> Normal is &lt;3.0 mg/L. Elevated in inflammation, infection, and cardiovascular disease risk.</p>
<p><strong>ESR (Erythrocyte Sedimentation Rate):</strong> Normal ranges vary by age and gender. Generally &lt;20 mm/hr for men and &lt;30 mm/hr for women. Non-specific marker of inflammation.</p>
<p><strong>Clinical Significance:</strong> Elevated inflammatory markers in older adults may indicate underlying chronic disease, infection, or malignancy. Consider baseline values for each patient.</p>
<p><strong>Interpretation in Older Adults:</strong> Inflammatory markers may be mildly elevated in older adults due to age-related chronic inflammation ("inflammaging"). Serial measurements may be more informative than single values.</p>
<p><strong>High-Sensitivity CRP (hs-CRP):</strong> Used to assess cardiovascular risk. Values &lt;1 mg/L indicate low risk, 1-3 mg/L moderate risk, and &gt;3 mg/L high risk.</p>`,
    summary: "Guidance on interpreting inflammatory markers in older adults",
    category_id: 2, // Lab Results
    context_key: "lab_results_inflammation",
    is_published: true,
    tags: ["inflammation", "CRP", "ESR", "infection", "cardiovascular risk"]
  },
  
  // Urinalysis
  {
    title: "Urinalysis",
    content: `<p><strong>Urinalysis:</strong> Provides valuable information about kidney function, urinary tract infections, and systemic diseases.</p>
<p><strong>Physical Properties:</strong> Normal urine is clear to pale yellow. Cloudiness may indicate infection, while abnormal colors can suggest various conditions.</p>
<p><strong>Chemical Tests:</strong> pH (normal 4.5-8.0), protein (normally absent), glucose (normally absent).</p>
<p><strong>Microscopic Examination:</strong> RBCs (&lt;3/HPF), WBCs (&lt;5/HPF), epithelial cells (few), crystals (absent), casts (absent).</p>
<p><strong>Interpretation in Older Adults:</strong></p>
<ul>
<li>Proteinuria may indicate kidney disease, but transient proteinuria can occur with fever, exercise, or dehydration.</li>
<li>Hematuria requires evaluation for urinary tract infection, stones, or malignancy.</li>
<li>Pyuria (elevated WBCs) suggests infection, but asymptomatic bacteriuria is common in older adults and may not require treatment.</li>
<li>Glucose in urine may indicate diabetes, but renal threshold for glucose may be altered in older adults.</li>
</ul>`,
    summary: "Guidance on interpreting urinalysis results in older adults",
    category_id: 2, // Lab Results
    context_key: "lab_results_urinalysis",
    is_published: true,
    tags: ["urinalysis", "urine", "kidney", "infection", "proteinuria", "hematuria"]
  },
  
  // Cancer Screening
  {
    title: "Cancer Screening",
    content: `<p><strong>PSA (Prostate-Specific Antigen):</strong> Normal is generally &lt;4.0 ng/mL, but age-specific ranges exist. Screening recommendations vary; discuss risks/benefits with patients.</p>
<p><strong>CA-125 (Cancer Antigen 125):</strong> Normal is generally &lt;35 U/mL. Primarily used to monitor ovarian cancer treatment response, but may be used as part of screening in high-risk women. Can be elevated in other conditions including endometriosis, fibroids, and other cancers.</p>
<p><strong>Cancer Screening for Older Adults:</strong> Consider life expectancy, comorbidities, and patient preferences when recommending cancer screenings.</p>
<p><strong>Recommended Screenings by Age:</strong> Colorectal (until age 75), breast (until age 74), cervical (until age 65 with adequate prior screening), lung (ages 50-80 with smoking history), and prostate (individualized decision).</p>
<p><strong>Other Cancer Markers:</strong></p>
<ul>
<li>CEA (Carcinoembryonic Antigen): Used primarily for monitoring colorectal cancer. Normal &lt;5 ng/mL.</li>
<li>AFP (Alpha-Fetoprotein): Used for liver cancer screening in high-risk individuals. Normal &lt;10 ng/mL.</li>
<li>CA 19-9: Used primarily for monitoring pancreatic cancer. Normal &lt;37 U/mL.</li>
</ul>
<p><strong>Considerations for Older Adults:</strong> The benefits of cancer screening decrease with age and increasing comorbidities. Shared decision-making is essential.</p>`,
    summary: "Guidance on cancer screening in older adults",
    category_id: 2, // Lab Results
    context_key: "lab_results_cancer",
    is_published: true,
    tags: ["cancer", "screening", "PSA", "CA-125", "tumor markers"]
  }
];

// Function to insert guidance content
async function populateGuidance() {
  try {
    console.log('Starting to populate additional lab results guidance tables...');

    // Insert each guidance entry
    for (const guidance of guidanceContent) {
      // Insert the guidance
      const guidanceResult = await db.query(
        `INSERT INTO clinical_guidance
         (title, content, summary, category_id, context_key, is_published, created_at, updated_at)
         VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
         RETURNING guidance_id`,
        [
          guidance.title,
          guidance.content,
          guidance.summary,
          guidance.category_id,
          guidance.context_key,
          guidance.is_published
        ]
      );

      const guidanceId = guidanceResult.rows[0].guidance_id;
      console.log(`Inserted guidance: ${guidance.title} (ID: ${guidanceId})`);

      // Insert tags if any
      if (guidance.tags && guidance.tags.length > 0) {
        for (const tag of guidance.tags) {
          // Check if tag exists
          const tagResult = await db.query(
            `SELECT tag_id FROM guidance_tags WHERE name = $1`,
            [tag]
          );

          let tagId;
          if (tagResult.rows.length > 0) {
            tagId = tagResult.rows[0].tag_id;
          } else {
            // Create new tag
            const newTagResult = await db.query(
              `INSERT INTO guidance_tags (name, created_at)
               VALUES ($1, NOW())
               RETURNING tag_id`,
              [tag]
            );
            tagId = newTagResult.rows[0].tag_id;
            console.log(`Created new tag: ${tag} (ID: ${tagId})`);
          }

          // Create tag relation
          await db.query(
            `INSERT INTO guidance_tag_relations (guidance_id, tag_id)
             VALUES ($1, $2)`,
            [guidanceId, tagId]
          );
        }
      }

      // Create initial version
      await db.query(
        `INSERT INTO guidance_versions
         (guidance_id, version_number, title, content, summary, category_id, created_at)
         VALUES ($1, 1, $2, $3, $4, $5, NOW())`,
        [
          guidanceId,
          guidance.title,
          guidance.content,
          guidance.summary,
          guidance.category_id
        ]
      );

      // Create audit entry
      await db.query(
        `INSERT INTO guidance_audit
         (guidance_id, action, details, performed_at)
         VALUES ($1, 'create', $2, NOW())`,
        [
          guidanceId,
          JSON.stringify({ title: guidance.title, context_key: guidance.context_key })
        ]
      );
    }

    console.log('Additional lab results guidance population completed successfully!');
  } catch (error) {
    console.error('Error populating additional lab results guidance:', error);
  } finally {
    // Close the database connection
    process.exit(0);
  }
}

// Run the population function
populateGuidance();
