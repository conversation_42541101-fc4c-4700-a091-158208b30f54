/**
 * <PERSON><PERSON><PERSON> to create a Preventive Care category and update the Vaccinations guidance entry
 */

const db = require('../db');

// Function to create category and update guidance
async function createCategoryAndUpdateGuidance() {
  try {
    console.log('Starting to create Preventive Care category and update Vaccinations guidance...');

    // Create new Preventive Care category
    const categoryResult = await db.query(
      `INSERT INTO guidance_categories
       (name, description, created_at, updated_at)
       VALUES ($1, $2, NOW(), NOW())
       RETURNING category_id`,
      [
        'Preventive Care',
        'Guidance related to preventive care measures including vaccinations and screenings'
      ]
    );

    const newCategoryId = categoryResult.rows[0].category_id;
    console.log(`Created new category: Preventive Care (ID: ${newCategoryId})`);

    // Get the current guidance entry
    const guidanceResult = await db.query(
      `SELECT guidance_id, title, category_id FROM clinical_guidance WHERE context_key = $1`,
      ['vaccinations_older_adults']
    );

    if (guidanceResult.rows.length === 0) {
      console.log(`No guidance found with context_key: vaccinations_older_adults`);
      process.exit(0);
    }

    const guidance = guidanceResult.rows[0];
    const oldCategoryId = guidance.category_id;

    // Update the guidance category
    await db.query(
      `UPDATE clinical_guidance 
       SET category_id = $1, updated_at = NOW()
       WHERE context_key = $2`,
      [newCategoryId, 'vaccinations_older_adults']
    );

    console.log(`Updated guidance: ${guidance.title} (ID: ${guidance.guidance_id}) - Category changed from ${oldCategoryId} to ${newCategoryId}`);

    // Update the latest version's category
    await db.query(
      `UPDATE guidance_versions
       SET category_id = $1
       WHERE guidance_id = $2 AND version_number = (
         SELECT MAX(version_number) FROM guidance_versions WHERE guidance_id = $2
       )`,
      [newCategoryId, guidance.guidance_id]
    );

    // Create audit entry
    await db.query(
      `INSERT INTO guidance_audit
       (guidance_id, action, details, performed_at)
       VALUES ($1, 'update', $2, NOW())`,
      [
        guidance.guidance_id,
        JSON.stringify({ 
          title: guidance.title, 
          context_key: 'vaccinations_older_adults',
          category_change: {
            from: oldCategoryId,
            to: newCategoryId
          }
        })
      ]
    );

    console.log('Preventive Care category creation and Vaccinations guidance update completed successfully!');
  } catch (error) {
    console.error('Error creating category and updating guidance:', error);
  } finally {
    // Close the database connection
    process.exit(0);
  }
}

// Run the function
createCategoryAndUpdateGuidance();
