/**
 * <PERSON><PERSON><PERSON> to update sample health metrics data in the database
 * This script runs the SQL file that adds realistic values for various health metrics
 */

const fs = require('fs');
const path = require('path');
const pool = require('../db');
require('dotenv').config();

async function updateHealthMetricsData() {
  const client = await pool.connect();
  try {
    console.log('Updating health metrics sample data...');
    
    // Read the SQL file
    const sqlPath = path.join(__dirname, '../sql/update_health_metrics_sample_data.sql');
    console.log('Reading SQL file from:', sqlPath);
    const sql = fs.readFileSync(sqlPath, 'utf8');
    
    // Execute the SQL
    console.log('Executing SQL...');
    const result = await client.query(sql);
    console.log('SQL executed successfully');
    
    console.log('✅ Health metrics sample data updated successfully!');
    
  } catch (error) {
    console.error('❌ Error updating health metrics sample data:', error);
  } finally {
    client.release();
    await pool.end();
    console.log('Database connection closed');
  }
}

// Run the update
updateHealthMetricsData();
