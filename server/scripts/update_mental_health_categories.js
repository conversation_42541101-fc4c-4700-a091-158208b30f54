/**
 * <PERSON><PERSON><PERSON> to update the category assignments for mental health guidance entries
 */

const db = require('../db');

// Guidance entries to update
const guidanceUpdates = [
  {
    context_key: 'mental_health_depression',
    new_category_id: 5 // Mental Health
  },
  {
    context_key: 'mental_health_anxiety',
    new_category_id: 5 // Mental Health
  }
];

// Function to update guidance categories
async function updateGuidanceCategories() {
  try {
    console.log('Starting to update mental health guidance categories...');

    // Update each guidance entry
    for (const update of guidanceUpdates) {
      // Get the current guidance entry
      const guidanceResult = await db.query(
        `SELECT guidance_id, title, category_id FROM clinical_guidance WHERE context_key = $1`,
        [update.context_key]
      );

      if (guidanceResult.rows.length === 0) {
        console.log(`No guidance found with context_key: ${update.context_key}`);
        continue;
      }

      const guidance = guidanceResult.rows[0];
      const oldCategoryId = guidance.category_id;

      // Update the guidance category
      await db.query(
        `UPDATE clinical_guidance 
         SET category_id = $1, updated_at = NOW()
         WHERE context_key = $2`,
        [update.new_category_id, update.context_key]
      );

      console.log(`Updated guidance: ${guidance.title} (ID: ${guidance.guidance_id}) - Category changed from ${oldCategoryId} to ${update.new_category_id}`);

      // Update the latest version's category
      await db.query(
        `UPDATE guidance_versions
         SET category_id = $1
         WHERE guidance_id = $2 AND version_number = (
           SELECT MAX(version_number) FROM guidance_versions WHERE guidance_id = $2
         )`,
        [update.new_category_id, guidance.guidance_id]
      );

      // Create audit entry
      await db.query(
        `INSERT INTO guidance_audit
         (guidance_id, action, details, performed_at)
         VALUES ($1, 'update', $2, NOW())`,
        [
          guidance.guidance_id,
          JSON.stringify({ 
            title: guidance.title, 
            context_key: update.context_key,
            category_change: {
              from: oldCategoryId,
              to: update.new_category_id
            }
          })
        ]
      );
    }

    console.log('Mental health guidance category updates completed successfully!');
  } catch (error) {
    console.error('Error updating mental health guidance categories:', error);
  } finally {
    // Close the database connection
    process.exit(0);
  }
}

// Run the update function
updateGuidanceCategories();
