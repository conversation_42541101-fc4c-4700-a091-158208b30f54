/**
 * <PERSON><PERSON><PERSON> to test the combined health metrics API endpoint
 */

require('dotenv').config();
const axios = require('axios');
const jwt = require('jsonwebtoken');
const pool = require('../db');

// Create a JWT token for authentication
const createToken = (userId, role) => {
  return jwt.sign(
    { user: { id: userId, role } },
    process.env.JWT_SECRET,
    { expiresIn: '1h' }
  );
};

// Function to test the combined health metrics API endpoint
const testHealthMetricsApi = async () => {
  try {
    // Get admin user ID
    const userResult = await pool.query('SELECT user_id FROM users WHERE role = \'admin\' LIMIT 1');
    
    if (userResult.rows.length === 0) {
      console.error('No admin user found');
      return;
    }
    
    const adminId = userResult.rows[0].user_id;
    
    // Get a patient ID
    const patientResult = await pool.query('SELECT patient_id FROM patients LIMIT 1');
    
    if (patientResult.rows.length === 0) {
      console.error('No patients found');
      return;
    }
    
    const patientId = patientResult.rows[0].patient_id;
    
    // Create a token
    const token = createToken(adminId, 'admin');
    
    // Set up axios with the token
    const api = axios.create({
      baseURL: 'http://localhost:5000/api',
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': token
      }
    });
    
    // Call the combined health metrics endpoint
    console.log(`Fetching combined health metrics for patient ID: ${patientId}`);
    const response = await api.get(`/visits/health-metrics/${patientId}`);
    
    // Check if the request was successful
    if (response.data.success) {
      console.log('Combined health metrics fetched successfully!');
      
      const data = response.data.data;
      console.log(`Total data points: ${data.length}`);
      console.log(`Baseline data included: ${response.data.baselineIncluded}`);
      
      // Print the first data point
      if (data.length > 0) {
        console.log('\nFirst data point:');
        console.log(`- Type: ${data[0].visit_type}`);
        console.log(`- Date: ${data[0].visit_date}`);
        
        // Print some health metrics
        console.log('\nVital Signs:');
        console.log(`- Heart rhythm: ${data[0].heart_rhythm}`);
        console.log(`- Sitting heart rate: ${data[0].sitting_heart_rate}`);
        
        console.log('\nLipid Panel:');
        console.log(`- Total cholesterol: ${data[0].cholesterol_total || data[0].total_cholesterol}`);
        console.log(`- VLDL: ${data[0].vldl}`);
        
        console.log('\nLiver Function:');
        console.log(`- Total bilirubin: ${data[0].bilirubin_t || data[0].bilirubin_total || data[0].total_bilirubin}`);
        console.log(`- Direct bilirubin: ${data[0].bilirubin_d || data[0].bilirubin_direct || data[0].direct_bilirubin}`);
        console.log(`- Total protein: ${data[0].total_protein || data[0].protein_total}`);
        console.log(`- GGT: ${data[0].ggt}`);
        
        console.log('\nComplete Blood Count:');
        console.log(`- RBC: ${data[0].rbc}`);
        console.log(`- Platelets: ${data[0].platelets || data[0].platelet_count}`);
        
        console.log('\nRBC Indices:');
        console.log(`- MCV: ${data[0].mcv}`);
        console.log(`- MCH: ${data[0].mch}`);
        console.log(`- MCHC: ${data[0].mchc}`);
        console.log(`- RDW: ${data[0].rdw}`);
        
        console.log('\nWBC Differential:');
        console.log(`- Neutrophils: ${data[0].neutrophils}`);
        console.log(`- Lymphocytes: ${data[0].lymphocytes}`);
        console.log(`- Monocytes: ${data[0].monocytes}`);
        console.log(`- Eosinophils: ${data[0].eosinophils}`);
        console.log(`- Basophils: ${data[0].basophils}`);
        
        console.log('\nUrinalysis:');
        console.log(`- Color: ${data[0].urineColor || data[0].urine_color}`);
        console.log(`- Transparency: ${data[0].urineTransparency || data[0].urine_transparency || data[0].urine_clarity}`);
        console.log(`- pH: ${data[0].urinePh || data[0].urine_ph}`);
        console.log(`- Protein: ${data[0].urineProtein || data[0].urine_protein}`);
        
        console.log('\nCancer Markers:');
        console.log(`- CA-125: ${data[0].ca125 || data[0].ca_125}`);
      }
      
      // Print the field names of the first data point
      if (data.length > 0) {
        console.log('\nAll field names in the first data point:');
        const fieldNames = Object.keys(data[0]);
        console.log(fieldNames.join(', '));
      }
    } else {
      console.error('Failed to fetch combined health metrics:', response.data);
    }
  } catch (error) {
    console.error('Error testing combined health metrics API:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  } finally {
    // Close the database connection
    await pool.end();
  }
};

// Run the test
testHealthMetricsApi();
