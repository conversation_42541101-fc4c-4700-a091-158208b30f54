/**
 * <PERSON><PERSON><PERSON> to test the patient data generator
 */

const { generateComprehensivePatientData } = require('../utils/patientDataGenerator');
require('dotenv').config();

// Generate sample patient data
const patientData = generateComprehensivePatientData();

// Print some basic info
console.log('Generated patient data:');

// Print the new metrics
console.log('Cardiovascular metrics:');
console.log('- heart_rhythm:', patientData.heartRhythm);
console.log('- sitting_heart_rate:', patientData.sittingHeartRate);
console.log('\nLipid panel:');
console.log('- vldl:', patientData.vldl);
console.log('\nAdditional blood chemistry:');
console.log('- uric_acid:', patientData.uricAcid);
console.log('\nLiver function tests:');
console.log('- ggt:', patientData.ggt);
console.log('- bilirubin_t:', patientData.bilirubin_t);
console.log('- bilirubin_d:', patientData.bilirubin_d);
console.log('- total_protein:', patientData.total_protein);
console.log('\nComplete blood count:');
console.log('- rbc:', patientData.rbc);
console.log('- platelets:', patientData.platelets);
console.log('\nRed blood cell indices:');
console.log('- mcv:', patientData.mcv);
console.log('- mch:', patientData.mch);
console.log('- mchc:', patientData.mchc);
console.log('- rdw:', patientData.rdw);
console.log('\nWhite blood cell differential:');
console.log('- neutrophils:', patientData.neutrophils);
console.log('- lymphocytes:', patientData.lymphocytes);
console.log('- monocytes:', patientData.monocytes);
console.log('- eosinophils:', patientData.eosinophils);
console.log('- basophils:', patientData.basophils);
console.log('\nIron studies:');
console.log('- iron:', patientData.iron);
console.log('\nUrinalysis:');
console.log('- urine_color:', patientData.urineColor);
console.log('- urine_transparency:', patientData.urineTransparency);
console.log('- urine_ph:', patientData.urinePh);
console.log('- urine_protein:', patientData.urineProtein);
console.log('- urine_sugar:', patientData.urineSugar);
console.log('- urine_rbcs:', patientData.urineRbcs);
console.log('- urine_pus_cells:', patientData.urinePusCells);
console.log('- urine_crystals:', patientData.urineCrystals);
console.log('- urine_casts:', patientData.urineCasts);
console.log('\nCancer markers:');
console.log('- ca125:', patientData.ca125);
