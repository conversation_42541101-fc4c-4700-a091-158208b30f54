/**
 * <PERSON><PERSON><PERSON> to populate the clinical guidance database with vital signs guidance
 */

const db = require('../db');

// Guidance content organized by context
const guidanceContent = [
  // Basic Measurements
  {
    title: "Basic Measurements Guidance",
    content: `<p><strong>Height and Weight:</strong> Measure with patient in light clothing without shoes. Calculate BMI to assess nutritional status.</p>
<p><strong>BMI Interpretation:</strong></p>
<ul>
<li>Underweight: &lt;18.5 kg/m²</li>
<li>Normal weight: 18.5-24.9 kg/m²</li>
<li>Overweight: 25-29.9 kg/m²</li>
<li>Obesity: ≥30 kg/m²</li>
</ul>
<p><strong>Temperature:</strong> Normal range is 36.1-37.2°C (97-99°F). Consider age-related changes in thermoregulation when interpreting results in older adults.</p>
<p><strong>Respiratory Rate:</strong> Normal range is 12-20 breaths/minute. Assess for pattern, depth, and effort.</p>
<p><strong>Considerations for Older Adults:</strong></p>
<ul>
<li>Weight loss of ≥5% in 1 month or ≥10% in 6 months is significant and requires evaluation</li>
<li>BMI &lt;23 kg/m² may indicate undernutrition in older adults</li>
<li>Older adults may have blunted fever response; even small elevations in temperature may indicate infection</li>
</ul>`,
    summary: "Guidance on measuring and interpreting basic vital measurements in older adults",
    category_id: 1, // Vital Signs
    context_key: "vital_signs_basic",
    is_published: true,
    tags: ["height", "weight", "BMI", "temperature", "respiratory rate", "vital signs"]
  },
  
  // Blood Pressure & Heart Rate
  {
    title: "Blood Pressure & Heart Rate Guidance",
    content: `<p><strong>Blood Pressure Measurement:</strong> Measure after 5 minutes of rest, with arm at heart level. Take readings in sitting, lying, and standing positions to assess for orthostatic hypotension.</p>
<p><strong>Blood Pressure Classification:</strong></p>
<ul>
<li>Normal: &lt;120/80 mmHg</li>
<li>Elevated: 120-129/&lt;80 mmHg</li>
<li>Hypertension Stage 1: 130-139/80-89 mmHg</li>
<li>Hypertension Stage 2: ≥140/90 mmHg</li>
<li>Hypertensive Crisis: &gt;180/120 mmHg</li>
</ul>
<p><strong>Heart Rate:</strong> Normal resting heart rate is 60-100 beats per minute. Assess for regularity and quality.</p>
<p><strong>Orthostatic Hypotension:</strong> A drop in systolic BP ≥20 mmHg or diastolic BP ≥10 mmHg within 3 minutes of standing. Common in older adults and associated with increased fall risk.</p>
<p><strong>Pulse Oximetry:</strong> Normal SpO₂ is 95-100%. Values &lt;90% indicate significant hypoxemia.</p>
<p><strong>Considerations for Older Adults:</strong></p>
<ul>
<li>Target BP may be higher in frail older adults (e.g., SBP 130-150 mmHg)</li>
<li>Isolated systolic hypertension is common</li>
<li>Orthostatic hypotension is more prevalent and may be asymptomatic</li>
<li>Medication review is essential when BP is outside target range</li>
<li>Heart rate may be affected by medications (e.g., beta-blockers)</li>
</ul>`,
    summary: "Guidance on measuring and interpreting blood pressure and heart rate in older adults",
    category_id: 1, // Vital Signs
    context_key: "vital_signs_bp_hr",
    is_published: true,
    tags: ["blood pressure", "hypertension", "heart rate", "orthostatic hypotension", "pulse oximetry", "vital signs"]
  }
];

// Function to insert guidance content
async function populateGuidance() {
  try {
    console.log('Starting to populate vital signs guidance...');

    // Insert each guidance entry
    for (const guidance of guidanceContent) {
      // Insert the guidance
      const guidanceResult = await db.query(
        `INSERT INTO clinical_guidance
         (title, content, summary, category_id, context_key, is_published, created_at, updated_at)
         VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
         RETURNING guidance_id`,
        [
          guidance.title,
          guidance.content,
          guidance.summary,
          guidance.category_id,
          guidance.context_key,
          guidance.is_published
        ]
      );

      const guidanceId = guidanceResult.rows[0].guidance_id;
      console.log(`Inserted guidance: ${guidance.title} (ID: ${guidanceId})`);

      // Insert tags if any
      if (guidance.tags && guidance.tags.length > 0) {
        for (const tag of guidance.tags) {
          // Check if tag exists
          const tagResult = await db.query(
            `SELECT tag_id FROM guidance_tags WHERE name = $1`,
            [tag]
          );

          let tagId;
          if (tagResult.rows.length > 0) {
            tagId = tagResult.rows[0].tag_id;
          } else {
            // Create new tag
            const newTagResult = await db.query(
              `INSERT INTO guidance_tags (name, created_at)
               VALUES ($1, NOW())
               RETURNING tag_id`,
              [tag]
            );
            tagId = newTagResult.rows[0].tag_id;
            console.log(`Created new tag: ${tag} (ID: ${tagId})`);
          }

          // Create tag relation
          await db.query(
            `INSERT INTO guidance_tag_relations (guidance_id, tag_id)
             VALUES ($1, $2)`,
            [guidanceId, tagId]
          );
        }
      }

      // Create initial version
      await db.query(
        `INSERT INTO guidance_versions
         (guidance_id, version_number, title, content, summary, category_id, created_at)
         VALUES ($1, 1, $2, $3, $4, $5, NOW())`,
        [
          guidanceId,
          guidance.title,
          guidance.content,
          guidance.summary,
          guidance.category_id
        ]
      );

      // Create audit entry
      await db.query(
        `INSERT INTO guidance_audit
         (guidance_id, action, details, performed_at)
         VALUES ($1, 'create', $2, NOW())`,
        [
          guidanceId,
          JSON.stringify({ title: guidance.title, context_key: guidance.context_key })
        ]
      );
    }

    console.log('Vital signs guidance population completed successfully!');
  } catch (error) {
    console.error('Error populating vital signs guidance:', error);
  } finally {
    // Close the database connection
    process.exit(0);
  }
}

// Run the population function
populateGuidance();
