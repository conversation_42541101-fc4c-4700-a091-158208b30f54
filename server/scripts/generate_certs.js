/**
 * <PERSON><PERSON><PERSON> to generate self-signed certificates for local HTTPS development
 * 
 * This script creates a 'certs' directory in the server folder and generates
 * self-signed certificates for local development with HTTPS.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Define paths
const certsDir = path.join(__dirname, '../certs');

// Create certs directory if it doesn't exist
if (!fs.existsSync(certsDir)) {
  console.log('Creating certs directory...');
  fs.mkdirSync(certsDir, { recursive: true });
}

// Check if OpenSSL is available
try {
  execSync('openssl version', { stdio: 'pipe' });
  console.log('OpenSSL is available. Proceeding with certificate generation...');
} catch (error) {
  console.error('OpenSSL is not available. Please install OpenSSL to generate certificates.');
  process.exit(1);
}

// Generate certificates
try {
  console.log('Generating self-signed certificates...');
  
  // Generate a self-signed certificate valid for 365 days
  execSync(
    `openssl req -x509 -nodes -days 365 -newkey rsa:2048 -keyout ${path.join(certsDir, 'key.pem')} -out ${path.join(certsDir, 'cert.pem')} -subj "/C=US/ST=State/L=City/O=Organization/CN=localhost"`,
    { stdio: 'inherit' }
  );
  
  console.log('Certificates generated successfully!');
  console.log(`Key: ${path.join(certsDir, 'key.pem')}`);
  console.log(`Certificate: ${path.join(certsDir, 'cert.pem')}`);
  
  // Set appropriate permissions
  if (process.platform !== 'win32') {
    console.log('Setting appropriate file permissions...');
    execSync(`chmod 600 ${path.join(certsDir, 'key.pem')}`);
    execSync(`chmod 600 ${path.join(certsDir, 'cert.pem')}`);
  }
  
  console.log('\nIMPORTANT: These certificates are self-signed and intended for local development only.');
  console.log('Browsers will show a security warning when accessing your local site.');
  console.log('You will need to manually accept the certificate in your browser.');
  
} catch (error) {
  console.error('Error generating certificates:', error.message);
  process.exit(1);
}
