/**
 * Simple test script for the health metrics
 */

// Helper function to generate random values with a chance of abnormal values
const randomWithAbnormal = (min, max, abnormalLow, abnormalHigh, decimals = 1, abnormalChance = 0.3) => {
  // Determine if this value should be abnormal
  const isAbnormal = Math.random() < abnormalChance;
  
  let value;
  
  if (isAbnormal) {
    // Generate an abnormal value (either high or low)
    const isHigh = Math.random() < 0.5;
    
    if (isHigh) {
      // Generate a high abnormal value (20-50% above max)
      const abnormalFactor = 1.2 + (Math.random() * 0.3);
      value = max * abnormalFactor;
    } else {
      // Generate a low abnormal value (20-50% below min)
      const abnormalFactor = 0.5 + (Math.random() * 0.3);
      value = min * abnormalFactor;
    }
  } else {
    // Generate a normal value
    value = min + (Math.random() * (max - min));
  }
  
  return parseFloat(value.toFixed(decimals));
};

// Heart rhythms
const heartRhythms = ['Regular', 'Irregular', 'Bradycardia', 'Tachycardia', 'Arrhythmia'];
const heartRate = 60 + Math.floor(Math.random() * 40); // 60-100 bpm
const lyingHeartRate = heartRate - Math.floor(Math.random() * 5); // Slightly lower when lying
const standingHeartRate = heartRate + Math.floor(Math.random() * 10); // Slightly higher when standing
const sittingHeartRate = heartRate - Math.floor(Math.random() * 3); // Slightly lower than standing, higher than lying
const heartRhythm = heartRhythms[Math.floor(Math.random() * heartRhythms.length)];

// Urinalysis
const urineColors = ['Pale Yellow', 'Yellow', 'Dark Yellow', 'Amber', 'Orange', 'Red', 'Brown'];
const urineColor = urineColors[Math.floor(Math.random() * urineColors.length)];

const urineTransparencies = ['Clear', 'Slightly Cloudy', 'Cloudy', 'Turbid'];
const urineTransparency = urineTransparencies[Math.floor(Math.random() * urineTransparencies.length)];

const urineProteinLevels = ['Negative', 'Trace', '1+', '2+', '3+', '4+'];
// 70% chance of Negative or Trace, 30% chance of 1+ to 4+
const urineProtein = Math.random() < 0.7 
  ? urineProteinLevels[Math.floor(Math.random() * 2)] 
  : urineProteinLevels[2 + Math.floor(Math.random() * 4)];

const urineSugarLevels = ['Negative', 'Trace', '1+', '2+', '3+', '4+'];
// 70% chance of Negative or Trace, 30% chance of 1+ to 4+
const urineSugar = Math.random() < 0.7 
  ? urineSugarLevels[Math.floor(Math.random() * 2)] 
  : urineSugarLevels[2 + Math.floor(Math.random() * 4)];

const urineRbcLevels = ['0-2/HPF', '3-5/HPF', '6-10/HPF', '>10/HPF'];
// 70% chance of 0-2/HPF, 30% chance of higher values
const urineRbcs = Math.random() < 0.7 
  ? urineRbcLevels[0] 
  : urineRbcLevels[1 + Math.floor(Math.random() * 3)];

const urinePusCellLevels = ['0-5/HPF', '6-10/HPF', '11-20/HPF', '>20/HPF'];
// 70% chance of 0-5/HPF, 30% chance of higher values
const urinePusCells = Math.random() < 0.7 
  ? urinePusCellLevels[0] 
  : urinePusCellLevels[1 + Math.floor(Math.random() * 3)];

const urineCrystalTypes = ['None', 'Calcium Oxalate', 'Uric Acid', 'Triple Phosphate', 'Amorphous'];
// 70% chance of None, 30% chance of crystals
const urineCrystals = Math.random() < 0.7 
  ? urineCrystalTypes[0] 
  : urineCrystalTypes[1 + Math.floor(Math.random() * 4)];

const urineCastTypes = ['None', 'Hyaline', 'Granular', 'Waxy', 'RBC', 'WBC'];
// 70% chance of None, 30% chance of casts
const urineCasts = Math.random() < 0.7 
  ? urineCastTypes[0] 
  : urineCastTypes[1 + Math.floor(Math.random() * 5)];

// Generate the metrics
const metrics = {
  // Cardiovascular metrics
  heartRhythm,
  sittingHeartRate,
  
  // Lipid panel
  vldl: randomWithAbnormal(5, 40, 2, 60, 0),
  
  // Additional blood chemistry
  uric_acid: randomWithAbnormal(3.5, 7.2, 2.0, 10.0, 1),
  
  // Liver function tests
  ggt: randomWithAbnormal(8, 61, 4, 100, 0),
  bilirubin_t: randomWithAbnormal(0.1, 1.2, 0.05, 3.0, 2),
  bilirubin_d: randomWithAbnormal(0.0, 0.3, 0.0, 1.0, 2),
  total_protein: randomWithAbnormal(6.0, 8.3, 4.0, 10.0, 1),
  
  // Complete blood count
  rbc: randomWithAbnormal(4.5, 5.9, 3.0, 7.0, 2),
  platelets: randomWithAbnormal(150, 450, 50, 700, 0),
  
  // Red blood cell indices
  mcv: randomWithAbnormal(80, 100, 60, 120, 1),
  mch: randomWithAbnormal(27, 33, 20, 40, 1),
  mchc: randomWithAbnormal(32, 36, 25, 40, 1),
  rdw: randomWithAbnormal(11.5, 14.5, 9.0, 20.0, 1),
  
  // White blood cell differential
  neutrophils: randomWithAbnormal(40, 60, 20, 80, 0),
  lymphocytes: randomWithAbnormal(20, 40, 10, 60, 0),
  monocytes: randomWithAbnormal(2, 8, 1, 15, 0),
  eosinophils: randomWithAbnormal(1, 4, 0, 10, 0),
  basophils: randomWithAbnormal(0.5, 1, 0, 3, 1),
  
  // Iron studies
  iron: randomWithAbnormal(60, 170, 30, 250, 0),
  
  // Urinalysis
  urineColor,
  urineTransparency,
  urinePh: randomWithAbnormal(4.5, 8.0, 4.0, 9.0, 1),
  urineProtein,
  urineSugar,
  urineRbcs,
  urinePusCells,
  urineCrystals,
  urineCasts,
  
  // Cancer markers
  ca125: randomWithAbnormal(0, 35, 0, 200, 0)
};

// Print the metrics
console.log('\nCardiovascular metrics:');
console.log('- heart_rhythm:', metrics.heartRhythm);
console.log('- sitting_heart_rate:', metrics.sittingHeartRate);

console.log('\nLipid panel:');
console.log('- vldl:', metrics.vldl);

console.log('\nAdditional blood chemistry:');
console.log('- uric_acid:', metrics.uric_acid);

console.log('\nLiver function tests:');
console.log('- ggt:', metrics.ggt);
console.log('- bilirubin_t:', metrics.bilirubin_t);
console.log('- bilirubin_d:', metrics.bilirubin_d);
console.log('- total_protein:', metrics.total_protein);

console.log('\nComplete blood count:');
console.log('- rbc:', metrics.rbc);
console.log('- platelets:', metrics.platelets);

console.log('\nRed blood cell indices:');
console.log('- mcv:', metrics.mcv);
console.log('- mch:', metrics.mch);
console.log('- mchc:', metrics.mchc);
console.log('- rdw:', metrics.rdw);

console.log('\nWhite blood cell differential:');
console.log('- neutrophils:', metrics.neutrophils);
console.log('- lymphocytes:', metrics.lymphocytes);
console.log('- monocytes:', metrics.monocytes);
console.log('- eosinophils:', metrics.eosinophils);
console.log('- basophils:', metrics.basophils);

console.log('\nIron studies:');
console.log('- iron:', metrics.iron);

console.log('\nUrinalysis:');
console.log('- urineColor:', metrics.urineColor);
console.log('- urineTransparency:', metrics.urineTransparency);
console.log('- urinePh:', metrics.urinePh);
console.log('- urineProtein:', metrics.urineProtein);
console.log('- urineSugar:', metrics.urineSugar);
console.log('- urineRbcs:', metrics.urineRbcs);
console.log('- urinePusCells:', metrics.urinePusCells);
console.log('- urineCrystals:', metrics.urineCrystals);
console.log('- urineCasts:', metrics.urineCasts);

console.log('\nCancer markers:');
console.log('- ca125:', metrics.ca125);
