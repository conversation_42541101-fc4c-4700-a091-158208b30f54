const pool = require('../db');

// Sample data for access logs
async function addAccessLogs() {
  console.log('Adding sample access logs...');

  try {
    // First, check if tables exist
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'patient_access_logs'
      ) as exists
    `);

    if (!tableExists.rows[0].exists) {
      console.log('patient_access_logs table does not exist, creating...');
      await pool.query(`
        CREATE TABLE patient_access_logs (
          log_id SERIAL PRIMARY KEY,
          patient_id INTEGER NOT NULL,
          user_id INTEGER NOT NULL,
          access_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          access_type VARCHAR(50) NOT NULL,
          ip_address VARCHAR(50),
          user_agent TEXT
        )
      `);
    }

    // Get some valid patient IDs
    const patients = await pool.query('SELECT patient_id FROM patients LIMIT 5');
    // Get some valid user IDs
    const users = await pool.query('SELECT user_id FROM users LIMIT 5');

    if (patients.rows.length === 0 || users.rows.length === 0) {
      console.log('No patients or users found in the database');
      return;
    }

    // Create sample access logs
    const accessTypes = ['view', 'search', 'export', 'print'];
    const ipAddresses = ['***********', '********', '**********', '127.0.0.1'];
    const userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/98.0.4758.102 Safari/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.3 Safari/605.1.15',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:97.0) Gecko/20100101 Firefox/97.0'
    ];

    // Insert random log entries
    for (let i = 0; i < 15; i++) {
      const patientId = patients.rows[Math.floor(Math.random() * patients.rows.length)].patient_id;
      const userId = users.rows[Math.floor(Math.random() * users.rows.length)].user_id;
      const accessType = accessTypes[Math.floor(Math.random() * accessTypes.length)];
      const ipAddress = ipAddresses[Math.floor(Math.random() * ipAddresses.length)];
      const userAgent = userAgents[Math.floor(Math.random() * userAgents.length)];
      const accessTime = new Date(Date.now() - Math.floor(Math.random() * 7 * 24 * 60 * 60 * 1000)); // Random time in the last week

      await pool.query(`
        INSERT INTO patient_access_logs (patient_id, user_id, access_time, access_type, ip_address, user_agent)
        VALUES ($1, $2, $3, $4, $5, $6)
      `, [patientId, userId, accessTime, accessType, ipAddress, userAgent]);
    }

    console.log('Added 15 sample access logs');

  } catch (err) {
    console.error('Error adding access logs:', err);
  }
}

// Sample data for edit logs
async function addEditLogs() {
  console.log('Adding sample edit logs...');

  try {
    // First, check if tables exist
    const tableExists = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'patient_edit_logs'
      ) as exists
    `);

    if (!tableExists.rows[0].exists) {
      console.log('patient_edit_logs table does not exist, creating...');
      await pool.query(`
        CREATE TABLE patient_edit_logs (
          edit_id SERIAL PRIMARY KEY,
          patient_id INTEGER NOT NULL,
          user_id INTEGER NOT NULL,
          edit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          field_changed VARCHAR(100) NOT NULL,
          old_value TEXT,
          new_value TEXT
        )
      `);
    }

    // Get some valid patient IDs
    const patients = await pool.query('SELECT patient_id FROM patients LIMIT 5');
    // Get some valid user IDs
    const users = await pool.query('SELECT user_id FROM users LIMIT 5');

    if (patients.rows.length === 0 || users.rows.length === 0) {
      console.log('No patients or users found in the database');
      return;
    }

    // Create sample edit logs
    const fields = ['temperature', 'blood_pressure', 'heart_rate', 'weight', 'height', 'medication'];
    const oldValues = ['98.6', '120/80', '72', '185', '6.0', 'Aspirin 85mg'];
    const newValues = ['99.1', '125/82', '75', '180', '6.1', 'Aspirin 100mg'];

    // Insert random log entries
    for (let i = 0; i < 10; i++) {
      const patientId = patients.rows[Math.floor(Math.random() * patients.rows.length)].patient_id;
      const userId = users.rows[Math.floor(Math.random() * users.rows.length)].user_id;
      const fieldIndex = Math.floor(Math.random() * fields.length);
      const fieldChanged = fields[fieldIndex];
      const oldValue = oldValues[fieldIndex];
      const newValue = newValues[fieldIndex];
      const editTime = new Date(Date.now() - Math.floor(Math.random() * 14 * 24 * 60 * 60 * 1000)); // Random time in the last 2 weeks

      await pool.query(`
        INSERT INTO patient_edit_logs (patient_id, user_id, edit_time, field_changed, old_value, new_value)
        VALUES ($1, $2, $3, $4, $5, $6)
      `, [patientId, userId, editTime, fieldChanged, oldValue, newValue]);
    }

    console.log('Added 10 sample edit logs');

  } catch (err) {
    console.error('Error adding edit logs:', err);
  }
}

// Run the functions
(async function() {
  try {
    await addAccessLogs();
    await addEditLogs();
    console.log('Finished adding sample log data');
    process.exit(0);
  } catch (err) {
    console.error('Error in main function:', err);
    process.exit(1);
  }
})();