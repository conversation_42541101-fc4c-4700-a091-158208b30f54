const pool = require('../db');

async function checkPasswordPolicy() {
  try {
    console.log('Checking current password policy...');
    
    const result = await pool.query('SELECT * FROM password_policy ORDER BY policy_id DESC LIMIT 1');
    
    if (result.rows.length === 0) {
      console.log('No password policy found.');
    } else {
      console.log('Current password policy:');
      console.table(result.rows[0]);
    }
    
    process.exit(0);
  } catch (err) {
    console.error('Error:', err);
    process.exit(1);
  }
}

checkPasswordPolicy();
