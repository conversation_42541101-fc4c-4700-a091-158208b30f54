const pool = require('../db');

async function setupDoctor() {
  try {
    // First, get the user record for smith_john
    const userResult = await pool.query(
      'SELECT user_id, email FROM users WHERE username = $1',
      ['smith_john']
    );

    if (userResult.rows.length === 0) {
      console.error('User smith_john not found');
      return;
    }

    const user = userResult.rows[0];
    console.log('Found user:', user);

    // Check if doctor record exists
    const doctorResult = await pool.query(
      'SELECT doctor_id FROM doctors WHERE email = $1',
      [user.email]
    );

    if (doctorResult.rows.length === 0) {
      // Create doctor record
      const newDoctorResult = await pool.query(
        `INSERT INTO doctors (
          first_name,
          last_name,
          email,
          phone,
          specialty,
          created_at,
          updated_at
        ) VALUES ($1, $2, $3, $4, $5, NOW(), NOW()) RETURNING doctor_id`,
        ['<PERSON>', '<PERSON>', user.email, '555-0123', 'General Practice']
      );

      console.log('Created new doctor record:', newDoctorResult.rows[0]);
    } else {
      console.log('Doctor record already exists:', doctorResult.rows[0]);
    }

    // Update user role to doctor if not already
    await pool.query(
      'UPDATE users SET role = $1 WHERE user_id = $2',
      ['doctor', user.user_id]
    );

    console.log('Setup completed successfully');
  } catch (err) {
    console.error('Error:', err);
  } finally {
    await pool.end();
  }
}

setupDoctor();