/**
 * <PERSON><PERSON>t to test the health metrics API endpoint
 */

const axios = require('axios');
const jwt = require('jsonwebtoken');
require('dotenv').config();

// Create a JWT token for testing
const createToken = (userId, role) => {
  // Match the structure expected by the auth middleware
  const payload = {
    user: {
      id: userId,
      username: 'testuser',
      role: role,
      patient_id: null // Set to null for admin/doctor roles
    }
  };

  console.log('Creating token with payload:', payload);
  console.log('JWT_SECRET:', process.env.JWT_SECRET ? 'exists' : 'missing');

  return jwt.sign(
    payload,
    process.env.JWT_SECRET || 'fallbacksecret',
    { expiresIn: '1h' }
  );
};

// Test the health metrics API endpoint
const testHealthMetricsApi = async () => {
  try {
    // Create a token for an admin user
    const token = createToken(1, 'admin');

    // Set up axios with the token
    const api = axios.create({
      baseURL: 'http://localhost:5000/api',
      headers: {
        'Content-Type': 'application/json',
        'x-auth-token': token
      }
    });

    // Get a patient ID to test with
    const patientsResponse = await api.get('/patients');
    if (!patientsResponse.data || patientsResponse.data.length === 0) {
      console.error('No patients found');
      return;
    }

    const patientId = patientsResponse.data[0].patient_id;
    console.log(`Testing health metrics API for patient ID: ${patientId}`);

    // Call the health metrics API endpoint
    const response = await api.get(`/visits/health-metrics/${patientId}`);

    console.log('API Response:', {
      success: response.data.success,
      dataLength: response.data.data ? response.data.data.length : 0,
      baselineIncluded: response.data.baselineIncluded
    });

    // Print the first data point if available
    if (response.data.data && response.data.data.length > 0) {
      console.log('First data point:', {
        visit_type: response.data.data[0].visit_type,
        visit_date: response.data.data[0].visit_date,
        weight: response.data.data[0].weight,
        height: response.data.data[0].height,
        bmi: response.data.data[0].bmi
      });
    }

    console.log('API test completed successfully');
  } catch (error) {
    console.error('Error testing API:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
  }
};

// Run the test
testHealthMetricsApi();
