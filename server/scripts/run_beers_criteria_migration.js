/**
 * <PERSON><PERSON><PERSON> to run the BEERS criteria migration
 * This script creates the BEERS criteria table and adds related fields to the patient_visits table
 */

const fs = require('fs');
const path = require('path');
const pool = require('../db');

async function runMigration() {
  const client = await pool.connect();
  try {
    console.log('Running BEERS criteria migration...');
    
    // Read the migration SQL file
    const migrationPath = path.join(__dirname, '../migrations/050_create_beers_criteria.sql');
    console.log('Reading migration file from:', migrationPath);
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Run the migration
    console.log('Executing migration SQL...');
    const result = await client.query(migrationSQL);
    console.log('Migration SQL executed successfully');
    
    console.log('✅ BEERS criteria migration completed successfully!');
    
    // Now populate the table with sample data
    console.log('Populating BEERS criteria table with sample data...');
    
    // Import the sample data
    const populateScript = require('./populate_beers_criteria');
    
    console.log('✅ BEERS criteria implementation is now ready to use!');
    
  } catch (error) {
    console.error('❌ Error running BEERS criteria migration:', error);
  } finally {
    client.release();
    await pool.end();
    console.log('Database connection closed');
  }
}

// Run the migration
runMigration();
