/**
 * Simple test script for the patient data generator
 */

const patientGenerator = require('../utils/patientDataGenerator');

console.log('Available functions:', Object.keys(patientGenerator));

const patientData = patientGenerator.generateComprehensivePatientData();

console.log('Generated patient data:');
console.log('- heart_rhythm:', patientData.heartRhythm);
console.log('- sitting_heart_rate:', patientData.sittingHeartRate);
console.log('- vldl:', patientData.vldl);
console.log('- uric_acid:', patientData.uricAcid);
console.log('- urinalysis:', {
  color: patientData.urineColor,
  transparency: patientData.urineTransparency,
  ph: patientData.urinePh,
  protein: patientData.urineProtein
});
