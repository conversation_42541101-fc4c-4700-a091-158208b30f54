const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Create a new pool using the connection string from .env
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
});

async function updateSchema() {
  const client = await pool.connect();
  
  try {
    console.log('Connected to database. Running schema update...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../sql/update_patient_edit_logs.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    await client.query(sql);
    
    console.log('Schema update completed successfully!');
    console.log('The patient_edit_logs table has been updated with new columns for enhanced logging.');
  } catch (err) {
    console.error('Error updating schema:', err);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the update
updateSchema();
