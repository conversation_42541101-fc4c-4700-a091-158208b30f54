const pool = require('../db');
const bcrypt = require('bcryptjs');

async function createPatientUser() {
  try {
    // Choose a patient to link
    const patientResult = await pool.query('SELECT patient_id, first_name, last_name FROM patients WHERE patient_id = 4');
    const patient = patientResult.rows[0];
    
    if (!patient) {
      console.log('No patient found');
      return;
    }
    
    console.log('Selected patient:', patient);
    
    // Create a username from patient's name
    const username = `${patient.first_name.toLowerCase()}_${patient.last_name.toLowerCase()}`;
    const email = `${patient.first_name.toLowerCase()}.${patient.last_name.toLowerCase()}@example.com`;
    const password = 'patient123';
    
    // Hash the password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);
    
    // Create the user
    const userResult = await pool.query(
      'INSERT INTO users (username, email, password, role) VALUES ($1, $2, $3, $4) RETURNING *',
      [username, email, hashedPassword, 'patient']
    );
    
    const user = userResult.rows[0];
    console.log('Created user:', user);
    
    // Now we need to add a user_id column to the patients table
    try {
      // Check if user_id column exists
      const columnCheckResult = await pool.query(`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'patients' AND column_name = 'user_id'
      `);
      
      if (columnCheckResult.rows.length === 0) {
        // Add the user_id column if it doesn't exist
        console.log('Adding user_id column to patients table');
        await pool.query('ALTER TABLE patients ADD COLUMN user_id INTEGER REFERENCES users(user_id)');
      }
      
      // Link the user to the patient
      await pool.query('UPDATE patients SET user_id = $1 WHERE patient_id = $2', [user.user_id, patient.patient_id]);
      console.log(`Linked user ID ${user.user_id} to patient ID ${patient.patient_id}`);
      
      console.log('---------------------');
      console.log('PATIENT LOGIN DETAILS');
      console.log('---------------------');
      console.log(`Username: ${username}`);
      console.log(`Email: ${email}`);
      console.log(`Password: ${password}`);
      console.log('---------------------');
    } catch (err) {
      console.error('Error linking patient to user:', err);
    }
  } catch (err) {
    console.error('Error creating patient user:', err);
  } finally {
    // Close the pool
    pool.end();
  }
}

createPatientUser(); 