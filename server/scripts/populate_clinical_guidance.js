/**
 * <PERSON><PERSON><PERSON> to populate the clinical guidance tables with existing guidance content
 *
 * Run with: node populate_clinical_guidance.js
 */

const db = require('../db');

// Guidance content organized by context
const guidanceContent = [
  // Basic Measurements
  {
    title: "Basic Measurements",
    content: `<p><strong>BMI:</strong> Underweight &lt;18.5, Normal 18.5-24.9, Overweight 25-29.9, Obese ≥30. For older adults, a slightly higher BMI (23-30) may be acceptable.</p>
<p><strong>Temperature:</strong> Normal range is 36.1-37.2°C. Fever is defined as ≥38.0°C. Hypothermia is &lt;35.0°C.</p>
<p><strong>Respiratory Rate:</strong> Normal range for adults is 12-20 breaths/min. Tachypnea is &gt;20 breaths/min.</p>
<p><strong>Oxygen Saturation:</strong> Normal is ≥95%. Values 90-94% indicate mild hypoxemia, &lt;90% severe hypoxemia.</p>`,
    summary: "Guidance on interpreting basic measurements including BMI, temperature, respiratory rate, and oxygen saturation",
    category_id: 3, // Vital Signs
    context_key: "basic_measurements",
    is_published: true,
    tags: ["BMI", "temperature", "respiratory rate", "oxygen saturation"]
  },

  // Blood Pressure & Heart Rate
  {
    title: "Blood Pressure & Heart Rate",
    content: `<p><strong>Blood Pressure:</strong> Normal is &lt;120/80 mmHg. Elevated is 120-129/&lt;80 mmHg. Hypertension Stage 1 is 130-139/80-89 mmHg. Hypertension Stage 2 is ≥140/90 mmHg.</p>
<p><strong>Heart Rate:</strong> Normal resting heart rate for adults is 60-100 bpm. Athletes may have lower rates (40-60 bpm).</p>
<p><strong>Orthostatic Hypotension:</strong> A drop in systolic BP ≥20 mmHg or diastolic BP ≥10 mmHg within 3 minutes of standing.</p>
<p><strong>Orthostatic Tachycardia:</strong> An increase in heart rate ≥30 bpm within 10 minutes of standing.</p>`,
    summary: "Guidance on interpreting blood pressure and heart rate measurements",
    category_id: 3, // Vital Signs
    context_key: "bp_heart_rate",
    is_published: true,
    tags: ["blood pressure", "heart rate", "orthostatic hypotension", "orthostatic tachycardia"]
  },
  // Lab Results - Diabetes and Metabolic Markers
  {
    title: "Diabetes Markers",
    content: `<p><strong>Blood Glucose:</strong> Normal fasting range is 70-99 mg/dL. Values between 100-125 mg/dL indicate prediabetes, while ≥126 mg/dL suggests diabetes.</p>
<p><strong>HbA1c:</strong> Normal is &lt;5.7%. Prediabetes: 5.7-6.4%. Diabetes: ≥6.5%. For older adults, less stringent targets (7-8%) may be appropriate.</p>`,
    summary: "Guidance on interpreting blood glucose and HbA1c levels in older adults",
    category_id: 2, // Lab Results
    context_key: "lab_results_diabetes",
    is_published: true,
    tags: ["diabetes", "blood glucose", "HbA1c", "metabolic"]
  },

  // Lab Results - Lipid Profile
  {
    title: "Lipid Profile",
    content: `<p><strong>Total Cholesterol:</strong> Desirable: &lt;200 mg/dL. Borderline high: 200-239 mg/dL. High: ≥240 mg/dL.</p>
<p><strong>LDL Cholesterol:</strong> Optimal: &lt;100 mg/dL. Near optimal: 100-129 mg/dL. Borderline high: 130-159 mg/dL. High: 160-189 mg/dL. Very high: ≥190 mg/dL.</p>
<p><strong>HDL Cholesterol:</strong> Low: &lt;40 mg/dL (men), &lt;50 mg/dL (women). High: ≥60 mg/dL (protective).</p>`,
    summary: "Guidance on interpreting lipid profile results in older adults",
    category_id: 2, // Lab Results
    context_key: "lab_results_lipids",
    is_published: true,
    tags: ["cholesterol", "lipids", "LDL", "HDL"]
  },

  // Lab Results - Liver Function
  {
    title: "Liver Function",
    content: `<p><strong>ALT/SGPT (Alanine Transaminase):</strong> Normal range is 7-56 U/L for men and 7-45 U/L for women. Elevated levels may indicate liver damage. ALT and SGPT are two names for the same enzyme.</p>
<p><strong>AST/SGOT (Aspartate Transaminase):</strong> Normal range is 8-48 U/L for men and 8-43 U/L for women. Elevated with liver damage, but less specific than ALT. AST and SGOT are two names for the same enzyme.</p>
<p><strong>ALP (Alkaline Phosphatase):</strong> Normal range is 40-129 U/L for adults. Elevated in liver disease, bone disorders, or bile duct obstruction.</p>
<p><strong>GGT (Gamma-Glutamyl Transferase):</strong> Normal range is 8-61 U/L for men and 5-36 U/L for women. Sensitive to alcohol use and certain medications.</p>
<p><strong>Total Bilirubin:</strong> Normal range is 0.1-1.2 mg/dL. Elevated levels may indicate liver disease, bile duct obstruction, or hemolytic anemia.</p>
<p><strong>Direct Bilirubin:</strong> Normal range is 0.0-0.3 mg/dL. Elevated levels may indicate liver disease or bile duct obstruction.</p>
<p><strong>Albumin:</strong> Normal range is 3.5-5.0 g/dL. Low levels may indicate chronic liver disease, malnutrition, or protein loss.</p>
<p><strong>Total Protein:</strong> Normal range is 6.0-8.3 g/dL. Includes albumin and globulins. Abnormal levels may indicate liver disease, kidney disease, or nutritional status.</p>`,
    summary: "Guidance on interpreting liver function test results in older adults",
    category_id: 2, // Lab Results
    context_key: "lab_results_liver",
    is_published: true,
    tags: ["liver", "ALT", "AST", "enzymes", "bilirubin", "albumin", "protein"]
  },

  // Lab Results - Kidney Function Tests
  {
    title: "Kidney Function Tests",
    content: `<p><strong>Creatinine:</strong> Normal range is 0.7-1.3 mg/dL for men and 0.6-1.1 mg/dL for women. Elevated levels suggest kidney dysfunction.</p>
<p><strong>eGFR:</strong> Normal is ≥90 mL/min/1.73m². Values 60-89 indicate mild kidney damage, 30-59 moderate damage, 15-29 severe damage, and &lt;15 kidney failure.</p>
<p><strong>Blood Urea Nitrogen (BUN):</strong> Normal range is 7-20 mg/dL. Elevated levels may indicate kidney dysfunction, dehydration, or high protein intake.</p>
<p><strong>Uric Acid:</strong> Normal range is 3.5-7.2 mg/dL for men and 2.6-6.0 mg/dL for women. Elevated levels are associated with gout and kidney stones.</p>
<p><strong>Urine Albumin-Creatinine Ratio:</strong> Normal is &lt;30 mg/g. Values 30-300 mg/g indicate microalbuminuria, while &gt;300 mg/g indicates macroalbuminuria.</p>
<p><strong>Important for BEERS Criteria:</strong> Entering kidney function data (eGFR and creatinine) is essential for proper medication alerts. Some medications will only trigger BEERS Criteria alerts when kidney function is impaired.</p>`,
    summary: "Guidance on interpreting kidney function test results in older adults",
    category_id: 2, // Lab Results
    context_key: "lab_results_kidney",
    is_published: true,
    tags: ["kidney", "creatinine", "eGFR", "BUN", "uric acid", "albumin-creatinine ratio"]
  },

  // Lab Results - Inflammatory Markers
  {
    title: "Inflammatory Markers",
    content: `<p><strong>CRP (C-Reactive Protein):</strong> Normal is &lt;3.0 mg/L. Elevated in inflammation, infection, and cardiovascular disease risk.</p>
<p><strong>ESR (Erythrocyte Sedimentation Rate):</strong> Normal ranges vary by age and gender. Generally &lt;20 mm/hr for men and &lt;30 mm/hr for women. Non-specific marker of inflammation.</p>
<p><strong>Clinical Significance:</strong> Elevated inflammatory markers in older adults may indicate underlying chronic disease, infection, or malignancy. Consider baseline values for each patient.</p>`,
    summary: "Guidance on interpreting inflammatory markers in older adults",
    category_id: 2, // Lab Results
    context_key: "lab_results_inflammation",
    is_published: true,
    tags: ["inflammation", "CRP", "ESR"]
  },

  // Lab Results - Cancer Screening
  {
    title: "Cancer Screening",
    content: `<p><strong>PSA (Prostate-Specific Antigen):</strong> Normal is generally &lt;4.0 ng/mL, but age-specific ranges exist. Screening recommendations vary; discuss risks/benefits with patients.</p>
<p><strong>CA-125 (Cancer Antigen 125):</strong> Normal is generally &lt;35 U/mL. Primarily used to monitor ovarian cancer treatment response, but may be used as part of screening in high-risk women. Can be elevated in other conditions including endometriosis, fibroids, and other cancers.</p>
<p><strong>Cancer Screening for Older Adults:</strong> Consider life expectancy, comorbidities, and patient preferences when recommending cancer screenings.</p>
<p><strong>Recommended Screenings by Age:</strong> Colorectal (until age 75), breast (until age 74), cervical (until age 65 with adequate prior screening), lung (ages 50-80 with smoking history), and prostate (individualized decision).</p>`,
    summary: "Guidance on cancer screening markers and recommendations for older adults",
    category_id: 2, // Lab Results
    context_key: "lab_results_cancer",
    is_published: true,
    tags: ["cancer", "screening", "PSA", "CA-125"]
  },

  // Mental Health - Cognitive Assessment
  {
    title: "Cognitive Assessment",
    content: `<p><strong>Mini-Cog:</strong> A brief cognitive screening tool that includes a 3-item recall test and a clock drawing test. Scores range from 0-5, with scores ≤2 suggesting possible cognitive impairment.</p>
<p><strong>Interpretation:</strong> The Mini-Cog is not diagnostic but helps identify patients who may need further cognitive evaluation. Consider factors like education, language barriers, and sensory impairments when interpreting results.</p>
<p><strong>Follow-up:</strong> For patients with scores ≤2, consider referral for comprehensive cognitive assessment, including detailed neuropsychological testing and brain imaging if appropriate.</p>`,
    summary: "Guidance on cognitive assessment using Mini-Cog in older adults",
    category_id: 5, // Mental Health
    context_key: "mental_health_cognitive",
    is_published: true,
    tags: ["cognitive", "Mini-Cog", "dementia", "screening"]
  },

  // Sleep Assessment - PSQI
  {
    title: "Pittsburgh Sleep Quality Index (PSQI)",
    content: `<p><strong>PSQI Overview:</strong> The Pittsburgh Sleep Quality Index is a self-rated questionnaire that assesses sleep quality and disturbances over a 1-month time interval. Scores range from 0-21, with higher scores indicating worse sleep quality.</p>
<p><strong>Interpretation:</strong> A global PSQI score &gt;5 indicates poor sleep quality. Scores can be used to monitor sleep quality over time and response to interventions.</p>
<p><strong>Components:</strong> The PSQI measures seven components: subjective sleep quality, sleep latency, sleep duration, habitual sleep efficiency, sleep disturbances, use of sleeping medication, and daytime dysfunction.</p>
<p><strong>Clinical Relevance:</strong> Poor sleep quality is associated with numerous health problems in older adults, including cognitive decline, depression, cardiovascular disease, and increased fall risk.</p>`,
    summary: "Guidance on using and interpreting the Pittsburgh Sleep Quality Index in older adults",
    category_id: 6, // Sleep Assessment
    context_key: "sleep_assessment_psqi",
    is_published: true,
    tags: ["sleep", "PSQI", "insomnia", "assessment"]
  },

  // Sleep Assessment - General
  {
    title: "Sleep in Older Adults",
    content: `<p><strong>Sleep Changes in Aging:</strong> Older adults often experience changes in sleep patterns, including earlier bedtimes, earlier morning awakening, decreased total sleep time, and increased sleep fragmentation.</p>
<p><strong>Common Sleep Disorders:</strong> Insomnia, sleep apnea, restless legs syndrome, and REM sleep behavior disorder are more common in older adults.</p>
<p><strong>Impact on Health:</strong> Poor sleep quality is associated with cognitive decline, depression, cardiovascular disease, metabolic disorders, and increased fall risk.</p>
<p><strong>Assessment Approach:</strong> Evaluate sleep patterns, difficulties initiating or maintaining sleep, daytime sleepiness, and use of sleep medications.</p>`,
    summary: "General guidance on sleep changes and disorders in older adults",
    category_id: 6, // Sleep Assessment
    context_key: "sleep_assessment_general",
    is_published: true,
    tags: ["sleep", "aging", "insomnia", "sleep disorders"]
  },

  // Vital Signs - Blood Pressure
  {
    title: "Blood Pressure Assessment",
    content: `<p><strong>Normal Blood Pressure:</strong> Systolic 90-140 mmHg, Diastolic 60-90 mmHg.</p>
<p><strong>Hypertension:</strong> Systolic ≥140 mmHg or Diastolic ≥90 mmHg. In older adults, treatment targets may be less aggressive (e.g., &lt;150/90 mmHg for adults ≥60 years).</p>
<p><strong>Orthostatic Hypotension:</strong> A drop in systolic BP ≥20 mmHg or diastolic BP ≥10 mmHg within 3 minutes of standing. Common in older adults and associated with increased fall risk.</p>
<p><strong>Measurement Considerations:</strong> Measure BP in both arms at initial assessment. Ensure proper cuff size and patient positioning. Consider home BP monitoring and ambulatory BP monitoring for better assessment.</p>`,
    summary: "Guidance on blood pressure assessment and interpretation in older adults",
    category_id: 3, // Vital Signs
    context_key: "vital_signs_bp",
    is_published: true,
    tags: ["blood pressure", "hypertension", "orthostatic hypotension"]
  },

  // Vital Signs - Heart Rate
  {
    title: "Heart Rate Assessment",
    content: `<p><strong>Normal Heart Rate:</strong> 60-100 beats per minute (bpm) at rest.</p>
<p><strong>Bradycardia:</strong> Heart rate &lt;60 bpm. May be normal in physically fit individuals but can indicate conduction system disease or medication effects in older adults.</p>
<p><strong>Tachycardia:</strong> Heart rate &gt;100 bpm. May indicate anxiety, pain, infection, dehydration, anemia, hyperthyroidism, or cardiac conditions.</p>
<p><strong>Heart Rate Variability:</strong> Decreased with aging and associated with increased cardiovascular risk. Medications like beta-blockers affect heart rate response.</p>`,
    summary: "Guidance on heart rate assessment and interpretation in older adults",
    category_id: 3, // Vital Signs
    context_key: "vital_signs_hr",
    is_published: true,
    tags: ["heart rate", "bradycardia", "tachycardia", "arrhythmia"]
  },

  // Frailty Assessment
  {
    title: "Frailty Assessment",
    content: `<p><strong>Frailty Definition:</strong> A clinical syndrome characterized by decreased physiological reserve and increased vulnerability to stressors, leading to adverse health outcomes.</p>
<p><strong>Assessment Components:</strong> Physical measurements (grip strength, gait speed, weight loss), cognitive function, and psychosocial factors.</p>
<p><strong>Frailty Risk Factors:</strong> Advanced age, chronic diseases, malnutrition, physical inactivity, cognitive impairment, and social isolation.</p>
<p><strong>Interventions:</strong> Exercise programs, nutritional support, medication review, cognitive stimulation, and social engagement can help prevent or manage frailty.</p>`,
    summary: "Guidance on frailty assessment and management in older adults",
    category_id: 4, // Frailty Assessment
    context_key: "frailty_assessment",
    is_published: true,
    tags: ["frailty", "geriatric assessment", "physical function"]
  },

  // Falls Risk Assessment
  {
    title: "Falls Risk Assessment",
    content: `<p><strong>Falls Risk Factors:</strong> Previous falls, gait/balance problems, muscle weakness, visual impairment, polypharmacy (especially psychoactive medications), environmental hazards, and chronic conditions.</p>
<p><strong>Assessment Tools:</strong> The Falls Risk Assessment Tool (FRAT) evaluates recent falls, medications, psychological factors, and cognitive status to determine risk level.</p>
<p><strong>Risk Stratification:</strong> Low risk (0-5 points), Medium risk (6-13 points), High risk (14-20 points).</p>
<p><strong>Interventions:</strong> Exercise programs focusing on balance and strength, medication review, vision correction, home safety modifications, and management of underlying conditions.</p>`,
    summary: "Guidance on falls risk assessment and prevention in older adults",
    category_id: 7, // Falls Risk
    context_key: "falls_risk",
    is_published: true,
    tags: ["falls", "balance", "FRAT", "prevention"]
  }
];

// Function to insert guidance content
async function populateGuidance() {
  try {
    console.log('Starting to populate clinical guidance tables...');

    // Insert each guidance entry
    for (const guidance of guidanceContent) {
      // Insert the guidance
      const guidanceResult = await db.query(
        `INSERT INTO clinical_guidance
         (title, content, summary, category_id, context_key, is_published, created_at, updated_at)
         VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
         RETURNING guidance_id`,
        [
          guidance.title,
          guidance.content,
          guidance.summary,
          guidance.category_id,
          guidance.context_key,
          guidance.is_published
        ]
      );

      const guidanceId = guidanceResult.rows[0].guidance_id;
      console.log(`Inserted guidance: ${guidance.title} (ID: ${guidanceId})`);

      // Insert tags if any
      if (guidance.tags && guidance.tags.length > 0) {
        for (const tag of guidance.tags) {
          // Check if tag exists
          const tagResult = await db.query(
            `SELECT tag_id FROM guidance_tags WHERE name = $1`,
            [tag]
          );

          let tagId;
          if (tagResult.rows.length > 0) {
            tagId = tagResult.rows[0].tag_id;
          } else {
            // Create new tag
            const newTagResult = await db.query(
              `INSERT INTO guidance_tags (name, created_at)
               VALUES ($1, NOW())
               RETURNING tag_id`,
              [tag]
            );
            tagId = newTagResult.rows[0].tag_id;
            console.log(`Created new tag: ${tag} (ID: ${tagId})`);
          }

          // Create tag relation
          await db.query(
            `INSERT INTO guidance_tag_relations (guidance_id, tag_id)
             VALUES ($1, $2)`,
            [guidanceId, tagId]
          );
        }
      }

      // Create initial version
      await db.query(
        `INSERT INTO guidance_versions
         (guidance_id, version_number, title, content, summary, category_id, created_at)
         VALUES ($1, 1, $2, $3, $4, $5, NOW())`,
        [
          guidanceId,
          guidance.title,
          guidance.content,
          guidance.summary,
          guidance.category_id
        ]
      );

      // Create audit entry
      await db.query(
        `INSERT INTO guidance_audit
         (guidance_id, action, details, performed_at)
         VALUES ($1, 'create', $2, NOW())`,
        [
          guidanceId,
          JSON.stringify({ title: guidance.title, context_key: guidance.context_key })
        ]
      );
    }

    console.log('Clinical guidance population completed successfully!');
  } catch (error) {
    console.error('Error populating clinical guidance:', error);
  } finally {
    // Close the database connection
    process.exit(0);
  }
}

// Run the population function
populateGuidance();
