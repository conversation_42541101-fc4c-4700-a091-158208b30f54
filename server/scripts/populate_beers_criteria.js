const pool = require('../db');
require('dotenv').config();

/**
 * <PERSON><PERSON><PERSON> to populate the BEERS criteria table with sample data
 * Based on the 2023 American Geriatrics Society BEERS Criteria
 */

// Sample BEERS criteria data
const beersCriteriaData = [
  // Avoid category - Antihistamines
  {
    medication_name: 'Brompheniramine',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Highly anticholinergic; clearance reduced with advanced age; risk of confusion, dry mouth, constipation, and other anticholinergic effects.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Chlorpheniramine maleate',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Highly anticholinergic; clearance reduced with advanced age; risk of confusion, dry mouth, constipation, and other anticholinergic effects.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Cyproheptadine',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Highly anticholinergic; clearance reduced with advanced age; risk of confusion, dry mouth, constipation, and other anticholinergic effects.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Dimenhydrinate',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Highly anticholinergic; clearance reduced with advanced age; risk of confusion, dry mouth, constipation, and other anticholinergic effects.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Diphenhydramine hydrochloride',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use as a first-line treatment for insomnia or allergies in older adults (except for acute, severe allergic reactions).',
    rationale: 'Highly anticholinergic; clearance reduced with advanced age, and tolerance develops when used as hypnotic; risk of confusion, dry mouth, constipation, and other anticholinergic effects.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Doxylamine',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Highly anticholinergic; clearance reduced with advanced age; risk of confusion, dry mouth, constipation, and other anticholinergic effects.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Hydroxyzine hydrochloride',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Highly anticholinergic; clearance reduced with advanced age; risk of confusion, dry mouth, constipation, and other anticholinergic effects.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Meclizine',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Highly anticholinergic; clearance reduced with advanced age; risk of confusion, dry mouth, constipation, and other anticholinergic effects.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Promethazine',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Highly anticholinergic; clearance reduced with advanced age; risk of confusion, dry mouth, constipation, and other anticholinergic effects.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Triprolidine',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Highly anticholinergic; clearance reduced with advanced age; risk of confusion, dry mouth, constipation, and other anticholinergic effects.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Nitrofurantoin macrocrystals',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults, especially those with CrCl <30 mL/min.',
    rationale: 'Potential for multisystem organ toxicity and peripheral neuropathy, especially with long-term use; safer alternatives available.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Amitriptyline',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use as an antidepressant in older adults.',
    rationale: 'Highly anticholinergic, sedating, and causes orthostatic hypotension; safety profile of low-dose amitriptyline for neuropathic pain or migraine prophylaxis is comparable with that of other alternatives.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Diazepam',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Older adults have increased sensitivity to benzodiazepines and decreased metabolism of long-acting agents; in general, all benzodiazepines increase risk of cognitive impairment, delirium, falls, fractures, and motor vehicle crashes in older adults.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },

  // Avoid category - Cardiovascular Medications and Antithrombotics
  {
    medication_name: 'Aspirin (for primary prevention)',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use for primary prevention of cardiovascular disease in adults aged ≥70 years.',
    rationale: 'Risk of major bleeding may outweigh benefit in older adults without specific cardiovascular risk factors.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Warfarin sodium (as initial therapy)',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid as initial therapy unless DOACs are contraindicated.',
    rationale: 'Higher risk of bleeding compared to direct oral anticoagulants (DOACs); requires more monitoring.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Rivaroxaban',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid for long-term treatment of atrial fibrillation or VTE.',
    rationale: 'Higher risk of major bleeding compared to other DOACs.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Dipyridamole',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Risk of orthostatic hypotension; more effective alternatives available.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Doxazosin mesylate',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use as an antihypertensive in older adults.',
    rationale: 'High risk of orthostatic hypotension; not recommended as routine treatment for hypertension; alternative agents have superior risk/benefit profile.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Prazosin hydrochloride',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use as an antihypertensive in older adults.',
    rationale: 'High risk of orthostatic hypotension; not recommended as routine treatment for hypertension; alternative agents have superior risk/benefit profile.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Terazosin hydrochloride',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use as an antihypertensive in older adults.',
    rationale: 'High risk of orthostatic hypotension; not recommended as routine treatment for hypertension; alternative agents have superior risk/benefit profile.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Clonidine',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use as a first-line antihypertensive in older adults.',
    rationale: 'High risk of CNS adverse effects, orthostatic hypotension, and bradycardia.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Guanfacine hydrochloride',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use as a first-line antihypertensive in older adults.',
    rationale: 'High risk of CNS adverse effects, orthostatic hypotension, and bradycardia.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Nifedipine (immediate release)',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Potential for hypotension and risk of precipitating myocardial ischemia.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Amiodarone hydrochloride',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid as first-line therapy for atrial fibrillation unless patient has heart failure or substantial left ventricular hypertrophy.',
    rationale: 'Effective for maintaining sinus rhythm but has greater toxicities than other antiarrhythmics; may be reasonable first-line therapy for patients with concomitant heart failure or substantial left ventricular hypertrophy.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Dronedarone',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid in older adults with permanent atrial fibrillation or heart failure.',
    rationale: 'Worse outcomes in patients with permanent atrial fibrillation or heart failure; in general, rate control is preferred over rhythm control for permanent atrial fibrillation.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Digoxin',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid as first-line treatment for atrial fibrillation or heart failure.',
    rationale: 'For atrial fibrillation, other rate control agents preferred; for heart failure, evidence suggests that in the absence of atrial fibrillation, digoxin may be associated with increased mortality.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // Avoid category - CNS Medications - Antidepressants
  {
    medication_name: 'Amoxapine',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Highly anticholinergic, sedating, and causes orthostatic hypotension; safety profile of low-dose amoxapine is comparable with that of other alternatives.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Clomipramine hydrochloride',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Highly anticholinergic, sedating, and causes orthostatic hypotension.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Desipramine hydrochloride',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Highly anticholinergic, sedating, and causes orthostatic hypotension.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Doxepin hydrochloride (dose >6 mg/day)',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid doses >6 mg/day in older adults.',
    rationale: 'Highly anticholinergic, sedating, and causes orthostatic hypotension; doses ≤6 mg/day may be appropriate for treating insomnia in older adults.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Imipramine hydrochloride',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Highly anticholinergic, sedating, and causes orthostatic hypotension.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Nortriptyline hydrochloride',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Highly anticholinergic, sedating, and causes orthostatic hypotension.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Paroxetine hydrochloride',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Highly anticholinergic, sedating, and causes orthostatic hypotension.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },

  // Avoid category - CNS Medications - Antiparkinsonian agents
  {
    medication_name: 'Benztropine mesylate',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults for Parkinson disease.',
    rationale: 'Not recommended for prevention of extrapyramidal symptoms with antipsychotics; more effective agents available for treatment of Parkinson disease.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Trihexyphenidyl',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults for Parkinson disease.',
    rationale: 'Not recommended for prevention of extrapyramidal symptoms with antipsychotics; more effective agents available for treatment of Parkinson disease.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // Avoid category - CNS Medications - Antipsychotics
  {
    medication_name: 'Aripiprazole',
    category: 'avoid',
    condition_name: 'Dementia',
    interacting_medication: null,
    recommendation: 'Avoid antipsychotics for behavioral problems of dementia unless nonpharmacological options have failed and patient is a threat to themselves or others.',
    rationale: 'Increased risk of cerebrovascular accident (stroke) and mortality in persons with dementia.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Haloperidol',
    category: 'avoid',
    condition_name: 'Dementia',
    interacting_medication: null,
    recommendation: 'Avoid antipsychotics for behavioral problems of dementia unless nonpharmacological options have failed and patient is a threat to themselves or others.',
    rationale: 'Increased risk of cerebrovascular accident (stroke) and mortality in persons with dementia.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Olanzapine',
    category: 'avoid',
    condition_name: 'Dementia',
    interacting_medication: null,
    recommendation: 'Avoid antipsychotics for behavioral problems of dementia unless nonpharmacological options have failed and patient is a threat to themselves or others.',
    rationale: 'Increased risk of cerebrovascular accident (stroke) and mortality in persons with dementia.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Quetiapine fumarate',
    category: 'avoid',
    condition_name: 'Dementia',
    interacting_medication: null,
    recommendation: 'Avoid antipsychotics for behavioral problems of dementia unless nonpharmacological options have failed and patient is a threat to themselves or others.',
    rationale: 'Increased risk of cerebrovascular accident (stroke) and mortality in persons with dementia.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Risperidone',
    category: 'avoid',
    condition_name: 'Dementia',
    interacting_medication: null,
    recommendation: 'Avoid antipsychotics for behavioral problems of dementia unless nonpharmacological options have failed and patient is a threat to themselves or others.',
    rationale: 'Increased risk of cerebrovascular accident (stroke) and mortality in persons with dementia.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },

  // Avoid category - CNS Medications - Barbiturates
  {
    medication_name: 'Butalbital',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'High rate of physical dependence; tolerance to sleep benefits; risk of overdose at low dosages.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Phenobarbital',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'High rate of physical dependence; tolerance to sleep benefits; risk of overdose at low dosages.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Primidone',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'High rate of physical dependence; tolerance to sleep benefits; risk of overdose at low dosages.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },

  // Avoid category - CNS Medications - Benzodiazepines
  {
    medication_name: 'Alprazolam',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Older adults have increased sensitivity to benzodiazepines and decreased metabolism of long-acting agents; in general, all benzodiazepines increase risk of cognitive impairment, delirium, falls, fractures, and motor vehicle crashes in older adults.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Chlordiazepoxide hydrochloride',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Older adults have increased sensitivity to benzodiazepines and decreased metabolism of long-acting agents; in general, all benzodiazepines increase risk of cognitive impairment, delirium, falls, fractures, and motor vehicle crashes in older adults.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Clobazam',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Older adults have increased sensitivity to benzodiazepines and decreased metabolism of long-acting agents; in general, all benzodiazepines increase risk of cognitive impairment, delirium, falls, fractures, and motor vehicle crashes in older adults.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Clonazepam',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Older adults have increased sensitivity to benzodiazepines and decreased metabolism of long-acting agents; in general, all benzodiazepines increase risk of cognitive impairment, delirium, falls, fractures, and motor vehicle crashes in older adults.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Clorazepate',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Older adults have increased sensitivity to benzodiazepines and decreased metabolism of long-acting agents; in general, all benzodiazepines increase risk of cognitive impairment, delirium, falls, fractures, and motor vehicle crashes in older adults.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Estazolam',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Older adults have increased sensitivity to benzodiazepines and decreased metabolism of long-acting agents; in general, all benzodiazepines increase risk of cognitive impairment, delirium, falls, fractures, and motor vehicle crashes in older adults.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Lorazepam',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Older adults have increased sensitivity to benzodiazepines and decreased metabolism of long-acting agents; in general, all benzodiazepines increase risk of cognitive impairment, delirium, falls, fractures, and motor vehicle crashes in older adults.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Midazolam hydrochloride',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Older adults have increased sensitivity to benzodiazepines and decreased metabolism of long-acting agents; in general, all benzodiazepines increase risk of cognitive impairment, delirium, falls, fractures, and motor vehicle crashes in older adults.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Oxazepam',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Older adults have increased sensitivity to benzodiazepines and decreased metabolism of long-acting agents; in general, all benzodiazepines increase risk of cognitive impairment, delirium, falls, fractures, and motor vehicle crashes in older adults.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Temazepam',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Older adults have increased sensitivity to benzodiazepines and decreased metabolism of long-acting agents; in general, all benzodiazepines increase risk of cognitive impairment, delirium, falls, fractures, and motor vehicle crashes in older adults.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Triazolam',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Older adults have increased sensitivity to benzodiazepines and decreased metabolism of long-acting agents; in general, all benzodiazepines increase risk of cognitive impairment, delirium, falls, fractures, and motor vehicle crashes in older adults.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },

  // Avoid category - CNS Medications - Nonbenzodiazepine hypnotics
  {
    medication_name: 'Eszopiclone',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid chronic use (>90 days) in older adults.',
    rationale: 'Benzodiazepine-receptor agonists have adverse events similar to those of benzodiazepines in older adults (e.g., delirium, falls, fractures); minimal improvement in sleep latency and duration.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Zaleplon',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid chronic use (>90 days) in older adults.',
    rationale: 'Benzodiazepine-receptor agonists have adverse events similar to those of benzodiazepines in older adults (e.g., delirium, falls, fractures); minimal improvement in sleep latency and duration.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Zolpidem',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid chronic use (>90 days) in older adults.',
    rationale: 'Benzodiazepine-receptor agonists have adverse events similar to those of benzodiazepines in older adults (e.g., delirium, falls, fractures); minimal improvement in sleep latency and duration.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Meprobamate',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'High rate of physical dependence; very sedating; safer alternatives available.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Ergoloid mesylates',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Lack of efficacy for dementia.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },

  // Avoid category - Endocrine Medications
  {
    medication_name: 'Androgens',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults except for confirmed hypogonadism with clinical symptoms.',
    rationale: 'Potential for cardiac problems and contraindicated in men with prostate cancer.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Estrogen (oral and transdermal)',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid oral and topical patch estrogen in older women.',
    rationale: 'Evidence of carcinogenic potential (breast and endometrium); lack of cardioprotective effect and cognitive protection in older women.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Sliding scale insulin',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid sliding scale insulin as the sole method for glycemic control in older adults.',
    rationale: 'Higher risk of hypoglycemia without improvement in hyperglycemia management.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Gliclazide',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Prolonged half-life in older adults; can cause prolonged hypoglycemia; causes syndrome of inappropriate antidiuretic hormone secretion.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Glimepiride',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Prolonged half-life in older adults; can cause prolonged hypoglycemia; causes syndrome of inappropriate antidiuretic hormone secretion.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Glipizide',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Prolonged half-life in older adults; can cause prolonged hypoglycemia; causes syndrome of inappropriate antidiuretic hormone secretion.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Glyburide',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Prolonged half-life in older adults; can cause prolonged hypoglycemia; causes syndrome of inappropriate antidiuretic hormone secretion.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Desiccated thyroid',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Concerns about cardiac effects; safer alternatives available.',
    quality_of_evidence: 'low',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Megestrol acetate',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Minimal effect on weight; increases risk of thrombotic events and possibly death in older adults.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Growth hormone',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults except as hormone replacement after pituitary gland removal.',
    rationale: 'Effect on body composition is small and associated with edema, arthralgia, carpal tunnel syndrome, gynecomastia, impaired fasting glucose.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },

  // Avoid category - Gastrointestinal (GI) Medications
  {
    medication_name: 'Dexlansoprazole',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid scheduled use for >8 weeks unless for high-risk patients, erosive esophagitis, Barrett esophagitis, pathological hypersecretory condition, or demonstrated need for maintenance treatment.',
    rationale: 'Risk of Clostridium difficile infection, bone loss and fractures, hypomagnesemia.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Esomeprazole magnesium',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid scheduled use for >8 weeks unless for high-risk patients, erosive esophagitis, Barrett esophagitis, pathological hypersecretory condition, or demonstrated need for maintenance treatment.',
    rationale: 'Risk of Clostridium difficile infection, bone loss and fractures, hypomagnesemia.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Lansoprazole',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid scheduled use for >8 weeks unless for high-risk patients, erosive esophagitis, Barrett esophagitis, pathological hypersecretory condition, or demonstrated need for maintenance treatment.',
    rationale: 'Risk of Clostridium difficile infection, bone loss and fractures, hypomagnesemia.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Omeprazole',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid scheduled use for >8 weeks unless for high-risk patients, erosive esophagitis, Barrett esophagitis, pathological hypersecretory condition, or demonstrated need for maintenance treatment.',
    rationale: 'Risk of Clostridium difficile infection, bone loss and fractures, hypomagnesemia.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Pantoprazole sodium',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid scheduled use for >8 weeks unless for high-risk patients, erosive esophagitis, Barrett esophagitis, pathological hypersecretory condition, or demonstrated need for maintenance treatment.',
    rationale: 'Risk of Clostridium difficile infection, bone loss and fractures, hypomagnesemia.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Rabeprazole',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid scheduled use for >8 weeks unless for high-risk patients, erosive esophagitis, Barrett esophagitis, pathological hypersecretory condition, or demonstrated need for maintenance treatment.',
    rationale: 'Risk of Clostridium difficile infection, bone loss and fractures, hypomagnesemia.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Metoclopramide',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults, except for gastroparesis or short-term treatment.',
    rationale: 'Can cause extrapyramidal effects including tardive dyskinesia; risk may be further increased in frail older adults.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Atropine sulfate',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Highly anticholinergic, uncertain effectiveness.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Clidinium-chlordiazepoxide',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Highly anticholinergic, uncertain effectiveness.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Dicyclomine',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Highly anticholinergic, uncertain effectiveness.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Hyoscyamine',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Highly anticholinergic, uncertain effectiveness.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Scopolamine',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Highly anticholinergic, uncertain effectiveness.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Mineral oil',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid oral use in older adults.',
    rationale: 'Potential for aspiration and adverse effects; safer alternatives available.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // Avoid category - Genitourinary Medication
  {
    medication_name: 'Desmopressin',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults for treatment of nocturia or nocturnal polyuria.',
    rationale: 'High risk of hyponatremia; safer alternatives available.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // Avoid category - Pain Medications - NSAIDs
  {
    medication_name: 'Aspirin (high dose)',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid chronic use of oral NSAIDs in older adults.',
    rationale: 'Increased risk of GI bleeding and peptic ulcer disease; risk is dose-related. Use with caution in older adults with heart failure, chronic kidney disease, or on anticoagulants.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Diclofenac',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid chronic use of oral NSAIDs in older adults.',
    rationale: 'Increased risk of GI bleeding and peptic ulcer disease; risk is dose-related. Use with caution in older adults with heart failure, chronic kidney disease, or on anticoagulants.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Diflunisal',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid chronic use of oral NSAIDs in older adults.',
    rationale: 'Increased risk of GI bleeding and peptic ulcer disease; risk is dose-related. Use with caution in older adults with heart failure, chronic kidney disease, or on anticoagulants.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Etodolac',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid chronic use of oral NSAIDs in older adults.',
    rationale: 'Increased risk of GI bleeding and peptic ulcer disease; risk is dose-related. Use with caution in older adults with heart failure, chronic kidney disease, or on anticoagulants.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Flurbiprofen',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid chronic use of oral NSAIDs in older adults.',
    rationale: 'Increased risk of GI bleeding and peptic ulcer disease; risk is dose-related. Use with caution in older adults with heart failure, chronic kidney disease, or on anticoagulants.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Ibuprofen',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid chronic use of oral NSAIDs in older adults.',
    rationale: 'Increased risk of GI bleeding and peptic ulcer disease; risk is dose-related. Use with caution in older adults with heart failure, chronic kidney disease, or on anticoagulants.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Indomethacin',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid chronic use of oral NSAIDs in older adults.',
    rationale: 'Increased risk of GI bleeding and peptic ulcer disease; risk is dose-related. Use with caution in older adults with heart failure, chronic kidney disease, or on anticoagulants.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Ketorolac tromethamine',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Increased risk of GI bleeding, peptic ulcer disease, and acute kidney injury, especially in older adults.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Meloxicam',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid chronic use of oral NSAIDs in older adults.',
    rationale: 'Increased risk of GI bleeding and peptic ulcer disease; risk is dose-related. Use with caution in older adults with heart failure, chronic kidney disease, or on anticoagulants.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Nabumetone',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid chronic use of oral NSAIDs in older adults.',
    rationale: 'Increased risk of GI bleeding and peptic ulcer disease; risk is dose-related. Use with caution in older adults with heart failure, chronic kidney disease, or on anticoagulants.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Naproxen sodium',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid chronic use of oral NSAIDs in older adults.',
    rationale: 'Increased risk of GI bleeding and peptic ulcer disease; risk is dose-related. Use with caution in older adults with heart failure, chronic kidney disease, or on anticoagulants.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Oxaprozin',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid chronic use of oral NSAIDs in older adults.',
    rationale: 'Increased risk of GI bleeding and peptic ulcer disease; risk is dose-related. Use with caution in older adults with heart failure, chronic kidney disease, or on anticoagulants.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Piroxicam',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid chronic use of oral NSAIDs in older adults.',
    rationale: 'Increased risk of GI bleeding and peptic ulcer disease; risk is dose-related. Use with caution in older adults with heart failure, chronic kidney disease, or on anticoagulants.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Sulindac',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid chronic use of oral NSAIDs in older adults.',
    rationale: 'Increased risk of GI bleeding and peptic ulcer disease; risk is dose-related. Use with caution in older adults with heart failure, chronic kidney disease, or on anticoagulants.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },

  // Avoid category - Pain Medications - Other
  {
    medication_name: 'Meperidine',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Not effective oral analgesic in dosages commonly used; may cause neurotoxicity; safer alternatives available.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },

  // Avoid category - Skeletal muscle relaxants
  {
    medication_name: 'Carisoprodol',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Most muscle relaxants poorly tolerated by older adults because of anticholinergic adverse effects, sedation, risk of fracture; effectiveness questionable.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Chlorzoxazone',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Most muscle relaxants poorly tolerated by older adults because of anticholinergic adverse effects, sedation, risk of fracture; effectiveness questionable.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Cyclobenzaprine',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Most muscle relaxants poorly tolerated by older adults because of anticholinergic adverse effects, sedation, risk of fracture; effectiveness questionable.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Metaxalone',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Most muscle relaxants poorly tolerated by older adults because of anticholinergic adverse effects, sedation, risk of fracture; effectiveness questionable.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Methocarbamol',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Most muscle relaxants poorly tolerated by older adults because of anticholinergic adverse effects, sedation, risk of fracture; effectiveness questionable.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Orphenadrine',
    category: 'avoid',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid use in older adults.',
    rationale: 'Most muscle relaxants poorly tolerated by older adults because of anticholinergic adverse effects, sedation, risk of fracture; effectiveness questionable.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // Disease interaction category - Cardiovascular Diseases

  // Heart Failure
  {
    medication_name: 'NSAIDs',
    category: 'disease_interaction',
    condition_name: 'Heart failure',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with heart failure.',
    rationale: 'Potential to promote fluid retention and exacerbate heart failure.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'COX-2 inhibitors',
    category: 'disease_interaction',
    condition_name: 'Heart failure',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with heart failure.',
    rationale: 'Potential to promote fluid retention and exacerbate heart failure.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Diltiazem',
    category: 'disease_interaction',
    condition_name: 'Heart failure',
    interacting_medication: null,
    recommendation: 'Avoid non-dihydropyridine calcium channel blockers in older adults with heart failure with reduced ejection fraction.',
    rationale: 'Potential to promote fluid retention and exacerbate heart failure; might increase risk of cardiovascular events.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Verapamil',
    category: 'disease_interaction',
    condition_name: 'Heart failure',
    interacting_medication: null,
    recommendation: 'Avoid non-dihydropyridine calcium channel blockers in older adults with heart failure with reduced ejection fraction.',
    rationale: 'Potential to promote fluid retention and exacerbate heart failure; might increase risk of cardiovascular events.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Thiazolidinediones',
    category: 'disease_interaction',
    condition_name: 'Heart failure',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with heart failure.',
    rationale: 'Potential to promote fluid retention and exacerbate heart failure.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Cilostazol',
    category: 'disease_interaction',
    condition_name: 'Heart failure',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with heart failure.',
    rationale: 'Potential to promote fluid retention and exacerbate heart failure.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Dronedarone',
    category: 'disease_interaction',
    condition_name: 'Heart failure',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with heart failure.',
    rationale: 'Potential to promote fluid retention and exacerbate heart failure; increased risk of mortality.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Dextromethorphan-quinidine',
    category: 'disease_interaction',
    condition_name: 'Heart failure',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with heart failure.',
    rationale: 'Potential to promote fluid retention and exacerbate heart failure.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // Syncope
  {
    medication_name: 'Chlorpromazine',
    category: 'disease_interaction',
    condition_name: 'Syncope',
    interacting_medication: null,
    recommendation: 'Avoid antipsychotics in older adults with history of syncope.',
    rationale: 'Increased risk of orthostatic hypotension or bradycardia.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Olanzapine',
    category: 'disease_interaction',
    condition_name: 'Syncope',
    interacting_medication: null,
    recommendation: 'Avoid antipsychotics in older adults with history of syncope.',
    rationale: 'Increased risk of orthostatic hypotension or bradycardia.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Amitriptyline',
    category: 'disease_interaction',
    condition_name: 'Syncope',
    interacting_medication: null,
    recommendation: 'Avoid tertiary tricyclic antidepressants in older adults with history of syncope.',
    rationale: 'Increased risk of orthostatic hypotension or bradycardia.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Clomipramine',
    category: 'disease_interaction',
    condition_name: 'Syncope',
    interacting_medication: null,
    recommendation: 'Avoid tertiary tricyclic antidepressants in older adults with history of syncope.',
    rationale: 'Increased risk of orthostatic hypotension or bradycardia.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Doxepin',
    category: 'disease_interaction',
    condition_name: 'Syncope',
    interacting_medication: null,
    recommendation: 'Avoid tertiary tricyclic antidepressants in older adults with history of syncope.',
    rationale: 'Increased risk of orthostatic hypotension or bradycardia.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Imipramine',
    category: 'disease_interaction',
    condition_name: 'Syncope',
    interacting_medication: null,
    recommendation: 'Avoid tertiary tricyclic antidepressants in older adults with history of syncope.',
    rationale: 'Increased risk of orthostatic hypotension or bradycardia.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Donepezil',
    category: 'disease_interaction',
    condition_name: 'Syncope',
    interacting_medication: null,
    recommendation: 'Avoid cholinesterase inhibitors in older adults with history of syncope.',
    rationale: 'Increased risk of orthostatic hypotension or bradycardia.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Galantamine',
    category: 'disease_interaction',
    condition_name: 'Syncope',
    interacting_medication: null,
    recommendation: 'Avoid cholinesterase inhibitors in older adults with history of syncope.',
    rationale: 'Increased risk of orthostatic hypotension or bradycardia.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Rivastigmine',
    category: 'disease_interaction',
    condition_name: 'Syncope',
    interacting_medication: null,
    recommendation: 'Avoid cholinesterase inhibitors in older adults with history of syncope.',
    rationale: 'Increased risk of orthostatic hypotension or bradycardia.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Doxazosin',
    category: 'disease_interaction',
    condition_name: 'Syncope',
    interacting_medication: null,
    recommendation: 'Avoid nonselective peripheral alpha-1 blockers in older adults with history of syncope.',
    rationale: 'Increased risk of orthostatic hypotension or bradycardia.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Prazosin',
    category: 'disease_interaction',
    condition_name: 'Syncope',
    interacting_medication: null,
    recommendation: 'Avoid nonselective peripheral alpha-1 blockers in older adults with history of syncope.',
    rationale: 'Increased risk of orthostatic hypotension or bradycardia.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Terazosin',
    category: 'disease_interaction',
    condition_name: 'Syncope',
    interacting_medication: null,
    recommendation: 'Avoid nonselective peripheral alpha-1 blockers in older adults with history of syncope.',
    rationale: 'Increased risk of orthostatic hypotension or bradycardia.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // Other disease interactions
  {
    medication_name: 'Metformin',
    category: 'disease_interaction',
    condition_name: 'Chronic kidney disease',
    interacting_medication: null,
    recommendation: 'Use with caution in older adults with chronic kidney disease stage 4 or higher.',
    rationale: 'May increase risk of lactic acidosis; alternative glucose-lowering therapies may be needed.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Antipsychotics',
    category: 'disease_interaction',
    condition_name: 'Dementia or cognitive impairment',
    interacting_medication: null,
    recommendation: 'Avoid antipsychotics for behavioral problems of dementia unless nonpharmacological options have failed and patient is a threat to themselves or others.',
    rationale: 'Increased risk of cerebrovascular accident (stroke) and mortality in persons with dementia.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },

  // Disease interaction category - Central Nervous System Disorders

  // Delirium
  {
    medication_name: 'Opioids',
    category: 'disease_interaction',
    condition_name: 'Delirium',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with or at high risk of delirium.',
    rationale: 'May induce or worsen delirium in older adults; if used, reduce dose and monitor closely.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Benzodiazepines',
    category: 'disease_interaction',
    condition_name: 'Delirium',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with or at high risk of delirium.',
    rationale: 'May induce or worsen delirium in older adults.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Anticholinergics',
    category: 'disease_interaction',
    condition_name: 'Delirium',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with or at high risk of delirium.',
    rationale: 'May induce or worsen delirium in older adults.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Corticosteroids',
    category: 'disease_interaction',
    condition_name: 'Delirium',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with or at high risk of delirium.',
    rationale: 'May induce or worsen delirium in older adults; if used, reduce dose and monitor closely.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'H2-receptor antagonists',
    category: 'disease_interaction',
    condition_name: 'Delirium',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with or at high risk of delirium.',
    rationale: 'May induce or worsen delirium in older adults.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Nonbenzodiazepine receptor agonist hypnotics',
    category: 'disease_interaction',
    condition_name: 'Delirium',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with or at high risk of delirium.',
    rationale: 'May induce or worsen delirium in older adults.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // Dementia or Cognitive Impairment (additional medications)
  {
    medication_name: 'Benzodiazepines',
    category: 'disease_interaction',
    condition_name: 'Dementia or cognitive impairment',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with dementia or cognitive impairment.',
    rationale: 'May cause paradoxical reactions, worsen cognition, and increase risk of falls.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Anticholinergics',
    category: 'disease_interaction',
    condition_name: 'Dementia or cognitive impairment',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with dementia or cognitive impairment.',
    rationale: 'May worsen cognitive impairment and cause confusion.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Nonbenzodiazepine receptor agonist hypnotics',
    category: 'disease_interaction',
    condition_name: 'Dementia or cognitive impairment',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with dementia or cognitive impairment.',
    rationale: 'Adverse CNS effects similar to those of benzodiazepines; increased risk of delirium, falls, and fractures.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // History of Falls or Fractures
  {
    medication_name: 'Anticholinergics',
    category: 'disease_interaction',
    condition_name: 'History of falls or fractures',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with history of falls or fractures.',
    rationale: 'May cause ataxia, impaired psychomotor function, syncope, and additional falls.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Antidepressants',
    category: 'disease_interaction',
    condition_name: 'History of falls or fractures',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with history of falls or fractures.',
    rationale: 'May cause ataxia, impaired psychomotor function, syncope, and additional falls; includes SNRIs, SSRIs, and tricyclic antidepressants.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Antiepileptics',
    category: 'disease_interaction',
    condition_name: 'History of falls or fractures',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with history of falls or fractures.',
    rationale: 'May cause ataxia, impaired psychomotor function, syncope, and additional falls.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Antipsychotics',
    category: 'disease_interaction',
    condition_name: 'History of falls or fractures',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with history of falls or fractures.',
    rationale: 'May cause ataxia, impaired psychomotor function, syncope, and additional falls.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Benzodiazepines',
    category: 'disease_interaction',
    condition_name: 'History of falls or fractures',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with history of falls or fractures.',
    rationale: 'May cause ataxia, impaired psychomotor function, syncope, and additional falls.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Opioids',
    category: 'disease_interaction',
    condition_name: 'History of falls or fractures',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with history of falls or fractures.',
    rationale: 'May cause ataxia, impaired psychomotor function, syncope, and additional falls.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Nonbenzodiazepine receptor agonist hypnotics',
    category: 'disease_interaction',
    condition_name: 'History of falls or fractures',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with history of falls or fractures.',
    rationale: 'May cause ataxia, impaired psychomotor function, syncope, and additional falls.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },

  // Parkinson Disease
  {
    medication_name: 'Metoclopramide',
    category: 'disease_interaction',
    condition_name: 'Parkinson disease',
    interacting_medication: null,
    recommendation: 'Avoid antiemetics in older adults with Parkinson disease.',
    rationale: 'Risk of worsening parkinsonian symptoms due to dopamine receptor blockade.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Prochlorperazine',
    category: 'disease_interaction',
    condition_name: 'Parkinson disease',
    interacting_medication: null,
    recommendation: 'Avoid antiemetics in older adults with Parkinson disease.',
    rationale: 'Risk of worsening parkinsonian symptoms due to dopamine receptor blockade.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Promethazine',
    category: 'disease_interaction',
    condition_name: 'Parkinson disease',
    interacting_medication: null,
    recommendation: 'Avoid antiemetics in older adults with Parkinson disease.',
    rationale: 'Risk of worsening parkinsonian symptoms due to dopamine receptor blockade.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Antipsychotics',
    category: 'disease_interaction',
    condition_name: 'Parkinson disease',
    interacting_medication: null,
    recommendation: 'Avoid antipsychotics in older adults with Parkinson disease.',
    rationale: 'Risk of worsening parkinsonian symptoms due to dopamine receptor blockade.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // Disease interaction category - Gastrointestinal Disorders

  // History of Gastric or Duodenal Ulcers
  {
    medication_name: 'Aspirin',
    category: 'disease_interaction',
    condition_name: 'History of gastric or duodenal ulcers',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with history of gastric or duodenal ulcers.',
    rationale: 'May exacerbate existing ulcers or cause new or additional ulcers.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Non-COX-2 selective NSAIDs',
    category: 'disease_interaction',
    condition_name: 'History of gastric or duodenal ulcers',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with history of gastric or duodenal ulcers.',
    rationale: 'May exacerbate existing ulcers or cause new or additional ulcers.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },

  // Disease interaction category - Kidney/Urinary Tract Disorders

  // Urinary Incontinence in Women
  {
    medication_name: 'Doxazosin mesylate',
    category: 'disease_interaction',
    condition_name: 'Urinary incontinence in women',
    interacting_medication: null,
    recommendation: 'Avoid nonselective peripheral alpha-1 blockers in older women with urinary incontinence.',
    rationale: 'May increase stress incontinence or worsen urinary incontinence in women.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Prazosin hydrochloride',
    category: 'disease_interaction',
    condition_name: 'Urinary incontinence in women',
    interacting_medication: null,
    recommendation: 'Avoid nonselective peripheral alpha-1 blockers in older women with urinary incontinence.',
    rationale: 'May increase stress incontinence or worsen urinary incontinence in women.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Terazosin hydrochloride',
    category: 'disease_interaction',
    condition_name: 'Urinary incontinence in women',
    interacting_medication: null,
    recommendation: 'Avoid nonselective peripheral alpha-1 blockers in older women with urinary incontinence.',
    rationale: 'May increase stress incontinence or worsen urinary incontinence in women.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Estrogens (conjugated)',
    category: 'disease_interaction',
    condition_name: 'Urinary incontinence in women',
    interacting_medication: null,
    recommendation: 'Avoid estrogens in older women with urinary incontinence.',
    rationale: 'Aggravation of incontinence.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },

  // Lower Urinary Tract Symptoms/Benign Prostatic Hyperplasia in Men
  {
    medication_name: 'Anticholinergics',
    category: 'disease_interaction',
    condition_name: 'Lower urinary tract symptoms/benign prostatic hyperplasia',
    interacting_medication: null,
    recommendation: 'Avoid anticholinergics in older men with lower urinary tract symptoms or benign prostatic hyperplasia.',
    rationale: 'May decrease urinary flow and cause urinary retention.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // Disease interaction category - Electrolyte Disorders

  // SIADH or Hyponatremia
  {
    medication_name: 'Mirtazapine',
    category: 'disease_interaction',
    condition_name: 'SIADH or hyponatremia',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with SIADH or hyponatremia.',
    rationale: 'May exacerbate or cause SIADH or hyponatremia; monitor sodium level closely when starting or changing dosages in older adults.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'SNRIs',
    category: 'disease_interaction',
    condition_name: 'SIADH or hyponatremia',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with SIADH or hyponatremia.',
    rationale: 'May exacerbate or cause SIADH or hyponatremia; monitor sodium level closely when starting or changing dosages in older adults.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'SSRIs',
    category: 'disease_interaction',
    condition_name: 'SIADH or hyponatremia',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with SIADH or hyponatremia.',
    rationale: 'May exacerbate or cause SIADH or hyponatremia; monitor sodium level closely when starting or changing dosages in older adults.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Tricyclic antidepressants',
    category: 'disease_interaction',
    condition_name: 'SIADH or hyponatremia',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with SIADH or hyponatremia.',
    rationale: 'May exacerbate or cause SIADH or hyponatremia; monitor sodium level closely when starting or changing dosages in older adults.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Carbamazepine',
    category: 'disease_interaction',
    condition_name: 'SIADH or hyponatremia',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with SIADH or hyponatremia.',
    rationale: 'May exacerbate or cause SIADH or hyponatremia; monitor sodium level closely when starting or changing dosages in older adults.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Oxcarbazepine',
    category: 'disease_interaction',
    condition_name: 'SIADH or hyponatremia',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with SIADH or hyponatremia.',
    rationale: 'May exacerbate or cause SIADH or hyponatremia; monitor sodium level closely when starting or changing dosages in older adults.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Antipsychotics',
    category: 'disease_interaction',
    condition_name: 'SIADH or hyponatremia',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with SIADH or hyponatremia.',
    rationale: 'May exacerbate or cause SIADH or hyponatremia; monitor sodium level closely when starting or changing dosages in older adults.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Diuretics',
    category: 'disease_interaction',
    condition_name: 'SIADH or hyponatremia',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with SIADH or hyponatremia.',
    rationale: 'May exacerbate or cause SIADH or hyponatremia; monitor sodium level closely when starting or changing dosages in older adults.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Tramadol',
    category: 'disease_interaction',
    condition_name: 'SIADH or hyponatremia',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with SIADH or hyponatremia.',
    rationale: 'May exacerbate or cause SIADH or hyponatremia; monitor sodium level closely when starting or changing dosages in older adults.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // Drug interaction category
  {
    medication_name: 'Warfarin',
    category: 'drug_interaction',
    condition_name: null,
    interacting_medication: 'NSAIDs',
    recommendation: 'Avoid concurrent use of warfarin with NSAIDs.',
    rationale: 'Increased risk of bleeding.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'ACE inhibitors',
    category: 'drug_interaction',
    condition_name: null,
    interacting_medication: 'Potassium-sparing diuretics',
    recommendation: 'Avoid routine concurrent use unless for specific conditions.',
    rationale: 'Increased risk of hyperkalemia.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Opioids',
    category: 'drug_interaction',
    condition_name: null,
    interacting_medication: 'Benzodiazepines',
    recommendation: 'Avoid concurrent use of opioids with benzodiazepines.',
    rationale: 'Increased risk of overdose and respiratory depression.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },

  // Adjust for kidney function category
  {
    medication_name: 'Gabapentin',
    category: 'adjust_for_kidney_function',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Reduce dose in older adults with reduced kidney function.',
    rationale: 'Increased risk of CNS adverse effects including somnolence, confusion, and dizziness.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Ciprofloxacin',
    category: 'adjust_for_kidney_function',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Reduce dose in older adults with reduced kidney function.',
    rationale: 'Increased risk of CNS effects and tendon rupture.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Dabigatran',
    category: 'adjust_for_kidney_function',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Avoid in older adults with CrCl <30 mL/min.',
    rationale: 'Increased risk of bleeding.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // Use with caution category

  // Antithrombotics
  {
    medication_name: 'Aspirin',
    category: 'use_with_caution',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Use with caution in adults ≥70 years for primary prevention of cardiovascular disease or colorectal cancer.',
    rationale: 'Risk of major bleeding may outweigh benefit in older adults without specific cardiovascular risk factors.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'weak'
  },
  {
    medication_name: 'Dabigatran',
    category: 'use_with_caution',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Use with caution in older adults.',
    rationale: 'May increase the risk of GI bleeding compared with warfarin and other direct oral anticoagulants.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Prasugrel',
    category: 'use_with_caution',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Use with caution in older adults.',
    rationale: 'Increases risk of major bleeding compared with clopidogrel in older adults.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Ticagrelor',
    category: 'use_with_caution',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Use with caution in older adults.',
    rationale: 'Increases risk of major bleeding compared with clopidogrel in older adults.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // Drugs that may exacerbate or cause SIADH or hyponatremia
  {
    medication_name: 'Mirtazapine',
    category: 'use_with_caution',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Use with caution in older adults.',
    rationale: 'May exacerbate or cause SIADH or hyponatremia; monitor sodium level closely when starting or changing dosages in older adults.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'SNRIs',
    category: 'use_with_caution',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Use with caution in older adults.',
    rationale: 'May exacerbate or cause SIADH or hyponatremia; monitor sodium level closely when starting or changing dosages in older adults.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'SSRIs',
    category: 'use_with_caution',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Use with caution in older adults.',
    rationale: 'May exacerbate or cause SIADH or hyponatremia; monitor sodium level closely when starting or changing dosages in older adults.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Tricyclic antidepressants',
    category: 'use_with_caution',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Use with caution in older adults.',
    rationale: 'May exacerbate or cause SIADH or hyponatremia; monitor sodium level closely when starting or changing dosages in older adults.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Carbamazepine',
    category: 'use_with_caution',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Use with caution in older adults.',
    rationale: 'May exacerbate or cause SIADH or hyponatremia; also a strong CYP3A4 inducer that can reduce the effectiveness of many medications.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Oxcarbazepine',
    category: 'use_with_caution',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Use with caution in older adults.',
    rationale: 'May exacerbate or cause SIADH or hyponatremia; monitor sodium level closely when starting or changing dosages in older adults.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Antipsychotics',
    category: 'use_with_caution',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Use with caution in older adults.',
    rationale: 'May exacerbate or cause SIADH or hyponatremia; monitor sodium level closely when starting or changing dosages in older adults.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Diuretics',
    category: 'use_with_caution',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Use with caution in older adults.',
    rationale: 'May exacerbate or cause SIADH or hyponatremia; monitor sodium level closely when starting or changing dosages in older adults.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Tramadol hydrochloride',
    category: 'use_with_caution',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Use with caution in older adults.',
    rationale: 'Increased risk of seizures, serotonin syndrome, and SIADH; also a weak opioid agonist with risks similar to other opioids.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // Other drugs
  {
    medication_name: 'Dextromethorphan-quinidine',
    category: 'use_with_caution',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Use with caution in older adults.',
    rationale: 'May increase risk of falls and shows limited efficacy in select patients with dementia.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Trimethoprim-sulfamethoxazole',
    category: 'use_with_caution',
    condition_name: null,
    interacting_medication: 'ACE inhibitors, ARBs, or ARNIs',
    recommendation: 'Use with caution in older adults taking ACE inhibitors, ARBs, or ARNIs.',
    rationale: 'May increase risk of hyperkalemia when used with ACE inhibitors, ARBs, or ARNIs.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // SGLT2 inhibitors
  {
    medication_name: 'Canagliflozin',
    category: 'use_with_caution',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Use with caution in older adults.',
    rationale: 'May increase risk of urogenital infections and euglycemic diabetic ketoacidosis.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Dapagliflozin',
    category: 'use_with_caution',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Use with caution in older adults.',
    rationale: 'May increase risk of urogenital infections and euglycemic diabetic ketoacidosis.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Empagliflozin',
    category: 'use_with_caution',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Use with caution in older adults.',
    rationale: 'May increase risk of urogenital infections and euglycemic diabetic ketoacidosis.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Ertugliflozin',
    category: 'use_with_caution',
    condition_name: null,
    interacting_medication: null,
    recommendation: 'Use with caution in older adults.',
    rationale: 'May increase risk of urogenital infections and euglycemic diabetic ketoacidosis.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // Drug interaction category - Potentially Clinically Important Drug-Drug Interactions to Avoid

  // Renin-angiotensin system (RAS) inhibitors or potassium-sparing diuretics
  {
    medication_name: 'RAS inhibitors',
    category: 'drug_interaction',
    condition_name: null,
    interacting_medication: 'Another RAS inhibitor or potassium-sparing diuretic',
    recommendation: 'Avoid concurrent use of RAS inhibitors with another RAS inhibitor or potassium-sparing diuretic.',
    rationale: 'Increases risk of hyperkalemia.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Potassium-sparing diuretics',
    category: 'drug_interaction',
    condition_name: null,
    interacting_medication: 'Another potassium-sparing diuretic or RAS inhibitor',
    recommendation: 'Avoid concurrent use of potassium-sparing diuretics with another potassium-sparing diuretic or RAS inhibitor.',
    rationale: 'Increases risk of hyperkalemia.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },

  // Opioids + Benzodiazepines
  {
    medication_name: 'Opioids',
    category: 'drug_interaction',
    condition_name: null,
    interacting_medication: 'Benzodiazepines',
    recommendation: 'Avoid concurrent use of opioids with benzodiazepines.',
    rationale: 'Increases risk of overdose.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },

  // Opioids + Gabapentin or Pregabalin
  {
    medication_name: 'Opioids',
    category: 'drug_interaction',
    condition_name: null,
    interacting_medication: 'Gabapentin or Pregabalin',
    recommendation: 'Avoid concurrent use of opioids with gabapentin or pregabalin.',
    rationale: 'Increases risk of severe sedation, respiratory depression, and death.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },

  // Multiple Anticholinergic Medications
  {
    medication_name: 'Anticholinergic medications',
    category: 'drug_interaction',
    condition_name: null,
    interacting_medication: 'Other anticholinergic medications',
    recommendation: 'Avoid concurrent use of multiple anticholinergic medications.',
    rationale: 'Increases risk of cognitive decline, delirium, and falls/fractures.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },

  // Any combination of three or more CNS-active drugs
  {
    medication_name: 'CNS-active drugs',
    category: 'drug_interaction',
    condition_name: null,
    interacting_medication: 'Multiple CNS-active drugs',
    recommendation: 'Avoid any combination of three or more of the following: antiepileptics, antidepressants (tricyclic antidepressants, SNRIs, SSRIs), antipsychotics, benzodiazepines, nonbenzodiazepine benzodiazepine receptor agonist hypnotics, opioids, skeletal muscle relaxants.',
    rationale: 'Increases risk of falls.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },

  // Lithium + ACE inhibitors, ARBs, ARNIs, or loop diuretics
  {
    medication_name: 'Lithium',
    category: 'drug_interaction',
    condition_name: null,
    interacting_medication: 'ACE inhibitors, ARBs, ARNIs, or loop diuretics',
    recommendation: 'Avoid concurrent use of lithium with ACE inhibitors, ARBs, ARNIs, or loop diuretics.',
    rationale: 'Increases risk of lithium toxicity.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // Nonselective peripheral alpha-1 blockers + Loop diuretics
  {
    medication_name: 'Nonselective peripheral alpha-1 blockers',
    category: 'drug_interaction',
    condition_name: null,
    interacting_medication: 'Loop diuretics',
    recommendation: 'Avoid concurrent use of nonselective peripheral alpha-1 blockers with loop diuretics in older women.',
    rationale: 'Increases risk of urinary incontinence in older women.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // Phenytoin + Trimethoprim-sulfamethoxazole
  {
    medication_name: 'Phenytoin',
    category: 'drug_interaction',
    condition_name: null,
    interacting_medication: 'Trimethoprim-sulfamethoxazole',
    recommendation: 'Avoid concurrent use of phenytoin with trimethoprim-sulfamethoxazole.',
    rationale: 'Increases risk of phenytoin toxicity.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // Theophylline + Cimetidine or Ciprofloxacin
  {
    medication_name: 'Theophylline',
    category: 'drug_interaction',
    condition_name: null,
    interacting_medication: 'Cimetidine or Ciprofloxacin',
    recommendation: 'Avoid concurrent use of theophylline with cimetidine or ciprofloxacin.',
    rationale: 'Increases risk of theophylline toxicity.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // Warfarin + Various medications
  {
    medication_name: 'Warfarin',
    category: 'drug_interaction',
    condition_name: null,
    interacting_medication: 'Amiodarone',
    recommendation: 'Avoid concurrent use of warfarin with amiodarone.',
    rationale: 'Increases risk of bleeding.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Warfarin',
    category: 'drug_interaction',
    condition_name: null,
    interacting_medication: 'Ciprofloxacin',
    recommendation: 'Avoid concurrent use of warfarin with ciprofloxacin.',
    rationale: 'Increases risk of bleeding.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Warfarin',
    category: 'drug_interaction',
    condition_name: null,
    interacting_medication: 'Macrolides (except azithromycin)',
    recommendation: 'Avoid concurrent use of warfarin with macrolides (except azithromycin).',
    rationale: 'Increases risk of bleeding.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Warfarin',
    category: 'drug_interaction',
    condition_name: null,
    interacting_medication: 'Trimethoprim-sulfamethoxazole',
    recommendation: 'Avoid concurrent use of warfarin with trimethoprim-sulfamethoxazole.',
    rationale: 'Increases risk of bleeding.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Warfarin',
    category: 'drug_interaction',
    condition_name: null,
    interacting_medication: 'SSRIs',
    recommendation: 'Avoid concurrent use of warfarin with SSRIs.',
    rationale: 'Increases risk of bleeding.',
    quality_of_evidence: 'high',
    strength_of_recommendation: 'strong'
  },

  // Kidney function category - Medications to Avoid or Adjust Dosage Based on Kidney Function

  // Antimicrobials
  {
    medication_name: 'Ciprofloxacin',
    category: 'adjust_for_kidney_function',
    condition_name: 'Reduced kidney function',
    interacting_medication: null,
    recommendation: 'Avoid or adjust dosage in older adults with reduced kidney function.',
    rationale: 'May increase risk of CNS effects and tendon rupture; reduced clearance in renal impairment.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Nitrofurantoin macrocrystals',
    category: 'adjust_for_kidney_function',
    condition_name: 'Reduced kidney function',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with CrCl <30 mL/min.',
    rationale: 'Inadequate drug concentration in the urine due to reduced renal excretion; increased risk of peripheral neuropathy and hepatotoxicity.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Trimethoprim-sulfamethoxazole',
    category: 'adjust_for_kidney_function',
    condition_name: 'Reduced kidney function',
    interacting_medication: null,
    recommendation: 'Avoid or adjust dosage in older adults with reduced kidney function.',
    rationale: 'Increased risk of hyperkalemia and reduced renal function, especially when used with other medications that increase potassium.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // Cardiovascular medications
  {
    medication_name: 'Amiloride hydrochloride',
    category: 'adjust_for_kidney_function',
    condition_name: 'Reduced kidney function',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with CrCl <30 mL/min.',
    rationale: 'Increased risk of hyperkalemia in older adults with reduced kidney function.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Spironolactone',
    category: 'adjust_for_kidney_function',
    condition_name: 'Reduced kidney function',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with CrCl <30 mL/min.',
    rationale: 'Increased risk of hyperkalemia in older adults with reduced kidney function.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Triamterene',
    category: 'adjust_for_kidney_function',
    condition_name: 'Reduced kidney function',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with CrCl <30 mL/min.',
    rationale: 'Increased risk of hyperkalemia in older adults with reduced kidney function.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // Anticoagulants
  {
    medication_name: 'Dabigatran etexilate mesylate',
    category: 'adjust_for_kidney_function',
    condition_name: 'Reduced kidney function',
    interacting_medication: null,
    recommendation: 'Avoid or adjust dosage in older adults with reduced kidney function.',
    rationale: 'Increased risk of bleeding in older adults with reduced kidney function.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Dofetilide',
    category: 'adjust_for_kidney_function',
    condition_name: 'Reduced kidney function',
    interacting_medication: null,
    recommendation: 'Avoid or adjust dosage in older adults with reduced kidney function.',
    rationale: 'Increased risk of QT prolongation and torsades de pointes in older adults with reduced kidney function.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Edoxaban tosylate',
    category: 'adjust_for_kidney_function',
    condition_name: 'Reduced kidney function',
    interacting_medication: null,
    recommendation: 'Avoid or adjust dosage in older adults with reduced kidney function.',
    rationale: 'Increased risk of bleeding in older adults with reduced kidney function.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Enoxaparin sodium',
    category: 'adjust_for_kidney_function',
    condition_name: 'Reduced kidney function',
    interacting_medication: null,
    recommendation: 'Avoid or adjust dosage in older adults with reduced kidney function.',
    rationale: 'Increased risk of bleeding in older adults with reduced kidney function.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Fondaparinux sodium',
    category: 'adjust_for_kidney_function',
    condition_name: 'Reduced kidney function',
    interacting_medication: null,
    recommendation: 'Avoid or adjust dosage in older adults with reduced kidney function.',
    rationale: 'Increased risk of bleeding in older adults with reduced kidney function.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Rivaroxaban',
    category: 'adjust_for_kidney_function',
    condition_name: 'Reduced kidney function',
    interacting_medication: null,
    recommendation: 'Avoid or adjust dosage in older adults with reduced kidney function.',
    rationale: 'Increased risk of bleeding in older adults with reduced kidney function.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // CNS medications
  {
    medication_name: 'Baclofen',
    category: 'adjust_for_kidney_function',
    condition_name: 'Reduced kidney function',
    interacting_medication: null,
    recommendation: 'Avoid or adjust dosage in older adults with reduced kidney function.',
    rationale: 'May cause CNS toxicity in older adults with reduced kidney function.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Duloxetine',
    category: 'adjust_for_kidney_function',
    condition_name: 'Reduced kidney function',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with CrCl <30 mL/min.',
    rationale: 'Increased risk of adverse GI effects (nausea, diarrhea) and CNS effects in older adults with reduced kidney function.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Gabapentin',
    category: 'adjust_for_kidney_function',
    condition_name: 'Reduced kidney function',
    interacting_medication: null,
    recommendation: 'Avoid or adjust dosage in older adults with reduced kidney function.',
    rationale: 'May cause CNS toxicity in older adults with reduced kidney function.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Levetiracetam',
    category: 'adjust_for_kidney_function',
    condition_name: 'Reduced kidney function',
    interacting_medication: null,
    recommendation: 'Avoid or adjust dosage in older adults with reduced kidney function.',
    rationale: 'May cause CNS toxicity in older adults with reduced kidney function.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Pregabalin',
    category: 'adjust_for_kidney_function',
    condition_name: 'Reduced kidney function',
    interacting_medication: null,
    recommendation: 'Avoid or adjust dosage in older adults with reduced kidney function.',
    rationale: 'May cause CNS toxicity in older adults with reduced kidney function.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Tramadol',
    category: 'adjust_for_kidney_function',
    condition_name: 'Reduced kidney function',
    interacting_medication: null,
    recommendation: 'Avoid or adjust dosage in older adults with reduced kidney function.',
    rationale: 'May cause CNS toxicity in older adults with reduced kidney function.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // Pain medications
  {
    medication_name: 'NSAIDs',
    category: 'adjust_for_kidney_function',
    condition_name: 'Reduced kidney function',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with CrCl <30 mL/min.',
    rationale: 'May increase risk of acute kidney injury and further decline in renal function.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // Gastrointestinal medications
  {
    medication_name: 'Cimetidine',
    category: 'adjust_for_kidney_function',
    condition_name: 'Reduced kidney function',
    interacting_medication: null,
    recommendation: 'Avoid or adjust dosage in older adults with reduced kidney function.',
    rationale: 'May cause CNS toxicity in older adults with reduced kidney function.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Famotidine',
    category: 'adjust_for_kidney_function',
    condition_name: 'Reduced kidney function',
    interacting_medication: null,
    recommendation: 'Avoid or adjust dosage in older adults with reduced kidney function.',
    rationale: 'May cause CNS toxicity in older adults with reduced kidney function.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Nizatidine',
    category: 'adjust_for_kidney_function',
    condition_name: 'Reduced kidney function',
    interacting_medication: null,
    recommendation: 'Avoid or adjust dosage in older adults with reduced kidney function.',
    rationale: 'May cause CNS toxicity in older adults with reduced kidney function.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },

  // Other medications
  {
    medication_name: 'Colchicine',
    category: 'adjust_for_kidney_function',
    condition_name: 'Reduced kidney function',
    interacting_medication: null,
    recommendation: 'Avoid or adjust dosage in older adults with reduced kidney function.',
    rationale: 'Increased risk of toxicity in older adults with reduced kidney function.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
  {
    medication_name: 'Probenecid',
    category: 'adjust_for_kidney_function',
    condition_name: 'Reduced kidney function',
    interacting_medication: null,
    recommendation: 'Avoid in older adults with CrCl <30 mL/min.',
    rationale: 'Ineffective in older adults with reduced kidney function.',
    quality_of_evidence: 'moderate',
    strength_of_recommendation: 'strong'
  },
];

/**
 * Populate the BEERS criteria table
 */
async function populateBeersCriteria() {
  const client = await pool.connect();

  try {
    // Begin transaction
    await client.query('BEGIN');

    // Clear existing data
    await client.query('DELETE FROM beers_criteria');

    // Insert new data
    for (const criteria of beersCriteriaData) {
      const query = `
        INSERT INTO beers_criteria (
          medication_name,
          category,
          condition_name,
          interacting_medication,
          recommendation,
          rationale,
          quality_of_evidence,
          strength_of_recommendation
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `;

      const values = [
        criteria.medication_name,
        criteria.category,
        criteria.condition_name,
        criteria.interacting_medication,
        criteria.recommendation,
        criteria.rationale,
        criteria.quality_of_evidence,
        criteria.strength_of_recommendation
      ];

      await client.query(query, values);
    }

    // Commit transaction
    await client.query('COMMIT');

    console.log(`Successfully populated BEERS criteria table with ${beersCriteriaData.length} entries.`);
  } catch (err) {
    // Rollback transaction on error
    await client.query('ROLLBACK');
    console.error('Error populating BEERS criteria table:', err);
    throw err;
  } finally {
    client.release();
  }
}

// Run the function
(async () => {
  try {
    await populateBeersCriteria();
    process.exit(0);
  } catch (err) {
    console.error('Script failed:', err);
    process.exit(1);
  }
})();
