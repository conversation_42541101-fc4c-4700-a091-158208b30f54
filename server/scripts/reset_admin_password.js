const bcrypt = require('bcryptjs');
const pool = require('../db');

async function resetAdminPassword() {
  try {
    console.log('Resetting admin password...');
    
    // Hash the new password 'admin@123'
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash('admin@123', salt);
    
    // Update the admin user's password
    const result = await pool.query(
      `UPDATE users 
       SET password = $1, 
           is_locked = false, 
           failed_login_attempts = 0, 
           lockout_until = NULL,
           is_first_login = false,
           default_password = false,
           password_last_changed = NOW()
       WHERE username = 'admin' 
       RETURNING user_id, username, email, role`,
      [hashedPassword]
    );
    
    if (result.rows.length === 0) {
      console.log('Admin user not found. Creating admin user...');
      
      // Create admin user if it doesn't exist
      const createResult = await pool.query(
        `INSERT INTO users (username, email, password, role, is_first_login, default_password, password_last_changed)
         VALUES ('admin', '<EMAIL>', $1, 'admin', false, false, NOW())
         RETURNING user_id, username, email, role`,
        [hashedPassword]
      );
      
      console.log('Admin user created successfully:', createResult.rows[0]);
    } else {
      console.log('Admin password reset successfully for user:', result.rows[0]);
    }
    
    console.log('Admin password has been set to: admin@123');
    
  } catch (error) {
    console.error('Error resetting admin password:', error);
  } finally {
    // Close the database connection
    await pool.end();
  }
}

// Run the script
resetAdminPassword();
