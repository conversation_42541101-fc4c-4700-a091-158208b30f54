/**
 * <PERSON><PERSON><PERSON> to populate the clinical guidance database with mental health guidance
 */

const db = require('../db');

// Guidance content organized by context
const guidanceContent = [
  // Depression Assessment (PHQ-9)
  {
    title: "Depression Assessment (PHQ-9)",
    content: `<p><strong>PHQ-9 (Patient Health Questionnaire-9):</strong> A validated screening tool for depression that assesses the frequency of depressive symptoms over the past 2 weeks.</p>
<p><strong>Scoring:</strong></p>
<ul>
<li>0-4: Minimal or no depression</li>
<li>5-9: Mild depression</li>
<li>10-14: Moderate depression</li>
<li>15-19: Moderately severe depression</li>
<li>20-27: Severe depression</li>
</ul>
<p><strong>Clinical Significance:</strong></p>
<ul>
<li>Scores ≥10 have a sensitivity of 88% and a specificity of 88% for major depression</li>
<li>Question 9 (thoughts of self-harm) requires immediate attention if scored above 0</li>
<li>Consider the impact of symptoms on daily functioning when interpreting scores</li>
</ul>
<p><strong>Considerations for Older Adults:</strong></p>
<ul>
<li>Depression may present differently in older adults, with more somatic complaints and fewer reports of dysphoria</li>
<li>Cognitive impairment may affect the reliability of self-reported symptoms</li>
<li>Medical comorbidities can complicate diagnosis and treatment</li>
<li>Social factors (isolation, loss, financial concerns) may contribute to depression in older adults</li>
</ul>
<p><strong>Follow-up Recommendations:</strong></p>
<ul>
<li>Mild depression: Consider watchful waiting, supportive counseling, lifestyle modifications</li>
<li>Moderate depression: Consider psychotherapy and/or pharmacotherapy</li>
<li>Moderately severe to severe depression: Recommend combined psychotherapy and pharmacotherapy</li>
<li>Any positive response to Question 9: Conduct thorough suicide risk assessment</li>
</ul>`,
    summary: "Guidance on administering and interpreting the PHQ-9 depression screening tool in older adults",
    category_id: 3, // Mental Health
    context_key: "mental_health_depression",
    is_published: true,
    tags: ["depression", "PHQ-9", "screening", "mental health", "geriatric psychiatry"]
  },
  
  // Anxiety Assessment (GAD-7)
  {
    title: "Anxiety Assessment (GAD-7)",
    content: `<p><strong>GAD-7 (Generalized Anxiety Disorder-7):</strong> A validated screening tool for anxiety that assesses the frequency of anxiety symptoms over the past 2 weeks.</p>
<p><strong>Scoring:</strong></p>
<ul>
<li>0-4: Minimal anxiety</li>
<li>5-9: Mild anxiety</li>
<li>10-14: Moderate anxiety</li>
<li>15-21: Severe anxiety</li>
</ul>
<p><strong>Clinical Significance:</strong></p>
<ul>
<li>Scores ≥10 have a sensitivity of 89% and a specificity of 82% for generalized anxiety disorder</li>
<li>The GAD-7 also has good sensitivity and specificity for panic disorder, social anxiety disorder, and post-traumatic stress disorder</li>
<li>Consider the impact of symptoms on daily functioning when interpreting scores</li>
</ul>
<p><strong>Considerations for Older Adults:</strong></p>
<ul>
<li>Anxiety may present differently in older adults, with more somatic complaints and fewer psychological symptoms</li>
<li>Medical conditions and medications can mimic or exacerbate anxiety symptoms</li>
<li>Cognitive impairment may affect the reliability of self-reported symptoms</li>
<li>Late-life anxiety is often comorbid with depression and cognitive disorders</li>
</ul>
<p><strong>Follow-up Recommendations:</strong></p>
<ul>
<li>Mild anxiety: Consider watchful waiting, supportive counseling, lifestyle modifications</li>
<li>Moderate anxiety: Consider psychotherapy (CBT is particularly effective) and/or pharmacotherapy</li>
<li>Severe anxiety: Recommend combined psychotherapy and pharmacotherapy</li>
<li>Rule out medical causes of anxiety (e.g., thyroid disorders, cardiac conditions, respiratory disorders)</li>
<li>Review medications that may contribute to anxiety (e.g., stimulants, steroids, bronchodilators)</li>
</ul>`,
    summary: "Guidance on administering and interpreting the GAD-7 anxiety screening tool in older adults",
    category_id: 3, // Mental Health
    context_key: "mental_health_anxiety",
    is_published: true,
    tags: ["anxiety", "GAD-7", "screening", "mental health", "geriatric psychiatry"]
  }
];

// Function to insert guidance content
async function populateGuidance() {
  try {
    console.log('Starting to populate mental health guidance...');

    // Insert each guidance entry
    for (const guidance of guidanceContent) {
      // Insert the guidance
      const guidanceResult = await db.query(
        `INSERT INTO clinical_guidance
         (title, content, summary, category_id, context_key, is_published, created_at, updated_at)
         VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
         RETURNING guidance_id`,
        [
          guidance.title,
          guidance.content,
          guidance.summary,
          guidance.category_id,
          guidance.context_key,
          guidance.is_published
        ]
      );

      const guidanceId = guidanceResult.rows[0].guidance_id;
      console.log(`Inserted guidance: ${guidance.title} (ID: ${guidanceId})`);

      // Insert tags if any
      if (guidance.tags && guidance.tags.length > 0) {
        for (const tag of guidance.tags) {
          // Check if tag exists
          const tagResult = await db.query(
            `SELECT tag_id FROM guidance_tags WHERE name = $1`,
            [tag]
          );

          let tagId;
          if (tagResult.rows.length > 0) {
            tagId = tagResult.rows[0].tag_id;
          } else {
            // Create new tag
            const newTagResult = await db.query(
              `INSERT INTO guidance_tags (name, created_at)
               VALUES ($1, NOW())
               RETURNING tag_id`,
              [tag]
            );
            tagId = newTagResult.rows[0].tag_id;
            console.log(`Created new tag: ${tag} (ID: ${tagId})`);
          }

          // Create tag relation
          await db.query(
            `INSERT INTO guidance_tag_relations (guidance_id, tag_id)
             VALUES ($1, $2)`,
            [guidanceId, tagId]
          );
        }
      }

      // Create initial version
      await db.query(
        `INSERT INTO guidance_versions
         (guidance_id, version_number, title, content, summary, category_id, created_at)
         VALUES ($1, 1, $2, $3, $4, $5, NOW())`,
        [
          guidanceId,
          guidance.title,
          guidance.content,
          guidance.summary,
          guidance.category_id
        ]
      );

      // Create audit entry
      await db.query(
        `INSERT INTO guidance_audit
         (guidance_id, action, details, performed_at)
         VALUES ($1, 'create', $2, NOW())`,
        [
          guidanceId,
          JSON.stringify({ title: guidance.title, context_key: guidance.context_key })
        ]
      );
    }

    console.log('Mental health guidance population completed successfully!');
  } catch (error) {
    console.error('Error populating mental health guidance:', error);
  } finally {
    // Close the database connection
    process.exit(0);
  }
}

// Run the population function
populateGuidance();
