/**
 * <PERSON><PERSON><PERSON> to populate the clinical guidance database with lab results guidance
 */

const db = require('../db');

// Guidance content organized by context
const guidanceContent = [
  // Diabetes Markers
  {
    title: "Diabetes Markers",
    content: `<p><strong>Blood Glucose:</strong> Normal fasting range is 70-99 mg/dL. Values 100-125 mg/dL indicate prediabetes, while ≥126 mg/dL suggests diabetes.</p>
<p><strong>HbA1c:</strong> Normal is &lt;5.7%. Values 5.7-6.4% indicate prediabetes, while ≥6.5% suggests diabetes.</p>
<p><strong>Considerations for Older Adults:</strong> Less stringent glycemic targets may be appropriate for older adults with multiple comorbidities or limited life expectancy (HbA1c 7.5-8.5%).</p>`,
    summary: "Guidance on interpreting diabetes markers in older adults",
    category_id: 2, // Lab Results
    context_key: "lab_results_diabetes",
    is_published: true,
    tags: ["diabetes", "blood glucose", "HbA1c", "glycemic control"]
  },
  
  // Lipid Profile
  {
    title: "Lipid Profile",
    content: `<p><strong>Total Cholesterol:</strong> Desirable &lt;200 mg/dL. Borderline high 200-239 mg/dL. High ≥240 mg/dL.</p>
<p><strong>HDL Cholesterol:</strong> Low &lt;40 mg/dL. Optimal ≥60 mg/dL. Higher levels are protective.</p>
<p><strong>LDL Cholesterol:</strong> Optimal &lt;100 mg/dL. Near optimal 100-129 mg/dL. Borderline high 130-159 mg/dL. High 160-189 mg/dL. Very high ≥190 mg/dL.</p>
<p><strong>VLDL Cholesterol:</strong> Normal &lt;30 mg/dL. Elevated levels are associated with increased cardiovascular risk.</p>
<p><strong>Triglycerides:</strong> Normal &lt;150 mg/dL. Borderline high 150-199 mg/dL. High 200-499 mg/dL. Very high ≥500 mg/dL.</p>`,
    summary: "Guidance on interpreting lipid profile results in older adults",
    category_id: 2, // Lab Results
    context_key: "lab_results_lipids",
    is_published: true,
    tags: ["cholesterol", "lipids", "LDL", "HDL", "triglycerides"]
  },
  
  // Kidney Function Tests
  {
    title: "Kidney Function Tests",
    content: `<p><strong>Creatinine:</strong> Normal range is 0.7-1.3 mg/dL for men and 0.6-1.1 mg/dL for women. Elevated levels suggest kidney dysfunction.</p>
<p><strong>eGFR:</strong> Normal is ≥90 mL/min/1.73m². Values 60-89 indicate mild kidney damage, 30-59 moderate damage, 15-29 severe damage, and &lt;15 kidney failure.</p>
<p><strong>Blood Urea Nitrogen (BUN):</strong> Normal range is 7-20 mg/dL. Elevated levels may indicate kidney dysfunction, dehydration, or high protein intake.</p>
<p><strong>Uric Acid:</strong> Normal range is 3.5-7.2 mg/dL for men and 2.6-6.0 mg/dL for women. Elevated levels are associated with gout and kidney stones.</p>
<p><strong>Urine Albumin-Creatinine Ratio:</strong> Normal is &lt;30 mg/g. Values 30-300 mg/g indicate microalbuminuria, while &gt;300 mg/g indicates macroalbuminuria.</p>
<p><strong>Important for BEERS Criteria:</strong> Entering kidney function data (eGFR and creatinine) is essential for proper medication alerts. Some medications will only trigger BEERS Criteria alerts when kidney function is impaired.</p>`,
    summary: "Guidance on interpreting kidney function test results in older adults",
    category_id: 2, // Lab Results
    context_key: "lab_results_kidney",
    is_published: true,
    tags: ["kidney", "creatinine", "eGFR", "BUN", "uric acid", "albumin-creatinine ratio"]
  },
  
  // Liver Function Tests
  {
    title: "Liver Function Tests",
    content: `<p><strong>ALT (Alanine Aminotransferase):</strong> Normal range is 7-56 U/L for males and 7-45 U/L for females. Elevated levels suggest liver damage.</p>
<p><strong>AST (Aspartate Aminotransferase):</strong> Normal range is 8-48 U/L for males and 8-43 U/L for females. Elevated levels suggest liver damage but are less specific than ALT.</p>
<p><strong>ALP (Alkaline Phosphatase):</strong> Normal range is 40-129 U/L for adults. Elevated levels may indicate bile duct obstruction or bone disorders.</p>
<p><strong>GGT (Gamma-Glutamyl Transferase):</strong> Normal range is 8-61 U/L for males and 5-36 U/L for females. Elevated levels may indicate alcohol use, liver disease, or bile duct obstruction.</p>
<p><strong>Total Bilirubin:</strong> Normal range is 0.1-1.2 mg/dL. Elevated levels may indicate liver disease, bile duct obstruction, or hemolysis.</p>
<p><strong>Direct Bilirubin:</strong> Normal range is 0.0-0.3 mg/dL. Elevated levels suggest liver disease or bile duct obstruction.</p>
<p><strong>Albumin:</strong> Normal range is 3.5-5.0 g/dL. Low levels may indicate liver disease, malnutrition, or kidney disease with protein loss.</p>
<p><strong>Total Protein:</strong> Normal range is 6.0-8.3 g/dL. Abnormal levels may indicate liver disease, kidney disease, or nutritional status.</p>`,
    summary: "Guidance on interpreting liver function test results in older adults",
    category_id: 2, // Lab Results
    context_key: "lab_results_liver",
    is_published: true,
    tags: ["liver", "ALT", "AST", "enzymes", "bilirubin", "albumin", "protein"]
  },
  
  // Electrolytes and Minerals
  {
    title: "Electrolytes and Minerals",
    content: `<p><strong>Sodium:</strong> Normal range is 135-145 mEq/L. Hyponatremia (&lt;135 mEq/L) may cause confusion, seizures. Hypernatremia (&gt;145 mEq/L) indicates dehydration.</p>
<p><strong>Potassium:</strong> Normal range is 3.5-5.0 mEq/L. Hypokalemia (&lt;3.5 mEq/L) or hyperkalemia (&gt;5.0 mEq/L) can cause cardiac arrhythmias.</p>
<p><strong>Calcium:</strong> Normal range is 8.5-10.5 mg/dL. Abnormal levels can affect bone health, muscle function, and cardiac rhythm.</p>
<p><strong>Magnesium:</strong> Normal range is 1.7-2.2 mg/dL. Deficiency can cause muscle cramps, arrhythmias, and neurological symptoms.</p>`,
    summary: "Guidance on interpreting electrolyte and mineral levels in older adults",
    category_id: 2, // Lab Results
    context_key: "lab_results_electrolytes",
    is_published: true,
    tags: ["electrolytes", "sodium", "potassium", "calcium", "magnesium"]
  },
  
  // Complete Blood Count
  {
    title: "Complete Blood Count",
    content: `<p><strong>Red Blood Cell Parameters:</strong></p>
<p><strong>RBC (Red Blood Cell Count):</strong> Normal range is 4.5-5.9 million cells/μL for males and 4.1-5.1 million cells/μL for females. Low values may indicate anemia, while high values may indicate polycythemia.</p>
<p><strong>Hemoglobin:</strong> Normal range is 13.5-17.5 g/dL for males and 12.0-15.5 g/dL for females. Low values indicate anemia, while high values may indicate polycythemia or dehydration.</p>
<p><strong>Hematocrit:</strong> Normal range is 41-50% for males and 36-44% for females. Low values indicate anemia, while high values may indicate polycythemia or dehydration.</p>
<p><strong>Platelets:</strong> Normal range is 150,000-450,000/μL. Low values may indicate thrombocytopenia, while high values may indicate thrombocytosis or inflammation.</p>
<p><strong>Red Blood Cell Indices:</strong></p>
<p><strong>MCV (Mean Corpuscular Volume):</strong> Normal range is 80-100 fL. Low values indicate microcytic anemia, while high values indicate macrocytic anemia.</p>
<p><strong>MCH (Mean Corpuscular Hemoglobin):</strong> Normal range is 27-33 pg. Low values may indicate hypochromic anemia, while high values may indicate macrocytic anemia.</p>
<p><strong>MCHC (Mean Corpuscular Hemoglobin Concentration):</strong> Normal range is 32-36 g/dL. Low values may indicate hypochromic anemia, while high values may indicate spherocytosis.</p>
<p><strong>RDW (Red Cell Distribution Width):</strong> Normal range is 11.5-14.5%. Elevated values may indicate anemia, especially iron deficiency anemia.</p>
<p><strong>White Blood Cell Count & Differential:</strong></p>
<p><strong>WBC (White Blood Cell Count):</strong> Normal range is 4,500-11,000/μL. Low values may indicate leukopenia, while high values may indicate infection, inflammation, or leukemia.</p>
<p><strong>Neutrophils:</strong> Normal range is 40-60% of total WBC. Elevated values may indicate bacterial infection or inflammation.</p>
<p><strong>Lymphocytes:</strong> Normal range is 20-40% of total WBC. Elevated values may indicate viral infection or certain leukemias.</p>
<p><strong>Monocytes:</strong> Normal range is 2-8% of total WBC. Elevated values may indicate chronic inflammation or certain infections.</p>
<p><strong>Eosinophils:</strong> Normal range is 1-4% of total WBC. Elevated values may indicate allergic reactions or parasitic infections.</p>
<p><strong>Basophils:</strong> Normal range is 0.5-1% of total WBC. Elevated values may indicate allergic reactions or certain leukemias.</p>`,
    summary: "Guidance on interpreting complete blood count results in older adults",
    category_id: 2, // Lab Results
    context_key: "lab_results_cbc",
    is_published: true,
    tags: ["CBC", "blood count", "RBC", "WBC", "hemoglobin", "platelets", "anemia"]
  },
  
  // Iron Studies
  {
    title: "Iron Studies",
    content: `<p><strong>Ferritin:</strong> Normal range is 20-250 ng/mL for males and 10-120 ng/mL for females. Low values indicate iron deficiency, while high values may indicate iron overload, inflammation, or liver disease.</p>
<p><strong>Iron:</strong> Normal range is 65-175 μg/dL for males and 50-170 μg/dL for females. Low values indicate iron deficiency, while high values may indicate iron overload or hemochromatosis.</p>`,
    summary: "Guidance on interpreting iron studies in older adults",
    category_id: 2, // Lab Results
    context_key: "lab_results_iron",
    is_published: true,
    tags: ["iron", "ferritin", "anemia", "iron deficiency", "hemochromatosis"]
  }
];

// Function to insert guidance content
async function populateGuidance() {
  try {
    console.log('Starting to populate lab results guidance tables...');

    // Insert each guidance entry
    for (const guidance of guidanceContent) {
      // Insert the guidance
      const guidanceResult = await db.query(
        `INSERT INTO clinical_guidance
         (title, content, summary, category_id, context_key, is_published, created_at, updated_at)
         VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
         RETURNING guidance_id`,
        [
          guidance.title,
          guidance.content,
          guidance.summary,
          guidance.category_id,
          guidance.context_key,
          guidance.is_published
        ]
      );

      const guidanceId = guidanceResult.rows[0].guidance_id;
      console.log(`Inserted guidance: ${guidance.title} (ID: ${guidanceId})`);

      // Insert tags if any
      if (guidance.tags && guidance.tags.length > 0) {
        for (const tag of guidance.tags) {
          // Check if tag exists
          const tagResult = await db.query(
            `SELECT tag_id FROM guidance_tags WHERE name = $1`,
            [tag]
          );

          let tagId;
          if (tagResult.rows.length > 0) {
            tagId = tagResult.rows[0].tag_id;
          } else {
            // Create new tag
            const newTagResult = await db.query(
              `INSERT INTO guidance_tags (name, created_at)
               VALUES ($1, NOW())
               RETURNING tag_id`,
              [tag]
            );
            tagId = newTagResult.rows[0].tag_id;
            console.log(`Created new tag: ${tag} (ID: ${tagId})`);
          }

          // Create tag relation
          await db.query(
            `INSERT INTO guidance_tag_relations (guidance_id, tag_id)
             VALUES ($1, $2)`,
            [guidanceId, tagId]
          );
        }
      }

      // Create initial version
      await db.query(
        `INSERT INTO guidance_versions
         (guidance_id, version_number, title, content, summary, category_id, created_at)
         VALUES ($1, 1, $2, $3, $4, $5, NOW())`,
        [
          guidanceId,
          guidance.title,
          guidance.content,
          guidance.summary,
          guidance.category_id
        ]
      );

      // Create audit entry
      await db.query(
        `INSERT INTO guidance_audit
         (guidance_id, action, details, performed_at)
         VALUES ($1, 'create', $2, NOW())`,
        [
          guidanceId,
          JSON.stringify({ title: guidance.title, context_key: guidance.context_key })
        ]
      );
    }

    console.log('Lab results guidance population completed successfully!');
  } catch (error) {
    console.error('Error populating lab results guidance:', error);
  } finally {
    // Close the database connection
    process.exit(0);
  }
}

// Run the population function
populateGuidance();
