const fs = require('fs');
const path = require('path');
const pool = require('../db');

async function runMigration() {
  const client = await pool.connect();
  try {
    console.log('Running IP restrictions migration...');
    
    // Read the migration SQL file
    const migrationPath = path.join(__dirname, '../sql/add_ip_restrictions_table.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Run the migration
    const result = await client.query(migrationSQL);
    
    console.log('Migration completed successfully!');
    console.log('Message:', result[result.length - 1]?.rows[0]?.message || 'IP restrictions table created');
    
  } catch (error) {
    console.error('Error running IP restrictions migration:', error);
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the migration
runMigration();
