/**
 * <PERSON><PERSON><PERSON> to populate the clinical guidance database with vaccination guidance
 */

const db = require('../db');

// Guidance content organized by context
const guidanceContent = [
  // Vaccinations for Older Adults
  {
    title: "Vaccinations for Older Adults",
    content: `<p><strong>Influenza (Flu):</strong> Annual vaccination is recommended for all adults, especially those 65 and older.</p>
<p><strong>Pneumococcal:</strong> Two types of pneumococcal vaccines are recommended for adults 65 and older: PCV13 and PPSV23, typically given 1 year apart.</p>
<p><strong>Zoster (Shingles):</strong> Two doses of Shingrix, 2-6 months apart, are recommended for adults 50 and older, regardless of previous shingles or Zostavax vaccination.</p>
<p><strong>Tdap/Td:</strong> One dose of Tdap if not previously received, followed by Td or Tdap booster every 10 years.</p>
<p><strong>COVID-19:</strong> Initial vaccination series plus recommended boosters according to current guidelines.</p>
<p><strong>Other Vaccines:</strong> Hepatitis A, Hepatitis B, and MMR may be recommended based on risk factors, previous vaccination history, and immunity status.</p>
<p><strong>Considerations for Older Adults:</strong></p>
<ul>
<li>Immune response may be reduced in older adults (immunosenescence)</li>
<li>Some vaccines have special formulations for older adults (e.g., high-dose or adjuvanted flu vaccines)</li>
<li>Vaccination history may be incomplete; consider serologic testing when appropriate</li>
<li>Timing between vaccines may be important for optimal response</li>
</ul>`,
    summary: "Guidance on recommended vaccinations for older adults",
    category_id: 4, // Preventive Care
    context_key: "vaccinations_older_adults",
    is_published: true,
    tags: ["vaccinations", "immunizations", "flu", "pneumococcal", "shingles", "COVID-19", "preventive care"]
  }
];

// Function to insert guidance content
async function populateGuidance() {
  try {
    console.log('Starting to populate vaccination guidance...');

    // Insert each guidance entry
    for (const guidance of guidanceContent) {
      // Insert the guidance
      const guidanceResult = await db.query(
        `INSERT INTO clinical_guidance
         (title, content, summary, category_id, context_key, is_published, created_at, updated_at)
         VALUES ($1, $2, $3, $4, $5, $6, NOW(), NOW())
         RETURNING guidance_id`,
        [
          guidance.title,
          guidance.content,
          guidance.summary,
          guidance.category_id,
          guidance.context_key,
          guidance.is_published
        ]
      );

      const guidanceId = guidanceResult.rows[0].guidance_id;
      console.log(`Inserted guidance: ${guidance.title} (ID: ${guidanceId})`);

      // Insert tags if any
      if (guidance.tags && guidance.tags.length > 0) {
        for (const tag of guidance.tags) {
          // Check if tag exists
          const tagResult = await db.query(
            `SELECT tag_id FROM guidance_tags WHERE name = $1`,
            [tag]
          );

          let tagId;
          if (tagResult.rows.length > 0) {
            tagId = tagResult.rows[0].tag_id;
          } else {
            // Create new tag
            const newTagResult = await db.query(
              `INSERT INTO guidance_tags (name, created_at)
               VALUES ($1, NOW())
               RETURNING tag_id`,
              [tag]
            );
            tagId = newTagResult.rows[0].tag_id;
            console.log(`Created new tag: ${tag} (ID: ${tagId})`);
          }

          // Create tag relation
          await db.query(
            `INSERT INTO guidance_tag_relations (guidance_id, tag_id)
             VALUES ($1, $2)`,
            [guidanceId, tagId]
          );
        }
      }

      // Create initial version
      await db.query(
        `INSERT INTO guidance_versions
         (guidance_id, version_number, title, content, summary, category_id, created_at)
         VALUES ($1, 1, $2, $3, $4, $5, NOW())`,
        [
          guidanceId,
          guidance.title,
          guidance.content,
          guidance.summary,
          guidance.category_id
        ]
      );

      // Create audit entry
      await db.query(
        `INSERT INTO guidance_audit
         (guidance_id, action, details, performed_at)
         VALUES ($1, 'create', $2, NOW())`,
        [
          guidanceId,
          JSON.stringify({ title: guidance.title, context_key: guidance.context_key })
        ]
      );
    }

    console.log('Vaccination guidance population completed successfully!');
  } catch (error) {
    console.error('Error populating vaccination guidance:', error);
  } finally {
    // Close the database connection
    process.exit(0);
  }
}

// Run the population function
populateGuidance();
