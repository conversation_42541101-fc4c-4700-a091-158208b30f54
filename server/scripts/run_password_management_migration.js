/**
 * <PERSON><PERSON><PERSON> to run the password management migration
 * This script applies the SQL migration for the password management system
 */

const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config({ path: path.resolve(__dirname, '../.env') });

// Create a new pool using the individual connection parameters from .env
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'medapp',
});

async function runMigration() {
  console.log('Starting password management migration...');

  try {
    // Read the migration SQL file
    const migrationPath = path.resolve(__dirname, '../sql/password_management_migration.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Connect to the database
    const client = await pool.connect();

    try {
      // Start a transaction
      await client.query('BEGIN');

      console.log('Executing migration SQL...');

      // Execute the migration SQL
      await client.query(migrationSQL);

      // Commit the transaction
      await client.query('COMMIT');

      console.log('Migration completed successfully!');
    } catch (err) {
      // Rollback the transaction on error
      await client.query('ROLLBACK');
      console.error('Error during migration, transaction rolled back:', err.message);
      throw err;
    } finally {
      // Release the client back to the pool
      client.release();
    }
  } catch (err) {
    console.error('Migration failed:', err.message);
    process.exit(1);
  } finally {
    // Close the pool
    await pool.end();
  }
}

// Run the migration
runMigration();
