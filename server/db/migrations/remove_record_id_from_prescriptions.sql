-- First, update existing prescriptions that have record_id but not patient_id
-- Get the patient_id from the medical_records table
UPDATE prescriptions p
SET patient_id = mr.patient_id,
    visit_type = 'initial'
FROM medical_records mr
WHERE p.record_id = mr.record_id
AND p.patient_id IS NULL
AND p.visit_id IS NULL;

-- Remove record_id column and constraint from prescriptions table
ALTER TABLE prescriptions
DROP CONSTRAINT IF EXISTS prescriptions_record_id_fkey;

-- Drop the check constraint that includes record_id
ALTER TABLE prescriptions
DROP CONSTRAINT IF EXISTS prescriptions_id_check;

-- Add a new check constraint that only checks for patient_id or visit_id
ALTER TABLE prescriptions
ADD CONSTRAINT prescriptions_id_check
CHECK (
  (patient_id IS NOT NULL) OR
  (visit_id IS NOT NULL)
);

-- Now it's safe to drop the record_id column
ALTER TABLE prescriptions
DROP COLUMN IF EXISTS record_id;
