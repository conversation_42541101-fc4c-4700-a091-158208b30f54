-- Add BEERS Criteria override columns to the prescriptions table
ALTER TABLE prescriptions
ADD COLUMN IF NOT EXISTS beers_criteria_id INTEGER REFERENCES beers_criteria(criteria_id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS beers_override_reason TEXT,
ADD COLUMN IF NOT EXISTS beers_overridden_by INTEGER REFERENCES users(user_id) ON DELETE SET NULL,
ADD COLUMN IF NOT EXISTS beers_overridden_at TIMESTAMP WITHOUT TIME ZONE;

-- Add comments to explain the columns
COMMENT ON COLUMN prescriptions.beers_criteria_id IS 'ID of the BEERS criterion that was triggered for this prescription';
COMMENT ON COLUMN prescriptions.beers_override_reason IS 'Clinical reason provided for overriding the BEERS Criteria alert';
COMMENT ON COLUMN prescriptions.beers_overridden_by IS 'ID of the user who overrode the BEERS Criteria alert';
COMMENT ON COLUMN prescriptions.beers_overridden_at IS 'Timestamp when the BEERS Criteria alert was overridden';
