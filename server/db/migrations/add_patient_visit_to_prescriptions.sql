-- Add patient_id, visit_id, and visit_type columns to prescriptions table
ALTER TABLE prescriptions 
ADD COLUMN IF NOT EXISTS patient_id INTEGER REFERENCES patients(patient_id) ON DELETE CASCADE,
ADD COLUMN IF NOT EXISTS visit_id INTEGER REFERENCES patient_visits(visit_id) ON DELETE CASCADE,
ADD COLUMN IF NOT EXISTS visit_type VARCHAR(20) DEFAULT 'initial';

-- Add comment to explain the relationship
COMMENT ON COLUMN prescriptions.patient_id IS 'Direct reference to patient for initial visit prescriptions';
COMMENT ON COLUMN prescriptions.visit_id IS 'Reference to patient_visits for follow-up visit prescriptions';
COMMENT ON COLUMN prescriptions.visit_type IS 'Type of visit: initial or follow-up';

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_prescriptions_patient_id ON prescriptions(patient_id);
CREATE INDEX IF NOT EXISTS idx_prescriptions_visit_id ON prescriptions(visit_id);

-- Make record_id nullable (it was previously required)
ALTER TABLE prescriptions ALTER COLUMN record_id DROP NOT NULL;

-- Add constraint to ensure at least one ID is provided
ALTER TABLE prescriptions 
ADD CONSTRAINT prescriptions_id_check 
CHECK (
  (record_id IS NOT NULL) OR 
  (patient_id IS NOT NULL) OR 
  (visit_id IS NOT NULL)
);
