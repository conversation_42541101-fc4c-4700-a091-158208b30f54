-- Add prescribed_by column to track who created the prescription
ALTER TABLE prescriptions 
ADD COLUMN IF NOT EXISTS prescribed_by INTEGER REFERENCES users(user_id);

-- Add comment to explain the field
COMMENT ON COLUMN prescriptions.prescribed_by IS 'Reference to the user who prescribed the medication';

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_prescriptions_prescribed_by ON prescriptions(prescribed_by);

-- Add active flag to track if prescription is currently active
ALTER TABLE prescriptions
ADD COLUMN IF NOT EXISTS active BOOLEAN DEFAULT TRUE;

-- Add comment to explain the field
COMMENT ON COLUMN prescriptions.active IS 'Indicates if the prescription is currently active';

-- Add prescribed_date to track when the prescription was created
ALTER TABLE prescriptions
ADD COLUMN IF NOT EXISTS prescribed_date DATE DEFAULT CURRENT_DATE;

-- Add comment to explain the field
COMMENT ON COLUMN prescriptions.prescribed_date IS 'Date when the prescription was created';

-- Note: We're keeping record_id for backward compatibility, but it's now redundant
-- In a future migration, we could remove it after ensuring all data is properly migrated
