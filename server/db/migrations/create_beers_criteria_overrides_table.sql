-- Create a table to store BEERS Criteria overrides
CREATE TABLE IF NOT EXISTS beers_criteria_overrides (
  override_id SERIAL PRIMARY KEY,
  patient_id INTEGER NOT NULL REFERENCES patients(patient_id) ON DELETE CASCADE,
  visit_id INTEGER REFERENCES patient_visits(visit_id) ON DELETE CASCADE,
  prescription_id INTEGER REFERENCES prescriptions(prescription_id) ON DELETE CASCADE,
  criteria_id INTEGER NOT NULL REFERENCES beers_criteria(criteria_id) ON DELETE CASCADE,
  medication_name TEXT NOT NULL,
  override_reason TEXT NOT NULL,
  overridden_by INTEGER REFERENCES users(user_id) ON DELETE SET NULL,
  overridden_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP WITHOUT TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS beers_criteria_overrides_patient_id_idx ON beers_criteria_overrides(patient_id);
CREATE INDEX IF NOT EXISTS beers_criteria_overrides_visit_id_idx ON beers_criteria_overrides(visit_id);
CREATE INDEX IF NOT EXISTS beers_criteria_overrides_prescription_id_idx ON beers_criteria_overrides(prescription_id);
CREATE INDEX IF NOT EXISTS beers_criteria_overrides_criteria_id_idx ON beers_criteria_overrides(criteria_id);

-- Add comments to explain the table and columns
COMMENT ON TABLE beers_criteria_overrides IS 'Stores information about BEERS Criteria alerts that have been overridden';
COMMENT ON COLUMN beers_criteria_overrides.override_id IS 'Unique identifier for the override';
COMMENT ON COLUMN beers_criteria_overrides.patient_id IS 'ID of the patient for whom the override was made';
COMMENT ON COLUMN beers_criteria_overrides.visit_id IS 'ID of the visit during which the override was made (if applicable)';
COMMENT ON COLUMN beers_criteria_overrides.prescription_id IS 'ID of the prescription that triggered the alert (if applicable)';
COMMENT ON COLUMN beers_criteria_overrides.criteria_id IS 'ID of the BEERS criterion that was overridden';
COMMENT ON COLUMN beers_criteria_overrides.medication_name IS 'Name of the medication that triggered the alert';
COMMENT ON COLUMN beers_criteria_overrides.override_reason IS 'Clinical reason provided for overriding the alert';
COMMENT ON COLUMN beers_criteria_overrides.overridden_by IS 'ID of the user who overrode the alert';
COMMENT ON COLUMN beers_criteria_overrides.overridden_at IS 'Timestamp when the alert was overridden';
COMMENT ON COLUMN beers_criteria_overrides.is_active IS 'Whether the override is still active';
COMMENT ON COLUMN beers_criteria_overrides.created_at IS 'Timestamp when the override was created';
