const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

// Create a pool connection using the same configuration as the main app
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'medapp',
});

// Get the migration file name from command line arguments
const migrationFile = process.argv[2];

if (!migrationFile) {
  console.error('Please provide a migration file name');
  process.exit(1);
}

const migrationPath = path.join(__dirname, 'migrations', migrationFile);

// Check if the file exists
if (!fs.existsSync(migrationPath)) {
  console.error(`Migration file ${migrationPath} does not exist`);
  process.exit(1);
}

// Read the migration file
const sql = fs.readFileSync(migrationPath, 'utf8');

// Run the migration
async function runMigration() {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');
    console.log(`Running migration: ${migrationFile}`);
    await client.query(sql);
    await client.query('COMMIT');
    console.log('Migration completed successfully');
  } catch (err) {
    await client.query('ROLLBACK');
    console.error('Error running migration:', err);
    process.exit(1);
  } finally {
    client.release();
    await pool.end();
  }
}

runMigration();
