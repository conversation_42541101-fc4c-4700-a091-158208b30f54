/**
 * Test script for vaccination auto-fill functionality
 * 
 * This script tests the auto-fill functionality for vaccination dates:
 * 1. For a new patient with no previous visits, it should fetch vaccination data from the patients table
 * 2. For a patient with previous visits, it should fetch vaccination data from the most recent visit
 */

const db = require('../db');
const { Pool } = require('pg');

// Create a test patient with vaccination data
async function createTestPatient() {
  try {
    // Generate a unique identifier for this test
    const testId = Date.now().toString();
    
    // Create a test patient with vaccination data
    const patientResult = await db.query(`
      INSERT INTO patients (
        first_name, 
        last_name, 
        date_of_birth, 
        gender,
        hepatitis_b_vaccination_date,
        mmr_vaccination_date,
        varicella_vaccination_date
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7
      ) RETURNING patient_id
    `, [
      `Test${testId}`, 
      'Patient', 
      '1950-01-01', 
      'Male',
      '2020-01-15', // hepatitis_b_vaccination_date
      '2020-02-20', // mmr_vaccination_date
      '2020-03-25'  // varicella_vaccination_date
    ]);
    
    const patientId = patientResult.rows[0].patient_id;
    console.log(`Created test patient with ID: ${patientId}`);
    
    return patientId;
  } catch (error) {
    console.error('Error creating test patient:', error);
    throw error;
  }
}

// Create a test visit with updated vaccination data
async function createTestVisit(patientId) {
  try {
    // Create a test visit with updated vaccination data
    const visitResult = await db.query(`
      INSERT INTO patient_visits (
        patient_id,
        visit_date,
        visit_reason,
        hepatitis_b_vaccination_date,
        mmr_vaccination_date,
        varicella_vaccination_date,
        influenza_vaccination_date
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7
      ) RETURNING visit_id
    `, [
      patientId,
      new Date().toISOString().split('T')[0],
      'Test Visit',
      '2021-01-15', // Updated hepatitis_b_vaccination_date
      '2021-02-20', // Updated mmr_vaccination_date
      '2021-03-25', // Updated varicella_vaccination_date
      '2021-04-30'  // New influenza_vaccination_date
    ]);
    
    const visitId = visitResult.rows[0].visit_id;
    console.log(`Created test visit with ID: ${visitId} for patient ID: ${patientId}`);
    
    return visitId;
  } catch (error) {
    console.error('Error creating test visit:', error);
    throw error;
  }
}

// Test fetching vaccination data from patient record (no visits)
async function testFetchFromPatient(patientId) {
  try {
    // Fetch patient data
    const patientResult = await db.query(`
      SELECT 
        hepatitis_b_vaccination_date,
        mmr_vaccination_date,
        varicella_vaccination_date
      FROM patients
      WHERE patient_id = $1
    `, [patientId]);
    
    if (patientResult.rows.length === 0) {
      console.error('Test patient not found');
      return false;
    }
    
    const patientData = patientResult.rows[0];
    console.log('Vaccination data from patient record:');
    console.log(patientData);
    
    return true;
  } catch (error) {
    console.error('Error testing fetch from patient:', error);
    return false;
  }
}

// Test fetching vaccination data from most recent visit
async function testFetchFromVisit(patientId) {
  try {
    // Fetch most recent visit data
    const visitResult = await db.query(`
      SELECT 
        hepatitis_b_vaccination_date,
        mmr_vaccination_date,
        varicella_vaccination_date,
        influenza_vaccination_date
      FROM patient_visits
      WHERE patient_id = $1
      ORDER BY visit_date DESC
      LIMIT 1
    `, [patientId]);
    
    if (visitResult.rows.length === 0) {
      console.error('No visits found for test patient');
      return false;
    }
    
    const visitData = visitResult.rows[0];
    console.log('Vaccination data from most recent visit:');
    console.log(visitData);
    
    return true;
  } catch (error) {
    console.error('Error testing fetch from visit:', error);
    return false;
  }
}

// Clean up test data
async function cleanupTestData(patientId) {
  try {
    // Delete test visits
    await db.query('DELETE FROM patient_visits WHERE patient_id = $1', [patientId]);
    console.log(`Deleted test visits for patient ID: ${patientId}`);
    
    // Delete test patient
    await db.query('DELETE FROM patients WHERE patient_id = $1', [patientId]);
    console.log(`Deleted test patient with ID: ${patientId}`);
    
    return true;
  } catch (error) {
    console.error('Error cleaning up test data:', error);
    return false;
  }
}

// Run the tests
async function runTests() {
  let patientId = null;
  
  try {
    // Create test patient
    patientId = await createTestPatient();
    
    // Test fetching from patient record (no visits yet)
    console.log('\n--- Testing fetch from patient record (no visits) ---');
    await testFetchFromPatient(patientId);
    
    // Create test visit
    await createTestVisit(patientId);
    
    // Test fetching from most recent visit
    console.log('\n--- Testing fetch from most recent visit ---');
    await testFetchFromVisit(patientId);
    
    console.log('\nAll tests completed successfully!');
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    // Clean up test data
    if (patientId) {
      console.log('\n--- Cleaning up test data ---');
      await cleanupTestData(patientId);
    }
    
    // Close database connection
    await db.end();
    console.log('Database connection closed');
  }
}

// Run the tests
runTests();
