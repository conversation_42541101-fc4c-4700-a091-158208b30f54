/**
 * <PERSON><PERSON><PERSON> to check if sleep_patterns field exists in the database
 */

const { Pool } = require('pg');
require('dotenv').config();

// Create a connection to the database
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'medapp',
  password: process.env.DB_PASSWORD || '',
  port: process.env.DB_PORT || 5432,
});

async function checkSleepPatternsField() {
  try {
    console.log('Checking if sleep_patterns field exists in the database...');

    // Check patients table
    const patientsQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'patients' 
      AND column_name = 'sleep_patterns'
    `;
    const patientsResult = await pool.query(patientsQuery);
    
    console.log('Patients table:');
    if (patientsResult.rows.length === 0) {
      console.log('  sleep_patterns field does NOT exist in patients table');
    } else {
      console.log('  sleep_patterns field EXISTS in patients table');
    }

    // Check patient_visits table
    const visitsQuery = `
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'patient_visits' 
      AND column_name = 'sleep_patterns'
    `;
    const visitsResult = await pool.query(visitsQuery);
    
    console.log('Patient_visits table:');
    if (visitsResult.rows.length === 0) {
      console.log('  sleep_patterns field does NOT exist in patient_visits table');
    } else {
      console.log('  sleep_patterns field EXISTS in patient_visits table');
    }

  } catch (err) {
    console.error('❌ Error checking sleep_patterns field:', err);
  } finally {
    // Close the pool
    await pool.end();
    console.log('Database connection closed');
  }
}

// Run the function
checkSleepPatternsField();
