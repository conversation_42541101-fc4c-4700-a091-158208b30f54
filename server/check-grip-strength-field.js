/**
 * <PERSON><PERSON><PERSON> to check if grip_strength field exists in the database
 */

const { Pool } = require('pg');
require('dotenv').config();

// Create a connection to the database
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'medapp',
  password: process.env.DB_PASSWORD || '',
  port: process.env.DB_PORT || 5432,
});

async function checkGripStrengthField() {
  try {
    console.log('Checking if grip_strength field exists in the database...');

    // Check patients table
    const patientsQuery = `
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'patients' 
      AND column_name = 'grip_strength'
    `;
    const patientsResult = await pool.query(patientsQuery);
    
    console.log('Patients table:');
    if (patientsResult.rows.length === 0) {
      console.log('  grip_strength field does NOT exist in patients table');
    } else {
      console.log(`  grip_strength field EXISTS in patients table with data type: ${patientsResult.rows[0].data_type}`);
    }

    // Check patient_visits table
    const visitsQuery = `
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name = 'patient_visits' 
      AND column_name = 'grip_strength'
    `;
    const visitsResult = await pool.query(visitsQuery);
    
    console.log('Patient_visits table:');
    if (visitsResult.rows.length === 0) {
      console.log('  grip_strength field does NOT exist in patient_visits table');
    } else {
      console.log(`  grip_strength field EXISTS in patient_visits table with data type: ${visitsResult.rows[0].data_type}`);
    }

  } catch (err) {
    console.error('❌ Error checking grip_strength field:', err);
  } finally {
    // Close the pool
    await pool.end();
    console.log('Database connection closed');
  }
}

// Run the function
checkGripStrengthField();
