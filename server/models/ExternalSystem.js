const pool = require('../db');
const logger = require('../utils/logger');

class ExternalSystem {
  // Create a new external system
  static async create(systemData, userId) {
    try {
      const { name, type, description, connection_details, credentials, status, data_mapping } = systemData;
      
      const result = await pool.query(
        `INSERT INTO external_systems (
          name, 
          type, 
          description, 
          connection_details, 
          credentials, 
          status, 
          data_mapping, 
          created_by
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) 
        RETURNING *`,
        [
          name,
          type,
          description,
          connection_details || {},
          credentials || null,
          status || 'disconnected',
          data_mapping || {},
          userId
        ]
      );

      logger.info(`External system created: ${name} by user ${userId}`);
      return result.rows[0];
    } catch (err) {
      logger.error('Error in ExternalSystem.create():', err.message);
      throw err;
    }
  }

  // Get all external systems
  static async getAll() {
    try {
      const result = await pool.query(
        `SELECT 
          s.system_id, 
          s.name, 
          s.type, 
          s.description, 
          s.connection_details, 
          s.status, 
          s.data_mapping, 
          s.created_at, 
          s.updated_at,
          u.username as created_by_username
        FROM external_systems s
        LEFT JOIN users u ON s.created_by = u.user_id
        ORDER BY s.created_at DESC`
      );
      
      return result.rows;
    } catch (err) {
      logger.error('Error in ExternalSystem.getAll():', err.message);
      throw err;
    }
  }

  // Get external system by ID
  static async getById(systemId) {
    try {
      const result = await pool.query(
        `SELECT 
          s.system_id, 
          s.name, 
          s.type, 
          s.description, 
          s.connection_details, 
          s.status, 
          s.data_mapping, 
          s.created_at, 
          s.updated_at,
          u.username as created_by_username
        FROM external_systems s
        LEFT JOIN users u ON s.created_by = u.user_id
        WHERE s.system_id = $1`,
        [systemId]
      );
      
      return result.rows[0];
    } catch (err) {
      logger.error(`Error in ExternalSystem.getById(${systemId}):`, err.message);
      throw err;
    }
  }

  // Update an external system
  static async update(systemId, systemData, userId) {
    try {
      const { name, type, description, connection_details, credentials, status, data_mapping } = systemData;
      
      // Check if credentials are being updated
      let credentialsUpdate = '';
      let params = [
        name,
        type,
        description,
        connection_details || {},
        status || 'disconnected',
        data_mapping || {},
        systemId
      ];
      
      if (credentials !== undefined) {
        credentialsUpdate = ', credentials = $8';
        params.push(credentials);
      }
      
      const result = await pool.query(
        `UPDATE external_systems 
        SET 
          name = $1, 
          type = $2, 
          description = $3, 
          connection_details = $4, 
          status = $5, 
          data_mapping = $6, 
          updated_at = CURRENT_TIMESTAMP
          ${credentialsUpdate}
        WHERE system_id = $7 
        RETURNING *`,
        params
      );

      logger.info(`External system updated: ${systemId} by user ${userId}`);
      return result.rows[0];
    } catch (err) {
      logger.error(`Error in ExternalSystem.update(${systemId}):`, err.message);
      throw err;
    }
  }

  // Delete an external system
  static async delete(systemId, userId) {
    try {
      const result = await pool.query(
        'DELETE FROM external_systems WHERE system_id = $1 RETURNING *',
        [systemId]
      );
      
      logger.info(`External system deleted: ${systemId} by user ${userId}`);
      return result.rows[0];
    } catch (err) {
      logger.error(`Error in ExternalSystem.delete(${systemId}):`, err.message);
      throw err;
    }
  }

  // Update external system status
  static async updateStatus(systemId, status, userId) {
    try {
      const result = await pool.query(
        `UPDATE external_systems 
        SET 
          status = $1, 
          updated_at = CURRENT_TIMESTAMP
        WHERE system_id = $2 
        RETURNING *`,
        [status, systemId]
      );

      logger.info(`External system status updated: ${systemId} to ${status} by user ${userId}`);
      return result.rows[0];
    } catch (err) {
      logger.error(`Error in ExternalSystem.updateStatus(${systemId}):`, err.message);
      throw err;
    }
  }

  // Test connection to external system
  static async testConnection(systemId) {
    try {
      // Get the system details
      const system = await this.getById(systemId);
      
      if (!system) {
        throw new Error('External system not found');
      }
      
      // This is a placeholder for actual connection testing logic
      // In a real implementation, this would use the connection_details and credentials
      // to attempt to connect to the external system
      
      // For now, we'll simulate a successful connection
      const connectionSuccessful = Math.random() > 0.2; // 80% success rate
      
      // Update the system status based on the connection result
      const newStatus = connectionSuccessful ? 'connected' : 'error';
      await this.updateStatus(systemId, newStatus, 'system');
      
      return {
        success: connectionSuccessful,
        message: connectionSuccessful ? 'Connection successful' : 'Connection failed',
        details: {
          timestamp: new Date().toISOString(),
          status: newStatus
        }
      };
    } catch (err) {
      logger.error(`Error in ExternalSystem.testConnection(${systemId}):`, err.message);
      
      // Update the system status to error
      await this.updateStatus(systemId, 'error', 'system').catch(() => {});
      
      return {
        success: false,
        message: `Connection test failed: ${err.message}`,
        details: {
          timestamp: new Date().toISOString(),
          status: 'error',
          error: err.message
        }
      };
    }
  }
}

module.exports = ExternalSystem;
