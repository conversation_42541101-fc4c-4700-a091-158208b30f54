const pool = require('../db');

class PatientAccessLog {
  // Create a new access log entry
  static async create(patientId, userId, accessType, ipAddress = null, userAgent = null) {
    try {
      const result = await pool.query(
        `INSERT INTO patient_access_logs
         (patient_id, user_id, access_type, ip_address, user_agent)
         VALUES ($1, $2, $3, $4, $5)
         RETURNING *`,
        [patientId, userId, accessType, ipAddress, userAgent]
      );
      return result.rows[0];
    } catch (err) {
      console.error('Error creating access log:', err);
      throw err;
    }
  }

  // Get all access logs for a patient
  static async getByPatientId(patientId) {
    try {
      const result = await pool.query(
        `SELECT al.*, u.username, u.email, u.role
         FROM patient_access_logs al
         JOIN users u ON al.user_id = u.user_id
         WHERE al.patient_id = $1
         ORDER BY al.access_time DESC`,
        [patientId]
      );
      return result.rows;
    } catch (err) {
      console.error('Error fetching patient access logs:', err);
      throw err;
    }
  }

  // Get all access logs for admin (paginated)
  static async getAll(page = 1, limit = 20) {
    console.log('PatientAccessLog.getAll called with page:', page, 'limit:', limit);
    try {
      // First check if table exists
      const tableCheck = await pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_name = 'patient_access_logs'
        )
      `);

      const tableExists = tableCheck.rows[0].exists;
      console.log('patient_access_logs table exists:', tableExists);

      if (!tableExists) {
        return {
          logs: [],
          totalLogs: 0,
          totalPages: 0,
          currentPage: page
        };
      }

      // Get total count for pagination
      const countResult = await pool.query('SELECT COUNT(*) FROM patient_access_logs');
      const totalLogs = parseInt(countResult.rows[0].count);
      console.log('Total access logs found:', totalLogs);

      // Calculate pagination
      const offset = (page - 1) * limit;
      const totalPages = Math.ceil(totalLogs / limit);

      // Get logs with user and patient information
      const result = await pool.query(`
        SELECT
          l.log_id,
          l.patient_id,
          l.user_id,
          l.access_time,
          l.access_type,
          l.ip_address,
          l.user_agent,
          u.username,
          p.first_name,
          p.last_name,
          p.unique_id
        FROM patient_access_logs l
        LEFT JOIN users u ON l.user_id = u.user_id
        LEFT JOIN patients p ON l.patient_id = p.patient_id
        ORDER BY l.access_time DESC
        LIMIT $1 OFFSET $2
      `, [limit, offset]);

      console.log('Fetched access logs:', result.rows.length);

      return {
        logs: result.rows,
        totalLogs,
        totalPages,
        currentPage: page
      };
    } catch (err) {
      console.error('Error in PatientAccessLog.getAll:', err);
      throw err;
    }
  }

  // Get access logs by user
  static async getByUserId(userId, page = 1, limit = 20) {
    const offset = (page - 1) * limit;
    try {
      const result = await pool.query(
        `SELECT al.*,
          p.first_name as patient_first_name,
          p.last_name as patient_last_name,
          p.unique_id as patient_unique_id
         FROM patient_access_logs al
         JOIN patients p ON al.patient_id = p.patient_id
         WHERE al.user_id = $1
         ORDER BY al.access_time DESC
         LIMIT $2 OFFSET $3`,
        [userId, limit, offset]
      );

      const countResult = await pool.query(
        'SELECT COUNT(*) FROM patient_access_logs WHERE user_id = $1',
        [userId]
      );

      return {
        logs: result.rows,
        total: parseInt(countResult.rows[0].count),
        page,
        limit,
        totalPages: Math.ceil(parseInt(countResult.rows[0].count) / limit)
      };
    } catch (err) {
      console.error('Error fetching user access logs:', err);
      throw err;
    }
  }
}

module.exports = PatientAccessLog;