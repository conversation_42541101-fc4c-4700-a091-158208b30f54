const pool = require('../db');

class ClinicalGuidance {
  /**
   * Get all clinical guidance entries
   * @param {Object} options - Query options
   * @param {string} options.search - Search term for title or content
   * @param {number} options.categoryId - Filter by category ID
   * @param {string} options.contextKey - Filter by context key
   * @param {Array<string>} options.tags - Filter by tags
   * @param {boolean} options.publishedOnly - Only return published guidance
   * @param {string} options.role - User role for access control
   * @returns {Promise<Array>} Array of guidance entries
   */
  static async getAll(options = {}) {
    try {
      const {
        search,
        categoryId,
        contextKey,
        tags,
        publishedOnly = false,
        role
      } = options;

      let query = `
        SELECT g.*, 
               c.name AS category_name,
               u.username AS created_by_username,
               ARRAY_AGG(DISTINCT t.name) AS tags
        FROM clinical_guidance g
        LEFT JOIN guidance_categories c ON g.category_id = c.category_id
        LEFT JOIN users u ON g.created_by = u.user_id
        LEFT JOIN guidance_tag_relations gtr ON g.guidance_id = gtr.guidance_id
        LEFT JOIN guidance_tags t ON gtr.tag_id = t.tag_id
      `;

      // Add access control if role is provided
      if (role) {
        query += `
          LEFT JOIN guidance_access ga ON g.guidance_id = ga.guidance_id AND ga.role = $1
        `;
      }

      query += ' WHERE 1=1';

      // Add filters
      const params = role ? [role] : [];
      let paramIndex = params.length + 1;

      if (publishedOnly) {
        query += ` AND g.is_published = true`;
      }

      if (search) {
        query += ` AND (g.title ILIKE $${paramIndex} OR g.content ILIKE $${paramIndex})`;
        params.push(`%${search}%`);
        paramIndex++;
      }

      if (categoryId) {
        query += ` AND g.category_id = $${paramIndex}`;
        params.push(categoryId);
        paramIndex++;
      }

      if (contextKey) {
        query += ` AND g.context_key = $${paramIndex}`;
        params.push(contextKey);
        paramIndex++;
      }

      // Add access control condition if role is provided
      if (role) {
        query += ` AND (ga.can_view = true OR ga.guidance_id IS NULL)`;
      }

      // Group by to handle the array_agg
      query += `
        GROUP BY g.guidance_id, c.name, u.username
        ORDER BY g.updated_at DESC
      `;

      // Filter by tags after grouping (using HAVING)
      if (tags && tags.length > 0) {
        query += ` HAVING ARRAY_AGG(t.name) @> ARRAY[${tags.map((_, i) => `$${paramIndex + i}`).join(', ')}]`;
        params.push(...tags);
      }

      const result = await pool.query(query, params);
      return result.rows;
    } catch (err) {
      console.error('Error getting clinical guidance:', err);
      throw err;
    }
  }

  /**
   * Get a single clinical guidance entry by ID
   * @param {number} guidanceId - Guidance ID
   * @param {string} role - User role for access control
   * @returns {Promise<Object>} Guidance entry
   */
  static async getById(guidanceId, role = null) {
    try {
      let query = `
        SELECT g.*, 
               c.name AS category_name,
               u.username AS created_by_username,
               ARRAY_AGG(DISTINCT t.name) AS tags
        FROM clinical_guidance g
        LEFT JOIN guidance_categories c ON g.category_id = c.category_id
        LEFT JOIN users u ON g.created_by = u.user_id
        LEFT JOIN guidance_tag_relations gtr ON g.guidance_id = gtr.guidance_id
        LEFT JOIN guidance_tags t ON gtr.tag_id = t.tag_id
      `;

      // Add access control if role is provided
      if (role) {
        query += `
          LEFT JOIN guidance_access ga ON g.guidance_id = ga.guidance_id AND ga.role = $2
        `;
      }

      query += `
        WHERE g.guidance_id = $1
      `;

      // Add access control condition if role is provided
      if (role) {
        query += ` AND (ga.can_view = true OR ga.guidance_id IS NULL)`;
      }

      query += `
        GROUP BY g.guidance_id, c.name, u.username
      `;

      const params = role ? [guidanceId, role] : [guidanceId];
      const result = await pool.query(query, params);

      if (result.rows.length === 0) {
        return null;
      }

      return result.rows[0];
    } catch (err) {
      console.error(`Error getting clinical guidance with ID ${guidanceId}:`, err);
      throw err;
    }
  }

  /**
   * Create a new clinical guidance entry
   * @param {Object} guidanceData - Guidance data
   * @param {number} userId - User ID of the creator
   * @returns {Promise<Object>} Created guidance entry
   */
  static async create(guidanceData, userId) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      const {
        title,
        content,
        summary,
        categoryId,
        contextKey,
        isPublished = false,
        tags = []
      } = guidanceData;

      // Insert the guidance
      const guidanceQuery = `
        INSERT INTO clinical_guidance (
          title, content, summary, category_id, context_key, is_published, created_by
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING *
      `;
      const guidanceParams = [title, content, summary, categoryId, contextKey, isPublished, userId];
      const guidanceResult = await client.query(guidanceQuery, guidanceParams);
      const guidance = guidanceResult.rows[0];

      // Insert the initial version
      const versionQuery = `
        INSERT INTO guidance_versions (
          guidance_id, version_number, title, content, summary, category_id, created_by
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7)
      `;
      const versionParams = [guidance.guidance_id, 1, title, content, summary, categoryId, userId];
      await client.query(versionQuery, versionParams);

      // Insert tags
      if (tags.length > 0) {
        for (const tagName of tags) {
          // Insert tag if it doesn't exist
          const tagQuery = `
            INSERT INTO guidance_tags (name)
            VALUES ($1)
            ON CONFLICT (name) DO NOTHING
            RETURNING tag_id
          `;
          const tagResult = await client.query(tagQuery, [tagName]);
          
          // Get tag ID (either from insert or existing)
          let tagId;
          if (tagResult.rows.length > 0) {
            tagId = tagResult.rows[0].tag_id;
          } else {
            const existingTagResult = await client.query(
              'SELECT tag_id FROM guidance_tags WHERE name = $1',
              [tagName]
            );
            tagId = existingTagResult.rows[0].tag_id;
          }

          // Create relation
          await client.query(
            'INSERT INTO guidance_tag_relations (guidance_id, tag_id) VALUES ($1, $2)',
            [guidance.guidance_id, tagId]
          );
        }
      }

      // Add audit entry
      const auditQuery = `
        INSERT INTO guidance_audit (
          guidance_id, action, details, performed_by
        )
        VALUES ($1, $2, $3, $4)
      `;
      const auditParams = [
        guidance.guidance_id,
        'create',
        JSON.stringify({ title, categoryId, isPublished }),
        userId
      ];
      await client.query(auditQuery, auditParams);

      await client.query('COMMIT');
      return guidance;
    } catch (err) {
      await client.query('ROLLBACK');
      console.error('Error creating clinical guidance:', err);
      throw err;
    } finally {
      client.release();
    }
  }

  /**
   * Update a clinical guidance entry
   * @param {number} guidanceId - Guidance ID
   * @param {Object} guidanceData - Updated guidance data
   * @param {number} userId - User ID of the updater
   * @returns {Promise<Object>} Updated guidance entry
   */
  static async update(guidanceId, guidanceData, userId) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      const {
        title,
        content,
        summary,
        categoryId,
        contextKey,
        isPublished,
        tags = []
      } = guidanceData;

      // Get current version number
      const versionResult = await client.query(
        'SELECT MAX(version_number) as current_version FROM guidance_versions WHERE guidance_id = $1',
        [guidanceId]
      );
      const currentVersion = versionResult.rows[0].current_version || 0;
      const newVersionNumber = currentVersion + 1;

      // Update the guidance
      const updateFields = [];
      const updateParams = [];
      let paramIndex = 1;

      if (title !== undefined) {
        updateFields.push(`title = $${paramIndex}`);
        updateParams.push(title);
        paramIndex++;
      }

      if (content !== undefined) {
        updateFields.push(`content = $${paramIndex}`);
        updateParams.push(content);
        paramIndex++;
      }

      if (summary !== undefined) {
        updateFields.push(`summary = $${paramIndex}`);
        updateParams.push(summary);
        paramIndex++;
      }

      if (categoryId !== undefined) {
        updateFields.push(`category_id = $${paramIndex}`);
        updateParams.push(categoryId);
        paramIndex++;
      }

      if (contextKey !== undefined) {
        updateFields.push(`context_key = $${paramIndex}`);
        updateParams.push(contextKey);
        paramIndex++;
      }

      if (isPublished !== undefined) {
        updateFields.push(`is_published = $${paramIndex}`);
        updateParams.push(isPublished);
        paramIndex++;
      }

      if (updateFields.length === 0) {
        throw new Error('No fields to update');
      }

      updateParams.push(guidanceId);
      const guidanceQuery = `
        UPDATE clinical_guidance
        SET ${updateFields.join(', ')}
        WHERE guidance_id = $${paramIndex}
        RETURNING *
      `;
      const guidanceResult = await client.query(guidanceQuery, updateParams);
      
      if (guidanceResult.rows.length === 0) {
        throw new Error(`Guidance with ID ${guidanceId} not found`);
      }
      
      const guidance = guidanceResult.rows[0];

      // Insert a new version
      if (title !== undefined || content !== undefined || summary !== undefined || categoryId !== undefined) {
        const versionQuery = `
          INSERT INTO guidance_versions (
            guidance_id, version_number, title, content, summary, category_id, created_by
          )
          VALUES ($1, $2, $3, $4, $5, $6, $7)
        `;
        const versionParams = [
          guidanceId,
          newVersionNumber,
          guidance.title,
          guidance.content,
          guidance.summary,
          guidance.category_id,
          userId
        ];
        await client.query(versionQuery, versionParams);
      }

      // Update tags if provided
      if (tags.length > 0) {
        // Remove existing tag relations
        await client.query(
          'DELETE FROM guidance_tag_relations WHERE guidance_id = $1',
          [guidanceId]
        );

        // Add new tags
        for (const tagName of tags) {
          // Insert tag if it doesn't exist
          const tagQuery = `
            INSERT INTO guidance_tags (name)
            VALUES ($1)
            ON CONFLICT (name) DO NOTHING
            RETURNING tag_id
          `;
          const tagResult = await client.query(tagQuery, [tagName]);
          
          // Get tag ID (either from insert or existing)
          let tagId;
          if (tagResult.rows.length > 0) {
            tagId = tagResult.rows[0].tag_id;
          } else {
            const existingTagResult = await client.query(
              'SELECT tag_id FROM guidance_tags WHERE name = $1',
              [tagName]
            );
            tagId = existingTagResult.rows[0].tag_id;
          }

          // Create relation
          await client.query(
            'INSERT INTO guidance_tag_relations (guidance_id, tag_id) VALUES ($1, $2)',
            [guidanceId, tagId]
          );
        }
      }

      // Add audit entry
      const auditQuery = `
        INSERT INTO guidance_audit (
          guidance_id, action, details, performed_by
        )
        VALUES ($1, $2, $3, $4)
      `;
      const auditParams = [
        guidanceId,
        'update',
        JSON.stringify({ 
          title: title !== undefined ? title : undefined,
          content: content !== undefined ? 'Content updated' : undefined,
          categoryId: categoryId !== undefined ? categoryId : undefined,
          isPublished: isPublished !== undefined ? isPublished : undefined,
          newVersionNumber: newVersionNumber
        }),
        userId
      ];
      await client.query(auditQuery, auditParams);

      await client.query('COMMIT');
      return guidance;
    } catch (err) {
      await client.query('ROLLBACK');
      console.error(`Error updating clinical guidance with ID ${guidanceId}:`, err);
      throw err;
    } finally {
      client.release();
    }
  }

  /**
   * Delete a clinical guidance entry
   * @param {number} guidanceId - Guidance ID
   * @param {number} userId - User ID performing the deletion
   * @returns {Promise<boolean>} Success status
   */
  static async delete(guidanceId, userId) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      // Add audit entry before deletion
      const auditQuery = `
        INSERT INTO guidance_audit (
          guidance_id, action, details, performed_by
        )
        VALUES ($1, $2, $3, $4)
      `;
      const auditParams = [guidanceId, 'delete', JSON.stringify({}), userId];
      await client.query(auditQuery, auditParams);

      // Delete the guidance (cascades to versions, access, and tag relations)
      const deleteQuery = 'DELETE FROM clinical_guidance WHERE guidance_id = $1';
      const result = await client.query(deleteQuery, [guidanceId]);

      await client.query('COMMIT');
      return result.rowCount > 0;
    } catch (err) {
      await client.query('ROLLBACK');
      console.error(`Error deleting clinical guidance with ID ${guidanceId}:`, err);
      throw err;
    } finally {
      client.release();
    }
  }

  /**
   * Get version history for a guidance entry
   * @param {number} guidanceId - Guidance ID
   * @returns {Promise<Array>} Array of versions
   */
  static async getVersionHistory(guidanceId) {
    try {
      const query = `
        SELECT v.*, u.username AS created_by_username
        FROM guidance_versions v
        LEFT JOIN users u ON v.created_by = u.user_id
        WHERE v.guidance_id = $1
        ORDER BY v.version_number DESC
      `;
      const result = await pool.query(query, [guidanceId]);
      return result.rows;
    } catch (err) {
      console.error(`Error getting version history for guidance ID ${guidanceId}:`, err);
      throw err;
    }
  }

  /**
   * Get audit trail for a guidance entry
   * @param {number} guidanceId - Guidance ID
   * @returns {Promise<Array>} Array of audit entries
   */
  static async getAuditTrail(guidanceId) {
    try {
      const query = `
        SELECT a.*, u.username AS performed_by_username
        FROM guidance_audit a
        LEFT JOIN users u ON a.performed_by = u.user_id
        WHERE a.guidance_id = $1
        ORDER BY a.performed_at DESC
      `;
      const result = await pool.query(query, [guidanceId]);
      return result.rows;
    } catch (err) {
      console.error(`Error getting audit trail for guidance ID ${guidanceId}:`, err);
      throw err;
    }
  }

  /**
   * Update access control for a guidance entry
   * @param {number} guidanceId - Guidance ID
   * @param {Array<Object>} accessList - List of access control entries
   * @param {number} userId - User ID performing the update
   * @returns {Promise<boolean>} Success status
   */
  static async updateAccess(guidanceId, accessList, userId) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      // Delete existing access entries
      await client.query(
        'DELETE FROM guidance_access WHERE guidance_id = $1',
        [guidanceId]
      );

      // Insert new access entries
      for (const access of accessList) {
        const { role, canView = true, canEdit = false } = access;
        await client.query(
          `INSERT INTO guidance_access (guidance_id, role, can_view, can_edit)
           VALUES ($1, $2, $3, $4)`,
          [guidanceId, role, canView, canEdit]
        );
      }

      // Add audit entry
      const auditQuery = `
        INSERT INTO guidance_audit (
          guidance_id, action, details, performed_by
        )
        VALUES ($1, $2, $3, $4)
      `;
      const auditParams = [
        guidanceId,
        'update_access',
        JSON.stringify({ accessList }),
        userId
      ];
      await client.query(auditQuery, auditParams);

      await client.query('COMMIT');
      return true;
    } catch (err) {
      await client.query('ROLLBACK');
      console.error(`Error updating access for guidance ID ${guidanceId}:`, err);
      throw err;
    } finally {
      client.release();
    }
  }

  /**
   * Get access control for a guidance entry
   * @param {number} guidanceId - Guidance ID
   * @returns {Promise<Array>} Array of access control entries
   */
  static async getAccess(guidanceId) {
    try {
      const query = `
        SELECT * FROM guidance_access
        WHERE guidance_id = $1
        ORDER BY role
      `;
      const result = await pool.query(query, [guidanceId]);
      return result.rows;
    } catch (err) {
      console.error(`Error getting access for guidance ID ${guidanceId}:`, err);
      throw err;
    }
  }
}

module.exports = ClinicalGuidance;
