const pool = require('../db');

class MedicalRecord {
  // Create a new medical record
  static async create(recordData) {
    const {
      patient_id,
      record_date,
      diagnosis,
      treatment,
      notes,
      created_by,
      attending_doctor_id
    } = recordData;

    try {
      const result = await pool.query(
        `INSERT INTO medical_records
        (patient_id, record_date, diagnosis, treatment, notes, created_by, attending_doctor_id)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
        RETURNING *`,
        [
          patient_id,
          record_date || new Date(),
          diagnosis,
          treatment,
          notes,
          created_by,
          attending_doctor_id
        ]
      );

      return result.rows[0];
    } catch (err) {
      console.error('Error in MedicalRecord.create():', err);
      throw err;
    }
  }

  // Get all records for a patient
  static async getByPatientId(patientId) {
    try {
      const result = await pool.query(
        `SELECT mr.*,
          u.username as created_by_name,
          CONCAT(d.first_name, ' ', d.last_name) as attending_doctor_name
        FROM medical_records mr
        LEFT JOIN users u ON mr.created_by = u.user_id
        LEFT JOIN doctors d ON mr.attending_doctor_id = d.doctor_id
        WHERE mr.patient_id = $1
        ORDER BY mr.record_date DESC`,
        [patientId]
      );
      return result.rows;
    } catch (err) {
      console.error('Error in MedicalRecord.getByPatientId():', err);
      throw err;
    }
  }

  // Get record by ID
  static async getById(recordId) {
    try {
      const result = await pool.query(
        `SELECT mr.*,
          u.username as created_by_name,
          CONCAT(d.first_name, ' ', d.last_name) as attending_doctor_name
        FROM medical_records mr
        LEFT JOIN users u ON mr.created_by = u.user_id
        LEFT JOIN doctors d ON mr.attending_doctor_id = d.doctor_id
        WHERE mr.record_id = $1`,
        [recordId]
      );
      return result.rows[0];
    } catch (err) {
      console.error('Error in MedicalRecord.getById():', err);
      throw err;
    }
  }

  // Update record
  static async update(recordId, recordData) {
    const {
      record_date,
      diagnosis,
      treatment,
      notes,
      attending_doctor_id
    } = recordData;

    try {
      const result = await pool.query(
        `UPDATE medical_records
        SET record_date = $1, diagnosis = $2, treatment = $3, notes = $4, attending_doctor_id = $5
        WHERE record_id = $6
        RETURNING *`,
        [record_date, diagnosis, treatment, notes, attending_doctor_id, recordId]
      );

      if (result.rows.length === 0) {
        throw new Error(`Record with ID ${recordId} not found`);
      }

      return result.rows[0];
    } catch (err) {
      console.error('Error in MedicalRecord.update():', err);
      throw err;
    }
  }

  // Delete record
  static async delete(recordId) {
    try {
      await pool.query('DELETE FROM medical_records WHERE record_id = $1', [recordId]);
      return { success: true };
    } catch (err) {
      console.error('Error in MedicalRecord.delete():', err);
      throw err;
    }
  }

  // Get recent records
  static async getRecent(limit = 10) {
    try {
      const result = await pool.query(
        `SELECT mr.*,
          p.first_name, p.last_name, p.unique_id,
          u.username as created_by_name,
          CONCAT(d.first_name, ' ', d.last_name) as attending_doctor_name
        FROM medical_records mr
        JOIN patients p ON mr.patient_id = p.patient_id
        LEFT JOIN users u ON mr.created_by = u.user_id
        LEFT JOIN doctors d ON mr.attending_doctor_id = d.doctor_id
        ORDER BY mr.created_at DESC
        LIMIT $1`,
        [limit]
      );
      return result.rows;
    } catch (err) {
      console.error('Error in MedicalRecord.getRecent():', err);
      throw err;
    }
  }

  /**
   * Create an initial medical record for a new patient
   * @param {number} patientId - Patient ID
   * @param {number} createdBy - User ID who created the patient
   * @param {number|null} doctorId - Doctor ID assigned to the patient
   * @param {Object} patientData - Patient data for initial record
   * @returns {Promise<Object>} Created medical record
   */
  static async createInitialRecord(patientId, createdBy, doctorId = null, patientData = {}) {
    try {
      console.log('Creating initial medical record for patient:', patientId);

      // Extract relevant data from patient data for the initial record
      const {
        diagnosis = 'Initial assessment',
        treatment_plan = '',
        notes = 'Initial medical record created during patient registration',
        current_medications = ''
      } = patientData;

      // Create the medical record
      const recordData = {
        patient_id: patientId,
        record_date: new Date(),
        diagnosis,
        treatment: treatment_plan,
        notes,
        created_by: createdBy,
        attending_doctor_id: doctorId
      };

      const record = await this.create(recordData);
      console.log('Initial medical record created:', record.record_id);

      return record;
    } catch (err) {
      console.error('Error in MedicalRecord.createInitialRecord():', err);
      throw err;
    }
  }
}

module.exports = MedicalRecord;