const pool = require('../db');
const crypto = require('crypto');

class UserMfa {
  // Get MFA status for a user
  static async getForUser(userId) {
    try {
      // Check if the table exists first
      const tableCheck = await pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_name = 'user_mfa'
        );
      `);

      if (!tableCheck.rows[0].exists) {
        return {
          mfa_enabled: false,
          mfa_method: null
        };
      }

      const result = await pool.query(
        'SELECT mfa_enabled, mfa_method, last_verified FROM user_mfa WHERE user_id = $1',
        [userId]
      );

      if (result.rows.length === 0) {
        return {
          mfa_enabled: false,
          mfa_method: null
        };
      }

      return result.rows[0];
    } catch (err) {
      console.error(`Error fetching MFA status for user ${userId}:`, err.message);
      throw err;
    }
  }

  // Enable MFA for a user
  static async enable(userId, method) {
    try {
      // Generate a secret for app-based MFA
      const secret = method === 'app' ? this.generateSecret() : null;
      
      // Generate backup codes
      const backupCodes = this.generateBackupCodes();

      // Check if user already has an MFA record
      const existingRecord = await pool.query(
        'SELECT id FROM user_mfa WHERE user_id = $1',
        [userId]
      );

      if (existingRecord.rows.length > 0) {
        // Update existing record
        await pool.query(
          `UPDATE user_mfa 
           SET mfa_enabled = TRUE, 
               mfa_method = $2, 
               mfa_secret = $3, 
               backup_codes = $4,
               updated_at = CURRENT_TIMESTAMP
           WHERE user_id = $1`,
          [userId, method, secret, backupCodes]
        );
      } else {
        // Create new record
        await pool.query(
          `INSERT INTO user_mfa 
           (user_id, mfa_enabled, mfa_method, mfa_secret, backup_codes) 
           VALUES ($1, TRUE, $2, $3, $4)`,
          [userId, method, secret, backupCodes]
        );
      }

      // Update the user record to indicate MFA is required
      await pool.query(
        'UPDATE users SET mfa_required = TRUE WHERE user_id = $1',
        [userId]
      );

      return {
        mfa_enabled: true,
        mfa_method: method,
        secret,
        backup_codes: backupCodes
      };
    } catch (err) {
      console.error(`Error enabling MFA for user ${userId}:`, err.message);
      throw err;
    }
  }

  // Disable MFA for a user
  static async disable(userId) {
    try {
      await pool.query(
        `UPDATE user_mfa 
         SET mfa_enabled = FALSE, 
             mfa_method = NULL, 
             mfa_secret = NULL, 
             backup_codes = NULL,
             updated_at = CURRENT_TIMESTAMP
         WHERE user_id = $1`,
        [userId]
      );

      // Update the user record to indicate MFA is not required
      await pool.query(
        'UPDATE users SET mfa_required = FALSE WHERE user_id = $1',
        [userId]
      );

      return {
        mfa_enabled: false,
        mfa_method: null
      };
    } catch (err) {
      console.error(`Error disabling MFA for user ${userId}:`, err.message);
      throw err;
    }
  }

  // Verify MFA code
  static async verify(userId, code, method) {
    try {
      const result = await pool.query(
        'SELECT mfa_secret, backup_codes FROM user_mfa WHERE user_id = $1 AND mfa_enabled = TRUE',
        [userId]
      );

      if (result.rows.length === 0) {
        return false;
      }

      const { mfa_secret, backup_codes } = result.rows[0];

      let isValid = false;

      // Check if it's a backup code
      if (backup_codes && backup_codes.includes(code)) {
        // Remove the used backup code
        const updatedBackupCodes = backup_codes.filter(c => c !== code);
        
        await pool.query(
          `UPDATE user_mfa 
           SET backup_codes = $2, 
               last_verified = CURRENT_TIMESTAMP,
               updated_at = CURRENT_TIMESTAMP
           WHERE user_id = $1`,
          [userId, updatedBackupCodes]
        );
        
        isValid = true;
      } else if (method === 'app' && mfa_secret) {
        // Verify TOTP code
        isValid = this.verifyTotp(mfa_secret, code);
        
        if (isValid) {
          await pool.query(
            `UPDATE user_mfa 
             SET last_verified = CURRENT_TIMESTAMP,
                 updated_at = CURRENT_TIMESTAMP
             WHERE user_id = $1`,
            [userId]
          );
        }
      } else if (method === 'email') {
        // In a real implementation, you would verify against a stored email code
        // For this demo, we'll just return true
        isValid = true;
        
        await pool.query(
          `UPDATE user_mfa 
           SET last_verified = CURRENT_TIMESTAMP,
               updated_at = CURRENT_TIMESTAMP
           WHERE user_id = $1`,
          [userId]
        );
      }

      return isValid;
    } catch (err) {
      console.error(`Error verifying MFA for user ${userId}:`, err.message);
      throw err;
    }
  }

  // Get all users with MFA status
  static async getAllUsersWithMfaStatus() {
    try {
      const result = await pool.query(`
        SELECT 
          u.user_id, 
          u.username, 
          u.email, 
          u.role,
          COALESCE(m.mfa_enabled, FALSE) as mfa_enabled,
          m.mfa_method,
          u.last_login
        FROM 
          users u
        LEFT JOIN 
          user_mfa m ON u.user_id = m.user_id
        ORDER BY 
          u.username
      `);

      return result.rows;
    } catch (err) {
      console.error('Error fetching users with MFA status:', err.message);
      throw err;
    }
  }

  // Helper methods
  static generateSecret() {
    return crypto.randomBytes(20).toString('hex');
  }

  static generateBackupCodes(count = 8) {
    const codes = [];
    for (let i = 0; i < count; i++) {
      codes.push(crypto.randomBytes(4).toString('hex'));
    }
    return codes;
  }

  static verifyTotp(secret, code) {
    // In a real implementation, you would use a TOTP library
    // For this demo, we'll just return true
    return true;
  }
}

module.exports = UserMfa;
