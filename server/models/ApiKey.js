const pool = require('../db');
const crypto = require('crypto');
const logger = require('../utils/logger');

class ApiKey {
  // Generate a new API key
  static generateKey() {
    return crypto.randomBytes(32).toString('hex');
  }

  // Create a new API key
  static async create(keyData, userId) {
    try {
      const { name, description, permissions, rate_limit, expires_at } = keyData;
      
      // Generate a new key value
      const key_value = this.generateKey();
      
      const result = await pool.query(
        `INSERT INTO api_keys (
          name, 
          key_value, 
          description, 
          permissions, 
          rate_limit, 
          expires_at, 
          created_by
        ) VALUES ($1, $2, $3, $4, $5, $6, $7) 
        RETURNING *`,
        [
          name,
          key_value,
          description,
          permissions || [],
          rate_limit || 100,
          expires_at,
          userId
        ]
      );

      logger.info(`API key created: ${name} by user ${userId}`);
      return result.rows[0];
    } catch (err) {
      logger.error('Error in ApiKey.create():', err.message);
      throw err;
    }
  }

  // Get all API keys
  static async getAll() {
    try {
      const result = await pool.query(
        `SELECT 
          k.key_id, 
          k.name, 
          k.description, 
          k.permissions, 
          k.rate_limit, 
          k.is_active, 
          k.expires_at, 
          k.created_at, 
          k.updated_at,
          u.username as created_by_username
        FROM api_keys k
        LEFT JOIN users u ON k.created_by = u.user_id
        ORDER BY k.created_at DESC`
      );
      
      return result.rows;
    } catch (err) {
      logger.error('Error in ApiKey.getAll():', err.message);
      throw err;
    }
  }

  // Get API key by ID
  static async getById(keyId) {
    try {
      const result = await pool.query(
        `SELECT 
          k.key_id, 
          k.name, 
          k.description, 
          k.permissions, 
          k.rate_limit, 
          k.is_active, 
          k.expires_at, 
          k.created_at, 
          k.updated_at,
          u.username as created_by_username
        FROM api_keys k
        LEFT JOIN users u ON k.created_by = u.user_id
        WHERE k.key_id = $1`,
        [keyId]
      );
      
      return result.rows[0];
    } catch (err) {
      logger.error(`Error in ApiKey.getById(${keyId}):`, err.message);
      throw err;
    }
  }

  // Update an API key
  static async update(keyId, keyData, userId) {
    try {
      const { name, description, permissions, rate_limit, is_active, expires_at } = keyData;
      
      const result = await pool.query(
        `UPDATE api_keys 
        SET 
          name = $1, 
          description = $2, 
          permissions = $3, 
          rate_limit = $4, 
          is_active = $5, 
          expires_at = $6, 
          updated_at = CURRENT_TIMESTAMP
        WHERE key_id = $7 
        RETURNING *`,
        [
          name,
          description,
          permissions || [],
          rate_limit || 100,
          is_active !== undefined ? is_active : true,
          expires_at,
          keyId
        ]
      );

      logger.info(`API key updated: ${keyId} by user ${userId}`);
      return result.rows[0];
    } catch (err) {
      logger.error(`Error in ApiKey.update(${keyId}):`, err.message);
      throw err;
    }
  }

  // Delete an API key
  static async delete(keyId, userId) {
    try {
      const result = await pool.query(
        'DELETE FROM api_keys WHERE key_id = $1 RETURNING *',
        [keyId]
      );
      
      logger.info(`API key deleted: ${keyId} by user ${userId}`);
      return result.rows[0];
    } catch (err) {
      logger.error(`Error in ApiKey.delete(${keyId}):`, err.message);
      throw err;
    }
  }

  // Log API usage
  static async logUsage(keyId, endpoint, method, statusCode, responseTime, ipAddress, userAgent) {
    try {
      await pool.query(
        `INSERT INTO api_usage_logs (
          key_id, 
          endpoint, 
          method, 
          status_code, 
          response_time, 
          ip_address, 
          user_agent
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)`,
        [
          keyId,
          endpoint,
          method,
          statusCode,
          responseTime,
          ipAddress,
          userAgent
        ]
      );
    } catch (err) {
      logger.error('Error in ApiKey.logUsage():', err.message);
      // Don't throw the error to prevent API calls from failing due to logging issues
    }
  }

  // Get usage statistics for an API key
  static async getUsageStats(keyId, startDate, endDate) {
    try {
      const result = await pool.query(
        `SELECT 
          date_trunc('hour', created_at) as time_period,
          COUNT(*) as request_count,
          AVG(response_time) as avg_response_time,
          COUNT(CASE WHEN status_code >= 400 THEN 1 END) as error_count
        FROM api_usage_logs
        WHERE key_id = $1
          AND created_at BETWEEN $2 AND $3
        GROUP BY time_period
        ORDER BY time_period ASC`,
        [keyId, startDate, endDate]
      );
      
      return result.rows;
    } catch (err) {
      logger.error(`Error in ApiKey.getUsageStats(${keyId}):`, err.message);
      throw err;
    }
  }
}

module.exports = ApiKey;
