const pool = require('../db');
const logger = require('../utils/logger');

class IntegrationLog {
  // Create a new log entry
  static async create(logData) {
    try {
      const { integration_id, external_system_id, event_type, status, message, details } = logData;
      
      const result = await pool.query(
        `INSERT INTO integration_logs (
          integration_id, 
          external_system_id, 
          event_type, 
          status, 
          message, 
          details
        ) VALUES ($1, $2, $3, $4, $5, $6) 
        RETURNING *`,
        [
          integration_id,
          external_system_id,
          event_type,
          status,
          message,
          details || {}
        ]
      );

      return result.rows[0];
    } catch (err) {
      logger.error('Error in IntegrationLog.create():', err.message);
      throw err;
    }
  }

  // Get all logs
  static async getAll(limit = 100, offset = 0) {
    try {
      const result = await pool.query(
        `SELECT 
          l.log_id, 
          l.integration_id, 
          l.external_system_id, 
          l.event_type, 
          l.status, 
          l.message, 
          l.details, 
          l.created_at,
          i.name as integration_name,
          e.name as external_system_name
        FROM integration_logs l
        LEFT JOIN integrations i ON l.integration_id = i.integration_id
        LEFT JOIN external_systems e ON l.external_system_id = e.system_id
        ORDER BY l.created_at DESC
        LIMIT $1 OFFSET $2`,
        [limit, offset]
      );
      
      return result.rows;
    } catch (err) {
      logger.error('Error in IntegrationLog.getAll():', err.message);
      throw err;
    }
  }

  // Get logs by integration ID
  static async getByIntegrationId(integrationId, limit = 100, offset = 0) {
    try {
      const result = await pool.query(
        `SELECT 
          l.log_id, 
          l.integration_id, 
          l.external_system_id, 
          l.event_type, 
          l.status, 
          l.message, 
          l.details, 
          l.created_at,
          i.name as integration_name,
          e.name as external_system_name
        FROM integration_logs l
        LEFT JOIN integrations i ON l.integration_id = i.integration_id
        LEFT JOIN external_systems e ON l.external_system_id = e.system_id
        WHERE l.integration_id = $1
        ORDER BY l.created_at DESC
        LIMIT $2 OFFSET $3`,
        [integrationId, limit, offset]
      );
      
      return result.rows;
    } catch (err) {
      logger.error(`Error in IntegrationLog.getByIntegrationId(${integrationId}):`, err.message);
      throw err;
    }
  }

  // Get logs by external system ID
  static async getByExternalSystemId(systemId, limit = 100, offset = 0) {
    try {
      const result = await pool.query(
        `SELECT 
          l.log_id, 
          l.integration_id, 
          l.external_system_id, 
          l.event_type, 
          l.status, 
          l.message, 
          l.details, 
          l.created_at,
          i.name as integration_name,
          e.name as external_system_name
        FROM integration_logs l
        LEFT JOIN integrations i ON l.integration_id = i.integration_id
        LEFT JOIN external_systems e ON l.external_system_id = e.system_id
        WHERE l.external_system_id = $1
        ORDER BY l.created_at DESC
        LIMIT $2 OFFSET $3`,
        [systemId, limit, offset]
      );
      
      return result.rows;
    } catch (err) {
      logger.error(`Error in IntegrationLog.getByExternalSystemId(${systemId}):`, err.message);
      throw err;
    }
  }

  // Get logs by status
  static async getByStatus(status, limit = 100, offset = 0) {
    try {
      const result = await pool.query(
        `SELECT 
          l.log_id, 
          l.integration_id, 
          l.external_system_id, 
          l.event_type, 
          l.status, 
          l.message, 
          l.details, 
          l.created_at,
          i.name as integration_name,
          e.name as external_system_name
        FROM integration_logs l
        LEFT JOIN integrations i ON l.integration_id = i.integration_id
        LEFT JOIN external_systems e ON l.external_system_id = e.system_id
        WHERE l.status = $1
        ORDER BY l.created_at DESC
        LIMIT $2 OFFSET $3`,
        [status, limit, offset]
      );
      
      return result.rows;
    } catch (err) {
      logger.error(`Error in IntegrationLog.getByStatus(${status}):`, err.message);
      throw err;
    }
  }

  // Get logs by event type
  static async getByEventType(eventType, limit = 100, offset = 0) {
    try {
      const result = await pool.query(
        `SELECT 
          l.log_id, 
          l.integration_id, 
          l.external_system_id, 
          l.event_type, 
          l.status, 
          l.message, 
          l.details, 
          l.created_at,
          i.name as integration_name,
          e.name as external_system_name
        FROM integration_logs l
        LEFT JOIN integrations i ON l.integration_id = i.integration_id
        LEFT JOIN external_systems e ON l.external_system_id = e.system_id
        WHERE l.event_type = $1
        ORDER BY l.created_at DESC
        LIMIT $2 OFFSET $3`,
        [eventType, limit, offset]
      );
      
      return result.rows;
    } catch (err) {
      logger.error(`Error in IntegrationLog.getByEventType(${eventType}):`, err.message);
      throw err;
    }
  }

  // Get log statistics
  static async getStatistics(startDate, endDate) {
    try {
      const result = await pool.query(
        `SELECT 
          COUNT(*) as total_logs,
          COUNT(CASE WHEN status = 'success' THEN 1 END) as success_count,
          COUNT(CASE WHEN status = 'failure' THEN 1 END) as failure_count,
          COUNT(CASE WHEN status = 'warning' THEN 1 END) as warning_count,
          COUNT(DISTINCT integration_id) as integration_count,
          COUNT(DISTINCT external_system_id) as system_count
        FROM integration_logs
        WHERE created_at BETWEEN $1 AND $2`,
        [startDate, endDate]
      );
      
      return result.rows[0];
    } catch (err) {
      logger.error('Error in IntegrationLog.getStatistics():', err.message);
      throw err;
    }
  }
}

module.exports = IntegrationLog;
