const pool = require('../db');
const User = require('./User');
const SystemLog = require('./SystemLog');

class UserBatch {
  /**
   * Lock multiple user accounts
   * @param {number[]} userIds - Array of user IDs to lock
   * @param {number} adminId - ID of the admin performing the action
   * @returns {Promise<{success: Array, failed: Array}>} - Arrays of successful and failed operations
   */
  static async lockAccounts(userIds, adminId) {
    const results = {
      success: [],
      failed: []
    };

    // Get user details before locking
    const usersQuery = await pool.query(
      'SELECT user_id, username, email, role FROM users WHERE user_id = ANY($1)',
      [userIds]
    );
    const users = usersQuery.rows;

    for (const user of users) {
      try {
        // Skip if trying to lock admin's own account
        if (user.user_id === adminId) {
          results.failed.push({
            ...user,
            reason: 'Cannot lock your own account'
          });
          continue;
        }

        // Lock the account
        await pool.query(
          'UPDATE users SET is_locked = true, lockout_until = NULL WHERE user_id = $1',
          [user.user_id]
        );

        // Log the action
        await SystemLog.create({
          log_type: 'user_lock',
          user_id: adminId,
          related_id: user.user_id,
          related_name: user.username,
          details: {
            action: 'lock',
            email: user.email,
            role: user.role
          }
        });

        results.success.push(user);
      } catch (err) {
        console.error(`Error locking account for user ID: ${user.user_id}`, err);
        results.failed.push({
          ...user,
          reason: err.message
        });
      }
    }

    return results;
  }

  /**
   * Unlock multiple user accounts
   * @param {number[]} userIds - Array of user IDs to unlock
   * @param {number} adminId - ID of the admin performing the action
   * @returns {Promise<{success: Array, failed: Array}>} - Arrays of successful and failed operations
   */
  static async unlockAccounts(userIds, adminId) {
    const results = {
      success: [],
      failed: []
    };

    // Get user details before unlocking
    const usersQuery = await pool.query(
      'SELECT user_id, username, email, role FROM users WHERE user_id = ANY($1)',
      [userIds]
    );
    const users = usersQuery.rows;

    for (const user of users) {
      try {
        // Unlock the account
        await pool.query(
          'UPDATE users SET is_locked = false, failed_login_attempts = 0, lockout_until = NULL WHERE user_id = $1',
          [user.user_id]
        );

        // Log the action
        await SystemLog.create({
          log_type: 'user_unlock',
          user_id: adminId,
          related_id: user.user_id,
          related_name: user.username,
          details: {
            action: 'unlock',
            email: user.email,
            role: user.role
          }
        });

        results.success.push(user);
      } catch (err) {
        console.error(`Error unlocking account for user ID: ${user.user_id}`, err);
        results.failed.push({
          ...user,
          reason: err.message
        });
      }
    }

    return results;
  }

  /**
   * Delete multiple users
   * @param {number[]} userIds - Array of user IDs to delete
   * @param {number} adminId - ID of the admin performing the action
   * @returns {Promise<{success: Array, failed: Array}>} - Arrays of successful and failed operations
   */
  static async deleteUsers(userIds, adminId) {
    const results = {
      success: [],
      failed: []
    };

    // Get user details before deletion
    const usersQuery = await pool.query(
      'SELECT user_id, username, email, role FROM users WHERE user_id = ANY($1)',
      [userIds]
    );
    const users = usersQuery.rows;

    for (const user of users) {
      try {
        // Skip if trying to delete admin's own account
        if (user.user_id === adminId) {
          results.failed.push({
            ...user,
            reason: 'Cannot delete your own account'
          });
          continue;
        }

        // Delete the user
        await pool.query('DELETE FROM users WHERE user_id = $1', [user.user_id]);

        // Log the action
        await SystemLog.create({
          log_type: 'user_deletion',
          user_id: adminId,
          related_id: user.user_id,
          related_name: user.username,
          details: {
            action: 'delete',
            email: user.email,
            role: user.role
          }
        });

        results.success.push(user);
      } catch (err) {
        console.error(`Error deleting user ID: ${user.user_id}`, err);
        results.failed.push({
          ...user,
          reason: err.message
        });
      }
    }

    return results;
  }

  /**
   * Change role for multiple users
   * @param {number[]} userIds - Array of user IDs to change role
   * @param {string} newRole - New role to assign
   * @param {number} adminId - ID of the admin performing the action
   * @returns {Promise<{success: Array, failed: Array}>} - Arrays of successful and failed operations
   */
  static async changeRole(userIds, newRole, adminId) {
    const results = {
      success: [],
      failed: []
    };

    // Validate role
    const validRoles = ['admin', 'doctor', 'staff', 'patient', 'kin', 'researcher'];
    if (!validRoles.includes(newRole)) {
      throw new Error(`Invalid role: ${newRole}. Must be one of: ${validRoles.join(', ')}`);
    }

    // Get user details before role change
    const usersQuery = await pool.query(
      'SELECT user_id, username, email, role FROM users WHERE user_id = ANY($1)',
      [userIds]
    );
    const users = usersQuery.rows;

    for (const user of users) {
      try {
        // Skip if trying to change admin's own role
        if (user.user_id === adminId) {
          results.failed.push({
            ...user,
            reason: 'Cannot change your own role'
          });
          continue;
        }

        // Skip if role is already the same
        if (user.role === newRole) {
          results.failed.push({
            ...user,
            reason: `User already has role '${newRole}'`
          });
          continue;
        }

        // Change the role
        await pool.query(
          'UPDATE users SET role = $1 WHERE user_id = $2',
          [newRole, user.user_id]
        );

        // Log the action
        await SystemLog.create({
          log_type: 'user_role_change',
          user_id: adminId,
          related_id: user.user_id,
          related_name: user.username,
          details: {
            action: 'change_role',
            email: user.email,
            old_role: user.role,
            new_role: newRole
          }
        });

        results.success.push({
          ...user,
          role: newRole
        });
      } catch (err) {
        console.error(`Error changing role for user ID: ${user.user_id}`, err);
        results.failed.push({
          ...user,
          reason: err.message
        });
      }
    }

    return results;
  }

  /**
   * Import multiple users
   * @param {Array} users - Array of user objects to import
   * @param {number} adminId - ID of the admin performing the action
   * @returns {Promise<{success: Array, failed: Array, total: number}>} - Import results
   */
  static async importUsers(users, adminId) {
    const results = {
      success: [],
      failed: [],
      total: users.length
    };

    for (const userData of users) {
      try {
        // Validate required fields
        if (!userData.username || !userData.email || !userData.role) {
          throw new Error('Missing required fields: username, email, and role are required');
        }

        // Check if user already exists
        const existingUser = await User.findByEmail(userData.email) || await User.findByUsername(userData.username);
        if (existingUser) {
          throw new Error(`User already exists with username '${userData.username}' or email '${userData.email}'`);
        }

        // Create the user
        const password = userData.password || null;
        const useDefaultPassword = !password;
        
        const result = await User.register(
          userData.username,
          userData.email,
          password,
          userData.role,
          userData.doctor_id || null,
          userData.patient_id || null,
          useDefaultPassword,
          userData.relationship_type || null
        );

        // Log the action
        await SystemLog.create({
          log_type: 'user_import',
          user_id: adminId,
          related_id: result.user.user_id,
          related_name: result.user.username,
          details: {
            action: 'import',
            email: result.user.email,
            role: result.user.role
          }
        });

        results.success.push({
          username: userData.username,
          email: userData.email,
          role: userData.role
        });
      } catch (err) {
        console.error(`Error importing user: ${userData.username}`, err);
        results.failed.push({
          user: userData,
          reason: err.message
        });
      }
    }

    return results;
  }
}

module.exports = UserBatch;
