const pool = require('../db');

class SystemSettings {
  // Get the current system settings
  static async getCurrent() {
    try {
      const result = await pool.query('SELECT * FROM system_settings ORDER BY setting_id DESC LIMIT 1');

      if (result.rows.length === 0) {
        // If no settings exist, create default settings
        return this.createDefault();
      }

      return result.rows[0];
    } catch (err) {
      console.error('Error in SystemSettings.getCurrent():', err.message);
      throw err;
    }
  }

  // Create default system settings
  static async createDefault() {
    try {
      const result = await pool.query(`
        INSERT INTO system_settings (
          system_name, 
          timezone, 
          date_format, 
          maintenance_mode,
          session_timeout,
          two_factor_auth,
          ip_restriction,
          smtp_server,
          smtp_port,
          smtp_username,
          email_from,
          email_enabled,
          email_notifications,
          sms_notifications,
          auto_backup,
          backup_frequency,
          backup_retention,
          language,
          currency
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19)
        RETURNING *
      `, [
        'Medical Records System', 
        'UTC', 
        'MM/DD/YYYY', 
        false,
        30,
        false,
        false,
        'smtp.medapp.com',
        587,
        'smtp_user',
        '<EMAIL>',
        true,
        true,
        false,
        true,
        'daily',
        30,
        'en',
        'USD'
      ]);

      return result.rows[0];
    } catch (err) {
      console.error('Error in SystemSettings.createDefault():', err.message);
      throw err;
    }
  }

  // Update system settings
  static async update(settingsData, userId) {
    try {
      // Extract fields from settingsData
      const {
        system_name,
        timezone,
        date_format,
        maintenance_mode,
        session_timeout,
        two_factor_auth,
        ip_restriction,
        smtp_server,
        smtp_port,
        smtp_username,
        smtp_password,
        email_from,
        email_enabled,
        email_notifications,
        sms_notifications,
        auto_backup,
        backup_frequency,
        backup_retention,
        language,
        currency
      } = settingsData;

      // Build the query dynamically based on provided fields
      let updateFields = [];
      let queryParams = [];
      let paramIndex = 1;

      // Helper function to add a field to the update query if it's defined
      const addFieldIfDefined = (fieldName, value) => {
        if (value !== undefined) {
          updateFields.push(`${fieldName} = $${paramIndex}`);
          queryParams.push(value);
          paramIndex++;
        }
      };

      // Add all fields that might be updated
      addFieldIfDefined('system_name', system_name);
      addFieldIfDefined('timezone', timezone);
      addFieldIfDefined('date_format', date_format);
      addFieldIfDefined('maintenance_mode', maintenance_mode);
      addFieldIfDefined('session_timeout', session_timeout);
      addFieldIfDefined('two_factor_auth', two_factor_auth);
      addFieldIfDefined('ip_restriction', ip_restriction);
      addFieldIfDefined('smtp_server', smtp_server);
      addFieldIfDefined('smtp_port', smtp_port);
      addFieldIfDefined('smtp_username', smtp_username);
      addFieldIfDefined('smtp_password', smtp_password);
      addFieldIfDefined('email_from', email_from);
      addFieldIfDefined('email_enabled', email_enabled);
      addFieldIfDefined('email_notifications', email_notifications);
      addFieldIfDefined('sms_notifications', sms_notifications);
      addFieldIfDefined('auto_backup', auto_backup);
      addFieldIfDefined('backup_frequency', backup_frequency);
      addFieldIfDefined('backup_retention', backup_retention);
      addFieldIfDefined('language', language);
      addFieldIfDefined('currency', currency);

      // Always update these fields
      updateFields.push(`updated_at = NOW()`);
      updateFields.push(`updated_by = $${paramIndex}`);
      queryParams.push(userId);

      // If no fields to update, return current settings
      if (updateFields.length <= 2) {
        return this.getCurrent();
      }

      const updateQuery = `
        UPDATE system_settings
        SET ${updateFields.join(', ')}
        WHERE setting_id = (SELECT MAX(setting_id) FROM system_settings)
        RETURNING *
      `;

      const result = await pool.query(updateQuery, queryParams);

      if (result.rows.length === 0) {
        return this.createDefault();
      }

      return result.rows[0];
    } catch (err) {
      console.error('Error in SystemSettings.update():', err.message);
      throw err;
    }
  }

  // Test email configuration
  static async testEmailConfig(config) {
    try {
      // In a real application, this would send a test email
      // For now, we'll just return success
      return {
        success: true,
        message: 'Test email sent successfully'
      };
    } catch (err) {
      console.error('Error in SystemSettings.testEmailConfig():', err.message);
      return {
        success: false,
        message: err.message
      };
    }
  }
}

module.exports = SystemSettings;
