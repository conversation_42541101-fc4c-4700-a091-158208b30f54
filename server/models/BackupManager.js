const pool = require('../db');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');
const util = require('util');
const execPromise = util.promisify(exec);

class BackupManager {
  // Get all backup history entries
  static async getBackupHistory(limit = 100, offset = 0) {
    try {
      const result = await pool.query(`
        SELECT 
          bh.backup_id, 
          bh.backup_name, 
          bh.backup_path, 
          bh.backup_size, 
          bh.backup_type, 
          bh.status, 
          bh.is_encrypted, 
          bh.retention_date, 
          bh.notes, 
          bh.created_at, 
          u.username as created_by_username
        FROM backup_history bh
        LEFT JOIN users u ON bh.created_by = u.user_id
        ORDER BY bh.created_at DESC
        LIMIT $1 OFFSET $2
      `, [limit, offset]);

      return result.rows;
    } catch (err) {
      console.error('Error in BackupManager.getBackupHistory():', err.message);
      throw err;
    }
  }

  // Get a specific backup by ID
  static async getBackupById(backupId) {
    try {
      const result = await pool.query(`
        SELECT 
          bh.backup_id, 
          bh.backup_name, 
          bh.backup_path, 
          bh.backup_size, 
          bh.backup_type, 
          bh.status, 
          bh.is_encrypted, 
          bh.retention_date, 
          bh.notes, 
          bh.created_at, 
          u.username as created_by_username
        FROM backup_history bh
        LEFT JOIN users u ON bh.created_by = u.user_id
        WHERE bh.backup_id = $1
      `, [backupId]);

      if (result.rows.length === 0) {
        throw new Error(`Backup with ID ${backupId} not found`);
      }

      return result.rows[0];
    } catch (err) {
      console.error(`Error in BackupManager.getBackupById(${backupId}):`, err.message);
      throw err;
    }
  }

  // Create a new backup
  static async createBackup(userId, options = {}) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      // Get database connection info from environment variables or config
      const dbUser = process.env.DB_USER || 'postgres';
      const dbPassword = process.env.DB_PASSWORD || 'postgres';
      const dbHost = process.env.DB_HOST || 'localhost';
      const dbPort = process.env.DB_PORT || 5432;
      const dbName = process.env.DB_NAME || 'medapp';

      // Create backup directory if it doesn't exist
      const backupDir = path.join(__dirname, '..', 'backups');
      if (!fs.existsSync(backupDir)) {
        fs.mkdirSync(backupDir, { recursive: true });
      }

      // Generate backup filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const backupType = options.backupType || 'manual';
      const backupName = options.backupName || `backup_${dbName}_${timestamp}`;
      const backupPath = path.join(backupDir, `${backupName}.sql`);

      // Create a record in the backup_history table with 'in_progress' status
      const insertResult = await client.query(`
        INSERT INTO backup_history (
          backup_name, 
          backup_path, 
          backup_size, 
          backup_type, 
          status, 
          is_encrypted, 
          retention_date, 
          notes, 
          created_by
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING backup_id
      `, [
        backupName,
        backupPath,
        0, // Initial size is 0
        backupType,
        'in_progress',
        options.isEncrypted || false,
        options.retentionDate || null,
        options.notes || null,
        userId
      ]);

      const backupId = insertResult.rows[0].backup_id;

      // Execute pg_dump command
      const pgDumpCmd = `PGPASSWORD=${dbPassword} pg_dump -h ${dbHost} -p ${dbPort} -U ${dbUser} -d ${dbName} -f "${backupPath}"`;
      
      await client.query('COMMIT');

      try {
        // Execute the backup command
        await execPromise(pgDumpCmd);

        // Get the file size
        const stats = fs.statSync(backupPath);
        const fileSizeInBytes = stats.size;

        // Update the backup record with completed status and file size
        await pool.query(`
          UPDATE backup_history
          SET status = 'completed', backup_size = $1
          WHERE backup_id = $2
        `, [fileSizeInBytes, backupId]);

        // Get the updated backup record
        const result = await pool.query(`
          SELECT * FROM backup_history WHERE backup_id = $1
        `, [backupId]);

        return result.rows[0];
      } catch (execError) {
        // Update the backup record with failed status
        await pool.query(`
          UPDATE backup_history
          SET status = 'failed', notes = COALESCE(notes, '') || E'\\n' || $1
          WHERE backup_id = $2
        `, [execError.message, backupId]);

        throw execError;
      }
    } catch (err) {
      await client.query('ROLLBACK');
      console.error('Error in BackupManager.createBackup():', err.message);
      throw err;
    } finally {
      client.release();
    }
  }

  // Restore from a backup
  static async restoreFromBackup(backupId, userId) {
    try {
      // Get the backup record
      const backup = await this.getBackupById(backupId);

      // Check if the backup file exists
      if (!fs.existsSync(backup.backup_path)) {
        throw new Error(`Backup file not found: ${backup.backup_path}`);
      }

      // Get database connection info from environment variables or config
      const dbUser = process.env.DB_USER || 'postgres';
      const dbPassword = process.env.DB_PASSWORD || 'postgres';
      const dbHost = process.env.DB_HOST || 'localhost';
      const dbPort = process.env.DB_PORT || 5432;
      const dbName = process.env.DB_NAME || 'medapp';

      // Execute psql command to restore the database
      const psqlCmd = `PGPASSWORD=${dbPassword} psql -h ${dbHost} -p ${dbPort} -U ${dbUser} -d ${dbName} -f "${backup.backup_path}"`;
      
      await execPromise(psqlCmd);

      // Log the restore operation
      await pool.query(`
        INSERT INTO backup_history (
          backup_name, 
          backup_path, 
          backup_size, 
          backup_type, 
          status, 
          is_encrypted, 
          notes, 
          created_by
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      `, [
        `restore_${backup.backup_name}`,
        backup.backup_path,
        backup.backup_size,
        'restore',
        'completed',
        backup.is_encrypted,
        `Restored from backup ID ${backupId}`,
        userId
      ]);

      return { success: true, message: 'Database restored successfully' };
    } catch (err) {
      console.error(`Error in BackupManager.restoreFromBackup(${backupId}):`, err.message);
      throw err;
    }
  }

  // Delete a backup
  static async deleteBackup(backupId) {
    try {
      // Get the backup record
      const backup = await this.getBackupById(backupId);

      // Delete the backup file if it exists
      if (fs.existsSync(backup.backup_path)) {
        fs.unlinkSync(backup.backup_path);
      }

      // Delete the backup record
      await pool.query('DELETE FROM backup_history WHERE backup_id = $1', [backupId]);

      return { success: true, message: 'Backup deleted successfully' };
    } catch (err) {
      console.error(`Error in BackupManager.deleteBackup(${backupId}):`, err.message);
      throw err;
    }
  }

  // Clean up old backups based on retention policy
  static async cleanupOldBackups() {
    try {
      // Get backups that have passed their retention date
      const result = await pool.query(`
        SELECT backup_id, backup_path
        FROM backup_history
        WHERE retention_date IS NOT NULL AND retention_date < NOW()
      `);

      const deletedBackups = [];
      for (const backup of result.rows) {
        try {
          // Delete the backup file if it exists
          if (fs.existsSync(backup.backup_path)) {
            fs.unlinkSync(backup.backup_path);
          }

          // Delete the backup record
          await pool.query('DELETE FROM backup_history WHERE backup_id = $1', [backup.backup_id]);
          deletedBackups.push(backup.backup_id);
        } catch (deleteErr) {
          console.error(`Error deleting backup ${backup.backup_id}:`, deleteErr.message);
        }
      }

      return {
        success: true,
        message: `Cleaned up ${deletedBackups.length} old backups`,
        deletedBackups
      };
    } catch (err) {
      console.error('Error in BackupManager.cleanupOldBackups():', err.message);
      throw err;
    }
  }
}

module.exports = BackupManager;
