const pool = require('../db');
const logger = require('../utils/logger');

class Integration {
  // Create a new integration
  static async create(integrationData, userId) {
    try {
      const { name, type, description, config, status } = integrationData;
      
      const result = await pool.query(
        `INSERT INTO integrations (
          name, 
          type, 
          description, 
          config, 
          status, 
          created_by
        ) VALUES ($1, $2, $3, $4, $5, $6) 
        RETURNING *`,
        [
          name,
          type,
          description,
          config || {},
          status || 'inactive',
          userId
        ]
      );

      logger.info(`Integration created: ${name} by user ${userId}`);
      return result.rows[0];
    } catch (err) {
      logger.error('Error in Integration.create():', err.message);
      throw err;
    }
  }

  // Get all integrations
  static async getAll() {
    try {
      const result = await pool.query(
        `SELECT 
          i.integration_id, 
          i.name, 
          i.type, 
          i.description, 
          i.config, 
          i.status, 
          i.last_sync, 
          i.health_status, 
          i.created_at, 
          i.updated_at,
          u.username as created_by_username
        FROM integrations i
        LEFT JOIN users u ON i.created_by = u.user_id
        ORDER BY i.created_at DESC`
      );
      
      return result.rows;
    } catch (err) {
      logger.error('Error in Integration.getAll():', err.message);
      throw err;
    }
  }

  // Get integration by ID
  static async getById(integrationId) {
    try {
      const result = await pool.query(
        `SELECT 
          i.integration_id, 
          i.name, 
          i.type, 
          i.description, 
          i.config, 
          i.status, 
          i.last_sync, 
          i.health_status, 
          i.created_at, 
          i.updated_at,
          u.username as created_by_username
        FROM integrations i
        LEFT JOIN users u ON i.created_by = u.user_id
        WHERE i.integration_id = $1`,
        [integrationId]
      );
      
      return result.rows[0];
    } catch (err) {
      logger.error(`Error in Integration.getById(${integrationId}):`, err.message);
      throw err;
    }
  }

  // Update an integration
  static async update(integrationId, integrationData, userId) {
    try {
      const { name, type, description, config, status, health_status } = integrationData;
      
      const result = await pool.query(
        `UPDATE integrations 
        SET 
          name = $1, 
          type = $2, 
          description = $3, 
          config = $4, 
          status = $5, 
          health_status = $6, 
          updated_at = CURRENT_TIMESTAMP
        WHERE integration_id = $7 
        RETURNING *`,
        [
          name,
          type,
          description,
          config || {},
          status || 'inactive',
          health_status || 'unknown',
          integrationId
        ]
      );

      logger.info(`Integration updated: ${integrationId} by user ${userId}`);
      return result.rows[0];
    } catch (err) {
      logger.error(`Error in Integration.update(${integrationId}):`, err.message);
      throw err;
    }
  }

  // Delete an integration
  static async delete(integrationId, userId) {
    try {
      const result = await pool.query(
        'DELETE FROM integrations WHERE integration_id = $1 RETURNING *',
        [integrationId]
      );
      
      logger.info(`Integration deleted: ${integrationId} by user ${userId}`);
      return result.rows[0];
    } catch (err) {
      logger.error(`Error in Integration.delete(${integrationId}):`, err.message);
      throw err;
    }
  }

  // Update integration status
  static async updateStatus(integrationId, status, healthStatus, userId) {
    try {
      const result = await pool.query(
        `UPDATE integrations 
        SET 
          status = $1, 
          health_status = $2, 
          last_sync = CURRENT_TIMESTAMP, 
          updated_at = CURRENT_TIMESTAMP
        WHERE integration_id = $3 
        RETURNING *`,
        [status, healthStatus, integrationId]
      );

      logger.info(`Integration status updated: ${integrationId} to ${status} by user ${userId}`);
      return result.rows[0];
    } catch (err) {
      logger.error(`Error in Integration.updateStatus(${integrationId}):`, err.message);
      throw err;
    }
  }

  // Log integration activity
  static async logActivity(integrationId, externalSystemId, eventType, status, message, details) {
    try {
      await pool.query(
        `INSERT INTO integration_logs (
          integration_id, 
          external_system_id, 
          event_type, 
          status, 
          message, 
          details
        ) VALUES ($1, $2, $3, $4, $5, $6)`,
        [
          integrationId,
          externalSystemId,
          eventType,
          status,
          message,
          details || {}
        ]
      );
    } catch (err) {
      logger.error('Error in Integration.logActivity():', err.message);
      // Don't throw the error to prevent integration operations from failing due to logging issues
    }
  }

  // Get integration logs
  static async getLogs(integrationId, limit = 100, offset = 0) {
    try {
      const result = await pool.query(
        `SELECT 
          l.log_id, 
          l.integration_id, 
          l.external_system_id, 
          l.event_type, 
          l.status, 
          l.message, 
          l.details, 
          l.created_at,
          i.name as integration_name,
          e.name as external_system_name
        FROM integration_logs l
        LEFT JOIN integrations i ON l.integration_id = i.integration_id
        LEFT JOIN external_systems e ON l.external_system_id = e.system_id
        WHERE l.integration_id = $1
        ORDER BY l.created_at DESC
        LIMIT $2 OFFSET $3`,
        [integrationId, limit, offset]
      );
      
      return result.rows;
    } catch (err) {
      logger.error(`Error in Integration.getLogs(${integrationId}):`, err.message);
      throw err;
    }
  }
}

module.exports = Integration;
