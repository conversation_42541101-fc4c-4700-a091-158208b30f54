const pool = require('../db');

class PasswordPolicy {
  // Get the current password policy
  static async getCurrent() {
    try {
      const result = await pool.query('SELECT * FROM password_policy ORDER BY policy_id DESC LIMIT 1');

      if (result.rows.length === 0) {
        // If no policy exists, create default policy
        return this.createDefault();
      }

      return result.rows[0];
    } catch (err) {
      console.error('Error in PasswordPolicy.getCurrent():', err.message);
      throw err;
    }
  }

  // Create default password policy
  static async createDefault() {
    try {
      const result = await pool.query(`
        INSERT INTO password_policy (
          min_length,
          max_length,
          require_uppercase,
          require_lowercase,
          require_numbers,
          require_special_chars,
          password_expiry_days,
          password_history_count,
          max_failed_attempts,
          default_password_pattern,
          created_at,
          updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, NOW(), NOW())
        RETURNING *
      `, [8, 30, true, true, true, true, 90, 3, 5, '{username}123']);

      return result.rows[0];
    } catch (err) {
      console.error('Error in PasswordPolicy.createDefault():', err.message);
      throw err;
    }
  }

  // Update password policy
  static async update(policyData) {
    try {
      const {
        min_length,
        max_length,
        require_uppercase,
        require_lowercase,
        require_numbers,
        require_special_chars,
        password_expiry_days,
        password_history_count,
        max_failed_attempts,
        default_password_pattern
      } = policyData;

      const result = await pool.query(`
        UPDATE password_policy
        SET
          min_length = $1,
          max_length = $2,
          require_uppercase = $3,
          require_lowercase = $4,
          require_numbers = $5,
          require_special_chars = $6,
          password_expiry_days = $7,
          password_history_count = $8,
          max_failed_attempts = $9,
          default_password_pattern = $10,
          updated_at = NOW()
        WHERE policy_id = (SELECT MAX(policy_id) FROM password_policy)
        RETURNING *
      `, [
        min_length,
        max_length,
        require_uppercase,
        require_lowercase,
        require_numbers,
        require_special_chars,
        password_expiry_days,
        password_history_count,
        max_failed_attempts,
        default_password_pattern
      ]);

      if (result.rows.length === 0) {
        return this.createDefault();
      }

      return result.rows[0];
    } catch (err) {
      console.error('Error in PasswordPolicy.update():', err.message);
      throw err;
    }
  }

  // Validate a password against the current policy
  static async validatePassword(password, userId = null, username = null) {
    try {
      // Get current policy
      const policy = await this.getCurrent();
      const errors = [];

      // Check length
      if (password.length < policy.min_length) {
        errors.push(`Password must be at least ${policy.min_length} characters long`);
      }

      if (password.length > policy.max_length) {
        errors.push(`Password must be no more than ${policy.max_length} characters long`);
      }

      // Check character requirements
      if (policy.require_uppercase && !/[A-Z]/.test(password)) {
        errors.push('Password must contain at least one uppercase letter');
      }

      if (policy.require_lowercase && !/[a-z]/.test(password)) {
        errors.push('Password must contain at least one lowercase letter');
      }

      if (policy.require_numbers && !/[0-9]/.test(password)) {
        errors.push('Password must contain at least one number');
      }

      if (policy.require_special_chars && !/[^A-Za-z0-9]/.test(password)) {
        errors.push('Password must contain at least one special character');
      }

      // Check password history if userId is provided
      if (userId) {
        const historyResult = await pool.query('SELECT password_history FROM users WHERE user_id = $1', [userId]);

        if (historyResult.rows.length > 0) {
          const passwordHistory = historyResult.rows[0].password_history || [];

          // Check if the new password is in the history
          for (const oldPassword of passwordHistory) {
            const isMatch = await require('bcryptjs').compare(password, oldPassword);
            if (isMatch) {
              errors.push(`Cannot reuse one of your last ${policy.password_history_count} passwords`);
              break;
            }
          }
        }
      }

      // Check if password contains username
      if (username && password.toLowerCase().includes(username.toLowerCase())) {
        errors.push('Password cannot contain your username');
      }

      return {
        isValid: errors.length === 0,
        errors
      };
    } catch (err) {
      console.error('Error in PasswordPolicy.validatePassword():', err.message);
      throw err;
    }
  }

  // Generate a default password based on the policy pattern
  static async generateDefaultPassword(username) {
    try {
      console.log('PasswordPolicy.generateDefaultPassword called with username:', username);

      console.log('Getting current password policy...');
      const policy = await this.getCurrent();
      console.log('Current password policy:', JSON.stringify(policy));

      // Start with a base password that includes the username
      let defaultPassword = username.toLowerCase();

      // Ensure the password meets the minimum length requirement
      if (defaultPassword.length < policy.min_length) {
        defaultPassword += '123';
      }

      // Always add these characters to ensure policy compliance
      defaultPassword += 'A';  // Uppercase
      defaultPassword += '1';  // Number (additional)
      defaultPassword += '!';  // Special character

      console.log('Generated default password:', defaultPassword);

      // Validate the password against the policy
      const validation = await this.validatePassword(defaultPassword, null, username);
      console.log('Validation result:', JSON.stringify(validation));

      if (!validation.isValid) {
        console.log('Warning: Generated password does not meet policy requirements:', validation.errors);
        // If still not valid, add more characters as needed
        if (policy.require_uppercase && !/[A-Z]/.test(defaultPassword)) {
          defaultPassword += 'A';
          console.log('Added uppercase character');
        }
        if (policy.require_lowercase && !/[a-z]/.test(defaultPassword)) {
          defaultPassword += 'a';
          console.log('Added lowercase character');
        }
        if (policy.require_numbers && !/[0-9]/.test(defaultPassword)) {
          defaultPassword += '1';
          console.log('Added number');
        }
        if (policy.require_special_chars && !/[^A-Za-z0-9]/.test(defaultPassword)) {
          defaultPassword += '!';
          console.log('Added special character');
        }
        console.log('Modified password:', defaultPassword);
      }

      console.log('Final default password generated successfully');
      return defaultPassword;
    } catch (err) {
      console.error('Error in PasswordPolicy.generateDefaultPassword():', err.message);
      console.error('Error stack:', err.stack);
      throw err;
    }
  }
}

module.exports = PasswordPolicy;
