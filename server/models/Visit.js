const db = require('../db');

// Helper function to format dates with exact date preservation
const formatDateForNepal = (dateString) => {
  try {
    if (!dateString) return null;

    // Parse the date parts directly from the string to avoid any timezone adjustments
    let year, month, day;

    // If it's in ISO format with a T separator
    if (dateString.includes('T')) {
      [year, month, day] = dateString.split('T')[0].split('-').map(num => parseInt(num, 10));
    }
    // If it's just a date in YYYY-MM-DD format
    else if (dateString.includes('-')) {
      [year, month, day] = dateString.split('-').map(num => parseInt(num, 10));
    }
    // If we couldn't parse it properly, return as is
    else {
      return dateString;
    }

    // Always set time to noon (12:00:00) in Nepal to avoid any midnight boundary issues
    // This ensures the date will never shift due to timezone conversions
    const formattedDate = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')} 12:00:00`;

    console.log('Original date string:', dateString);
    console.log('Formatted as exact date with noon time:', formattedDate);

    return formattedDate;
  } catch (err) {
    console.error('Error preserving exact date:', err);
    return dateString; // Return original if error
  }
};

class Visit {
  /**
   * Create a new patient visit
   * @param {Object} visitData - Visit data
   * @returns {Promise<Object>} Created visit
   */
  static async create(visitData) {
    try {
      console.log('=== Starting Visit Creation ===');
      console.log('Raw incoming visit data:', visitData);

      // Debug log for CBC and Iron Studies fields
      console.log('CBC and Iron Studies fields received by server:', {
        // Red Blood Cell Parameters
        rbc: visitData.rbc,
        hemoglobin: visitData.hemoglobin,
        hematocrit: visitData.hematocrit,
        platelets: visitData.platelets,
        // Red Blood Cell Indices
        mcv: visitData.mcv,
        mch: visitData.mch,
        mchc: visitData.mchc,
        rdw: visitData.rdw,
        // White Blood Cell Count & Differential
        wbc: visitData.wbc,
        neutrophils: visitData.neutrophils,
        lymphocytes: visitData.lymphocytes,
        monocytes: visitData.monocytes,
        eosinophils: visitData.eosinophils,
        basophils: visitData.basophils,
        // Iron Studies
        ferritin: visitData.ferritin,
        iron: visitData.iron
      });

      // Debug log for contact information fields
      console.log('Contact information fields received by server:', {
        phone: visitData.phone,
        email: visitData.email,
        address: visitData.address,
        emergency_contact_name: visitData.emergency_contact_name,
        emergency_contact_phone: visitData.emergency_contact_phone,
        emergency_contact_relationship: visitData.emergency_contact_relationship
      });

      // Validation
      if (!visitData.patient_id) {
        throw new Error('Patient ID is required');
      }

      // Format visit_date and visit_time into a timestamp
      let formattedVisitDate = null;
      if (visitData.visit_date) {
        formattedVisitDate = formatDateForNepal(visitData.visit_date);
      }

      // Check if patient exists
      const patientResult = await db.query(
        'SELECT patient_id FROM patients WHERE patient_id = $1',
        [visitData.patient_id]
      );

      if (patientResult.rows.length === 0) {
        throw new Error(`Patient with ID ${visitData.patient_id} does not exist`);
      }

      // Check if doctor exists (if provided)
      if (visitData.doctor_id) {
        const doctorResult = await db.query(
          'SELECT doctor_id FROM doctors WHERE doctor_id = $1',
          [visitData.doctor_id]
        );

        if (doctorResult.rows.length === 0) {
          throw new Error(`Doctor with ID ${visitData.doctor_id} does not exist`);
        }
      }

      // Get valid columns to properly handle numeric types
      const tableInfoQuery = await db.query(`
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = 'patient_visits'
      `);

      const validColumns = tableInfoQuery.rows.map(row => ({
        name: row.column_name,
        type: row.data_type
      }));

      console.log('Column definitions:', validColumns);

      // Clean and validate the data
      const cleanedData = {};

      // Set default values for time fields if they exist in the database
      const now = new Date();
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:00`;

      // Check if visit_time column exists in the database
      const hasVisitTimeColumn = validColumns.some(col => col.name === 'visit_time');
      const hasEndTimeColumn = validColumns.some(col => col.name === 'end_time');

      console.log('Database column check:', {
        hasVisitTimeColumn,
        hasEndTimeColumn,
        validColumnNames: validColumns.map(col => col.name).join(', ')
      });

      // Only set visit_time if the column exists
      if (hasVisitTimeColumn && (!visitData.visit_time || visitData.visit_time === '')) {
        console.log('Setting default visit_time:', currentTime);
        cleanedData.visit_time = currentTime;
      }

      // Only set end_time if the column exists
      if (hasEndTimeColumn && (!visitData.end_time || visitData.end_time === '')) {
        console.log('Setting default end_time:', currentTime);
        cleanedData.end_time = currentTime;
      }

      Object.entries(visitData).forEach(([key, value]) => {
        const columnInfo = validColumns.find(col => col.name === key);
        if (columnInfo && value !== undefined && value !== null) {
          // Handle numeric fields
          if (columnInfo.type === 'integer' ||
              columnInfo.type.includes('numeric') ||
              columnInfo.type.includes('decimal') ||
              // Explicitly handle PSQI fields as numeric
              key === 'psqi_subjective_sleep_quality' ||
              key === 'psqi_sleep_latency' ||
              key === 'psqi_sleep_duration' ||
              key === 'psqi_sleep_efficiency' ||
              key === 'psqi_sleep_disturbances' ||
              key === 'psqi_sleep_medication' ||
              key === 'psqi_daytime_dysfunction' ||
              key === 'psqi_total_score') {
            // Convert all database numeric types from string to number
            const numValue = typeof value === 'string' ? parseFloat(value) : value;
            if (!isNaN(numValue)) {
              cleanedData[key] = numValue;
              console.log(`Processing numeric field ${key}:`, {
                original: value,
                cleaned: numValue,
                type: typeof numValue,
                column_type: columnInfo.type
              });
            }
          } else if (key === 'visit_date' ||
                    key === 'influenza_vaccination_date' ||
                    key === 'pneumococcal_vaccination_date' ||
                    key === 'pneumococcal_booster_vaccination_date' ||
                    key === 'zoster_vaccination_date' ||
                    key === 'tdap_vaccination_date' ||
                    key === 'covid19_vaccination_date' ||
                    key === 'covid19_booster_date' ||
                    key === 'hepatitis_a_vaccination_date' ||
                    key === 'hepatitis_b_vaccination_date' ||
                    key === 'mmr_vaccination_date' ||
                    key === 'varicella_vaccination_date' ||
                    key === 'psqi_assessment_date') {
            // Handle all date fields consistently
            cleanedData[key] = formatDateForNepal(value);
            console.log(`Processing date field ${key}:`, {
              original: value,
              cleaned: formatDateForNepal(value)
            });
          } else if (key === 'psqi_bedtime' || key === 'psqi_wake_up_time' ||
                    (key === 'visit_time' && hasVisitTimeColumn) ||
                    (key === 'end_time' && hasEndTimeColumn)) {
            // Handle time fields - ensure they're not empty strings
            if (value === '') {
              // Skip empty time values as we've already set defaults above
              console.log(`Skipping empty time field ${key}, will use default`);
            } else {
              cleanedData[key] = value;
              console.log(`Processing time field ${key}:`, {
                value,
                type: typeof value
              });
            }
          } else if (key === 'age_friendly_environment' && columnInfo.type === 'boolean') {
            // Handle boolean conversion for age_friendly_environment
            let boolValue;
            if (typeof value === 'string') {
              boolValue = value.toLowerCase() === 'true';
            } else {
              boolValue = !!value; // Convert to boolean
            }
            cleanedData[key] = boolValue;
            console.log(`Processing boolean field ${key}:`, {
              original: value,
              originalType: typeof value,
              cleaned: boolValue,
              type: typeof boolValue,
              column_type: columnInfo.type
            });
          } else if (key === 'referrals' || key === 'medication_changes') {
            // Special handling for these text fields to ensure they're preserved correctly
            cleanedData[key] = value || ''; // Ensure it's never null or undefined
            console.log(`Processing text field ${key}:`, {
              value,
              type: typeof value,
              length: value ? value.length : 0,
              cleaned: cleanedData[key]
            });
          } else {
            cleanedData[key] = value;
          }
        }
      });

      // Construct query
      const columns = [];
      const values = [];
      const placeholders = [];
      let paramCount = 1;

      Object.entries(cleanedData).forEach(([key, value]) => {
        columns.push(key);
        values.push(value);
        placeholders.push(`$${paramCount++}`);
      });

      // Build and execute query
      const query = `
        INSERT INTO patient_visits (${columns.join(', ')})
        VALUES (${placeholders.join(', ')})
        RETURNING visit_id
      `;

      console.log('Insert query:', query);
      console.log('Insert values:', values);

      const result = await db.query(query, values);

      if (result.rows.length === 0) {
        throw new Error('Failed to create visit');
      }

      // Get the complete visit data including joins
      const visitResult = await db.query(
        `SELECT
          v.*,
          p.first_name AS patient_first_name,
          p.last_name AS patient_last_name,
          p.unique_id AS patient_unique_id,
          d.first_name AS doctor_first_name,
          d.last_name AS doctor_last_name,
          d.specialty AS doctor_specialty,
          u.username AS created_by_username,
          editor.username AS last_edited_by_username
        FROM patient_visits v
        LEFT JOIN patients p ON v.patient_id = p.patient_id
        LEFT JOIN doctors d ON v.doctor_id = d.doctor_id
        LEFT JOIN users u ON v.created_by = u.user_id
        LEFT JOIN users editor ON v.last_edited_by = editor.user_id
        WHERE v.visit_id = $1`,
        [result.rows[0].visit_id]
      );

      return visitResult.rows[0];
    } catch (error) {
      console.error('Error creating visit:', error);
      console.error('Error stack:', error.stack);
      throw error;
    }
  }

  /**
   * Get all visits for a specific patient
   * @param {number} patientId - Patient ID
   * @returns {Promise<Array>} Array of visits
   */
  static async getByPatientId(patientId) {
    try {
      const result = await db.query(
        `SELECT
          v.*,
          p.first_name AS patient_first_name,
          p.last_name AS patient_last_name,
          d.first_name AS doctor_first_name,
          d.last_name AS doctor_last_name,
          d.specialty AS doctor_specialty,
          u.username AS created_by_username,
          editor.username AS last_edited_by_username
        FROM patient_visits v
        LEFT JOIN patients p ON v.patient_id = p.patient_id
        LEFT JOIN doctors d ON v.doctor_id = d.doctor_id
        LEFT JOIN users u ON v.created_by = u.user_id
        LEFT JOIN users editor ON v.last_edited_by = editor.user_id
        WHERE v.patient_id = $1
        ORDER BY v.visit_date DESC`,
        [patientId]
      );
      return result.rows;
    } catch (error) {
      console.error('Error getting patient visits:', error);
      throw error;
    }
  }

  /**
   * Get all visits for a specific doctor
   * @param {number} doctorId - Doctor ID
   * @returns {Promise<Array>} Array of visits
   */
  static async getByDoctorId(doctorId) {
    try {
      const result = await db.query(
        `SELECT
          v.*,
          p.first_name AS patient_first_name,
          p.last_name AS patient_last_name,
          p.unique_id AS patient_unique_id,
          d.first_name AS doctor_first_name,
          d.last_name AS doctor_last_name,
          u.username AS created_by_username
        FROM patient_visits v
        LEFT JOIN patients p ON v.patient_id = p.patient_id
        LEFT JOIN doctors d ON v.doctor_id = d.doctor_id
        LEFT JOIN users u ON v.created_by = u.user_id
        WHERE v.doctor_id = $1
        ORDER BY v.visit_date DESC`,
        [doctorId]
      );
      return result.rows;
    } catch (error) {
      console.error('Error getting doctor visits:', error);
      throw error;
    }
  }

  /**
   * Get a single visit by ID
   * @param {number} visitId - Visit ID
   * @returns {Promise<Object>} Visit data
   */
  static async getById(visitId) {
    try {
      console.log(`Getting visit with ID ${visitId}`);

      const result = await db.query(
        `SELECT
          v.*,
          p.first_name AS patient_first_name,
          p.last_name AS patient_last_name,
          p.unique_id AS patient_unique_id,
          d.first_name AS doctor_first_name,
          d.last_name AS doctor_last_name,
          d.specialty AS doctor_specialty,
          u.username AS created_by_username,
          editor.username AS last_edited_by_username
        FROM patient_visits v
        LEFT JOIN patients p ON v.patient_id = p.patient_id
        LEFT JOIN doctors d ON v.doctor_id = d.doctor_id
        LEFT JOIN users u ON v.created_by = u.user_id
        LEFT JOIN users editor ON v.last_edited_by = editor.user_id
        WHERE v.visit_id = $1`,
        [visitId]
      );

      if (result.rows.length === 0) {
        throw new Error(`Visit with ID ${visitId} not found`);
      }

      console.log('Retrieved visit data with JOINs:', {
        visitId: result.rows[0].visit_id,
        patientFirstName: result.rows[0].patient_first_name,
        doctorFirstName: result.rows[0].doctor_first_name,
        hasPatientFirstName: result.rows[0].hasOwnProperty('patient_first_name')
      });

      return result.rows[0];
    } catch (error) {
      console.error('Error getting visit by ID:', error);
      console.error('Error stack:', error.stack);
      throw error;
    }
  }

  /**
   * Update a visit
   * @param {number} visitId - Visit ID
   * @param {Object} visitData - Updated visit data
   * @param {number} userId - User ID of the person making the update
   * @returns {Promise<Object>} Updated visit
   */
  static async update(visitId, visitData, userId) {
    try {
      console.log('=== Starting Visit Update ===');
      console.log('Visit ID:', visitId);
      console.log('Raw update data:', visitData);
      console.log('Updated by user ID:', userId);

      // Get valid columns
      const tableInfoQuery = await db.query(`
        SELECT column_name, data_type
        FROM information_schema.columns
        WHERE table_name = 'patient_visits'
      `);

      const validColumns = tableInfoQuery.rows.map(row => ({
        name: row.column_name,
        type: row.data_type
      }));

      // Clean and validate the data
      const cleanedData = {};

      // Set default values for time fields if they exist in the database
      const now = new Date();
      const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:00`;

      // Check if time columns exist in the database
      const hasVisitTimeColumn = validColumns.some(col => col.name === 'visit_time');
      const hasEndTimeColumn = validColumns.some(col => col.name === 'end_time');

      console.log('Database column check for update:', {
        hasVisitTimeColumn,
        hasEndTimeColumn,
        validColumnNames: validColumns.map(col => col.name).join(', ')
      });

      // Only handle visit_time if the column exists
      if (hasVisitTimeColumn && (visitData.visit_time === '' || visitData.visit_time === undefined)) {
        console.log('Setting default visit_time for update:', currentTime);
        visitData.visit_time = currentTime;
      }

      // Only handle end_time if the column exists
      if (hasEndTimeColumn && (visitData.end_time === '' || visitData.end_time === undefined)) {
        console.log('Setting default end_time for update:', currentTime);
        visitData.end_time = currentTime;
      }

      Object.entries(visitData).forEach(([key, value]) => {
        const columnInfo = validColumns.find(col => col.name === key);
        if (columnInfo && value !== undefined && value !== null) {
          // Handle numeric fields
          if (columnInfo.type === 'integer' ||
              columnInfo.type.includes('numeric') ||
              columnInfo.type.includes('decimal') ||
              // Explicitly handle PSQI fields as numeric
              key === 'psqi_subjective_sleep_quality' ||
              key === 'psqi_sleep_latency' ||
              key === 'psqi_sleep_duration' ||
              key === 'psqi_sleep_efficiency' ||
              key === 'psqi_sleep_disturbances' ||
              key === 'psqi_sleep_medication' ||
              key === 'psqi_daytime_dysfunction' ||
              key === 'psqi_total_score') {
            const numValue = typeof value === 'string' ? parseFloat(value) : value;
            if (!isNaN(numValue)) {
              cleanedData[key] = numValue;
              console.log(`Processing numeric field ${key}:`, {
                original: value,
                cleaned: numValue,
                type: typeof numValue
              });
            }
          }
          // Handle all date fields
          else if (columnInfo.type === 'date' ||
                   key === 'visit_date' ||
                   key === 'influenza_vaccination_date' ||
                   key === 'pneumococcal_vaccination_date' ||
                   key === 'pneumococcal_booster_vaccination_date' ||
                   key === 'zoster_vaccination_date' ||
                   key === 'tdap_vaccination_date' ||
                   key === 'covid19_vaccination_date' ||
                   key === 'covid19_booster_date' ||
                   key === 'hepatitis_a_vaccination_date' ||
                   key === 'hepatitis_b_vaccination_date' ||
                   key === 'mmr_vaccination_date' ||
                   key === 'varicella_vaccination_date' ||
                   key === 'psqi_assessment_date') {
            cleanedData[key] = formatDateForNepal(value);
          }
          // Handle time fields
          else if (key === 'psqi_bedtime' || key === 'psqi_wake_up_time' ||
                   (key === 'visit_time' && hasVisitTimeColumn) ||
                   (key === 'end_time' && hasEndTimeColumn)) {
            // Skip empty time values as we've already set defaults above
            if (value !== '') {
              cleanedData[key] = value;
              console.log(`Processing time field ${key}:`, {
                value,
                type: typeof value
              });
            } else {
              console.log(`Skipping empty time field ${key}, using default value`);
            }
          }
          // Handle boolean fields
          else if (columnInfo.type === 'boolean') {
            cleanedData[key] = typeof value === 'string' ? value.toLowerCase() === 'true' : !!value;
          }
          // Handle special text fields that should never be null
          else if (key === 'referrals' || key === 'medication_changes') {
            // Always include these fields with at least an empty string
            cleanedData[key] = value || ''; // Ensure it's never null or undefined
            console.log(`Processing special text field ${key}:`, {
              value,
              type: typeof value,
              length: value ? value.length : 0,
              cleaned: cleanedData[key]
            });
          }
          // Handle other text fields
          else {
            cleanedData[key] = value;
          }
        }
      });

      // Build update query
      const updates = [];
      const values = [visitId];
      let paramCount = 2;

      // Add updated_at and last_edited_by fields
      cleanedData.updated_at = new Date().toISOString();
      if (userId) {
        cleanedData.last_edited_by = userId;
      }

      Object.entries(cleanedData).forEach(([key, value]) => {
        updates.push(`${key} = $${paramCount}`);
        values.push(value);
        paramCount++;
      });

      const query = `
        UPDATE patient_visits
        SET ${updates.join(', ')}
        WHERE visit_id = $1
        RETURNING *
      `;

      console.log('Update query:', query);
      console.log('Update values:', values);

      const result = await db.query(query, values);

      if (result.rows.length === 0) {
        throw new Error(`Visit with ID ${visitId} not found`);
      }

      // Get the complete updated visit data including joins
      const visitResult = await db.query(
        `SELECT
          v.*,
          p.first_name AS patient_first_name,
          p.last_name AS patient_last_name,
          p.unique_id AS patient_unique_id,
          d.first_name AS doctor_first_name,
          d.last_name AS doctor_last_name,
          d.specialty AS doctor_specialty,
          u.username AS created_by_username,
          editor.username AS last_edited_by_username
        FROM patient_visits v
        LEFT JOIN patients p ON v.patient_id = p.patient_id
        LEFT JOIN doctors d ON v.doctor_id = d.doctor_id
        LEFT JOIN users u ON v.created_by = u.user_id
        LEFT JOIN users editor ON v.last_edited_by = editor.user_id
        WHERE v.visit_id = $1`,
        [visitId]
      );

      return visitResult.rows[0];
    } catch (error) {
      console.error('Error updating visit:', error);
      throw error;
    }
  }

  /**
   * Delete a visit
   * @param {number} visitId - Visit ID
   * @param {number} userId - User ID of the person deleting the visit
   * @returns {Promise<boolean>} Success status
   */
  static async delete(visitId, userId) {
    try {
      // First get the visit data for logging purposes
      const visitQuery = `
        SELECT v.*,
          p.first_name AS patient_first_name,
          p.last_name AS patient_last_name
        FROM patient_visits v
        LEFT JOIN patients p ON v.patient_id = p.patient_id
        WHERE v.visit_id = $1
      `;
      const visitResult = await db.query(visitQuery, [visitId]);

      if (visitResult.rows.length === 0) {
        throw new Error(`Visit with ID ${visitId} not found`);
      }

      const visitData = visitResult.rows[0];
      const patientId = visitData.patient_id;
      const patientName = `${visitData.patient_first_name || ''} ${visitData.patient_last_name || ''}`.trim();

      // Log the deletion in the system_logs table
      const SystemLog = require('./SystemLog');
      try {
        // Create a system log entry for the visit deletion
        await SystemLog.create({
          log_type: 'visit_deletion',
          user_id: userId,
          related_id: visitId,
          related_name: patientName,
          details: {
            visit_id: visitId,
            patient_id: patientId,
            patient_name: patientName,
            doctor_id: visitData.doctor_id,
            visit_date: visitData.visit_date,
            visit_time: visitData.visit_time,
            visit_reason: visitData.visit_reason,
            diagnosis: visitData.diagnosis,
            treatment_plan: visitData.treatment_plan
          }
        });

        console.log(`Created system log for visit deletion (ID: ${visitId}, Patient: ${patientName})`);
      } catch (logErr) {
        console.error('Error creating visit deletion log:', logErr);
        console.error('Error details:', logErr.stack);
        // Continue with deletion even if logging fails
      }

      // Then delete it
      const result = await db.query(
        'DELETE FROM patient_visits WHERE visit_id = $1',
        [visitId]
      );

      return {
        success: true,
        visitId,
        patientId,
        patientName
      };
    } catch (error) {
      console.error('Error deleting visit:', error);
      throw error;
    }
  }

  /**
   * Get recent visits across all patients
   * @param {number} limit - Number of visits to fetch
   * @returns {Promise<Array>} Array of recent visits
   */
  static async getRecent(limit = 10) {
    try {
      const result = await db.query(
        `SELECT
          v.*,
          p.first_name AS patient_first_name,
          p.last_name AS patient_last_name,
          p.unique_id AS patient_unique_id,
          d.first_name AS doctor_first_name,
          d.last_name AS doctor_last_name,
          d.specialty AS doctor_specialty,
          u.username AS created_by_username,
          editor.username AS last_edited_by_username
        FROM patient_visits v
        LEFT JOIN patients p ON v.patient_id = p.patient_id
        LEFT JOIN doctors d ON v.doctor_id = d.doctor_id
        LEFT JOIN users u ON v.created_by = u.user_id
        LEFT JOIN users editor ON v.last_edited_by = editor.user_id
        ORDER BY v.created_at DESC
        LIMIT $1`,
        [limit]
      );
      return result.rows;
    } catch (error) {
      console.error('Error getting recent visits:', error);
      throw error;
    }
  }
}

module.exports = Visit;