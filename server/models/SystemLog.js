const pool = require('../db');

class SystemLog {
  /**
   * Create a new system log entry
   * @param {Object} logData - Log data
   * @param {string} logData.log_type - Type of log (patient_deletion, visit_deletion, etc.)
   * @param {number} logData.user_id - User ID of the person who performed the action
   * @param {number} logData.related_id - ID of the related entity (patient_id, visit_id, etc.)
   * @param {string} logData.related_name - Name of the related entity (patient name, etc.)
   * @param {Object} logData.details - JSON data with details about the event
   * @param {string} logData.ip_address - IP address of the user
   * @returns {Promise<Object>} Created log entry
   */
  static async create(logData) {
    try {
      const {
        log_type,
        user_id,
        related_id,
        related_name,
        details,
        ip_address = 'system'
      } = logData;

      const result = await pool.query(
        `INSERT INTO system_logs
         (log_type, user_id, related_id, related_name, details, ip_address)
         VALUES ($1, $2, $3, $4, $5, $6)
         RETURNING *`,
        [log_type, user_id, related_id, related_name, JSON.stringify(details), ip_address]
      );

      console.log(`Created ${log_type} log for ID ${related_id} (${related_name})`);
      return result.rows[0];
    } catch (err) {
      console.error(`Error creating system log (${logData.log_type}):`, err);
      console.error('Error details:', err.stack);
      // Don't throw the error - logging should never break the main functionality
      return null;
    }
  }

  /**
   * Get all system logs
   * @param {number} page - Page number
   * @param {number} limit - Number of logs per page
   * @returns {Promise<Object>} Logs with pagination info
   */
  static async getAll(page = 1, limit = 20) {
    try {
      // Check if table exists first
      const tableCheck = await pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_name = 'system_logs'
        )
      `);

      const tableExists = tableCheck.rows[0].exists;
      console.log('system_logs table exists:', tableExists);

      if (!tableExists) {
        return {
          logs: [],
          totalLogs: 0,
          totalPages: 0,
          currentPage: page
        };
      }

      // Get total count for pagination
      const countResult = await pool.query('SELECT COUNT(*) FROM system_logs');
      const totalLogs = parseInt(countResult.rows[0].count);
      console.log('Total system logs found:', totalLogs);

      // Calculate pagination
      const offset = (page - 1) * limit;
      const totalPages = Math.ceil(totalLogs / limit);

      // Get logs with user information
      const result = await pool.query(`
        SELECT
          l.log_id,
          l.log_type,
          l.user_id,
          l.related_id,
          l.related_name,
          l.log_time,
          l.details,
          l.ip_address,
          u.username
        FROM system_logs l
        LEFT JOIN users u ON l.user_id = u.user_id
        ORDER BY l.log_time DESC
        LIMIT $1 OFFSET $2
      `, [limit, offset]);

      console.log('Fetched system logs:', result.rows.length);

      return {
        logs: result.rows,
        totalLogs,
        totalPages,
        currentPage: page
      };
    } catch (err) {
      console.error('Error in SystemLog.getAll:', err);
      throw err;
    }
  }

  /**
   * Get system logs by type
   * @param {string} logType - Type of log to retrieve
   * @param {number} page - Page number
   * @param {number} limit - Number of logs per page
   * @returns {Promise<Object>} Logs with pagination info
   */
  static async getByType(logType, page = 1, limit = 20) {
    try {
      // Get total count for pagination
      const countResult = await pool.query(
        'SELECT COUNT(*) FROM system_logs WHERE log_type = $1',
        [logType]
      );
      const totalLogs = parseInt(countResult.rows[0].count);

      // Calculate pagination
      const offset = (page - 1) * limit;
      const totalPages = Math.ceil(totalLogs / limit);

      // Get logs with user information
      const result = await pool.query(`
        SELECT
          l.log_id,
          l.log_type,
          l.user_id,
          l.related_id,
          l.related_name,
          l.log_time,
          l.details,
          l.ip_address,
          u.username
        FROM system_logs l
        LEFT JOIN users u ON l.user_id = u.user_id
        WHERE l.log_type = $1
        ORDER BY l.log_time DESC
        LIMIT $2 OFFSET $3
      `, [logType, limit, offset]);

      return {
        logs: result.rows,
        totalLogs,
        totalPages,
        currentPage: page
      };
    } catch (err) {
      console.error(`Error in SystemLog.getByType(${logType}):`, err);
      throw err;
    }
  }

  /**
   * Get system logs by user
   * @param {number} userId - User ID
   * @param {number} page - Page number
   * @param {number} limit - Number of logs per page
   * @returns {Promise<Object>} Logs with pagination info
   */
  static async getByUser(userId, page = 1, limit = 20) {
    try {
      // Get total count for pagination
      const countResult = await pool.query(
        'SELECT COUNT(*) FROM system_logs WHERE user_id = $1',
        [userId]
      );
      const totalLogs = parseInt(countResult.rows[0].count);

      // Calculate pagination
      const offset = (page - 1) * limit;
      const totalPages = Math.ceil(totalLogs / limit);

      // Get logs
      const result = await pool.query(`
        SELECT
          l.log_id,
          l.log_type,
          l.user_id,
          l.related_id,
          l.related_name,
          l.log_time,
          l.details,
          l.ip_address,
          u.username
        FROM system_logs l
        LEFT JOIN users u ON l.user_id = u.user_id
        WHERE l.user_id = $1
        ORDER BY l.log_time DESC
        LIMIT $2 OFFSET $3
      `, [userId, limit, offset]);

      return {
        logs: result.rows,
        totalLogs,
        totalPages,
        currentPage: page
      };
    } catch (err) {
      console.error(`Error in SystemLog.getByUser(${userId}):`, err);
      throw err;
    }
  }
}

module.exports = SystemLog;
