const db = require('../db');
let securityLogger;

// Try to import the securityLogger, but don't fail if it's not available
try {
  securityLogger = require('../utils/securityLogger');
} catch (err) {
  console.warn('Security logger not available, security events will not be logged');
  // Create a dummy logger that does nothing
  securityLogger = {
    logSecurityEvent: () => Promise.resolve(null)
  };
}

class Message {
  /**
   * Create a new message
   * @param {Object} messageData - Message data
   * @returns {Object} Created message
   */
  static async create(messageData) {
    try {
      const {
        content,
        sender_id,
        recipient_id,
        patient_id
      } = messageData;

      const result = await db.query(
        `INSERT INTO messages (
          content,
          sender_id,
          recipient_id,
          patient_id,
          read
        ) VALUES ($1, $2, $3, $4, $5) RETURNING *`,
        [content, sender_id, recipient_id, patient_id, false]
      );

      return result.rows[0];
    } catch (error) {
      console.error('Error in Message.create:', error);
      throw error;
    }
  }

  /**
   * Get messages for a user (sent or received)
   * @param {number} userId - User ID
   * @returns {Array} Messages
   */
  static async getForUser(userId) {
    try {
      const result = await db.query(
        `SELECT
          m.*,
          sender.username as sender_username,
          CONCAT(sender_doctor.first_name, ' ', sender_doctor.last_name) as sender_doctor_name,
          CONCAT(sender_patient.first_name, ' ', sender_patient.last_name) as sender_patient_name,
          recipient.username as recipient_username,
          CONCAT(recipient_doctor.first_name, ' ', recipient_doctor.last_name) as recipient_doctor_name,
          CONCAT(recipient_patient.first_name, ' ', recipient_patient.last_name) as recipient_patient_name,
          CONCAT(patient.first_name, ' ', patient.last_name) as patient_name
        FROM messages m
        LEFT JOIN users sender ON m.sender_id = sender.user_id
        LEFT JOIN doctors sender_doctor ON sender.user_id = sender_doctor.user_id
        LEFT JOIN patients sender_patient ON sender.user_id = sender_patient.user_id
        LEFT JOIN users recipient ON m.recipient_id = recipient.user_id
        LEFT JOIN doctors recipient_doctor ON recipient.user_id = recipient_doctor.user_id
        LEFT JOIN patients recipient_patient ON recipient.user_id = recipient_patient.user_id
        LEFT JOIN patients patient ON m.patient_id = patient.patient_id
        WHERE m.sender_id = $1 OR m.recipient_id = $1
        ORDER BY m.created_at DESC`,
        [userId]
      );

      return result.rows;
    } catch (error) {
      console.error('Error in Message.getForUser:', error);
      throw error;
    }
  }

  /**
   * Get messages for a specific patient
   * @param {number} patientId - Patient ID
   * @returns {Array} Messages
   */
  static async getForPatient(patientId) {
    try {
      const result = await db.query(
        `SELECT
          m.*,
          sender.username as sender_username,
          CONCAT(sender_doctor.first_name, ' ', sender_doctor.last_name) as sender_doctor_name,
          CONCAT(sender_patient.first_name, ' ', sender_patient.last_name) as sender_patient_name,
          recipient.username as recipient_username,
          CONCAT(recipient_doctor.first_name, ' ', recipient_doctor.last_name) as recipient_doctor_name,
          CONCAT(recipient_patient.first_name, ' ', recipient_patient.last_name) as recipient_patient_name
        FROM messages m
        LEFT JOIN users sender ON m.sender_id = sender.user_id
        LEFT JOIN doctors sender_doctor ON sender.user_id = sender_doctor.user_id
        LEFT JOIN patients sender_patient ON sender.user_id = sender_patient.user_id
        LEFT JOIN users recipient ON m.recipient_id = recipient.user_id
        LEFT JOIN doctors recipient_doctor ON recipient.user_id = recipient_doctor.user_id
        LEFT JOIN patients recipient_patient ON recipient.user_id = recipient_patient.user_id
        WHERE m.patient_id = $1
        ORDER BY m.created_at DESC`,
        [patientId]
      );

      return result.rows;
    } catch (error) {
      console.error('Error in Message.getForPatient:', error);
      throw error;
    }
  }

  /**
   * Mark a message as read
   * @param {number} messageId - Message ID
   * @returns {Object} Updated message
   */
  static async markAsRead(messageId) {
    try {
      const result = await db.query(
        `UPDATE messages SET read = true WHERE id = $1 RETURNING *`,
        [messageId]
      );

      return result.rows[0];
    } catch (error) {
      console.error('Error in Message.markAsRead:', error);
      throw error;
    }
  }

  /**
   * Delete a message
   * @param {number} messageId - Message ID
   * @param {number} userId - User ID (for security logging)
   * @returns {boolean} Success status
   */
  static async delete(messageId, userId) {
    try {
      // Get message details for logging
      const messageResult = await db.query(
        `SELECT * FROM messages WHERE id = $1`,
        [messageId]
      );

      if (messageResult.rows.length === 0) {
        return false;
      }

      const message = messageResult.rows[0];

      // Delete the message
      const result = await db.query(
        `DELETE FROM messages WHERE id = $1 RETURNING *`,
        [messageId]
      );

      if (result.rows.length === 0) {
        return false;
      }

      // Log the security event
      await securityLogger.logSecurityEvent({
        event_type: 'MESSAGE_DELETE',
        user_id: userId,
        details: `Deleted message ID: ${messageId}`,
        ip_address: 'system', // This would be replaced with the actual IP in the route handler
        resource_type: 'messages',
        resource_id: messageId
      });

      return true;
    } catch (error) {
      console.error('Error in Message.delete:', error);
      throw error;
    }
  }
}

module.exports = Message;
