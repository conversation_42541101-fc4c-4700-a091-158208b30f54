const pool = require('../db');

class PatientEditLog {
  // Create a new edit log entry
  static async create(logData) {
    try {
      // Handle both traditional and enhanced logging formats
      if (typeof logData === 'object' && 'patient_id' in logData) {
        // Enhanced format with additional fields
        const {
          patient_id,
          user_id,
          edit_type = 'standard',
          edit_summary = null,
          field_changed = null,
          old_value = null,
          new_value = null,
          original_data = null,
          new_data = null,
          ip_address = null
        } = logData;

        // If we have original_data, this could be a comprehensive log or a deletion
        if (original_data !== null) {
          // For deletion logs, new_data might be null
          const result = await pool.query(
            `INSERT INTO patient_edit_logs
             (patient_id, user_id, edit_type, edit_summary, field_changed, original_data, new_data, ip_address)
             VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
             RETURNING *`,
            [patient_id, user_id, edit_type, edit_summary, field_changed || 'visit', original_data, new_data, ip_address]
          );
          return result.rows[0];
        } else {
          // Standard field-level logging
          const result = await pool.query(
            `INSERT INTO patient_edit_logs
             (patient_id, user_id, edit_type, field_changed, old_value, new_value, ip_address)
             VALUES ($1, $2, $3, $4, $5, $6, $7)
             RETURNING *`,
            [patient_id, user_id, edit_type, field_changed, old_value, new_value, ip_address]
          );
          return result.rows[0];
        }
      } else {
        // Legacy format (for backward compatibility)
        const [patientId, userId, fieldChanged, oldValue, newValue] = arguments;
        const result = await pool.query(
          `INSERT INTO patient_edit_logs
           (patient_id, user_id, field_changed, old_value, new_value)
           VALUES ($1, $2, $3, $4, $5)
           RETURNING *`,
          [patientId, userId, fieldChanged, oldValue, newValue]
        );
        return result.rows[0];
      }
    } catch (err) {
      console.error('Error creating edit log:', err);
      throw err;
    }
  }

  // Get all edit logs for a patient
  static async getByPatientId(patientId) {
    try {
      const result = await pool.query(
        `SELECT el.*, u.username, u.email, u.role
         FROM patient_edit_logs el
         JOIN users u ON el.user_id = u.user_id
         WHERE el.patient_id = $1
         ORDER BY el.edit_time DESC`,
        [patientId]
      );
      return result.rows;
    } catch (err) {
      console.error('Error fetching patient edit logs:', err);
      throw err;
    }
  }

  // Get edit logs for admin (paginated)
  static async getAll(page = 1, limit = 20) {
    console.log('PatientEditLog.getAll called with page:', page, 'limit:', limit);
    try {
      // First check if table exists
      const tableCheck = await pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_name = 'patient_edit_logs'
        )
      `);

      const tableExists = tableCheck.rows[0].exists;
      console.log('patient_edit_logs table exists:', tableExists);

      if (!tableExists) {
        return {
          logs: [],
          totalLogs: 0,
          totalPages: 0,
          currentPage: page
        };
      }

      // Get total count for pagination
      const countResult = await pool.query('SELECT COUNT(*) FROM patient_edit_logs');
      const totalLogs = parseInt(countResult.rows[0].count);
      console.log('Total edit logs found:', totalLogs);

      // Calculate pagination
      const offset = (page - 1) * limit;
      const totalPages = Math.ceil(totalLogs / limit);

      // First check if the edit_type column exists
      const columnCheckQuery = await pool.query(`
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'patient_edit_logs'
        AND column_name = 'edit_type'
      `);

      const hasEditTypeColumn = columnCheckQuery.rows.length > 0;
      console.log('patient_edit_logs has edit_type column:', hasEditTypeColumn);

      // Use different queries based on schema version
      let result;
      if (hasEditTypeColumn) {
        // New schema with edit_type column
        result = await pool.query(`
          SELECT
            l.edit_id,
            l.patient_id,
            l.user_id,
            l.edit_time,
            l.field_changed,
            l.old_value,
            l.new_value,
            l.edit_type,
            l.edit_summary,
            l.original_data,
            l.new_data,
            u.username,
            p.first_name,
            p.last_name,
            p.unique_id
          FROM patient_edit_logs l
          LEFT JOIN users u ON l.user_id = u.user_id
          LEFT JOIN patients p ON l.patient_id = p.patient_id
          ORDER BY l.edit_time DESC
          LIMIT $1 OFFSET $2
        `, [limit, offset]);
      } else {
        // Old schema without edit_type column
        result = await pool.query(`
          SELECT
            l.edit_id,
            l.patient_id,
            l.user_id,
            l.edit_time,
            l.field_changed,
            l.old_value,
            l.new_value,
            u.username,
            p.first_name,
            p.last_name,
            p.unique_id
          FROM patient_edit_logs l
          LEFT JOIN users u ON l.user_id = u.user_id
          LEFT JOIN patients p ON l.patient_id = p.patient_id
          ORDER BY l.edit_time DESC
          LIMIT $1 OFFSET $2
        `, [limit, offset]);

        // Add virtual edit_type field based on data patterns
        result.rows = result.rows.map(row => {
          // If original_data exists and new_data is null, it's likely a deletion
          if (row.original_data && !row.new_data) {
            row.edit_type = 'delete';
          }
          // If field_changed is 'user_account', it's a user-related operation
          else if (row.field_changed === 'user_account') {
            row.edit_type = row.new_value ? 'update' : 'delete';
          }
          // If field_changed is 'visit', it's a visit-related operation
          else if (row.field_changed === 'visit') {
            row.edit_type = row.new_value ? 'update' : 'delete';
          }
          // Default to standard edit
          else {
            row.edit_type = 'standard';
          }
          return row;
        });
      }

      console.log('Fetched edit logs:', result.rows.length);

      return {
        logs: result.rows,
        totalLogs,
        totalPages,
        currentPage: page
      };
    } catch (err) {
      console.error('Error in PatientEditLog.getAll:', err);
      throw err;
    }
  }

  // Create multiple edit logs at once (for batch updates)
  static async createBatch(logs) {
    // Start a transaction
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      for (const log of logs) {
        const { patientId, userId, fieldChanged, oldValue, newValue } = log;
        await client.query(
          `INSERT INTO patient_edit_logs
           (patient_id, user_id, field_changed, old_value, new_value)
           VALUES ($1, $2, $3, $4, $5)`,
          [patientId, userId, fieldChanged, oldValue, newValue]
        );
      }

      // Update the last_edited_by and last_edited_at fields
      if (logs.length > 0) {
        const { patientId, userId } = logs[0];
        await client.query(
          `UPDATE patients
           SET last_edited_by = $1, last_edited_at = CURRENT_TIMESTAMP
           WHERE patient_id = $2`,
          [userId, patientId]
        );
      }

      await client.query('COMMIT');
    } catch (err) {
      await client.query('ROLLBACK');
      console.error('Error creating batch edit logs:', err);
      throw err;
    } finally {
      client.release();
    }
  }

  // Get edit logs by field
  static async getByField(fieldName, page = 1, limit = 20) {
    const offset = (page - 1) * limit;
    try {
      const result = await pool.query(
        `SELECT el.*,
          u.username as user_username,
          p.first_name as patient_first_name,
          p.last_name as patient_last_name
         FROM patient_edit_logs el
         JOIN users u ON el.user_id = u.user_id
         JOIN patients p ON el.patient_id = p.patient_id
         WHERE el.field_changed = $1
         ORDER BY el.edit_time DESC
         LIMIT $2 OFFSET $3`,
        [fieldName, limit, offset]
      );

      const countResult = await pool.query(
        'SELECT COUNT(*) FROM patient_edit_logs WHERE field_changed = $1',
        [fieldName]
      );

      return {
        logs: result.rows,
        total: parseInt(countResult.rows[0].count),
        page,
        limit,
        totalPages: Math.ceil(parseInt(countResult.rows[0].count) / limit)
      };
    } catch (err) {
      console.error('Error fetching field edit logs:', err);
      throw err;
    }
  }
}

module.exports = PatientEditLog;