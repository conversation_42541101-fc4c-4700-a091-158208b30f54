const pool = require('../db');

class Prescription {
  /**
   * Create a new prescription
   * @param {Object} prescriptionData - Prescription data
   * @returns {Promise<Object>} Created prescription
   */
  static async create(prescriptionData) {
    const {
      patient_id,
      visit_id,
      visit_type = 'initial', // Default to initial visit
      medication,
      dosage,
      frequency,
      duration,
      notes,
      // BEERS criteria fields
      beers_criteria_id,
      beers_override_reason,
      beers_overridden_by,
      beers_overridden_at
    } = prescriptionData;

    // Validate that either patient_id or visit_id is provided
    if (!patient_id && !visit_id) {
      throw new Error('Either patient_id or visit_id must be provided');
    }

    // If we have visit_id but no patient_id, try to get patient_id from the visit
    let resolvedPatientId = patient_id;
    if (!resolvedPatientId && visit_id) {
      try {
        const visitQuery = 'SELECT patient_id FROM patient_visits WHERE visit_id = $1';
        const visitResult = await pool.query(visitQuery, [visit_id]);

        if (visitResult.rows.length > 0 && visitResult.rows[0].patient_id) {
          resolvedPatientId = visitResult.rows[0].patient_id;
          console.log(`Retrieved patient_id ${resolvedPatientId} from visit ${visit_id}`);
        } else {
          console.error(`Could not find patient_id for visit ${visit_id}`);
        }
      } catch (err) {
        console.error(`Error retrieving patient_id from visit ${visit_id}:`, err);
      }
    }

    try {
      // Check if we have BEERS criteria fields
      const hasBeersData = beers_criteria_id || beers_override_reason || beers_overridden_by;

      // Log BEERS criteria data for debugging
      console.log('Creating prescription with BEERS data:', {
        medication,
        patient_id: resolvedPatientId,
        visit_id,
        hasBeersData,
        beers_criteria_id,
        beers_override_reason,
        beers_overridden_by,
        beers_overridden_at
      });

      // Ensure beers_overridden_by is properly set if we have an override reason
      if (beers_override_reason && !beers_overridden_by) {
        console.log('Warning: beers_override_reason is set but beers_overridden_by is not. This may indicate a missing user ID.');
      }

      let query, values;

      if (hasBeersData) {
        // Include BEERS criteria fields in the query
        query = `
          INSERT INTO prescriptions
          (patient_id, visit_id, visit_type, medication, dosage, frequency, duration, notes,
           beers_criteria_id, beers_override_reason, beers_overridden_by, beers_overridden_at)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
          RETURNING *
        `;

        // Use the provided override timestamp or current timestamp if reason is provided
        const overrideTimestamp = beers_overridden_at ||
          (beers_override_reason ? new Date().toISOString() : null);

        values = [
          resolvedPatientId, visit_id, visit_type, medication, dosage, frequency, duration, notes,
          beers_criteria_id, beers_override_reason, beers_overridden_by, overrideTimestamp
        ];
      } else {
        // Basic query without BEERS criteria fields
        query = `
          INSERT INTO prescriptions
          (patient_id, visit_id, visit_type, medication, dosage, frequency, duration, notes)
          VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
          RETURNING *
        `;

        values = [resolvedPatientId, visit_id, visit_type, medication, dosage, frequency, duration, notes];
      }

      // Log the SQL query and values for debugging
      console.log('DETAILED DEBUG: Executing SQL query:', {
        query,
        values
      });

      const result = await pool.query(query, values);
      console.log('DETAILED DEBUG: Created prescription:', result.rows[0]);
      return result.rows[0];
    } catch (err) {
      console.error('Error in Prescription.create():', err);
      throw err;
    }
  }

  /**
   * Create multiple prescriptions
   * @param {Array} prescriptions - Array of prescription objects
   * @param {Object} options - Options for creating prescriptions
   * @param {number} [options.patientId] - Patient ID (for initial visit)
   * @param {number} [options.visitId] - Visit ID (for follow-up visit)
   * @param {string} [options.visitType] - Visit type ('initial' or 'follow-up')
   * @returns {Promise<Array>} Created prescriptions
   */
  static async createMany(prescriptions, options = {}) {
    try {
      const createdPrescriptions = [];
      const { patientId, visitId, visitType = 'initial', userId } = options;

      // Log the user ID from options
      console.log('User ID from options:', userId);

      // Detailed debug logging
      console.log('DETAILED DEBUG: Creating prescriptions with options:', JSON.stringify(options, null, 2));
      console.log('DETAILED DEBUG: Prescriptions to create:', JSON.stringify(prescriptions, null, 2));

      console.log('Creating multiple prescriptions with options:', {
        patientId,
        visitId,
        visitType,
        userId,
        prescriptionCount: prescriptions.length
      });

      for (const prescription of prescriptions) {
        // Log the prescription data for debugging
        console.log('Processing prescription:', {
          medication: prescription.medication,
          patient_id: prescription.patient_id,
          beers_criteria_id: prescription.beers_criteria_id,
          beers_override_reason: prescription.beers_override_reason,
          beers_overridden_by: prescription.beers_overridden_by,
          beers_overridden_at: prescription.beers_overridden_at
        });

        // Check if this prescription has BEERS criteria data
        const hasBeersData = prescription.beers_criteria_id ||
                            prescription.beers_override_reason ||
                            prescription.beers_overridden_by;

        if (hasBeersData) {
          console.log(`Prescription for ${prescription.medication} has BEERS criteria data that needs to be preserved`);
        }

        const prescriptionData = {
          ...prescription
        };

        // Set the appropriate ID based on what's provided
        if (prescriptionData.patient_id) {
          // If prescription already has patient_id, use it
          console.log(`Using prescription's own patient_id: ${prescriptionData.patient_id}`);
          prescriptionData.visit_type = visitType;
        } else if (patientId) {
          // Otherwise use the patientId from options
          console.log(`Setting patient_id from options: ${patientId}`);
          prescriptionData.patient_id = patientId;
          prescriptionData.visit_type = visitType;
        } else if (visitId) {
          // If no patient_id is available, use visitId and retrieve patient_id from the visit
          console.log(`Setting visit_id from options: ${visitId}`);
          prescriptionData.visit_id = visitId;
          prescriptionData.visit_type = 'follow-up';

          // Retrieve patient_id from the visit if not already set
          if (!prescriptionData.patient_id) {
            try {
              const visitQuery = 'SELECT patient_id FROM patient_visits WHERE visit_id = $1';
              const visitResult = await pool.query(visitQuery, [visitId]);

              if (visitResult.rows.length > 0 && visitResult.rows[0].patient_id) {
                prescriptionData.patient_id = visitResult.rows[0].patient_id;
                console.log(`Retrieved patient_id ${prescriptionData.patient_id} from visit ${visitId}`);
              } else {
                console.error(`Could not find patient_id for visit ${visitId}`);
              }
            } catch (err) {
              console.error(`Error retrieving patient_id from visit ${visitId}:`, err);
            }
          }
        } else {
          throw new Error('Either patientId or visitId must be provided');
        }

        // Ensure visit_id is set if provided in options
        if (visitId) {
          // Always set the visit_id from options to ensure consistency
          prescriptionData.visit_id = visitId;
          console.log(`Setting visit_id to ${visitId} for prescription ${prescriptionData.medication}`);
        }

        // Ensure BEERS criteria fields are preserved
        if (prescription.beers_criteria_id || prescription.beers_override_reason ||
            prescription.beers_overridden_by || prescription.beers_overridden_at) {

          // Make sure these fields are explicitly copied from the original prescription
          prescriptionData.beers_criteria_id = prescription.beers_criteria_id;
          prescriptionData.beers_override_reason = prescription.beers_override_reason;

          // Use the user ID from options if available, otherwise use the one from the prescription
          if (prescription.beers_override_reason) {
            if (userId) {
              prescriptionData.beers_overridden_by = userId;
              console.log(`Using user ID from options for beers_overridden_by: ${userId}`);
            } else if (prescription.beers_overridden_by) {
              prescriptionData.beers_overridden_by = prescription.beers_overridden_by;
              console.log(`Using prescription's beers_overridden_by: ${prescription.beers_overridden_by}`);
            } else {
              console.log('No user ID available for beers_overridden_by');
            }
          }

          prescriptionData.beers_overridden_at = prescription.beers_overridden_at ||
                                                (prescription.beers_override_reason ? new Date().toISOString() : null);

          console.log('Preserved BEERS criteria data:', {
            beers_criteria_id: prescriptionData.beers_criteria_id,
            beers_override_reason: prescriptionData.beers_override_reason,
            beers_overridden_by: prescriptionData.beers_overridden_by,
            beers_overridden_at: prescriptionData.beers_overridden_at
          });
        }

        // Log the final prescription data before creation
        console.log('Final prescription data:', {
          medication: prescriptionData.medication,
          patient_id: prescriptionData.patient_id,
          visit_id: prescriptionData.visit_id,
          beers_criteria_id: prescriptionData.beers_criteria_id,
          beers_override_reason: prescriptionData.beers_override_reason,
          beers_overridden_by: prescriptionData.beers_overridden_by,
          beers_overridden_at: prescriptionData.beers_overridden_at
        });

        const createdPrescription = await this.create(prescriptionData);
        console.log('Created prescription:', {
          prescription_id: createdPrescription.prescription_id,
          patient_id: createdPrescription.patient_id,
          visit_id: createdPrescription.visit_id,
          beers_criteria_id: createdPrescription.beers_criteria_id,
          beers_override_reason: createdPrescription.beers_override_reason,
          beers_overridden_by: createdPrescription.beers_overridden_by,
          beers_overridden_at: createdPrescription.beers_overridden_at
        });

        createdPrescriptions.push(createdPrescription);
      }

      return createdPrescriptions;
    } catch (err) {
      console.error('Error in Prescription.createMany():', err);
      throw err;
    }
  }



  /**
   * Get all prescriptions for a patient that are not associated with a visit
   * @param {number} patientId - Patient ID
   * @returns {Promise<Array>} Prescriptions
   */
  static async getByPatientId(patientId) {
    try {
      // Modified query to only return prescriptions that don't have a visit_id
      // This ensures prescriptions are only shown in the Edit Patient view
      // and not duplicated in the Visit view
      const result = await pool.query(
        `SELECT
          p.*,
          NULL as prescription_date, -- No visit date for these prescriptions
          bc.medication_name as beers_criteria_name,
          bc.category as beers_criteria_category,
          bc.recommendation as beers_criteria_recommendation,
          bc.rationale as beers_criteria_rationale,
          u.username as overridden_by_username
        FROM prescriptions p
        LEFT JOIN beers_criteria bc ON p.beers_criteria_id = bc.criteria_id
        LEFT JOIN users u ON p.beers_overridden_by = u.user_id
        WHERE
          p.patient_id = $1 AND
          p.visit_id IS NULL
        ORDER BY p.created_at DESC`,
        [patientId]
      );

      console.log(`Retrieved ${result.rows.length} prescriptions for patient ${patientId} (without visit_id)`);
      return result.rows;
    } catch (err) {
      console.error('Error in Prescription.getByPatientId():', err);
      throw err;
    }
  }

  /**
   * Get all prescriptions for a visit
   * @param {number} visitId - Visit ID
   * @returns {Promise<Array>} Prescriptions
   */
  static async getByVisitId(visitId) {
    try {
      // Get the patient_id for this visit to ensure we're getting the right prescriptions
      const visitQuery = 'SELECT patient_id FROM patient_visits WHERE visit_id = $1';
      const visitResult = await pool.query(visitQuery, [visitId]);

      if (visitResult.rows.length === 0) {
        console.error(`Visit with ID ${visitId} not found`);
        return [];
      }

      const patientId = visitResult.rows[0].patient_id;
      console.log(`Found patient_id ${patientId} for visit ${visitId}`);

      // Query prescriptions that have this specific visit_id
      const result = await pool.query(
        `SELECT
          p.*,
          pv.visit_date as prescription_date,
          bc.medication_name as beers_criteria_name,
          bc.category as beers_criteria_category,
          bc.recommendation as beers_criteria_recommendation,
          bc.rationale as beers_criteria_rationale,
          u.username as overridden_by_username
        FROM prescriptions p
        LEFT JOIN patient_visits pv ON p.visit_id = pv.visit_id
        LEFT JOIN beers_criteria bc ON p.beers_criteria_id = bc.criteria_id
        LEFT JOIN users u ON p.beers_overridden_by = u.user_id
        WHERE p.visit_id = $1
        ORDER BY p.created_at DESC`,
        [visitId]
      );

      console.log(`Retrieved ${result.rows.length} prescriptions for visit ${visitId}`);
      return result.rows;
    } catch (err) {
      console.error('Error in Prescription.getByVisitId():', err);
      throw err;
    }
  }

  /**
   * Get all prescriptions for a patient, including those associated with visits
   * @param {number} patientId - Patient ID
   * @returns {Promise<Array>} Prescriptions
   */
  static async getAllByPatientId(patientId) {
    try {
      // Query that gets all prescriptions for a patient, including those associated with visits
      const result = await pool.query(
        `SELECT
          p.*,
          pv.visit_date as prescription_date,
          bc.medication_name as beers_criteria_name,
          bc.category as beers_criteria_category,
          bc.recommendation as beers_criteria_recommendation,
          bc.rationale as beers_criteria_rationale,
          u.username as overridden_by_username
        FROM prescriptions p
        LEFT JOIN patient_visits pv ON p.visit_id = pv.visit_id
        LEFT JOIN beers_criteria bc ON p.beers_criteria_id = bc.criteria_id
        LEFT JOIN users u ON p.beers_overridden_by = u.user_id
        WHERE
          p.patient_id = $1 OR
          (pv.patient_id = $1 AND p.visit_id IS NOT NULL)
        ORDER BY p.created_at DESC`,
        [patientId]
      );

      console.log(`Retrieved ${result.rows.length} total prescriptions for patient ${patientId} (including visit prescriptions)`);
      return result.rows;
    } catch (err) {
      console.error('Error in Prescription.getAllByPatientId():', err);
      throw err;
    }
  }

  /**
   * Get prescription by ID
   * @param {number} prescriptionId - Prescription ID
   * @returns {Promise<Object>} Prescription
   */
  static async getById(prescriptionId) {
    try {
      const result = await pool.query(
        `SELECT
          p.*,
          bc.medication_name as beers_criteria_name,
          bc.category as beers_criteria_category,
          bc.recommendation as beers_criteria_recommendation,
          bc.rationale as beers_criteria_rationale,
          u.username as overridden_by_username
        FROM prescriptions p
        LEFT JOIN beers_criteria bc ON p.beers_criteria_id = bc.criteria_id
        LEFT JOIN users u ON p.beers_overridden_by = u.user_id
        WHERE p.prescription_id = $1`,
        [prescriptionId]
      );

      return result.rows[0];
    } catch (err) {
      console.error('Error in Prescription.getById():', err);
      throw err;
    }
  }

  /**
   * Update prescription
   * @param {number} prescriptionId - Prescription ID
   * @param {Object} prescriptionData - Updated prescription data
   * @returns {Promise<Object>} Updated prescription
   */
  static async update(prescriptionId, prescriptionData) {
    const {
      medication,
      dosage,
      frequency,
      duration,
      notes,
      beers_criteria_id,
      beers_override_reason,
      beers_overridden_by,
      beers_overridden_at,
      visit_id,
      patient_id
    } = prescriptionData;

    try {
      let query = `UPDATE prescriptions SET medication = $1, dosage = $2, frequency = $3, duration = $4, notes = $5`;
      const values = [medication, dosage, frequency, duration, notes];
      let paramCount = 5;

      // Add visit_id if provided
      if (visit_id) {
        paramCount++;
        query += `, visit_id = $${paramCount}`;
        values.push(visit_id);
      }

      // Add patient_id if provided
      if (patient_id) {
        paramCount++;
        query += `, patient_id = $${paramCount}`;
        values.push(patient_id);
      }

      // Add BEERS override fields if provided
      if (beers_criteria_id || beers_override_reason || beers_overridden_by) {
        if (beers_criteria_id) {
          paramCount++;
          query += `, beers_criteria_id = $${paramCount}`;
          values.push(beers_criteria_id);
        }

        if (beers_override_reason) {
          paramCount++;
          query += `, beers_override_reason = $${paramCount}`;
          values.push(beers_override_reason);
        }

        if (beers_overridden_by) {
          paramCount++;
          query += `, beers_overridden_by = $${paramCount}`;
          values.push(beers_overridden_by);
        }

        // Set the override timestamp to now if not provided
        paramCount++;
        query += `, beers_overridden_at = $${paramCount}`;
        values.push(beers_overridden_at || 'NOW()');
      }

      // Add WHERE clause and RETURNING
      paramCount++;
      query += ` WHERE prescription_id = $${paramCount} RETURNING *`;
      values.push(prescriptionId);

      const result = await pool.query(query, values);

      if (result.rows.length === 0) {
        throw new Error(`Prescription with ID ${prescriptionId} not found`);
      }

      return result.rows[0];
    } catch (err) {
      console.error('Error in Prescription.update():', err);
      throw err;
    }
  }

  /**
   * Delete prescription
   * @param {number} prescriptionId - Prescription ID
   * @returns {Promise<Object>} Success status
   */
  static async delete(prescriptionId) {
    try {
      await pool.query(
        'DELETE FROM prescriptions WHERE prescription_id = $1',
        [prescriptionId]
      );

      return { success: true };
    } catch (err) {
      console.error('Error in Prescription.delete():', err);
      throw err;
    }
  }

  /**
   * Override BEERS Criteria alert for a prescription
   * @param {number} prescriptionId - Prescription ID
   * @param {Object} overrideData - Override data
   * @param {number} overrideData.criteriaId - BEERS Criteria ID
   * @param {string} overrideData.reason - Override reason
   * @param {number} overrideData.userId - User ID who overrode the alert
   * @returns {Promise<Object>} Updated prescription
   */
  static async overrideBeersCriteria(prescriptionId, overrideData) {
    try {
      const { criteriaId, reason, userId } = overrideData;

      if (!criteriaId || !reason || !userId) {
        throw new Error('Missing required override data');
      }

      const query = `
        UPDATE prescriptions
        SET
          beers_criteria_id = $1,
          beers_override_reason = $2,
          beers_overridden_by = $3,
          beers_overridden_at = CURRENT_TIMESTAMP
        WHERE prescription_id = $4
        RETURNING *
      `;

      const result = await pool.query(query, [criteriaId, reason, userId, prescriptionId]);

      if (result.rows.length === 0) {
        throw new Error(`Prescription with ID ${prescriptionId} not found`);
      }

      return result.rows[0];
    } catch (err) {
      console.error('Error in Prescription.overrideBeersCriteria():', err);
      throw err;
    }
  }
}

module.exports = Prescription;
