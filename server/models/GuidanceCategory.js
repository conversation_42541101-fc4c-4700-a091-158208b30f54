const pool = require('../db');

class GuidanceCategory {
  /**
   * Get all guidance categories
   * @param {boolean} includeHierarchy - Whether to include hierarchical structure
   * @returns {Promise<Array>} Array of categories
   */
  static async getAll(includeHierarchy = false) {
    try {
      if (includeHierarchy) {
        // Get categories with hierarchical structure
        const query = `
          WITH RECURSIVE category_tree AS (
            SELECT 
              c.category_id, 
              c.name, 
              c.description, 
              c.parent_id, 
              c.created_at, 
              c.updated_at,
              0 AS level,
              ARRAY[c.category_id] AS path
            FROM guidance_categories c
            WHERE c.parent_id IS NULL
            
            UNION ALL
            
            SELECT 
              c.category_id, 
              c.name, 
              c.description, 
              c.parent_id, 
              c.created_at, 
              c.updated_at,
              ct.level + 1,
              ct.path || c.category_id
            FROM guidance_categories c
            JOIN category_tree ct ON c.parent_id = ct.category_id
          )
          SELECT 
            ct.*,
            (SELECT COUNT(*) FROM clinical_guidance g WHERE g.category_id = ct.category_id) AS guidance_count
          FROM category_tree ct
          ORDER BY path
        `;
        const result = await pool.query(query);
        return result.rows;
      } else {
        // Get flat list of categories
        const query = `
          SELECT 
            c.*,
            p.name AS parent_name,
            (SELECT COUNT(*) FROM clinical_guidance g WHERE g.category_id = c.category_id) AS guidance_count
          FROM guidance_categories c
          LEFT JOIN guidance_categories p ON c.parent_id = p.category_id
          ORDER BY c.name
        `;
        const result = await pool.query(query);
        return result.rows;
      }
    } catch (err) {
      console.error('Error getting guidance categories:', err);
      throw err;
    }
  }

  /**
   * Get a single guidance category by ID
   * @param {number} categoryId - Category ID
   * @returns {Promise<Object>} Category
   */
  static async getById(categoryId) {
    try {
      const query = `
        SELECT 
          c.*,
          p.name AS parent_name,
          (SELECT COUNT(*) FROM clinical_guidance g WHERE g.category_id = c.category_id) AS guidance_count
        FROM guidance_categories c
        LEFT JOIN guidance_categories p ON c.parent_id = p.category_id
        WHERE c.category_id = $1
      `;
      const result = await pool.query(query, [categoryId]);
      
      if (result.rows.length === 0) {
        return null;
      }
      
      return result.rows[0];
    } catch (err) {
      console.error(`Error getting guidance category with ID ${categoryId}:`, err);
      throw err;
    }
  }

  /**
   * Create a new guidance category
   * @param {Object} categoryData - Category data
   * @returns {Promise<Object>} Created category
   */
  static async create(categoryData) {
    try {
      const { name, description, parentId } = categoryData;
      
      const query = `
        INSERT INTO guidance_categories (name, description, parent_id)
        VALUES ($1, $2, $3)
        RETURNING *
      `;
      const result = await pool.query(query, [name, description, parentId || null]);
      
      return result.rows[0];
    } catch (err) {
      console.error('Error creating guidance category:', err);
      throw err;
    }
  }

  /**
   * Update a guidance category
   * @param {number} categoryId - Category ID
   * @param {Object} categoryData - Updated category data
   * @returns {Promise<Object>} Updated category
   */
  static async update(categoryId, categoryData) {
    try {
      const { name, description, parentId } = categoryData;
      
      // Prevent circular references
      if (parentId) {
        const checkCircularQuery = `
          WITH RECURSIVE category_tree AS (
            SELECT category_id, parent_id
            FROM guidance_categories
            WHERE category_id = $1
            
            UNION ALL
            
            SELECT c.category_id, c.parent_id
            FROM guidance_categories c
            JOIN category_tree ct ON c.category_id = ct.parent_id
          )
          SELECT COUNT(*) > 0 AS is_circular
          FROM category_tree
          WHERE category_id = $2
        `;
        const circularCheck = await pool.query(checkCircularQuery, [parentId, categoryId]);
        
        if (circularCheck.rows[0].is_circular) {
          throw new Error('Circular reference detected in category hierarchy');
        }
      }
      
      const query = `
        UPDATE guidance_categories
        SET 
          name = $1,
          description = $2,
          parent_id = $3,
          updated_at = CURRENT_TIMESTAMP
        WHERE category_id = $4
        RETURNING *
      `;
      const result = await pool.query(query, [name, description, parentId || null, categoryId]);
      
      if (result.rows.length === 0) {
        throw new Error(`Category with ID ${categoryId} not found`);
      }
      
      return result.rows[0];
    } catch (err) {
      console.error(`Error updating guidance category with ID ${categoryId}:`, err);
      throw err;
    }
  }

  /**
   * Delete a guidance category
   * @param {number} categoryId - Category ID
   * @returns {Promise<boolean>} Success status
   */
  static async delete(categoryId) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');
      
      // Update child categories to have null parent
      await client.query(
        'UPDATE guidance_categories SET parent_id = NULL WHERE parent_id = $1',
        [categoryId]
      );
      
      // Update guidance entries to have null category
      await client.query(
        'UPDATE clinical_guidance SET category_id = NULL WHERE category_id = $1',
        [categoryId]
      );
      
      // Delete the category
      const deleteQuery = 'DELETE FROM guidance_categories WHERE category_id = $1';
      const result = await client.query(deleteQuery, [categoryId]);
      
      await client.query('COMMIT');
      return result.rowCount > 0;
    } catch (err) {
      await client.query('ROLLBACK');
      console.error(`Error deleting guidance category with ID ${categoryId}:`, err);
      throw err;
    } finally {
      client.release();
    }
  }

  /**
   * Get child categories for a parent category
   * @param {number} parentId - Parent category ID
   * @returns {Promise<Array>} Array of child categories
   */
  static async getChildren(parentId) {
    try {
      const query = `
        SELECT 
          c.*,
          (SELECT COUNT(*) FROM clinical_guidance g WHERE g.category_id = c.category_id) AS guidance_count
        FROM guidance_categories c
        WHERE c.parent_id = $1
        ORDER BY c.name
      `;
      const result = await pool.query(query, [parentId]);
      return result.rows;
    } catch (err) {
      console.error(`Error getting child categories for parent ID ${parentId}:`, err);
      throw err;
    }
  }

  /**
   * Get all tags used in the system
   * @returns {Promise<Array>} Array of tags
   */
  static async getAllTags() {
    try {
      const query = `
        SELECT 
          t.*,
          COUNT(gtr.guidance_id) AS usage_count
        FROM guidance_tags t
        LEFT JOIN guidance_tag_relations gtr ON t.tag_id = gtr.tag_id
        GROUP BY t.tag_id
        ORDER BY t.name
      `;
      const result = await pool.query(query);
      return result.rows;
    } catch (err) {
      console.error('Error getting guidance tags:', err);
      throw err;
    }
  }
}

module.exports = GuidanceCategory;
