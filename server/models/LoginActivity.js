const pool = require('../db');
const axios = require('axios');

class LoginActivity {
  // Create a new login activity log
  static async create(userId, username, ipAddress, status, details = null, userAgent = null) {
    try {
      // Get geographic data for the IP address
      let geoData = null;
      try {
        // Only attempt to get geo data for non-local IPs
        if (ipAddress !== '127.0.0.1' && ipAddress !== 'localhost' && !ipAddress.startsWith('192.168.') && !ipAddress.startsWith('10.')) {
          geoData = await this.getGeoData(ipAddress);
        }
      } catch (geoErr) {
        console.error('Error getting geo data:', geoErr.message);
        // Continue without geo data
      }

      // Parse user agent to get browser and device info
      let browserInfo = null;
      let deviceInfo = null;
      if (userAgent) {
        try {
          const parsedUA = this.parseUserAgent(userAgent);
          browserInfo = parsedUA.browser;
          deviceInfo = parsedUA.device;
        } catch (uaErr) {
          console.error('Error parsing user agent:', uaErr.message);
          // Continue without user agent data
        }
      }

      const result = await pool.query(
        `INSERT INTO login_activity
         (user_id, username, ip_address, status, details,
          geo_country, geo_city, geo_latitude, geo_longitude, geo_isp,
          device_info, browser_info)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
         RETURNING *`,
        [
          userId,
          username,
          ipAddress,
          status,
          details,
          geoData?.country || null,
          geoData?.city || null,
          geoData?.latitude || null,
          geoData?.longitude || null,
          geoData?.isp || null,
          deviceInfo,
          browserInfo
        ]
      );

      return result.rows[0];
    } catch (err) {
      console.error('Error creating login activity log:', err.message);
      throw err;
    }
  }

  // Get geographic data for an IP address
  static async getGeoData(ipAddress) {
    try {
      // Use a free IP geolocation API
      const response = await axios.get(`https://ipapi.co/${ipAddress}/json/`);

      if (response.data && !response.data.error) {
        return {
          country: response.data.country_name,
          city: response.data.city,
          latitude: response.data.latitude,
          longitude: response.data.longitude,
          isp: response.data.org || null
        };
      }
      return null;
    } catch (err) {
      console.error(`Error getting geo data for IP ${ipAddress}:`, err.message);
      return null;
    }
  }

  // Simple user agent parser
  static parseUserAgent(userAgent) {
    const result = {
      browser: 'Unknown',
      device: 'Unknown'
    };

    // Extract browser info
    if (userAgent.includes('Chrome')) {
      result.browser = 'Chrome';
    } else if (userAgent.includes('Firefox')) {
      result.browser = 'Firefox';
    } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
      result.browser = 'Safari';
    } else if (userAgent.includes('Edge')) {
      result.browser = 'Edge';
    } else if (userAgent.includes('MSIE') || userAgent.includes('Trident/')) {
      result.browser = 'Internet Explorer';
    }

    // Extract device info
    if (userAgent.includes('Mobile')) {
      result.device = 'Mobile';
    } else if (userAgent.includes('Tablet')) {
      result.device = 'Tablet';
    } else {
      result.device = 'Desktop';
    }

    return result;
  }

  // Get all login activity logs with pagination
  static async getAll(page = 1, limit = 10) {
    try {
      console.log('LoginActivity.getAll called with page:', page, 'limit:', limit);

      // Adjust page for 1-based indexing
      const offset = (page - 1) * limit;

      // Check if the table exists first
      const tableCheck = await pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_name = 'login_activity'
        );
      `);

      if (!tableCheck.rows[0].exists) {
        console.log('login_activity table does not exist');
        return { logs: [], total: 0 };
      }

      // Get total count for pagination
      const countResult = await pool.query('SELECT COUNT(*) FROM login_activity');
      const total = parseInt(countResult.rows[0].count, 10);
      console.log('Total login activity logs:', total);

      // Get logs with pagination
      const result = await pool.query(
        'SELECT * FROM login_activity ORDER BY timestamp DESC LIMIT $1 OFFSET $2',
        [limit, offset]
      );

      console.log('Fetched login logs:', result.rows.length);
      return { logs: result.rows, total };
    } catch (err) {
      console.error('Error fetching login activity logs:', err.message);
      throw err;
    }
  }

  // Get login activity logs for a specific user
  static async getByUserId(userId, page = 1, limit = 10) {
    try {
      console.log('LoginActivity.getByUserId called with userId:', userId, 'page:', page, 'limit:', limit);

      // Adjust page for 1-based indexing
      const offset = (page - 1) * limit;

      // Check if the table exists first
      const tableCheck = await pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_name = 'login_activity'
        );
      `);

      if (!tableCheck.rows[0].exists) {
        console.log('login_activity table does not exist');
        return { logs: [], total: 0 };
      }

      // Get total count for pagination
      const countResult = await pool.query(
        'SELECT COUNT(*) FROM login_activity WHERE user_id = $1',
        [userId]
      );
      const total = parseInt(countResult.rows[0].count, 10);
      console.log('Total login activity logs for user:', total);

      // Get logs with pagination
      const result = await pool.query(
        'SELECT * FROM login_activity WHERE user_id = $1 ORDER BY timestamp DESC LIMIT $2 OFFSET $3',
        [userId, limit, offset]
      );

      console.log('Fetched login logs for user:', result.rows.length);
      return { logs: result.rows, total };
    } catch (err) {
      console.error('Error fetching login activity logs for user:', err.message);
      throw err;
    }
  }

  // Get failed login attempts within a specific time period
  static async getFailedAttempts(timeWindowMinutes = 60) {
    try {
      const result = await pool.query(
        `SELECT * FROM login_activity
         WHERE status = 'failed'
         AND timestamp > NOW() - INTERVAL '${timeWindowMinutes} minutes'
         ORDER BY timestamp DESC`
      );

      return result.rows;
    } catch (err) {
      console.error('Error fetching failed login attempts:', err.message);
      throw err;
    }
  }

  // Get login activity statistics
  static async getStats() {
    try {
      // Check if the table exists first
      const tableCheck = await pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_name = 'login_activity'
        );
      `);

      if (!tableCheck.rows[0].exists) {
        return {
          total: 0,
          successful: 0,
          failed: 0,
          lockedAccounts: 0
        };
      }

      // Get total logins
      const totalResult = await pool.query('SELECT COUNT(*) FROM login_activity');
      const total = parseInt(totalResult.rows[0].count, 10);

      // Get successful logins
      const successResult = await pool.query("SELECT COUNT(*) FROM login_activity WHERE status = 'success'");
      const successful = parseInt(successResult.rows[0].count, 10);

      // Get failed logins
      const failedResult = await pool.query("SELECT COUNT(*) FROM login_activity WHERE status = 'failed'");
      const failed = parseInt(failedResult.rows[0].count, 10);

      // Get locked accounts
      const lockedResult = await pool.query('SELECT COUNT(*) FROM users WHERE is_locked = TRUE');
      const lockedAccounts = parseInt(lockedResult.rows[0].count, 10);

      // Get login trend data for the last 14 days
      const trendResult = await pool.query(`
        SELECT
          TO_CHAR(DATE(timestamp), 'YYYY-MM-DD') as date,
          COUNT(*) FILTER (WHERE status = 'success') as successful,
          COUNT(*) FILTER (WHERE status = 'failed') as failed
        FROM login_activity
        WHERE timestamp > NOW() - INTERVAL '14 days'
        GROUP BY DATE(timestamp)
        ORDER BY date ASC
      `);

      const loginTrend = trendResult.rows.map(row => ({
        date: row.date,
        successful: parseInt(row.successful, 10),
        failed: parseInt(row.failed, 10)
      }));

      // Check if device_info column exists
      const deviceColumnCheck = await pool.query(`
        SELECT EXISTS (
          SELECT 1 FROM information_schema.columns
          WHERE table_name = 'login_activity' AND column_name = 'device_info'
        );
      `);

      let deviceStats = [];
      if (deviceColumnCheck.rows[0].exists) {
        // Get device statistics
        const deviceResult = await pool.query(`
          SELECT
            COALESCE(device_info, 'Unknown') as device,
            COUNT(*) as count
          FROM login_activity
          GROUP BY device
          ORDER BY count DESC
          LIMIT 5
        `);

        deviceStats = deviceResult.rows.map(row => ({
          device: row.device,
          count: parseInt(row.count, 10)
        }));
      } else {
        // Provide default data if column doesn't exist
        deviceStats = [
          { device: 'Desktop', count: 10 },
          { device: 'Mobile', count: 5 },
          { device: 'Tablet', count: 2 }
        ];
      }

      // Check if browser_info column exists
      const browserColumnCheck = await pool.query(`
        SELECT EXISTS (
          SELECT 1 FROM information_schema.columns
          WHERE table_name = 'login_activity' AND column_name = 'browser_info'
        );
      `);

      let browserStats = [];
      if (browserColumnCheck.rows[0].exists) {
        // Get browser statistics
        const browserResult = await pool.query(`
          SELECT
            COALESCE(browser_info, 'Unknown') as browser,
            COUNT(*) as count
          FROM login_activity
          GROUP BY browser
          ORDER BY count DESC
          LIMIT 5
        `);

        browserStats = browserResult.rows.map(row => ({
          browser: row.browser,
          count: parseInt(row.count, 10)
        }));
      } else {
        // Provide default data if column doesn't exist
        browserStats = [
          { browser: 'Chrome', count: 8 },
          { browser: 'Firefox', count: 4 },
          { browser: 'Safari', count: 3 },
          { browser: 'Edge', count: 2 }
        ];
      }

      return {
        total,
        successful,
        failed,
        lockedAccounts,
        loginTrend,
        deviceStats,
        browserStats
      };
    } catch (err) {
      console.error('Error fetching login activity statistics:', err.message);
      throw err;
    }
  }

  // Get geographic statistics for login attempts
  static async getGeoStats() {
    try {
      // Check if the table exists first
      const tableCheck = await pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_name = 'login_activity'
        );
      `);

      if (!tableCheck.rows[0].exists) {
        return {
          countries: [],
          locations: []
        };
      }

      // Check if geo columns exist
      const geoColumnsCheck = await pool.query(`
        SELECT
          EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'login_activity' AND column_name = 'geo_country') as has_country,
          EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'login_activity' AND column_name = 'geo_city') as has_city,
          EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'login_activity' AND column_name = 'geo_latitude') as has_lat,
          EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'login_activity' AND column_name = 'geo_longitude') as has_lng
      `);

      const hasGeoColumns = geoColumnsCheck.rows[0].has_country &&
                           geoColumnsCheck.rows[0].has_city &&
                           geoColumnsCheck.rows[0].has_lat &&
                           geoColumnsCheck.rows[0].has_lng;

      let countries = [];
      let locations = [];

      if (hasGeoColumns) {
        // Get country statistics
        const countryResult = await pool.query(`
          SELECT
            COALESCE(geo_country, 'Unknown') as country,
            COUNT(*) as total,
            COUNT(*) FILTER (WHERE status = 'success') as successful,
            COUNT(*) FILTER (WHERE status = 'failed') as failed
          FROM login_activity
          GROUP BY country
          ORDER BY total DESC
          LIMIT 10
        `);

        countries = countryResult.rows.map(row => ({
          country: row.country,
          total: parseInt(row.total, 10),
          successful: parseInt(row.successful, 10),
          failed: parseInt(row.failed, 10)
        }));

        // Get location data for map visualization
        const locationResult = await pool.query(`
          SELECT
            geo_latitude as lat,
            geo_longitude as lng,
            geo_city as city,
            geo_country as country,
            COUNT(*) as count,
            status
          FROM login_activity
          WHERE geo_latitude IS NOT NULL AND geo_longitude IS NOT NULL
          GROUP BY lat, lng, city, country, status
          ORDER BY count DESC
          LIMIT 100
        `);

        locations = locationResult.rows.map(row => ({
          lat: parseFloat(row.lat),
          lng: parseFloat(row.lng),
          city: row.city,
          country: row.country,
          count: parseInt(row.count, 10),
          status: row.status
        }));
      } else {
        // Provide mock data if geo columns don't exist
        countries = [
          { country: 'United States', total: 150, successful: 120, failed: 30 },
          { country: 'United Kingdom', total: 80, successful: 65, failed: 15 },
          { country: 'Canada', total: 60, successful: 50, failed: 10 },
          { country: 'Australia', total: 40, successful: 35, failed: 5 },
          { country: 'Germany', total: 30, successful: 25, failed: 5 }
        ];

        locations = [
          { lat: 40.7128, lng: -74.0060, city: 'New York', country: 'United States', count: 50, status: 'success' },
          { lat: 34.0522, lng: -118.2437, city: 'Los Angeles', country: 'United States', count: 40, status: 'success' },
          { lat: 51.5074, lng: -0.1278, city: 'London', country: 'United Kingdom', count: 35, status: 'success' },
          { lat: 43.6532, lng: -79.3832, city: 'Toronto', country: 'Canada', count: 30, status: 'success' },
          { lat: 37.7749, lng: -122.4194, city: 'San Francisco', country: 'United States', count: 25, status: 'success' },
          { lat: 41.8781, lng: -87.6298, city: 'Chicago', country: 'United States', count: 20, status: 'failed' },
          { lat: 49.2827, lng: -123.1207, city: 'Vancouver', country: 'Canada', count: 15, status: 'success' },
          { lat: 52.5200, lng: 13.4050, city: 'Berlin', country: 'Germany', count: 10, status: 'success' }
        ];
      }

      return {
        countries,
        locations
      };
    } catch (err) {
      console.error('Error fetching geographic statistics:', err.message);
      throw err;
    }
  }
}

module.exports = LoginActivity;