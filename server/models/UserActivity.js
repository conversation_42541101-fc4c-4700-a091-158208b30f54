const pool = require('../db');

class UserActivity {
  /**
   * Create a new user activity record
   * @param {Object} activityData - Activity data
   * @param {number} activityData.user_id - User ID
   * @param {string} activityData.activity_type - Type of activity (login, logout, view, edit, create, delete)
   * @param {string} activityData.activity_details - Details of the activity
   * @param {string} activityData.ip_address - IP address of the user
   * @returns {Promise<Object>} Created activity record
   */
  static async create(activityData) {
    try {
      // Check if user_activities table exists
      const tableExists = await this.ensureTableExists();
      
      if (!tableExists) {
        await this.createTable();
      }

      const { user_id, activity_type, activity_details, ip_address } = activityData;

      const result = await pool.query(
        `INSERT INTO user_activities 
         (user_id, activity_type, activity_details, ip_address)
         VALUES ($1, $2, $3, $4)
         RETURNING *`,
        [user_id, activity_type, activity_details, ip_address]
      );

      return result.rows[0];
    } catch (err) {
      console.error('Error creating user activity record:', err);
      // Don't throw the error to prevent disrupting the main application flow
      return null;
    }
  }

  /**
   * Get all user activities
   * @param {Object} options - Query options
   * @param {number} options.limit - Maximum number of records to return
   * @param {number} options.offset - Number of records to skip
   * @param {number} options.user_id - Filter by user ID
   * @param {string} options.activity_type - Filter by activity type
   * @param {string} options.start_date - Filter by start date
   * @param {string} options.end_date - Filter by end date
   * @returns {Promise<Array>} Array of activity records
   */
  static async getAll(options = {}) {
    try {
      // Check if user_activities table exists
      const tableExists = await this.ensureTableExists();
      
      if (!tableExists) {
        await this.createTable();
        return [];
      }

      const { limit = 100, offset = 0, user_id, activity_type, start_date, end_date } = options;

      let query = `
        SELECT a.*, u.username
        FROM user_activities a
        JOIN users u ON a.user_id = u.user_id
        WHERE 1=1
      `;
      
      const params = [];
      let paramIndex = 1;

      if (user_id) {
        query += ` AND a.user_id = $${paramIndex++}`;
        params.push(user_id);
      }

      if (activity_type) {
        query += ` AND a.activity_type = $${paramIndex++}`;
        params.push(activity_type);
      }

      if (start_date) {
        query += ` AND a.timestamp >= $${paramIndex++}`;
        params.push(start_date);
      }

      if (end_date) {
        query += ` AND a.timestamp <= $${paramIndex++}`;
        params.push(end_date);
      }

      query += ` ORDER BY a.timestamp DESC LIMIT $${paramIndex++} OFFSET $${paramIndex++}`;
      params.push(limit, offset);

      const result = await pool.query(query, params);
      return result.rows;
    } catch (err) {
      console.error('Error getting user activities:', err);
      throw err;
    }
  }

  /**
   * Get user activity statistics
   * @returns {Promise<Array>} Array of user statistics
   */
  static async getUserStats() {
    try {
      // Check if user_activities table exists
      const tableExists = await this.ensureTableExists();
      
      if (!tableExists) {
        await this.createTable();
        return [];
      }

      const query = `
        SELECT 
          u.user_id,
          u.username,
          u.email,
          u.role,
          u.created_at,
          u.last_login,
          u.is_locked,
          COUNT(CASE WHEN a.activity_type = 'login' THEN 1 END) AS login_count,
          COUNT(a.*) AS activity_count,
          CASE 
            WHEN u.last_login IS NOT NULL THEN 
              EXTRACT(DAY FROM NOW() - u.last_login)::INTEGER
            ELSE 
              EXTRACT(DAY FROM NOW() - u.created_at)::INTEGER
          END AS inactive_days
        FROM 
          users u
        LEFT JOIN 
          user_activities a ON u.user_id = a.user_id
        GROUP BY 
          u.user_id, u.username, u.email, u.role, u.created_at, u.last_login, u.is_locked
        ORDER BY 
          inactive_days DESC
      `;

      const result = await pool.query(query);
      return result.rows;
    } catch (err) {
      console.error('Error getting user statistics:', err);
      throw err;
    }
  }

  /**
   * Get inactive users
   * @param {number} days - Number of days of inactivity
   * @returns {Promise<Array>} Array of inactive users
   */
  static async getInactiveUsers(days = 30) {
    try {
      // Check if user_activities table exists
      const tableExists = await this.ensureTableExists();
      
      if (!tableExists) {
        await this.createTable();
        return [];
      }

      const query = `
        SELECT 
          u.user_id,
          u.username,
          u.email,
          u.role,
          u.created_at,
          u.last_login,
          u.is_locked,
          CASE 
            WHEN u.last_login IS NOT NULL THEN 
              EXTRACT(DAY FROM NOW() - u.last_login)::INTEGER
            ELSE 
              EXTRACT(DAY FROM NOW() - u.created_at)::INTEGER
          END AS inactive_days
        FROM 
          users u
        WHERE 
          (u.last_login IS NULL AND EXTRACT(DAY FROM NOW() - u.created_at) >= $1)
          OR
          (u.last_login IS NOT NULL AND EXTRACT(DAY FROM NOW() - u.last_login) >= $1)
        ORDER BY 
          inactive_days DESC
      `;

      const result = await pool.query(query, [days]);
      return result.rows;
    } catch (err) {
      console.error('Error getting inactive users:', err);
      throw err;
    }
  }

  /**
   * Check if user_activities table exists
   * @returns {Promise<boolean>} True if table exists, false otherwise
   */
  static async ensureTableExists() {
    try {
      const result = await pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_name = 'user_activities'
        ) as table_exists
      `);

      return result.rows[0].table_exists;
    } catch (err) {
      console.error('Error checking if user_activities table exists:', err);
      throw err;
    }
  }

  /**
   * Create user_activities table
   * @returns {Promise<void>}
   */
  static async createTable() {
    try {
      await pool.query(`
        CREATE TABLE IF NOT EXISTS user_activities (
          activity_id SERIAL PRIMARY KEY,
          user_id INTEGER REFERENCES users(user_id),
          activity_type VARCHAR(50) NOT NULL,
          activity_details TEXT,
          ip_address VARCHAR(50),
          timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        CREATE INDEX IF NOT EXISTS idx_user_activities_user_id ON user_activities(user_id);
        CREATE INDEX IF NOT EXISTS idx_user_activities_activity_type ON user_activities(activity_type);
        CREATE INDEX IF NOT EXISTS idx_user_activities_timestamp ON user_activities(timestamp);
      `);

      console.log('user_activities table created successfully');
    } catch (err) {
      console.error('Error creating user_activities table:', err);
      throw err;
    }
  }
}

module.exports = UserActivity;
