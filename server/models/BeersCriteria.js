const pool = require('../db');
const logger = require('../utils/logger');

/**
 * BeersCriteria model for handling BEERS criteria data and operations
 */
class BeersCriteria {
  /**
   * Get all BEERS criteria
   * @returns {Promise<Array>} Array of BEERS criteria
   */
  static async getAll() {
    try {
      const query = `
        SELECT * FROM beers_criteria
        ORDER BY category, medication_name
      `;
      const result = await pool.query(query);
      return result.rows;
    } catch (err) {
      logger.error('Error fetching BEERS criteria:', err);
      throw err;
    }
  }

  /**
   * Get BEERS criteria by ID
   * @param {number} criteriaId - The criteria ID
   * @returns {Promise<Object>} BEERS criteria object
   */
  static async getById(criteriaId) {
    try {
      const query = `
        SELECT * FROM beers_criteria
        WHERE criteria_id = $1
      `;
      const result = await pool.query(query, [criteriaId]);
      return result.rows[0];
    } catch (err) {
      logger.error(`Error fetching BEERS criteria with ID ${criteriaId}:`, err);
      throw err;
    }
  }

  /**
   * Get BEERS criteria by medication name
   * @param {string} medicationName - The medication name
   * @returns {Promise<Array>} Array of BEERS criteria for the medication
   */
  static async getByMedicationName(medicationName) {
    try {
      const query = `
        SELECT * FROM beers_criteria
        WHERE medication_name ILIKE $1
        ORDER BY category
      `;
      const result = await pool.query(query, [`%${medicationName}%`]);
      return result.rows;
    } catch (err) {
      logger.error(`Error fetching BEERS criteria for medication ${medicationName}:`, err);
      throw err;
    }
  }

  /**
   * Get BEERS criteria by category
   * @param {string} category - The category ('avoid', 'use_with_caution', etc.)
   * @returns {Promise<Array>} Array of BEERS criteria in the category
   */
  static async getByCategory(category) {
    try {
      const query = `
        SELECT * FROM beers_criteria
        WHERE category = $1
        ORDER BY medication_name
      `;
      const result = await pool.query(query, [category]);
      return result.rows;
    } catch (err) {
      logger.error(`Error fetching BEERS criteria for category ${category}:`, err);
      throw err;
    }
  }

  /**
   * Get BEERS criteria by condition
   * @param {string} condition - The medical condition
   * @returns {Promise<Array>} Array of BEERS criteria for the condition
   */
  static async getByCondition(condition) {
    try {
      const query = `
        SELECT * FROM beers_criteria
        WHERE condition_name ILIKE $1
        ORDER BY medication_name
      `;
      const result = await pool.query(query, [`%${condition}%`]);
      return result.rows;
    } catch (err) {
      logger.error(`Error fetching BEERS criteria for condition ${condition}:`, err);
      throw err;
    }
  }

  /**
   * Create a new BEERS criteria entry
   * @param {Object} criteriaData - The criteria data
   * @returns {Promise<Object>} Created BEERS criteria object
   */
  static async create(criteriaData) {
    try {
      const {
        medication_name,
        category,
        condition_name,
        interacting_medication,
        recommendation,
        rationale,
        quality_of_evidence,
        strength_of_recommendation
      } = criteriaData;

      const query = `
        INSERT INTO beers_criteria (
          medication_name,
          category,
          condition_name,
          interacting_medication,
          recommendation,
          rationale,
          quality_of_evidence,
          strength_of_recommendation
        )
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *
      `;

      const values = [
        medication_name,
        category,
        condition_name || null,
        interacting_medication || null,
        recommendation,
        rationale,
        quality_of_evidence,
        strength_of_recommendation
      ];

      const result = await pool.query(query, values);
      return result.rows[0];
    } catch (err) {
      logger.error('Error creating BEERS criteria:', err);
      throw err;
    }
  }

  /**
   * Update a BEERS criteria entry
   * @param {number} criteriaId - The criteria ID
   * @param {Object} criteriaData - The updated criteria data
   * @returns {Promise<Object>} Updated BEERS criteria object
   */
  static async update(criteriaId, criteriaData) {
    try {
      const {
        medication_name,
        category,
        condition_name,
        interacting_medication,
        recommendation,
        rationale,
        quality_of_evidence,
        strength_of_recommendation
      } = criteriaData;

      const query = `
        UPDATE beers_criteria
        SET
          medication_name = $1,
          category = $2,
          condition_name = $3,
          interacting_medication = $4,
          recommendation = $5,
          rationale = $6,
          quality_of_evidence = $7,
          strength_of_recommendation = $8,
          updated_at = CURRENT_TIMESTAMP
        WHERE criteria_id = $9
        RETURNING *
      `;

      const values = [
        medication_name,
        category,
        condition_name || null,
        interacting_medication || null,
        recommendation,
        rationale,
        quality_of_evidence,
        strength_of_recommendation,
        criteriaId
      ];

      const result = await pool.query(query, values);
      return result.rows[0];
    } catch (err) {
      logger.error(`Error updating BEERS criteria with ID ${criteriaId}:`, err);
      throw err;
    }
  }

  /**
   * Delete a BEERS criteria entry
   * @param {number} criteriaId - The criteria ID
   * @returns {Promise<boolean>} True if deleted successfully
   */
  static async delete(criteriaId) {
    try {
      const query = `
        DELETE FROM beers_criteria
        WHERE criteria_id = $1
        RETURNING criteria_id
      `;
      const result = await pool.query(query, [criteriaId]);
      return result.rowCount > 0;
    } catch (err) {
      logger.error(`Error deleting BEERS criteria with ID ${criteriaId}:`, err);
      throw err;
    }
  }

  /**
   * Check medications against BEERS criteria
   * @param {Array<string>} medications - Array of medication names
   * @param {Object} patientData - Patient data including age, conditions, etc.
   * @returns {Promise<Array>} Array of alerts for potentially inappropriate medications
   */
  static async checkMedications(medications, patientData) {
    try {
      // Skip check if patient is under 65
      if (patientData.age < 65) {
        return [];
      }

      const alerts = [];

      // Check each medication against BEERS criteria
      for (const medication of medications) {
        // Skip empty medications
        if (!medication || medication.trim() === '') continue;

        // Get criteria for this medication
        const criteria = await this.getByMedicationName(medication.trim());

        if (criteria.length > 0) {
          for (const criterion of criteria) {
            // Check if this criterion applies to the patient
            if (this.criterionApplies(criterion, patientData)) {
              alerts.push({
                medication: medication.trim(),
                criterion: criterion,
                message: this.generateAlertMessage(criterion)
              });
            }
          }
        }
      }

      return alerts;
    } catch (err) {
      logger.error('Error checking medications against BEERS criteria:', err);
      throw err;
    }
  }

  /**
   * Check if a criterion applies to a patient
   * @param {Object} criterion - The BEERS criterion
   * @param {Object} patientData - Patient data
   * @returns {boolean} True if the criterion applies
   */
  static criterionApplies(criterion, patientData) {
    // For 'avoid' category, always applies to older adults
    if (criterion.category === 'avoid') {
      return true;
    }

    // For disease-specific criteria, check if patient has the condition
    if (criterion.category === 'disease_interaction' && criterion.condition_name) {
      // Check medical history, diagnosis, etc. for the condition
      const conditionRegex = new RegExp(criterion.condition_name, 'i');

      if (patientData.medical_history && conditionRegex.test(patientData.medical_history)) {
        return true;
      }

      if (patientData.diagnosis && conditionRegex.test(patientData.diagnosis)) {
        return true;
      }
    }

    // For drug-drug interactions, check if patient is taking both medications
    if (criterion.category === 'drug_interaction' && criterion.interacting_medication) {
      // Check if the interacting medication is in the patient's medication list
      const medications = patientData.current_medications || '';
      const interactingMedRegex = new RegExp(criterion.interacting_medication, 'i');

      if (interactingMedRegex.test(medications)) {
        return true;
      }
    }

    // For kidney function criteria, check patient's eGFR or creatinine
    if (criterion.category === 'adjust_for_kidney_function') {
      // Check if patient has reduced kidney function
      if (patientData.egfr && patientData.egfr < 60) {
        return true;
      }

      if (patientData.creatinine && patientData.creatinine > 1.2) {
        return true;
      }
    }

    // For use_with_caution category
    if (criterion.category === 'use_with_caution') {
      // This category should also apply to older adults, similar to 'avoid'
      return true;
    }

    return false;
  }

  /**
   * Generate an alert message for a criterion
   * @param {Object} criterion - The BEERS criterion
   * @returns {string} Alert message
   */
  static generateAlertMessage(criterion) {
    let message = `BEERS Criteria Alert: ${criterion.medication_name} is potentially inappropriate for older adults`;

    if (criterion.category === 'disease_interaction' && criterion.condition_name) {
      message += ` with ${criterion.condition_name}`;
    }

    if (criterion.category === 'drug_interaction' && criterion.interacting_medication) {
      message += ` when taken with ${criterion.interacting_medication}`;
    }

    if (criterion.category === 'adjust_for_kidney_function') {
      message += ` in patients with reduced kidney function`;
    }

    message += `. ${criterion.recommendation}`;

    return message;
  }

  /**
   * Save BEERS criteria alerts for a patient visit
   * @param {number} visitId - The visit ID
   * @param {Array} alerts - Array of BEERS criteria alerts
   * @returns {Promise<boolean>} True if saved successfully
   */
  static async saveAlerts(visitId, alerts) {
    try {
      const query = `
        UPDATE patient_visits
        SET beers_criteria_alerts = $1
        WHERE visit_id = $2
        RETURNING visit_id
      `;

      const alertsJson = JSON.stringify(alerts);
      const result = await pool.query(query, [alertsJson, visitId]);

      return result.rowCount > 0;
    } catch (err) {
      logger.error(`Error saving BEERS criteria alerts for visit ${visitId}:`, err);
      throw err;
    }
  }

  /**
   * Save BEERS criteria overrides for a patient visit
   * @param {number} visitId - The visit ID
   * @param {Array} overrides - Array of BEERS criteria overrides
   * @returns {Promise<boolean>} True if saved successfully
   */
  static async saveOverrides(visitId, overrides) {
    try {
      const query = `
        UPDATE patient_visits
        SET beers_criteria_overrides = $1
        WHERE visit_id = $2
        RETURNING visit_id
      `;

      const overridesJson = JSON.stringify(overrides);
      const result = await pool.query(query, [overridesJson, visitId]);

      return result.rowCount > 0;
    } catch (err) {
      logger.error(`Error saving BEERS criteria overrides for visit ${visitId}:`, err);
      throw err;
    }
  }

  /**
   * Get BEERS criteria alerts for a patient visit
   * @param {number} visitId - The visit ID
   * @returns {Promise<Array>} Array of BEERS criteria alerts
   */
  static async getAlerts(visitId) {
    try {
      const query = `
        SELECT beers_criteria_alerts
        FROM patient_visits
        WHERE visit_id = $1
      `;

      const result = await pool.query(query, [visitId]);

      if (result.rows.length === 0 || !result.rows[0].beers_criteria_alerts) {
        return [];
      }

      return JSON.parse(result.rows[0].beers_criteria_alerts);
    } catch (err) {
      logger.error(`Error getting BEERS criteria alerts for visit ${visitId}:`, err);
      throw err;
    }
  }

  /**
   * Get BEERS criteria overrides for a patient visit
   * @param {number} visitId - The visit ID
   * @returns {Promise<Array>} Array of BEERS criteria overrides
   */
  static async getOverrides(visitId) {
    try {
      const query = `
        SELECT beers_criteria_overrides
        FROM patient_visits
        WHERE visit_id = $1
      `;

      const result = await pool.query(query, [visitId]);

      if (result.rows.length === 0 || !result.rows[0].beers_criteria_overrides) {
        return [];
      }

      return JSON.parse(result.rows[0].beers_criteria_overrides);
    } catch (err) {
      logger.error(`Error getting BEERS criteria overrides for visit ${visitId}:`, err);
      throw err;
    }
  }
}

module.exports = BeersCriteria;
