const pool = require('../db');
const PatientEditLog = require('./PatientEditLog');
const User = require('./User');
const MedicalRecord = require('./MedicalRecord');
const Prescription = require('./Prescription');
const {
  processNumericFields,
  createInsertQuery,
  logDbError
} = require('../utils/modelHelpers');

// Define common patient fields
const PATIENT_NUMERIC_FIELDS = [
  // Blood pressure measurements
  'lying_bp_systolic', 'lying_bp_diastolic',
  'sitting_bp_systolic', 'sitting_bp_diastolic',
  'standing_bp_systolic', 'standing_bp_diastolic',

  // Heart rate measurements
  'lying_heart_rate', 'standing_heart_rate', 'heart_rate',

  // Other vital signs
  'respiratory_rate', 'pulse_oximetry', 'oxygen_saturation',
  'body_temperature', 'temperature',

  // Lab results
  'blood_glucose', 'hba1c', 'cholesterol_total',
  'hdl_cholesterol', 'ldl_cholesterol', 'triglycerides',
  'sodium', 'potassium', 'calcium', 'magnesium', 'phosphorus',
  'creatinine', 'egfr', 'blood_urea_nitrogen', 'urine_albumin_creatinine_ratio',

  // Liver function
  'alt', 'ast', 'alp', 'bilirubin', 'albumin',

  // Bone health and other markers
  'vitamin_d', 'tsh', 't4', 't3',
  'crp', 'esr', 'hemoglobin', 'hematocrit', 'ferritin', 'psa', 'vitamin_b12', 'folate',

  // Assessment scores
  'medication_adherence', 'pain_level',
  'health_checkup_adherence', 'pill_burden', 'cognitive_impairment_score',
  'depression_score', 'anxiety_score',
  'calf_circumference', 'grip_strength', 'sleep_quality_duration', 'sleep_duration',

  // Mini-Cog scores
  'mini_cog_word_recall_score', 'mini_cog_clock_drawing_score',

  // PHQ-9 scores
  'phq9_interest_pleasure', 'phq9_feeling_down', 'phq9_sleep_issues',
  'phq9_tired', 'phq9_appetite', 'phq9_feeling_bad',
  'phq9_concentration', 'phq9_moving_speaking', 'phq9_thoughts_hurting',
  'phq9_difficulty_level',

  // GAD-7 scores
  'gad7_feeling_nervous', 'gad7_stop_worrying', 'gad7_worrying_much',
  'gad7_trouble_relaxing', 'gad7_restless', 'gad7_annoyed',
  'gad7_feeling_afraid', 'gad7_difficulty_level',

  // PSQI scores
  'psqi_subjective_sleep_quality', 'psqi_sleep_latency', 'psqi_sleep_duration',
  'psqi_sleep_efficiency', 'psqi_sleep_disturbances', 'psqi_sleep_medication',
  'psqi_daytime_dysfunction', 'psqi_total_score', 'psqi_hours_of_sleep',
  'psqi_minutes_to_fall_asleep',

  // FRAT scores
  'frat_fall_history', 'frat_fall_history_score',
  'frat_medications', 'frat_medications_score',
  'frat_psychological', 'frat_psychological_score',
  'frat_cognitive', 'frat_cognitive_score',
  'frat_total_score',

  // Physical measurements
  'weight', 'height', 'bmi',

  // References
  'doctor_id'
];

// Define field mappings from form fields to database columns
const FIELD_MAPPINGS = {
  // Map form fields to database columns where they differ
  'emergency_contact': 'emergency_contact_name', // Map emergency_contact to emergency_contact_name
  'emergency_contact_number': 'emergency_contact_phone', // Map emergency_contact_number to emergency_contact_phone
  'free_t4': 't4', // Map free_t4 to t4
  'free_t3': 't3', // Map free_t3 to t3
  'medical_allergies': 'medical_allergies', // Explicitly map medical_allergies to medical_allergies

  // Fields that are now in the database after migration
  'lying_heart_rate': 'lying_heart_rate',
  'sitting_heart_rate': 'sitting_heart_rate',
  'standing_heart_rate': 'standing_heart_rate',
  'medical_history': 'medical_history',
  'emergency_contact_name': 'emergency_contact_name',
  'height': 'height',

  // Lab Results - Lipid Profile
  'cholesterol_total': 'cholesterol_total',
  'ldl_cholesterol': 'ldl_cholesterol',
  'hdl_cholesterol': 'hdl_cholesterol',
  'vldl': 'vldl',
  'triglycerides': 'triglycerides',

  // Lab Results - Liver Function
  'alt': 'alt',
  'ast': 'ast',
  'alp': 'alp',
  'ggt': 'ggt',
  'bilirubin_t': 'bilirubin_t',
  'bilirubin_d': 'bilirubin_d',
  'total_protein': 'total_protein',

  // Lab Results - Kidney Function
  'blood_urea_nitrogen': 'blood_urea_nitrogen',
  'uric_acid': 'uric_acid',
  'urine_albumin_creatinine_ratio': 'urine_albumin_creatinine_ratio',

  // Lab Results - Electrolytes
  'sodium': 'sodium',
  'potassium': 'potassium',
  'calcium': 'calcium',
  'magnesium': 'magnesium',
  'phosphorus': 'phosphorus',

  // Lab Results - Complete Blood Count
  'rbc': 'rbc',
  'wbc': 'wbc',
  'platelets': 'platelets',
  'hemoglobin': 'hemoglobin',
  'hematocrit': 'hematocrit',
  'mcv': 'mcv',
  'mch': 'mch',
  'mchc': 'mchc',
  'rdw': 'rdw',

  // Lab Results - Inflammation & Anemia
  'crp': 'crp',
  'esr': 'esr',
  'ferritin': 'ferritin',

  // Lab Results - Thyroid Function
  'tsh': 'tsh',

  // Lab Results - Vitamins
  'vitamin_b12': 'vitamin_b12',
  'vitamin_d': 'vitamin_d',
  'folate': 'folate',

  // Lab Results - Cancer Markers
  'psa': 'psa',
  'ca125': 'ca125',

  // Lab Results - Urine Analysis
  'urine_color': 'urine_color',
  'urine_transparency': 'urine_transparency',
  'urine_ph': 'urine_ph',
  'urine_sugar': 'urine_sugar',
  'urine_pus_cells': 'urine_pus_cells',
  'urine_rbcs': 'urine_rbcs',
  'urine_epithelial_cells': 'urine_epithelial_cells',
  'urine_crystals': 'urine_crystals',
  'urine_casts': 'urine_casts',
  'urine_protein': 'urine_protein',

  // Lab Results - Additional Blood Tests
  'iron': 'iron',
  'neutrophils': 'neutrophils',
  'lymphocytes': 'lymphocytes',
  'monocytes': 'monocytes',
  'eosinophils': 'eosinophils',
  'basophils': 'basophils',

  // Fields that are not in the database
  'parathyroid_hormone': null, // Not in database
  'alkaline_phosphatase_bone': null, // Not in database
  'medication_changes': null, // Not in database
  'fall_risk_assessment': null, // Not in database
  'nutrition_diet': null, // Not in database
  'sleep_patterns': 'sleep_patterns', // Now in database
  'social_support_network': 'social_support_network', // Now in database
  'home_safety_evaluation': 'home_safety_evaluation', // Now in database
  'emergency_contact_relationship': 'emergency_contact_relationship', // Now in database
  'influenza_vaccination_date': 'influenza_vaccination_date',
  'pneumococcal_vaccination_date': 'pneumococcal_vaccination_date',
  'pneumococcal_booster_vaccination_date': 'pneumococcal_booster_vaccination_date',
  'zoster_vaccination_date': 'zoster_vaccination_date',
  'tdap_vaccination_date': 'tdap_vaccination_date',
  'covid19_vaccination_date': 'covid19_vaccination_date',
  'covid19_booster_date': 'covid19_booster_date',
  'hepatitis_a_vaccination_date': 'hepatitis_a_vaccination_date',
  'hepatitis_b_vaccination_date': 'hepatitis_b_vaccination_date',
  'mmr_vaccination_date': 'mmr_vaccination_date',
  'varicella_vaccination_date': 'varicella_vaccination_date',
  'other_vaccinations': 'other_vaccinations',
  'diagnosis': 'diagnosis', // Now in database after migration
  'treatment_plan': 'treatment_plan', // Now in database after migration
  'follow_up_instructions': 'follow_up_instructions', // Now in database after migration
  'referrals': 'referrals', // Now in database after migration
  'notes': null, // Not in database
  'assistive_devices_used': 'assistive_devices_used', // Now in database after migration
  'fall_detection_incidents': 'fall_detection_incidents', // Now in database after migration
  'transportation_access': 'transportation_access', // Now in database
  'financial_concern': 'financial_concern', // Now in database
  'financial_concerns': 'financial_concern', // Map to financial_concern for backward compatibility
  'cholesterol_levels': null, // Not in database - can be inferred from cholesterol_total
  'grip_strength': 'grip_strength', // Now in database after migration

  // FRAT fields
  'frat_assessment_date': 'frat_assessment_date',
  'frat_fall_history': 'frat_fall_history',
  'frat_fall_history_score': 'frat_fall_history_score',
  'frat_medications': 'frat_medications',
  'frat_medications_score': 'frat_medications_score',
  'frat_psychological': 'frat_psychological',
  'frat_psychological_score': 'frat_psychological_score',
  'frat_cognitive': 'frat_cognitive',
  'frat_cognitive_score': 'frat_cognitive_score',
  'frat_total_score': 'frat_total_score',
  'frat_risk_level': 'frat_risk_level',
  'frat_notes': 'frat_notes',

  // Mini-Cog fields
  'mini_cog_word_recall_score': 'mini_cog_word_recall_score',
  'mini_cog_clock_drawing_score': 'mini_cog_clock_drawing_score',
  'mini_cog_words_used': 'mini_cog_words_used',
  'mini_cog_words_recalled': 'mini_cog_words_recalled',
  'mini_cog_notes': 'mini_cog_notes',

  // PHQ-9 fields
  'phq9_interest_pleasure': 'phq9_interest_pleasure',
  'phq9_feeling_down': 'phq9_feeling_down',
  'phq9_sleep_issues': 'phq9_sleep_issues',
  'phq9_tired': 'phq9_tired',
  'phq9_appetite': 'phq9_appetite',
  'phq9_feeling_bad': 'phq9_feeling_bad',
  'phq9_concentration': 'phq9_concentration',
  'phq9_moving_speaking': 'phq9_moving_speaking',
  'phq9_thoughts_hurting': 'phq9_thoughts_hurting',
  'phq9_difficulty_level': 'phq9_difficulty_level',
  'phq9_notes': 'phq9_notes',

  // GAD-7 fields
  'gad7_feeling_nervous': 'gad7_feeling_nervous',
  'gad7_stop_worrying': 'gad7_stop_worrying',
  'gad7_worrying_much': 'gad7_worrying_much',
  'gad7_trouble_relaxing': 'gad7_trouble_relaxing',
  'gad7_restless': 'gad7_restless',
  'gad7_annoyed': 'gad7_annoyed',
  'gad7_feeling_afraid': 'gad7_feeling_afraid',
  'gad7_difficulty_level': 'gad7_difficulty_level',
  'gad7_notes': 'gad7_notes'
};

// Helper function to map form field names to database column names
const mapFieldToColumn = (fieldName) => {
  // If there's a mapping for this field, use it
  if (fieldName in FIELD_MAPPINGS) {
    return FIELD_MAPPINGS[fieldName];
  }
  // Otherwise, return the field name as is
  return fieldName;
};

// Define patient columns for database operations - only include columns that actually exist in the database
const PATIENT_COLUMNS = [
  // Basic Information
  'first_name', 'last_name', 'unique_id', 'date_of_birth', 'gender',
  'phone', 'email', 'address', 'doctor_id',
  'medical_history', 'medical_allergies', 'diagnosis',
  'treatment_plan', 'follow_up_instructions', 'referrals',

  // Vital Signs
  'lying_bp_systolic', 'lying_bp_diastolic', 'sitting_bp_systolic', 'sitting_bp_diastolic',
  'standing_bp_systolic', 'standing_bp_diastolic', 'lying_heart_rate', 'sitting_heart_rate', 'standing_heart_rate',
  'heart_rate', 'heart_rhythm', 'body_temperature', 'respiratory_rate', 'pulse_oximetry', 'temperature',
  'oxygen_saturation', 'height',

  // Lab Results
  'blood_glucose', 'hba1c', 'cholesterol_total', 'hdl_cholesterol', 'ldl_cholesterol',
  'triglycerides', 'creatinine', 'egfr', 'blood_urea_nitrogen', 'urine_albumin_creatinine_ratio',
  'cancer_screening_results', 'vldl', 'total_protein', 'uric_acid', 'ca125', 'iron',

  // Complete Blood Count
  'rbc', 'wbc', 'platelets', 'mcv', 'mch', 'mchc', 'rdw',
  'neutrophils', 'lymphocytes', 'monocytes', 'eosinophils', 'basophils',

  // Urine Analysis
  'urine_color', 'urine_transparency', 'urine_ph', 'urine_sugar', 'urine_pus_cells',
  'urine_rbcs', 'urine_epithelial_cells', 'urine_crystals', 'urine_casts', 'urine_protein',

  // Electrolytes
  'sodium', 'potassium', 'calcium', 'magnesium', 'phosphorus',

  // Liver Function
  'alt', 'ast', 'alp', 'bilirubin_t', 'bilirubin_d', 'albumin', 'ggt',

  // Bone Health
  'vitamin_d',

  // Thyroid Function
  'tsh', 't4', 't3', 'free_t4', 'free_t3',

  // Inflammation & Anemia
  'crp', 'esr', 'hemoglobin', 'hematocrit', 'ferritin', 'psa', 'vitamin_b12', 'folate',

  // Medication
  'current_medications', 'medication_adherence', 'medication_side_effects',
  'pill_burden',

  // Cognitive & Mental Health
  'cognitive_impairment_score', 'cognitive_test_results', 'mental_health_assessment',
  'depression_screening', 'anxiety_screening', 'depression_score', 'anxiety_score',

  // Mini-Cog Assessment
  'mini_cog_word_recall_score', 'mini_cog_clock_drawing_score',
  'mini_cog_words_used', 'mini_cog_words_recalled', 'mini_cog_notes',

  // PHQ-9 Assessment
  'phq9_interest_pleasure', 'phq9_feeling_down', 'phq9_sleep_issues',
  'phq9_tired', 'phq9_appetite', 'phq9_feeling_bad',
  'phq9_concentration', 'phq9_moving_speaking', 'phq9_thoughts_hurting',
  'phq9_difficulty_level', 'phq9_notes',

  // GAD-7 Assessment
  'gad7_feeling_nervous', 'gad7_stop_worrying', 'gad7_worrying_much',
  'gad7_trouble_relaxing', 'gad7_restless', 'gad7_annoyed',
  'gad7_feeling_afraid', 'gad7_difficulty_level', 'gad7_notes',

  // Pain Management
  'pain_level', 'pain_location', 'pain_character', 'safe_pain_medications',

  // Physical Activity & Lifestyle
  'activity_level', 'exercise_frequency', 'fall_detection_incidents',
  'mobility_aids_used', 'calf_circumference', 'grip_strength',
  'mobility_status',

  // Nutrition & Hydration
  'nutritional_status', 'dietary_intake_quality', 'hydration_status',
  'vitamin_mineral_levels', 'supplements',

  // Sleep
  'sleep_quality', 'sleep_duration', 'sleep_disturbances',
  'sleep_initiation_difficulties', 'sleep_quality_duration', 'sleep_patterns',

  // Pittsburgh Sleep Quality Index (PSQI)
  'psqi_subjective_sleep_quality', 'psqi_sleep_latency', 'psqi_sleep_duration',
  'psqi_sleep_efficiency', 'psqi_sleep_disturbances', 'psqi_sleep_medication',
  'psqi_daytime_dysfunction', 'psqi_total_score', 'psqi_assessment_date',
  'psqi_bedtime', 'psqi_minutes_to_fall_asleep', 'psqi_wake_up_time',
  'psqi_hours_of_sleep', 'psqi_notes',

  // Falls Risk Assessment Tool (FRAT)
  'frat_assessment_date', 'frat_fall_history', 'frat_fall_history_score',
  'frat_medications', 'frat_medications_score', 'frat_psychological', 'frat_psychological_score',
  'frat_cognitive', 'frat_cognitive_score', 'frat_total_score', 'frat_risk_level', 'frat_notes',


  // Sensory
  'vision_status', 'hearing_status', 'use_of_aid_vision', 'use_of_aid_hearing',

  // Social & Environmental
  'social_interaction_levels', 'living_conditions', 'living_situation',
  'age_friendly_environment', 'social_support', 'environmental_risks',
  'transportation_access', 'financial_concern', 'social_support_network', 'home_safety_evaluation',

  // Safety & Emergency
  'emergency_contact_name', 'emergency_contact_phone', 'emergency_contact_relationship',
  'emergency_contact_updated', 'sos_alerts', 'fall_risk', 'assistive_devices_used',

  // Preventive Care & Vaccinations
  'health_checkup_adherence', 'vaccination_updated', 'preventive_care_adherence',
  'influenza_vaccination_date', 'pneumococcal_vaccination_date', 'pneumococcal_booster_vaccination_date',
  'zoster_vaccination_date', 'tdap_vaccination_date', 'covid19_vaccination_date',
  'covid19_booster_date', 'hepatitis_a_vaccination_date', 'hepatitis_b_vaccination_date',
  'mmr_vaccination_date', 'varicella_vaccination_date', 'other_vaccinations',

  // Additional Health Concerns
  'urinary_bowel_issues', 'substance_abuse', 'substance_abuse_details', 'additional_health_concerns',
  'blood_type',

  // Physical measurements
  'weight', 'height', 'bmi'
];

class Patient {
  // Generate a unique patient ID
  static async generateUniqueId() {
    // Format: PXXXXX where X is alphanumeric (changed from MED-XXXXX)
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    let isUnique = false;

    while (!isUnique) {
      // Generate the ID with P prefix
      result = 'P';
      // Generate 5 alphanumeric characters
      for (let i = 0; i < 5; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
      }

      // Check if this ID already exists in the database
      try {
        const existingPatient = await pool.query(
          'SELECT patient_id FROM patients WHERE unique_id = $1',
          [result]
        );

        // If no patient found with this ID, it's unique
        if (existingPatient.rows.length === 0) {
          isUnique = true;
        }
      } catch (err) {
        console.error('Error checking unique ID:', err);
        // If there's an error, generate a new ID to be safe
        continue;
      }
    }

    return result;
  }

  // Create a new patient
  static async create(patientData) {
    try {
      // Define fields that should always be treated as strings
      const stringFields = ['phone', 'emergency_contact_phone', 'emergency_contact_name', 'unique_id'];

      // Create a copy of the patient data for processing
      let dataToProcess = { ...patientData };

      // Pre-process numeric fields to handle empty strings
      PATIENT_NUMERIC_FIELDS.forEach(field => {
        if (field in dataToProcess && dataToProcess[field] === '') {
          dataToProcess[field] = null;
        }
      });

      // Special handling for doctor_id - ensure it's a valid number or null
      if ('doctor_id' in dataToProcess) {
        if (dataToProcess.doctor_id === '' || dataToProcess.doctor_id === undefined) {
          dataToProcess.doctor_id = null;
        } else if (typeof dataToProcess.doctor_id === 'string') {
          // Try to convert to number
          const doctorIdNum = Number(dataToProcess.doctor_id);
          if (!isNaN(doctorIdNum)) {
            dataToProcess.doctor_id = doctorIdNum;
          } else {
            // If it's not a valid number, set to null
            dataToProcess.doctor_id = null;
          }
        }
      }

      // Process the patient data to handle empty strings for numeric fields and ensure string fields
      let processedData = processNumericFields(dataToProcess, PATIENT_NUMERIC_FIELDS, stringFields);

      // Generate unique_id - always generate a new one, regardless of input
      processedData.unique_id = await this.generateUniqueId();

      // Create the insert query
      const { query } = createInsertQuery('patients', PATIENT_COLUMNS);

      // Extract values in the same order as columns
      const values = PATIENT_COLUMNS.map(column => processedData[column]);

      // Execute the query
      const result = await pool.query(query, values);
      const newPatient = result.rows[0];

      // Create a user account for the patient
      try {
        // Create a username from the patient's name (remove spaces and special characters)
        const cleanFirstName = processedData.first_name.toLowerCase().replace(/[^a-z0-9]/g, '');
        const cleanLastName = processedData.last_name.toLowerCase().replace(/[^a-z0-9]/g, '');
        const username = `${cleanFirstName}_${cleanLastName}`;

        // Generate an email if not provided
        const email = processedData.email || `${username}@example.com`;

        // Create user data
        const userData = {
          username,
          email,
          role: 'patient',
          patient_id: newPatient.patient_id
        };

        // Create the user with default password
        const userResult = await User.createWithDefaultPassword(userData);

        // Update the patient record with the user_id
        await pool.query(
          'UPDATE patients SET user_id = $1 WHERE patient_id = $2',
          [userResult.user.user_id, newPatient.patient_id]
        );

        // Add user information to the patient object
        newPatient.user = {
          user_id: userResult.user.user_id,
          username: userResult.user.username,
          default_password: userResult.defaultPassword
        };
      } catch (userErr) {
        // Continue even if user creation fails
        newPatient.userError = userErr.message;
      }

      // Create an initial medical record for the patient
      try {
        const createdBy = patientData.created_by || 1; // Default to admin if not specified
        const doctorId = processedData.doctor_id || null;

        // Create the initial medical record
        const medicalRecord = await MedicalRecord.createInitialRecord(
          newPatient.patient_id,
          createdBy,
          doctorId,
          processedData
        );

        // Add medical record information to the patient object
        newPatient.medical_record = {
          record_id: medicalRecord.record_id,
          record_date: medicalRecord.record_date
        };

        // Check if there are prescriptions to add
        if (patientData.prescriptions && Array.isArray(patientData.prescriptions) && patientData.prescriptions.length > 0) {
          // Add prescriptions directly to the patient (initial visit)
          const prescriptions = await Prescription.createMany(patientData.prescriptions, {
            patientId: newPatient.patient_id,
            visitType: 'initial'
          });

          // Add prescriptions information to the patient object
          newPatient.prescriptions = prescriptions;
        }
      } catch (medicalRecordErr) {
        // Continue even if medical record creation fails
        console.error('Error creating initial medical record:', medicalRecordErr);
        newPatient.medicalRecordError = medicalRecordErr.message;
      }

      return newPatient;
    } catch (err) {
      logDbError(err, 'Patient.create()');
      throw err;
    }
  }

  // Get all patients
  static async getAll() {
    try {
      const query = `
        SELECT p.*,
          CONCAT(d.first_name, ' ', d.last_name) as doctor_name,
          u.username as last_edited_by_username
        FROM patients p
        LEFT JOIN doctors d ON p.doctor_id = d.doctor_id
        LEFT JOIN users u ON p.last_edited_by = u.user_id
        ORDER BY p.last_name, p.first_name
      `;

      const result = await pool.query(query);
      return result.rows;
    } catch (err) {
      logDbError(err, 'Patient.getAll()');
      throw err;
    }
  }

  // Get patient by ID
  static async getById(patientId) {
    try {
      const query = `
        SELECT p.*,
          CONCAT(d.first_name, ' ', d.last_name) as doctor_name,
          d.specialty as doctor_specialty,
          d.email as doctor_email,
          d.phone as doctor_phone,
          u.username as last_edited_by_username
        FROM patients p
        LEFT JOIN doctors d ON p.doctor_id = d.doctor_id
        LEFT JOIN users u ON p.last_edited_by = u.user_id
        WHERE p.patient_id = $1
      `;

      const result = await pool.query(query, [patientId]);
      return result.rows[0];
    } catch (err) {
      logDbError(err, 'Patient.getById()');
      throw err;
    }
  }

  // Update patient
  static async update(id, patientData, userId) {
    try {
      // First get the current patient data for comparison
      const currentPatient = await this.getById(id);
      if (!currentPatient) {
        throw new Error(`Patient with ID ${id} not found`);
      }

      // Pre-process numeric fields to handle empty strings
      const preprocessedData = { ...patientData };

      // Handle all numeric fields
      PATIENT_NUMERIC_FIELDS.forEach(field => {
        if (field in preprocessedData && preprocessedData[field] === '') {
          preprocessedData[field] = null;
        }
      });

      // Map form fields to database columns
      const mappedData = {};

      // Process each field in the input data
      Object.entries(preprocessedData).forEach(([field, value]) => {
        // Map the field name to a database column name
        const dbColumn = mapFieldToColumn(field);

        // Only include fields that have a corresponding database column
        if (dbColumn !== null) {
          mappedData[dbColumn] = value;
        }
      });

      // Handle special case for emergency_contact (backward compatibility)
      if (patientData.emergency_contact && !patientData.emergency_contact_name) {
        mappedData.emergency_contact_name = patientData.emergency_contact;
      }

      // Define fields that should always be treated as strings
      const stringFields = ['phone', 'emergency_contact_phone', 'emergency_contact_name', 'unique_id'];

      // Special handling for doctor_id - ensure it's a valid number or null
      if ('doctor_id' in mappedData) {
        if (mappedData.doctor_id === '' || mappedData.doctor_id === undefined) {
          mappedData.doctor_id = null;
        } else if (typeof mappedData.doctor_id === 'string') {
          // Try to convert to number
          const doctorIdNum = Number(mappedData.doctor_id);
          if (!isNaN(doctorIdNum)) {
            mappedData.doctor_id = doctorIdNum;
          } else {
            // If it's not a valid number, set to null
            mappedData.doctor_id = null;
          }
        }
      }

      // Process the mapped data to handle empty strings for numeric fields and ensure string fields
      const processedData = processNumericFields({ ...mappedData }, PATIENT_NUMERIC_FIELDS, stringFields);

      // Add the last_edited_by field
      processedData.last_edited_by = userId;

      // Create columns array with last_edited_by
      const columnsWithEdit = [...PATIENT_COLUMNS, 'last_edited_by'];

      // Filter out columns that don't exist in the database
      // This prevents errors when the form sends fields that aren't in the database
      let validColumns = [];
      let validValues = [];

      // Check which columns from our list actually have data in the request
      columnsWithEdit.forEach((column) => {
        // Only include columns that have a value in the processed data
        // This prevents "column does not exist" errors
        if (column in processedData) {
          validColumns.push(column);
          validValues.push(processedData[column]);
        }
      });

      // Final check for numeric fields - ensure no empty strings are passed to numeric columns
      const finalValidColumns = [];
      const finalValidValues = [];
      const processedColumns = new Set(); // Track columns we've already processed to avoid duplicates

      // Define all columns that must be numeric in the database
      const mustBeNumeric = [
        'lying_bp_systolic', 'lying_bp_diastolic', 'sitting_bp_systolic', 'sitting_bp_diastolic',
        'standing_bp_systolic', 'standing_bp_diastolic', 'heart_rate', 'respiratory_rate',
        'lying_heart_rate', 'sitting_heart_rate', 'standing_heart_rate',
        'blood_glucose', 'hba1c', 'cholesterol_total', 'hdl_cholesterol', 'ldl_cholesterol',
        'triglycerides', 'sodium', 'potassium', 'calcium', 'magnesium', 'creatinine',
        'weight', 'height', 'bmi', 'doctor_id', 'egfr', 'pulse_oximetry',
        'pain_level', 'health_checkup_adherence', 'pill_burden',
        'cognitive_impairment_score', 'depression_score', 'anxiety_score',
        'calf_circumference', 'grip_strength', 'sleep_quality_duration', 'sleep_duration',
        'vldl', 'total_protein', 'uric_acid', 'ca125', 'iron',
        'rbc', 'wbc', 'platelets', 'mcv', 'mch', 'mchc', 'rdw',
        'neutrophils', 'lymphocytes', 'monocytes', 'eosinophils', 'basophils',
        'alt', 'ast', 'alp', 'ggt', 'bilirubin_t', 'bilirubin_d', 'albumin',
        'vitamin_d', 'tsh', 't4', 't3', 'free_t4', 'free_t3',
        'crp', 'esr', 'ferritin', 'psa', 'vitamin_b12', 'folate',
        'urine_ph', // Only urine_ph is numeric (DECIMAL), the other urine fields are VARCHAR

        // PSQI numeric fields
        'psqi_subjective_sleep_quality', 'psqi_sleep_latency', 'psqi_sleep_duration',
        'psqi_sleep_efficiency', 'psqi_sleep_disturbances', 'psqi_sleep_medication',
        'psqi_daytime_dysfunction', 'psqi_total_score', 'psqi_hours_of_sleep',
        'psqi_minutes_to_fall_asleep',

        // FRAT numeric fields
        'frat_fall_history', 'frat_fall_history_score',
        'frat_medications', 'frat_medications_score',
        'frat_psychological', 'frat_psychological_score',
        'frat_cognitive', 'frat_cognitive_score',
        'frat_total_score',

        // Mini-Cog numeric fields
        'mini_cog_word_recall_score', 'mini_cog_clock_drawing_score',

        // PHQ-9 numeric fields
        'phq9_interest_pleasure', 'phq9_feeling_down', 'phq9_sleep_issues',
        'phq9_tired', 'phq9_appetite', 'phq9_feeling_bad',
        'phq9_concentration', 'phq9_moving_speaking', 'phq9_thoughts_hurting',
        'phq9_difficulty_level',

        // GAD-7 numeric fields
        'gad7_feeling_nervous', 'gad7_stop_worrying', 'gad7_worrying_much',
        'gad7_trouble_relaxing', 'gad7_restless', 'gad7_annoyed',
        'gad7_feeling_afraid', 'gad7_difficulty_level'
      ];

      // Define date fields that need special handling
      const dateFields = [
        'influenza_vaccination_date', 'pneumococcal_vaccination_date', 'pneumococcal_booster_vaccination_date',
        'zoster_vaccination_date', 'tdap_vaccination_date', 'covid19_vaccination_date',
        'covid19_booster_date', 'hepatitis_a_vaccination_date', 'hepatitis_b_vaccination_date',
        'mmr_vaccination_date', 'varicella_vaccination_date', 'psqi_assessment_date',
        'frat_assessment_date'
      ];

      // Define time fields that need special handling
      const timeFields = [
        'psqi_bedtime', 'psqi_wake_up_time'
      ];

      // Check each column/value pair
      for (let i = 0; i < validColumns.length; i++) {
        const column = validColumns[i];
        let value = validValues[i];

        // Skip if we've already processed this column
        if (processedColumns.has(column)) {
          continue;
        }

        // If this is a date field and the value is an empty string, set to null
        if (dateFields.includes(column) && (value === '' || value === undefined)) {
          value = null;
        }

        // If this is a time field and the value is an empty string, set to null
        if (timeFields.includes(column) && (value === '' || value === undefined)) {
          value = null;
        }

        // If this is a numeric column and the value is an empty string, skip it
        if (mustBeNumeric.includes(column) && (value === '' || value === undefined)) {
          continue;
        }

        // If this is a numeric column, ensure it's actually a number or null
        if (mustBeNumeric.includes(column) && value !== null) {
          if (typeof value === 'string') {
            // Try to convert to number
            const numValue = Number(value);
            if (!isNaN(numValue)) {
              value = numValue;
            } else {
              // If it's not a valid number, skip this column
              continue;
            }
          }
        }

        // Add to final arrays and mark as processed
        finalValidColumns.push(column);
        finalValidValues.push(value);
        processedColumns.add(column);
      }

      // Create update query with only valid columns
      const updateColumns = finalValidColumns.map((col, i) => `${col} = $${i + 1}`);
      const query = `
        UPDATE patients
        SET
          ${updateColumns.join(', ')},
          last_edited_at = CURRENT_TIMESTAMP
        WHERE patient_id = $${finalValidColumns.length + 1}
        RETURNING *
      `;

      // Add the ID as the last parameter
      finalValidValues.push(id);

      // Replace the original arrays with the filtered ones
      validColumns = finalValidColumns;
      validValues = finalValidValues;

      // Execute the query
      let result;
      try {

        result = await pool.query(query, validValues);

        if (result.rows.length === 0) {
          throw new Error(`Update failed: No rows returned for patient ID ${id}`);
        }
      } catch (queryErr) {

        // Rethrow with more details
        throw new Error(`Database error: ${queryErr.message} (Code: ${queryErr.code}, Column: ${queryErr.column || 'unknown'})`);
      }

      const updatedPatient = result.rows[0];

      // Log the changes
      const editLogs = [];

      // Compare old and new values for important fields
      const fieldsToCheck = [
        'first_name', 'last_name', 'unique_id', 'date_of_birth', 'gender',
        'phone', 'email', 'address', 'doctor_id'
      ];

      // Add additional fields to check based on what was in the request
      const additionalFields = Object.keys(processedData).filter(field =>
        !fieldsToCheck.includes(field) &&
        field !== 'last_edited_by' &&
        field in currentPatient
      );

      const allFieldsToCheck = [...fieldsToCheck, ...additionalFields];

      allFieldsToCheck.forEach(field => {
        // Convert values to strings for comparison to handle different types
        const oldValue = currentPatient[field] !== null ? String(currentPatient[field]) : null;
        const newValue = updatedPatient[field] !== null ? String(updatedPatient[field]) : null;

        if (oldValue !== newValue) {
          editLogs.push({
            patientId: id,
            userId,
            fieldChanged: field,
            oldValue: currentPatient[field],
            newValue: updatedPatient[field]
          });
        }
      });

      // If there are changes, log them
      if (editLogs.length > 0) {
        await PatientEditLog.createBatch(editLogs);
      }

      // Handle prescriptions if they exist in the patientData
      if (patientData.prescriptions && Array.isArray(patientData.prescriptions)) {
        try {
          console.log(`Processing prescriptions for patient ID: ${id}`);

          // Get existing prescriptions for this patient
          const existingPrescriptions = await Prescription.getByPatientId(id);
          console.log(`Found ${existingPrescriptions.length} existing prescriptions`);

          // Create a map of existing prescription IDs for quick lookup
          const existingPrescriptionMap = {};
          existingPrescriptions.forEach(p => {
            existingPrescriptionMap[p.prescription_id] = p;
          });

          // Track which existing prescriptions should be kept
          const keptPrescriptionIds = new Set();

          // Process each prescription in the form data
          const processedPrescriptions = [];

          for (const prescription of patientData.prescriptions) {
            if (prescription.id) {
              // This is an existing prescription - keep it
              keptPrescriptionIds.add(prescription.id);
              processedPrescriptions.push(existingPrescriptionMap[prescription.id]);
            } else {
              // This is a new prescription - create it
              console.log(`Creating new prescription: ${prescription.medication}`);
              const newPrescription = await Prescription.create({
                patient_id: id,
                visit_type: 'initial',
                medication: prescription.medication,
                dosage: prescription.dosage,
                frequency: prescription.frequency,
                duration: prescription.duration,
                notes: prescription.notes
              });
              processedPrescriptions.push(newPrescription);
            }
          }

          // Delete prescriptions that weren't kept
          for (const existingPrescription of existingPrescriptions) {
            if (!keptPrescriptionIds.has(existingPrescription.prescription_id)) {
              console.log(`Deleting prescription ID: ${existingPrescription.prescription_id}`);
              await Prescription.delete(existingPrescription.prescription_id);
            }
          }

          // Add prescriptions to the response
          updatedPatient.prescriptions = processedPrescriptions;
          console.log(`Patient now has ${processedPrescriptions.length} prescriptions`);

        } catch (prescriptionErr) {
          console.error('Error processing prescriptions during patient update:', prescriptionErr);
        }
      }

      return updatedPatient;
    } catch (err) {
      logDbError(err, 'Patient.update()');
      throw err;
    }
  }

  // Delete patient
  static async delete(patientId, userId) {
    try {
      // First, get the patient data for logging purposes
      const patientQuery = 'SELECT * FROM patients WHERE patient_id = $1';
      const patientResult = await pool.query(patientQuery, [patientId]);

      if (patientResult.rows.length === 0) {
        throw new Error(`Patient with ID ${patientId} not found`);
      }

      const patientData = patientResult.rows[0];

      // Log the deletion in the system_logs table
      const SystemLog = require('./SystemLog');
      try {
        // Create a system log entry for the patient deletion
        await SystemLog.create({
          log_type: 'patient_deletion',
          user_id: userId,
          related_id: patientId,
          related_name: `${patientData.first_name} ${patientData.last_name}`,
          details: {
            patient_id: patientId,
            unique_id: patientData.unique_id,
            first_name: patientData.first_name,
            last_name: patientData.last_name,
            date_of_birth: patientData.date_of_birth,
            gender: patientData.gender,
            email: patientData.email,
            phone: patientData.phone,
            doctor_id: patientData.doctor_id
          }
        });

      } catch (logErr) {
        // Continue with deletion even if logging fails
      }

      // Now delete the patient
      const deleteQuery = 'DELETE FROM patients WHERE patient_id = $1 RETURNING patient_id';
      const deleteResult = await pool.query(deleteQuery, [patientId]);

      if (deleteResult.rowCount === 0) {
        throw new Error(`Patient with ID ${patientId} not found or already deleted`);
      }

      return { success: true, patientId, patientName: `${patientData.first_name} ${patientData.last_name}` };
    } catch (err) {
      logDbError(err, 'Patient.delete()');
      throw err;
    }
  }

  // Search for patients
  static async search(searchTerm) {
    try {
      // Sanitize the search term to prevent SQL injection
      const sanitizedTerm = searchTerm.replace(/[%_]/g, char => `\\${char}`);
      const searchPattern = `%${sanitizedTerm}%`;

      const query = `
        SELECT p.*,
          CONCAT(d.first_name, ' ', d.last_name) as doctor_name,
          u.username as last_edited_by_username
        FROM patients p
        LEFT JOIN doctors d ON p.doctor_id = d.doctor_id
        LEFT JOIN users u ON p.last_edited_by = u.user_id
        WHERE
          LOWER(p.first_name) LIKE LOWER($1) OR
          LOWER(p.last_name) LIKE LOWER($1) OR
          LOWER(p.unique_id) LIKE LOWER($1) OR
          LOWER(p.email) LIKE LOWER($1) OR
          CONCAT(LOWER(p.first_name), ' ', LOWER(p.last_name)) LIKE LOWER($1)
        ORDER BY p.last_name, p.first_name
        LIMIT 100
      `;

      const result = await pool.query(query, [searchPattern]);
      return result.rows;
    } catch (err) {
      logDbError(err, 'Patient.search()');
      throw err;
    }
  }
}

module.exports = Patient;