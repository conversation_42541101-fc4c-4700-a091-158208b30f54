const pool = require('../db');

class Doctor {
  // Create a new doctor
  static async create(doctorData) {
    try {
      const { first_name, last_name, specialty, email, phone } = doctorData;

      const result = await pool.query(
        `INSERT INTO doctors (first_name, last_name, specialty, email, phone)
        VALUES ($1, $2, $3, $4, $5)
        RETURNING *`,
        [first_name, last_name, specialty, email, phone]
      );

      return result.rows[0];
    } catch (err) {
      console.error('Error in Doctor.create():', err);
      throw err;
    }
  }

  // Get all doctors
  static async getAll() {
    try {
      const result = await pool.query(
        `SELECT * FROM doctors
        ORDER BY last_name, first_name`
      );

      return result.rows;
    } catch (err) {
      console.error('Error in Doctor.getAll():', err);
      throw err;
    }
  }

  // Get doctor by ID
  static async getById(doctorId) {
    try {
      const result = await pool.query(
        `SELECT * FROM doctors
        WHERE doctor_id = $1`,
        [doctorId]
      );

      return result.rows[0];
    } catch (err) {
      console.error('Error in Doctor.getById():', err);
      throw err;
    }
  }

  // Update doctor
  static async update(doctorId, doctorData) {
    try {
      const { first_name, last_name, specialty, email, phone } = doctorData;

      const result = await pool.query(
        `UPDATE doctors
        SET first_name = $1, last_name = $2, specialty = $3, email = $4, phone = $5
        WHERE doctor_id = $6
        RETURNING *`,
        [first_name, last_name, specialty, email, phone, doctorId]
      );

      if (result.rows.length === 0) {
        throw new Error(`Doctor with ID ${doctorId} not found`);
      }

      return result.rows[0];
    } catch (err) {
      console.error('Error in Doctor.update():', err);
      throw err;
    }
  }

  // Delete doctor
  static async delete(doctorId) {
    try {
      const result = await pool.query(
        `DELETE FROM doctors WHERE doctor_id = $1 RETURNING *`,
        [doctorId]
      );

      if (result.rows.length === 0) {
        throw new Error(`Doctor with ID ${doctorId} not found`);
      }

      return { success: true };
    } catch (err) {
      console.error('Error in Doctor.delete():', err);
      throw err;
    }
  }

  // Get patients by doctor ID
  static async getPatients(doctorId) {
    try {
      // Get patients with visit information and doctor name
      const result = await pool.query(
        `SELECT p.*,
          (SELECT COUNT(*) FROM patient_visits v WHERE v.patient_id = p.patient_id)::integer as visit_count,
          (SELECT MAX(visit_date) FROM patient_visits v WHERE v.patient_id = p.patient_id) as last_visit_date,
          CONCAT(d.first_name, ' ', d.last_name) as doctor_name
        FROM patients p
        LEFT JOIN doctors d ON p.doctor_id = d.doctor_id
        WHERE p.doctor_id = $1
        ORDER BY p.last_name, p.first_name`,
        [doctorId]
      );

      console.log('Fetched patients with visit information for doctor:', doctorId);

      // Debug: Log each patient's visit information
      result.rows.forEach(patient => {
        // Convert visit_count to a number to ensure proper comparison
        patient.visit_count = parseInt(patient.visit_count || 0, 10);

        console.log(`Patient ${patient.first_name} ${patient.last_name} (ID: ${patient.patient_id}):`, {
          visit_count: patient.visit_count,
          visit_count_type: typeof patient.visit_count,
          last_visit_date: patient.last_visit_date,
          doctor_name: patient.doctor_name
        });

        // Check if there are actually visits for this patient
        pool.query(
          `SELECT COUNT(*)::integer FROM patient_visits WHERE patient_id = $1`,
          [patient.patient_id]
        ).then(visitResult => {
          const directCount = parseInt(visitResult.rows[0].count, 10);
          console.log(`Direct count of visits for patient ${patient.patient_id}:`, directCount,
            `(type: ${typeof directCount})`);
        }).catch(err => {
          console.error(`Error checking visits for patient ${patient.patient_id}:`, err);
        });
      });

      return result.rows;
    } catch (err) {
      console.error('Error in Doctor.getPatients():', err);
      throw err;
    }
  }
}

module.exports = Doctor;