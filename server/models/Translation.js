const pool = require('../db');

class Translation {
  // Get all translations for a specific language
  static async getByLanguage(languageCode) {
    try {
      const result = await pool.query(`
        SELECT 
          translation_id, 
          language_code, 
          key, 
          value, 
          context,
          created_at,
          updated_at
        FROM translations
        WHERE language_code = $1
        ORDER BY key ASC
      `, [languageCode]);

      return result.rows;
    } catch (err) {
      console.error(`Error in Translation.getByLanguage(${languageCode}):`, err.message);
      throw err;
    }
  }

  // Get a specific translation by ID
  static async getById(translationId) {
    try {
      const result = await pool.query(`
        SELECT 
          translation_id, 
          language_code, 
          key, 
          value, 
          context,
          created_at,
          updated_at
        FROM translations
        WHERE translation_id = $1
      `, [translationId]);

      if (result.rows.length === 0) {
        throw new Error(`Translation with ID ${translationId} not found`);
      }

      return result.rows[0];
    } catch (err) {
      console.error(`Error in Translation.getById(${translationId}):`, err.message);
      throw err;
    }
  }

  // Get a specific translation by key and language
  static async getByKeyAndLanguage(key, languageCode) {
    try {
      const result = await pool.query(`
        SELECT 
          translation_id, 
          language_code, 
          key, 
          value, 
          context,
          created_at,
          updated_at
        FROM translations
        WHERE key = $1 AND language_code = $2
      `, [key, languageCode]);

      if (result.rows.length === 0) {
        return null;
      }

      return result.rows[0];
    } catch (err) {
      console.error(`Error in Translation.getByKeyAndLanguage(${key}, ${languageCode}):`, err.message);
      throw err;
    }
  }

  // Create a new translation
  static async create(translationData, userId) {
    try {
      const { language_code, key, value, context } = translationData;

      // Check if translation already exists
      const existingTranslation = await this.getByKeyAndLanguage(key, language_code);
      if (existingTranslation) {
        throw new Error(`Translation for key '${key}' in language '${language_code}' already exists`);
      }

      const result = await pool.query(`
        INSERT INTO translations (
          language_code, 
          key, 
          value, 
          context,
          created_by,
          updated_by
        ) VALUES ($1, $2, $3, $4, $5, $5)
        RETURNING *
      `, [
        language_code,
        key,
        value,
        context || null,
        userId
      ]);

      return result.rows[0];
    } catch (err) {
      console.error('Error in Translation.create():', err.message);
      throw err;
    }
  }

  // Update an existing translation
  static async update(translationId, translationData, userId) {
    try {
      const { language_code, key, value, context } = translationData;

      const result = await pool.query(`
        UPDATE translations
        SET 
          language_code = $1,
          key = $2,
          value = $3,
          context = $4,
          updated_at = NOW(),
          updated_by = $5
        WHERE translation_id = $6
        RETURNING *
      `, [
        language_code,
        key,
        value,
        context || null,
        userId,
        translationId
      ]);

      if (result.rows.length === 0) {
        throw new Error(`Translation with ID ${translationId} not found`);
      }

      return result.rows[0];
    } catch (err) {
      console.error(`Error in Translation.update(${translationId}):`, err.message);
      throw err;
    }
  }

  // Delete a translation
  static async delete(translationId) {
    try {
      const result = await pool.query(`
        DELETE FROM translations
        WHERE translation_id = $1
        RETURNING *
      `, [translationId]);

      if (result.rows.length === 0) {
        throw new Error(`Translation with ID ${translationId} not found`);
      }

      return result.rows[0];
    } catch (err) {
      console.error(`Error in Translation.delete(${translationId}):`, err.message);
      throw err;
    }
  }

  // Import translations from a JSON object
  static async importFromJSON(languageCode, translations, userId) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      const results = [];
      for (const [key, value] of Object.entries(translations)) {
        // Check if translation already exists
        const existingTranslation = await this.getByKeyAndLanguage(key, languageCode);
        
        if (existingTranslation) {
          // Update existing translation
          const result = await client.query(`
            UPDATE translations
            SET 
              value = $1,
              updated_at = NOW(),
              updated_by = $2
            WHERE key = $3 AND language_code = $4
            RETURNING *
          `, [
            value,
            userId,
            key,
            languageCode
          ]);
          
          results.push(result.rows[0]);
        } else {
          // Create new translation
          const result = await client.query(`
            INSERT INTO translations (
              language_code, 
              key, 
              value, 
              created_by,
              updated_by
            ) VALUES ($1, $2, $3, $4, $4)
            RETURNING *
          `, [
            languageCode,
            key,
            value,
            userId
          ]);
          
          results.push(result.rows[0]);
        }
      }

      await client.query('COMMIT');
      return results;
    } catch (err) {
      await client.query('ROLLBACK');
      console.error(`Error in Translation.importFromJSON(${languageCode}):`, err.message);
      throw err;
    } finally {
      client.release();
    }
  }

  // Export translations to a JSON object
  static async exportToJSON(languageCode) {
    try {
      const translations = await this.getByLanguage(languageCode);
      
      const result = {};
      for (const translation of translations) {
        result[translation.key] = translation.value;
      }
      
      return result;
    } catch (err) {
      console.error(`Error in Translation.exportToJSON(${languageCode}):`, err.message);
      throw err;
    }
  }

  // Get all available languages
  static async getLanguages() {
    try {
      const result = await pool.query(`
        SELECT DISTINCT language_code
        FROM translations
        ORDER BY language_code
      `);

      return result.rows.map(row => row.language_code);
    } catch (err) {
      console.error('Error in Translation.getLanguages():', err.message);
      throw err;
    }
  }
}

module.exports = Translation;
