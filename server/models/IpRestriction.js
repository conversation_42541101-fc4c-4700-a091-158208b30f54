const pool = require('../db');
const SystemSettings = require('./SystemSettings');

class IpRestriction {
  // Get all IP restrictions
  static async getAll() {
    try {
      const result = await pool.query(
        'SELECT * FROM ip_restrictions ORDER BY created_at DESC'
      );
      return result.rows;
    } catch (err) {
      console.error('Error in IpRestriction.getAll():', err.message);
      throw err;
    }
  }

  // Get IP restriction by ID
  static async getById(restrictionId) {
    try {
      const result = await pool.query(
        'SELECT * FROM ip_restrictions WHERE restriction_id = $1',
        [restrictionId]
      );
      return result.rows[0];
    } catch (err) {
      console.error('Error in IpRestriction.getById():', err.message);
      throw err;
    }
  }

  // Create a new IP restriction
  static async create(ipData, userId) {
    const { ip_address, description, is_allowed } = ipData;

    try {
      const result = await pool.query(
        `INSERT INTO ip_restrictions 
        (ip_address, description, is_allowed, created_by, updated_by) 
        VALUES ($1, $2, $3, $4, $5) 
        RETURNING *`,
        [ip_address, description, is_allowed, userId, userId]
      );
      return result.rows[0];
    } catch (err) {
      console.error('Error in IpRestriction.create():', err.message);
      throw err;
    }
  }

  // Update an IP restriction
  static async update(restrictionId, ipData, userId) {
    const { ip_address, description, is_allowed } = ipData;

    try {
      const result = await pool.query(
        `UPDATE ip_restrictions 
        SET ip_address = $1, 
            description = $2, 
            is_allowed = $3, 
            updated_at = NOW(), 
            updated_by = $4 
        WHERE restriction_id = $5 
        RETURNING *`,
        [ip_address, description, is_allowed, userId, restrictionId]
      );
      return result.rows[0];
    } catch (err) {
      console.error('Error in IpRestriction.update():', err.message);
      throw err;
    }
  }

  // Delete an IP restriction
  static async delete(restrictionId) {
    try {
      const result = await pool.query(
        'DELETE FROM ip_restrictions WHERE restriction_id = $1 RETURNING *',
        [restrictionId]
      );
      return result.rows[0];
    } catch (err) {
      console.error('Error in IpRestriction.delete():', err.message);
      throw err;
    }
  }

  // Check if an IP address is allowed
  static async isIpAllowed(ipAddress) {
    try {
      // First check if IP restriction is enabled in system settings
      const settings = await SystemSettings.getCurrent();
      
      // If IP restriction is not enabled, allow all IPs
      if (!settings.ip_restriction) {
        return true;
      }

      // Check if the IP is in the restrictions table
      const result = await pool.query(
        'SELECT is_allowed FROM ip_restrictions WHERE ip_address = $1',
        [ipAddress]
      );

      // If IP is not in the table, deny access (whitelist approach)
      if (result.rows.length === 0) {
        return false;
      }

      // Return whether the IP is allowed
      return result.rows[0].is_allowed;
    } catch (err) {
      console.error('Error in IpRestriction.isIpAllowed():', err.message);
      // In case of error, allow access to prevent lockouts
      return true;
    }
  }

  // Import a list of IP restrictions
  static async importList(ipList, userId) {
    const client = await pool.connect();
    try {
      await client.query('BEGIN');

      for (const ip of ipList) {
        await client.query(
          `INSERT INTO ip_restrictions 
          (ip_address, description, is_allowed, created_by, updated_by) 
          VALUES ($1, $2, $3, $4, $5) 
          ON CONFLICT (ip_address) 
          DO UPDATE SET 
            description = $2, 
            is_allowed = $3, 
            updated_at = NOW(), 
            updated_by = $4`,
          [ip.ip_address, ip.description, ip.is_allowed, userId, userId]
        );
      }

      await client.query('COMMIT');
      return { success: true, message: `Imported ${ipList.length} IP restrictions` };
    } catch (err) {
      await client.query('ROLLBACK');
      console.error('Error in IpRestriction.importList():', err.message);
      throw err;
    } finally {
      client.release();
    }
  }

  // Check if the IP restrictions table exists
  static async tableExists() {
    try {
      const result = await pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_name = 'ip_restrictions'
        ) as table_exists
      `);
      return result.rows[0].table_exists;
    } catch (err) {
      console.error('Error checking if ip_restrictions table exists:', err.message);
      return false;
    }
  }
}

module.exports = IpRestriction;
