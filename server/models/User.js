const pool = require('../db');
const bcrypt = require('bcryptjs');
const PasswordPolicy = require('./PasswordPolicy');

class User {
  // Register a user
  static async register(username, email, password, role = 'user', doctor_id = null, patient_id = null, useDefaultPassword = true, relationship_type = null) {
    try {
      // If useDefaultPassword is true, generate a default password based on policy
      let finalPassword = password;
      let isDefaultPassword = useDefaultPassword;

      if (useDefaultPassword) {
        finalPassword = await PasswordPolicy.generateDefaultPassword(username);
      }

      // Hash password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(finalPassword, salt);

      // Validate role
      const validRoles = ['admin', 'doctor', 'staff', 'patient', 'kin', 'researcher', 'user'];
      if (!validRoles.includes(role)) {
        throw new Error(`Invalid role: ${role}. Must be one of: ${validRoles.join(', ')}`);
      }

      // Set current time for password_last_changed
      const now = new Date();

      // Insert user into database with role-specific IDs if provided
      let query, params;

      if (role === 'doctor' && doctor_id) {
        query = `INSERT INTO users (
          username, email, password, role, doctor_id,
          is_first_login, default_password, password_last_changed
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *`;
        params = [username, email, hashedPassword, role, doctor_id, true, isDefaultPassword, now];
      } else if (role === 'patient' && patient_id) {
        query = `INSERT INTO users (
          username, email, password, role, patient_id,
          is_first_login, default_password, password_last_changed
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *`;
        params = [username, email, hashedPassword, role, patient_id, true, isDefaultPassword, now];
      } else if (role === 'kin' && patient_id) {
        // For kin users, we need to store the related patient_id
        query = `INSERT INTO users (
          username, email, password, role, patient_id,
          is_first_login, default_password, password_last_changed
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8) RETURNING *`;
        params = [username, email, hashedPassword, role, patient_id, true, isDefaultPassword, now];
        console.log('Creating kin user linked to patient ID:', patient_id);

        // If relationship_type is provided, add it to the kin_patient_relationships table
        if (relationship_type) {
          console.log('Adding relationship type:', relationship_type);
          // We'll add this relationship after the user is created
        }
      } else {
        query = `INSERT INTO users (
          username, email, password, role,
          is_first_login, default_password, password_last_changed
        ) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *`;
        params = [username, email, hashedPassword, role, true, isDefaultPassword, now];
      }

      const result = await pool.query(query, params);
      const createdUser = result.rows[0];

      // If this is a kin user and relationship_type is provided, add to kin_patient_relationships table
      if (role === 'kin' && patient_id && relationship_type && createdUser) {
        try {
          console.log(`Adding relationship: kin_user_id=${createdUser.user_id}, patient_id=${patient_id}, relationship_type=${relationship_type}`);

          // Insert into kin_patient_relationships table
          await pool.query(
            `INSERT INTO kin_patient_relationships
             (kin_user_id, patient_id, relationship_type, is_primary)
             VALUES ($1, $2, $3, $4)
             ON CONFLICT (kin_user_id, patient_id)
             DO UPDATE SET relationship_type = $3, is_primary = $4`,
            [createdUser.user_id, patient_id, relationship_type, true]
          );

          console.log('Relationship added successfully');
        } catch (relationshipErr) {
          console.error('Error adding relationship:', relationshipErr);
          // Continue even if relationship creation fails
        }
      }

      // Return the created user and the plaintext password if it's a default password
      return {
        user: createdUser,
        defaultPassword: isDefaultPassword ? finalPassword : null
      };
    } catch (err) {
      console.error('Error in User.register():', err.message);
      throw err;
    }
  }

  // Find user by email
  static async findByEmail(email) {
    try {
      const result = await pool.query('SELECT * FROM users WHERE email = $1', [email]);
      return result.rows[0];
    } catch (err) {
      throw err;
    }
  }

  // Find user by username
  static async findByUsername(username) {
    try {
      const result = await pool.query('SELECT * FROM users WHERE username = $1', [username]);
      return result.rows[0];
    } catch (err) {
      throw err;
    }
  }

  // Find user by ID
  static async findById(userId) {
    try {
      const result = await pool.query('SELECT * FROM users WHERE user_id = $1', [userId]);
      return result.rows[0];
    } catch (err) {
      throw err;
    }
  }

  // Update user
  static async update(userId, userData) {
    try {
      // If password is included, hash it
      if (userData.password) {
        const salt = await bcrypt.genSalt(10);
        userData.password = await bcrypt.hash(userData.password, salt);
      }

      // Build query dynamically based on provided fields
      const fields = Object.keys(userData);
      const values = fields.map(field => userData[field]);

      // Add userId as the last value
      values.push(userId);

      // Create set clause for SQL query
      const setClause = fields.map((field, index) => `${field} = $${index + 1}`).join(', ');

      // Execute update query
      const query = `UPDATE users SET ${setClause} WHERE user_id = $${values.length} RETURNING *`;
      const result = await pool.query(query, values);

      if (result.rows.length === 0) {
        throw new Error(`User with ID ${userId} not found`);
      }

      return result.rows[0];
    } catch (err) {
      throw err;
    }
  }

  // Compare password
  static async comparePassword(password, hashedPassword) {
    try {
      return await bcrypt.compare(password, hashedPassword);
    } catch (error) {
      console.error('Error comparing passwords:', error);
      return false;
    }
  }

  // Lock a user account - only an admin can unlock it
  static async lockAccount(userId) {
    try {
      console.log(`Locking account for user ID: ${userId}`);

      const result = await pool.query(
        'UPDATE users SET is_locked = true, lockout_until = NULL WHERE user_id = $1 RETURNING *',
        [userId]
      );

      if (result.rows.length === 0) {
        throw new Error('User not found');
      }

      console.log(`Account locked successfully for user ID: ${userId}`);
      return result.rows[0];
    } catch (err) {
      console.error(`Error locking account for user ID: ${userId}`, err);
      throw err;
    }
  }

  // Check if account is locked
  static async isAccountLocked(userId) {
    try {
      const result = await pool.query(
        'SELECT is_locked FROM users WHERE user_id = $1',
        [userId]
      );

      if (result.rows.length === 0) {
        throw new Error('User not found');
      }

      const user = result.rows[0];
      return user.is_locked;
    } catch (err) {
      throw err;
    }
  }

  // Unlock a user account
  static async unlockAccount(userId) {
    try {
      const result = await pool.query(
        'UPDATE users SET is_locked = false, failed_login_attempts = 0, lockout_until = NULL WHERE user_id = $1 RETURNING *',
        [userId]
      );

      if (result.rows.length === 0) {
        throw new Error('User not found');
      }

      return result.rows[0];
    } catch (err) {
      throw err;
    }
  }

  // Track login attempts and auto-lock if too many failures based on password policy
  static async incrementLoginAttempts(userId) {
    try {
      console.log(`Incrementing failed login attempts for user ID: ${userId}`);

      const result = await pool.query(
        'UPDATE users SET failed_login_attempts = failed_login_attempts + 1 WHERE user_id = $1 RETURNING failed_login_attempts, is_locked',
        [userId]
      );

      if (result.rows.length === 0) {
        throw new Error('User not found');
      }

      const failedAttempts = result.rows[0].failed_login_attempts;
      let isLocked = result.rows[0].is_locked;

      console.log(`Current failed attempts: ${failedAttempts}, is_locked: ${isLocked}`);

      // Get the current password policy to check max_failed_attempts
      const policy = await PasswordPolicy.getCurrent();
      const maxAttempts = policy.max_failed_attempts;

      console.log(`Password policy max_failed_attempts: ${maxAttempts}`);

      // Auto-lock account after max failed attempts
      if (failedAttempts >= maxAttempts && !isLocked) {
        console.log(`Failed attempts (${failedAttempts}) >= max attempts (${maxAttempts}). Locking account.`);
        await this.lockAccount(userId);
        isLocked = true;
      }

      return {
        failedAttempts,
        maxAttempts,
        isLocked: isLocked || failedAttempts >= maxAttempts
      };
    } catch (err) {
      console.error(`Error incrementing login attempts for user ID: ${userId}`, err);
      throw err;
    }
  }

  // Reset login attempts after successful login
  static async resetLoginAttempts(userId) {
    try {
      const result = await pool.query(
        'UPDATE users SET failed_login_attempts = 0, last_login = NOW() WHERE user_id = $1 RETURNING *',
        [userId]
      );

      if (result.rows.length === 0) {
        throw new Error('User not found');
      }

      return result.rows[0];
    } catch (err) {
      throw err;
    }
  }

  // Get all users (for admin purposes)
  static async getAllUsers() {
    try {
      const result = await pool.query(
        'SELECT user_id, username, email, role, is_locked, created_at, last_login, is_first_login, default_password FROM users ORDER BY created_at DESC'
      );

      return result.rows;
    } catch (err) {
      throw err;
    }
  }

  // Check if user needs to change password (first login or expired password)
  static async needsPasswordChange(userId) {
    try {
      // Get the user and password policy
      const userResult = await pool.query(
        'SELECT is_first_login, password_last_changed, default_password FROM users WHERE user_id = $1',
        [userId]
      );

      if (userResult.rows.length === 0) {
        throw new Error('User not found');
      }

      const user = userResult.rows[0];
      const policy = await PasswordPolicy.getCurrent();

      // Check if this is the first login
      if (user.is_first_login) {
        return {
          required: true,
          reason: 'first_login'
        };
      }

      // Check if using default password
      if (user.default_password) {
        return {
          required: true,
          reason: 'default_password'
        };
      }

      // Check if password has expired
      if (policy.password_expiry_days > 0 && user.password_last_changed) {
        const expiryDate = new Date(user.password_last_changed);
        expiryDate.setDate(expiryDate.getDate() + policy.password_expiry_days);

        if (new Date() > expiryDate) {
          return {
            required: true,
            reason: 'password_expired',
            expiryDate
          };
        }
      }

      return {
        required: false
      };
    } catch (err) {
      console.error('Error in User.needsPasswordChange():', err.message);
      throw err;
    }
  }

  // Change user password
  static async changePassword(userId, newPassword) {
    try {
      // Get current password policy
      const policy = await PasswordPolicy.getCurrent();

      // Get current user data
      const userResult = await pool.query(
        'SELECT password, password_history FROM users WHERE user_id = $1',
        [userId]
      );

      if (userResult.rows.length === 0) {
        throw new Error('User not found');
      }

      const user = userResult.rows[0];

      // Hash the new password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(newPassword, salt);

      // Prepare password history array
      let passwordHistory = user.password_history || [];

      // Add current password to history
      if (user.password) {
        passwordHistory.push(user.password);
      }

      // Keep only the most recent passwords according to policy
      if (passwordHistory.length > policy.password_history_count) {
        passwordHistory = passwordHistory.slice(-policy.password_history_count);
      }

      // Update user with new password and mark first login as completed
      const result = await pool.query(
        `UPDATE users SET
          password = $1,
          is_first_login = false,
          default_password = false,
          password_last_changed = NOW(),
          password_history = $2
        WHERE user_id = $3 RETURNING *`,
        [hashedPassword, passwordHistory, userId]
      );

      return result.rows[0];
    } catch (err) {
      console.error('Error in User.changePassword():', err.message);
      throw err;
    }
  }

  // Create user with default password
  static async createWithDefaultPassword(userData) {
    try {
      console.log('User.createWithDefaultPassword called with userData:', JSON.stringify(userData));
      const { username, email, role, doctor_id, patient_id, relationship_type } = userData;

      console.log('Generating default password for username:', username);
      // Generate default password based on policy
      const defaultPassword = await PasswordPolicy.generateDefaultPassword(username);
      console.log('Default password generated successfully');

      // Register user with default password
      console.log('Calling User.register with role:', role, 'doctor_id:', doctor_id, 'patient_id:', patient_id, 'relationship_type:', relationship_type);
      const result = await this.register(username, email, defaultPassword, role, doctor_id, patient_id, true, relationship_type);
      console.log('User.register completed successfully');

      return result;
    } catch (err) {
      console.error('Error in User.createWithDefaultPassword():', err.message);
      console.error('Error stack:', err.stack);
      throw err;
    }
  }
}

module.exports = User;