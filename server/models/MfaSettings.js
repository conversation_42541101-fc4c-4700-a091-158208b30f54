const pool = require('../db');

class MfaSettings {
  // Get all MFA settings
  static async getAll() {
    try {
      // Check if the table exists first
      const tableCheck = await pool.query(`
        SELECT EXISTS (
          SELECT FROM information_schema.tables
          WHERE table_name = 'mfa_settings'
        );
      `);

      if (!tableCheck.rows[0].exists) {
        return {};
      }

      const result = await pool.query('SELECT setting_key, setting_value FROM mfa_settings');
      
      // Convert array of rows to an object
      const settings = {};
      result.rows.forEach(row => {
        // Parse JSON values if needed
        try {
          if (row.setting_value.startsWith('[') || row.setting_value.startsWith('{')) {
            settings[row.setting_key] = JSON.parse(row.setting_value);
          } else if (row.setting_value === 'true' || row.setting_value === 'false') {
            settings[row.setting_key] = row.setting_value === 'true';
          } else if (!isNaN(row.setting_value)) {
            settings[row.setting_key] = Number(row.setting_value);
          } else {
            settings[row.setting_key] = row.setting_value;
          }
        } catch (e) {
          settings[row.setting_key] = row.setting_value;
        }
      });

      return settings;
    } catch (err) {
      console.error('Error fetching MFA settings:', err.message);
      throw err;
    }
  }

  // Update MFA settings
  static async update(settings) {
    try {
      // Start a transaction
      await pool.query('BEGIN');

      for (const [key, value] of Object.entries(settings)) {
        // Convert value to string for storage
        let stringValue;
        if (typeof value === 'object') {
          stringValue = JSON.stringify(value);
        } else {
          stringValue = String(value);
        }

        // Update or insert the setting
        await pool.query(
          `INSERT INTO mfa_settings (setting_key, setting_value) 
           VALUES ($1, $2) 
           ON CONFLICT (setting_key) 
           DO UPDATE SET setting_value = $2, updated_at = CURRENT_TIMESTAMP`,
          [key, stringValue]
        );
      }

      // Commit the transaction
      await pool.query('COMMIT');

      return await this.getAll();
    } catch (err) {
      // Rollback in case of error
      await pool.query('ROLLBACK');
      console.error('Error updating MFA settings:', err.message);
      throw err;
    }
  }

  // Get MFA settings for a specific user role
  static async getForRole(role) {
    try {
      const settings = await this.getAll();
      
      // Check if MFA is required for this role
      const isRequired = settings[`mfa_required_for_${role}s`] || false;
      
      return {
        required: isRequired,
        methods: settings.mfa_methods_available || ['email', 'app'],
        setupOnFirstLogin: settings.mfa_setup_on_first_login || false,
        gracePeriodDays: settings.mfa_grace_period_days || 7
      };
    } catch (err) {
      console.error(`Error fetching MFA settings for role ${role}:`, err.message);
      throw err;
    }
  }
}

module.exports = MfaSettings;
