/**
 * Middleware for encoding output data to prevent XSS attacks
 */
const { sanitizeContent } = require('../utils/validation');

/**
 * Encodes response data to prevent XSS attacks
 * This middleware intercepts the res.json method to sanitize the output
 */
const outputEncoder = (req, res, next) => {
  // Store the original json method
  const originalJson = res.json;
  
  // Override the json method to sanitize the output
  res.json = function(data) {
    // Skip sanitization for certain endpoints that need raw data
    const skipEndpoints = [
      '/api/auth/token', // JWT tokens should not be sanitized
      '/api/settings/export', // Data exports should not be sanitized
    ];
    
    if (skipEndpoints.some(endpoint => req.path.startsWith(endpoint))) {
      return originalJson.call(this, data);
    }
    
    // Sanitize the data
    const sanitizedData = sanitizeContent(data);
    
    // Call the original json method with sanitized data
    return originalJson.call(this, sanitizedData);
  };
  
  next();
};

module.exports = outputEncoder;
