const PatientAccessLog = require('../models/PatientAccessLog');

module.exports = function(accessTypeParam) {
  return function(req, res, next) {
    // This middleware logs patient access
    const logAccess = async () => {
      try {
        // Extract patient ID from URL parameters
        const patientId = req.params.id || req.params.patientId;

        if (!patientId) {
          return; // No patient ID to log
        }

        // Extract user ID from authenticated request
        const userId = req.user ? req.user.id : null;

        // Get access type from parameter or based on request method
        let accessType = accessTypeParam || 'view';
        if (!accessTypeParam) {
          if (req.method === 'POST') accessType = 'create';
          if (req.method === 'PUT') accessType = 'update';
          if (req.method === 'DELETE') accessType = 'delete';
        }

        // Get IP address and user agent
        const ipAddress = req.ip || req.connection.remoteAddress;
        const userAgent = req.get('User-Agent');

        // Log the access
        await PatientAccessLog.create(
          patientId,
          userId,
          accessType,
          ipAddress,
          userAgent
        );
      } catch (err) {
        console.error('Error logging patient access:', err);
        // Continue processing even if logging fails
      }
    };

    // Call next() immediately, but also log the access
    next();
    logAccess();
  };
};
