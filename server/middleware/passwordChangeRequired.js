const User = require('../models/User');

/**
 * Middleware to check if a user needs to change their password
 * If password change is required, certain routes will be blocked
 * until the user changes their password
 */
module.exports = async function(req, res, next) {
  try {
    // Skip check for auth routes and password change route
    const skipRoutes = [
      '/api/auth/change-password',
      '/api/auth/password-policy',
      '/api/auth/validate-password',
      '/api/auth/user'
    ];
    
    if (skipRoutes.includes(req.path)) {
      return next();
    }
    
    // Check if user needs to change password
    const passwordChangeCheck = await User.needsPasswordChange(req.user.id);
    
    if (passwordChangeCheck.required) {
      return res.status(403).json({
        msg: 'Password change required',
        passwordChange: passwordChangeCheck,
        requiredAction: 'CHANGE_PASSWORD'
      });
    }
    
    next();
  } catch (err) {
    console.error('Error in passwordChangeRequired middleware:', err.message);
    next();
  }
};
