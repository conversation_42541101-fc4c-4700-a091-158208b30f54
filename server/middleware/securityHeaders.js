/**
 * Middleware for setting security headers including Content Security Policy
 */

/**
 * Sets security headers for all responses
 * Includes Content Security Policy (CSP) to prevent XSS attacks
 */
const securityHeaders = (req, res, next) => {
  // Content Security Policy
  // Customize these directives based on your application's needs
  res.setHeader(
    'Content-Security-Policy',
    [
      // Default fallback - restrict to same origin
      "default-src 'self'",
      
      // Scripts - allow from same origin and trusted CDNs
      "script-src 'self' https://cdn.jsdelivr.net https://unpkg.com 'unsafe-inline'",
      
      // Styles - allow from same origin and trusted CDNs
      "style-src 'self' https://fonts.googleapis.com https://cdn.jsdelivr.net 'unsafe-inline'",
      
      // Fonts - allow from same origin and Google Fonts
      "font-src 'self' https://fonts.gstatic.com data:",
      
      // Images - allow from same origin and data URIs (for charts/graphs)
      "img-src 'self' data: blob:",
      
      // Connect - allow API connections to same origin
      "connect-src 'self'",
      
      // Media - restrict to same origin
      "media-src 'self'",
      
      // Object - restrict completely
      "object-src 'none'",
      
      // Frame - restrict to same origin
      "frame-src 'self'",
      
      // Worker - restrict to same origin
      "worker-src 'self' blob:",
      
      // Form actions - restrict to same origin
      "form-action 'self'",
      
      // Base URI - restrict to same origin
      "base-uri 'self'",
      
      // Frame ancestors - restrict to same origin (prevents clickjacking)
      "frame-ancestors 'self'",
      
      // Upgrade insecure requests
      "upgrade-insecure-requests",
    ].join('; ')
  );
  
  // X-Content-Type-Options
  // Prevents browsers from MIME-sniffing a response away from the declared content-type
  res.setHeader('X-Content-Type-Options', 'nosniff');
  
  // X-Frame-Options
  // Prevents clickjacking by not allowing the page to be framed
  res.setHeader('X-Frame-Options', 'SAMEORIGIN');
  
  // X-XSS-Protection
  // Enables the Cross-site scripting (XSS) filter in browsers
  res.setHeader('X-XSS-Protection', '1; mode=block');
  
  // Referrer-Policy
  // Controls how much referrer information should be included with requests
  res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
  
  // Feature-Policy / Permissions-Policy
  // Restricts which browser features the site can use
  res.setHeader(
    'Permissions-Policy',
    'camera=(), microphone=(), geolocation=(self), payment=()'
  );
  
  // Cache-Control
  // For sensitive pages, prevent caching
  if (req.path.includes('/api/auth/') || req.path.includes('/api/admin/')) {
    res.setHeader('Cache-Control', 'no-store, max-age=0');
    res.setHeader('Pragma', 'no-cache');
    res.setHeader('Expires', '0');
  }
  
  next();
};

module.exports = securityHeaders;
