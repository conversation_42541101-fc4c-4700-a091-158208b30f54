const IpRestriction = require('../models/IpRestriction');
const SystemSettings = require('../models/SystemSettings');

/**
 * Middleware to check if the client's IP address is allowed
 * This should be used after the auth middleware
 */
module.exports = async function(req, res, next) {
  try {
    // Get system settings to check if IP restriction is enabled
    const settings = await SystemSettings.getCurrent();
    
    // If IP restriction is not enabled, allow access
    if (!settings.ip_restriction) {
      return next();
    }
    
    // Get client IP address
    const clientIp = getClientIp(req);
    console.log(`Checking IP restriction for ${clientIp}`);
    
    // Check if IP is allowed
    const isAllowed = await IpRestriction.isIpAllowed(clientIp);
    
    if (!isAllowed) {
      console.log(`Access denied for IP ${clientIp}`);
      return res.status(403).json({ msg: 'Access denied from your IP address' });
    }
    
    // IP is allowed, proceed
    next();
  } catch (err) {
    console.error('Error in IP check middleware:', err.message);
    // In case of error, allow access to prevent lockouts
    next();
  }
};

/**
 * Get the client's IP address from the request
 * Handles various proxy scenarios
 */
function getClientIp(req) {
  // Check for X-Forwarded-For header (common with proxies)
  const xForwardedFor = req.headers['x-forwarded-for'];
  if (xForwardedFor) {
    // X-Forwarded-For can contain multiple IPs, take the first one
    const ips = xForwardedFor.split(',').map(ip => ip.trim());
    return ips[0];
  }
  
  // Check for other common headers
  if (req.headers['x-real-ip']) {
    return req.headers['x-real-ip'];
  }
  
  // Fall back to the remote address
  return req.connection.remoteAddress || 
         req.socket.remoteAddress || 
         (req.connection.socket ? req.connection.socket.remoteAddress : null) ||
         '127.0.0.1'; // Default to localhost if all else fails
}
