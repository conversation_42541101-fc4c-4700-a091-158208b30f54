const db = require('./db');

// Function to check if contact information is being saved correctly
const checkContactInfo = async () => {
  try {
    console.log('Checking if contact information is being saved correctly...');

    // Create a test visit with contact information
    const insertResult = await db.query(
      `INSERT INTO patient_visits (
        patient_id,
        visit_date,
        visit_reason,
        phone,
        email,
        address
      ) VALUES ($1, $2, $3, $4, $5, $6) RETURNING visit_id`,
      [
        41,
        '2024-05-19',
        'Test visit from script',
        '************',
        '<EMAIL>',
        '648 Pine St, Lakeside, PA 57736'
      ]
    );

    const visitId = insertResult.rows[0].visit_id;
    console.log(`Created test visit with ID: ${visitId}`);

    // Retrieve the visit to check if the contact information was saved
    const selectResult = await db.query(
      `SELECT visit_id, patient_id, phone, email, address
       FROM patient_visits
       WHERE visit_id = $1`,
      [visitId]
    );

    const visit = selectResult.rows[0];
    console.log('Retrieved visit:', visit);

    // Check if the contact information was saved correctly
    const phoneCorrect = visit.phone === '************';
    const emailCorrect = visit.email === '<EMAIL>';
    const addressCorrect = visit.address === '648 Pine St, Lakeside, PA 57736';

    console.log('Contact information saved correctly:', {
      phone: phoneCorrect,
      email: emailCorrect,
      address: addressCorrect
    });

    // Clean up the test visit
    await db.query('DELETE FROM patient_visits WHERE visit_id = $1', [visitId]);
    console.log(`Deleted test visit with ID: ${visitId}`);

    // Exit the process
    process.exit(0);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
};

// Run the test
checkContactInfo();
