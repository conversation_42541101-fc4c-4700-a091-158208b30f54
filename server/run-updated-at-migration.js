/**
 * <PERSON><PERSON><PERSON> to run the migration for adding updated_at field to patient_visits table
 */

const fs = require('fs');
const path = require('path');
const pool = require('./db');

async function runMigration() {
  const client = await pool.connect();

  try {
    console.log('Starting migration to add updated_at field to patient_visits table...');

    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'migrations', 'add_updated_at_to_patient_visits.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');

    // Execute the SQL
    const result = await client.query(sql);

    console.log('✅ Successfully executed migration');
    console.log('updated_at and last_edited_by fields have been added to patient_visits table');

    // Log any messages from the migration
    if (result && result.rows && result.rows.length > 0) {
      console.log('Migration results:');
      result.rows.forEach(row => {
        console.log(row);
      });
    }

  } catch (error) {
    console.error('❌ Error executing migration:', error);
  } finally {
    client.release();
    console.log('Database connection closed');
    pool.end();
  }
}

// Run the migration
runMigration();
