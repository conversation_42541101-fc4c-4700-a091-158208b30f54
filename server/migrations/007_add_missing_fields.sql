-- Add Activity & Nutrition fields
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS activity_level TEXT,
ADD COLUMN IF NOT EXISTS nutritional_status TEXT,
ADD COLUMN IF NOT EXISTS hydration_status TEXT;

-- Add Social & Environment Factors
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS social_interaction_levels TEXT,
ADD COLUMN IF NOT EXISTS living_conditions TEXT,
ADD COLUMN IF NOT EXISTS age_friendly_environment TEXT,
ADD COLUMN IF NOT EXISTS social_support_network TEXT,
ADD COLUMN IF NOT EXISTS transportation_access TEXT,
ADD COLUMN IF NOT EXISTS financial_concerns TEXT;

-- Add Safety & Emergency fields
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS emergency_contact_name TEXT,
ADD COLUMN IF NOT EXISTS emergency_contact_number TEXT,
ADD COLUMN IF NOT EXISTS emergency_contact_relationship TEXT,
ADD COLUMN IF NOT EXISTS fall_risk_assessment TEXT,
ADD COLUMN IF NOT EXISTS home_safety_evaluation TEXT,
ADD COLUMN IF NOT EXISTS assistive_devices_used TEXT; 