-- Add physical measurements columns to patient_visits table

-- Add height column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'patient_visits' AND column_name = 'height') THEN
        ALTER TABLE patient_visits ADD COLUMN height NUMERIC(5,2);
    END IF;
END $$;

-- Add BMI column if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'patient_visits' AND column_name = 'bmi') THEN
        ALTER TABLE patient_visits ADD COLUMN bmi NUMERIC(4,1);
    END IF;
END $$;

COMMENT ON COLUMN patient_visits.height IS 'Patient height in centimeters';
COMMENT ON COLUMN patient_visits.bmi IS 'Body Mass Index calculated from height and weight'; 