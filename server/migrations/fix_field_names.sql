-- Fix field name discrepancies between database and form

-- Fix financial_concerns/financial_concern field in patients table
ALTER TABLE patients 
ADD COLUMN IF NOT EXISTS financial_concerns TEXT;

-- Copy data from financial_concern to financial_concerns in patients table
UPDATE patients 
SET financial_concerns = financial_concern
WHERE financial_concern IS NOT NULL AND financial_concerns IS NULL;

-- Fix financial_concerns/financial_concern field in patient_visits table
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS financial_concerns TEXT;

-- Copy data from financial_concern to financial_concerns in patient_visits table
UPDATE patient_visits 
SET financial_concerns = financial_concern
WHERE financial_concern IS NOT NULL AND financial_concerns IS NULL;

-- Add home_safety_evaluation to patients table if it doesn't exist
ALTER TABLE patients 
ADD COLUMN IF NOT EXISTS home_safety_evaluation TEXT;

-- Log the completion of the migration
SELECT 'Migration completed: Fixed field name discrepancies between database and form' as message;
