/**
 * Migration script to add edit_type and related columns to patient_edit_logs table
 * Run this script with: node migrations/add_edit_type_columns.js
 */

const pool = require('../db');

async function addColumnsToPatientEditLogs() {
  const client = await pool.connect();
  
  try {
    console.log('Starting migration: Adding columns to patient_edit_logs table');
    
    // Start a transaction
    await client.query('BEGIN');
    
    // Check if the table exists
    const tableCheck = await client.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'patient_edit_logs'
      )
    `);
    
    if (!tableCheck.rows[0].exists) {
      console.log('patient_edit_logs table does not exist. Migration skipped.');
      await client.query('COMMIT');
      return;
    }
    
    // Check if edit_type column already exists
    const columnCheck = await client.query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'patient_edit_logs' 
      AND column_name = 'edit_type'
    `);
    
    if (columnCheck.rows.length > 0) {
      console.log('edit_type column already exists. Migration skipped.');
      await client.query('COMMIT');
      return;
    }
    
    // Add the new columns if they don't exist
    console.log('Adding edit_type column...');
    await client.query(`
      ALTER TABLE patient_edit_logs 
      ADD COLUMN IF NOT EXISTS edit_type VARCHAR(50) DEFAULT 'standard'
    `);
    
    console.log('Adding edit_summary column...');
    await client.query(`
      ALTER TABLE patient_edit_logs 
      ADD COLUMN IF NOT EXISTS edit_summary TEXT
    `);
    
    console.log('Adding original_data column...');
    await client.query(`
      ALTER TABLE patient_edit_logs 
      ADD COLUMN IF NOT EXISTS original_data JSONB
    `);
    
    console.log('Adding new_data column...');
    await client.query(`
      ALTER TABLE patient_edit_logs 
      ADD COLUMN IF NOT EXISTS new_data JSONB
    `);
    
    console.log('Adding ip_address column...');
    await client.query(`
      ALTER TABLE patient_edit_logs 
      ADD COLUMN IF NOT EXISTS ip_address VARCHAR(50)
    `);
    
    // Update existing records to set edit_type based on patterns
    console.log('Updating existing records...');
    await client.query(`
      UPDATE patient_edit_logs
      SET edit_type = 
        CASE 
          WHEN field_changed = 'visit' AND new_value IS NULL THEN 'delete'
          WHEN field_changed = 'user_account' AND new_value IS NULL THEN 'delete'
          WHEN field_changed = 'patient' AND new_value IS NULL THEN 'delete'
          ELSE 'standard'
        END
    `);
    
    // Commit the transaction
    await client.query('COMMIT');
    console.log('Migration completed successfully');
    
  } catch (error) {
    // Rollback in case of error
    await client.query('ROLLBACK');
    console.error('Migration failed:', error);
    throw error;
  } finally {
    // Release the client
    client.release();
  }
}

// Run the migration
addColumnsToPatientEditLogs()
  .then(() => {
    console.log('Migration script completed');
    process.exit(0);
  })
  .catch(err => {
    console.error('Migration script failed:', err);
    process.exit(1);
  });
