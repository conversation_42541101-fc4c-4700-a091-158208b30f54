-- Migration to update cognitive health fields:
-- 1. Add cognitive_impairment_score (integer)
-- 2. Remove cognitive_status (as it's being replaced)
-- 3. Keep cognitive_test_results for Mini-Cog clinical notes

-- First, handle the patients table
DO $$
BEGIN
    -- Add cognitive_impairment_score if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'cognitive_impairment_score'
    ) THEN
        ALTER TABLE patients ADD COLUMN cognitive_impairment_score INTEGER;
        RAISE NOTICE 'Added cognitive_impairment_score column to patients table';
    ELSE
        RAISE NOTICE 'cognitive_impairment_score column already exists in patients table';
    END IF;

    -- Drop cognitive_status if it exists
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'cognitive_status'
    ) THEN
        ALTER TABLE patients DROP COLUMN cognitive_status;
        RAISE NOTICE 'Dropped cognitive_status column from patients table';
    ELSE
        RAISE NOTICE 'cognitive_status column does not exist in patients table';
    END IF;
END $$;

-- Then, handle the patient_visits table
DO $$
BEGIN
    -- Add cognitive_impairment_score if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'cognitive_impairment_score'
    ) THEN
        ALTER TABLE patient_visits ADD COLUMN cognitive_impairment_score INTEGER;
        RAISE NOTICE 'Added cognitive_impairment_score column to patient_visits table';
    ELSE
        RAISE NOTICE 'cognitive_impairment_score column already exists in patient_visits table';
    END IF;

    -- Drop cognitive_status if it exists
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'cognitive_status'
    ) THEN
        ALTER TABLE patient_visits DROP COLUMN cognitive_status;
        RAISE NOTICE 'Dropped cognitive_status column from patient_visits table';
    ELSE
        RAISE NOTICE 'cognitive_status column does not exist in patient_visits table';
    END IF;
END $$;

-- Log the completion of the migration
SELECT 'Migration completed: Updated cognitive health fields in database' as message;
