-- Add bilirubin_t (total bilirubin) and bilirubin_d (direct bilirubin) fields to patients and patient_visits tables
-- Remove existing bilirubin field from patients and bilirubin_total from patient_visits

-- Update patients table
ALTER TABLE patients
DROP COLUMN IF EXISTS bilirubin,
ADD COLUMN IF NOT EXISTS bilirubin_t DECIMAL(3,1),
ADD COLUMN IF NOT EXISTS bilirubin_d DECIMAL(3,1);

-- Update patient_visits table
ALTER TABLE patient_visits
DROP COLUMN IF EXISTS bilirubin_total,
ADD COLUMN IF NOT EXISTS bilirubin_t DECIMAL(3,1),
ADD COLUMN IF NOT EXISTS bilirubin_d DECIMAL(3,1);

-- Log the completion of the migration
SELECT 'Migration completed: Added bilirubin_t and bilirubin_d fields to patients and patient_visits tables' as message;
