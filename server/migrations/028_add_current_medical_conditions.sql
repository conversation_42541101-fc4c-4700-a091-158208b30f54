-- Migration to add current_medical_conditions field to patient_visits table

-- First, check if the column exists and add it if it doesn't
DO $$
BEGIN
    -- Add current_medical_conditions if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'current_medical_conditions'
    ) THEN
        ALTER TABLE patient_visits ADD COLUMN current_medical_conditions TEXT;
        RAISE NOTICE 'Added current_medical_conditions column to patient_visits table';
    ELSE
        RAISE NOTICE 'current_medical_conditions column already exists in patient_visits table';
    END IF;

    -- Check if allergies field exists
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'allergies'
    ) THEN
        ALTER TABLE patient_visits ADD COLUMN allergies TEXT;
        RAISE NOTICE 'Added allergies column to patient_visits table';
    ELSE
        RAISE NOTICE 'allergies column already exists in patient_visits table';
    END IF;
END $$;

-- Log the completion of the migration
SELECT 'Migration completed: Added current_medical_conditions and allergies fields to patient_visits table' as message;
