-- Check for mental health fields in patients table
SELECT column_name, data_type, character_maximum_length
FROM information_schema.columns
WHERE table_name = 'patients' 
AND column_name IN (
  'cognitive_impairment_score', 
  'cognitive_test_results', 
  'depression_screening', 
  'depression_score', 
  'anxiety_screening', 
  'anxiety_score',
  'mental_health_assessment'
);

-- Check for mental health fields in patient_visits table
SELECT column_name, data_type, character_maximum_length
FROM information_schema.columns
WHERE table_name = 'patient_visits' 
AND column_name IN (
  'cognitive_impairment_score', 
  'cognitive_test_results', 
  'depression_screening', 
  'depression_score', 
  'anxiety_screening', 
  'anxiety_score',
  'mental_health_assessment'
);
