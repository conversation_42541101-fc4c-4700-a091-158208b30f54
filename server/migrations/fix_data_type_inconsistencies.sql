-- Fix data type inconsistencies between patients and patient_visits tables

-- Update calf_circumference in patient_visits table to match patients table (NUMERIC(4,1))
-- This allows values from 0.0 to 999.9 with one decimal place precision
-- Research shows calf circumference is typically measured to the nearest 0.1 cm
ALTER TABLE patient_visits
ALTER COLUMN calf_circumference TYPE NUMERIC(4,1) USING calf_circumference::NUMERIC(4,1);

-- Update fall_detection_incidents in patient_visits table to match patients table (INTEGER)
-- This field counts the number of fall incidents, which should be a whole number
ALTER TABLE patient_visits
ALTER COLUMN fall_detection_incidents TYPE INTEGER USING fall_detection_incidents::INTEGER;

-- Log the completion of the migration
SELECT 'Migration completed: Fixed data type inconsistencies in patient_visits table' as message;
