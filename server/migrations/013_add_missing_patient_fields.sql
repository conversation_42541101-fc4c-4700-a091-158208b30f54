-- Add missing fields to patients table to match PatientFormData interface
ALTER TABLE patients 
ADD COLUMN IF NOT EXISTS lying_heart_rate INTEGER,
ADD COLUMN IF NOT EXISTS standing_heart_rate INTEGER,
ADD COLUMN IF NOT EXISTS medical_history TEXT,
ADD COLUMN IF NOT EXISTS emergency_contact_name TEXT,
ADD COLUMN IF NOT EXISTS emergency_contact_phone TEXT,
ADD COLUMN IF NOT EXISTS height DECIMAL(5,2);

-- Update field mappings in the database
ALTER TABLE patients
ALTER COLUMN lying_bp_systolic TYPE INTEGER USING lying_bp_systolic::integer,
ALTER COLUMN lying_bp_diastolic TYPE INTEGER USING lying_bp_diastolic::integer,
ALTER COLUMN standing_bp_systolic TYPE INTEGER USING standing_bp_systolic::integer,
ALTER COLUMN standing_bp_diastolic TYPE INTEGER USING standing_bp_diastolic::integer,
ALTER COLUMN heart_rate TYPE INTEGER USING heart_rate::integer,
ALTER COLUMN respiratory_rate TYPE INTEGER USING respiratory_rate::integer,
ALTER COLUMN pulse_oximetry TYPE INTEGER USING pulse_oximetry::integer,
ALTER COLUMN weight TYPE DECIMAL(5,2) USING weight::decimal(5,2),
ALTER COLUMN bmi TYPE DECIMAL(4,1) USING bmi::decimal(4,1);

-- Log the changes
SELECT 'Added missing fields to patients table' as message;
