-- Add vision and hearing fields to patients and patient_visits tables
-- Remove use_of_aids field

-- Update patients table
ALTER TABLE patients
ADD COLUMN IF NOT EXISTS vision_status TEXT,
ADD COLUMN IF NOT EXISTS hearing_status TEXT,
ADD COLUMN IF NOT EXISTS use_of_aid_vision TEXT,
ADD COLUMN IF NOT EXISTS use_of_aid_hearing TEXT;

-- Remove use_of_aids from patients table if it exists
DO $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'use_of_aids'
    ) THEN
        ALTER TABLE patients DROP COLUMN use_of_aids;
        RAISE NOTICE 'Removed use_of_aids column from patients table';
    ELSE
        RAISE NOTICE 'use_of_aids column does not exist in patients table';
    END IF;
END $$;

-- Update patient_visits table
ALTER TABLE patient_visits
ADD COLUMN IF NOT EXISTS vision_status TEXT,
ADD COLUMN IF NOT EXISTS hearing_status TEXT,
ADD COLUMN IF NOT EXISTS use_of_aid_vision TEXT,
ADD COLUMN IF NOT EXISTS use_of_aid_hearing TEXT;

-- Remove use_of_aids from patient_visits table if it exists
DO $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'use_of_aids'
    ) THEN
        ALTER TABLE patient_visits DROP COLUMN use_of_aids;
        RAISE NOTICE 'Removed use_of_aids column from patient_visits table';
    ELSE
        RAISE NOTICE 'use_of_aids column does not exist in patient_visits table';
    END IF;
END $$;
