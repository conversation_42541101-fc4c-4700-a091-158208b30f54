-- Add sitting BP and heart rate fields to patients table
ALTER TABLE patients 
ADD COLUMN IF NOT EXISTS sitting_bp_systolic INTEGER,
ADD COLUMN IF NOT EXISTS sitting_bp_diastolic INTEGER,
ADD COLUMN IF NOT EXISTS sitting_heart_rate INTEGER;

-- Add sitting BP and heart rate fields to patient_visits table
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS sitting_bp_systolic INTEGER,
ADD COLUMN IF NOT EXISTS sitting_bp_diastolic INTEGER,
ADD COLUMN IF NOT EXISTS sitting_heart_rate INTEGER;

-- Remove heart_rate field from patients table
ALTER TABLE patients 
DROP COLUMN IF EXISTS heart_rate;

-- Remove heart_rate field from patient_visits table
ALTER TABLE patient_visits 
DROP COLUMN IF EXISTS heart_rate;
