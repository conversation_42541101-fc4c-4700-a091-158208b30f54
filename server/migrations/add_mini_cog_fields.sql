-- Migration to add Mini-Cog assessment fields to patients and patient_visits tables

-- Add fields to patients table
ALTER TABLE patients 
ADD COLUMN IF NOT EXISTS mini_cog_word_recall_score INTEGER, -- Word recall score: 0-3
ADD COLUMN IF NOT EXISTS mini_cog_clock_drawing_score INTEGER, -- Clock drawing score: 0 or 2
ADD COLUMN IF NOT EXISTS mini_cog_words_used TEXT, -- Words used in the test
ADD COLUMN IF NOT EXISTS mini_cog_words_recalled TEXT, -- Which words were recalled (e.g., "1,0,1" for first and third recalled)
ADD COLUMN IF NOT EXISTS mini_cog_assessment_date DATE; -- Date of Mini-Cog assessment

-- Add fields to patient_visits table
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS mini_cog_word_recall_score INTEGER, -- Word recall score: 0-3
ADD COLUMN IF NOT EXISTS mini_cog_clock_drawing_score INTEGER, -- Clock drawing score: 0 or 2
ADD COLUMN IF NOT EXISTS mini_cog_words_used TEXT, -- Words used in the test
ADD COLUMN IF NOT EXISTS mini_cog_words_recalled TEXT, -- Which words were recalled
ADD COLUMN IF NOT EXISTS mini_cog_assessment_date DATE; -- Date of Mini-Cog assessment

-- Log the completion of the migration
SELECT 'Migration completed: Added Mini-Cog fields to patients and patient_visits tables' as message;
