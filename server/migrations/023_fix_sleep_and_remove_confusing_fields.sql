-- Fix data type issues and remove confusing fields

DO $$
BEGIN
    -- 1. Change sleep_initiation_difficulties from boolean to text
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'sleep_initiation_difficulties' AND data_type = 'boolean'
    ) THEN
        -- First create a temporary column
        ALTER TABLE patients ADD COLUMN sleep_initiation_difficulties_text TEXT;
        
        -- Copy data with conversion
        UPDATE patients 
        SET sleep_initiation_difficulties_text = 
            CASE 
                WHEN sleep_initiation_difficulties = TRUE THEN 'Yes'
                WHEN sleep_initiation_difficulties = FALSE THEN 'No'
                ELSE NULL
            END;
        
        -- Drop the old column
        ALTER TABLE patients DROP COLUMN sleep_initiation_difficulties;
        
        -- Rename the new column to the original name
        ALTER TABLE patients RENAME COLUMN sleep_initiation_difficulties_text TO sleep_initiation_difficulties;
        
        RAISE NOTICE 'Changed sleep_initiation_difficulties from boolean to text in patients table';
    ELSE
        RAISE NOTICE 'sleep_initiation_difficulties is not boolean or does not exist in patients table';
    END IF;

    -- 2. Change sleep_duration from text to numeric
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'sleep_duration' AND data_type = 'text'
    ) THEN
        -- First create a temporary column
        ALTER TABLE patients ADD COLUMN sleep_duration_numeric NUMERIC;
        
        -- Copy data with conversion (safely handling non-numeric values)
        UPDATE patients 
        SET sleep_duration_numeric = 
            CASE 
                WHEN sleep_duration ~ '^[0-9]+(\.[0-9]+)?$' THEN sleep_duration::NUMERIC
                ELSE NULL
            END;
        
        -- Drop the old column
        ALTER TABLE patients DROP COLUMN sleep_duration;
        
        -- Rename the new column to the original name
        ALTER TABLE patients RENAME COLUMN sleep_duration_numeric TO sleep_duration;
        
        RAISE NOTICE 'Changed sleep_duration from text to numeric in patients table';
    ELSE
        RAISE NOTICE 'sleep_duration is not text or does not exist in patients table';
    END IF;

    -- 3. Remove hydration_levels to avoid confusion with hydration_status
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'hydration_levels'
    ) THEN
        ALTER TABLE patients DROP COLUMN hydration_levels;
        RAISE NOTICE 'Removed hydration_levels from patients table';
    ELSE
        RAISE NOTICE 'hydration_levels does not exist in patients table';
    END IF;

    -- 4. Remove daily_activity_levels to avoid confusion with activity_level
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'daily_activity_levels'
    ) THEN
        ALTER TABLE patients DROP COLUMN daily_activity_levels;
        RAISE NOTICE 'Removed daily_activity_levels from patients table';
    ELSE
        RAISE NOTICE 'daily_activity_levels does not exist in patients table';
    END IF;

    -- 5. Do the same changes for patient_visits table if needed
    -- Change sleep_initiation_difficulties from boolean to text in patient_visits
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'sleep_initiation_difficulties' AND data_type = 'boolean'
    ) THEN
        -- First create a temporary column
        ALTER TABLE patient_visits ADD COLUMN sleep_initiation_difficulties_text TEXT;
        
        -- Copy data with conversion
        UPDATE patient_visits 
        SET sleep_initiation_difficulties_text = 
            CASE 
                WHEN sleep_initiation_difficulties = TRUE THEN 'Yes'
                WHEN sleep_initiation_difficulties = FALSE THEN 'No'
                ELSE NULL
            END;
        
        -- Drop the old column
        ALTER TABLE patient_visits DROP COLUMN sleep_initiation_difficulties;
        
        -- Rename the new column to the original name
        ALTER TABLE patient_visits RENAME COLUMN sleep_initiation_difficulties_text TO sleep_initiation_difficulties;
        
        RAISE NOTICE 'Changed sleep_initiation_difficulties from boolean to text in patient_visits table';
    ELSE
        RAISE NOTICE 'sleep_initiation_difficulties is not boolean or does not exist in patient_visits table';
    END IF;

    -- Change sleep_duration from text to numeric in patient_visits
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'sleep_duration' AND data_type = 'text'
    ) THEN
        -- First create a temporary column
        ALTER TABLE patient_visits ADD COLUMN sleep_duration_numeric NUMERIC;
        
        -- Copy data with conversion (safely handling non-numeric values)
        UPDATE patient_visits 
        SET sleep_duration_numeric = 
            CASE 
                WHEN sleep_duration ~ '^[0-9]+(\.[0-9]+)?$' THEN sleep_duration::NUMERIC
                ELSE NULL
            END;
        
        -- Drop the old column
        ALTER TABLE patient_visits DROP COLUMN sleep_duration;
        
        -- Rename the new column to the original name
        ALTER TABLE patient_visits RENAME COLUMN sleep_duration_numeric TO sleep_duration;
        
        RAISE NOTICE 'Changed sleep_duration from text to numeric in patient_visits table';
    ELSE
        RAISE NOTICE 'sleep_duration is not text or does not exist in patient_visits table';
    END IF;

    -- Remove hydration_levels from patient_visits
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'hydration_levels'
    ) THEN
        ALTER TABLE patient_visits DROP COLUMN hydration_levels;
        RAISE NOTICE 'Removed hydration_levels from patient_visits table';
    ELSE
        RAISE NOTICE 'hydration_levels does not exist in patient_visits table';
    END IF;

    -- Remove daily_activity_levels from patient_visits
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'daily_activity_levels'
    ) THEN
        ALTER TABLE patient_visits DROP COLUMN daily_activity_levels;
        RAISE NOTICE 'Removed daily_activity_levels from patient_visits table';
    ELSE
        RAISE NOTICE 'daily_activity_levels does not exist in patient_visits table';
    END IF;
END $$;
