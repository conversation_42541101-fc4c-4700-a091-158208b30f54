-- Check for vaccination fields in patients table
SELECT column_name, data_type
FROM information_schema.columns
WHERE table_name = 'patients' 
AND column_name IN (
  'influenza_vaccination_date',
  'pneumococcal_vaccination_date',
  'zoster_vaccination_date',
  'tdap_vaccination_date',
  'covid19_vaccination_date',
  'covid19_booster_date',
  'covid19_booster_vaccination_date',
  'hepatitis_a_vaccination_date',
  'hepatitis_b_vaccination_date',
  'mmr_vaccination_date',
  'varicella_vaccination_date',
  'other_vaccinations'
);

-- Check for vaccination fields in patient_visits table
SELECT column_name, data_type
FROM information_schema.columns
WHERE table_name = 'patient_visits' 
AND column_name IN (
  'influenza_vaccination_date',
  'pneumococcal_vaccination_date',
  'zoster_vaccination_date',
  'tdap_vaccination_date',
  'covid19_vaccination_date',
  'covid19_booster_date',
  'covid19_booster_vaccination_date',
  'hepatitis_a_vaccination_date',
  'hepatitis_b_vaccination_date',
  'mmr_vaccination_date',
  'varicella_vaccination_date',
  'other_vaccinations'
);
