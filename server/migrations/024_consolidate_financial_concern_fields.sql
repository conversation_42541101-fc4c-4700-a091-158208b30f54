-- Consolidate financial_concern and financial_concerns fields

-- First, copy data from financial_concerns to financial_concern in patients table if financial_concern is null
UPDATE patients
SET financial_concern = financial_concerns
WHERE financial_concern IS NULL AND financial_concerns IS NOT NULL;

-- Then, copy data from financial_concerns to financial_concern in patient_visits table if financial_concern is null
UPDATE patient_visits
SET financial_concern = financial_concerns
WHERE financial_concern IS NULL AND financial_concerns IS NOT NULL;

-- Drop the financial_concerns column from patients table
ALTER TABLE patients DROP COLUMN IF EXISTS financial_concerns;

-- Drop the financial_concerns column from patient_visits table
ALTER TABLE patient_visits DROP COLUMN IF EXISTS financial_concerns;

-- Log the completion of the migration
SELECT 'Migration completed: Consolidated financial_concern and financial_concerns fields' as message;
