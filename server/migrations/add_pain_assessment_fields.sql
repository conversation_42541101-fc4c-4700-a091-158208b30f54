-- Add pain assessment fields to patients and patient_visits tables

-- Update patients table
ALTER TABLE patients
ADD COLUMN IF NOT EXISTS pain_level INTEGER,
ADD COLUMN IF NOT EXISTS pain_location TEXT,
ADD COLUMN IF NOT EXISTS pain_character TEXT,
ADD COLUMN IF NOT EXISTS pain_levels_locations TEXT,
ADD COLUMN IF NOT EXISTS safe_pain_medications TEXT;

-- Update patient_visits table
ALTER TABLE patient_visits
ADD COLUMN IF NOT EXISTS pain_level INTEGER,
ADD COLUMN IF NOT EXISTS pain_location TEXT,
ADD COLUMN IF NOT EXISTS pain_character TEXT,
ADD COLUMN IF NOT EXISTS pain_levels_locations TEXT,
ADD COLUMN IF NOT EXISTS safe_pain_medications TEXT;
