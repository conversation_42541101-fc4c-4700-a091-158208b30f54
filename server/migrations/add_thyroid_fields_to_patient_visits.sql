-- Add t3 and t4 fields to patient_visits table
-- Remove free_t3 and free_t4 fields from patient_visits table

-- Update patient_visits table
ALTER TABLE patient_visits
DROP COLUMN IF EXISTS free_t3,
DROP COLUMN IF EXISTS free_t4,
ADD COLUMN IF NOT EXISTS t3 DECIMAL(3,1),
ADD COLUMN IF NOT EXISTS t4 DECIMAL(3,1);

-- Log the completion of the migration
SELECT 'Migration completed: Added t3 and t4 fields to patient_visits table and removed free_t3 and free_t4' as message;
