-- Migration to fix data types for medication_adherence and safe_pain_medications fields

-- First, check if the columns exist and change their data types if they do
DO $$
BEGIN
    -- Change medication_adherence to TEXT in patients table if it exists
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'medication_adherence'
    ) THEN
        ALTER TABLE patients 
        ALTER COLUMN medication_adherence TYPE TEXT;
        RAISE NOTICE 'Changed medication_adherence column in patients table to TEXT';
    ELSE
        RAISE NOTICE 'medication_adherence column does not exist in patients table';
    END IF;

    -- Change safe_pain_medications to TEXT in patients table if it exists
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'safe_pain_medications'
    ) THEN
        ALTER TABLE patients 
        ALTER COLUMN safe_pain_medications TYPE TEXT;
        RAISE NOTICE 'Changed safe_pain_medications column in patients table to TEXT';
    ELSE
        RAISE NOTICE 'safe_pain_medications column does not exist in patients table';
    END IF;

    -- Change medication_adherence to TEXT in patient_visits table if it exists
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'medication_adherence'
    ) THEN
        ALTER TABLE patient_visits 
        ALTER COLUMN medication_adherence TYPE TEXT;
        RAISE NOTICE 'Changed medication_adherence column in patient_visits table to TEXT';
    ELSE
        RAISE NOTICE 'medication_adherence column does not exist in patient_visits table';
    END IF;

    -- Change safe_pain_medications to TEXT in patient_visits table if it exists
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'safe_pain_medications'
    ) THEN
        ALTER TABLE patient_visits 
        ALTER COLUMN safe_pain_medications TYPE TEXT;
        RAISE NOTICE 'Changed safe_pain_medications column in patient_visits table to TEXT';
    ELSE
        RAISE NOTICE 'safe_pain_medications column does not exist in patient_visits table';
    END IF;
END $$;

-- Log the completion of the migration
SELECT 'Migration completed: Changed medication_adherence and safe_pain_medications fields to TEXT data type' as message;
