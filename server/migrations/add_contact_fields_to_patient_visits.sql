-- Migration to add contact information fields to patient_visits table
-- This ensures we can track changes in contact information over time

-- Add phone, email, and address columns to patient_visits table
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS phone VARCHAR(20),
ADD COLUMN IF NOT EXISTS email VARCHAR(100),
ADD COLUMN IF NOT EXISTS address TEXT;

-- Rename emergency_contact_number to emergency_contact_phone for consistency
DO $$
BEGIN
    -- Check if emergency_contact_number exists and emergency_contact_phone doesn't
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'emergency_contact_number'
    ) AND NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'emergency_contact_phone'
    ) THEN
        -- Rename the column
        ALTER TABLE patient_visits 
        RENAME COLUMN emergency_contact_number TO emergency_contact_phone;
        RAISE NOTICE 'Renamed emergency_contact_number to emergency_contact_phone in patient_visits table';
    END IF;
END $$;

-- Log the completion of the migration
SELECT 'Migration completed: Added phone, email, and address fields to patient_visits table' as message;
