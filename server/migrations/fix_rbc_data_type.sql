-- Migration to fix the data type of the rbc field in patients and patient_visits tables
-- The rbc field should be NUMERIC(4,2) to allow for values like 4.5 million cells/μL

-- First, check if the columns exist and update their data type if needed
DO $$
BEGIN
    -- Check and update rbc in patients table
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'rbc'
    ) THEN
        ALTER TABLE patients 
        ALTER COLUMN rbc TYPE NUMERIC(4,2) USING rbc::NUMERIC(4,2);
        RAISE NOTICE 'Updated rbc to NUMERIC(4,2) type in patients table';
    ELSE
        RAISE NOTICE 'rbc column does not exist in patients table';
    END IF;

    -- Check and update rbc in patient_visits table
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'rbc'
    ) THEN
        ALTER TABLE patient_visits 
        ALTER COLUMN rbc TYPE NUMERIC(4,2) USING rbc::NUMERIC(4,2);
        RAISE NOTICE 'Updated rbc to NUMERIC(4,2) type in patient_visits table';
    ELSE
        RAISE NOTICE 'rbc column does not exist in patient_visits table';
    END IF;
END $$;

-- Add comments to the columns for better documentation
COMMENT ON COLUMN patients.rbc IS 'Red Blood Cell Count (million cells/μL) - Normal range: 4.1-5.9 for men, 3.8-5.2 for women';
COMMENT ON COLUMN patient_visits.rbc IS 'Red Blood Cell Count (million cells/μL) - Normal range: 4.1-5.9 for men, 3.8-5.2 for women';

-- Log the completion of the migration
SELECT 'Migration completed: Fixed rbc data type in patients and patient_visits tables' as message;
