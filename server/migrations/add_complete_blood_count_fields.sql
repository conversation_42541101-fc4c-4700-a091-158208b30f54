-- Add Complete Blood Count fields to patients table
ALTER TABLE patients 
ADD COLUMN IF NOT EXISTS wbc INTEGER,
ADD COLUMN IF NOT EXISTS rbc INTEGER,
ADD COLUMN IF NOT EXISTS platelets INTEGER,
ADD COLUMN IF NOT EXISTS mcv DECIMAL(4,1),
ADD COLUMN IF NOT EXISTS mch DECIMAL(4,1),
ADD COLUMN IF NOT EXISTS mchc DECIMAL(4,1),
ADD COLUMN IF NOT EXISTS rdw DECIMAL(4,1),
ADD COLUMN IF NOT EXISTS neutrophils INTEGER,
ADD COLUMN IF NOT EXISTS lymphocytes INTEGER,
ADD COLUMN IF NOT EXISTS monocytes INTEGER,
ADD COLUMN IF NOT EXISTS eosinophils INTEGER,
ADD COLUMN IF NOT EXISTS basophils INTEGER;

-- Add Complete Blood Count fields to patient_visits table
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS wbc INTEGER,
ADD COLUMN IF NOT EXISTS rbc INTEGER,
ADD COLUMN IF NOT EXISTS platelets INTEGER,
ADD COLUMN IF NOT EXISTS mcv DECIMAL(4,1),
ADD COLUMN IF NOT EXISTS mch DECIMAL(4,1),
ADD COLUMN IF NOT EXISTS mchc DECIMAL(4,1),
ADD COLUMN IF NOT EXISTS rdw DECIMAL(4,1),
ADD COLUMN IF NOT EXISTS neutrophils INTEGER,
ADD COLUMN IF NOT EXISTS lymphocytes INTEGER,
ADD COLUMN IF NOT EXISTS monocytes INTEGER,
ADD COLUMN IF NOT EXISTS eosinophils INTEGER,
ADD COLUMN IF NOT EXISTS basophils INTEGER;
