-- Migration to remove the cholesterol_levels column from the patients table
-- since it can be inferred from cholesterol_total

-- First, check if the column exists
DO $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'cholesterol_levels'
    ) THEN
        -- Remove the column if it exists
        ALTER TABLE patients DROP COLUMN cholesterol_levels;
        RAISE NOTICE 'Removed cholesterol_levels column from patients table';
    ELSE
        RAISE NOTICE 'cholesterol_levels column does not exist in patients table';
    END IF;
END $$;

-- Also check and remove from patient_visits table if it exists
DO $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'cholesterol_levels'
    ) THEN
        -- Remove the column if it exists
        ALTER TABLE patient_visits DROP COLUMN cholesterol_levels;
        RAISE NOTICE 'Removed cholesterol_levels column from patient_visits table';
    ELSE
        RAISE NOTICE 'cholesterol_levels column does not exist in patient_visits table';
    END IF;
END $$;

-- Log the completion of the migration
SELECT 'Migration completed: Removed cholesterol_levels column from database' as message;
