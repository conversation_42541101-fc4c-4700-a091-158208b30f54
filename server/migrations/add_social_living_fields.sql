-- Add social interaction and living condition fields to patients and patient_visits tables

-- Add to patients table
ALTER TABLE patients 
ADD COLUMN IF NOT EXISTS social_interaction_levels TEXT,
ADD COLUMN IF NOT EXISTS social_support TEXT,
ADD COLUMN IF NOT EXISTS social_support_network TEXT,
ADD COLUMN IF NOT EXISTS living_conditions TEXT,
ADD COLUMN IF NOT EXISTS living_situation TEXT,
ADD COLUMN IF NOT EXISTS environmental_risks TEXT,
ADD COLUMN IF NOT EXISTS age_friendly_environment BOOLEAN;

-- Add to patient_visits table
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS social_interaction_levels TEXT,
ADD COLUMN IF NOT EXISTS social_support TEXT,
ADD COLUMN IF NOT EXISTS social_support_network TEXT,
ADD COLUMN IF NOT EXISTS living_conditions TEXT,
ADD COLUMN IF NOT EXISTS living_situation TEXT,
ADD COLUMN IF NOT EXISTS environmental_risks TEXT,
ADD COLUMN IF NOT EXISTS age_friendly_environment BOOLEAN;

-- Log the completion of the migration
SELECT 'Migration completed: Added social interaction and living condition fields to patients and patient_visits tables' as message;
