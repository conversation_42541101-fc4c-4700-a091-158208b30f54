-- Add new lab parameters to patients table
ALTER TABLE patients 
ADD COLUMN IF NOT EXISTS uric_acid DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS sgpt INTEGER, -- Same as ALT
ADD COLUMN IF NOT EXISTS sgot INTEGER, -- Same as AST
ADD COLUMN IF NOT EXISTS alkaline_phosphatase INTEGER, -- Same as ALP
ADD COLUMN IF NOT EXISTS vldl INTEGER,
ADD COLUMN IF NOT EXISTS total_protein DECIMAL(3,1),
-- platelet_count and rbc already added in previous migration

-- Add urine parameters to patients table
ADD COLUMN IF NOT EXISTS urine_color VARCHAR(50),
ADD COLUMN IF NOT EXISTS urine_transparency VARCHAR(50),
ADD COLUMN IF NOT EXISTS urine_ph DECIMAL(3,1),
ADD COLUMN IF NOT EXISTS urine_sugar VARCHAR(50),
ADD COLUMN IF NOT EXISTS urine_pus_cells VARCHAR(50),
ADD COLUMN IF NOT EXISTS urine_rbcs VARCHAR(50),
ADD COLUMN IF NOT EXISTS urine_epithelial_cells VARCHAR(50),
ADD COLUMN IF NOT EXISTS urine_crystals VARCHAR(50),
ADD COLUMN IF NOT EXISTS urine_casts VARCHAR(50),
ADD COLUMN IF NOT EXISTS urine_protein VARCHAR(50);

-- Add new lab parameters to patient_visits table
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS uric_acid DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS sgpt INTEGER, -- Same as ALT
ADD COLUMN IF NOT EXISTS sgot INTEGER, -- Same as AST
ADD COLUMN IF NOT EXISTS alkaline_phosphatase INTEGER, -- Same as ALP
ADD COLUMN IF NOT EXISTS vldl INTEGER,
ADD COLUMN IF NOT EXISTS total_protein DECIMAL(3,1),
-- platelet_count and rbc already added in previous migration

-- Add urine parameters to patient_visits table
ADD COLUMN IF NOT EXISTS urine_color VARCHAR(50),
ADD COLUMN IF NOT EXISTS urine_transparency VARCHAR(50),
ADD COLUMN IF NOT EXISTS urine_ph DECIMAL(3,1),
ADD COLUMN IF NOT EXISTS urine_sugar VARCHAR(50),
ADD COLUMN IF NOT EXISTS urine_pus_cells VARCHAR(50),
ADD COLUMN IF NOT EXISTS urine_rbcs VARCHAR(50),
ADD COLUMN IF NOT EXISTS urine_epithelial_cells VARCHAR(50),
ADD COLUMN IF NOT EXISTS urine_crystals VARCHAR(50),
ADD COLUMN IF NOT EXISTS urine_casts VARCHAR(50),
ADD COLUMN IF NOT EXISTS urine_protein VARCHAR(50);
