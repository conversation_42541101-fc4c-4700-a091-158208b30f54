-- Add updated_at column to patient_visits table
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP;

-- Add last_edited_by column to patient_visits table
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS last_edited_by INTEGER REFERENCES users(user_id) ON DELETE SET NULL;

-- Update existing records to set updated_at to created_at
UPDATE patient_visits 
SET updated_at = created_at
WHERE updated_at IS NULL;

-- Log the changes
SELECT 'Added updated_at and last_edited_by columns to patient_visits table' as message;
SELECT COUNT(*) as rows_updated FROM patient_visits WHERE updated_at IS NOT NULL;
