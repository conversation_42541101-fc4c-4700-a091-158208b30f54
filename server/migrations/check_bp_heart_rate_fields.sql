-- Check for BP and heart rate fields in patients table
SELECT column_name, data_type
FROM information_schema.columns
WHERE table_name = 'patients' 
AND column_name IN (
  'sitting_bp_systolic', 
  'sitting_bp_diastolic', 
  'sitting_heart_rate',
  'heart_rate'
);

-- Check for BP and heart rate fields in patient_visits table
SELECT column_name, data_type
FROM information_schema.columns
WHERE table_name = 'patient_visits' 
AND column_name IN (
  'sitting_bp_systolic', 
  'sitting_bp_diastolic', 
  'sitting_heart_rate',
  'heart_rate'
);
