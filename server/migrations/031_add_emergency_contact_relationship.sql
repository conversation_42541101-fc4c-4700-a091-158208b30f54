-- Migration to add emergency_contact_relationship field to patients table

-- First, check if the column exists and add it if it doesn't
DO $$
BEGIN
    -- Add emergency_contact_relationship if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'emergency_contact_relationship'
    ) THEN
        ALTER TABLE patients ADD COLUMN emergency_contact_relationship TEXT;
        RAISE NOTICE 'Added emergency_contact_relationship column to patients table';
    ELSE
        RAISE NOTICE 'emergency_contact_relationship column already exists in patients table';
    END IF;
END $$;

-- Log the completion of the migration
SELECT 'Migration completed: Added emergency_contact_relationship field to patients table' as message;
