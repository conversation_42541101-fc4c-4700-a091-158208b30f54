-- Migration to add new pain management and physical activity fields

DO $$
BEGIN
    -- Add pain_character to patients table if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'pain_character'
    ) THEN
        ALTER TABLE patients ADD COLUMN pain_character TEXT;
        RAISE NOTICE 'Added pain_character column to patients table';
    ELSE
        RAISE NOTICE 'pain_character column already exists in patients table';
    END IF;

    -- Add fall_risk to patients table if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'fall_risk'
    ) THEN
        ALTER TABLE patients ADD COLUMN fall_risk TEXT;
        RAISE NOTICE 'Added fall_risk column to patients table';
    ELSE
        RAISE NOTICE 'fall_risk column already exists in patients table';
    END IF;

    -- Add sleep_duration to patients table if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'sleep_duration'
    ) THEN
        ALTER TABLE patients ADD COLUMN sleep_duration DECIMAL(4,1);
        RAISE NOTICE 'Added sleep_duration column to patients table';
    ELSE
        RAISE NOTICE 'sleep_duration column already exists in patients table';
    END IF;

    -- Add the same fields to patient_visits table
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'pain_character'
    ) THEN
        ALTER TABLE patient_visits ADD COLUMN pain_character TEXT;
        RAISE NOTICE 'Added pain_character column to patient_visits table';
    ELSE
        RAISE NOTICE 'pain_character column already exists in patient_visits table';
    END IF;

    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'fall_risk'
    ) THEN
        ALTER TABLE patient_visits ADD COLUMN fall_risk TEXT;
        RAISE NOTICE 'Added fall_risk column to patient_visits table';
    ELSE
        RAISE NOTICE 'fall_risk column already exists in patient_visits table';
    END IF;

    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'sleep_duration'
    ) THEN
        ALTER TABLE patient_visits ADD COLUMN sleep_duration DECIMAL(4,1);
        RAISE NOTICE 'Added sleep_duration column to patient_visits table';
    ELSE
        RAISE NOTICE 'sleep_duration column already exists in patient_visits table';
    END IF;
END $$;

-- Log the completion of the migration
SELECT 'Migration completed: Added pain_character, fall_risk, and sleep_duration fields' as message;
