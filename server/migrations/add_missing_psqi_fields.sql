-- Add missing PSQI fields to patient_visits table

-- Check if the columns exist and create them if they don't
DO $$
BEGIN
    -- Check and add psqi_sleep_disturbance column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'psqi_sleep_disturbance'
    ) THEN
        ALTER TABLE patient_visits 
        ADD COLUMN psqi_sleep_disturbance INTEGER;
        RAISE NOTICE 'Added psqi_sleep_disturbance column to patient_visits table';
    ELSE
        RAISE NOTICE 'psqi_sleep_disturbance column already exists in patient_visits table';
    END IF;

    -- Check and add psqi_day_dysfunction column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'psqi_day_dysfunction'
    ) THEN
        ALTER TABLE patient_visits 
        ADD COLUMN psqi_day_dysfunction INTEGER;
        RAISE NOTICE 'Added psqi_day_dysfunction column to patient_visits table';
    ELSE
        RAISE NOTICE 'psqi_day_dysfunction column already exists in patient_visits table';
    END IF;

    -- Check and add psqi_sleep_quality column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'psqi_sleep_quality'
    ) THEN
        ALTER TABLE patient_visits 
        ADD COLUMN psqi_sleep_quality INTEGER;
        RAISE NOTICE 'Added psqi_sleep_quality column to patient_visits table';
    ELSE
        RAISE NOTICE 'psqi_sleep_quality column already exists in patient_visits table';
    END IF;
END $$;

-- Log the completion of the migration
SELECT 'Migration completed: Added missing PSQI fields to patient_visits table' as message;
