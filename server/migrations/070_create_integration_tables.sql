-- Migration to create integration and API management tables

-- Create api_keys table
CREATE TABLE IF NOT EXISTS api_keys (
  key_id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  key_value VARCHAR(255) NOT NULL UNIQUE,
  description TEXT,
  permissions TEXT[], -- Array of permissions this key has
  rate_limit INTEGER DEFAULT 100, -- Requests per minute
  is_active BOOLEAN DEFAULT TRUE,
  expires_at TIMESTAMP WITH TIME ZONE,
  created_by INTEGER REFERENCES users(user_id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create integrations table
CREATE TABLE IF NOT EXISTS integrations (
  integration_id SERIAL PRIMARY KEY,
  name VA<PERSON>HA<PERSON>(100) NOT NULL,
  type VARCHAR(50) NOT NULL, -- e.g., 'webhook', 'api', 'database', etc.
  description TEXT,
  config JSONB NOT NULL DEFAULT '{}', -- Configuration details in JSON format
  status VARCHAR(20) DEFAULT 'inactive', -- 'active', 'inactive', 'error', 'pending'
  last_sync TIMESTAMP WITH TIME ZONE,
  health_status VARCHAR(20) DEFAULT 'unknown', -- 'healthy', 'degraded', 'error', 'unknown'
  created_by INTEGER REFERENCES users(user_id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create external_systems table
CREATE TABLE IF NOT EXISTS external_systems (
  system_id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  type VARCHAR(50) NOT NULL, -- e.g., 'ehr', 'lab', 'pharmacy', 'insurance', etc.
  description TEXT,
  connection_details JSONB NOT NULL DEFAULT '{}', -- Connection details in JSON format
  credentials JSONB, -- Encrypted credentials
  status VARCHAR(20) DEFAULT 'disconnected', -- 'connected', 'disconnected', 'error'
  data_mapping JSONB, -- Field mappings between systems
  created_by INTEGER REFERENCES users(user_id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create integration_logs table
CREATE TABLE IF NOT EXISTS integration_logs (
  log_id SERIAL PRIMARY KEY,
  integration_id INTEGER REFERENCES integrations(integration_id) ON DELETE CASCADE,
  external_system_id INTEGER REFERENCES external_systems(system_id) ON DELETE SET NULL,
  event_type VARCHAR(50) NOT NULL, -- e.g., 'sync', 'error', 'connection', etc.
  status VARCHAR(20) NOT NULL, -- 'success', 'failure', 'warning'
  message TEXT,
  details JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create api_usage_logs table
CREATE TABLE IF NOT EXISTS api_usage_logs (
  log_id SERIAL PRIMARY KEY,
  key_id INTEGER REFERENCES api_keys(key_id) ON DELETE SET NULL,
  endpoint VARCHAR(255) NOT NULL,
  method VARCHAR(10) NOT NULL, -- 'GET', 'POST', 'PUT', 'DELETE', etc.
  status_code INTEGER,
  response_time INTEGER, -- in milliseconds
  ip_address VARCHAR(45),
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_api_keys_is_active ON api_keys(is_active);
CREATE INDEX IF NOT EXISTS idx_integrations_status ON integrations(status);
CREATE INDEX IF NOT EXISTS idx_external_systems_status ON external_systems(status);
CREATE INDEX IF NOT EXISTS idx_integration_logs_integration_id ON integration_logs(integration_id);
CREATE INDEX IF NOT EXISTS idx_integration_logs_status ON integration_logs(status);
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_key_id ON api_usage_logs(key_id);
CREATE INDEX IF NOT EXISTS idx_api_usage_logs_created_at ON api_usage_logs(created_at);

-- Log the changes
SELECT 'Created integration and API management tables' as message;
