-- Add thyroid function test fields to patient_visits table
ALTER TABLE patient_visits ADD COLUMN IF NOT EXISTS t4 NUMERIC;
ALTER TABLE patient_visits ADD COLUMN IF NOT EXISTS t3 NUMERIC;

-- Add comments to explain the purpose of the columns
COMMENT ON COLUMN patient_visits.t4 IS 'Total T4 (Thyroxine) level (μg/dL)';
COMMENT ON COLUMN patient_visits.t3 IS 'Total T3 (Triiodothyronine) level (ng/dL)';

-- Make sure vitamin fields exist and add comments
ALTER TABLE patient_visits ADD COLUMN IF NOT EXISTS vitamin_b12 INTEGER;
ALTER TABLE patient_visits ADD COLUMN IF NOT EXISTS vitamin_d INTEGER;
ALTER TABLE patient_visits ADD COLUMN IF NOT EXISTS folate NUMERIC;

-- Add comments to vitamin fields
COMMENT ON COLUMN patient_visits.vitamin_b12 IS 'Vitamin B12 level (pg/mL)';
COMMENT ON COLUMN patient_visits.vitamin_d IS 'Vitamin D (25-OH) level (ng/mL)';
COMMENT ON COLUMN patient_visits.folate IS 'Folate level (ng/mL)';
