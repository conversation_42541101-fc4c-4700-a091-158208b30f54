-- Fix PSQI field names in patient_visits table to match the form field names

-- Check if the columns exist and create them if they don't
DO $$
BEGIN
    -- Check and rename psqi_sleep_disturbance to psqi_sleep_disturbances if it exists
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'psqi_sleep_disturbance'
    ) THEN
        ALTER TABLE patient_visits 
        RENAME COLUMN psqi_sleep_disturbance TO psqi_sleep_disturbances;
        RAISE NOTICE 'Renamed psqi_sleep_disturbance to psqi_sleep_disturbances in patient_visits table';
    ELSE
        -- Check if psqi_sleep_disturbances exists, if not create it
        IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'patient_visits' AND column_name = 'psqi_sleep_disturbances'
        ) THEN
            ALTER TABLE patient_visits 
            ADD COLUMN psqi_sleep_disturbances INTEGER;
            RAISE NOTICE 'Added psqi_sleep_disturbances column to patient_visits table';
        ELSE
            RAISE NOTICE 'psqi_sleep_disturbances column already exists in patient_visits table';
        END IF;
    END IF;

    -- Check and rename psqi_day_dysfunction to psqi_daytime_dysfunction if it exists
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'psqi_day_dysfunction'
    ) THEN
        ALTER TABLE patient_visits 
        RENAME COLUMN psqi_day_dysfunction TO psqi_daytime_dysfunction;
        RAISE NOTICE 'Renamed psqi_day_dysfunction to psqi_daytime_dysfunction in patient_visits table';
    ELSE
        -- Check if psqi_daytime_dysfunction exists, if not create it
        IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'patient_visits' AND column_name = 'psqi_daytime_dysfunction'
        ) THEN
            ALTER TABLE patient_visits 
            ADD COLUMN psqi_daytime_dysfunction INTEGER;
            RAISE NOTICE 'Added psqi_daytime_dysfunction column to patient_visits table';
        ELSE
            RAISE NOTICE 'psqi_daytime_dysfunction column already exists in patient_visits table';
        END IF;
    END IF;

    -- Check and rename psqi_sleep_quality to psqi_subjective_sleep_quality if it exists
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'psqi_sleep_quality'
    ) THEN
        ALTER TABLE patient_visits 
        RENAME COLUMN psqi_sleep_quality TO psqi_subjective_sleep_quality;
        RAISE NOTICE 'Renamed psqi_sleep_quality to psqi_subjective_sleep_quality in patient_visits table';
    ELSE
        -- Check if psqi_subjective_sleep_quality exists, if not create it
        IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'patient_visits' AND column_name = 'psqi_subjective_sleep_quality'
        ) THEN
            ALTER TABLE patient_visits 
            ADD COLUMN psqi_subjective_sleep_quality INTEGER;
            RAISE NOTICE 'Added psqi_subjective_sleep_quality column to patient_visits table';
        ELSE
            RAISE NOTICE 'psqi_subjective_sleep_quality column already exists in patient_visits table';
        END IF;
    END IF;
END $$;

-- Log the completion of the migration
SELECT 'Migration completed: Fixed PSQI field names in patient_visits table' as message;
