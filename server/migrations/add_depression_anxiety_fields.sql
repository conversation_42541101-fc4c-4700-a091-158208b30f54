-- Migration to add PHQ-9 and GAD-7 assessment fields to patients and patient_visits tables

-- Add PHQ-9 fields to patients table
ALTER TABLE patients 
ADD COLUMN IF NOT EXISTS phq9_interest_pleasure INTEGER, -- Little interest or pleasure in doing things
ADD COLUMN IF NOT EXISTS phq9_feeling_down INTEGER, -- Feeling down, depressed, or hopeless
ADD COLUMN IF NOT EXISTS phq9_sleep_issues INTEGER, -- Trouble falling or staying asleep, or sleeping too much
ADD COLUMN IF NOT EXISTS phq9_tired INTEGER, -- Feeling tired or having little energy
ADD COLUMN IF NOT EXISTS phq9_appetite INTEGER, -- Poor appetite or overeating
ADD COLUMN IF NOT EXISTS phq9_feeling_bad INTEGER, -- Feeling bad about yourself
ADD COLUMN IF NOT EXISTS phq9_concentration INTEGER, -- Trouble concentrating
ADD COLUMN IF NOT EXISTS phq9_moving_speaking INTEGER, -- Moving or speaking slowly or being fidgety or restless
ADD COLUMN IF NOT EXISTS phq9_thoughts_hurting INTEGER, -- Thoughts that you would be better off dead or of hurting yourself
ADD COLUMN IF NOT EXISTS phq9_difficulty_level INTEGER, -- How difficult have these problems made it for you
ADD COLUMN IF NOT EXISTS phq9_notes TEXT; -- Clinical notes for PHQ-9 assessment

-- Add GAD-7 fields to patients table
ALTER TABLE patients 
ADD COLUMN IF NOT EXISTS gad7_feeling_nervous INTEGER, -- Feeling nervous, anxious, or on edge
ADD COLUMN IF NOT EXISTS gad7_stop_worrying INTEGER, -- Not being able to stop or control worrying
ADD COLUMN IF NOT EXISTS gad7_worrying_much INTEGER, -- Worrying too much about different things
ADD COLUMN IF NOT EXISTS gad7_trouble_relaxing INTEGER, -- Trouble relaxing
ADD COLUMN IF NOT EXISTS gad7_restless INTEGER, -- Being so restless that it's hard to sit still
ADD COLUMN IF NOT EXISTS gad7_annoyed INTEGER, -- Becoming easily annoyed or irritable
ADD COLUMN IF NOT EXISTS gad7_feeling_afraid INTEGER, -- Feeling afraid as if something awful might happen
ADD COLUMN IF NOT EXISTS gad7_difficulty_level INTEGER, -- How difficult have these problems made it for you
ADD COLUMN IF NOT EXISTS gad7_notes TEXT; -- Clinical notes for GAD-7 assessment

-- Add PHQ-9 fields to patient_visits table
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS phq9_interest_pleasure INTEGER,
ADD COLUMN IF NOT EXISTS phq9_feeling_down INTEGER,
ADD COLUMN IF NOT EXISTS phq9_sleep_issues INTEGER,
ADD COLUMN IF NOT EXISTS phq9_tired INTEGER,
ADD COLUMN IF NOT EXISTS phq9_appetite INTEGER,
ADD COLUMN IF NOT EXISTS phq9_feeling_bad INTEGER,
ADD COLUMN IF NOT EXISTS phq9_concentration INTEGER,
ADD COLUMN IF NOT EXISTS phq9_moving_speaking INTEGER,
ADD COLUMN IF NOT EXISTS phq9_thoughts_hurting INTEGER,
ADD COLUMN IF NOT EXISTS phq9_difficulty_level INTEGER,
ADD COLUMN IF NOT EXISTS phq9_notes TEXT;

-- Add GAD-7 fields to patient_visits table
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS gad7_feeling_nervous INTEGER,
ADD COLUMN IF NOT EXISTS gad7_stop_worrying INTEGER,
ADD COLUMN IF NOT EXISTS gad7_worrying_much INTEGER,
ADD COLUMN IF NOT EXISTS gad7_trouble_relaxing INTEGER,
ADD COLUMN IF NOT EXISTS gad7_restless INTEGER,
ADD COLUMN IF NOT EXISTS gad7_annoyed INTEGER,
ADD COLUMN IF NOT EXISTS gad7_feeling_afraid INTEGER,
ADD COLUMN IF NOT EXISTS gad7_difficulty_level INTEGER,
ADD COLUMN IF NOT EXISTS gad7_notes TEXT;

-- Log the completion of the migration
SELECT 'Migration completed: Added PHQ-9 and GAD-7 fields to patients and patient_visits tables' as message;
