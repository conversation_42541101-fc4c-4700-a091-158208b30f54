-- Add anxiety_score and depression_score fields to patients table
ALTER TABLE patients 
ADD COLUMN IF NOT EXISTS anxiety_score INTEGER,
ADD COLUMN IF NOT EXISTS depression_score INTEGER;

-- Add anxiety_score and depression_score fields to patient_visits table
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS anxiety_score INTEGER,
ADD COLUMN IF NOT EXISTS depression_score INTEGER;

-- Update the existing anxiety_screening and depression_screening fields to TEXT if they're not already
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'patients' 
        AND column_name = 'anxiety_screening' 
        AND data_type != 'text'
    ) THEN
        ALTER TABLE patients ALTER COLUMN anxiety_screening TYPE TEXT;
    END IF;

    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'patients' 
        AND column_name = 'depression_screening' 
        AND data_type != 'text'
    ) THEN
        ALTER TABLE patients ALTER COLUMN depression_screening TYPE TEXT;
    END IF;

    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'patient_visits' 
        AND column_name = 'anxiety_screening' 
        AND data_type != 'text'
    ) THEN
        ALTER TABLE patient_visits ALTER COLUMN anxiety_screening TYPE TEXT;
    END IF;

    IF EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'patient_visits' 
        AND column_name = 'depression_screening' 
        AND data_type != 'text'
    ) THEN
        ALTER TABLE patient_visits ALTER COLUMN depression_screening TYPE TEXT;
    END IF;
END $$;
