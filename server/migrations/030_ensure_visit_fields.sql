-- Migration to ensure the patient_visits table has the correct fields for medication_changes and referrals

-- First, check if the columns exist and create them if they don't
DO $$
BEGIN
    -- Check and add medication_changes column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'medication_changes'
    ) THEN
        ALTER TABLE patient_visits 
        ADD COLUMN medication_changes TEXT;
        RAISE NOTICE 'Added medication_changes column to patient_visits table';
    ELSE
        -- Ensure medication_changes is TEXT type
        ALTER TABLE patient_visits 
        ALTER COLUMN medication_changes TYPE TEXT;
        RAISE NOTICE 'Ensured medication_changes column in patient_visits table is TEXT type';
    END IF;

    -- Check and add referrals column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'referrals'
    ) THEN
        ALTER TABLE patient_visits 
        ADD COLUMN referrals TEXT;
        RAISE NOTICE 'Added referrals column to patient_visits table';
    ELSE
        -- Ensure referrals is TEXT type
        ALTER TABLE patient_visits 
        ALTER COLUMN referrals TYPE TEXT;
        RAISE NOTICE 'Ensured referrals column in patient_visits table is TEXT type';
    END IF;
    
    -- Check and add bilirubin_total column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'bilirubin_total'
    ) THEN
        ALTER TABLE patient_visits 
        ADD COLUMN bilirubin_total DECIMAL(3,1);
        RAISE NOTICE 'Added bilirubin_total column to patient_visits table';
    END IF;
    
    -- Check and add ldl_cholesterol column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'ldl_cholesterol'
    ) THEN
        ALTER TABLE patient_visits 
        ADD COLUMN ldl_cholesterol INTEGER;
        RAISE NOTICE 'Added ldl_cholesterol column to patient_visits table';
    END IF;
    
    -- Check and add hdl_cholesterol column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'hdl_cholesterol'
    ) THEN
        ALTER TABLE patient_visits 
        ADD COLUMN hdl_cholesterol INTEGER;
        RAISE NOTICE 'Added hdl_cholesterol column to patient_visits table';
    END IF;
END $$;

-- Log the completion of the migration
SELECT 'Migration completed: Ensured patient_visits table has medication_changes, referrals, bilirubin_total, ldl_cholesterol, and hdl_cholesterol columns' as message;
