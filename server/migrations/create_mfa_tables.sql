-- Migration to create MFA-related tables

-- Create MFA settings table
CREATE TABLE IF NOT EXISTS mfa_settings (
  id SERIAL PRIMARY KEY,
  setting_key VARCHAR(100) NOT NULL UNIQUE,
  setting_value TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create MFA user enrollment table
CREATE TABLE IF NOT EXISTS user_mfa (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  mfa_enabled BOOLEAN DEFAULT FALSE,
  mfa_method VARCHAR(50),
  mfa_secret TEXT,
  backup_codes TEXT[],
  last_verified TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(user_id)
);

-- Insert default MFA settings
INSERT INTO mfa_settings (setting_key, setting_value)
VALUES 
  ('mfa_required_for_admins', 'true'),
  ('mfa_required_for_doctors', 'false'),
  ('mfa_required_for_staff', 'false'),
  ('mfa_required_for_patients', 'false'),
  ('mfa_methods_available', '["email", "app"]'),
  ('mfa_setup_on_first_login', 'true'),
  ('mfa_grace_period_days', '7')
ON CONFLICT (setting_key) DO NOTHING;

-- Add MFA-related columns to users table if they don't exist
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'users' AND column_name = 'mfa_required') THEN
    ALTER TABLE users ADD COLUMN mfa_required BOOLEAN DEFAULT FALSE;
  END IF;
  
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                WHERE table_name = 'users' AND column_name = 'mfa_grace_period_end') THEN
    ALTER TABLE users ADD COLUMN mfa_grace_period_end TIMESTAMP WITH TIME ZONE;
  END IF;
END $$;

-- Create index on user_id for faster lookups
CREATE INDEX IF NOT EXISTS idx_user_mfa_user_id ON user_mfa(user_id);

-- Add function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ LANGUAGE 'plpgsql';

-- Create triggers for updated_at timestamps
DROP TRIGGER IF EXISTS update_mfa_settings_modtime ON mfa_settings;
CREATE TRIGGER update_mfa_settings_modtime
BEFORE UPDATE ON mfa_settings
FOR EACH ROW
EXECUTE PROCEDURE update_modified_column();

DROP TRIGGER IF EXISTS update_user_mfa_modtime ON user_mfa;
CREATE TRIGGER update_user_mfa_modtime
BEFORE UPDATE ON user_mfa
FOR EACH ROW
EXECUTE PROCEDURE update_modified_column();
