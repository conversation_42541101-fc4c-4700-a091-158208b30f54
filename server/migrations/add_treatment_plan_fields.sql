-- Add treatment_plan, follow_up_instructions, and referrals fields to patients table

-- Check if the columns exist and create them if they don't
DO $$
BEGIN
    -- Check and add treatment_plan column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'treatment_plan'
    ) THEN
        ALTER TABLE patients 
        ADD COLUMN treatment_plan TEXT;
        RAISE NOTICE 'Added treatment_plan column to patients table';
    ELSE
        RAISE NOTICE 'treatment_plan column already exists in patients table';
    END IF;

    -- Check and add follow_up_instructions column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'follow_up_instructions'
    ) THEN
        ALTER TABLE patients 
        ADD COLUMN follow_up_instructions TEXT;
        RAISE NOTICE 'Added follow_up_instructions column to patients table';
    ELSE
        RAISE NOTICE 'follow_up_instructions column already exists in patients table';
    END IF;

    -- Check and add referrals column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'referrals'
    ) THEN
        ALTER TABLE patients 
        ADD COLUMN referrals TEXT;
        RAISE NOTICE 'Added referrals column to patients table';
    ELSE
        RAISE NOTICE 'referrals column already exists in patients table';
    END IF;
END $$;

-- Add comments to explain the fields
COMMENT ON COLUMN patients.treatment_plan IS 'Patient treatment plan details';
COMMENT ON COLUMN patients.follow_up_instructions IS 'Follow-up instructions for the patient';
COMMENT ON COLUMN patients.referrals IS 'Referrals to specialists or other healthcare providers';

-- Log the completion of the migration
SELECT 'Migration completed: Added treatment_plan, follow_up_instructions, and referrals fields to patients table' as message;
