-- Migration to fix the blood_urea_nitrogen field data type inconsistency
-- and ensure it's consistent across patients and patient_visits tables

-- First, check the data types of blood_urea_nitrogen in both tables
DO $$
DECLARE
    patients_data_type TEXT;
    patient_visits_data_type TEXT;
BEGIN
    -- Get data type from patients table
    SELECT data_type INTO patients_data_type
    FROM information_schema.columns
    WHERE table_name = 'patients' AND column_name = 'blood_urea_nitrogen';

    -- Get data type from patient_visits table
    SELECT data_type INTO patient_visits_data_type
    FROM information_schema.columns
    WHERE table_name = 'patient_visits' AND column_name = 'blood_urea_nitrogen';

    -- Log the current data types
    RAISE NOTICE 'Current data types - patients: %, patient_visits: %', patients_data_type, patient_visits_data_type;

    -- Make both fields NUMERIC for consistency
    IF patients_data_type IS NOT NULL AND patients_data_type != 'numeric' THEN
        ALTER TABLE patients ALTER COLUMN blood_urea_nitrogen TYPE NUMERIC USING blood_urea_nitrogen::NUMERIC;
        RAISE NOTICE 'Changed patients.blood_urea_nitrogen from % to numeric', patients_data_type;
    END IF;

    IF patient_visits_data_type IS NOT NULL AND patient_visits_data_type != 'numeric' THEN
        ALTER TABLE patient_visits ALTER COLUMN blood_urea_nitrogen TYPE NUMERIC USING blood_urea_nitrogen::NUMERIC;
        RAISE NOTICE 'Changed patient_visits.blood_urea_nitrogen from % to numeric', patient_visits_data_type;
    END IF;
END $$;

-- Log the completion of the migration
SELECT 'Migration completed: Fixed blood_urea_nitrogen field data type inconsistency' as message;
