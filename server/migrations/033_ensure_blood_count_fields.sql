-- This migration ensures that all the required blood count fields exist in the patient_visits table
-- If any field doesn't exist, it will be added

-- Red Blood Cell Parameters
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'patient_visits' AND column_name = 'rbc') THEN
        ALTER TABLE patient_visits ADD COLUMN rbc NUMERIC;
        COMMENT ON COLUMN patient_visits.rbc IS 'Red Blood Cell Count (million cells/μL)';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'patient_visits' AND column_name = 'hemoglobin') THEN
        ALTER TABLE patient_visits ADD COLUMN hemoglobin NUMERIC;
        COMMENT ON COLUMN patient_visits.hemoglobin IS 'Hemoglobin (g/dL)';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'patient_visits' AND column_name = 'hematocrit') THEN
        ALTER TABLE patient_visits ADD COLUMN hematocrit NUMERIC;
        COMMENT ON COLUMN patient_visits.hematocrit IS 'Hematocrit (%)';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'patient_visits' AND column_name = 'platelets') THEN
        ALTER TABLE patient_visits ADD COLUMN platelets NUMERIC;
        COMMENT ON COLUMN patient_visits.platelets IS 'Platelets (thousand/μL)';
    END IF;

    -- Red Blood Cell Indices
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'patient_visits' AND column_name = 'mcv') THEN
        ALTER TABLE patient_visits ADD COLUMN mcv NUMERIC;
        COMMENT ON COLUMN patient_visits.mcv IS 'Mean Corpuscular Volume (fL)';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'patient_visits' AND column_name = 'mch') THEN
        ALTER TABLE patient_visits ADD COLUMN mch NUMERIC;
        COMMENT ON COLUMN patient_visits.mch IS 'Mean Corpuscular Hemoglobin (pg)';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'patient_visits' AND column_name = 'mchc') THEN
        ALTER TABLE patient_visits ADD COLUMN mchc NUMERIC;
        COMMENT ON COLUMN patient_visits.mchc IS 'Mean Corpuscular Hemoglobin Concentration (g/dL)';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'patient_visits' AND column_name = 'rdw') THEN
        ALTER TABLE patient_visits ADD COLUMN rdw NUMERIC;
        COMMENT ON COLUMN patient_visits.rdw IS 'Red Cell Distribution Width (%)';
    END IF;

    -- White Blood Cell Count & Differential
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'patient_visits' AND column_name = 'wbc') THEN
        ALTER TABLE patient_visits ADD COLUMN wbc NUMERIC;
        COMMENT ON COLUMN patient_visits.wbc IS 'White Blood Cell Count (thousand/μL)';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'patient_visits' AND column_name = 'neutrophils') THEN
        ALTER TABLE patient_visits ADD COLUMN neutrophils NUMERIC;
        COMMENT ON COLUMN patient_visits.neutrophils IS 'Neutrophils (%)';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'patient_visits' AND column_name = 'lymphocytes') THEN
        ALTER TABLE patient_visits ADD COLUMN lymphocytes NUMERIC;
        COMMENT ON COLUMN patient_visits.lymphocytes IS 'Lymphocytes (%)';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'patient_visits' AND column_name = 'monocytes') THEN
        ALTER TABLE patient_visits ADD COLUMN monocytes NUMERIC;
        COMMENT ON COLUMN patient_visits.monocytes IS 'Monocytes (%)';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'patient_visits' AND column_name = 'eosinophils') THEN
        ALTER TABLE patient_visits ADD COLUMN eosinophils NUMERIC;
        COMMENT ON COLUMN patient_visits.eosinophils IS 'Eosinophils (%)';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'patient_visits' AND column_name = 'basophils') THEN
        ALTER TABLE patient_visits ADD COLUMN basophils NUMERIC;
        COMMENT ON COLUMN patient_visits.basophils IS 'Basophils (%)';
    END IF;

    -- Iron Studies
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'patient_visits' AND column_name = 'ferritin') THEN
        ALTER TABLE patient_visits ADD COLUMN ferritin NUMERIC;
        COMMENT ON COLUMN patient_visits.ferritin IS 'Ferritin (ng/mL)';
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'patient_visits' AND column_name = 'iron') THEN
        ALTER TABLE patient_visits ADD COLUMN iron NUMERIC;
        COMMENT ON COLUMN patient_visits.iron IS 'Iron (μg/dL)';
    END IF;
END $$;
