-- Migration to add new medication and cancer screening fields to the patients table

-- First, check if the columns exist and add them if they don't
DO $$
BEGIN
    -- Add cancer_screening_results if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'cancer_screening_results'
    ) THEN
        ALTER TABLE patients ADD COLUMN cancer_screening_results TEXT;
        RAISE NOTICE 'Added cancer_screening_results column to patients table';
    ELSE
        RAISE NOTICE 'cancer_screening_results column already exists in patients table';
    END IF;

    -- Add current_medications if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'current_medications'
    ) THEN
        ALTER TABLE patients ADD COLUMN current_medications TEXT;
        RAISE NOTICE 'Added current_medications column to patients table';
    ELSE
        RAISE NOTICE 'current_medications column already exists in patients table';
    END IF;

    -- Add medication_adherence if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'medication_adherence'
    ) THEN
        ALTER TABLE patients ADD COLUMN medication_adherence TEXT;
        RAISE NOTICE 'Added medication_adherence column to patients table';
    ELSE
        RAISE NOTICE 'medication_adherence column already exists in patients table';
    END IF;

    -- Add medication_side_effects if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'medication_side_effects'
    ) THEN
        ALTER TABLE patients ADD COLUMN medication_side_effects TEXT;
        RAISE NOTICE 'Added medication_side_effects column to patients table';
    ELSE
        RAISE NOTICE 'medication_side_effects column already exists in patients table';
    END IF;

    -- Add safe_pain_medications if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'safe_pain_medications'
    ) THEN
        ALTER TABLE patients ADD COLUMN safe_pain_medications TEXT;
        RAISE NOTICE 'Added safe_pain_medications column to patients table';
    ELSE
        RAISE NOTICE 'safe_pain_medications column already exists in patients table';
    END IF;

    -- Add medication_allergies if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'medication_allergies'
    ) THEN
        ALTER TABLE patients ADD COLUMN medication_allergies TEXT;
        RAISE NOTICE 'Added medication_allergies column to patients table';
    ELSE
        RAISE NOTICE 'medication_allergies column already exists in patients table';
    END IF;
END $$;

-- Also add the same fields to patient_visits table if they don't exist
DO $$
BEGIN
    -- Add cancer_screening_results if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'cancer_screening_results'
    ) THEN
        ALTER TABLE patient_visits ADD COLUMN cancer_screening_results TEXT;
        RAISE NOTICE 'Added cancer_screening_results column to patient_visits table';
    ELSE
        RAISE NOTICE 'cancer_screening_results column already exists in patient_visits table';
    END IF;

    -- Add current_medications if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'current_medications'
    ) THEN
        ALTER TABLE patient_visits ADD COLUMN current_medications TEXT;
        RAISE NOTICE 'Added current_medications column to patient_visits table';
    ELSE
        RAISE NOTICE 'current_medications column already exists in patient_visits table';
    END IF;

    -- Add medication_adherence if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'medication_adherence'
    ) THEN
        ALTER TABLE patient_visits ADD COLUMN medication_adherence TEXT;
        RAISE NOTICE 'Added medication_adherence column to patient_visits table';
    ELSE
        RAISE NOTICE 'medication_adherence column already exists in patient_visits table';
    END IF;

    -- Add medication_side_effects if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'medication_side_effects'
    ) THEN
        ALTER TABLE patient_visits ADD COLUMN medication_side_effects TEXT;
        RAISE NOTICE 'Added medication_side_effects column to patient_visits table';
    ELSE
        RAISE NOTICE 'medication_side_effects column already exists in patient_visits table';
    END IF;

    -- Add safe_pain_medications if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'safe_pain_medications'
    ) THEN
        ALTER TABLE patient_visits ADD COLUMN safe_pain_medications TEXT;
        RAISE NOTICE 'Added safe_pain_medications column to patient_visits table';
    ELSE
        RAISE NOTICE 'safe_pain_medications column already exists in patient_visits table';
    END IF;

    -- Add medication_allergies if it doesn't exist
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'medication_allergies'
    ) THEN
        ALTER TABLE patient_visits ADD COLUMN medication_allergies TEXT;
        RAISE NOTICE 'Added medication_allergies column to patient_visits table';
    ELSE
        RAISE NOTICE 'medication_allergies column already exists in patient_visits table';
    END IF;
END $$;

-- Log the completion of the migration
SELECT 'Migration completed: Added medication and cancer screening fields to database' as message;
