-- Migration to fix the data types of blood count fields in patients and patient_visits tables
-- WBC, neutrophils, lymphocytes, monocytes, eosinophils, and basophils should be NUMERIC types

-- First, check if the columns exist and update their data type if needed
DO $$
BEGIN
    -- Update WBC in patients table
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'wbc'
    ) THEN
        ALTER TABLE patients 
        ALTER COLUMN wbc TYPE NUMERIC(5,2) USING wbc::NUMERIC(5,2);
        RAISE NOTICE 'Updated wbc to NUMERIC(5,2) type in patients table';
    ELSE
        RAISE NOTICE 'wbc column does not exist in patients table';
    END IF;

    -- Update WBC in patient_visits table
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'wbc'
    ) THEN
        ALTER TABLE patient_visits 
        ALTER COLUMN wbc TYPE NUMERIC(5,2) USING wbc::NUMERIC(5,2);
        RAISE NOTICE 'Updated wbc to NUMERIC(5,2) type in patient_visits table';
    ELSE
        RAISE NOTICE 'wbc column does not exist in patient_visits table';
    END IF;

    -- Update neutrophils in patients table
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'neutrophils'
    ) THEN
        ALTER TABLE patients 
        ALTER COLUMN neutrophils TYPE NUMERIC(5,2) USING neutrophils::NUMERIC(5,2);
        RAISE NOTICE 'Updated neutrophils to NUMERIC(5,2) type in patients table';
    END IF;

    -- Update neutrophils in patient_visits table
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'neutrophils'
    ) THEN
        ALTER TABLE patient_visits 
        ALTER COLUMN neutrophils TYPE NUMERIC(5,2) USING neutrophils::NUMERIC(5,2);
        RAISE NOTICE 'Updated neutrophils to NUMERIC(5,2) type in patient_visits table';
    END IF;

    -- Update lymphocytes in patients table
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'lymphocytes'
    ) THEN
        ALTER TABLE patients 
        ALTER COLUMN lymphocytes TYPE NUMERIC(5,2) USING lymphocytes::NUMERIC(5,2);
        RAISE NOTICE 'Updated lymphocytes to NUMERIC(5,2) type in patients table';
    END IF;

    -- Update lymphocytes in patient_visits table
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'lymphocytes'
    ) THEN
        ALTER TABLE patient_visits 
        ALTER COLUMN lymphocytes TYPE NUMERIC(5,2) USING lymphocytes::NUMERIC(5,2);
        RAISE NOTICE 'Updated lymphocytes to NUMERIC(5,2) type in patient_visits table';
    END IF;

    -- Update monocytes in patients table
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'monocytes'
    ) THEN
        ALTER TABLE patients 
        ALTER COLUMN monocytes TYPE NUMERIC(5,2) USING monocytes::NUMERIC(5,2);
        RAISE NOTICE 'Updated monocytes to NUMERIC(5,2) type in patients table';
    END IF;

    -- Update monocytes in patient_visits table
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'monocytes'
    ) THEN
        ALTER TABLE patient_visits 
        ALTER COLUMN monocytes TYPE NUMERIC(5,2) USING monocytes::NUMERIC(5,2);
        RAISE NOTICE 'Updated monocytes to NUMERIC(5,2) type in patient_visits table';
    END IF;

    -- Update eosinophils in patients table
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'eosinophils'
    ) THEN
        ALTER TABLE patients 
        ALTER COLUMN eosinophils TYPE NUMERIC(5,2) USING eosinophils::NUMERIC(5,2);
        RAISE NOTICE 'Updated eosinophils to NUMERIC(5,2) type in patients table';
    END IF;

    -- Update eosinophils in patient_visits table
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'eosinophils'
    ) THEN
        ALTER TABLE patient_visits 
        ALTER COLUMN eosinophils TYPE NUMERIC(5,2) USING eosinophils::NUMERIC(5,2);
        RAISE NOTICE 'Updated eosinophils to NUMERIC(5,2) type in patient_visits table';
    END IF;

    -- Update basophils in patients table
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'basophils'
    ) THEN
        ALTER TABLE patients 
        ALTER COLUMN basophils TYPE NUMERIC(5,2) USING basophils::NUMERIC(5,2);
        RAISE NOTICE 'Updated basophils to NUMERIC(5,2) type in patients table';
    ELSE
        RAISE NOTICE 'basophils column does not exist in patients table';
    END IF;

    -- Update basophils in patient_visits table
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'basophils'
    ) THEN
        ALTER TABLE patient_visits 
        ALTER COLUMN basophils TYPE NUMERIC(5,2) USING basophils::NUMERIC(5,2);
        RAISE NOTICE 'Updated basophils to NUMERIC(5,2) type in patient_visits table';
    ELSE
        RAISE NOTICE 'basophils column does not exist in patient_visits table';
    END IF;
END $$;

-- Add comments to the columns for better documentation
COMMENT ON COLUMN patients.wbc IS 'White Blood Cell Count (thousand cells/μL) - Normal range: 4.5-11.0';
COMMENT ON COLUMN patient_visits.wbc IS 'White Blood Cell Count (thousand cells/μL) - Normal range: 4.5-11.0';

COMMENT ON COLUMN patients.neutrophils IS 'Neutrophils (%) - Normal range: 40-60%';
COMMENT ON COLUMN patient_visits.neutrophils IS 'Neutrophils (%) - Normal range: 40-60%';

COMMENT ON COLUMN patients.lymphocytes IS 'Lymphocytes (%) - Normal range: 20-40%';
COMMENT ON COLUMN patient_visits.lymphocytes IS 'Lymphocytes (%) - Normal range: 20-40%';

COMMENT ON COLUMN patients.monocytes IS 'Monocytes (%) - Normal range: 2-8%';
COMMENT ON COLUMN patient_visits.monocytes IS 'Monocytes (%) - Normal range: 2-8%';

COMMENT ON COLUMN patients.eosinophils IS 'Eosinophils (%) - Normal range: 1-4%';
COMMENT ON COLUMN patient_visits.eosinophils IS 'Eosinophils (%) - Normal range: 1-4%';

COMMENT ON COLUMN patients.basophils IS 'Basophils (%) - Normal range: 0.5-1%';
COMMENT ON COLUMN patient_visits.basophils IS 'Basophils (%) - Normal range: 0.5-1%';

-- Log the completion of the migration
SELECT 'Migration completed: Fixed blood count data types in patients and patient_visits tables' as message;
