-- Migration to add geographic data to login_activity table

-- Add new columns for geographic data
ALTER TABLE login_activity 
ADD COLUMN IF NOT EXISTS geo_country VARCHAR(100),
ADD COLUMN IF NOT EXISTS geo_city VARCHAR(100),
ADD COLUMN IF NOT EXISTS geo_latitude DECIMAL(10, 7),
ADD COLUMN IF NOT EXISTS geo_longitude DECIMAL(10, 7),
ADD COLUMN IF NOT EXISTS geo_isp VARCHAR(255),
ADD COLUMN IF NOT EXISTS device_info TEXT,
ADD COLUMN IF NOT EXISTS browser_info TEXT;

-- Add index on ip_address for faster lookups
CREATE INDEX IF NOT EXISTS idx_login_activity_ip_address ON login_activity(ip_address);

-- Add index on timestamp for faster time-based queries
CREATE INDEX IF NOT EXISTS idx_login_activity_timestamp ON login_activity(timestamp);

-- Add index on status for faster filtering
CREATE INDEX IF NOT EXISTS idx_login_activity_status ON login_activity(status);
