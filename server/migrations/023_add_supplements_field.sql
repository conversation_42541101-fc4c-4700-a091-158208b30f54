-- Add supplements field to patients and patient_visits tables if it doesn't exist

-- Check if supplements column exists in patients table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'supplements'
    ) THEN
        ALTER TABLE patients ADD COLUMN supplements TEXT;
        RAISE NOTICE 'Added supplements column to patients table';
    ELSE
        RAISE NOTICE 'supplements column already exists in patients table';
    END IF;
END $$;

-- Check if supplements column exists in patient_visits table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'supplements'
    ) THEN
        ALTER TABLE patient_visits ADD COLUMN supplements TEXT;
        RAISE NOTICE 'Added supplements column to patient_visits table';
    ELSE
        RAISE NOTICE 'supplements column already exists in patient_visits table';
    END IF;
END $$;

-- Log the completion of the migration
SELECT 'Migration completed: Added supplements field to patients and patient_visits tables' as message;
