-- Add Falls Risk Assessment Tool (FRAT) fields to patients and patient_visits tables

-- Add fields to patients table
ALTER TABLE patients
ADD COLUMN IF NOT EXISTS frat_assessment_date DATE, -- Date of FRAT assessment
ADD COLUMN IF NOT EXISTS frat_fall_history INTEGER, -- Fall history selection (1-4)
ADD COLUMN IF NOT EXISTS frat_fall_history_score INTEGER, -- Fall history score (2, 4, 6, or 8)
ADD COLUMN IF NOT EXISTS frat_medications INTEGER, -- Medications selection (1-4)
ADD COLUMN IF NOT EXISTS frat_medications_score INTEGER, -- Medications score (1, 2, 3, or 4)
ADD COLUMN IF NOT EXISTS frat_psychological INTEGER, -- Psychological selection (1-4)
ADD COLUMN IF NOT EXISTS frat_psychological_score INTEGER, -- Psychological score (1, 2, 3, or 4)
ADD COLUMN IF NOT EXISTS frat_cognitive INTEGER, -- Cognitive selection (1-4)
ADD COLUMN IF NOT EXISTS frat_cognitive_score INTEGER, -- Cognitive score (1, 2, 3, or 4)
ADD COLUMN IF NOT EXISTS frat_total_score INTEGER, -- Total FRAT score (0-20)
ADD COLUMN IF NOT EXISTS frat_risk_level VARCHAR(20), -- Risk level (Low: 5-11, Medium: 12-15, High: 16-20)
ADD COLUMN IF NOT EXISTS frat_notes TEXT; -- Additional notes about falls risk

-- Add fields to patient_visits table
ALTER TABLE patient_visits
ADD COLUMN IF NOT EXISTS frat_assessment_date DATE, -- Date of FRAT assessment
ADD COLUMN IF NOT EXISTS frat_fall_history INTEGER, -- Fall history selection (1-4)
ADD COLUMN IF NOT EXISTS frat_fall_history_score INTEGER, -- Fall history score (2, 4, 6, or 8)
ADD COLUMN IF NOT EXISTS frat_medications INTEGER, -- Medications selection (1-4)
ADD COLUMN IF NOT EXISTS frat_medications_score INTEGER, -- Medications score (1, 2, 3, or 4)
ADD COLUMN IF NOT EXISTS frat_psychological INTEGER, -- Psychological selection (1-4)
ADD COLUMN IF NOT EXISTS frat_psychological_score INTEGER, -- Psychological score (1, 2, 3, or 4)
ADD COLUMN IF NOT EXISTS frat_cognitive INTEGER, -- Cognitive selection (1-4)
ADD COLUMN IF NOT EXISTS frat_cognitive_score INTEGER, -- Cognitive score (1, 2, 3, or 4)
ADD COLUMN IF NOT EXISTS frat_total_score INTEGER, -- Total FRAT score (0-20)
ADD COLUMN IF NOT EXISTS frat_risk_level VARCHAR(20), -- Risk level (Low: 5-11, Medium: 12-15, High: 16-20)
ADD COLUMN IF NOT EXISTS frat_notes TEXT; -- Additional notes about falls risk
