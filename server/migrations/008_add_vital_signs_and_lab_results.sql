-- Add Vital Signs columns
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS lying_bp_systolic INTEGER,
ADD COLUMN IF NOT EXISTS lying_bp_diastolic INTEGER,
ADD COLUMN IF NOT EXISTS sitting_bp_systolic INTEGER,
ADD COLUMN IF NOT EXISTS sitting_bp_diastolic INTEGER,
ADD COLUMN IF NOT EXISTS standing_bp_systolic INTEGER,
ADD COLUMN IF NOT EXISTS standing_bp_diastolic INTEGER,
ADD COLUMN IF NOT EXISTS lying_heart_rate INTEGER,
ADD COLUMN IF NOT EXISTS standing_heart_rate INTEGER,
ADD COLUMN IF NOT EXISTS heart_rhythm TEXT,
ADD COLUMN IF NOT EXISTS temperature DECIMAL(4,1),
ADD COLUMN IF NOT EXISTS respiratory_rate INTEGER,
ADD COLUMN IF NOT EXISTS pulse_oximetry INTEGER;

-- Add Lab Results: Diabetes Markers
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS blood_glucose INTEGER,
ADD COLUMN IF NOT EXISTS hba1c DECIMAL(3,1);

-- Add Lab Results: Lipid Panel
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS cholesterol_total INTEGER,
ADD COLUMN IF NOT EXISTS hdl_cholesterol INTEGER,
ADD COLUMN IF NOT EXISTS ldl_cholesterol INTEGER,
ADD COLUMN IF NOT EXISTS triglycerides INTEGER;

-- Add Lab Results: Kidney Function
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS creatinine DECIMAL(4,2),
ADD COLUMN IF NOT EXISTS egfr INTEGER,
ADD COLUMN IF NOT EXISTS blood_urea_nitrogen INTEGER,
ADD COLUMN IF NOT EXISTS urine_albumin_creatinine_ratio DECIMAL(5,2);

-- Add Lab Results: Electrolytes
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS sodium INTEGER,
ADD COLUMN IF NOT EXISTS potassium DECIMAL(3,1),
ADD COLUMN IF NOT EXISTS calcium DECIMAL(3,1),
ADD COLUMN IF NOT EXISTS magnesium DECIMAL(3,1),
ADD COLUMN IF NOT EXISTS phosphorus DECIMAL(3,1),
ADD COLUMN IF NOT EXISTS chloride INTEGER;

-- Add Lab Results: Liver Function Tests
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS alt INTEGER,
ADD COLUMN IF NOT EXISTS ast INTEGER,
ADD COLUMN IF NOT EXISTS alp INTEGER,
ADD COLUMN IF NOT EXISTS ggt INTEGER,
ADD COLUMN IF NOT EXISTS bilirubin_total DECIMAL(3,1),
ADD COLUMN IF NOT EXISTS albumin DECIMAL(3,1);

-- Add Lab Results: Bone Health Markers
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS vitamin_d INTEGER,
ADD COLUMN IF NOT EXISTS parathyroid_hormone DECIMAL(5,1),
ADD COLUMN IF NOT EXISTS alkaline_phosphatase_bone INTEGER;

-- Add Lab Results: Thyroid Function
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS tsh DECIMAL(4,2),
ADD COLUMN IF NOT EXISTS free_t4 DECIMAL(3,1),
ADD COLUMN IF NOT EXISTS free_t3 DECIMAL(3,1);

-- Add Lab Results: Inflammation Markers
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS crp DECIMAL(4,1),
ADD COLUMN IF NOT EXISTS esr INTEGER;

-- Add Lab Results: Anemia Screening
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS hemoglobin DECIMAL(3,1),
ADD COLUMN IF NOT EXISTS hematocrit DECIMAL(3,1),
ADD COLUMN IF NOT EXISTS ferritin INTEGER,
ADD COLUMN IF NOT EXISTS iron INTEGER,
ADD COLUMN IF NOT EXISTS vitamin_b12 INTEGER,
ADD COLUMN IF NOT EXISTS folate DECIMAL(4,1); 