-- Migration to remove the cognitive_test_results column from patients and patient_visits tables
-- This field is redundant as the information is already stored in other fields

-- First, check if the column exists in the patients table
DO $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'cognitive_test_results'
    ) THEN
        ALTER TABLE patients DROP COLUMN cognitive_test_results;
        RAISE NOTICE 'Removed cognitive_test_results column from patients table';
    ELSE
        RAISE NOTICE 'cognitive_test_results column does not exist in patients table';
    END IF;
END $$;

-- Next, check if the column exists in the patient_visits table
DO $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'cognitive_test_results'
    ) THEN
        ALTER TABLE patient_visits DROP COLUMN cognitive_test_results;
        RAISE NOTICE 'Removed cognitive_test_results column from patient_visits table';
    ELSE
        RAISE NOTICE 'cognitive_test_results column does not exist in patient_visits table';
    END IF;
END $$;

-- Log the completion of the migration
SELECT 'Migration completed: Removed cognitive_test_results column from patients and patient_visits tables' as message;
