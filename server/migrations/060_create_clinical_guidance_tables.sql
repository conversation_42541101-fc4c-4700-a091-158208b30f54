-- Migration: Create Clinical Guidance Management Tables

-- Create guidance_categories table
CREATE TABLE IF NOT EXISTS guidance_categories (
  category_id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  parent_id INTEGER REFERENCES guidance_categories(category_id) ON DELETE SET NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create clinical_guidance table
CREATE TABLE IF NOT EXISTS clinical_guidance (
  guidance_id SERIAL PRIMARY KEY,
  title VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  summary TEXT,
  category_id INTEGER REFERENCES guidance_categories(category_id) ON DELETE SET NULL,
  context_key VARCHAR(100), -- Key for contextual linking (e.g., "lab_results", "frailty_assessment")
  is_published BOOLEAN DEFAULT false,
  created_by INTEGER REFERENCES users(user_id) ON DELETE SET NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create guidance_tags table
CREATE TABLE IF NOT EXISTS guidance_tags (
  tag_id SERIAL PRIMARY KEY,
  name VARCHAR(50) NOT NULL UNIQUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create guidance_tag_relations junction table
CREATE TABLE IF NOT EXISTS guidance_tag_relations (
  guidance_id INTEGER REFERENCES clinical_guidance(guidance_id) ON DELETE CASCADE,
  tag_id INTEGER REFERENCES guidance_tags(tag_id) ON DELETE CASCADE,
  PRIMARY KEY (guidance_id, tag_id)
);

-- Create guidance_access table for role-based access control
CREATE TABLE IF NOT EXISTS guidance_access (
  access_id SERIAL PRIMARY KEY,
  guidance_id INTEGER REFERENCES clinical_guidance(guidance_id) ON DELETE CASCADE,
  role VARCHAR(20) NOT NULL, -- 'admin', 'doctor', 'nurse', 'patient', etc.
  can_view BOOLEAN DEFAULT true,
  can_edit BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE (guidance_id, role)
);

-- Create guidance_versions table for version history
CREATE TABLE IF NOT EXISTS guidance_versions (
  version_id SERIAL PRIMARY KEY,
  guidance_id INTEGER REFERENCES clinical_guidance(guidance_id) ON DELETE CASCADE,
  version_number INTEGER NOT NULL,
  title VARCHAR(255) NOT NULL,
  content TEXT NOT NULL,
  summary TEXT,
  category_id INTEGER REFERENCES guidance_categories(category_id) ON DELETE SET NULL,
  created_by INTEGER REFERENCES users(user_id) ON DELETE SET NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE (guidance_id, version_number)
);

-- Create guidance_audit table for audit trail
CREATE TABLE IF NOT EXISTS guidance_audit (
  audit_id SERIAL PRIMARY KEY,
  guidance_id INTEGER REFERENCES clinical_guidance(guidance_id) ON DELETE CASCADE,
  action VARCHAR(20) NOT NULL, -- 'create', 'update', 'delete', 'publish', 'unpublish'
  details JSONB, -- Store details about the change
  performed_by INTEGER REFERENCES users(user_id) ON DELETE SET NULL,
  performed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add triggers to update the updated_at timestamp
CREATE TRIGGER update_guidance_timestamp
BEFORE UPDATE ON clinical_guidance
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_guidance_categories_timestamp
BEFORE UPDATE ON guidance_categories
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_guidance_access_timestamp
BEFORE UPDATE ON guidance_access
FOR EACH ROW
EXECUTE FUNCTION update_timestamp();

-- Create index for faster lookups
CREATE INDEX idx_guidance_context_key ON clinical_guidance(context_key);
CREATE INDEX idx_guidance_category ON clinical_guidance(category_id);
CREATE INDEX idx_guidance_versions_guidance_id ON guidance_versions(guidance_id);
CREATE INDEX idx_guidance_audit_guidance_id ON guidance_audit(guidance_id);

-- Insert default categories
INSERT INTO guidance_categories (name, description)
VALUES
  ('General', 'General clinical guidance applicable to all areas'),
  ('Lab Results', 'Guidance related to interpreting laboratory results'),
  ('Vital Signs', 'Guidance related to vital signs assessment'),
  ('Frailty Assessment', 'Guidance related to frailty assessment and interventions'),
  ('Mental Health', 'Guidance related to mental health assessments and interventions'),
  ('Sleep Assessment', 'Guidance related to sleep quality assessment'),
  ('Falls Risk', 'Guidance related to falls risk assessment and prevention'),
  ('Medication Management', 'Guidance related to medication management in older adults');

-- Log the completion of the migration
SELECT 'Migration completed: Created clinical guidance management tables' as message;
