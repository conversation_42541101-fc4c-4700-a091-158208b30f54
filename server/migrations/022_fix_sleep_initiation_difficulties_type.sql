-- Migration to change sleep_initiation_difficulties from boolean to text

DO $$
BEGIN
    -- Change sleep_initiation_difficulties to TEXT in patients table
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'sleep_initiation_difficulties' AND data_type = 'boolean'
    ) THEN
        ALTER TABLE patients ALTER COLUMN sleep_initiation_difficulties TYPE TEXT USING 
            CASE 
                WHEN sleep_initiation_difficulties = TRUE THEN 'Severe'
                WHEN sleep_initiation_difficulties = FALSE THEN 'None'
                ELSE NULL
            END;
        RAISE NOTICE 'Changed sleep_initiation_difficulties column in patients table from boolean to text';
    END IF;

    -- Change sleep_initiation_difficulties to TEXT in patient_visits table
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'sleep_initiation_difficulties' AND data_type = 'boolean'
    ) THEN
        ALTER TABLE patient_visits ALTER COLUMN sleep_initiation_difficulties TYPE TEXT USING 
            CASE 
                WHEN sleep_initiation_difficulties = TRUE THEN 'Severe'
                WHEN sleep_initiation_difficulties = FALSE THEN 'None'
                ELSE NULL
            END;
        RAISE NOTICE 'Changed sleep_initiation_difficulties column in patient_visits table from boolean to text';
    END IF;
END $$;

-- Log the completion of the migration
SELECT 'Migration completed: Changed sleep_initiation_difficulties from boolean to text' as message;
