-- Fix PSQI fields in patient_visits table to ensure they are properly defined as INTEGER

-- Check if the columns exist and update their data type if needed
DO $$
BEGIN
    -- Check and update psqi_subjective_sleep_quality
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'psqi_subjective_sleep_quality'
    ) THEN
        ALTER TABLE patient_visits 
        ALTER COLUMN psqi_subjective_sleep_quality TYPE INTEGER USING psqi_subjective_sleep_quality::INTEGER;
        RAISE NOTICE 'Updated psqi_subjective_sleep_quality to INTEGER type in patient_visits table';
    END IF;

    -- Check and update psqi_sleep_latency
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'psqi_sleep_latency'
    ) THEN
        ALTER TABLE patient_visits 
        ALTER COLUMN psqi_sleep_latency TYPE INTEGER USING psqi_sleep_latency::INTEGER;
        RAISE NOTICE 'Updated psqi_sleep_latency to INTEGER type in patient_visits table';
    END IF;

    -- Check and update psqi_sleep_duration
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'psqi_sleep_duration'
    ) THEN
        ALTER TABLE patient_visits 
        ALTER COLUMN psqi_sleep_duration TYPE INTEGER USING psqi_sleep_duration::INTEGER;
        RAISE NOTICE 'Updated psqi_sleep_duration to INTEGER type in patient_visits table';
    END IF;

    -- Check and update psqi_sleep_efficiency
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'psqi_sleep_efficiency'
    ) THEN
        ALTER TABLE patient_visits 
        ALTER COLUMN psqi_sleep_efficiency TYPE INTEGER USING psqi_sleep_efficiency::INTEGER;
        RAISE NOTICE 'Updated psqi_sleep_efficiency to INTEGER type in patient_visits table';
    END IF;

    -- Check and update psqi_sleep_disturbances
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'psqi_sleep_disturbances'
    ) THEN
        ALTER TABLE patient_visits 
        ALTER COLUMN psqi_sleep_disturbances TYPE INTEGER USING psqi_sleep_disturbances::INTEGER;
        RAISE NOTICE 'Updated psqi_sleep_disturbances to INTEGER type in patient_visits table';
    END IF;

    -- Check and update psqi_sleep_medication
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'psqi_sleep_medication'
    ) THEN
        ALTER TABLE patient_visits 
        ALTER COLUMN psqi_sleep_medication TYPE INTEGER USING psqi_sleep_medication::INTEGER;
        RAISE NOTICE 'Updated psqi_sleep_medication to INTEGER type in patient_visits table';
    END IF;

    -- Check and update psqi_daytime_dysfunction
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'psqi_daytime_dysfunction'
    ) THEN
        ALTER TABLE patient_visits 
        ALTER COLUMN psqi_daytime_dysfunction TYPE INTEGER USING psqi_daytime_dysfunction::INTEGER;
        RAISE NOTICE 'Updated psqi_daytime_dysfunction to INTEGER type in patient_visits table';
    END IF;

    -- Check and update psqi_total_score
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'psqi_total_score'
    ) THEN
        ALTER TABLE patient_visits 
        ALTER COLUMN psqi_total_score TYPE INTEGER USING psqi_total_score::INTEGER;
        RAISE NOTICE 'Updated psqi_total_score to INTEGER type in patient_visits table';
    END IF;
END $$;

-- Log the completion of the migration
SELECT 'Migration completed: Fixed PSQI field types in patient_visits table' as message;
