-- Add end_time column to patient_visits table
ALTER TABLE patient_visits 
ADD COLUMN IF NOT EXISTS end_time TIME;

-- Update existing records to set end_time to 1 hour after visit_date time
UPDATE patient_visits 
SET end_time = (visit_date::time + INTERVAL '1 hour')
WHERE end_time IS NULL AND visit_date IS NOT NULL;

-- Log the changes
SELECT 'Added end_time column to patient_visits table' as message;
SELECT COUNT(*) as rows_updated FROM patient_visits WHERE end_time IS NOT NULL;
