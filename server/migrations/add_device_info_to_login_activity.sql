-- Migration to add device and geo info to login_activity table

-- Add new columns for device and geo data if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'login_activity' AND column_name = 'geo_country') THEN
        ALTER TABLE login_activity ADD COLUMN geo_country VARCHAR(100);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'login_activity' AND column_name = 'geo_city') THEN
        ALTER TABLE login_activity ADD COLUMN geo_city VARCHAR(100);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'login_activity' AND column_name = 'geo_latitude') THEN
        ALTER TABLE login_activity ADD COLUMN geo_latitude DECIMAL(10, 7);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'login_activity' AND column_name = 'geo_longitude') THEN
        ALTER TABLE login_activity ADD COLUMN geo_longitude DECIMAL(10, 7);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'login_activity' AND column_name = 'geo_isp') THEN
        ALTER TABLE login_activity ADD COLUMN geo_isp VARCHAR(255);
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'login_activity' AND column_name = 'device_info') THEN
        ALTER TABLE login_activity ADD COLUMN device_info TEXT;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                  WHERE table_name = 'login_activity' AND column_name = 'browser_info') THEN
        ALTER TABLE login_activity ADD COLUMN browser_info TEXT;
    END IF;
END $$;

-- Add index on ip_address for faster lookups if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes 
                  WHERE tablename = 'login_activity' AND indexname = 'idx_login_activity_ip_address') THEN
        CREATE INDEX idx_login_activity_ip_address ON login_activity(ip_address);
    END IF;
END $$;

-- Add index on timestamp for faster time-based queries if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes 
                  WHERE tablename = 'login_activity' AND indexname = 'idx_login_activity_timestamp') THEN
        CREATE INDEX idx_login_activity_timestamp ON login_activity(timestamp);
    END IF;
END $$;

-- Add index on status for faster filtering if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_indexes 
                  WHERE tablename = 'login_activity' AND indexname = 'idx_login_activity_status') THEN
        CREATE INDEX idx_login_activity_status ON login_activity(status);
    END IF;
END $$;
