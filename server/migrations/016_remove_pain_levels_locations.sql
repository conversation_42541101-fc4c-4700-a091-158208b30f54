-- Migration to remove the pain_levels_locations column from the patients and patient_visits tables
-- since it's redundant with pain_level and pain_location fields

-- First, check if the column exists in patients table
DO $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patients' AND column_name = 'pain_levels_locations'
    ) THEN
        -- Remove the column if it exists
        ALTER TABLE patients DROP COLUMN pain_levels_locations;
        RAISE NOTICE 'Removed pain_levels_locations column from patients table';
    ELSE
        RAISE NOTICE 'pain_levels_locations column does not exist in patients table';
    END IF;
END $$;

-- Then, check if the column exists in patient_visits table
DO $$
BEGIN
    IF EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'pain_levels_locations'
    ) THEN
        -- Remove the column if it exists
        ALTER TABLE patient_visits DROP COLUMN pain_levels_locations;
        RAISE NOTICE 'Removed pain_levels_locations column from patient_visits table';
    ELSE
        RAISE NOTICE 'pain_levels_locations column does not exist in patient_visits table';
    END IF;
END $$;
