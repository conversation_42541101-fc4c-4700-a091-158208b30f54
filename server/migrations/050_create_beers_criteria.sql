-- Migration to create the BEERS criteria table

-- Create BEERS criteria table
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.tables
        WHERE table_name = 'beers_criteria'
    ) THEN
        CREATE TABLE beers_criteria (
            criteria_id SERIAL PRIMARY KEY,
            medication_name VARCHAR(100) NOT NULL,
            category VARCHAR(50) NOT NULL, -- 'avoid', 'use_with_caution', 'adjust_for_kidney_function', 'drug_interaction', 'disease_interaction'
            condition_name VARCHAR(100), -- For disease-specific criteria
            interacting_medication VARCHAR(100), -- For drug-drug interactions
            recommendation TEXT NOT NULL,
            rationale TEXT NOT NULL,
            quality_of_evidence VARCHAR(20), -- 'high', 'moderate', 'low'
            strength_of_recommendation VARCHAR(20), -- 'strong', 'weak', 'insufficient'
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        RAISE NOTICE 'Created beers_criteria table';
    ELSE
        RAISE NOTICE 'beers_criteria table already exists';
    END IF;

    -- Add fields to patient_visits table for BEERS criteria alerts
    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'beers_criteria_alerts'
    ) THEN
        ALTER TABLE patient_visits ADD COLUMN beers_criteria_alerts TEXT;
        RAISE NOTICE 'Added beers_criteria_alerts column to patient_visits table';
    ELSE
        RAISE NOTICE 'beers_criteria_alerts column already exists in patient_visits table';
    END IF;

    IF NOT EXISTS (
        SELECT 1
        FROM information_schema.columns
        WHERE table_name = 'patient_visits' AND column_name = 'beers_criteria_overrides'
    ) THEN
        ALTER TABLE patient_visits ADD COLUMN beers_criteria_overrides TEXT;
        RAISE NOTICE 'Added beers_criteria_overrides column to patient_visits table';
    ELSE
        RAISE NOTICE 'beers_criteria_overrides column already exists in patient_visits table';
    END IF;
END $$;
