/**
 * <PERSON><PERSON>t to run the migration for removing pain_levels_locations field
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Create a connection to the database
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'medapp',
  password: process.env.DB_PASSWORD || '',
  port: process.env.DB_PORT || 5432,
});

async function runMigration() {
  const client = await pool.connect();
  
  try {
    console.log('Starting migration to remove pain_levels_locations field...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'migrations', '016_remove_pain_levels_locations.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    await client.query(sql);
    
    console.log('✅ Successfully executed migration');
    console.log('pain_levels_locations field has been removed from patients and patient_visits tables');
  } catch (error) {
    console.error('❌ Error executing migration:', error);
  } finally {
    client.release();
    console.log('Database connection closed');
    pool.end();
  }
}

runMigration();
