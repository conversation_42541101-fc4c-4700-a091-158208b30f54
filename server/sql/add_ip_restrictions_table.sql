-- Create ip_restrictions table
CREATE TABLE IF NOT EXISTS ip_restrictions (
  restriction_id SERIAL PRIMARY KEY,
  ip_address VARCHAR(45) NOT NULL,  -- Support for both IPv4 and IPv6
  description VARCHAR(255),
  is_allowed BOOLEAN NOT NULL DEFAULT TRUE,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  created_by INTEGER REFERENCES users(user_id),
  updated_by INTEGER REFERENCES users(user_id)
);

-- Create index on ip_address for faster lookups
CREATE INDEX IF NOT EXISTS idx_ip_restrictions_ip_address ON ip_restrictions(ip_address);

-- Add default localhost entry (always allowed)
INSERT INTO ip_restrictions (ip_address, description, is_allowed)
SELECT '127.0.0.1', 'Localhost (always allowed)', TRUE
WHERE NOT EXISTS (SELECT 1 FROM ip_restrictions WHERE ip_address = '127.0.0.1');

-- Log the changes
SELECT 'Created ip_restrictions table and inserted default values' as message;
