-- Add new columns to patient_edit_logs table for enhanced logging

-- Check if the table exists
DO $$
BEGIN
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'patient_edit_logs') THEN
        -- Add edit_type column if it doesn't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                       WHERE table_name = 'patient_edit_logs' AND column_name = 'edit_type') THEN
            ALTER TABLE patient_edit_logs ADD COLUMN edit_type VARCHAR(50) DEFAULT 'standard';
        END IF;

        -- Add edit_summary column if it doesn't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                       WHERE table_name = 'patient_edit_logs' AND column_name = 'edit_summary') THEN
            ALTER TABLE patient_edit_logs ADD COLUMN edit_summary TEXT;
        END IF;

        -- Add original_data column if it doesn't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                       WHERE table_name = 'patient_edit_logs' AND column_name = 'original_data') THEN
            ALTER TABLE patient_edit_logs ADD COLUMN original_data TEXT;
        END IF;

        -- Add new_data column if it doesn't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                       WHERE table_name = 'patient_edit_logs' AND column_name = 'new_data') THEN
            ALTER TABLE patient_edit_logs ADD COLUMN new_data TEXT;
        END IF;

        -- Add ip_address column if it doesn't exist
        IF NOT EXISTS (SELECT FROM information_schema.columns 
                       WHERE table_name = 'patient_edit_logs' AND column_name = 'ip_address') THEN
            ALTER TABLE patient_edit_logs ADD COLUMN ip_address VARCHAR(50);
        END IF;

        -- Make field_changed nullable since we might use original_data/new_data instead
        ALTER TABLE patient_edit_logs ALTER COLUMN field_changed DROP NOT NULL;
    END IF;
END $$;
