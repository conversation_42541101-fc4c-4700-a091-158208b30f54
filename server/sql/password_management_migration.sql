-- Password Management System Migration

-- 1. Add new columns to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_first_login BOOLEAN DEFAULT TRUE;
ALTER TABLE users ADD COLUMN IF NOT EXISTS password_last_changed TIMESTAMP;
ALTER TABLE users ADD COLUMN IF NOT EXISTS password_history TEXT[] DEFAULT '{}';
ALTER TABLE users ADD COLUMN IF NOT EXISTS default_password BOOLEAN DEFAULT TRUE;

-- 2. Create password_policy table
CREATE TABLE IF NOT EXISTS password_policy (
  policy_id SERIAL PRIMARY KEY,
  min_length INTEGER NOT NULL DEFAULT 8,
  max_length INTEGER NOT NULL DEFAULT 30,
  require_uppercase BOOLEAN NOT NULL DEFAULT TRUE,
  require_lowercase BOOLEAN NOT NULL DEFAULT TRUE,
  require_numbers BOOLEAN NOT NULL DEFAULT TRUE,
  require_special_chars BOOLEAN NOT NULL DEFAULT TRUE,
  password_expiry_days INTEGER DEFAULT 90,
  password_history_count INTEGER DEFAULT 3,
  max_failed_attempts INTEGER DEFAULT 5,
  default_password_pattern VARCHAR(255) DEFAULT '{username}123',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. Insert default password policy if none exists
INSERT INTO password_policy (
  min_length, 
  max_length, 
  require_uppercase, 
  require_lowercase, 
  require_numbers, 
  require_special_chars, 
  password_expiry_days, 
  password_history_count, 
  max_failed_attempts,
  default_password_pattern
)
SELECT 8, 30, TRUE, TRUE, TRUE, TRUE, 90, 3, 5, '{username}123'
WHERE NOT EXISTS (SELECT 1 FROM password_policy);

-- 4. Update existing users to set password_last_changed
UPDATE users SET password_last_changed = created_at WHERE password_last_changed IS NULL;

-- 5. Create index on is_first_login for faster queries
CREATE INDEX IF NOT EXISTS idx_users_is_first_login ON users(is_first_login);
