-- Update existing visits with comprehensive lipid profiles and body metrics

-- <PERSON> (ID: 4) - Update existing visits with full lipid panel
UPDATE patient_visits 
SET 
  hdl_cholesterol = 42,
  ldl_cholesterol = 115,
  triglycerides = 155,
  weight = 83.5,
  bmi = 27.5
WHERE patient_id = 4 AND visit_date = '2024-01-15';

UPDATE patient_visits 
SET 
  hdl_cholesterol = 40,
  ldl_cholesterol = 112,
  triglycerides = 148,
  weight = 83.0,
  bmi = 27.3
WHERE patient_id = 4 AND visit_date = '2024-02-20';

UPDATE patient_visits 
SET 
  hdl_cholesterol = 43,
  ldl_cholesterol = 108,
  triglycerides = 145,
  weight = 82.8,
  bmi = 27.2
WHERE patient_id = 4 AND visit_date = '2024-03-25';

-- <PERSON> (ID: 5) - Update with lipid panel and body metrics
UPDATE patient_visits 
SET 
  cholesterol_total = 195,
  hdl_cholesterol = 52,
  ldl_cholesterol = 118,
  triglycerides = 125,
  weight = 65.5,
  bmi = 24.2
WHERE patient_id = 5 AND visit_date = '2024-01-10';

UPDATE patient_visits 
SET 
  cholesterol_total = 192,
  hdl_cholesterol = 54,
  ldl_cholesterol = 115,
  triglycerides = 122,
  weight = 65.2,
  bmi = 24.1
WHERE patient_id = 5 AND visit_date = '2024-02-25';

UPDATE patient_visits 
SET 
  cholesterol_total = 190,
  hdl_cholesterol = 55,
  ldl_cholesterol = 112,
  triglycerides = 120,
  weight = 65.0,
  bmi = 24.0
WHERE patient_id = 5 AND visit_date = '2024-03-20';

-- Eleanor Thompson (ID: 6) - Update diabetes patient with lipid monitoring
UPDATE patient_visits 
SET 
  hdl_cholesterol = 45,
  ldl_cholesterol = 128,
  triglycerides = 185,
  weight = 72.5,
  bmi = 27.0
WHERE patient_id = 6 AND visit_date = '2024-01-05';

UPDATE patient_visits 
SET 
  hdl_cholesterol = 46,
  ldl_cholesterol = 122,
  triglycerides = 175,
  weight = 72.0,
  bmi = 26.8
WHERE patient_id = 6 AND visit_date = '2024-02-15';

UPDATE patient_visits 
SET 
  hdl_cholesterol = 47,
  ldl_cholesterol = 118,
  triglycerides = 165,
  weight = 71.5,
  bmi = 26.6
WHERE patient_id = 6 AND visit_date = '2024-03-15';

-- David Lee (ID: 7) - Update cardiovascular patient with detailed lipid tracking
UPDATE patient_visits 
SET 
  hdl_cholesterol = 38,
  ldl_cholesterol = 142,
  triglycerides = 200,
  weight = 79.5,
  bmi = 25.8
WHERE patient_id = 7 AND visit_date = '2024-01-20';

UPDATE patient_visits 
SET 
  hdl_cholesterol = 42,
  ldl_cholesterol = 132,
  triglycerides = 180,
  weight = 79.0,
  bmi = 25.5
WHERE patient_id = 7 AND visit_date = '2024-02-28';

UPDATE patient_visits 
SET 
  hdl_cholesterol = 44,
  ldl_cholesterol = 125,
  triglycerides = 165,
  weight = 78.5,
  bmi = 25.2
WHERE patient_id = 7 AND visit_date = '2024-03-28';

-- Sarah Miller (ID: 8) - Update healthy patient with wellness metrics
UPDATE patient_visits 
SET 
  hdl_cholesterol = 58,
  ldl_cholesterol = 92,
  triglycerides = 110,
  weight = 63.8,
  bmi = 22.9
WHERE patient_id = 8 AND visit_date = '2024-01-25';

UPDATE patient_visits 
SET 
  hdl_cholesterol = 60,
  ldl_cholesterol = 90,
  triglycerides = 108,
  weight = 63.5,
  bmi = 22.8
WHERE patient_id = 8 AND visit_date = '2024-02-10';

UPDATE patient_visits 
SET 
  hdl_cholesterol = 62,
  ldl_cholesterol = 88,
  triglycerides = 105,
  weight = 63.2,
  bmi = 22.7
WHERE patient_id = 8 AND visit_date = '2024-03-10';

-- Add a comment to track the update
COMMENT ON TABLE patient_visits IS 'Updated with comprehensive lipid profiles and body metrics on all visits'; 