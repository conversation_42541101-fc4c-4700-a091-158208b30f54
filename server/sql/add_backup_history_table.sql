-- Create backup_history table
CREATE TABLE IF NOT EXISTS backup_history (
  backup_id SERIAL PRIMARY KEY,
  backup_name VA<PERSON>HAR(255) NOT NULL,
  backup_path VARCHAR(255) NOT NULL,
  backup_size BIGINT NOT NULL,
  backup_type VARCHAR(50) NOT NULL DEFAULT 'manual', -- 'manual', 'scheduled', 'auto'
  status VARCHAR(50) NOT NULL DEFAULT 'completed', -- 'completed', 'failed', 'in_progress'
  is_encrypted BOOLEAN NOT NULL DEFAULT FALSE,
  retention_date TIMESTAMP,
  notes TEXT,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  created_by INTEGER REFERENCES users(user_id)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_backup_history_created_at ON backup_history(created_at);
CREATE INDEX IF NOT EXISTS idx_backup_history_backup_type ON backup_history(backup_type);

-- Create maintenance_schedule table
CREATE TABLE IF NOT EXISTS maintenance_schedule (
  schedule_id SERIAL PRIMARY KEY,
  schedule_name VA<PERSON>HAR(100) NOT NULL,
  start_time TIMESTAMP NOT NULL,
  end_time TIMESTAMP NOT NULL,
  is_recurring BOOLEAN NOT NULL DEFAULT FALSE,
  recurrence_pattern VARCHAR(50), -- 'daily', 'weekly', 'monthly', 'custom'
  recurrence_config JSONB, -- Custom configuration for recurrence
  notification_minutes INTEGER DEFAULT 60, -- Minutes before to send notification
  status VARCHAR(50) NOT NULL DEFAULT 'scheduled', -- 'scheduled', 'in_progress', 'completed', 'cancelled'
  notes TEXT,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  created_by INTEGER REFERENCES users(user_id),
  updated_by INTEGER REFERENCES users(user_id)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_maintenance_schedule_start_time ON maintenance_schedule(start_time);
CREATE INDEX IF NOT EXISTS idx_maintenance_schedule_status ON maintenance_schedule(status);

-- Create database_optimization_history table
CREATE TABLE IF NOT EXISTS database_optimization_history (
  optimization_id SERIAL PRIMARY KEY,
  optimization_type VARCHAR(50) NOT NULL, -- 'vacuum', 'analyze', 'reindex', 'custom'
  tables_affected TEXT[], -- Array of table names affected
  start_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  end_time TIMESTAMP,
  status VARCHAR(50) NOT NULL DEFAULT 'in_progress', -- 'in_progress', 'completed', 'failed'
  details JSONB, -- Additional details about the optimization
  performance_impact JSONB, -- Performance metrics before and after
  created_by INTEGER REFERENCES users(user_id)
);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_db_optimization_start_time ON database_optimization_history(start_time);
CREATE INDEX IF NOT EXISTS idx_db_optimization_status ON database_optimization_history(status);

-- Log the changes
SELECT 'Created backup_history, maintenance_schedule, and database_optimization_history tables' as message;
