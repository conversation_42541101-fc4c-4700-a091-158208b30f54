-- Create system_logs table for tracking system-level events
CREATE TABLE IF NOT EXISTS system_logs (
  log_id SERIAL PRIMARY KEY,
  log_type VARCHAR(50) NOT NULL, -- 'patient_deletion', 'visit_deletion', 'user_deletion', etc.
  user_id INTEGER REFERENCES users(user_id) ON DELETE SET NULL,
  related_id INTEGER, -- ID of the related entity (patient_id, visit_id, etc.)
  related_name VARCHAR(255), -- Name of the related entity (patient name, etc.)
  log_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  details JSONB, -- JSON data with details about the event
  ip_address VARCHAR(50)
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_system_logs_log_type ON system_logs(log_type);
CREATE INDEX IF NOT EXISTS idx_system_logs_user_id ON system_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_system_logs_log_time ON system_logs(log_time);
CREATE INDEX IF NOT EXISTS idx_system_logs_related_id ON system_logs(related_id);

-- Log the changes
SELECT 'Created system_logs table for tracking system-level events' as message;
