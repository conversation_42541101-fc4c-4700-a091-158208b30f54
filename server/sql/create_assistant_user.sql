-- Check if the role 'assistant' exists, if not create it
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role' AND typtype = 'e') THEN
    CREATE TYPE user_role AS ENUM ('admin', 'doctor', 'nurse', 'patient', 'assistant');
  ELSE
    -- If the enum exists but doesn't have 'assistant', add it
    IF NOT EXISTS (
      SELECT 1 FROM pg_enum 
      WHERE enumlabel = 'assistant' 
      AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role')
    ) THEN
      ALTER TYPE user_role ADD VALUE 'assistant';
    END IF;
  END IF;
END
$$;

-- Create the assistant user (password is 'password123')
INSERT INTO users (username, password, email, role, created_at)
VALUES 
  ('rita_shrestha', '$2a$10$mLK.rrdlvx9DCFb6Eck1t.TlltnGulepXnov3bBp5T2TloO1MYj52', '<EMAIL>', 'assistant', NOW())
ON CONFLICT (username) DO NOTHING;

-- Output the result
SELECT 'User <PERSON> created successfully!' AS result; 