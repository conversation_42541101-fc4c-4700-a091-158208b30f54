-- Sample visits for patients
-- Each patient will have multiple visits with different doctors and varying health data

-- Patient: <PERSON> (ID: 4)
INSERT INTO patient_visits (
  patient_id, doctor_id, visit_date, visit_reason,
  lying_bp_systolic, lying_bp_diastolic, heart_rate, temperature,
  blood_glucose, cholesterol_total, current_medications,
  diagnosis, treatment_plan, notes, created_by
) VALUES
(4, 2, '2024-01-15', 'Annual checkup',
 135, 85, 72, 36.8,
 110, 180, 'Lisinopril 10mg daily',
 'Well-controlled hypertension', 'Continue current medications', 'Patient is doing well', 13),

(4, 3, '2024-02-20', 'Follow-up visit',
 142, 88, 75, 37.1,
 115, 175, 'Lisinopril 10mg daily, Metformin 500mg twice daily',
 'Mild hyperglycemia', 'Added Metformin for blood sugar control', 'Monitor blood sugar levels', 13),

(4, 2, '2024-03-25', 'Blood pressure check',
 128, 82, 70, 36.7,
 108, NULL, 'Lisinopril 10mg daily, Metformin 500mg twice daily',
 'Improved blood pressure', 'Continue current regimen', 'Good progress', 13);

-- Patient: <PERSON> (ID: 5)
INSERT INTO patient_visits (
  patient_id, doctor_id, visit_date, visit_reason,
  lying_bp_systolic, lying_bp_diastolic, heart_rate, temperature,
  blood_glucose, cholesterol_total, current_medications,
  diagnosis, treatment_plan, notes, created_by
) VALUES
(5, 4, '2024-01-10', 'Routine geriatric assessment',
 125, 75, 68, 36.6,
 95, 190, 'Vitamin D 1000IU daily',
 'Mild osteoarthritis', 'Physical therapy recommended', 'Good overall health', 13),

(5, 3, '2024-02-25', 'Joint pain evaluation',
 128, 78, 70, 36.7,
 98, 185, 'Vitamin D 1000IU daily, Acetaminophen as needed',
 'Osteoarthritis progression', 'Added pain management plan', 'Monitor pain levels', 13),

(5, 4, '2024-03-20', 'Follow-up assessment',
 122, 72, 65, 36.5,
 92, 188, 'Vitamin D 1000IU daily, Acetaminophen as needed',
 'Stable condition', 'Continue current treatment', 'Responding well to therapy', 13);

-- Patient: Eleanor Thompson (ID: 6)
INSERT INTO patient_visits (
  patient_id, doctor_id, visit_date, visit_reason,
  lying_bp_systolic, lying_bp_diastolic, heart_rate, temperature,
  blood_glucose, cholesterol_total, current_medications,
  diagnosis, treatment_plan, notes, created_by
) VALUES
(6, 5, '2024-01-05', 'Diabetes checkup',
 130, 80, 74, 36.8,
 145, 210, 'Metformin 1000mg twice daily',
 'Type 2 Diabetes', 'Adjust medication dosage', 'Need better glucose control', 13),

(6, 5, '2024-02-15', 'Follow-up',
 132, 82, 76, 36.9,
 135, 200, 'Metformin 1000mg twice daily, Glipizide 5mg daily',
 'Improving diabetes control', 'Continue adjusted medications', 'Better glucose readings', 13),

(6, 3, '2024-03-15', 'General checkup',
 128, 78, 72, 36.7,
 128, 195, 'Metformin 1000mg twice daily, Glipizide 5mg daily',
 'Well-controlled diabetes', 'Maintain current regimen', 'Good progress', 13);

-- Patient: David Lee (ID: 7)
INSERT INTO patient_visits (
  patient_id, doctor_id, visit_date, visit_reason,
  lying_bp_systolic, lying_bp_diastolic, heart_rate, temperature,
  blood_glucose, cholesterol_total, current_medications,
  diagnosis, treatment_plan, notes, created_by
) VALUES
(7, 2, '2024-01-20', 'Cardiac evaluation',
 145, 90, 82, 37.1,
 105, 220, 'Atorvastatin 40mg daily',
 'Hyperlipidemia', 'Start statin therapy', 'Monitor cholesterol levels', 13),

(7, 2, '2024-02-28', 'Follow-up',
 138, 85, 78, 36.9,
 108, 200, 'Atorvastatin 40mg daily, Aspirin 81mg daily',
 'Improving cholesterol levels', 'Continue current medications', 'Good response to treatment', 13),

(7, 3, '2024-03-28', 'Regular checkup',
 135, 82, 75, 36.7,
 102, 180, 'Atorvastatin 40mg daily, Aspirin 81mg daily',
 'Well-controlled hyperlipidemia', 'Maintain current treatment', 'Excellent progress', 13);

-- Patient: Sarah Miller (ID: 8)
INSERT INTO patient_visits (
  patient_id, doctor_id, visit_date, visit_reason,
  lying_bp_systolic, lying_bp_diastolic, heart_rate, temperature,
  blood_glucose, cholesterol_total, current_medications,
  diagnosis, treatment_plan, notes, created_by
) VALUES
(8, 3, '2024-01-25', 'Initial consultation',
 118, 75, 68, 36.6,
 92, 170, 'None',
 'Healthy check', 'Preventive care measures', 'Good overall health', 13),

(8, 4, '2024-02-10', 'Preventive care visit',
 120, 78, 70, 36.7,
 95, 175, 'Multivitamin daily',
 'Routine examination', 'Continue preventive care', 'Maintaining good health', 13),

(8, 3, '2024-03-10', 'Follow-up visit',
 115, 72, 65, 36.5,
 90, 168, 'Multivitamin daily',
 'Healthy status', 'Continue current regimen', 'Excellent health maintenance', 13);