-- Add lying_heart_rate and standing_heart_rate columns to patient_visits table
-- These columns are needed for orthostatic hypotension assessment

-- Add lying_heart_rate column if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'patient_visits' AND column_name = 'lying_heart_rate'
  ) THEN
    ALTER TABLE patient_visits 
    ADD COLUMN lying_heart_rate INTEGER;
  END IF;
END $$;

-- Add standing_heart_rate column if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'patient_visits' AND column_name = 'standing_heart_rate'
  ) THEN
    ALTER TABLE patient_visits 
    ADD COLUMN standing_heart_rate INTEGER;
  END IF;
END $$;

-- Add comment to explain the purpose of these columns
COMMENT ON COLUMN patient_visits.lying_heart_rate IS 'Heart rate (bpm) measured when patient is in lying position, used for orthostatic hypotension assessment';
COMMENT ON COLUMN patient_visits.standing_heart_rate IS 'Heart rate (bpm) measured when patient is in standing position, used for orthostatic hypotension assessment';

-- Update existing sample data to populate the new columns with demo values
-- Only for demonstration purposes
UPDATE patient_visits 
SET lying_heart_rate = heart_rate - 5,
    standing_heart_rate = heart_rate + 8
WHERE heart_rate IS NOT NULL; 