-- Update sample visit data with realistic values for health metrics
-- This script adds a mix of normal and abnormal values for various health metrics
-- Values are appropriate for elderly patients and use the correct database field names

-- First, let's create a function to generate random values within a range
CREATE OR REPLACE FUNCTION random_between(low NUMERIC, high NUMERIC, decimal_places INTEGER DEFAULT 1)
RETURNS NUMERIC AS $$
BEGIN
    RETURN ROUND((random() * (high - low) + low)::NUMERIC, decimal_places);
END;
$$ LANGUAGE plpgsql;

-- Create a function to randomly select a value that's either normal or abnormal (high or low)
CREATE OR REPLACE FUNCTION random_with_abnormal(normal_low NUMERIC, normal_high NUMERIC,
                                               abnormal_low NUMERIC, abnormal_high NUMERIC,
                                               decimal_places INTEGER DEFAULT 1)
RETURNS NUMERIC AS $$
DECLARE
    abnormal_chance NUMERIC := 0.3; -- 30% chance of abnormal value
    high_or_low NUMERIC := random();
BEGIN
    IF random() < abnormal_chance THEN
        -- Generate abnormal value
        IF high_or_low < 0.5 THEN
            -- Low abnormal
            RETURN ROUND((random() * (normal_low - abnormal_low) + abnormal_low)::NUMERIC, decimal_places);
        ELSE
            -- High abnormal
            RETURN ROUND((random() * (abnormal_high - normal_high) + normal_high)::NUMERIC, decimal_places);
        END IF;
    ELSE
        -- Generate normal value
        RETURN ROUND((random() * (normal_high - normal_low) + normal_low)::NUMERIC, decimal_places);
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Get all patient visit IDs
DO $$
DECLARE
    visit_record RECORD;
    gender TEXT;
BEGIN
    FOR visit_record IN SELECT pv.visit_id, p.gender
                        FROM patient_visits pv
                        JOIN patients p ON pv.patient_id = p.patient_id
    LOOP
        -- Update Thyroid Function Tests
        UPDATE patient_visits SET
            tsh = random_with_abnormal(0.4, 4.0, 0.1, 10.0, 2),
            t3 = random_with_abnormal(80, 200, 40, 300, 0),
            t4 = random_with_abnormal(4.5, 12.0, 2.0, 20.0, 1)
        WHERE visit_id = visit_record.visit_id;

        -- Update Inflammatory Markers
        UPDATE patient_visits SET
            crp = random_with_abnormal(0.0, 3.0, 0.0, 15.0, 1),
            esr = CASE
                    WHEN visit_record.gender = 'Male' THEN random_with_abnormal(0, 20, 0, 50, 0)
                    ELSE random_with_abnormal(0, 30, 0, 60, 0)
                  END
        WHERE visit_id = visit_record.visit_id;

        -- Update Electrolytes and Minerals
        UPDATE patient_visits SET
            sodium = random_with_abnormal(135, 145, 125, 155, 0),
            potassium = random_with_abnormal(3.5, 5.0, 2.5, 6.5, 1),
            calcium = random_with_abnormal(8.5, 10.5, 7.0, 12.0, 1),
            magnesium = random_with_abnormal(1.7, 2.2, 1.0, 3.0, 1)
        WHERE visit_id = visit_record.visit_id;

        -- Update Vitamin Status & Iron Studies
        UPDATE patient_visits SET
            vitamin_d = random_with_abnormal(30, 100, 10, 120, 1),
            vitamin_b12 = random_with_abnormal(200, 900, 100, 1200, 0),
            folate = random_with_abnormal(2.7, 17.0, 1.0, 25.0, 1),
            ferritin = CASE
                         WHEN visit_record.gender = 'Male' THEN random_with_abnormal(20, 250, 10, 500, 0)
                         ELSE random_with_abnormal(10, 120, 5, 300, 0)
                       END,
            iron = CASE
                     WHEN visit_record.gender = 'Male' THEN random_with_abnormal(60, 170, 30, 200, 0)
                     ELSE random_with_abnormal(50, 130, 20, 180, 0)
                   END
        WHERE visit_id = visit_record.visit_id;

        -- Update Liver Function Tests
        UPDATE patient_visits SET
            bilirubin_t = random_with_abnormal(0.1, 1.2, 0.05, 3.0, 2),
            bilirubin_d = random_with_abnormal(0.0, 0.3, 0.0, 1.0, 2),
            albumin = random_with_abnormal(3.5, 5.0, 2.0, 6.0, 1),
            total_protein = random_with_abnormal(6.0, 8.3, 4.0, 10.0, 1),
            alt = CASE
                    WHEN visit_record.gender = 'Male' THEN random_with_abnormal(7, 56, 3, 120, 0)
                    ELSE random_with_abnormal(7, 45, 3, 100, 0)
                  END,
            ast = CASE
                    WHEN visit_record.gender = 'Male' THEN random_with_abnormal(8, 48, 4, 110, 0)
                    ELSE random_with_abnormal(8, 43, 4, 100, 0)
                  END,
            alp = random_with_abnormal(40, 129, 20, 300, 0),
            ggt = CASE
                    WHEN visit_record.gender = 'Male' THEN random_with_abnormal(8, 61, 4, 150, 0)
                    ELSE random_with_abnormal(5, 36, 2, 100, 0)
                  END
        WHERE visit_id = visit_record.visit_id;

        -- Update Complete Blood Count (CBC)
        UPDATE patient_visits SET
            hemoglobin = CASE
                           WHEN visit_record.gender = 'Male' THEN random_with_abnormal(13.5, 17.5, 10.0, 20.0, 1)
                           ELSE random_with_abnormal(12.0, 15.5, 9.0, 18.0, 1)
                         END,
            hematocrit = CASE
                           WHEN visit_record.gender = 'Male' THEN random_with_abnormal(41, 50, 30, 60, 0)
                           ELSE random_with_abnormal(36, 48, 27, 55, 0)
                         END,
            rbc = CASE
                    WHEN visit_record.gender = 'Male' THEN random_with_abnormal(4.5, 5.9, 3.5, 7.0, 2)
                    ELSE random_with_abnormal(4.1, 5.1, 3.0, 6.0, 2)
                  END,
            wbc = random_with_abnormal(4.5, 11.0, 2.0, 15.0, 1),
            platelets = random_with_abnormal(150, 450, 50, 600, 0)
        WHERE visit_id = visit_record.visit_id;


    END LOOP;
END $$;

-- Drop the temporary functions
DROP FUNCTION IF EXISTS random_between;
DROP FUNCTION IF EXISTS random_with_abnormal;

-- Confirm the update
SELECT 'Health metrics sample data updated successfully!' AS result;
