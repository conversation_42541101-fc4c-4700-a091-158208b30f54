-- Create translations table
CREATE TABLE IF NOT EXISTS translations (
  translation_id SERIAL PRIMARY KEY,
  language_code VARCHAR(10) NOT NULL,
  key VARCHAR(255) NOT NULL,
  value TEXT NOT NULL,
  context TEXT,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  created_by INTEGER REFERENCES users(user_id),
  updated_by INTEGER REFERENCES users(user_id)
);

-- Create unique index for language_code and key combination
CREATE UNIQUE INDEX IF NOT EXISTS idx_translations_language_key ON translations(language_code, key);

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_translations_language ON translations(language_code);
CREATE INDEX IF NOT EXISTS idx_translations_key ON translations(key);

-- Insert default English translations
INSERT INTO translations (language_code, key, value)
VALUES
  -- Common UI elements
  ('en', 'common.save', 'Save'),
  ('en', 'common.cancel', 'Cancel'),
  ('en', 'common.delete', 'Delete'),
  ('en', 'common.edit', 'Edit'),
  ('en', 'common.add', 'Add'),
  ('en', 'common.search', 'Search'),
  ('en', 'common.filter', 'Filter'),
  ('en', 'common.reset', 'Reset'),
  ('en', 'common.submit', 'Submit'),
  ('en', 'common.back', 'Back'),
  ('en', 'common.next', 'Next'),
  ('en', 'common.previous', 'Previous'),
  ('en', 'common.yes', 'Yes'),
  ('en', 'common.no', 'No'),
  ('en', 'common.ok', 'OK'),
  ('en', 'common.confirm', 'Confirm'),
  ('en', 'common.loading', 'Loading...'),
  ('en', 'common.error', 'Error'),
  ('en', 'common.success', 'Success'),
  ('en', 'common.warning', 'Warning'),
  ('en', 'common.info', 'Information'),
  
  -- Navigation
  ('en', 'nav.dashboard', 'Dashboard'),
  ('en', 'nav.patients', 'Patients'),
  ('en', 'nav.doctors', 'Doctors'),
  ('en', 'nav.appointments', 'Appointments'),
  ('en', 'nav.settings', 'Settings'),
  ('en', 'nav.admin', 'Admin'),
  ('en', 'nav.profile', 'Profile'),
  ('en', 'nav.logout', 'Logout'),
  
  -- Patient related
  ('en', 'patient.add', 'Add Patient'),
  ('en', 'patient.edit', 'Edit Patient'),
  ('en', 'patient.details', 'Patient Details'),
  ('en', 'patient.history', 'Patient History'),
  ('en', 'patient.visits', 'Patient Visits'),
  ('en', 'patient.medications', 'Medications'),
  ('en', 'patient.allergies', 'Allergies'),
  ('en', 'patient.vitals', 'Vital Signs'),
  
  -- Admin dashboard
  ('en', 'admin.users', 'User Management'),
  ('en', 'admin.roles', 'Role Management'),
  ('en', 'admin.permissions', 'Permissions'),
  ('en', 'admin.settings', 'System Settings'),
  ('en', 'admin.logs', 'System Logs'),
  ('en', 'admin.backup', 'Backup & Restore'),
  
  -- System settings
  ('en', 'settings.general', 'General Settings'),
  ('en', 'settings.appearance', 'Appearance'),
  ('en', 'settings.notifications', 'Notifications'),
  ('en', 'settings.backup', 'Backup & Storage'),
  ('en', 'settings.email', 'Email Settings'),
  ('en', 'settings.localization', 'Localization'),
  ('en', 'settings.templates', 'Configuration Templates')
ON CONFLICT (language_code, key) DO NOTHING;

-- Insert default Spanish translations
INSERT INTO translations (language_code, key, value)
VALUES
  -- Common UI elements
  ('es', 'common.save', 'Guardar'),
  ('es', 'common.cancel', 'Cancelar'),
  ('es', 'common.delete', 'Eliminar'),
  ('es', 'common.edit', 'Editar'),
  ('es', 'common.add', 'Añadir'),
  ('es', 'common.search', 'Buscar'),
  ('es', 'common.filter', 'Filtrar'),
  ('es', 'common.reset', 'Restablecer'),
  ('es', 'common.submit', 'Enviar'),
  ('es', 'common.back', 'Atrás'),
  ('es', 'common.next', 'Siguiente'),
  ('es', 'common.previous', 'Anterior'),
  ('es', 'common.yes', 'Sí'),
  ('es', 'common.no', 'No'),
  ('es', 'common.ok', 'OK'),
  ('es', 'common.confirm', 'Confirmar'),
  ('es', 'common.loading', 'Cargando...'),
  ('es', 'common.error', 'Error'),
  ('es', 'common.success', 'Éxito'),
  ('es', 'common.warning', 'Advertencia'),
  ('es', 'common.info', 'Información')
ON CONFLICT (language_code, key) DO NOTHING;

-- Log the changes
SELECT 'Created translations table and inserted default translations' as message;
