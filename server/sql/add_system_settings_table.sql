-- Create system_settings table
CREATE TABLE IF NOT EXISTS system_settings (
  setting_id SERIAL PRIMARY KEY,
  system_name VARCHAR(100) NOT NULL DEFAULT 'Medical Records System',
  timezone VARCHAR(50) NOT NULL DEFAULT 'UTC',
  date_format VARCHAR(20) NOT NULL DEFAULT 'MM/DD/YYYY',
  maintenance_mode BOOLEAN NOT NULL DEFAULT FALSE,
  session_timeout INTEGER NOT NULL DEFAULT 30,
  two_factor_auth BOOLEAN NOT NULL DEFAULT FALSE,
  ip_restriction BOOLEAN NOT NULL DEFAULT FALSE,
  
  -- Email settings
  smtp_server VARCHAR(100),
  smtp_port INTEGER DEFAULT 587,
  smtp_username VARCHAR(100),
  smtp_password VARCHAR(255),
  email_from VARCHAR(100),
  email_enabled BOOLEAN NOT NULL DEFAULT TRUE,
  
  -- Notification settings
  email_notifications BOOLEAN NOT NULL DEFAULT TRUE,
  sms_notifications BOOLEAN NOT NULL DEFAULT FALSE,
  
  -- Backup settings
  auto_backup BOOLEAN NOT NULL DEFAULT TRUE,
  backup_frequency VARCHAR(20) NOT NULL DEFAULT 'daily',
  backup_retention INTEGER NOT NULL DEFAULT 30,
  
  -- Localization settings
  language VARCHAR(10) NOT NULL DEFAULT 'en',
  currency VARCHAR(10) NOT NULL DEFAULT 'USD',
  
  -- System fields
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  created_by INTEGER REFERENCES users(user_id),
  updated_by INTEGER REFERENCES users(user_id)
);

-- Insert default settings if table is empty
INSERT INTO system_settings (
  system_name, 
  timezone, 
  date_format, 
  maintenance_mode,
  session_timeout,
  two_factor_auth,
  ip_restriction,
  smtp_server,
  smtp_port,
  smtp_username,
  email_from,
  email_enabled,
  email_notifications,
  sms_notifications,
  auto_backup,
  backup_frequency,
  backup_retention,
  language,
  currency
)
SELECT 
  'Medical Records System', 
  'UTC', 
  'MM/DD/YYYY', 
  FALSE,
  30,
  FALSE,
  FALSE,
  'smtp.medapp.com',
  587,
  'smtp_user',
  '<EMAIL>',
  TRUE,
  TRUE,
  FALSE,
  TRUE,
  'daily',
  30,
  'en',
  'USD'
WHERE NOT EXISTS (SELECT 1 FROM system_settings);

-- Log the changes
SELECT 'Created system_settings table and inserted default values' as message;
