-- Add edit_type column to patient_edit_logs table if it doesn't exist
ALTER TABLE patient_edit_logs ADD COLUMN IF NOT EXISTS edit_type VARCHAR(50) DEFAULT 'standard';

-- Add edit_summary column to patient_edit_logs table if it doesn't exist
ALTER TABLE patient_edit_logs ADD COLUMN IF NOT EXISTS edit_summary TEXT;

-- Add original_data column to patient_edit_logs table if it doesn't exist
ALTER TABLE patient_edit_logs ADD COLUMN IF NOT EXISTS original_data TEXT;

-- Add new_data column to patient_edit_logs table if it doesn't exist
ALTER TABLE patient_edit_logs ADD COLUMN IF NOT EXISTS new_data TEXT;

-- Add ip_address column to patient_edit_logs table if it doesn't exist
ALTER TABLE patient_edit_logs ADD COLUMN IF NOT EXISTS ip_address VARCHAR(50);

-- Create index for faster queries on edit_type
CREATE INDEX IF NOT EXISTS idx_edit_logs_edit_type ON patient_edit_logs(edit_type);

-- Update existing visit deletion logs to have the correct edit_type
UPDATE patient_edit_logs 
SET edit_type = 'delete' 
WHERE field_changed = 'visit' AND new_value IS NULL;

-- Log the changes
SELECT 'Added edit_type and related columns to patient_edit_logs table' as message;
