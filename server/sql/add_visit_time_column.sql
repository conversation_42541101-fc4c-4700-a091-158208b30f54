-- Add visit_time column to patient_visits table if it doesn't exist
ALTER TABLE patient_visits ADD COLUMN IF NOT EXISTS visit_time TIME;

-- Add status column to patient_visits table if it doesn't exist
ALTER TABLE patient_visits ADD COLUMN IF NOT EXISTS status VARCHAR(20) DEFAULT 'scheduled';

-- Update the table to set default values for existing rows
UPDATE patient_visits SET visit_time = '09:00:00' WHERE visit_time IS NULL;
UPDATE patient_visits SET status = 'scheduled' WHERE status IS NULL;

-- Log the changes
SELECT 'Added visit_time and status columns to patient_visits table' as message;
SELECT COUNT(*) as rows_updated FROM patient_visits; 