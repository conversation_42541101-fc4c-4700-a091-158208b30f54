-- Add new columns to patients table
ALTER TABLE patients
ADD COLUMN IF NOT EXISTS unique_id VARCHAR(50) UNIQUE,
ADD COLUMN IF NOT EXISTS doctor_id INTEGER,
ADD COLUMN IF NOT EXISTS lying_bp_systolic INTEGER,
ADD COLUMN IF NOT EXISTS lying_bp_diastolic INTEGER,
ADD COLUMN IF NOT EXISTS sitting_bp_systolic INTEGER,
ADD COLUMN IF NOT EXISTS sitting_bp_diastolic INTEGER,
ADD COLUMN IF NOT EXISTS standing_bp_systolic INTEGER,
ADD COLUMN IF NOT EXISTS standing_bp_diastolic INTEGER,
ADD COLUMN IF NOT EXISTS heart_rate INTEGER,
ADD COLUMN IF NOT EXISTS heart_rhythm VARCHAR(50),
ADD COLUMN IF NOT EXISTS body_temperature DECIMAL(4,1),
ADD COLUMN IF NOT EXISTS respiratory_rate INTEGER,
ADD COLUMN IF NOT EXISTS pulse_oximetry INTEGER,
ADD COLUMN IF NOT EXISTS blood_glucose INTEGER,
ADD COLUMN IF NOT EXISTS hba1c DECIMAL(3,1),
ADD COLUMN IF NOT EXISTS cholesterol_levels INTEGER,
ADD COLUMN IF NOT EXISTS creatinine DECIMAL(3,1),
ADD COLUMN IF NOT EXISTS egfr INTEGER,
ADD COLUMN IF NOT EXISTS cancer_screening_results TEXT,
ADD COLUMN IF NOT EXISTS medication_adherence INTEGER,
ADD COLUMN IF NOT EXISTS medication_side_effects TEXT,
ADD COLUMN IF NOT EXISTS pill_burden INTEGER,
ADD COLUMN IF NOT EXISTS cognitive_test_results TEXT,
ADD COLUMN IF NOT EXISTS mental_health_assessment TEXT,
ADD COLUMN IF NOT EXISTS daily_activity_levels INTEGER,
ADD COLUMN IF NOT EXISTS fall_detection_incidents INTEGER,
ADD COLUMN IF NOT EXISTS mobility_aids_used TEXT,
ADD COLUMN IF NOT EXISTS calf_circumference DECIMAL(4,1),
ADD COLUMN IF NOT EXISTS dietary_intake_quality TEXT,
ADD COLUMN IF NOT EXISTS hydration_levels DECIMAL(3,1),
ADD COLUMN IF NOT EXISTS vitamin_mineral_levels TEXT,
ADD COLUMN IF NOT EXISTS sleep_initiation_difficulties BOOLEAN,
ADD COLUMN IF NOT EXISTS sleep_quality_duration DECIMAL(3,1),
ADD COLUMN IF NOT EXISTS vision_status TEXT,
ADD COLUMN IF NOT EXISTS hearing_status TEXT,
ADD COLUMN IF NOT EXISTS use_of_aids TEXT,
ADD COLUMN IF NOT EXISTS pain_levels_locations TEXT,
ADD COLUMN IF NOT EXISTS safe_pain_medications BOOLEAN,
ADD COLUMN IF NOT EXISTS social_interaction_levels TEXT,
ADD COLUMN IF NOT EXISTS living_conditions TEXT,
ADD COLUMN IF NOT EXISTS age_friendly_environment BOOLEAN,
ADD COLUMN IF NOT EXISTS emergency_contact_updated BOOLEAN,
ADD COLUMN IF NOT EXISTS sos_alerts BOOLEAN,
ADD COLUMN IF NOT EXISTS health_checkup_adherence INTEGER,
ADD COLUMN IF NOT EXISTS vaccination_updated BOOLEAN,
ADD COLUMN IF NOT EXISTS urinary_bowel_issues TEXT,
ADD COLUMN IF NOT EXISTS substance_abuse BOOLEAN,
ADD COLUMN IF NOT EXISTS weight DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS bmi DECIMAL(4,1);

-- Add additional new columns based on the updated Patient model
ALTER TABLE patients
ADD COLUMN IF NOT EXISTS temperature DECIMAL(4,1),
ADD COLUMN IF NOT EXISTS cholesterol_total INTEGER,
ADD COLUMN IF NOT EXISTS hdl_cholesterol INTEGER,
ADD COLUMN IF NOT EXISTS ldl_cholesterol INTEGER,
ADD COLUMN IF NOT EXISTS triglycerides INTEGER,
ADD COLUMN IF NOT EXISTS sodium INTEGER,
ADD COLUMN IF NOT EXISTS potassium INTEGER,
ADD COLUMN IF NOT EXISTS calcium INTEGER,
ADD COLUMN IF NOT EXISTS magnesium INTEGER,
ADD COLUMN IF NOT EXISTS current_medications TEXT,
ADD COLUMN IF NOT EXISTS medication_allergies TEXT,
ADD COLUMN IF NOT EXISTS cognitive_status TEXT,
ADD COLUMN IF NOT EXISTS depression_screening TEXT,
ADD COLUMN IF NOT EXISTS anxiety_screening TEXT,
ADD COLUMN IF NOT EXISTS activity_level TEXT,
ADD COLUMN IF NOT EXISTS exercise_frequency TEXT,
ADD COLUMN IF NOT EXISTS mobility_status TEXT,
ADD COLUMN IF NOT EXISTS nutritional_status TEXT,
ADD COLUMN IF NOT EXISTS hydration_status TEXT,
ADD COLUMN IF NOT EXISTS height DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS sleep_quality TEXT,
ADD COLUMN IF NOT EXISTS sleep_duration TEXT,
ADD COLUMN IF NOT EXISTS sleep_disturbances TEXT,
ADD COLUMN IF NOT EXISTS pain_level INTEGER,
ADD COLUMN IF NOT EXISTS pain_location TEXT,
ADD COLUMN IF NOT EXISTS pain_character TEXT,
ADD COLUMN IF NOT EXISTS living_situation TEXT,
ADD COLUMN IF NOT EXISTS social_support TEXT,
ADD COLUMN IF NOT EXISTS environmental_risks TEXT,
ADD COLUMN IF NOT EXISTS fall_risk TEXT,
ADD COLUMN IF NOT EXISTS emergency_contact_name TEXT,
ADD COLUMN IF NOT EXISTS emergency_contact_phone TEXT,
ADD COLUMN IF NOT EXISTS preventive_care_adherence TEXT,
ADD COLUMN IF NOT EXISTS additional_health_concerns TEXT;

-- Create doctors table if it doesn't exist
CREATE TABLE IF NOT EXISTS doctors (
  doctor_id SERIAL PRIMARY KEY,
  first_name VARCHAR(50) NOT NULL,
  last_name VARCHAR(50) NOT NULL,
  specialty VARCHAR(100),
  email VARCHAR(100) UNIQUE,
  phone VARCHAR(20),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add foreign key constraint if doctor_id doesn't have it
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.constraint_column_usage
    WHERE table_name = 'patients' AND column_name = 'doctor_id'
  ) THEN
    ALTER TABLE patients
    ADD CONSTRAINT fk_doctor_id FOREIGN KEY (doctor_id)
    REFERENCES doctors(doctor_id) ON DELETE SET NULL;
  END IF;
EXCEPTION
  WHEN others THEN
    -- If the constraint already exists or can't be added, do nothing
END $$;

-- Add attending_doctor_id to medical_records table
ALTER TABLE medical_records
ADD COLUMN IF NOT EXISTS attending_doctor_id INTEGER REFERENCES doctors(doctor_id) ON DELETE SET NULL;

-- Create patient visits table to track each visit separately
CREATE TABLE IF NOT EXISTS patient_visits (
  visit_id SERIAL PRIMARY KEY,
  patient_id INTEGER NOT NULL REFERENCES patients(patient_id) ON DELETE CASCADE,
  doctor_id INTEGER REFERENCES doctors(doctor_id) ON DELETE SET NULL,
  visit_date DATE NOT NULL DEFAULT CURRENT_DATE,
  visit_time TIME,
  visit_reason TEXT,
  status VARCHAR(20) DEFAULT 'scheduled',

  -- Vital Signs
  lying_bp_systolic INTEGER,
  lying_bp_diastolic INTEGER,
  sitting_bp_systolic INTEGER,
  sitting_bp_diastolic INTEGER,
  standing_bp_systolic INTEGER,
  standing_bp_diastolic INTEGER,
  lying_heart_rate INTEGER,
  standing_heart_rate INTEGER,
  heart_rhythm VARCHAR(50),
  temperature DECIMAL(4,1),
  respiratory_rate INTEGER,
  pulse_oximetry INTEGER,

  -- Lab Results: Diabetes Markers
  blood_glucose INTEGER,
  hba1c DECIMAL(3,1),

  -- Lab Results: Lipid Panel
  cholesterol_total INTEGER,
  hdl_cholesterol INTEGER,
  ldl_cholesterol INTEGER,
  triglycerides INTEGER,

  -- Lab Results: Kidney Function
  creatinine DECIMAL(3,1),
  egfr INTEGER,
  blood_urea_nitrogen INTEGER,
  urine_albumin_creatinine_ratio DECIMAL(5,1),

  -- Lab Results: Electrolytes
  sodium INTEGER,
  potassium INTEGER,
  calcium INTEGER,
  magnesium INTEGER,
  phosphorus INTEGER,
  chloride INTEGER,

  -- Lab Results: Liver Function Tests
  alt INTEGER,  -- Alanine Transaminase
  ast INTEGER,  -- Aspartate Transaminase
  alp INTEGER,  -- Alkaline Phosphatase
  ggt INTEGER,  -- Gamma-Glutamyl Transferase
  bilirubin_total DECIMAL(3,1),
  albumin DECIMAL(3,1),

  -- Lab Results: Bone Health Markers
  vitamin_d DECIMAL(4,1),
  parathyroid_hormone DECIMAL(5,1),
  alkaline_phosphatase_bone INTEGER,

  -- Lab Results: Thyroid Function
  tsh DECIMAL(4,2),  -- Thyroid Stimulating Hormone
  free_t4 DECIMAL(3,1),
  free_t3 DECIMAL(3,1),

  -- Lab Results: Inflammation Markers
  crp DECIMAL(4,1),  -- C-Reactive Protein
  esr INTEGER,       -- Erythrocyte Sedimentation Rate

  -- Lab Results: Anemia Screening
  hemoglobin DECIMAL(3,1),
  hematocrit DECIMAL(4,1),
  ferritin INTEGER,
  iron INTEGER,
  vitamin_b12 INTEGER,
  folate DECIMAL(3,1),

  -- Medication
  current_medications TEXT,
  medication_adherence INTEGER,
  medication_side_effects TEXT,

  -- Health Assessment
  weight DECIMAL(5,2),
  height DECIMAL(5,2),
  bmi DECIMAL(4,1),
  cognitive_status TEXT,
  mental_health_assessment TEXT,
  pain_level INTEGER,
  pain_location TEXT,

  -- Activity & Nutrition
  activity_level TEXT,
  nutritional_status TEXT,
  hydration_status TEXT,

  -- Social & Environment Factors
  social_interaction_levels TEXT,
  living_conditions TEXT,
  age_friendly_environment BOOLEAN,
  social_support_network TEXT,
  transportation_access TEXT,
  financial_concerns TEXT,

  -- Safety & Emergency
  emergency_contact_name TEXT,
  emergency_contact_number TEXT,
  emergency_contact_relationship TEXT,
  fall_risk_assessment TEXT,
  home_safety_evaluation TEXT,
  assistive_devices_used TEXT,

  -- Vaccination Status
  influenza_vaccination_date DATE,
  pneumococcal_vaccination_date DATE,
  zoster_vaccination_date DATE,
  tdap_vaccination_date DATE,
  covid19_vaccination_date DATE,
  covid19_booster_date DATE,
  hepatitis_a_vaccination_date DATE,
  hepatitis_b_vaccination_date DATE,
  mmr_vaccination_date DATE,
  varicella_vaccination_date DATE,
  other_vaccinations TEXT,

  -- Notes and diagnosis
  notes TEXT,
  diagnosis TEXT,
  treatment_plan TEXT,
  follow_up_instructions TEXT,
  assessment_plan VARCHAR(50),
  referrals TEXT,
  medication_changes TEXT,

  -- System fields
  created_by INTEGER REFERENCES users(user_id),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create patient_access_logs table for tracking record access
CREATE TABLE IF NOT EXISTS patient_access_logs (
  log_id SERIAL PRIMARY KEY,
  patient_id INTEGER NOT NULL REFERENCES patients(patient_id) ON DELETE CASCADE,
  user_id INTEGER NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  access_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  access_type VARCHAR(20) NOT NULL, -- 'view', 'edit', etc.
  ip_address VARCHAR(50),
  user_agent TEXT
);

-- Create patient_edit_logs table for tracking who made changes
CREATE TABLE IF NOT EXISTS patient_edit_logs (
  edit_id SERIAL PRIMARY KEY,
  patient_id INTEGER NOT NULL REFERENCES patients(patient_id) ON DELETE CASCADE,
  user_id INTEGER NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  edit_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  field_changed VARCHAR(100) NOT NULL,
  old_value TEXT,
  new_value TEXT
);

-- Add last_edited_by and last_edited_at columns to patients table
ALTER TABLE patients ADD COLUMN IF NOT EXISTS last_edited_by INTEGER REFERENCES users(user_id) ON DELETE SET NULL;
ALTER TABLE patients ADD COLUMN IF NOT EXISTS last_edited_at TIMESTAMP;

-- Create index for faster log queries
CREATE INDEX IF NOT EXISTS idx_access_logs_patient_id ON patient_access_logs(patient_id);
CREATE INDEX IF NOT EXISTS idx_access_logs_user_id ON patient_access_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_edit_logs_patient_id ON patient_edit_logs(patient_id);
CREATE INDEX IF NOT EXISTS idx_edit_logs_user_id ON patient_edit_logs(user_id);

-- Add columns for account locking to users table if they don't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'is_locked') THEN
        ALTER TABLE users ADD COLUMN is_locked BOOLEAN DEFAULT false;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'failed_login_attempts') THEN
        ALTER TABLE users ADD COLUMN failed_login_attempts INTEGER DEFAULT 0;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'last_login') THEN
        ALTER TABLE users ADD COLUMN last_login TIMESTAMP;
    END IF;
END
$$;

-- Ensure unique_id is not null and unique for all patients
ALTER TABLE patients ALTER COLUMN unique_id SET NOT NULL;
ALTER TABLE patients ADD CONSTRAINT unique_patient_id_constraint UNIQUE (unique_id);

-- Create login_activity table to track login attempts
CREATE TABLE IF NOT EXISTS login_activity (
  log_id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(user_id) ON DELETE SET NULL,
  username VARCHAR(100) NOT NULL,
  ip_address VARCHAR(45),
  status VARCHAR(20) NOT NULL, -- 'success', 'failed', 'locked'
  details TEXT,
  timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create index for faster login activity queries
CREATE INDEX IF NOT EXISTS idx_login_activity_user_id ON login_activity(user_id);
CREATE INDEX IF NOT EXISTS idx_login_activity_status ON login_activity(status);
CREATE INDEX IF NOT EXISTS idx_login_activity_timestamp ON login_activity(timestamp);

-- Create the necessary tables for the system

-- Create appointments table if it doesn't exist
CREATE TABLE IF NOT EXISTS appointments (
  appointment_id SERIAL PRIMARY KEY,
  patient_id INTEGER NOT NULL REFERENCES patients(patient_id),
  doctor_id INTEGER NOT NULL REFERENCES doctors(doctor_id),
  title VARCHAR(255) NOT NULL,
  date DATE NOT NULL,
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  status VARCHAR(50) NOT NULL CHECK (status IN ('scheduled', 'completed', 'cancelled', 'no-show')),
  type VARCHAR(50) NOT NULL CHECK (type IN ('checkup', 'follow-up', 'urgent', 'consultation', 'other')),
  notes TEXT,
  created_by INTEGER REFERENCES users(user_id),
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP
);

-- Create index for faster appointment lookups
CREATE INDEX IF NOT EXISTS idx_appointments_doctor ON appointments(doctor_id);
CREATE INDEX IF NOT EXISTS idx_appointments_patient ON appointments(patient_id);
CREATE INDEX IF NOT EXISTS idx_appointments_date ON appointments("date");

-- Create user_activities table for tracking user activity
CREATE TABLE IF NOT EXISTS user_activities (
  activity_id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(user_id) ON DELETE CASCADE,
  activity_type VARCHAR(50) NOT NULL,
  activity_details TEXT,
  ip_address VARCHAR(50),
  timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for faster user activity queries
CREATE INDEX IF NOT EXISTS idx_user_activities_user_id ON user_activities(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activities_activity_type ON user_activities(activity_type);
CREATE INDEX IF NOT EXISTS idx_user_activities_timestamp ON user_activities(timestamp);

