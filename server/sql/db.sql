-- Create database (run this separately)
-- CREATE DATABASE medapp;

-- Connect to the database
\c medapp;

-- Create users table
CREATE TABLE IF NOT EXISTS users (
  user_id SERIAL PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL,
  email VARCHAR(100) UNIQUE NOT NULL,
  password VARCHAR(255) NOT NULL,
  role VARCHAR(20) NOT NULL DEFAULT 'user',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create patients table
CREATE TABLE IF NOT EXISTS patients (
  patient_id SERIAL PRIMARY KEY,
  first_name VARCHAR(50) NOT NULL,
  last_name VA<PERSON>HAR(50) NOT NULL,
  unique_id VARCHAR(50) UNIQUE,
  date_of_birth DATE NOT NULL,
  gender VARCHAR(10),
  phone VARCHAR(20),
  email VARCHAR(100),
  address TEXT,
  
  -- Vital Signs
  lying_bp_systolic INTEGER,
  lying_bp_diastolic INTEGER,
  standing_bp_systolic INTEGER,
  standing_bp_diastolic INTEGER,
  heart_rate INTEGER,
  heart_rhythm VARCHAR(50),
  body_temperature DECIMAL(4,1),
  respiratory_rate INTEGER,
  pulse_oximetry INTEGER,
  
  -- Lab Results
  blood_glucose INTEGER,
  hba1c DECIMAL(3,1),
  cholesterol_levels INTEGER,
  creatinine DECIMAL(3,1),
  egfr INTEGER,
  cancer_screening_results TEXT,
  
  -- Medication
  medication_adherence INTEGER,
  medication_side_effects TEXT,
  pill_burden INTEGER,
  
  -- Cognitive & Mental Health
  cognitive_test_results TEXT,
  mental_health_assessment TEXT,
  
  -- Physical Activity
  daily_activity_levels INTEGER,
  fall_detection_incidents INTEGER,
  mobility_aids_used TEXT,
  calf_circumference DECIMAL(4,1),
  
  -- Nutrition & Hydration
  dietary_intake_quality TEXT,
  hydration_levels DECIMAL(3,1),
  vitamin_mineral_levels TEXT,
  
  -- Sleep
  sleep_initiation_difficulties BOOLEAN,
  sleep_quality_duration DECIMAL(3,1),
  
  -- Sensory
  vision_status TEXT,
  hearing_status TEXT,
  use_of_aids TEXT,
  
  -- Pain Management
  pain_levels_locations TEXT,
  safe_pain_medications BOOLEAN,
  
  -- Social & Environmental
  social_interaction_levels TEXT,
  living_conditions TEXT,
  age_friendly_environment BOOLEAN,
  
  -- Safety & Emergency
  emergency_contact_updated BOOLEAN,
  sos_alerts BOOLEAN,
  
  -- Preventive Care
  health_checkup_adherence INTEGER,
  vaccination_updated BOOLEAN,
  
  -- Additional Health Concerns
  urinary_bowel_issues TEXT,
  substance_abuse BOOLEAN,
  
  -- System fields
  weight DECIMAL(5,2),
  bmi DECIMAL(4,1),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create medical records table
CREATE TABLE IF NOT EXISTS medical_records (
  record_id SERIAL PRIMARY KEY,
  patient_id INTEGER REFERENCES patients(patient_id) ON DELETE CASCADE,
  record_date DATE NOT NULL DEFAULT CURRENT_DATE,
  diagnosis TEXT,
  treatment TEXT,
  notes TEXT,
  created_by INTEGER REFERENCES users(user_id),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create prescriptions table
CREATE TABLE IF NOT EXISTS prescriptions (
  prescription_id SERIAL PRIMARY KEY,
  record_id INTEGER REFERENCES medical_records(record_id) ON DELETE CASCADE,
  medication VARCHAR(100) NOT NULL,
  dosage VARCHAR(50),
  frequency VARCHAR(50),
  duration VARCHAR(50),
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create doctor_patient_assignments table
CREATE TABLE IF NOT EXISTS doctor_patient_assignments (
  assignment_id SERIAL PRIMARY KEY,
  doctor_id INTEGER REFERENCES users(user_id) ON DELETE CASCADE,
  patient_id INTEGER REFERENCES patients(patient_id) ON DELETE CASCADE,
  assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(doctor_id, patient_id)
);

-- Insert sample assignments for dr.smith
INSERT INTO doctor_patient_assignments (doctor_id, patient_id)
SELECT u.user_id, p.patient_id
FROM users u
CROSS JOIN (
  SELECT patient_id FROM patients LIMIT 2
) p
WHERE u.username = 'dr.smith'
ON CONFLICT (doctor_id, patient_id) DO NOTHING;

-- Create appointments table
CREATE TABLE IF NOT EXISTS appointments (
  appointment_id SERIAL PRIMARY KEY,
  patient_id INTEGER REFERENCES patients(patient_id) ON DELETE CASCADE,
  doctor_id INTEGER REFERENCES users(user_id) ON DELETE CASCADE,
  appointment_date DATE NOT NULL,
  appointment_time TIME NOT NULL,
  reason TEXT,
  status VARCHAR(20) DEFAULT 'scheduled',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(doctor_id, appointment_date, appointment_time)
);

-- Insert sample appointments for dr.smith
INSERT INTO appointments (patient_id, doctor_id, appointment_date, appointment_time, reason)
SELECT 
  dpa.patient_id,
  dpa.doctor_id,
  CURRENT_DATE + (i * INTERVAL '1 day'),
  '09:00:00'::TIME + (i * INTERVAL '1 hour'),
  CASE i 
    WHEN 0 THEN 'Regular checkup'
    WHEN 1 THEN 'Follow-up visit'
    ELSE 'Consultation'
  END
FROM doctor_patient_assignments dpa
CROSS JOIN generate_series(0, 2) i
JOIN users u ON dpa.doctor_id = u.user_id
WHERE u.username = 'dr.smith'
ON CONFLICT (doctor_id, appointment_date, appointment_time) DO NOTHING;

-- Insert sample admin user (password: admin123)
INSERT INTO users (username, email, password, role)
VALUES ('admin', '<EMAIL>', '$2a$10$OMUlmIlHmxrXj1juYEIVZ.1Yq53FTZ8k5oG7RVt.VIBDYKtFmQIlG', 'admin')
ON CONFLICT (username) DO NOTHING; 