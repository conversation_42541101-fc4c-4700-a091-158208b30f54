-- Fix for login issues - ensure login_activity table exists

-- Create login_activity table to track login attempts
CREATE TABLE IF NOT EXISTS login_activity (
  log_id SERIAL PRIMARY KEY,
  user_id INTEGER REFERENCES users(user_id) ON DELETE SET NULL,
  username VARCHAR(100) NOT NULL,
  ip_address VARCHAR(45),
  status VARCHAR(20) NOT NULL, -- 'success', 'failed', 'locked', 'registered'
  details TEXT,
  timestamp TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Create index for faster login activity queries
CREATE INDEX IF NOT EXISTS idx_login_activity_user_id ON login_activity(user_id);
CREATE INDEX IF NOT EXISTS idx_login_activity_status ON login_activity(status);
CREATE INDEX IF NOT EXISTS idx_login_activity_timestamp ON login_activity(timestamp);

-- Add a try-catch wrapper for LoginActivity in auth routes
CREATE OR REPLACE FUNCTION log_activity_safely(
  user_id INTEGER, 
  username VARCHAR(100), 
  ip_address VARCHAR(45), 
  status VARCHAR(20), 
  details TEXT
) RETURNS VOID AS $$
BEGIN
  INSERT INTO login_activity (user_id, username, ip_address, status, details)
  VALUES (user_id, username, ip_address, status, details);
EXCEPTION
  WHEN OTHERS THEN
    -- Log error but continue execution
    RAISE NOTICE 'Failed to log login activity: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- Update auth routes to use this function instead of direct inserts 