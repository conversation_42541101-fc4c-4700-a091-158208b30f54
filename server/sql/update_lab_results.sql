-- Update lab results for all patient visits

-- <PERSON> (ID: 4) - Hypertension and Pre-diabetes monitoring
UPDATE patient_visits 
SET 
  blood_glucose = 125,
  hba1c = 6.2,
  cholesterol_total = 185,
  hdl_cholesterol = 42,
  ldl_cholesterol = 115,
  triglycerides = 155,
  creatinine = 1.1,
  egfr = 85
WHERE patient_id = 4 AND visit_date = '2024-01-15';

UPDATE patient_visits 
SET 
  blood_glucose = 128,
  hba1c = 6.3,
  cholesterol_total = 182,
  hdl_cholesterol = 40,
  ldl_cholesterol = 112,
  triglycerides = 148,
  creatinine = 1.2,
  egfr = 82
WHERE patient_id = 4 AND visit_date = '2024-02-20';

UPDATE patient_visits 
SET 
  blood_glucose = 122,
  hba1c = 6.1,
  cholesterol_total = 180,
  hdl_cholesterol = 43,
  ldl_cholesterol = 108,
  triglycerides = 145,
  creatinine = 1.1,
  egfr = 84
WHERE patient_id = 4 AND visit_date = '2024-03-25';

-- <PERSON> (ID: 5) - Arthritis and bone health monitoring
UPDATE patient_visits 
SET 
  blood_glucose = 92,
  hba1c = 5.4,
  cholesterol_total = 195,
  hdl_cholesterol = 52,
  ldl_cholesterol = 118,
  triglycerides = 125,
  creatinine = 0.9,
  egfr = 95
WHERE patient_id = 5 AND visit_date = '2024-01-10';

UPDATE patient_visits 
SET 
  blood_glucose = 94,
  hba1c = 5.5,
  cholesterol_total = 192,
  hdl_cholesterol = 54,
  ldl_cholesterol = 115,
  triglycerides = 122,
  creatinine = 0.9,
  egfr = 94
WHERE patient_id = 5 AND visit_date = '2024-02-25';

UPDATE patient_visits 
SET 
  blood_glucose = 90,
  hba1c = 5.4,
  cholesterol_total = 190,
  hdl_cholesterol = 55,
  ldl_cholesterol = 112,
  triglycerides = 120,
  creatinine = 0.8,
  egfr = 96
WHERE patient_id = 5 AND visit_date = '2024-03-20';

-- Eleanor Thompson (ID: 6) - Diabetes management
UPDATE patient_visits 
SET 
  blood_glucose = 145,
  hba1c = 7.2,
  cholesterol_total = 210,
  hdl_cholesterol = 45,
  ldl_cholesterol = 128,
  triglycerides = 185,
  creatinine = 1.3,
  egfr = 75
WHERE patient_id = 6 AND visit_date = '2024-01-05';

UPDATE patient_visits 
SET 
  blood_glucose = 135,
  hba1c = 6.8,
  cholesterol_total = 200,
  hdl_cholesterol = 46,
  ldl_cholesterol = 122,
  triglycerides = 175,
  creatinine = 1.2,
  egfr = 78
WHERE patient_id = 6 AND visit_date = '2024-02-15';

UPDATE patient_visits 
SET 
  blood_glucose = 128,
  hba1c = 6.5,
  cholesterol_total = 195,
  hdl_cholesterol = 47,
  ldl_cholesterol = 118,
  triglycerides = 165,
  creatinine = 1.1,
  egfr = 82
WHERE patient_id = 6 AND visit_date = '2024-03-15';

-- David Lee (ID: 7) - Cardiovascular health monitoring
UPDATE patient_visits 
SET 
  blood_glucose = 105,
  hba1c = 5.6,
  cholesterol_total = 220,
  hdl_cholesterol = 38,
  ldl_cholesterol = 142,
  triglycerides = 200,
  creatinine = 1.0,
  egfr = 88
WHERE patient_id = 7 AND visit_date = '2024-01-20';

UPDATE patient_visits 
SET 
  blood_glucose = 108,
  hba1c = 5.7,
  cholesterol_total = 200,
  hdl_cholesterol = 42,
  ldl_cholesterol = 132,
  triglycerides = 180,
  creatinine = 1.0,
  egfr = 89
WHERE patient_id = 7 AND visit_date = '2024-02-28';

UPDATE patient_visits 
SET 
  blood_glucose = 102,
  hba1c = 5.5,
  cholesterol_total = 180,
  hdl_cholesterol = 44,
  ldl_cholesterol = 125,
  triglycerides = 165,
  creatinine = 0.9,
  egfr = 90
WHERE patient_id = 7 AND visit_date = '2024-03-28';

-- Sarah Miller (ID: 8) - Healthy patient with preventive care
UPDATE patient_visits 
SET 
  blood_glucose = 92,
  hba1c = 5.2,
  cholesterol_total = 170,
  hdl_cholesterol = 58,
  ldl_cholesterol = 92,
  triglycerides = 110,
  creatinine = 0.8,
  egfr = 105
WHERE patient_id = 8 AND visit_date = '2024-01-25';

UPDATE patient_visits 
SET 
  blood_glucose = 95,
  hba1c = 5.3,
  cholesterol_total = 175,
  hdl_cholesterol = 60,
  ldl_cholesterol = 90,
  triglycerides = 108,
  creatinine = 0.8,
  egfr = 104
WHERE patient_id = 8 AND visit_date = '2024-02-10';

UPDATE patient_visits 
SET 
  blood_glucose = 90,
  hba1c = 5.2,
  cholesterol_total = 168,
  hdl_cholesterol = 62,
  ldl_cholesterol = 88,
  triglycerides = 105,
  creatinine = 0.7,
  egfr = 106
WHERE patient_id = 8 AND visit_date = '2024-03-10';

-- Add comments to explain the ranges used
COMMENT ON COLUMN patient_visits.blood_glucose IS 'Fasting Blood Glucose (mg/dL). Normal: 70-99, Prediabetes: 100-125, Diabetes: ≥126';
COMMENT ON COLUMN patient_visits.hba1c IS 'Glycated Hemoglobin (%). Normal: <5.7, Prediabetes: 5.7-6.4, Diabetes: ≥6.5';
COMMENT ON COLUMN patient_visits.cholesterol_total IS 'Total Cholesterol (mg/dL). Optimal: <200, Borderline: 200-239, High: ≥240';
COMMENT ON COLUMN patient_visits.hdl_cholesterol IS 'HDL Cholesterol (mg/dL). Low Risk: >60, Moderate: 40-60, High Risk: <40';
COMMENT ON COLUMN patient_visits.ldl_cholesterol IS 'LDL Cholesterol (mg/dL). Optimal: <100, Near Optimal: 100-129, Borderline: 130-159, High: ≥160';
COMMENT ON COLUMN patient_visits.triglycerides IS 'Triglycerides (mg/dL). Normal: <150, Borderline: 150-199, High: 200-499, Very High: ≥500';
COMMENT ON COLUMN patient_visits.creatinine IS 'Creatinine (mg/dL). Normal: 0.7-1.3 (men), 0.6-1.1 (women)';
COMMENT ON COLUMN patient_visits.egfr IS 'Estimated Glomerular Filtration Rate (mL/min/1.73m²). Normal: ≥90, Mild Decrease: 60-89, Moderate: 30-59, Severe: 15-29, Kidney Failure: <15'; 