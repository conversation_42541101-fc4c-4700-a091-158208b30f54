-- Create kin_patient_relationships table for many-to-many relationship between kin users and patients
CREATE TABLE IF NOT EXISTS kin_patient_relationships (
  relationship_id SERIAL PRIMARY KEY,
  kin_user_id INTEGER NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
  patient_id INTEGER NOT NULL REFERENCES patients(patient_id) ON DELETE CASCADE,
  relationship_type VARCHAR(50), -- e.g., 'spouse', 'child', 'parent', 'sibling', 'other'
  is_primary BOOLEAN DEFAULT false, -- Indicates if this is the primary patient for this kin
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(kin_user_id, patient_id)
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_kin_patient_kin_user_id ON kin_patient_relationships(kin_user_id);
CREATE INDEX IF NOT EXISTS idx_kin_patient_patient_id ON kin_patient_relationships(patient_id);

-- Migrate existing kin-patient relationships from users table
INSERT INTO kin_patient_relationships (kin_user_id, patient_id, is_primary)
SELECT user_id, patient_id, true
FROM users
WHERE role = 'kin' AND patient_id IS NOT NULL
ON CONFLICT (kin_user_id, patient_id) DO NOTHING;

-- Comment out this line if you want to keep the patient_id in the users table for backward compatibility
-- ALTER TABLE users DROP COLUMN IF EXISTS patient_id;
