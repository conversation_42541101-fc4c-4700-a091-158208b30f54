-- Check if role exists
DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role' AND typtype = 'e') THEN
    CREATE TYPE user_role AS ENUM ('admin', 'doctor', 'nurse', 'patient', 'assistant');
  ELSE
    -- If enum exists but doesn't have 'assistant'
    IF NOT EXISTS (
      SELECT 1 FROM pg_enum 
      WHERE enumlabel = 'assistant' 
      AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'user_role')
    ) THEN
      ALTER TYPE user_role ADD VALUE 'assistant';
    END IF;
  END IF;
END $$;

-- Delete user if exists to avoid conflicts
DELETE FROM users WHERE username = 'rita_shrestha';

-- Create Rita <PERSON>hrest<PERSON> user with assistant role
-- Password is 'password123'
INSERT INTO users (
  username, 
  email, 
  password, 
  role, 
  created_at, 
  is_locked, 
  failed_login_attempts
) VALUES (
  'rita_shrestha', 
  '<EMAIL>', 
  '$2a$10$tLKag7dLMrdl.SiSqmtiHenmjs2v1LTvgxZUk2o.AEKf1Kg9TE96K', 
  'assistant', 
  NOW(), 
  false, 
  0
);

-- Verify output
SELECT 
  user_id, 
  username, 
  email, 
  role,
  substring(password from 1 for 10) || '...' as password_preview
FROM users 
WHERE username = 'rita_shrestha';

-- Success message
SELECT 'User Rita Shrestha created successfully!' AS result; 