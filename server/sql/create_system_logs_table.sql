-- Create system_logs table for security and audit logging
CREATE TABLE IF NOT EXISTS system_logs (
  log_id SERIAL PRIMARY KEY,
  log_type VARCHAR(50) NOT NULL,
  user_id INTEGER REFERENCES users(user_id) ON DELETE SET NULL,
  related_id INTEGER,
  related_name TEXT,
  details JSON<PERSON>,
  ip_address VARCHAR(45),
  log_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_system_logs_log_type ON system_logs(log_type);
CREATE INDEX IF NOT EXISTS idx_system_logs_user_id ON system_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_system_logs_log_time ON system_logs(log_time);
