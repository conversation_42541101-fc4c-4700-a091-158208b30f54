const db = require('../db');
let SystemLog;

// Try to import the SystemLog model, but don't fail if it's not available
try {
  SystemLog = require('../models/SystemLog');
} catch (err) {
  console.warn('SystemLog model not available, falling back to direct database queries');
  SystemLog = null;
}

/**
 * Log a security event to the system_logs table
 * @param {Object} eventData - Event data
 * @param {string} eventData.event_type - Type of security event
 * @param {number} eventData.user_id - User ID who performed the action
 * @param {string} eventData.details - Details about the event
 * @param {string} eventData.ip_address - IP address of the user
 * @param {string} eventData.resource_type - Type of resource (patients, visits, messages, etc.)
 * @param {number} eventData.resource_id - ID of the resource
 * @returns {Promise<Object>} Created log entry
 */
const logSecurityEvent = async (eventData) => {
  try {
    const {
      event_type,
      user_id,
      details,
      ip_address = 'system',
      resource_type,
      resource_id
    } = eventData;

    // Use SystemLog model if available
    if (SystemLog && typeof SystemLog.create === 'function') {
      return await SystemLog.create({
        log_type: event_type,
        user_id,
        related_id: resource_id,
        related_name: `${resource_type} ID: ${resource_id}`,
        details: { details },
        ip_address
      });
    }

    // Fallback to direct database query if SystemLog model is not available
    const result = await db.query(
      `INSERT INTO system_logs
       (log_type, user_id, related_id, related_name, details, ip_address)
       VALUES ($1, $2, $3, $4, $5, $6)
       RETURNING *`,
      [
        event_type,
        user_id,
        resource_id,
        `${resource_type} ID: ${resource_id}`,
        JSON.stringify({ details }),
        ip_address
      ]
    );

    console.log(`Security event logged: ${event_type} by user ${user_id}`);
    return result.rows[0];
  } catch (err) {
    console.error('Error logging security event:', err);
    // Don't throw the error - logging should never break the main functionality
    return null;
  }
};

module.exports = {
  logSecurityEvent
};
