/**
 * Validation utility functions and schemas for consistent input validation
 */
const { check, validationResult, param, query, body } = require('express-validator');
const sanitizeHtml = require('sanitize-html');

/**
 * Common validation schemas for reuse across routes
 */
const validationSchemas = {
  // User validation
  user: {
    create: [
      check('username', 'Username is required').not().isEmpty().trim().escape(),
      check('email', 'Please include a valid email').isEmail().normalizeEmail(),
      check('password', 'Password must be at least 8 characters').isLength({ min: 8 }),
      check('role').optional().isIn(['admin', 'doctor', 'staff', 'patient', 'kin', 'researcher', 'user']),
    ],
    update: [
      check('username').optional().not().isEmpty().trim().escape(),
      check('email').optional().isEmail().normalizeEmail(),
      check('role').optional().isIn(['admin', 'doctor', 'staff', 'patient', 'kin', 'researcher', 'user']),
    ],
    changePassword: [
      check('currentPassword', 'Current password is required').not().isEmpty(),
      check('newPassword', 'New password must be at least 8 characters').isLength({ min: 8 }),
    ],
  },
  
  // Patient validation
  patient: {
    create: [
      check('first_name', 'First name is required').not().isEmpty().trim().escape(),
      check('last_name', 'Last name is required').not().isEmpty().trim().escape(),
      check('date_of_birth', 'Valid date of birth is required').isDate(),
      check('gender').optional().isIn(['Male', 'Female', 'Other', 'Prefer not to say']),
      check('blood_type').optional().isIn(['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-', 'Unknown']),
      check('contact_number').optional().isMobilePhone().withMessage('Please provide a valid phone number'),
      check('email').optional().isEmail().normalizeEmail(),
      check('address').optional().trim().escape(),
      check('emergency_contact_name').optional().trim().escape(),
      check('emergency_contact_number').optional().isMobilePhone().withMessage('Please provide a valid emergency contact number'),
      check('emergency_contact_relationship').optional().trim().escape(),
      check('allergies').optional().trim().escape(),
      check('medical_conditions').optional().trim().escape(),
      check('medications').optional().trim().escape(),
      check('notes').optional().trim().escape(),
    ],
    update: [
      check('first_name').optional().trim().escape(),
      check('last_name').optional().trim().escape(),
      check('date_of_birth').optional().isDate(),
      check('gender').optional().isIn(['Male', 'Female', 'Other', 'Prefer not to say']),
      check('blood_type').optional().isIn(['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-', 'Unknown']),
      check('contact_number').optional().isMobilePhone().withMessage('Please provide a valid phone number'),
      check('email').optional().isEmail().normalizeEmail(),
      check('address').optional().trim().escape(),
      check('emergency_contact_name').optional().trim().escape(),
      check('emergency_contact_number').optional().isMobilePhone().withMessage('Please provide a valid emergency contact number'),
      check('emergency_contact_relationship').optional().trim().escape(),
      check('allergies').optional().trim().escape(),
      check('medical_conditions').optional().trim().escape(),
      check('medications').optional().trim().escape(),
      check('notes').optional().trim().escape(),
    ],
  },
  
  // Visit validation
  visit: {
    create: [
      check('patient_id', 'Patient ID is required').isInt(),
      check('visit_date', 'Valid visit date is required').isDate(),
      check('doctor_id').optional().isInt(),
      check('chief_complaint').optional().trim().escape(),
      check('diagnosis').optional().trim().escape(),
      check('notes').optional().trim().escape(),
      check('temperature').optional().isFloat({ min: 30, max: 45 }),
      check('heart_rate').optional().isInt({ min: 30, max: 250 }),
      check('blood_pressure_systolic').optional().isInt({ min: 50, max: 250 }),
      check('blood_pressure_diastolic').optional().isInt({ min: 30, max: 150 }),
      check('respiratory_rate').optional().isInt({ min: 5, max: 60 }),
      check('oxygen_saturation').optional().isFloat({ min: 50, max: 100 }),
      check('weight').optional().isFloat({ min: 0, max: 500 }),
      check('height').optional().isFloat({ min: 0, max: 300 }),
    ],
    update: [
      check('visit_date').optional().isDate(),
      check('doctor_id').optional().isInt(),
      check('chief_complaint').optional().trim().escape(),
      check('diagnosis').optional().trim().escape(),
      check('notes').optional().trim().escape(),
      check('temperature').optional().isFloat({ min: 30, max: 45 }),
      check('heart_rate').optional().isInt({ min: 30, max: 250 }),
      check('blood_pressure_systolic').optional().isInt({ min: 50, max: 250 }),
      check('blood_pressure_diastolic').optional().isInt({ min: 30, max: 150 }),
      check('respiratory_rate').optional().isInt({ min: 5, max: 60 }),
      check('oxygen_saturation').optional().isFloat({ min: 50, max: 100 }),
      check('weight').optional().isFloat({ min: 0, max: 500 }),
      check('height').optional().isFloat({ min: 0, max: 300 }),
    ],
  },
  
  // Prescription validation
  prescription: {
    create: [
      check('patient_id', 'Patient ID is required').isInt(),
      check('medication_name', 'Medication name is required').not().isEmpty().trim().escape(),
      check('dosage').optional().trim().escape(),
      check('frequency').optional().trim().escape(),
      check('duration').optional().trim().escape(),
      check('start_date').optional().isDate(),
      check('end_date').optional().isDate(),
      check('notes').optional().trim().escape(),
      check('prescribed_by').optional().isInt(),
    ],
    update: [
      check('medication_name').optional().trim().escape(),
      check('dosage').optional().trim().escape(),
      check('frequency').optional().trim().escape(),
      check('duration').optional().trim().escape(),
      check('start_date').optional().isDate(),
      check('end_date').optional().isDate(),
      check('notes').optional().trim().escape(),
      check('prescribed_by').optional().isInt(),
    ],
  },
  
  // ID parameter validation
  idParam: [
    param('id').isInt().withMessage('ID must be a number'),
  ],
  
  // Pagination parameters
  pagination: [
    query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive number'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  ],
};

/**
 * Middleware to validate request based on schema
 * @param {Array} validations - Array of validation middleware
 * @returns {Function} Express middleware function
 */
const validate = (validations) => {
  return async (req, res, next) => {
    await Promise.all(validations.map(validation => validation.run(req)));
    
    const errors = validationResult(req);
    if (errors.isEmpty()) {
      return next();
    }
    
    return res.status(400).json({ 
      errors: errors.array().map(err => ({
        param: err.param,
        msg: err.msg,
        location: err.location
      }))
    });
  };
};

/**
 * Sanitize HTML content to prevent XSS
 * @param {string} content - Content to sanitize
 * @returns {string} Sanitized content
 */
const sanitizeContent = (content) => {
  if (!content) return content;
  
  if (typeof content === 'string') {
    return sanitizeHtml(content, {
      allowedTags: ['b', 'i', 'em', 'strong', 'p', 'br'],
      allowedAttributes: {},
    });
  }
  
  if (typeof content === 'object') {
    const sanitized = {};
    for (const [key, value] of Object.entries(content)) {
      if (typeof value === 'string') {
        sanitized[key] = sanitizeHtml(value, {
          allowedTags: ['b', 'i', 'em', 'strong', 'p', 'br'],
          allowedAttributes: {},
        });
      } else if (typeof value === 'object' && value !== null) {
        sanitized[key] = sanitizeContent(value);
      } else {
        sanitized[key] = value;
      }
    }
    return sanitized;
  }
  
  return content;
};

/**
 * Middleware to sanitize request body
 */
const sanitizeBody = (req, res, next) => {
  if (req.body) {
    req.body = sanitizeContent(req.body);
  }
  next();
};

module.exports = {
  validationSchemas,
  validate,
  sanitizeContent,
  sanitizeBody,
};
