/**
 * Simple logger utility for consistent logging across the application
 */

/**
 * Log an error message with optional error object
 * @param {string} message - Error message
 * @param {Error|Object} [error] - Error object or data
 */
const error = (message, error) => {
  console.error(`[ERROR] ${message}`);
  if (error) {
    if (error instanceof Error) {
      console.error('Details:', error.message);
      console.error('Stack:', error.stack);
    } else {
      console.error('Details:', error);
    }
  }
};

/**
 * Log a warning message
 * @param {string} message - Warning message
 * @param {Object} [data] - Additional data
 */
const warn = (message, data) => {
  console.warn(`[WARNING] ${message}`);
  if (data) {
    console.warn('Data:', data);
  }
};

/**
 * Log an info message
 * @param {string} message - Info message
 * @param {Object} [data] - Additional data
 */
const info = (message, data) => {
  console.log(`[INFO] ${message}`);
  if (data) {
    console.log('Data:', data);
  }
};

/**
 * Log a debug message (only in development)
 * @param {string} message - Debug message
 * @param {Object} [data] - Additional data
 */
const debug = (message, data) => {
  if (process.env.NODE_ENV === 'development') {
    console.log(`[DEBUG] ${message}`);
    if (data) {
      console.log('Data:', data);
    }
  }
};

module.exports = {
  error,
  warn,
  info,
  debug
};
