/**
 * Utility functions for generating comprehensive patient data
 */

/**
 * Generates comprehensive patient data for all fields in the patient schema
 * @returns {Object} Object containing all patient data fields
 */
function generateComprehensivePatientData() {
  // Helper function to generate random values with a chance of abnormal values
  const randomWithAbnormal = (normalLow, normalHigh, abnormalLow, abnormalHigh, decimals = 1) => {
    const abnormalChance = 0.3; // 30% chance of abnormal value
    const highOrLow = Math.random();

    if (Math.random() < abnormalChance) {
      // Generate abnormal value
      if (highOrLow < 0.5) {
        // Low abnormal
        return parseFloat((Math.random() * (normalLow - abnormalLow) + abnormalLow).toFixed(decimals));
      } else {
        // High abnormal
        return parseFloat((Math.random() * (abnormalHigh - normalHigh) + normalHigh).toFixed(decimals));
      }
    } else {
      // Generate normal value
      return parseFloat((Math.random() * (normalHigh - normalLow) + normalLow).toFixed(decimals));
    }
  };

  // Random vital signs
  const lyingBpSystolic = 110 + Math.floor(Math.random() * 40); // 110-150
  const lyingBpDiastolic = 70 + Math.floor(Math.random() * 20); // 70-90
  const standingBpSystolic = lyingBpSystolic + Math.floor(Math.random() * 10) - 5; // +/- 5 from lying
  const standingBpDiastolic = lyingBpDiastolic + Math.floor(Math.random() * 10) - 5; // +/- 5 from lying
  const sittingBpSystolic = lyingBpSystolic + Math.floor(Math.random() * 5); // +/- 5 from lying
  const sittingBpDiastolic = lyingBpDiastolic + Math.floor(Math.random() * 5); // +/- 5 from lying
  const heartRate = 60 + Math.floor(Math.random() * 40); // 60-100
  const lyingHeartRate = heartRate - Math.floor(Math.random() * 5); // Slightly lower when lying
  const standingHeartRate = heartRate + Math.floor(Math.random() * 10); // Slightly higher when standing
  const sittingHeartRate = heartRate - Math.floor(Math.random() * 3); // Slightly lower than standing, higher than lying
  const heartRhythms = ['Regular', 'Irregular', 'Bradycardia', 'Tachycardia', 'Arrhythmia'];
  const heartRhythm = heartRhythms[Math.floor(Math.random() * heartRhythms.length)];
  const temperature = 36.1 + (Math.random() * 1.1); // 36.1-37.2°C (normal range)
  const bodyTemperature = temperature - 0.2 + (Math.random() * 0.4); // Slight variation
  const respiratoryRate = 12 + Math.floor(Math.random() * 8); // 12-20
  const pulseOximetry = 95 + Math.floor(Math.random() * 5); // 95-100
  const oxygenSaturation = pulseOximetry; // Same as pulse oximetry

  // Random physical measurements
  const height = 150 + Math.floor(Math.random() * 50); // 150-200 cm
  const weight = 50 + Math.floor(Math.random() * 50); // 50-100 kg
  const bmi = (weight / ((height / 100) * (height / 100))); // BMI calculation
  const calfCircumference = 30 + Math.floor(Math.random() * 10); // 30-40 cm

  // Random lab values
  const bloodGlucose = 70 + Math.floor(Math.random() * 130); // 70-200 mg/dL
  const hba1c = 4 + (Math.random() * 3); // 4-7%
  const cholesterolTotal = 150 + Math.floor(Math.random() * 100); // 150-250 mg/dL
  const hdlCholesterol = 40 + Math.floor(Math.random() * 30); // 40-70 mg/dL
  const ldlCholesterol = 70 + Math.floor(Math.random() * 80); // 70-150 mg/dL
  const triglycerides = 50 + Math.floor(Math.random() * 150); // 50-200 mg/dL
  const vldl = randomWithAbnormal(5, 40, 2, 60, 0); // 5-40 mg/dL
  const creatinine = 0.6 + (Math.random() * 0.8); // 0.6-1.4 mg/dL
  const egfr = 60 + Math.floor(Math.random() * 60); // 60-120 mL/min
  const bloodUreaNitrogen = 7 + Math.floor(Math.random() * 13); // 7-20 mg/dL

  // Electrolytes
  const sodium = 135 + Math.floor(Math.random() * 10); // 135-145 mEq/L
  const potassium = Math.floor(3.5 + (Math.random() * 1.0)); // 3.5-4.5 mEq/L (integer in DB)
  const calcium = Math.floor(8.5 + (Math.random() * 1.5)); // 8.5-10.0 mg/dL (integer in DB)
  const magnesium = Math.floor(1.5 + (Math.random() * 1.0)); // 1.5-2.5 mg/dL (integer in DB)
  const phosphorus = 2.5 + (Math.random() * 1.5); // 2.5-4.0 mg/dL



  // Liver function tests
  const alt = randomWithAbnormal(7, 56, 3, 120, 0); // 7-56 U/L (males), 7-45 U/L (females)
  const ast = randomWithAbnormal(8, 48, 4, 110, 0); // 8-48 U/L (males), 8-43 U/L (females)
  const alp = randomWithAbnormal(40, 129, 20, 300, 0); // 40-129 U/L
  const bilirubin_t = randomWithAbnormal(0.1, 1.2, 0.05, 3.0, 2); // 0.1-1.2 mg/dL
  const bilirubin_d = randomWithAbnormal(0.0, 0.3, 0.0, 1.0, 2); // 0.0-0.3 mg/dL
  const albumin = randomWithAbnormal(3.5, 5.0, 2.0, 6.0, 1); // 3.5-5.0 g/dL
  const total_protein = randomWithAbnormal(6.0, 8.3, 4.0, 10.0, 1); // 6.0-8.3 g/dL
  const ggt = randomWithAbnormal(8, 61, 4, 150, 0); // 8-61 U/L (males), 5-36 U/L (females)

  // Thyroid function
  const tsh = randomWithAbnormal(0.4, 4.0, 0.1, 10.0, 2); // 0.4-4.0 mIU/L
  const t4 = randomWithAbnormal(4.5, 12.0, 2.0, 20.0, 1); // 4.5-12.0 μg/dL
  const t3 = randomWithAbnormal(80, 200, 40, 300, 0); // 80-200 ng/dL

  // Inflammation markers
  const crp = randomWithAbnormal(0.0, 3.0, 0.0, 15.0, 1); // 0-3 mg/L
  const esr = randomWithAbnormal(0, 20, 0, 50, 0); // 0-20 mm/hr (males), 0-30 mm/hr (females)

  // Complete Blood Count (CBC)
  const hemoglobin = randomWithAbnormal(13.5, 17.5, 10.0, 20.0, 1); // 13.5-17.5 g/dL (males), 12.0-15.5 g/dL (females)
  const hematocrit = randomWithAbnormal(41, 50, 30, 60, 0); // 41-50% (males), 36-48% (females)
  const rbc = randomWithAbnormal(4.5, 5.9, 3.5, 7.0, 2); // 4.5-5.9 million cells/μL (males), 4.1-5.1 million cells/μL (females)
  const wbc = randomWithAbnormal(4.5, 11.0, 2.0, 15.0, 1); // 4.5-11.0 thousand/μL
  const platelets = randomWithAbnormal(150, 450, 50, 600, 0); // 150-450 thousand/μL

  // Red Blood Cell Indices
  const mcv = randomWithAbnormal(80, 100, 65, 120, 1); // 80-100 fL
  const mch = randomWithAbnormal(27, 33, 20, 40, 1); // 27-33 pg
  const mchc = randomWithAbnormal(32, 36, 25, 40, 1); // 32-36 g/dL
  const rdw = randomWithAbnormal(11.5, 14.5, 10.0, 20.0, 1); // 11.5-14.5%

  // White Blood Cell Differential
  const neutrophils = randomWithAbnormal(40, 60, 20, 80, 0); // 40-60%
  const lymphocytes = randomWithAbnormal(20, 40, 10, 60, 0); // 20-40%
  const monocytes = randomWithAbnormal(2, 8, 1, 15, 0); // 2-8%
  const eosinophils = randomWithAbnormal(1, 4, 0, 10, 0); // 1-4%
  const basophils = randomWithAbnormal(0.5, 1, 0, 3, 1); // 0.5-1%

  // Vitamins and minerals
  const vitaminD = randomWithAbnormal(30, 100, 10, 120, 1); // 30-100 ng/mL
  const vitaminB12 = randomWithAbnormal(200, 900, 100, 1200, 0); // 200-900 pg/mL
  const folate = randomWithAbnormal(2.7, 17.0, 1.0, 25.0, 1); // 2.7-17.0 ng/mL
  const ferritin = randomWithAbnormal(20, 250, 10, 500, 0); // 20-250 ng/mL (males), 10-120 ng/mL (females)
  const iron = randomWithAbnormal(60, 170, 30, 200, 0); // 60-170 μg/dL (males), 50-130 μg/dL (females)

  // Additional blood chemistry
  const uricAcid = randomWithAbnormal(3.5, 7.2, 2.0, 10.0, 1); // Using male range as default

  // Urinalysis
  const urineColors = ['Pale Yellow', 'Yellow', 'Dark Yellow', 'Amber', 'Orange', 'Red', 'Brown'];
  const urineColor = urineColors[Math.floor(Math.random() * urineColors.length)];

  const urineTransparencies = ['Clear', 'Slightly Cloudy', 'Cloudy', 'Turbid'];
  const urineTransparency = urineTransparencies[Math.floor(Math.random() * urineTransparencies.length)];

  const urinePh = randomWithAbnormal(4.5, 8.0, 4.0, 9.0, 1); // 4.5-8.0

  const urineProteinLevels = ['Negative', 'Trace', '1+', '2+', '3+', '4+'];
  // 70% chance of Negative or Trace, 30% chance of 1+ to 4+
  const urineProtein = Math.random() < 0.7
    ? urineProteinLevels[Math.floor(Math.random() * 2)]
    : urineProteinLevels[2 + Math.floor(Math.random() * 4)];

  const urineSugarLevels = ['Negative', 'Trace', '1+', '2+', '3+', '4+'];
  // 70% chance of Negative or Trace, 30% chance of 1+ to 4+
  const urineSugar = Math.random() < 0.7
    ? urineSugarLevels[Math.floor(Math.random() * 2)]
    : urineSugarLevels[2 + Math.floor(Math.random() * 4)];

  const urineRbcLevels = ['0-2/HPF', '3-5/HPF', '6-10/HPF', '>10/HPF'];
  // 70% chance of 0-2/HPF, 30% chance of higher values
  const urineRbcs = Math.random() < 0.7
    ? urineRbcLevels[0]
    : urineRbcLevels[1 + Math.floor(Math.random() * 3)];

  const urinePusCellLevels = ['0-5/HPF', '6-10/HPF', '11-20/HPF', '>20/HPF'];
  // 70% chance of 0-5/HPF, 30% chance of higher values
  const urinePusCells = Math.random() < 0.7
    ? urinePusCellLevels[0]
    : urinePusCellLevels[1 + Math.floor(Math.random() * 3)];

  const urineCrystalTypes = ['None', 'Calcium Oxalate', 'Uric Acid', 'Triple Phosphate', 'Amorphous'];
  // 70% chance of None, 30% chance of crystals
  const urineCrystals = Math.random() < 0.7
    ? urineCrystalTypes[0]
    : urineCrystalTypes[1 + Math.floor(Math.random() * 4)];

  const urineCastTypes = ['None', 'Hyaline', 'Granular', 'Waxy', 'RBC', 'WBC'];
  // 70% chance of None, 30% chance of casts
  const urineCasts = Math.random() < 0.7
    ? urineCastTypes[0]
    : urineCastTypes[1 + Math.floor(Math.random() * 5)];

  // Cancer markers
  const psa = Math.random() * 4; // 0-4 ng/mL (for males)
  const ca125 = randomWithAbnormal(0, 35, 0, 200, 1); // 0-35 U/mL

  // Random health status
  const activityLevels = ['Sedentary', 'Light', 'Moderate', 'Active', 'Very Active'];
  const activityLevel = activityLevels[Math.floor(Math.random() * activityLevels.length)];
  const exerciseFrequencies = ['Never', 'Rarely', '1-2 times/week', '3-4 times/week', 'Daily'];
  const exerciseFrequency = exerciseFrequencies[Math.floor(Math.random() * exerciseFrequencies.length)];
  const mobilityStatuses = ['Bedridden', 'Wheelchair-bound', 'Uses walker', 'Uses cane', 'Independent'];
  const mobilityStatus = mobilityStatuses[Math.floor(Math.random() * mobilityStatuses.length)];
  const nutritionalStatuses = ['Poor', 'Fair', 'Good', 'Excellent'];
  const nutritionalStatus = nutritionalStatuses[Math.floor(Math.random() * nutritionalStatuses.length)];
  const hydrationStatuses = ['Dehydrated', 'Slightly Dehydrated', 'Well Hydrated', 'Overhydrated'];
  const hydrationStatus = hydrationStatuses[Math.floor(Math.random() * hydrationStatuses.length)];

  // Sleep data
  const sleepQualities = ['Poor', 'Fair', 'Good', 'Excellent'];
  const sleepQuality = sleepQualities[Math.floor(Math.random() * sleepQualities.length)];
  const sleepDisturbances = ['None', 'Insomnia', 'Sleep Apnea', 'Restless Leg Syndrome', 'Nightmares'];
  const sleepDisturbance = sleepDisturbances[Math.floor(Math.random() * sleepDisturbances.length)];
  const sleepDuration = 5 + Math.floor(Math.random() * 5); // 5-10 hours
  const sleepQualityDuration = sleepDuration - (Math.random() * 2); // Slightly less than total duration
  const sleepInitiationDifficulties = ['None', 'Mild', 'Moderate', 'Severe'];
  const sleepInitiationDifficulty = sleepInitiationDifficulties[Math.floor(Math.random() * sleepInitiationDifficulties.length)];

  // Pain assessment
  const painLevel = Math.floor(Math.random() * 11); // 0-10 scale
  const painLocations = ['None', 'Back', 'Joints', 'Head', 'Abdomen', 'Chest', 'Multiple locations'];
  const painLocation = painLocations[Math.floor(Math.random() * painLocations.length)];
  const painCharacters = ['None', 'Sharp', 'Dull', 'Burning', 'Throbbing', 'Stabbing', 'Aching'];
  const painCharacter = painCharacters[Math.floor(Math.random() * painCharacters.length)];
  const safePainMedications = painLevel > 3 ? 'Acetaminophen, NSAIDs with caution' : 'No pain medications needed';

  // Social and living situation
  const livingSituations = ['Alone', 'With spouse', 'With family', 'Assisted living', 'Nursing home'];
  const livingSituation = livingSituations[Math.floor(Math.random() * livingSituations.length)];
  const socialSupports = ['Strong', 'Adequate', 'Limited', 'None'];
  const socialSupport = socialSupports[Math.floor(Math.random() * socialSupports.length)];
  const socialSupportNetwork = socialSupport === 'Strong' ? 'Family, friends, community services' :
                             socialSupport === 'Adequate' ? 'Family and some friends' :
                             socialSupport === 'Limited' ? 'Few family members' : 'No support network';
  const socialInteractionLevels = ['Frequent', 'Regular', 'Occasional', 'Rare', 'None'];
  const socialInteractionLevel = socialInteractionLevels[Math.floor(Math.random() * socialInteractionLevels.length)];
  const livingConditions = livingSituation === 'Alone' ? 'Independent living, home maintenance adequate' :
                         livingSituation === 'With spouse' ? 'Shared living, good home conditions' :
                         livingSituation === 'With family' ? 'Multi-generational household, supportive environment' :
                         livingSituation === 'Assisted living' ? 'Facility-based care, 24-hour staff' :
                         'Full nursing care, medical supervision';
  const environmentalRisks = ['None identified', 'Fall hazards', 'Poor lighting', 'Cluttered spaces', 'Stairs without railings'];
  const environmentalRisk = environmentalRisks[Math.floor(Math.random() * environmentalRisks.length)];
  const ageFriendlyEnvironment = Math.random() > 0.3; // 70% chance of yes
  const transportationAccesses = ['Drives independently', 'Relies on family', 'Uses public transportation', 'Uses medical transport', 'No reliable transportation'];
  const transportationAccess = transportationAccesses[Math.floor(Math.random() * transportationAccesses.length)];
  const financialConcerns = ['None', 'Mild', 'Moderate', 'Severe'];
  const financialConcern = financialConcerns[Math.floor(Math.random() * financialConcerns.length)];

  // Emergency contacts
  const emergencyContactNames = ['John Smith', 'Mary Johnson', 'Robert Williams', 'Patricia Brown', 'Michael Jones'];
  const emergencyContactName = emergencyContactNames[Math.floor(Math.random() * emergencyContactNames.length)];
  const emergencyContactPhone = `(${Math.floor(Math.random() * 900) + 100})-${Math.floor(Math.random() * 900) + 100}-${Math.floor(Math.random() * 9000) + 1000}`;
  const emergencyContactUpdated = Math.random() > 0.2; // 80% chance of yes
  const sosAlerts = Math.random() > 0.7; // 30% chance of yes

  // Medical history and conditions
  const medicalHistoryConditions = [
    'Hypertension', 'Diabetes Type 2', 'Coronary Artery Disease', 'COPD', 'Arthritis',
    'Osteoporosis', 'Depression', 'Anxiety', 'Hypothyroidism', 'Chronic Kidney Disease',
    'Atrial Fibrillation', 'Congestive Heart Failure', 'Stroke', 'Cancer', 'Dementia'
  ];
  const numConditions = Math.floor(Math.random() * 4); // 0-3 conditions
  const medicalHistoryArray = [];
  for (let i = 0; i < numConditions; i++) {
    const condition = medicalHistoryConditions[Math.floor(Math.random() * medicalHistoryConditions.length)];
    if (!medicalHistoryArray.includes(condition)) {
      medicalHistoryArray.push(condition);
    }
  }
  const medicalHistory = medicalHistoryArray.length > 0 ? medicalHistoryArray.join(', ') : 'No significant medical history';

  // Allergies
  const allergyTypes = ['No known allergies', 'Penicillin', 'Sulfa drugs', 'NSAIDs', 'Shellfish', 'Nuts', 'Latex', 'Contrast dye'];
  const numAllergies = Math.floor(Math.random() * 3); // 0-2 allergies
  const allergiesArray = [];
  for (let i = 0; i < numAllergies; i++) {
    const allergy = allergyTypes[Math.floor(Math.random() * allergyTypes.length)];
    if (!allergiesArray.includes(allergy) && allergy !== 'No known allergies') {
      allergiesArray.push(allergy);
    }
  }
  const allergies = allergiesArray.length > 0 ? allergiesArray.join(', ') : 'No known allergies';
  const medicationAllergies = allergiesArray.filter(a => ['Penicillin', 'Sulfa drugs', 'NSAIDs'].includes(a)).join(', ') || 'None';

  // Blood type
  const bloodTypes = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
  const bloodType = bloodTypes[Math.floor(Math.random() * bloodTypes.length)];

  // Medications
  const medicationsList = [
    'Lisinopril 10mg daily', 'Metformin 500mg twice daily', 'Atorvastatin 20mg daily',
    'Levothyroxine 50mcg daily', 'Amlodipine 5mg daily', 'Metoprolol 25mg twice daily',
    'Furosemide 20mg daily', 'Omeprazole 20mg daily', 'Sertraline 50mg daily',
    'Aspirin 81mg daily', 'Albuterol inhaler as needed', 'Gabapentin 300mg three times daily'
  ];
  const numMedications = Math.floor(Math.random() * 5); // 0-4 medications
  const currentMedicationsArray = [];
  for (let i = 0; i < numMedications; i++) {
    const medication = medicationsList[Math.floor(Math.random() * medicationsList.length)];
    if (!currentMedicationsArray.includes(medication)) {
      currentMedicationsArray.push(medication);
    }
  }
  const currentMedications = currentMedicationsArray.length > 0 ? currentMedicationsArray.join('; ') : 'No current medications';
  const pillBurden = currentMedicationsArray.length;
  const medicationAdherence = pillBurden === 0 ? 'N/A' :
                            Math.random() > 0.7 ? 'Excellent' :
                            Math.random() > 0.4 ? 'Good' :
                            Math.random() > 0.2 ? 'Fair' : 'Poor';
  const medicationSideEffects = pillBurden === 0 ? 'N/A' :
                              Math.random() > 0.7 ? 'None reported' :
                              Math.random() > 0.4 ? 'Mild: occasional dizziness' :
                              Math.random() > 0.2 ? 'Moderate: GI upset, fatigue' : 'Severe: requires medication adjustment';

  // Cognitive and mental health
  const cognitiveTestResults = Math.random() > 0.8 ? 'Mild cognitive impairment noted' : 'Normal cognitive function';
  const cognitiveImpairmentScore = Math.random() > 0.8 ? Math.floor(Math.random() * 5) + 20 : Math.floor(Math.random() * 5) + 25; // 20-24 for impairment, 25-29 for normal
  const mentalHealthAssessment = Math.random() > 0.7 ? 'Signs of mild depression or anxiety' : 'No significant mental health concerns';
  const depressionScreening = Math.random() > 0.7 ? 'Positive: mild symptoms' : 'Negative: no significant symptoms';
  const anxietyScreening = Math.random() > 0.7 ? 'Positive: mild symptoms' : 'Negative: no significant symptoms';
  const depressionScore = depressionScreening.includes('Positive') ? Math.floor(Math.random() * 10) + 5 : Math.floor(Math.random() * 5); // 5-14 for positive, 0-4 for negative
  const anxietyScore = anxietyScreening.includes('Positive') ? Math.floor(Math.random() * 10) + 5 : Math.floor(Math.random() * 5); // 5-14 for positive, 0-4 for negative

  // Mobility and fall risk
  const fallRisks = ['Low', 'Moderate', 'High'];
  const fallRisk = fallRisks[Math.floor(Math.random() * fallRisks.length)];
  const fallDetectionIncidents = fallRisk === 'Low' ? 0 : fallRisk === 'Moderate' ? Math.floor(Math.random() * 2) : Math.floor(Math.random() * 3) + 1;
  const mobilityAidsUsed = mobilityStatus === 'Independent' ? 'None' :
                         mobilityStatus === 'Uses cane' ? 'Cane' :
                         mobilityStatus === 'Uses walker' ? 'Walker' :
                         mobilityStatus === 'Wheelchair-bound' ? 'Wheelchair' : 'Bed assistance devices';
  const assistiveDevicesUsed = mobilityAidsUsed === 'None' ?
                             Math.random() > 0.7 ? 'Shower grab bars' : 'None' :
                             `${mobilityAidsUsed}, shower grab bars, raised toilet seat`;

  // Vision and hearing
  const visionStatuses = ['Normal', 'Corrected with glasses', 'Moderate impairment', 'Severe impairment'];
  const visionStatus = visionStatuses[Math.floor(Math.random() * visionStatuses.length)];
  const hearingStatuses = ['Normal', 'Mild loss', 'Moderate loss', 'Severe loss', 'Uses hearing aid'];
  const hearingStatus = hearingStatuses[Math.floor(Math.random() * hearingStatuses.length)];
  const useOfAids = (visionStatus.includes('Corrected') ? 'Glasses' : '') +
                  (hearingStatus.includes('Uses hearing aid') ? (visionStatus.includes('Corrected') ? ', Hearing aid' : 'Hearing aid') : '');

  // Nutrition and hydration
  const dietaryIntakeQualities = ['Poor', 'Fair', 'Good', 'Excellent'];
  const dietaryIntakeQuality = dietaryIntakeQualities[Math.floor(Math.random() * dietaryIntakeQualities.length)];
  const vitaminMineralLevels = dietaryIntakeQuality === 'Poor' ? 'Deficiencies noted' :
                             dietaryIntakeQuality === 'Fair' ? 'Borderline adequate' :
                             'Within normal limits';

  // Urinary and bowel function
  const urinaryBowelIssues = Math.random() > 0.7 ? 'Occasional urinary incontinence' :
                           Math.random() > 0.5 ? 'Occasional constipation' :
                           Math.random() > 0.3 ? 'Nocturia' : 'No issues reported';

  // Substance use
  const substanceAbuse = Math.random() > 0.8; // 20% chance of yes
  const substanceAbuseDetails = substanceAbuse ?
                              Math.random() > 0.5 ? 'Moderate alcohol use' :
                              Math.random() > 0.3 ? 'Former smoker' : 'Current smoker' :
                              'No substance abuse';

  // Preventive care
  const healthCheckupAdherence = Math.floor(Math.random() * 5); // 0-4 scale
  const preventiveCareAdherence = healthCheckupAdherence >= 3 ? 'Good' : healthCheckupAdherence >= 1 ? 'Fair' : 'Poor';
  const vaccinationUpdated = Math.random() > 0.3; // 70% chance of yes

  // Vaccination dates
  const today = new Date();
  const getRandomPastDate = (yearsBack) => {
    const date = new Date(today);
    date.setFullYear(date.getFullYear() - Math.floor(Math.random() * yearsBack));
    date.setMonth(Math.floor(Math.random() * 12));
    date.setDate(Math.floor(Math.random() * 28) + 1);
    return date.toISOString().split('T')[0];
  };

  const influenzaVaccinationDate = getRandomPastDate(1);
  const pneumococcalVaccinationDate = getRandomPastDate(5);
  const zosterVaccinationDate = getRandomPastDate(3);
  const tdapVaccinationDate = getRandomPastDate(10);
  const covid19VaccinationDate = getRandomPastDate(2);
  const covid19BoosterDate = getRandomPastDate(1);
  const hepatitisAVaccinationDate = getRandomPastDate(15);
  const hepatitisBVaccinationDate = getRandomPastDate(15);
  const mmrVaccinationDate = getRandomPastDate(30);
  const varicellaVaccinationDate = getRandomPastDate(20);
  const pneumococcalBoosterVaccinationDate = getRandomPastDate(3);
  const covid19BoosterVaccinationDate = getRandomPastDate(1);
  const otherVaccinations = 'None';

  // Additional health concerns
  const additionalHealthConcerns = Math.random() > 0.7 ?
                                 'Patient expressed concern about memory changes' :
                                 Math.random() > 0.5 ?
                                 'Patient reports occasional dizziness' :
                                 Math.random() > 0.3 ?
                                 'Patient interested in weight management program' :
                                 'None reported';

  // Cancer screening
  const cancerScreeningResults = Math.random() > 0.9 ?
                               'Abnormal finding, follow-up recommended' :
                               'All screenings negative/within normal limits';

  return {
    // Vital signs
    lyingBpSystolic,
    lyingBpDiastolic,
    standingBpSystolic,
    standingBpDiastolic,
    sittingBpSystolic,
    sittingBpDiastolic,
    heartRate,
    lyingHeartRate,
    standingHeartRate,
    sittingHeartRate,
    heartRhythm,
    temperature,
    bodyTemperature,
    respiratoryRate,
    pulseOximetry,
    oxygenSaturation,

    // Physical measurements
    height,
    weight,
    bmi,
    calfCircumference,

    // Lab values
    bloodGlucose,
    hba1c,
    cholesterolTotal,
    hdlCholesterol,
    ldlCholesterol,
    triglycerides,
    vldl,
    creatinine,
    egfr,
    bloodUreaNitrogen,
    sodium,
    potassium,
    calcium,
    magnesium,
    phosphorus,
    // Liver function tests
    alt,
    ast,
    alp,
    bilirubin_t,
    bilirubin_d,
    albumin,
    total_protein,
    ggt,

    // Thyroid function tests
    tsh,
    t3,
    t4,

    // Inflammatory markers
    crp,
    esr,

    // Complete blood count
    hemoglobin,
    hematocrit,
    rbc,
    wbc,
    platelets,

    // Red blood cell indices
    mcv,
    mch,
    mchc,
    rdw,

    // White blood cell differential
    neutrophils,
    lymphocytes,
    monocytes,
    eosinophils,
    basophils,

    // Vitamins and iron studies
    vitaminD,
    vitaminB12,
    folate,
    ferritin,
    iron,

    // Additional blood chemistry
    uricAcid,

    // Cancer screening
    psa,
    ca125,

    // Urinalysis
    urineColor,
    urineTransparency,
    urinePh,
    urineProtein,
    urineSugar,
    urineRbcs,
    urinePusCells,
    urineCrystals,
    urineCasts,

    // Health status
    activityLevel,
    exerciseFrequency,
    mobilityStatus,
    nutritionalStatus,
    hydrationStatus,

    // Sleep data
    sleepQuality,
    sleepDisturbance,
    sleepDuration,
    sleepQualityDuration,
    sleepInitiationDifficulty,

    // Pain assessment
    painLevel,
    painLocation,
    painCharacter,
    safePainMedications,

    // Social and living situation
    livingSituation,
    socialSupport,
    socialSupportNetwork,
    socialInteractionLevel,
    livingConditions,
    environmentalRisk,
    ageFriendlyEnvironment,
    transportationAccess,
    financialConcern,

    // Emergency contacts
    emergencyContactName,
    emergencyContactPhone,
    emergencyContactUpdated,
    sosAlerts,

    // Medical history and conditions
    medicalHistory,
    allergies,
    medicationAllergies,
    bloodType,
    currentMedications,
    pillBurden,
    medicationAdherence,
    medicationSideEffects,

    // Cognitive and mental health
    cognitiveTestResults,
    cognitiveImpairmentScore,
    mentalHealthAssessment,
    depressionScreening,
    anxietyScreening,
    depressionScore,
    anxietyScore,

    // Mobility and fall risk
    fallRisk,
    fallDetectionIncidents,
    mobilityAidsUsed,
    assistiveDevicesUsed,

    // Vision and hearing
    visionStatus,
    hearingStatus,
    useOfAids,

    // Nutrition and hydration
    dietaryIntakeQuality,
    vitaminMineralLevels,

    // Urinary and bowel function
    urinaryBowelIssues,

    // Substance use
    substanceAbuse,
    substanceAbuseDetails,

    // Preventive care
    healthCheckupAdherence,
    preventiveCareAdherence,
    vaccinationUpdated,

    // Vaccination dates
    influenzaVaccinationDate,
    pneumococcalVaccinationDate,
    zosterVaccinationDate,
    tdapVaccinationDate,
    covid19VaccinationDate,
    covid19BoosterDate,
    hepatitisAVaccinationDate,
    hepatitisBVaccinationDate,
    mmrVaccinationDate,
    varicellaVaccinationDate,
    pneumococcalBoosterVaccinationDate,
    covid19BoosterVaccinationDate,
    otherVaccinations,

    // Additional health concerns
    additionalHealthConcerns,

    // Cancer screening
    cancerScreeningResults
  };
}

module.exports = {
  generateComprehensivePatientData
};
