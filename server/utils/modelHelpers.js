/**
 * Utility functions for model operations
 */

/**
 * Process numeric fields in patient data
 * @param {Object} data - The patient data object
 * @param {Array<string>} numericFields - Array of field names that should be numeric
 * @param {Array<string>} stringFields - Array of field names that should always be strings
 * @returns {Object} - Processed data with empty strings converted to null for numeric fields
 */
const processNumericFields = (data, numericFields, stringFields = []) => {
  const processedData = { ...data };

  // Define fields that should always be treated as strings, even if they contain only numbers
  const alwaysStringFields = [
    'phone', 'emergency_contact_phone', 'unique_id'
  ].concat(stringFields);

  // Process numeric fields - convert empty strings to null
  numericFields.forEach(field => {
    // Check if the field exists in the data
    if (field in processedData) {
      // Convert empty strings to null
      if (processedData[field] === '' || processedData[field] === undefined) {
        processedData[field] = null;
        console.log(`Converted empty value in field ${field} to null`);
      }
      // If it's not a number but should be, try to convert it
      else if (typeof processedData[field] === 'string' && processedData[field].trim() !== '') {
        // Try to convert to number if it looks like a number
        const numValue = Number(processedData[field]);
        if (!isNaN(numValue)) {
          processedData[field] = numValue;
          console.log(`Converted string value "${processedData[field]}" in field ${field} to number ${numValue}`);
        }
      }
    }
  });

  // Ensure string fields are always treated as strings
  alwaysStringFields.forEach(field => {
    if (field in processedData && processedData[field] !== null && processedData[field] !== undefined) {
      // Convert to string if it's not already a string
      processedData[field] = String(processedData[field]);
    }
  });

  return processedData;
};

/**
 * Generate SQL placeholders for parameterized queries
 * @param {number} count - Number of parameters
 * @returns {string} - Comma-separated placeholders ($1, $2, etc.)
 */
const generatePlaceholders = (count) => {
  return Array.from({ length: count }, (_, i) => `$${i + 1}`).join(', ');
};

/**
 * Create a SQL query for inserting data
 * @param {string} tableName - Name of the table
 * @param {Array<string>} columns - Array of column names
 * @returns {Object} - Object with query string and placeholders
 */
const createInsertQuery = (tableName, columns) => {
  const placeholders = generatePlaceholders(columns.length);

  const query = `
    INSERT INTO ${tableName} (${columns.join(', ')})
    VALUES (${placeholders})
    RETURNING *
  `;

  return { query, placeholders };
};

/**
 * Create a SQL query for updating data
 * @param {string} tableName - Name of the table
 * @param {Array<string>} columns - Array of column names
 * @param {string} idColumn - Name of the ID column
 * @param {number} idParamIndex - Index of the ID parameter
 * @returns {string} - SQL update query
 */
const createUpdateQuery = (tableName, columns, idColumn, idParamIndex) => {
  const setClause = columns
    .map((col, index) => `${col} = $${index + 1}`)
    .join(', ');

  return `
    UPDATE ${tableName}
    SET ${setClause}
    WHERE ${idColumn} = $${idParamIndex}
    RETURNING *
  `;
};

/**
 * Log database errors with context
 * @param {Error} err - The error object
 * @param {string} context - Context description
 */
const logDbError = (err, context) => {
  console.error(`Database error in ${context}:`, err);
  console.error('Error details:', {
    message: err.message,
    code: err.code,
    detail: err.detail,
    hint: err.hint,
    position: err.position,
    internalPosition: err.internalPosition,
    internalQuery: err.internalQuery,
    where: err.where,
    schema: err.schema,
    table: err.table,
    column: err.column,
    dataType: err.dataType,
    constraint: err.constraint
  });
};

module.exports = {
  processNumericFields,
  generatePlaceholders,
  createInsertQuery,
  createUpdateQuery,
  logDbError
};
