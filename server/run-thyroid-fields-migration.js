const fs = require('fs');
const path = require('path');
const { Pool } = require('pg');
require('dotenv').config();

// Create a new pool using the individual connection parameters from .env
const pool = new Pool({
  user: process.env.DB_USER,
  host: process.env.DB_HOST,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD,
  port: process.env.DB_PORT,
});

async function runMigration() {
  const client = await pool.connect();
  
  try {
    console.log('Starting migration to add thyroid and vitamin fields...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'migrations', '034_add_thyroid_fields.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    await client.query(sql);
    
    console.log('✅ Successfully executed migration');
    console.log('t4 and t3 fields have been added to the patient_visits table');
    console.log('Comments have been added to thyroid and vitamin fields');
  } catch (error) {
    console.error('❌ Error executing migration:', error);
  } finally {
    client.release();
    console.log('Database connection closed');
    pool.end();
  }
}

runMigration();
