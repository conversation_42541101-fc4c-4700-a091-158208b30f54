/**
 * <PERSON><PERSON><PERSON> to run the migration for adding missing mental health fields
 */

const { Pool } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Create a connection to the database
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'medapp',
  password: process.env.DB_PASSWORD || '',
  port: process.env.DB_PORT || 5432,
});

async function runMigration() {
  try {
    console.log('Starting migration to add missing mental health fields...');
    console.log('Database connection info:', {
      user: process.env.DB_USER || 'postgres',
      host: process.env.DB_HOST || 'localhost',
      database: process.env.DB_NAME || 'medapp',
      port: process.env.DB_PORT || 5432
    });

    // Read the SQL file
    const sqlFilePath = path.join(__dirname, 'migrations', '021_add_missing_mental_health_fields.sql');
    console.log('Reading SQL file from:', sqlFilePath);
    const sql = fs.readFileSync(sqlFilePath, 'utf8');
    console.log('SQL content:', sql);

    // Test database connection
    console.log('Testing database connection...');
    const testResult = await pool.query('SELECT NOW()');
    console.log('Database connection test successful:', testResult.rows[0]);

    // Execute the SQL
    console.log('Executing SQL...');
    const result = await pool.query(sql);
    console.log('SQL execution completed');

    console.log('✅ Successfully executed migration');
    console.log('Added missing mental health fields: depression_score and anxiety_score');

    // Log any messages from the migration
    if (result && result.rows && result.rows.length > 0) {
      console.log('Migration results:');
      result.rows.forEach(row => {
        console.log(row);
      });
    }

  } catch (err) {
    console.error('❌ Error running migration:', err);
    console.error('Error details:', err.stack);
  } finally {
    try {
      // Close the pool
      console.log('Closing database connection...');
      await pool.end();
      console.log('Database connection closed');
    } catch (err) {
      console.error('Error closing database connection:', err);
    }
  }
}

// Run the function
runMigration();
